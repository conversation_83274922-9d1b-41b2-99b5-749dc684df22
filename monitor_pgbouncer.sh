#!/bin/bash
# PgBouncer Monitoring Script for Multi-Tenant Architecture
# This script helps monitor PgBouncer performance and connection usage

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PGBOUNCER_PORT=6432
PGBOUNCER_USER="postgres"
OUTPUT_FORMAT="text"  # Options: text, json, csv

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo
  echo "Options:"
  echo "  -h, --host HOST        PgBouncer host (default: localhost)"
  echo "  -p, --port PORT        PgBouncer port (default: 6432)"
  echo "  -U, --user USER        PgBouncer user (default: postgres)"
  echo "  -f, --format FORMAT    Output format: text, json, csv (default: text)"
  echo "  -c, --command COMMAND  Specific command to run: pools, clients, servers, stats, all"
  echo "  --help                 Display this help message"
  echo
  exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -h|--host)
      PGBOUNCER_HOST="$2"
      shift 2
      ;;
    -p|--port)
      PGBOUNCER_PORT="$2"
      shift 2
      ;;
    -U|--user)
      PGBOUNCER_USER="$2"
      shift 2
      ;;
    -f|--format)
      OUTPUT_FORMAT="$2"
      shift 2
      ;;
    -c|--command)
      COMMAND="$2"
      shift 2
      ;;
    --help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# Set default host if not provided
PGBOUNCER_HOST=${PGBOUNCER_HOST:-localhost}

# Function to execute PgBouncer commands
execute_command() {
  local cmd="$1"
  local title="$2"
  
  echo -e "${BLUE}=== $title ===${NC}"
  
  if [ "$OUTPUT_FORMAT" == "json" ]; then
    PSQL_ARGS="-t -A -F, -c \"\\copy (SELECT row_to_json(t) FROM ($cmd) t) TO STDOUT\""
  elif [ "$OUTPUT_FORMAT" == "csv" ]; then
    PSQL_ARGS="-c \"\\copy ($cmd) TO STDOUT WITH CSV HEADER\""
  else
    PSQL_ARGS="-c \"$cmd\""
  fi
  
  eval "psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer $PSQL_ARGS"
  echo
}

# Function to show connection summary
show_connection_summary() {
  echo -e "${BLUE}=== Connection Summary ===${NC}"
  
  # Get total active connections
  ACTIVE_CONN=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SELECT sum(cl_active) FROM pools;")
  
  # Get total idle connections
  IDLE_CONN=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SELECT sum(cl_waiting) FROM pools;")
  
  # Get total server connections
  SERVER_CONN=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SELECT sum(sv_active) FROM pools;")
  
  # Get max client connections
  MAX_CONN=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SHOW CONFIG;" | grep max_client_conn | awk '{print $2}')
  
  echo -e "Active client connections: ${GREEN}$ACTIVE_CONN${NC}"
  echo -e "Waiting client connections: ${YELLOW}$IDLE_CONN${NC}"
  echo -e "Active server connections: ${BLUE}$SERVER_CONN${NC}"
  echo -e "Maximum client connections: ${RED}$MAX_CONN${NC}"
  
  # Calculate usage percentage
  USAGE=$(echo "scale=2; 100 * $ACTIVE_CONN / $MAX_CONN" | bc)
  echo -e "Connection usage: ${YELLOW}$USAGE%${NC}"
  echo
}

# Function to show database-specific stats
show_database_stats() {
  echo -e "${BLUE}=== Database-Specific Statistics ===${NC}"
  
  # Get list of databases
  DATABASES=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SELECT DISTINCT database FROM pools ORDER BY database;")
  
  for DB in $DATABASES; do
    # Skip pgbouncer database
    if [ "$DB" == "pgbouncer" ]; then
      continue
    fi
    
    echo -e "${GREEN}Database: $DB${NC}"
    
    # Get pool info for this database
    POOL_INFO=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SELECT pool_size, reserve_pool, max_db_connections FROM SHOW DATABASES WHERE name='$DB';")
    
    POOL_SIZE=$(echo "$POOL_INFO" | awk '{print $1}')
    RESERVE_POOL=$(echo "$POOL_INFO" | awk '{print $2}')
    MAX_DB_CONN=$(echo "$POOL_INFO" | awk '{print $3}')
    
    echo "  Pool size: $POOL_SIZE"
    echo "  Reserve pool: $RESERVE_POOL"
    echo "  Max connections: $MAX_DB_CONN"
    
    # Get current usage
    USAGE_INFO=$(psql -h $PGBOUNCER_HOST -p $PGBOUNCER_PORT -U $PGBOUNCER_USER pgbouncer -t -c "SELECT sum(cl_active), sum(cl_waiting), sum(sv_active) FROM pools WHERE database='$DB';")
    
    CL_ACTIVE=$(echo "$USAGE_INFO" | awk '{print $1}')
    CL_WAITING=$(echo "$USAGE_INFO" | awk '{print $2}')
    SV_ACTIVE=$(echo "$USAGE_INFO" | awk '{print $3}')
    
    echo "  Active clients: $CL_ACTIVE"
    echo "  Waiting clients: $CL_WAITING"
    echo "  Active server connections: $SV_ACTIVE"
    echo
  done
}

# Main execution
echo -e "${GREEN}PgBouncer Monitoring for Multi-Tenant Architecture${NC}"
echo "Host: $PGBOUNCER_HOST:$PGBOUNCER_PORT"
echo "User: $PGBOUNCER_USER"
echo "Format: $OUTPUT_FORMAT"
echo "Time: $(date)"
echo

# Check if PgBouncer is running
if ! nc -z $PGBOUNCER_HOST $PGBOUNCER_PORT &>/dev/null; then
  echo -e "${RED}Error: Cannot connect to PgBouncer at $PGBOUNCER_HOST:$PGBOUNCER_PORT${NC}"
  exit 1
fi

# Execute requested command or default to all
if [ -n "$COMMAND" ]; then
  case $COMMAND in
    pools)
      execute_command "SHOW POOLS" "Connection Pools"
      ;;
    clients)
      execute_command "SHOW CLIENTS" "Connected Clients"
      ;;
    servers)
      execute_command "SHOW SERVERS" "Server Connections"
      ;;
    stats)
      execute_command "SHOW STATS" "Statistics"
      ;;
    all)
      show_connection_summary
      show_database_stats
      execute_command "SHOW POOLS" "Connection Pools"
      execute_command "SHOW CLIENTS" "Connected Clients"
      execute_command "SHOW SERVERS" "Server Connections"
      execute_command "SHOW STATS" "Statistics"
      ;;
    *)
      echo -e "${RED}Unknown command: $COMMAND${NC}"
      usage
      ;;
  esac
else
  # Default behavior: show summary and pools
  show_connection_summary
  show_database_stats
  execute_command "SHOW POOLS" "Connection Pools"
fi

echo -e "${GREEN}Monitoring complete${NC}"
