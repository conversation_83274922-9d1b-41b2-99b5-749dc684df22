*.env
*.log
*.out
*.pid
*.sock
*.conf
acr_venv/
*.swp
__pycache__/
*.pyc
*.pyo
*.pyd
*.pyz
*.pyw
/app/automations/create_dynamicdb.sh
flask_session/
/app/static/uploads/logo/
/app/data/known_faces/*.png
/app/data/unknown_faces/*.png
/app/data/unknown_faces/*.jpg
/app/data/unknown_faces/*.jpeg
/app/static/uploads/logos/*.png
*.db
*.gz
*.zip
*.tar
*.tar.gz
*.tar.xz
*.jpeg
*.gif
*.bmp
*.tiff
*.tif
#app/static/images/*.png
app/static/images/*.jpg
app/frontend/
app/static/tailwind/
run_gunicorn.sh
gunicorn_server.sh
run_local.sh
app/models/accounts.json
app/models/vendors.json
/react-frontend/
*.sql
app/static/uploads/
.fuse_hidden00008c400000001c
