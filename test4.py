from app import app  
from app.helpers.auxillary import Auxillary
from app.automations.backup import create_backup

from app.automations.backup import send_backup_email

def run_backup_process():
    """Run the backup process inside Flask application context."""
    with app.app_context():
        try:
            send_backup_email()
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

# Call the function to run within the Flask app context
if __name__ == "__main__":
    run_backup_process()
