from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, IntegerField, DecimalField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Optional

class ManualSubscriptionForm(FlaskForm):
    """Form for manually managing company subscriptions."""
    company_id = SelectField('Company', validators=[DataRequired()], coerce=str)
    plan_id = SelectField('Plan', validators=[DataRequired()], coerce=str)
    num_days = IntegerField('Number of Days', validators=[DataRequired()])
    amount = DecimalField('Payment Amount', validators=[DataRequired()])
    payment_method = SelectField('Payment Method',
                                choices=[
                                    ('manual', 'Manual Payment'),
                                    ('bank_transfer', 'Bank Transfer'),
                                    ('cash', 'Cash'),
                                    ('mobile_money', 'Mobile Money')
                                ],
                                validators=[DataRequired()])
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Update Subscription')