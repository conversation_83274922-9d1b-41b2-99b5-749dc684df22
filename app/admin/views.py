from flask import Blueprint, render_template, current_app, flash, redirect, session, url_for, request
from app.decorators.hr_decorator import hr_required
from app.decorators.admin_decorator import admin_required
from app.models.company import Employee, Payroll, Insurance, Deductions, Attendance, User, LeaveApplication
from app import db
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection
from app.models.central import NsfContributions
from app.routes.payroll.goal_Seek_mine import SalaryCalculator, SalaryCalculatorGross
from app.models.central import Company, Plans
from app.decorators.role_decorator import role_required
from flask import jsonify
from datetime import datetime, timedelta, timezone
from app.helpers.auxillary import Auxillary
from app.models.company_salary_advance import SalaryAdvanceRequest
from decimal import Decimal, ROUND_HALF_UP
from app.admin.forms import ManualSubscriptionForm
from app.models.central_payments import Payments
import uuid
import os

from dotenv import load_dotenv
load_dotenv()

dashbboard_template = os.getenv('DASHBOARD_TEMPLATE')

admin_data_bp = Blueprint('admin_data', __name__)

@admin_data_bp.route('/hr_dashboard')
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def dashboard():
    """HR dashboard to display key metrics."""
    # Import Auxillary at the top to avoid scope issues
    from app.helpers.auxillary import Auxillary

    # Retrieve the session data
    attendance_service = session.get('attendance_service')
    username = session.get('username')
    company_name = session.get('company_name')
    company_name = company_name.upper()
    role = session.get('role')
    role = role.upper()
    first_name = session.get('first_name')
    last_name = session.get('last_name')
    first_name = first_name.strip().capitalize()
    last_name = last_name.strip().capitalize()
    company_id = session.get('company_id')
    # get company details
    company = Company.get_company_by_id(company_id)

    rama_applicable = company.get('rama_applicable')
    # set pay_date equal to today
    pay_date = datetime.now()
    try:
        rssb_contributions = NsfContributions.get_valid_contributions(pay_date)
        current_app.logger.info(f"rssb contributions: {rssb_contributions}")
    except Exception as e:
        current_app.logger.error(f"Error getting valid contributions: {str(e)}")
        rssb_contributions = []

    if rssb_contributions is None or len(rssb_contributions) == 0:
        # set the contributions to defaul values as a fall back
        pension_er_rate = 0.03
        pension_ee_rate = 0.03
        maternity_er_rate = 0.003
        maternity_ee_rate = 0.003
        cbhi_ee_rate = 0.005
        cbhi_er_rate = 0
        rama_er_rate = 0.0
        rama_ee_rate = 0.0
    else:
        # Initialize RAMA rates to default values in case they're not found in contributions
        rama_er_rate = 0.0
        rama_ee_rate = 0.0

        for contribution in rssb_contributions:
            contribution_name = contribution.contribution_name
            if contribution_name.lower() == "maternity":
                maternity_er_rate = contribution.employer_rate
                maternity_ee_rate = contribution.employee_rate
            elif contribution_name.lower() == "pension":
                pension_er_rate = contribution.employer_rate
                pension_ee_rate = contribution.employee_rate
            elif contribution_name.lower() == "cbhi":
                cbhi_er_rate = contribution.employer_rate
                cbhi_ee_rate = contribution.employee_rate
            elif contribution_name.lower() == "rama":
                rama_er_rate = contribution.employer_rate
                rama_ee_rate = contribution.employee_rate

    current_app.logger.info(f"rama er rate: {rama_er_rate}")
    current_app.logger.info(f"rama ee rate: {rama_ee_rate}")

    db_name = CompanyHelpers.get_company_database_name(company_id)
    db_connection = DatabaseConnection()
    unique_employee_ids = set()
    with db_connection.get_session(db_name) as db_session:
        try:
            no_employee = Employee.count_employees(db_session)
        except Exception as e:
            current_app.logger.error(f"Error retrieving number of employees: {str(e)}")
            flash("Error retrieving number of employees", "danger")
            no_employee = 0

        # Retrieve attendance data
        try:
            records = Attendance.get_attendance_by_date(db_session, datetime.now().date())
        except Exception as e:
            current_app.logger.error(f"Error retrieving attendance data: {str(e)}")
            flash("Error retrieving attendance data", "danger")
            records = []

        # Get the number of those who are present, off, leave and absent
        try:
            present = records['clocked_in']
            for record in present:
                employee_id = record['employee_id']
                if employee_id not in unique_employee_ids:
                    unique_employee_ids.add(employee_id)
            num_unique_present = len(unique_employee_ids)
            off = records['on_off']
            leave = records['on_leave']
        except Exception as e:
            current_app.logger.error(f"Error getting attendance data: {str(e)}")
            present = []
            off = []
            leave = []

        try:
            num_present = len(present)
            num_off = len(off)
            num_leave = len(leave)
            num_absent = no_employee - num_present - num_off - num_leave
        except Exception as e:
            current_app.logger.error(f"Error calculating attendance: {str(e)}")
            num_present = 0
            num_off = 0
            num_leave = 0
            num_absent = 0

        # Get pending salary advances
        try:
            pending_advance_requests = SalaryAdvanceRequest.get_salary_advance_requests(db_session)
            num_pending_advance_requests = len(pending_advance_requests)
        except Exception as e:
            current_app.logger.error(f"Error getting pending salary advance requests: {str(e)}")
            num_pending_advance_requests = 0

        # Get pending leave requests
        try:
            leave_requests = LeaveApplication.view_leave_applications(db_session)
            # count the leave requests
            num_leave_requests = len(leave_requests)
        except Exception as e:
            current_app.logger.error(f"error getting leave requests: {str(e)}")
            num_leave_requests = 0

        employees = Employee.get_employees(db_session)
        num_active_employees = len(employees)
        num_inactive_employees = no_employee - num_active_employees
        employees_with_deductions = []
        # Initialize the total values to zero
        total_gross = Decimal('0.00')
        total_net_salary = Decimal('0.00')
        total_pension_er = Decimal('0.00')
        total_maternity_er = Decimal('0.00')
        total_rama_ee = Decimal('0.00')
        total_payee = Decimal('0.00')
        total_pension1 = Decimal('0.00')
        total_maternity = Decimal('0.00')
        total_maternity_ee = Decimal('0.00')
        total_cbhi = Decimal('0.00')
        total_insurance = Decimal('0.00')
        total_contributions_payable = Decimal('0.00')
        if not employees:
            employee_insurance = Decimal('0.00')
            rama_ee_rate = Decimal('0.00')
        for employee in employees:
            try:
                # Initialize variables to avoid NameError
                gross_needed = Decimal('0.00')
                net_salary_value = Decimal('0.00')
                pension_ee_value = Decimal('0.00')
                pension_er_value = Decimal('0.00')
                maternity_ee_value = Decimal('0.00')
                maternity_er_value = Decimal('0.00')
                rama_ee = Decimal('0.00')
                paye = Decimal('0.00')
                cbhi_value = Decimal('0.00')
                basic_needed = Decimal('0.00')
                net_bcbhi = Decimal('0.00')
                net_cbhi = Decimal('0.00')
                total_deductions_value = Decimal('0.00')

                if not rama_applicable:
                        # If rama is not applicable, set the rates to 0
                        employee_insurance = 0
                        rama_ee_rate = Decimal('0.00')
                        rama_er_rate = Decimal('0.00')
                # Convert employee data to Decimal for consistent calculations using your pattern
                transport_allowance = Auxillary.to_decimal(employee['transport_allowance'])
                allowances = Auxillary.to_decimal(employee['allowances'])
                contributions_rate = Auxillary.to_decimal(cbhi_ee_rate)
                if employee['net_salary']:
                    #target_value_str = str(employee["net_salary"]).strip()
                    target_value = Auxillary.to_decimal(employee["net_salary"])
                initial_guess = Decimal('1.00')
                employee_type = employee["employee_type"]
                emp_deductions = Deductions.get_deductions_for_current_month_for_employee(db_session, employee['employee_id'])
                total_deductions = sum(d['deduction_amount'] for d in emp_deductions)

                gross = employee['gross_salary']
                total_staff_cost = employee['total_staff_cost']
                # Calculate the salary for the employee based on net or gross salary
                if employee['net_salary']:
                    try:
                    # Create an instance of the SalaryCalculator class
                        try:
                            total_deductions = Decimal(total_deductions)

                            calculator = SalaryCalculator(allowances, transport_allowance, total_deductions, contributions_rate, pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate, rama_ee_rate, employee_type, calculation_date=pay_date.date())

                        except Exception as e:
                            current_app.logger.error(f"Error creating SalaryCalculator instance: {str(e)}")


                        try:
                            results = calculator.goalseek(target_value, initial_guess)
                            # Initialize some variables with zero value and add them to the values as we loop
                            (basic_needed, gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                            maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = results
                        except Exception as e:
                            current_app.logger.error(f"Error_2 calculating salary for employee {employee['employee_id']}: {str(e)}")

                    except Exception as e:
                        current_app.logger.error(f"Error calculating salary for employee {employee['employee_id']}: {str(e)}")
                        flash("Error calculating salary", "danger")
                        return redirect(url_for('admin_data.dashboard'))
                elif employee['gross_salary']:
                    sample_gross = Auxillary.to_decimal(employee['gross_salary'])
                    try:
                        calculate = SalaryCalculatorGross(allowances, transport_allowance, total_deductions, contributions_rate, pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate, rama_ee_rate, employee_type, calculation_date=pay_date.date())
                        result10 = calculate.calculate_basic_salary(sample_gross)
                        # Calculate all
                        try:
                            all_results = calculate.calculate_all(result10)
                            # Unpack calculated results
                            (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                            maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = all_results
                            basic_needed = result10
                        except Exception as e:
                            current_app.logger.error(f"An error occurred while calculating all results: {e}")

                    except Exception as e:
                        current_app.logger.error(f"Error calculating salary for employee {employee['employee_id']}: {str(e)}")
                        flash("Error calculating salary", "danger")
                        return redirect(url_for('admin_data.dashboard'))

                elif employee['total_staff_cost']:
                    # Calculate the gross salary based on total staff cost
                    try:
                        # Calculate gross salary using direct mathematical method with current date
                        from datetime import date
                        gross_sarary = Employee.calculate_gross_based_on_total_staff_cost(db_session, employee, calculation_date=date.today())
                        current_app.logger.info(f"Calculated gross_salary from total_staff_cost: {gross_sarary}")

                        try:
                            # Use the same SalaryCalculatorGross as other salary types for consistency
                            calculation = SalaryCalculatorGross(allowances, transport_allowance, total_deductions, contributions_rate, pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate, rama_ee_rate, employee_type, calculation_date=pay_date.date())

                            # Calculate basic salary correctly: basic = gross - allowances - transport
                            result20 = calculation.calculate_basic_salary(gross_sarary)
                            current_app.logger.info(f"Calculated basic_salary from gross_salary: {result20}")

                            # Calculate all salary components using the standard flow
                            try:
                                my_results = calculation.calculate_all(result20)
                                # Unpack calculated results
                                (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                                maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = my_results
                                basic_needed = result20

                                current_app.logger.info(f"Total staff cost employee {employee['employee_id']}: gross={gross_needed}, basic={basic_needed}, net={net_salary_value}")
                            except Exception as e:
                                current_app.logger.error(f"An error occurred while calculating all results: {e}")

                        except Exception as e:
                            current_app.logger.error(f"An error occurred while calculating the basic salary: {e}")
                    except Exception as e:
                        current_app.logger.error(f"Error calculating salary for employee {employee['employee_id']}: {str(e)}")

                total_pension = pension_ee_value + pension_er_value

                # Add the values to the total values
                total_gross += gross_needed
                total_net_salary += net_salary_value
                total_pension_er += pension_er_value
                total_maternity_er += maternity_er_value
                total_maternity_ee += maternity_ee_value
                total_rama_ee += rama_ee
                total_payee += paye
                total_pension1 += total_pension
                total_cbhi += cbhi_value
                # Calculate total RAMA (Medical) correctly using the actual RAMA calculation logic
                # RAMA uses basic salary as the base (gross - total_allowances), matching SalaryCalculator.rama_er()
                total_allowances_for_rama = Auxillary.to_decimal(employee.get('total_allowances', 0))
                basic_salary_for_rama = gross_needed - total_allowances_for_rama
                rama_er_rate_decimal = Auxillary.to_decimal(rama_er_rate)
                rama_er_contribution = rama_er_rate_decimal * basic_salary_for_rama
                total_insurance += (rama_ee + rama_er_contribution)
                total_maternity += (maternity_er_value + maternity_ee_value)
                # Note: total_contributions_payable will be calculated after the loop
                # Create a dictionary to store the employee data
                employee_data = {
                    'employee': employee,
                    'deductions': emp_deductions,
                    'total_deductions': total_deductions,
                    'basic_needed': basic_needed,
                    'gross_needed': gross_needed,
                    'rama_ee': rama_ee,
                    'cbhi_value': cbhi_value,
                    'paye': paye,
                    'net_bcbhi': net_bcbhi,
                    'net_cbhi': net_cbhi,
                    'pension_ee_value': pension_ee_value,
                    'pension_er_value': pension_er_value,
                    'maternity_ee_value': maternity_ee_value,
                    'maternity_er_value': maternity_er_value,
                    'total_deductions_value': total_deductions_value,
                    'net_salary_value': net_salary_value,
                    'employee_type': employee_type,
                    'total_pension': pension_ee_value + pension_er_value,
                    'total_rama': rama_ee + rama_er_contribution,
                }
                # Append the employee data to the list
                employees_with_deductions.append(employee_data)
            except Exception as e:
                current_app.logger.error(

                    f"Error calculating salary for employee {employee['employee_id']}: {str(e)}")

    # Calculate total_contributions_payable correctly after processing all employees
    total_contributions_payable = total_payee + total_cbhi + total_maternity + total_pension1 + total_insurance
    current_app.logger.info(f"Payroll tax summary calculation: PAYE={total_payee}, CBHI={total_cbhi}, Maternity={total_maternity}, Pension={total_pension1}, RAMA={total_insurance}, Total={total_contributions_payable}")

    # Determine if occupational hazard applies based on current date
    from datetime import date
    occupational_hazard_applies = pay_date.date() >= date(2025, 1, 1)
    occupational_hazard_rate = Decimal('0.02') if occupational_hazard_applies else Decimal('0.00')
    current_app.logger.info(f"Occupational hazard applies: {occupational_hazard_applies}, rate: {occupational_hazard_rate}")

    try:
        # Calculate total debits and credits
        total_debits = total_gross + total_maternity_er + total_pension_er + total_rama_ee
        total_credits = total_net_salary + total_pension1 + total_maternity + total_cbhi + total_insurance + total_payee

        # Check if debits and credits balance
        if Auxillary.round_to_decimal(total_debits) != Auxillary.round_to_decimal(total_credits):
            current_app.logger.warning(f"Dashboard journal entries do not balance: DR={total_debits}, CR={total_credits}, Difference={total_debits-total_credits}")
            # Adjust the Net Salary to balance
            difference = total_debits - total_credits
            total_net_salary += difference
            # Recalculate total credits
            total_credits = total_net_salary + total_pension1 + total_maternity + total_cbhi + total_insurance + total_payee
            current_app.logger.info(f"Dashboard journal entries balanced by adjusting Net Salary: New DR={total_debits}, CR={total_credits}")
        # Round all values first for consistency
        total_rama_ee = Auxillary.round_to_decimal(total_rama_ee)
        total_cbhi = Auxillary.round_to_decimal(total_cbhi)
        total_gross = Auxillary.round_to_decimal(total_gross)
        total_net_salary = Auxillary.round_to_decimal(total_net_salary)
        total_pension_er = Auxillary.round_to_decimal(total_pension_er)
        total_maternity_er = Auxillary.round_to_decimal(total_maternity_er)
        total_payee = Auxillary.round_to_decimal(total_payee)
        total_pension1 = Auxillary.round_to_decimal(total_pension1)
        total_maternity = Auxillary.round_to_decimal(total_maternity)
        total_insurance = Auxillary.round_to_decimal(total_insurance)
        total_debits = Auxillary.round_to_decimal(total_debits)
        total_credits = Auxillary.round_to_decimal(total_credits)
        total_contributions_payable = Auxillary.round_to_decimal(total_contributions_payable)

        # Ensure all rates are Decimal for template calculations
        pension_er_rate = Decimal(str(pension_er_rate))
        pension_ee_rate = Decimal(str(pension_ee_rate))
        maternity_er_rate = Decimal(str(maternity_er_rate))
        maternity_ee_rate = Decimal(str(maternity_ee_rate))
        cbhi_er_rate = Decimal(str(cbhi_er_rate))
        cbhi_ee_rate = Decimal(str(cbhi_ee_rate))
        rama_ee_rate = Decimal(str(rama_ee_rate))
        rama_er_rate = Decimal(str(rama_er_rate))


        # Format values for display
        total_rama_ee_display = "{:,}".format(total_rama_ee)
        total_cbhi_display = "{:,}".format(total_cbhi)
        total_gross_display = "{:,}".format(total_gross)
        total_net_salary_display = "{:,}".format(total_net_salary)
        total_pension_er_display = "{:,}".format(total_pension_er)
        total_maternity_er_display = "{:,}".format(total_maternity_er)
        total_payee_display = "{:,}".format(total_payee)
        total_pension1_display = "{:,}".format(total_pension1)
        total_maternity_display = "{:,}".format(total_maternity)
        total_insurance_display = "{:,}".format(total_insurance)
        total_debits_display = "{:,}".format(total_debits)
        total_credits_display = "{:,}".format(total_credits)
        total_contributions_payable_display = "{:,}".format(total_contributions_payable)
        company_data = Company.get_company_by_id(company_id)
        # Get document counts for the dashboard
        try:
            # Get document counts
            from app.models.company_documents import Document
            # Count all documents for HR/admin users
            documents_count = db_session.query(Document).count()
        except Exception as e:
            current_app.logger.error(f"Error getting document counts: {str(e)}")
            documents_count = 0

        return render_template(dashbboard_template,
                            no_employee=no_employee,
                            total_gross=total_gross_display,
                            company_data=company_data,
                            total_pension_er=total_pension_er_display,
                            total_net_salary=total_net_salary_display,
                            total_maternity_er=total_maternity_er_display,
                            total_rama_ee=total_rama_ee_display,
                            total_payee=total_payee_display,
                            total_pension=total_pension1_display,
                            total_maternity=total_maternity_display,
                            total_cbhi=total_cbhi_display,
                            total_insurance=total_insurance_display,
                            employee_insurance=rama_ee_rate,
                            employer_insurance=rama_er_rate,
                            pension_er_rate=pension_er_rate,
                            pension_ee_rate=pension_ee_rate,
                            maternity_er_rate=maternity_er_rate,
                            maternity_ee_rate=maternity_ee_rate,
                            cbhi_er_rate=cbhi_er_rate,
                            cbhi_ee_rate=cbhi_ee_rate,
                            total_debits=total_debits_display,
                            total_credits=total_credits_display,
                            attendance_service=attendance_service,
                            num_present=num_unique_present,
                            num_off=num_off,
                            num_leave=num_leave,
                            num_absent=num_absent,
                            num_pending_advance_requests=num_pending_advance_requests,
                            num_leave_requests=num_leave_requests,
                            documents_count=documents_count,
                            num_active_employees=num_active_employees,
                            num_inactive_employees=num_inactive_employees,
                            Auxillary=Auxillary,
                            total_contributions_payable=total_contributions_payable_display,
                            occupational_hazard_applies=occupational_hazard_applies,
                            occupational_hazard_rate=occupational_hazard_rate,
                            )
    except Exception as e:
        current_app.logger.error(f"Error rendering the dashboard: {str(e)}")
        # Log types and values
        flash("Error rendering the dashboard", "danger")
        return redirect(url_for('user_data.login'))

@admin_data_bp.route('/hr_dashboard_censored')
@role_required(['hr'])
def dashboard_censored():
    return render_template('admin/dashboard_censored.html')



@admin_data_bp.route('/admin_dashboard')
@admin_required
def admin_dashboard():
    """Admin dashboard."""
    return render_template('admin/admin_dashboard.html')

@admin_data_bp.route('/manage_subscriptions', methods=['GET', 'POST'])
@admin_required
def manage_subscriptions():
    """Manage company subscriptions manually."""
    form = ManualSubscriptionForm()

    # Get all companies and plans for the dropdown
    companies = Company.get_companies()
    plans = Plans.get_plans()

    # Populate the form choices
    form.company_id.choices = [(str(company['company_id']), company['company_name']) for company in companies]
    form.plan_id.choices = [(str(plan['plan_id']), plan['plan_name']) for plan in plans]

    if form.validate_on_submit():
        company_id = form.company_id.data
        plan_id = form.plan_id.data
        num_days = form.num_days.data
        amount = form.amount.data
        payment_method = form.payment_method.data
        notes = form.notes.data

        # Generate a unique transaction ID
        unique_id = str(uuid.uuid4())[:8].upper()
        timestamp = datetime.now().strftime("%Y%m%d%H%M")
        transaction_id = f"TR-{unique_id}-{timestamp}"

        # Log the transaction ID for debugging
        current_app.logger.info(f"Generated transaction ID: {transaction_id}")

        try:
            # Update the company's subscription
            result, success = Company.update_subscription_end_period(company_id, num_days)

            if success:
                # Update the company's plan if needed
                company = db.session.query(Company).filter_by(company_id=company_id).first()
                if str(company.plan_id) != plan_id:
                    company.plan_id = plan_id
                    db.session.commit()

                # Record the payment
                user_id = session.get('user_id')
                current_app.logger.info(f"User ID: {user_id}")

                # Log the user_id for debugging
                current_app.logger.info(f"Creating payment with user_id: {user_id}")

                if not user_id:
                    current_app.logger.warning("No user_id found in session, cannot associate payment with user")
                    flash("Warning: Payment recorded but not associated with any user", "warning")

                payment = Payments.create_payment(
                    company_id=company_id,
                    amount=amount,
                    payment_method=payment_method,
                    transaction_id=transaction_id,
                    user_id=user_id
                )

                if payment:
                    flash(f"Subscription updated successfully. New end date: {result.get('new_end_date')}", "success")
                    return redirect(url_for('admin_data.manage_subscriptions'))
                else:
                    flash("Error recording payment", "danger")
            else:
                flash(f"Error updating subscription: {result.get('error')}", "danger")
        except Exception as e:
            current_app.logger.error(f"Error in manage_subscriptions: {str(e)}")
            flash(f"An error occurred: {str(e)}", "danger")

    # Get recent payments for display
    try:
        recent_payments = Payments.get_payments()
        # Sort by created_at in descending order and limit to 10
        recent_payments = sorted(recent_payments, key=lambda x: x['paid_at'], reverse=True)[:10]
    except Exception as e:
        current_app.logger.error(f"Error getting recent payments: {str(e)}")
        recent_payments = []

    return render_template('admin/manage_subscriptions.html', form=form, recent_payments=recent_payments)

@admin_data_bp.route('/supervisor_dashboard')
@role_required(['supervisor'])
def supervisor_dashboard():
    """Supervisor dashboard."""
    company_name = session.get('company_name')
    username = session.get('username')
    firstname = session.get('first_name')
    lastname = session.get('last_name')
    db_name = session.get('database_name')
    company_id = session.get('company_id')
    role = session.get('role')
    company_data = Company.get_company_by_id(company_id)

    db_connection = DatabaseConnection()

    # Initialize variables to ensure they are defined in case of early returns
    num_attendance = 0
    employees_attended = []
    current_date = datetime.now().date()
    num_leave = 0
    num_off = 0
    current_day = datetime.now().strftime("%d/%m/%Y")  # Format the current date for display

    with db_connection.get_session(db_name) as db_session:
        try:
            no_employee = Employee.count_employees(db_session)
            employees = Employee.get_employees(db_session)

            # Get attendance data for today
            attendance = Attendance.get_attendance(db_session)
            unique_employee_ids = set()

            if attendance:
                # Process each attendance record
                for att in attendance:
                    date_str = att.get('time_in')
                    try:
                        # Get employee_id from attendance
                        employee_id = att.get('employee_id')
                    except Exception as e:
                        current_app.logger.error(f"Error getting employee ID: {str(e)}")
                        flash("Error getting employee ID", "danger")


                    if date_str is None:
                        continue  # Skip entries without a valid date

                    try:
                        # Parse the date string from attendance
                        date = datetime.strptime(date_str, "%d/%m/%Y %H:%M:%S").date()
                    except Exception as e:
                        current_app.logger.error(f"Error parsing date: {str(e)}")
                        flash("Error parsing date", "danger")
                        continue

                    # Compare attendance date with today's date
                    if date == current_date:
                        employee_name = att.get('employee_name')
                        try:
                            employee_id = att.get('employee_id')
                        except Exception as e:
                            current_app.logger.error(f"Error getting employee ID: {str(e)}")
                            flash("Error getting employee ID", "danger")
                            continue

                        if employee_id not in unique_employee_ids:
                            unique_employee_ids.add(employee_id)
                            employees_attended.append(employee_name)
                            num_attendance += 1
                        else:
                            continue

                for leave in attendance:
                   work_status = leave.get('work_status')
                   if work_status:
                        # Retrieve the time_off_begin_date and time_off_end_date and compare if the current date is within the range
                        time_off_begin_date = leave.get('time_off_begin_date')
                        time_off_end_date = leave.get('time_off_end_date')
                        if time_off_begin_date and time_off_end_date:
                            time_off_begin_date = datetime.strptime(time_off_begin_date, "%d/%m/%Y").date()
                            time_off_end_date = datetime.strptime(time_off_end_date, "%d/%m/%Y").date()
                            if time_off_begin_date <= current_date <= time_off_end_date:
                                if work_status == 'leave':
                                    num_leave += 1
                                elif work_status == 'off':
                                    num_off += 1

        except Exception as e:
            current_app.logger.error(f"Error getting attendance data: {str(e)}")
            flash("Error getting attendance data", "danger")

    try:
        # Log final attendance details and render the dashboard
        absentees = no_employee - num_attendance - num_leave - num_off
        return render_template(
            'admin/supervisor_dashboard.html',
            user=username,
            firstname=firstname,
            company=company_name,
            no_employee=no_employee,
            employees=employees,
            num_attendance=num_attendance,
            employees_attended=employees_attended,
            current_date=current_day,
            company_data=company_data,
            lastname=lastname,
            role=role,
            num_leave=num_leave,
            num_off=num_off,
            absentees=absentees
        )
    except Exception as e:
        current_app.logger.error(f"Error rendering the supervisor dashboard: {str(e)}")
        flash("Error rendering the supervisor dashboard", "danger")
        return jsonify({'message': 'Error rendering the supervisor dashboard'})

@admin_data_bp.route('/employee_dashboard')
@role_required(['employee'])
def employee_dashboard():
    """Employee dashboard."""
    username = session.get('username')
    company_name = session.get('company_name')
    username = session.get('username')
    firstname = session.get('first_name')
    db_name = session.get('database_name')
    role = session.get('role')
    db_connection = DatabaseConnection()

    # connect to the database
    with db_connection.get_session(db_name) as db_session:
        # Get user details
        try:
            user = User.get_user_by_username(db_session, username)
        except Exception as e:
            current_app.logger.error(f"Error getting user data: {str(e)}")
            flash("Error getting user data", "danger")
            return redirect(url_for('company_users.login_company_users'))
        user_id = user.get('user_id')
        employee_id = user.get('employee_id')

        # Save the some info in session
        session['employee_id'] = employee_id
        session['user_id'] = user_id
        try:
            # Get employee details
            employee = Employee.get_employee_by_id(db_session, employee_id)
            employee_name = employee.get('full_name')
            net_salary = employee.get('net_salary')
            gross_salary = employee.get('gross_salary')
            total_staff_cost = employee.get('total_staff_cost')
            salary_advance_balance = employee.get('salary_advance_balance', 0)
            if salary_advance_balance is None:
                salary_advance_balance = 0
            annual_leave_balance = employee.get('annual_leave_balance') if employee.get('annual_leave_balance') is not None else 0
        except Exception as e:
            current_app.logger.error(f"Error getting employee data: {str(e)}")
            flash("Error getting employee data", "danger")
            return redirect(url_for('company_users.login_company_users'))

        try:
            # Get attendance for an employee
            attendance = Attendance.get_attendance_for_employee(db_session, employee_id)
            # get the number of attendance in the current month
            attendance_count = 0
            for att in attendance:
                date_str = att.get('time_in')
                if date_str is None:
                    continue
                try:
                    date = datetime.strptime(date_str, "%d/%m/%Y %H:%M:%S").date()
                except Exception as e:
                    current_app.logger.error(f"Error parsing date: {str(e)}")
                    flash("Error parsing date", "danger")
                    continue
                if date.month == datetime.now().month:
                    attendance_count += 1
        except Exception as e:
            current_app.logger.error(f"Error getting attendance data: {str(e)}")
            flash("Error getting attendance data", "danger")

        try:
            # Get leave details for the employee
            details = LeaveApplication.get_leave_application_for_employee(db_session, employee_id)
            # count the number of leave applications whose status is pending
            leave_pending = 0
            for detail in details:
                if detail['status'] == 'pending':
                    leave_pending += 1

        except Exception as e:
            current_app.logger.error(f"Error getting leave details: {str(e)}")
    # Get employee document counts
    try:
        from app.models.company_documents import Document
        # Count documents for this specific employee
        employee_documents_count = db_session.query(Document).filter(
            Document.document_type == 'employee',
            Document.employee_id == employee_id
        ).count()
    except Exception as e:
        current_app.logger.error(f"Error getting employee document counts: {str(e)}")
        employee_documents_count = 0

    try:
        return render_template('admin/employee_dashboard.html', user=username,
                           employee_name=employee_name, role=role, company=company_name,
                           net_salary=net_salary, gross_salary=gross_salary,
                           total_staff_cost=total_staff_cost, Auxillary=Auxillary,
                           leave_pending=leave_pending, annual_leave_balance=round(annual_leave_balance, 2), attendance_count=attendance_count,
                           salary_advance_balance=salary_advance_balance, firstname=firstname,
                           employee_documents_count=employee_documents_count)
    except Exception as e:
        current_app.logger.error(f"Error rendering the employee dashboard: {str(e)}")
        flash("Error rendering the employee dashboard", "danger")
        return jsonify({'message': 'Error rendering the employee dashboard'})