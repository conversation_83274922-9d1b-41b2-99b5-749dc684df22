from flask import Flask, current_app
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from dotenv import load_dotenv
import os
from flask_wtf.csrf import CSRFProtect
from flask_session import Session
import logging
from flask_mail import Mail, Message
from itsdangerous import URLSafeTimedSerializer
import secrets
from app.helpers.token_manager import TokenManager
from app.helpers.celery_config import make_celery
from flask_cors import CORS
from app.api import jwt_config
from app.helpers.common_data import get_common_data
from flask import g, session
from app.helpers.session_messages_mgmt import clear_session_messages

load_dotenv()
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('SQLALCHEMY_DATABASE_URI')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
app.config['SECURITY_PASSWORD_SALT'] = secrets.token_urlsafe(32)
app.config['RECAPTCHA_PUBLIC_KEY'] = os.getenv('RECAPTCHA_PUBLIC_KEY')
app.config['RECAPTCHA_PRIVATE_KEY'] = os.getenv('RECAPTCHA_PRIVATE_KEY')
app.config['TAILWIND_UI'] = True
CORS(app, resources={r"/api/v1/*": {"origins": "*"}}, supports_credentials=True) # To support CORS for API requests
# Configure session to use filesystem (server-side session)
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

db = SQLAlchemy(app)
migrate = Migrate(app, db)
_ = jwt_config.jwt.init_app(app) # To avoid circular imports

# configure basic flask_jwt_extended configurations
app.config['JWT_SECRET_KEY'] = "this is secret key to be changed"
# app.config['JWT_ACCESS_TOKEN_EXPIRES'] = 30 * 60
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = 24 * 60 * 60 # for development
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = 24 * 30 * 60 * 60 # To expire in a month
app.config['JWT_TOKEN_LOCATION'] = ['headers']



# Flask-Mail configuration
app.config['MAIL_SERVER'] = 'mail.accountants.co.rw'
app.config['MAIL_PORT'] = 465
app.config['MAIL_USE_TLS'] = False
app.config['MAIL_USE_SSL'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'
app.config['MAIL_PASSWORD'] = os.getenv('email_password')
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
print("email Password: ", os.getenv('email_password'))
mail = Mail(app)

# Add Celery configurations
app.config['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
app.config['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'

# Initialize Celery
celery = make_celery(app)


# Configure the Flask logger
handler = logging.FileHandler('app.log')
handler.setLevel(logging.INFO)  # Set the handler logging level

# Update formatter to include pathname, filename, and line number
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s - [in %(pathname)s:%(lineno)d]')
handler.setFormatter(formatter)

app.logger.addHandler(handler)
app.logger.setLevel(logging.INFO)
 # Set the Flask logger level


@app.context_processor
def inject_common_data():
    """
    Inject common data into templates.
    This function is called before rendering any template.
   """
    return get_common_data()

# This is used to clear messages that are from json based response(they are thrown not by that endpoint)
# They are coming out of endpoint where you can not change it as they are being used somewhere elese
# For example, they are coming from *forms* while the endpoint is JSON based.
# to prevent it to be shown on the next request with session store still holding it
app.after_request(clear_session_messages)

@app.before_request
def before_request():
   g.company_name = session.get('company_name', '')
   g.username = session.get('username', '')
   g.role = session.get('role', '')
   g.first_name = session.get('first_name', '')
   g.last_name = session.get('last_name', '')
   g.user = session.get('username', '')
   g.company_id = session.get('company_id', '')


# Import blueprints from the api and other routes
from app.api.v1.employees import employee_bp
from app.api.v1.taxbracket import tax_bracket_bp
from app.api.v1.rssb_contributions import rssb_contribution_bp
from app.company.views import company_data_bp
from app.auth.user.views import user_data_bp
from app.api.v1.users import users_bp
from app.admin.views import admin_data_bp
from app.routes.employees.employees import employees_bp
from app.routes.taxbracket.taxbracket_opps import taxbracket_bp
from app.routes.rssb.rssb_contributions import rssb_bp
from app.routes.rra.rra import rra_bp
from app.routes.taxbracket.second_employee import second_employee_bp
from app.routes.taxbracket.casuals_tax_bracket import casuals_bp
from app.routes.user_roles.views import user_role_bp
from app.routes.payroll.payroll_summary import payroll_summary_bp
from app.routes.payroll.payroll_summary_v2 import payroll_summary_v2_bp

from app.routes.deductions.views import deductions_bp
from app.routes.deductions.views_v2 import deductions_v2_bp
from app.routes.insurances.insurance import insurance_bp
from app.routes.insurances.insurance_v2 import insurance_bp_v2
from app.routes.employee_types.employee_types import employee_types_bp
from app.routes.taxbracket.consultant_tax_bracket import consultant_bp
from app.routes.token.token_manager import token_bp
from app.routes.errors.errors import errors_bp
from app.routes.pages.pages import pages_bp
from app.routes.reimbursements.reimbursements import reimbursements_bp
from app.routes.reimbursements.reimbursements_v2 import reimbursements_v2_bp
from app.routes.departments.departments import departments_bp
from app.routes.database_backup.db_backup import db_backup
from app.routes.sitemap.sites import sites_bp
from app.routes.feedback.feedback import feedback
from app.routes.attendance.attendance import attendance_bp
from app.routes.company_users.company_users import company_users_bp
from app.routes.company_locations.company_locations import company_locations_bp
from app.routes.company_locations.company_locations_v2 import company_locations_bp_v2
from app.routes.payroll.payroll_calculator import payroll_calculator_bp
from app.routes.brd_deductions.brd_deductions import brd_deductions_bp
from app.routes.features.features import features_bp
from app.routes.plans.plans import plans_bp
from app.routes.route_plans.route_plan_requirements import route_plan_requirements
from app.helpers.route_helpers import restrict_based_on_plan
from app.routes.leave_applications.leave_applications import leave_applications
from app.routes.approval_types.approval_types import approval_types
from app.routes.leave_types.leave_types import leave_types
from app.routes.approval_work_flow.approval_work_flow import approval_work_flow
from app.routes.salary_advance.salary_advance import advance_requests
from app.routes.quickbooks.quickbooks_ops import quickbooks_bp
from app.routes.quickbooks.quickbooks_ops_v2 import quickbooks_bp_v2
from app.routes.payroll.payroll_approval import payroll_approval
from app.routes.shifts.shifts import shifts
from app.routes.scheduler.scheduler import scheduler_bp, start_scheduler
from app.routes.blog_posts.blog import blog_posts
from app.routes.fingerprint.fingerprint import fingerprint
from app.routes.irembo.irembo import irembo_bp
from app.routes.subscriptions.views import subscription_bp
from app.routes.attendance.field import field_bp
from app.routes.documents.documents import document
from app.routes.agency.views import agency_bp
from app.routes.db_monitor.db_monitor import db_monitor_bp
attendance_bp.before_request(restrict_based_on_plan)
#employee_bp.before_request(restrict_based_on_plan)
from app.api.register_blueprints import api_bp
from app.routes.employees.my_employees import my_employees_bp
from app.routes.salary_advance.salary_advance_v2 import advance_requests_v2
from app.routes.departments.departments_v2 import departments_v2_bp
from app.routes.shifts.shifts_v2 import shifts_v2
from app.routes.documents.documents_v2 import document_v2
from app.routes.leave_applications.leave_applications_v2 import leave_applications_v2
from app.auth.user.views_v2 import user_data_bp_v2
from app.routes.documents.documents_v2 import document_v2
from app.routes.company_users.company_users_v2 import company_users_bp_v2
from app.routes.approval_work_flow.approval_work_flow_v2 import approval_work_flow_v2
from app.routes.attendance.attendance_v2 import attendance_bp_v2
from app.company.views_v2 import company_data_bp_v2
from app.routes.irembo.irembo_v2 import irembo_bp_v2

# Register blueprints
app.register_blueprint(employee_bp)
app.register_blueprint(tax_bracket_bp)
app.register_blueprint(rssb_contribution_bp)
app.register_blueprint(company_data_bp)
app.register_blueprint(user_data_bp)
app.register_blueprint(users_bp)
app.register_blueprint(admin_data_bp)
app.register_blueprint(employees_bp)
app.register_blueprint(taxbracket_bp)
app.register_blueprint(rssb_bp)
app.register_blueprint(rra_bp)
app.register_blueprint(second_employee_bp)
app.register_blueprint(casuals_bp)
app.register_blueprint(user_role_bp)
app.register_blueprint(payroll_summary_bp)
app.register_blueprint(payroll_summary_v2_bp)
app.register_blueprint(deductions_bp)
app.register_blueprint(insurance_bp)
app.register_blueprint(insurance_bp_v2)
app.register_blueprint(employee_types_bp)
app.register_blueprint(consultant_bp)
app.register_blueprint(token_bp)
app.register_blueprint(errors_bp)
app.register_blueprint(pages_bp)
app.register_blueprint(reimbursements_bp)
app.register_blueprint(departments_bp)
app.register_blueprint(db_backup)
app.register_blueprint(sites_bp)
app.register_blueprint(feedback)
app.register_blueprint(attendance_bp)
app.register_blueprint(company_users_bp)
app.register_blueprint(company_locations_bp)
app.register_blueprint(company_locations_bp_v2)
app.register_blueprint(payroll_calculator_bp)
app.register_blueprint(brd_deductions_bp)
app.register_blueprint(features_bp)
app.register_blueprint(plans_bp)
app.register_blueprint(route_plan_requirements)
app.register_blueprint(leave_applications)
app.register_blueprint(approval_types)
app.register_blueprint(leave_types)
app.register_blueprint(approval_work_flow)
app.register_blueprint(advance_requests)
app.register_blueprint(quickbooks_bp)
app.register_blueprint(quickbooks_bp_v2)
app.register_blueprint(payroll_approval)
app.register_blueprint(shifts)
app.register_blueprint(scheduler_bp)
app.register_blueprint(blog_posts)
app.register_blueprint(fingerprint)
app.register_blueprint(irembo_bp)
app.register_blueprint(subscription_bp)
app.register_blueprint(field_bp)
app.register_blueprint(document)
app.register_blueprint(agency_bp)
app.register_blueprint(db_monitor_bp)
app.register_blueprint(api_bp, url_prefix="/api/v1")
app.register_blueprint(my_employees_bp)
app.register_blueprint(reimbursements_v2_bp)
app.register_blueprint(deductions_v2_bp)
app.register_blueprint(advance_requests_v2)
app.register_blueprint(departments_v2_bp)
app.register_blueprint(shifts_v2)
app.register_blueprint(document_v2)
app.register_blueprint(leave_applications_v2)
app.register_blueprint(user_data_bp_v2)
app.register_blueprint(company_users_bp_v2)
app.register_blueprint(approval_work_flow_v2)
app.register_blueprint(attendance_bp_v2)
app.register_blueprint(company_data_bp_v2)
app.register_blueprint(irembo_bp_v2)

with app.app_context():
   # Get a variable informing us if the scheduler is supposed to run on this instance
   run_scheduler = os.getenv('RUN_SCHEDULER', 'False').lower() == 'true'
   # Run the scheduler if the environment variable is set to True
   # We wanna make sure the scheduler runs only on one instance
   if run_scheduler:
      app.logger.info("Starting scheduler on this instance.")
      start_scheduler()
   else:
      app.logger.info("Scheduler disabled for this instance.")
