from functools import wraps
from flask import request, jsonify, url_for, redirect, flash, session
from flask import current_app
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
from app.helpers.company_helpers import CompanyHelpers

load_dotenv()

def role_required(allowed_roles):
    """
    Decorator function to check if the user has one of the allowed roles and handle session expiration.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                timeout = os.getenv('SESSION_TIMEOUT')
                # Set the session timeout duration
                session_timeout = timedelta(minutes=int(timeout))
                role = session.get('role')

                # Check if the session has a 'last_activity' timestamp
                last_activity = session.get('last_activity')
                if last_activity and (role not in ['supervisor', 'employee']):
                    # Convert the timestamp back to a datetime object
                    last_activity = datetime.fromisoformat(last_activity)
                    # Check if the session has expired
                    if datetime.now() - last_activity > session_timeout:
                        # Session has expired
                        session.clear()  # Clear the session
                        flash('Your session has expired. Please log in again.', 'warning')
                        # Redirect the user to the right login page based on their role
                        if 'role' in session:
                            user_role = session.get('role')
                            if user_role in ['supervisor', 'employee']:
                                return redirect(url_for('company_users.login_company_user'))
                            return redirect(url_for('user_data.login'))
                        return redirect(url_for('user_data.login'))

                # Update the last activity timestamp
                session['last_activity'] = datetime.now().isoformat()

                # Check if the user's company has Attendance Microservice API access
                try:
                    api_key = CompanyHelpers.get_company_compreface_api_key()
                    # current_app.logger.info(f"API Key: {api_key} and the type is {type(api_key)}")
                except Exception as e:
                    current_app.logger.error(f"Error getting API Key: {e}")
                    api_key = None
                if api_key is None:
                    # Set the attanndance_service to True if the company has access to the Attendance Microservice API
                    attendance_service = False
                else:
                    attendance_service = True

                # Save the attendance_service status in the session
                session['attendance_service'] = attendance_service

                # Get the user role from the session
                user_role = session.get('role')
                if user_role:
                    # Check if the user has one of the allowed roles
                    if user_role in allowed_roles:
                        return f(*args, **kwargs)
                    else:
                        flash('You do not have permission to access this page.', 'danger')
                        if user_role in ['supervisor', 'employee']:
                            return redirect(url_for('company_users.login_company_user'))
                        return redirect(url_for('company_users.login_company_users'))
                else:
                    flash('You need to log in first.', 'warning')
                    return redirect(url_for('user_data.login'))
            except Exception as e:
                flash('An error occurred. Please try again.', 'danger')
                return redirect(url_for('user_data.login'))
        return decorated_function
    return decorator