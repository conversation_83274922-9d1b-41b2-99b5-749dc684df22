from functools import wraps
from flask import jsonify, url_for, redirect, flash, session
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

load_dotenv()

def admin_required(f):
    """
    Decorator function to check if the user is an Admin user.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            timeout = os.getenv('SESSION_TIMEOUT')
            # Set the session timeout duration 
            session_timeout = timedelta(minutes=int(timeout))

            # Check if the session has a 'last_activity' timestamp
            last_activity = session.get('last_activity')
            if last_activity:
                # Convert the timestamp back to a datetime object
                last_activity = datetime.fromisoformat(last_activity)
                # Check if the session has expired
                if datetime.now() - last_activity > session_timeout:
                    # Session has expired
                    session.clear()  # Clear the session
                    flash('Your session has expired. Please log in again.', 'warning')
                    return redirect(url_for('user_data.login'))

            # Update the last activity timestamp
            session['last_activity'] = datetime.now().isoformat()
            # Get the user data from the session
            user_role = session.get('role')
            if user_role:
                role = user_role.lower()
                # Check if the user is an Admin user
                if role == 'admin':
                    return f(*args, **kwargs)
                else:
                    flash('You do not have permission to access this page.', 'danger')
                    return redirect(url_for('user_data.login'))
            else:
                flash('You need to log in first.', 'warning')
                return redirect(url_for('user_data.login'))
        except Exception as e:
            flash('An error occurred. Please try again.', 'danger')
            return redirect(url_for('user_data.login'))
    return decorated_function