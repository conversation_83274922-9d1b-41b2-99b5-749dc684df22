from functools import wraps
from flask import jsonify, request, flash
from flask import session, url_for, redirect

def require_subscription(f):
    """This decorator checks if the user has an active subscription."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get has_access from session
        has_access = session.get('has_access')
        
        if not has_access:
            # Tell the user that they need to update their subscription
            # and redirect them to the appropriate dashboard
            message = "WE ARE SORRY! Your subscription has expired. Please update your subscription to continue using the service."
            flash(message, 'danger')
            # Redirect the user to the dashboard based on their role
            role = session.get('role')
            if role in ['hr', 'manager', 'company_hr', 'accountant']:
                return redirect(url_for('admin_data.dashboard'))
            elif role == 'employee':
                return redirect(url_for('admin_data.employee_dashboard'))
            else:
                return redirect(url_for('admin_data.supervisor_dashboard'))
        return f(*args, **kwargs)
    return decorated_function
