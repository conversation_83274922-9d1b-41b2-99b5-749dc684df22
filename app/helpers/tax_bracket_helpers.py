from app.models.central import TaxBracket
from app import db

class TaxOps():
    """Model representing a tax operation."""

    def __init__(self, lower_bound, upper_bound, rate):
        """Initialize the tax bracket object.
        Args:
            lower_bound (int): The lower bound of the tax bracket.
            upper_bound (int): The upper bound of the tax bracket.
            rate (float): The tax rate of the tax bracket."""
        self.lower_bound = lower_bound
        self.upper_bound = upper_bound
        self.rate = rate

    def insert_taxbracket(self):
        """Insert a tax bracket into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print("Error inserting tax bracket: ", e)
            return False
        
    @staticmethod
    def calculate_employee_contributions(employees, tax_brackets):
        """Calculate the tax contributions of employees.
        Args:
            employees (list): A list of employee objects.
            tax_brackets (list): A list of tax bracket objects.
        Returns:
            dict: A dictionary containing the employee contributions."""
        contributions_data = []
        for employee in employees:
            employee_contributions = {}
            employee_contributions['employee_id'] = employee.employee_id
            employee_contributions['employee_name'] = employee.first_name + " " + employee.last_name
            employee_contributions['employee_salary'] = employee.gross_salary
            employee_contributions['employee_contributions'] = 0
            for bracket in tax_brackets:
                if employee.salary > bracket.lower_bound and employee.salary <= bracket.upper_bound:
                    employee_contributions['employee_contributions'] = employee.salary * bracket.rate
                    break
            contributions_data.append(employee_contributions)
        return contributions_data
        