from flask import g, session, current_app

def get_common_data():
    """
    Helper function to provide common data for templates.
    Returns a dictionary with data like company name, username, etc.
    """
    # Fetch data from g, session, or database (replace with your logic)
    company_name = g.get('company_name', session.get('company_name', ''))
    username = g.get('username', session.get('username', 'Guest'))
    role = g.get('role', session.get('role', 'User'))
    first_name = g.get('first_name', session.get('first_name', 'N/A'))
    last_name = g.get('last_name', session.get('last_name', 'N/A'))
    role = g.get('role', session.get('role', 'User'))
    user = g.get('user', session.get('username', 'N/A'))
    company_id = g.get('company_id', session.get('company_id', ''))
    attendance_service = g.get('attendance_service', session.get('attendance_service', ''))
    company_logo = g.get('company_logo', session.get('company_logo', ''))
    companies = g.get('companies', session.get('companies', []))
    try:    
        common_data = {
            'company_name': company_name.upper(),
            'username': username,
            'role': role,
            'first_name': first_name.strip().capitalize(),
            'last_name': last_name.strip().capitalize(),
            'user': user,
            'company_id': company_id,
            'attendance_service': attendance_service or '',
            'company_logo': company_logo or '',
            'companies': companies,
        }
    except AttributeError as e:
        current_app.logger.error(f"Error fetching common data: {e}")
    return common_data