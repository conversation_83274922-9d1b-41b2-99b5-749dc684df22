

import re
from flask import request, session, jsonify, current_app, flash, redirect, url_for
from flask_jwt_extended import current_user, get_jwt, jwt_required
from app.models.central import RoutePlanRequirement, Plans, Company
# from app.models.company import 
from app.helpers.auxillary import Auxillary

def is_route_allowed(route_path, plan_id, is_for_api=False):
    """Check if the given route is accessible by the specified plan ID."""
    allowed_routes = RoutePlanRequirement.get_route_path_given_plan_id(plan_id)
    current_app.logger.info(f"Allowed routes: {allowed_routes}")

    if is_for_api and route_path.startswith('/api/v1/'):
        return True # Allow access for API routes - For development purposes
    
    # Loop through allowed patterns and check if any match the route_path
    for pattern in allowed_routes:
        # Strip spaces from the pattern to handle cases where routes have leading/trailing spaces
        clean_pattern = pattern.strip()
        if re.match(clean_pattern, route_path) or re.match(pattern, route_path):
            return True

    # Special case for send_payslip_email route
    if route_path.startswith('/send_payslip_email'):
        for pattern in allowed_routes:
            if ' /send_payslip_email' in pattern:
                return True

    return False
    #return route_path in allowed_routes

def restrict_based_on_plan():
    """Middleware to restrict access based on plan before each request."""
    route_path = request.path
    company_plan_id = session.get('company_plan_id')
    plan_name = session.get('company_plan')
    company_name = session.get('company_name')
    full_name = session.get('full_name')
    current_app.logger.info(f"Full name: {full_name}")
    username = session.get('username')
    current_app.logger.info(f"Username: {username}")
    first_name = session.get('first_name')
    last_name = session.get('last_name')
    full_name = first_name + " " + last_name
    current_app.logger.info(f"Full name: {full_name}")


    if not is_route_allowed(route_path, company_plan_id):
        message = f"""
        {full_name} from {company_name}  with the following plan: {plan_name}
        tried to access route {route_path} and was denied access.
        """
        current_app.logger.warning(message)
        # Send an email to the admin so that they can take action
        try:
            subject = "Unauthorized access to restricted route"
            recipient = "<EMAIL>"
            body = f"""
            Dear Admin,
           {message}
            """
            sent = Auxillary.send_netpipo_email(subject, recipient, body)
            current_app.logger.info(f"Email sent: {sent}")
        except Exception as e:
            current_app.logger.error(f"Error sending email: {e}")

        message = f"Your current plan {plan_name} does not allow access to this feature. Please upgrade to a higher plan."
        flash(message, 'danger')
        return redirect(url_for('irembo_v2.create_invoice'))

@jwt_required()
def jwt_restrict_based_on_plan():
    """Middleware to restrict access based on plan before each request."""
    route_path = request.path
    jwt_data = get_jwt()
    username = current_user.get('username')
    first_name = current_user.get('first_name')
    last_name = current_user.get('last_name')
    # full_name = current_user.get('full_name')
    full_name = first_name + " " + last_name
    company_plan_id = jwt_data.get('company_plan_id')
    plan_name = Plans.get_plan_by_id(company_plan_id).get('plan_name')
    company_name = Company.get_company_by_id(jwt_data.get('company_id')).get('company_name')
    current_app.logger.info(f"Full name: {full_name}")
    current_app.logger.info(f"Username: {username}")


    is_allowed = is_route_allowed(route_path, company_plan_id, is_for_api=True)
    if not is_allowed:
        message = f"""
            {full_name} from {company_name}  with the following plan: {plan_name}
            tried to access route {route_path} and was denied access.
        """
        current_app.logger.warning(message)
        # Send an email to the admin so that they can take action
        try:
            subject = "Unauthorized access to restricted route"
            recipient = "<EMAIL>"
            body = f"""
                Dear Admin,
                {message}
            """
            sent = Auxillary.send_netpipo_email(subject, recipient, body)
            current_app.logger.info(f"Email sent: {sent}")
        except Exception as e:
            current_app.logger.error(f"Error sending email: {e}")

        message = f"Your current plan \'{plan_name}\' does not allow access to this feature. Please upgrade to a higher plan."
        return jsonify({"message": message}), 403