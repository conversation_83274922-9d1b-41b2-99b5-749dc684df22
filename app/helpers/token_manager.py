from itsdangerous import URLSafeTimedSerializer
from flask import current_app

class TokenManager:
    def __init__(self):
        """Initialize the serializer using the app's SECRET_KEY."""
        self.serializer = None

    def _initialize(self):
        """Initialize the serializer using the app's SECRET_KEY."""
        if self.serializer is None:
            self.serializer = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    
    def generate_confirmation_token(self, email):
        """Generate a confirmation token for the given email."""
        self._initialize()
        return self.serializer.dumps(email, salt=current_app.config['SECURITY_PASSWORD_SALT'])
    
    def confirm_token(self, token, expiration=3600):
        """Confirm the token by decoding it."""
        self._initialize()
        try:
            email = self.serializer.loads(token, salt=current_app.config['SECURITY_PASSWORD_SALT'], max_age=expiration)
        except:
            return False
        return email
