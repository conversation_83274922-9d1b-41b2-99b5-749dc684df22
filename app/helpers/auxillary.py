"""random_password module."""
import secrets
import string
import os
import pyotp
import qrcode
from flask import current_app
from flask_mail import Message
from itsdangerous import URLSafeTimedSerializer
from email.utils import formataddr
from num2words import num2words
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MIMEB<PERSON>
from email import encoders
from threading import Thread
from decimal import Decimal, ROUND_HALF_UP
import os
import smtplib
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from mailjet_rest import Client
import requests
from flask import flash, redirect, url_for
from flask import current_app
from flask import render_template
from flask import request
from flask import jsonify
from flask import session
from flask import g
from flask import abort
from flask import make_response
import tempfile
import re
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, To, Email
from dotenv import load_dotenv
import fcntl


load_dotenv()

class Auxillary:
    """Auxillary class.
    Description:
    - This class provides helper functions that perform certain tasks.
    - The functions are static methods, so they can be called without creating an instance of the class.
    """
    MAX_CONCURRENT_EMAILS = 5  # Limit concurrent connections
    DELAY_BETWEEN_BATCHES = 10  # Seconds

    @staticmethod
    def random_password(length=10):
        """
        Generate a random temporary password.

        Parameters:
        - length (int): The length of the password. Default is 10.

        Returns:
        str: The generated temporary password.
        """
        characters = string.ascii_letters + string.digits
        temporary_password = ''.join(secrets.choice(characters) for _ in range(length))
        return temporary_password

    @staticmethod
    def generate_totp_qr(email):
        """
        Generate a Time-based One-Time Password (TOTP) QR code image and save it to a file.

        Args:
            email (str): The email of the user.

        Returns:
            tuple: A tuple containing the OTP key and the path to the saved QR code image.
        """
        key = pyotp.random_base32()
        totp = pyotp.TOTP(key)
        uri = totp.provisioning_uri(email, issuer_name="netpipo.com")

        save_dir = os.path.join("app", "static", "images")
        os.makedirs(save_dir, exist_ok=True)
        img_path = os.path.join(save_dir, f"{email}.png")
        qrcode.make(uri).save(img_path)

        return key, img_path

    @staticmethod
    def verify_totp(token, key):
        """
        Verify a Time-based One-Time Password (TOTP) token.

        Args:
            key (str): The base32-encoded secret key.
            token (str): The TOTP token to verify.

        Returns:
            bool: True if the token is valid, False otherwise.
        """
        totp = pyotp.TOTP(key)
        return totp.verify(token)

    @staticmethod
    def send_email(subject, recipients, body):
        current_year = datetime.now().year
        logo_url = "www.netpipo.com"
        logo_image = "https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/net_logo_sas.png"
        font_style_link = "https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap"
        def email_task(batch):
            sender_email = os.getenv('sender_email')
            password = os.getenv('email_password')

            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = ", ".join(batch)  # Display all recipients in header
            msg['Subject'] = subject

            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="{ font_style_link }" rel="stylesheet">
            <title>
                    Email from Netpipo
            </title>
                <style>
                    body {{
                    font-family: Jost, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f0f6f5;
                    line-height: 1;
                    }}
                    .container {{
                    max-width: 80%;
                    margin: 20px auto;
                    background-color: #ffffff;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                    }}
                    .header {{
                    text-align: left;
                    padding: 20px 10px;
                    border-bottom: 2px solid #17B8A6;
                    }}
                    .content h1{{
                    color: #374957
                    }}
                    .content h3{{
                    color: #374957
                    }}
                    .header h1 {{
                    margin: 0;
                    }}
                    .content {{
                    padding: 20px;
                    }}
                    .content h2 {{
                    color: #333333;
                    }}
                    .content p {{
                    line-height: 1.6;
                    color:#6C757D;
                    }}
                    .cta {{
                    text-align: center;
                    margin: 20px 0;
                    }}
                    .cta a {{
                    background-color: #17B8A6;
                    color: white;
                    text-decoration: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    }}
                    .footer {{
                    background-color: #17B8A6;
                    color: #ffffff;
                    text-align: center;
                    padding: 5px 10px;
                    font-size: 14px;
                    }}
                    .footer a {{
                    text-decoration: none;
                    color: #ffffff;
                    }}
                    .footer a:hover{{
                    text-decoration: underline;
                    }}
                    .logo img {{
                    width: 100px;
                    }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 10px; text-align: left; }}
                    th {{ background-color: #ebfaf0; color:#25a38b;
                    }}
                </style>
            </head>
            <body>
            <div class="container">
                <!-- Header -->
                <div class="header">
                    <div class="logo">
                        <a href="{logo_url}"><img src="{logo_image}" alt="netpipo" border="0"></a>
                    </div>
                </div>
                <!-- Content -->
                <div class="content">
                    <h1>{ subject }</h1>

                { body }
                <p>
                    If you have any questions, feel free to reach out to our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.
                </p>
                <p>
                    Cheers, <br>
                    The Netpipo Team
                </p>
                </div>

                <!-- Footer -->
                <div class="footer">
                <p>&copy; { current_year } Netpipo. All rights reserved.</p>
                <p>
                    <a href="netpipo.com/privacy" target=_blank>Privacy Policy</a> | <a href="netpipo.com/terms">Terms of Service</a> | <a href="netpipo.com/home">Home</a>
                </p>
                </div>
            </div>
            </body>
            </html>

            """

            msg.attach(MIMEText(html_body, 'html'))

            try:
                with smtplib.SMTP('smtp.office365.com', 587) as server:
                    server.starttls()
                    server.login(sender_email, password)
                    server.sendmail(sender_email, batch, msg.as_string())  # Send email to batch
                print(f"Email sent to {', '.join(batch)} successfully!")
            except Exception as e:
                print(f"Error sending email to {', '.join(batch)}: {e}")

        if isinstance(recipients, str):
            recipients = [recipients]

        with ThreadPoolExecutor(max_workers=Auxillary.MAX_CONCURRENT_EMAILS) as executor:
            for i in range(0, len(recipients), Auxillary.MAX_CONCURRENT_EMAILS):
                batch = recipients[i:i + Auxillary.MAX_CONCURRENT_EMAILS]
                executor.submit(email_task, batch)
                time.sleep(Auxillary.DELAY_BETWEEN_BATCHES)  # Delay between batches

    @staticmethod
    def send_netpipo_email(subject, to_email, html_content):
        """Send an email using netpipo's email service."""
        service_url = os.getenv('NETPIPO_EMAIL_SERVICE_URL')
        if not service_url:
            current_app.logger.error("NETPIPO_EMAIL_SERVICE_URL is not set in environment variables.")
            return False
        
        # send a POST request to the email service
        try:
            response = requests.post(
                service_url,
                json={
                    'subject': subject,
                    'recipients': to_email,
                    'body': html_content
                }
            )
            if response.status_code == 200:
                # current_app.logger.info(f"Email sent via api successfully to {to_email}.")
                return True
            else:
                current_app.logger.error(f"Failed to send email. Status code: {response.status_code}, Response: {response.text}")
                return False
        except requests.RequestException as e:
            current_app.logger.error(f"An error occurred while sending email: {e}")
            return False

    @staticmethod
    def send_netpipo_email_attachment(subject, recipient, body, attachment_path):
        """Send an email with an attachment using the netpipo's email service."""
        service_url = "https://netpipo.com/send_email_netpipo_with_attachment"
        current_year = datetime.now().year
        logo_url = "www.netpipo.com"
        logo_image = "https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/net_logo_sas.png"
        font_style_link = "https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap"
        if not service_url:
            current_app.logger.error("NETPIPO_EMAIL_SERVICE_URL is not set in environment variables.")
            return False
        html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="{ font_style_link }" rel="stylesheet">
            <title>
                    Email from Netpipo
            </title>
                <style>
                    body {{
                    font-family: Jost, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f0f6f5;
                    line-height: 1;
                    }}
                    .container {{
                    max-width: 80%;
                    margin: 20px auto;
                    background-color: #ffffff;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                    }}
                    .header {{
                    text-align: left;
                    padding: 20px 10px;
                    border-bottom: 2px solid #17B8A6;
                    }}
                    .content h1{{
                    color: #374957
                    }}
                    .content h3{{
                    color: #374957
                    }}
                    .header h1 {{
                    margin: 0;
                    }}
                    .content {{
                    padding: 20px;
                    }}
                    .content h2 {{
                    color: #333333;
                    }}
                    .content p {{
                    line-height: 1.6;
                    color:#6C757D;
                    }}
                    .cta {{
                    text-align: center;
                    margin: 20px 0;
                    }}
                    .cta a {{
                    background-color: #17B8A6;
                    color: white;
                    text-decoration: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    }}
                    .footer {{
                    background-color: #17B8A6;
                    color: #ffffff;
                    text-align: center;
                    padding: 5px 10px;
                    font-size: 14px;
                    }}
                    .footer a {{
                    text-decoration: none;
                    color: #ffffff;
                    }}
                    .footer a:hover{{
                    text-decoration: underline;
                    }}
                    .logo img {{
                    width: 100px;
                    }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 10px; text-align: left; }}
                    th {{ background-color: #ebfaf0; color:#25a38b;
                    }}
                </style>
            </head>
            <body>
            <div class="container">
                <!-- Header -->
                <div class="header">
                    <div class="logo">
                        <a href="{logo_url}"><img src="{logo_image}" alt="netpipo" border="0"></a>
                    </div>
                </div>
                <!-- Content -->
                <div class="content">
                    <h1>{ subject }</h1>

                { body }
                <p>
                    If you have any questions, feel free to reach out to our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.
                </p>
                <p>
                    Cheers, <br>
                    The Netpipo Team
                </p>
                </div>

                <!-- Footer -->
                <div class="footer">
                <p>&copy; { current_year } Netpipo. All rights reserved.</p>
                <p>
                    <a href="netpipo.com/privacy" target=_blank>Privacy Policy</a> | <a href="netpipo.com/terms">Terms of Service</a> | <a href="netpipo.com/home">Home</a>
                </p>
                </div>
            </div>
            </body>
            </html>

            """
        
        # send a POST request to the email service
        try:
            with open(attachment_path, 'rb') as file:
                files = {'attachment': file}
                data = {
                    'subject': subject,
                    'recipients': recipient,
                    'body': html_body
                }
                response = requests.post(
                    service_url,
                    data=data,
                    files=files
                )
            current_app.logger.info(f"Email service response status: {response.status_code}")
            current_app.logger.info(f"Email service response: {response.text}")

            if response.status_code == 200:
                current_app.logger.info(f"Email with attachment sent successfully to {recipient}.")
                return True
            else:
                current_app.logger.error(f"Failed to send email with attachment. Status code: {response.status_code}, Response: {response.text}")
                return False
        except requests.RequestException as e:
            current_app.logger.error(f"An error occurred while sending email with attachment: {e}")
            return False
        except Exception as e:
            current_app.logger.error(f"Unexpected error while sending email with attachment: {e}")
            return False

    @staticmethod
    def get_serializer():
        """Get a URLSafeTimedSerializer object."""
        return URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    @staticmethod
    def generate_verification_token(email):
        """Generate a verification token to be sent to the users.
        Make sure the token is linked with the email.
        """
        serializer = Auxillary.get_serializer()
        return serializer.dumps(email, salt=current_app.config['SECURITY_PASSWORD_SALT'])

    @staticmethod
    def number_to_words(value):
        """Put numbers in words"""
        try:
            # Handle None or zero values
            if value is None:
                return "Zero"

            # Convert to float and check if zero
            try:
                float_value = float(value)
                if float_value == 0:
                    return "Zero"
            except (ValueError, TypeError):
                return "Zero"

            # Convert the number to words with the 'and' between hundreds and tens
            words = num2words(float_value, lang='en')

            # Safety check for words
            if words is None:
                return str(float_value)

            # Remove commas
            words = words.replace(',', '')

            # Capitalize each word in the resulting string
            capitalized_words = ' '.join(word.capitalize() for word in words.split())
            return capitalized_words
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting number to words: {e}")
            # Return a string representation of the value instead of None
            try:
                return str(float(value))
            except:
                return "Zero"

    @staticmethod
    def format_amount(amount):
        """Format the amount to have commas with no decimal places.
        Args:
            amount (int): The amount to format.
            Returns:
            str: The formatted amount.
        """
        return "{:,.0f}".format(amount)
    
    @staticmethod
    def calculate_days_difference(start_date, end_date):
        """
        Calculate the difference in days between two dates.

        Args:
            start_date (str): The start date in the format 'YYYY-MM-DD'.
            end_date (str): The end date in the format 'YYYY-MM-DD'.

        Returns:
            int: The number of days between the start and end dates.
        """
        print("Received: ", start_date, end_date)
        try:
            start = datetime.strptime(str(start_date), '%Y-%m-%d')
            end = datetime.strptime(str(end_date), '%Y-%m-%d')
            if end < start:
                raise ValueError("Start can not be greater than end.")
            print("This is end and start", start ,end , (end-start).days)
            return ((end - start).days) + 1
        except Exception as e:
            print("Error calculating days btn two dates: ", e)
            raise Exception(str(e))
    
    @staticmethod
    def to_decimal(value):
        """Convert a value to Decimal safely."""
        if value is None:
            return Decimal('0.00')
        if isinstance(value, Decimal):
            return value
        return Decimal(str(value))

    @staticmethod
    def round_to_decimal(value):
        """Round a number to the nearest whole number."""
        #current_app.logger.info(f"Rounding value: {value} (type: {type(value)})")
        decimal_value = Decimal(str(value))
        result = decimal_value.quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        return result
    
    @staticmethod
    def format_decimal(value):
        """Format a decimal value to one decimal places."""
        try:
            if isinstance(value, str):
                value = float(value)
            formatted_value = "{:,.1f}".format(value)
            return formatted_value
        except (ValueError, TypeError):
            current_app.logger.error(f"Error formatting value: {value}")
            return "0.00"

    @staticmethod
    def send_email_attachment(subject, recipient, body, attachment_path):
        """Send an email with an attachment."""
        # Define your email parameters
        sender_email = os.getenv('sender_email')
        password = os.getenv('email_password')
        # Create the MIMEMultipart email object
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient
        msg['Subject'] = subject
        # Attach the body of the email
        msg.attach(MIMEText(body, 'plain'))
        # Attach the file to the email
        try:
            with open(attachment_path, 'rb') as file:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(file.read())

            # Encode the attachment to base64
            encoders.encode_base64(part)
            # Add appropriate headers with the custom filename
            filename = os.path.basename(attachment_path)
            current_app.logger.info(f"Using attachment filename: {filename}")
            part.add_header('Content-Disposition', f'attachment; filename="{filename}"')
            # Attach the part to the email
            msg.attach(part)
        except Exception as e:
            print(f"Error attaching file: {e}")
            return
        try:
            # Establish a connection with the Outlook SMTP server
            with smtplib.SMTP('smtp.office365.com', 587) as server:
                server.starttls()  # Secure the connection
                server.login(sender_email, password)  # Login to your Outlook account
                server.sendmail(sender_email, recipient, msg.as_string())  # Send the email
                print("Email sent successfully!")
        except Exception as e:
            print(f"Error sending email: {e}")

        
    @classmethod
    def send_payslip_from_spaces(cls, employee_id, file_url, employee_email, subject, body):
        """Send payslip from the digital ocean spaces to the employee."""
        try:
            # Step 1: Download the file from Spaces
            response = requests.get(file_url)
            if response.status_code != 200:
                current_app.logger.error(f"Failed to download payslip from {file_url}")
                flash("Unable to fetch payslip from storage.", 'danger')
                return redirect(url_for('employees.single_employee_payslip', employee_id=employee_id))

            # Step 2: Extract filename from the URL
            filename = file_url.split("/")[-1]
            temp_path = os.path.join(tempfile.gettempdir(), filename)

            # Step 3: Save the file to a temporary location
            with open(temp_path, 'wb') as f:
                f.write(response.content)

            # Step 4: Send the email with attachment
            Auxillary.send_netpipo_email_attachment(
                subject=subject,
                recipient=employee_email,
                body=body,
                attachment_path=temp_path
            )

            # Step 5: Cleanup
            os.unlink(temp_path)
            flash(f"Payslip sent to {employee_email} successfully.", 'success')
            current_app.logger.info(f"Payslip sent to {employee_email} successfully.")
            return True

        except Exception as e:
            current_app.logger.error(f"An error occurred while sending payslip: {e}")
            flash("An error occurred while sending the payslip.", 'danger')
            return False
        
    @staticmethod
    def is_scheduler_locked(lock_file_path='/tmp/scheduler.lock'):
        try:
            lock_file = open(lock_file_path, 'w')
            fcntl.flock(lock_file, fcntl.LOCK_EX | fcntl.LOCK_NB)
            return False, lock_file  # Lock acquired
        except BlockingIOError:
            return True, None  # Lock already held



if __name__ == "__main__":
    subject = "Test Email"
    recipients = "<EMAIL>"
    body = "This is a test email from Mailjet."
    try:
        sent = Auxillary.send_mailjet_email(subject, recipients, body)
        print("Email sent successfully!")
    except Exception as e:
        print(f"Error sending email: {e}")

    #print(sent)
    #email = "<EMAIL>"

    #token = Auxillary.generate_verification_token(email)
    #print("token", token)
