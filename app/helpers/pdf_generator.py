"""PDF Generator module for generating PDFs from HTML templates."""
import os
from io import BytesIO
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from flask import current_app
from decimal import Decimal
from app.helpers.auxillary import Auxillary

class PDFGenerator:
    """PDF Generator class for generating PDFs from data."""

    @staticmethod
    def generate_payslip_pdf(emp_data, company_data, pay_date, first_day, last_day, days_in_month, prepared_by):
        """Generate a PDF payslip for an employee.

        Args:
            emp_data (dict): Employee data including salary details
            company_data (dict): Company information
            pay_date (datetime): The pay date
            first_day (str): First day of the pay period
            last_day (str): Last day of the pay period
            days_in_month (int): Number of days in the month
            prepared_by (str): Name of the person who prepared the payslip

        Returns:
            BytesIO: PDF file as a BytesIO object
        """
        # Log input parameters for debugging
        current_app.logger.info(f"generate_payslip_pdf called with: pay_date={pay_date}, first_day={first_day}, last_day={last_day}")
        current_app.logger.info(f"emp_data keys: {emp_data.keys() if emp_data else 'None'}")
        current_app.logger.info(f"company_data keys: {company_data.keys() if company_data else 'None'}")
        current_app.logger.info(f"emp_data_values: {emp_data.values() if emp_data else 'None'}")
        try:
            # Create a BytesIO buffer to receive the PDF data
            buffer = BytesIO()

            # Create the PDF document with smaller margins to fit more content
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=20,
                leftMargin=20,
                topMargin=20,
                bottomMargin=20
            )

            # Get styles
            styles = getSampleStyleSheet()

            # Define colors to match the CSS
            primary_color = colors.HexColor('#cce8e3')
            secondary_color = colors.HexColor('#25a38b')
            text_color = colors.HexColor('#525252')
            background_color = colors.HexColor('#eff3f3')

            # Create custom styles with smaller font sizes to fit more content
            title_style = ParagraphStyle(
                'TitleStyle',
                parent=styles['Title'],
                fontSize=22,  # Reduced from 28
                alignment=1,  # Center alignment
                textColor=secondary_color,
                fontName='Helvetica-Bold',
                spaceAfter=6  # Reduced from 12
            )

            header_style = ParagraphStyle(
                'HeaderStyle',
                parent=styles['Heading1'],
                fontSize=14,  # Reduced from 16
                alignment=1,  # Center alignment
                textColor=colors.white,
                fontName='Helvetica-Bold',
                spaceAfter=6,  # Reduced from 12
                backColor=secondary_color
            )

            label_style = ParagraphStyle(
                'LabelStyle',
                parent=styles['Normal'],
                fontSize=8,  # Reduced from 10
                fontName='Helvetica-Bold',
                textColor=text_color,
                backColor=background_color
            )

            value_style = ParagraphStyle(
                'ValueStyle',
                parent=styles['Normal'],
                fontSize=8,  # Reduced from 10
                textColor=text_color
            )

            normal_style = ParagraphStyle(
                'NormalStyle',
                parent=styles['Normal'],
                fontSize=8,  # Reduced from 10
                textColor=text_color
            )

            company_label_style = ParagraphStyle(
                'CompanyLabelStyle',
                parent=styles['Normal'],
                fontSize=8,  # Reduced from 10
                fontName='Helvetica-Bold',
                textColor=text_color,
                backColor=primary_color
            )

            company_value_style = ParagraphStyle(
                'CompanyValueStyle',
                parent=styles['Normal'],
                fontSize=8,  # Reduced from 10
                textColor=text_color,
                backColor=primary_color
            )

            footer_header_style = ParagraphStyle(
                'FooterHeaderStyle',
                parent=styles['Normal'],
                fontSize=12,
                fontName='Helvetica-Bold',
                textColor=secondary_color,
                backColor=primary_color,
                alignment=0  # Left alignment
            )

            footer_text_style = ParagraphStyle(
                'FooterTextStyle',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color
            )

            disclaimer_style = ParagraphStyle(
                'DisclaimerStyle',
                parent=styles['Normal'],
                fontSize=8,
                fontName='Helvetica-Bold',
                textColor=text_color,
                alignment=0  # Left alignment
            )

            # Elements to add to the PDF
            elements = []

            # Create a header with logo and title side by side
            header_data = [[]]

            # Add company logo if available
            current_app.logger.info(f"Company logo data: {company_data.get('logo')}")

            if company_data.get('logo'):
                try:
                    logo_path = company_data['logo']
                    current_app.logger.info(f"Logo path: {logo_path}")

                    # Check if it's a URL (starts with http or https)
                    if logo_path.startswith(('http://', 'https://')) or 'digitaloceanspaces.com' in logo_path:
                        # Use the full URL directly
                        current_app.logger.info(f"Detected URL: {logo_path}")

                        # For URLs, we need to use a different approach
                        import urllib.request
                        try:
                            # Download the image from the URL
                            current_app.logger.info(f"Attempting to download logo from URL: {logo_path}")

                            # URL encode the logo path to handle spaces and special characters
                            import urllib.parse

                            # Parse the URL
                            parsed_url = urllib.parse.urlparse(logo_path)

                            # Encode the path component
                            encoded_path = urllib.parse.quote(parsed_url.path)

                            # Reconstruct the URL with the encoded path
                            encoded_url = urllib.parse.urlunparse((
                                parsed_url.scheme,
                                parsed_url.netloc,
                                encoded_path,
                                parsed_url.params,
                                parsed_url.query,
                                parsed_url.fragment
                            ))

                            current_app.logger.info(f"Encoded URL: {encoded_url}")

                            # Create a proper request with headers to avoid 403 errors
                            req = urllib.request.Request(
                                encoded_url,
                                data=None,
                                headers={
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                                }
                            )

                            response = urllib.request.urlopen(req)
                            logo_data = response.read()
                            current_app.logger.info(f"Downloaded logo data size: {len(logo_data)} bytes")

                            # Create an in-memory image
                            logo_stream = BytesIO(logo_data)
                            img = Image(logo_stream, width=2*inch, height=1*inch)
                            header_data[0].append(img)
                            current_app.logger.info("Successfully added logo from URL to PDF")
                        except Exception as e:
                            current_app.logger.error(f"Error downloading logo from URL: {e}")
                            header_data[0].append(Paragraph("", normal_style))
                    else:
                        # Fallback for legacy logos stored on disk
                        logo_path = os.path.join(current_app.root_path, 'static', 'uploads', 'logos', company_data['logo'])
                        current_app.logger.info(f"Local logo path: {logo_path}")

                        # Add logo to the PDF if the file exists
                        if os.path.exists(logo_path):
                            current_app.logger.info(f"Logo file exists at: {logo_path}")
                            img = Image(logo_path, width=2*inch, height=1*inch)
                            header_data[0].append(img)
                            current_app.logger.info("Successfully added logo from local file to PDF")
                        else:
                            current_app.logger.warning(f"Logo file does not exist at: {logo_path}")
                            header_data[0].append(Paragraph("", normal_style))
                except Exception as e:
                    current_app.logger.error(f"Error adding logo to PDF: {e}")
                    header_data[0].append(Paragraph("", normal_style))
            else:
                current_app.logger.warning("No logo found in company data")
                header_data[0].append(Paragraph("", normal_style))

            # Add payslip title
            header_data[0].append(Paragraph("PAYSLIP", title_style))

            # Create a table for the header
            header_table = Table(header_data, colWidths=[2*inch, 5*inch])
            header_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, 0), 'LEFT'),
                ('ALIGN', (1, 0), (1, 0), 'CENTER'),
            ]))

            elements.append(header_table)
            elements.append(Spacer(1, 0.1*inch))  # Reduced from 0.2*inch

            # Helper functions for safe value handling
            def safe_str(value, default=''):
                if value is None:
                    return default
                return str(value)

            def safe_float(value, default=0):
                if value is None:
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    current_app.logger.warning(f"Invalid value for conversion to float: {value}")
                    return default

            # Add company details in two columns like in the HTML template
            company_left_data = [
                [Paragraph("Name:", company_label_style), Paragraph(safe_str(company_data.get('company_name')), company_value_style)],
                [Paragraph("TIN:", company_label_style), Paragraph(safe_str(company_data.get('company_tin')), company_value_style)],
                [Paragraph("RSSB No:", company_label_style), Paragraph(safe_str(company_data.get('rssb_number')), company_value_style)]
            ]

            company_right_data = [
                [Paragraph("Phone:", company_label_style), Paragraph(safe_str(company_data.get('phone_number')), company_value_style)],
                [Paragraph("Email:", company_label_style), Paragraph(safe_str(company_data.get('email')), company_value_style)],
                [Paragraph("Address:", company_label_style), Paragraph(f"{safe_str(company_data.get('province'))}, {safe_str(company_data.get('district'))}, {safe_str(company_data.get('sector'))}", company_value_style)]
            ]

            # Create two tables for company details
            company_left_table = Table(company_left_data, colWidths=[1*inch, 2.5*inch])
            company_left_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('BACKGROUND', (0, 0), (-1, -1), primary_color),
                ('GRID', (0, 0), (-1, -1), 0, colors.white),
            ]))

            company_right_table = Table(company_right_data, colWidths=[1*inch, 2.5*inch])
            company_right_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('BACKGROUND', (0, 0), (-1, -1), primary_color),
                ('GRID', (0, 0), (-1, -1), 0, colors.white),
            ]))

            # Create a table to hold both company tables side by side
            company_table = Table([[company_left_table, company_right_table]], colWidths=[3.5*inch, 3.5*inch])
            company_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                ('TOPPADDING', (0, 0), (-1, -1), 0),
            ]))

            elements.append(company_table)
            elements.append(Spacer(1, 0.1*inch))  # Reduced from 0.2*inch

            # Add payslip title with styled header
            title_data = [[Paragraph(f"EMPLOYEE PAYSLIP FOR THE MONTH OF {pay_date.strftime('%B %Y').upper()}", header_style)]]
            title_table = Table(title_data, colWidths=[7*inch])
            title_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), secondary_color),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.white),
            ]))
            elements.append(title_table)
            elements.append(Spacer(1, 0.1*inch))  # Reduced from 0.2*inch

            # Add employee details
            employee = emp_data['employee']

            # Create a more compact employee details table with fewer rows
            employee_data_table = [
                [Paragraph("Employee Name:", label_style), Paragraph(f"{safe_str(employee.get('first_name'))} {safe_str(employee.get('last_name'))}", value_style),
                 Paragraph("Bank Name:", label_style), Paragraph(safe_str(employee.get('bank_name')), value_style)],
                [Paragraph("Position/Dept:", label_style), Paragraph(f"{safe_str(employee.get('job_title'))} / {safe_str(employee.get('department'))}", value_style),
                 Paragraph("Bank Account:", label_style), Paragraph(safe_str(employee.get('bank_account')), value_style)],
                [Paragraph("ID/RSSB:", label_style), Paragraph(f"{safe_str(employee.get('nid'))} / {safe_str(employee.get('nsf'))}", value_style),
                 Paragraph("Amount to Credit:", label_style), Paragraph(f"RWF {format_amount(emp_data.get('net_salary_value', 0) + emp_data.get('total_reimbursements', 0) - emp_data.get('total_deductions', 0) - emp_data.get('brd_deduction', 0) - emp_data.get('salary_advance', 0))}", value_style)],
                [Paragraph("Contact:", label_style), Paragraph(f"Tel: {safe_str(employee.get('phone'))} | Email: {safe_str(employee.get('email'))}", value_style),
                 Paragraph("Pay Period:", label_style), Paragraph(f"From {safe_str(first_day)} to {safe_str(last_day)}", value_style)]
            ]

            employee_table = Table(employee_data_table, colWidths=[1.5*inch, 2*inch, 1.5*inch, 2*inch])
            employee_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('ALIGN', (2, 0), (2, -1), 'LEFT'),
                ('ALIGN', (3, 0), (3, -1), 'LEFT'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (0, -1), background_color),
                ('BACKGROUND', (2, 0), (2, -1), background_color),
            ]))

            elements.append(employee_table)
            elements.append(Spacer(1, 0.1*inch))  # Reduced from 0.2*inch

            # Add earnings and deductions with styled header
            earnings_header = [[Paragraph("Monthly Earnings", header_style), Paragraph("Monthly Deductions", header_style)]]
            earnings_header_table = Table(earnings_header, colWidths=[3.5*inch, 3.5*inch])
            earnings_header_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), secondary_color),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.white),
            ]))
            elements.append(earnings_header_table)
            elements.append(Spacer(1, 0.1*inch))

            # Create earnings and deductions data
            earnings_deductions_data = [
                [Paragraph("EARNINGS", label_style), Paragraph("AMOUNT", label_style),
                 Paragraph("DEDUCTIONS", label_style), Paragraph("AMOUNT", label_style)],
                [Paragraph("Basic Salary", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('basic_needed', 0))}", normal_style),
                 Paragraph("Pay as you Earn (PAYE)", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('paye', 0))}", normal_style)],
                [Paragraph("Transport Allowance", normal_style), Paragraph(f"RWF {format_amount(employee.get('transport_allowance', 0))}", normal_style),
                 Paragraph("Pension Contribution", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('pension_ee_value', 0))}", normal_style)],
                [Paragraph("Living Allowance", normal_style), Paragraph(f"RWF {format_amount(employee.get('housing_allowance', 0))}", normal_style),
                 Paragraph("Maternity Contribution", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('maternity_ee_value', 0))}", normal_style)],
                [Paragraph("Tel & Communication Allowance", normal_style), Paragraph(f"RWF {format_amount(employee.get('communication_allowance', 0))}", normal_style),
                 Paragraph("Medical Contribution", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('rama_ee', 0))}", normal_style)],
                [Paragraph("Overtime and Bonus", normal_style), Paragraph(f"RWF {format_amount(employee.get('over_time', 0))}", normal_style),
                 Paragraph("RSSB CBHI", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('cbhi_value', 0))}", normal_style)],
                [Paragraph("Other Allowances", normal_style), Paragraph(f"RWF {format_amount(employee.get('other_allowance', 0))}", normal_style),
                 Paragraph("Other Deductions", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('total_deductions', 0))}", normal_style)],
                [Paragraph("", normal_style), Paragraph("", normal_style),
                 Paragraph("BRD Deduction", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('brd_deduction', 0))}", normal_style)],
                [Paragraph("", normal_style), Paragraph("", normal_style),
                 Paragraph("Salary Advance", normal_style), Paragraph(f"RWF {format_amount(emp_data.get('salary_advance', 0))}", normal_style)],
                [Paragraph("GROSS SALARY (GS)", label_style), Paragraph(f"RWF {format_amount(emp_data.get('gross_needed', 0))}", label_style),
                 Paragraph("Total Payroll Deductions (TPD)", label_style), Paragraph(f"RWF {format_amount(safe_float(emp_data.get('total_deductions_value', 0)) + safe_float(emp_data.get('total_deductions', 0)) + safe_float(emp_data.get('cbhi_value', 0)) + safe_float(emp_data.get('brd_deduction', 0)) + safe_float(emp_data.get('salary_advance', 0)))}", label_style)]
            ]

            earnings_table = Table(earnings_deductions_data, colWidths=[2*inch, 1.5*inch, 2*inch, 1.5*inch])
            earnings_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (3, 0), background_color),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('ALIGN', (2, 0), (2, -1), 'LEFT'),
                ('ALIGN', (3, 0), (3, -1), 'RIGHT'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('FONTNAME', (0, 0), (3, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, -1), (3, -1), 'Helvetica-Bold'),
                ('BACKGROUND', (0, -1), (1, -1), background_color),
                ('BACKGROUND', (2, -1), (3, -1), background_color),
            ]))

            elements.append(earnings_table)
            elements.append(Spacer(1, 0.1*inch))  # Reduced from 0.2*inch

            # Add summary
            try:

                # Get all values safely
                net_salary_value = safe_float(emp_data.get('net_salary_value'))
                total_deductions = safe_float(emp_data.get('total_deductions'))
                brd_deduction = safe_float(emp_data.get('brd_deduction'))
                salary_advance = safe_float(emp_data.get('salary_advance'))
                total_reimbursements = safe_float(emp_data.get('total_reimbursements'))

                # Calculate net salary
                net_salary = net_salary_value - total_deductions - brd_deduction

                # Calculate net to pay
                net_to_pay = net_salary + total_reimbursements - salary_advance

                # Ensure values are not negative
                net_salary = max(0, net_salary)
                net_to_pay = max(0, net_to_pay)

                current_app.logger.info(f"Calculated net_salary: {net_salary}, net_to_pay: {net_to_pay}")
            except Exception as e:
                current_app.logger.error(f"Error calculating summary values: {e}")
                net_salary = 0
                total_reimbursements = 0
                net_to_pay = 0


            try:
                words_amount = Auxillary.number_to_words(net_to_pay)
                current_app.logger.info(f"Converted amount to words: {words_amount}")
                converted = True
            except Exception as e:
                current_app.logger.error(f"Error converting amount to words: {e}")
                words_amount = net_to_pay
                converted = False

            # Format the amount as a number with commas
            try:
                formatted_amount = "{:,.0f}".format(net_to_pay)
                # Add extra space after the amount in words
                amount_in_words = f"Amount in Words: {words_amount} Rwandan Francs    " if converted else f""
            except Exception as e:
                current_app.logger.error(f"Error formatting amount: {e}")
                amount_in_words = f"Amount: {net_to_pay} Rwandan Francs    "

            current_app.logger.info(f"Using formatted amount: {amount_in_words}")

            # Create a styled summary table
            summary_data = [
                [Paragraph("Net Salary (NS) = (GS-TPD)", label_style), Paragraph(f"RWF {format_amount(net_salary)}", normal_style)],
                [Paragraph("Total Reimbursements (Rbs)", label_style), Paragraph(f"RWF {format_amount(total_reimbursements)}", normal_style)],
                [Paragraph("Net to Pay (NS+Rbs)", label_style), Paragraph(f"RWF {format_amount(net_to_pay)}", normal_style)],
                [Paragraph(amount_in_words, label_style), Paragraph("", normal_style)]
            ]

            summary_table = Table(summary_data, colWidths=[5*inch, 2*inch])
            summary_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('SPAN', (0, 3), (1, 3)),  # Span the last row across both columns
                ('ALIGN', (0, 3), (0, 3), 'LEFT'),
                ('BACKGROUND', (0, 0), (0, -1), background_color),
            ]))

            elements.append(summary_table)
            elements.append(Spacer(1, 0.1*inch))  # Reduced from 0.5*inch

            # Add a simplified footer with signatures
            footer_border = Table([["", ""]], colWidths=[7*inch], rowHeights=[2])
            footer_border.setStyle(TableStyle([
                ('LINEABOVE', (0, 0), (-1, 0), 1, secondary_color),
            ]))
            elements.append(footer_border)

            # Create a compact signature line
            signature_style = ParagraphStyle(
                'SignatureStyle',
                parent=styles['Normal'],
                fontSize=7,  # Even smaller font
                textColor=text_color
            )

            # Create a single row with employee and prepared by (removed approved by)
            signature_data = [
                [Paragraph(f"Employee: {safe_str(employee.get('first_name')).upper()} {safe_str(employee.get('last_name')).upper()}", signature_style),
                 Paragraph(f"Prepared by: {safe_str(prepared_by)}", signature_style)]
            ]

            signature_table = Table(signature_data, colWidths=[3.5*inch, 3.5*inch])
            signature_table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
                ('TOPPADDING', (0, 0), (-1, -1), 4),
            ]))
            elements.append(signature_table)

            # Add the full disclaimer with "Disclaimer" in red
            disclaimer_style = ParagraphStyle(
                'DisclaimerStyle',
                parent=styles['Normal'],
                fontSize=6,
                fontName='Helvetica'
            )

            # Use HTML formatting to make "Disclaimer" red
            disclaimer_text = f"<font color='red'>Disclaimer:</font> This document is a system-generated payslip from {safe_str(company_data.get('company_name'))} and has been prepared in accordance with current Rwandan laws. If you find any errors, please contact our HR or finance department. Unauthorized disclosure, use, reproduction, sharing, or distribution of this document to third parties without prior written approval is prohibited. © {safe_str(company_data.get('company_name'))}, all rights reserved."
            disclaimer = Paragraph(disclaimer_text, disclaimer_style)
            elements.append(disclaimer)

            # Build the PDF
            doc.build(elements)

            # Reset the buffer position to the beginning
            buffer.seek(0)
            return buffer

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            current_app.logger.error(f"Error generating payslip PDF: {e}")
            current_app.logger.error(f"Traceback: {error_traceback}")

            # Create a simple PDF with error information instead of failing
            try:
                buffer = BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=A4)
                styles = getSampleStyleSheet()
                elements = []

                elements.append(Paragraph("Error Generating Payslip", styles['Title']))
                elements.append(Paragraph("An error occurred while generating the payslip. Please contact support.", styles['Normal']))

                doc.build(elements)
                buffer.seek(0)
                return buffer
            except:
                # If even the error PDF fails, just return an empty buffer
                buffer = BytesIO()
                return buffer

def format_amount(amount):
    """Format the amount to have commas with no decimal places."""
    try:
        # Handle None or empty values
        if amount is None or amount == '':
            return "0"

        # Convert to float and format
        amount_float = float(amount)
        return "{:,.0f}".format(amount_float)
    except (ValueError, TypeError) as e:
        current_app.logger.warning(f"Error formatting amount '{amount}': {e}")
        return "0"