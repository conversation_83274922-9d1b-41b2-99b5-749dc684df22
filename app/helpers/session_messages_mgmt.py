from flask import session, request

def clear_session_messages(response):
    print("This is request endpoint: ", request.endpoint)
    flashes = session.get("_flashes", None)
    contains_bulk_update = any("from_update" in message for message in flashes) if flashes else False
    if contains_bulk_update:
        session.pop('success_message', None)
        session.pop('error_message', None)
        session.pop("_flashes", None)
    return response