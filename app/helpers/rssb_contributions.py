from app.models.central import NsfContributions
import logging

class RssbContributionHelpers:
    """A class to provide helper methods for rssb contribution operations."""

    @staticmethod
    def get_rssb_contributions():
        """Get all rssb contributions.
        Returns:
            List of RssbContribution objects
        """
        try:
            rssb_contributions = NsfContributions.query.all()
            return rssb_contributions
        except Exception as e:
            logging.error(f"Error getting rssb contributions: {e}")
            return None, str(e)
        
    @staticmethod
    def calculate_employee_contributions(employees, rssb_contributions):
        """Calculate employee contributions.
        Args:
            employees: List of employee objects
            rssb_contributions: List of RSSB contribution objects
        Returns:
            List of employee contributions
        """
        contributions_data = []

        for employee in employees:
            print("Processing employee:", employee)

            employee_contributions = {
                'employee': employee,
                'contributions': []
            }

            for rssb_contribution in rssb_contributions:
                print("Processing RSSB contribution:", rssb_contribution)

                employee_rate = rssb_contribution.employee_rate
                employer_rate = rssb_contribution.employer_rate
                employee_salary = employee['gross_salary']
                employee_transport_allowance = employee['transport_allowance']

                print("Employee salary:", employee_salary)
                print("Employee rate:", employee_rate)
                print("Employer rate:", employer_rate)
                print("Employee transport allowance:", employee_transport_allowance)

                # Calculate employee and employer contribution amounts after subtracting transport allowance
                employee_contribution_amount = (employee_salary - employee_transport_allowance) * employee_rate
                employer_contribution_amount = (employee_salary - employee_transport_allowance) * employer_rate

                print("Employee contribution amount:", employee_contribution_amount)
                print("Employer contribution amount:", employer_contribution_amount)

                employee_contributions['contributions'].append({
                    'contribution_name': rssb_contribution.contribution_name,
                    'employee_contribution_amount': employee_contribution_amount,
                    'employer_contribution_amount': employer_contribution_amount
                })

            contributions_data.append(employee_contributions)
            print("Employee contributions:", employee_contributions)

        print("Final contributions data:", contributions_data)
        return contributions_data