from datetime import datetime, timed<PERSON><PERSON>
from flask import current_app
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.models.company import Attendance, User
from app.models.central import Company
from app.models.company import Employee
from app import app
from app.helpers.users_helpers import UsersHelpers
import uuid

class JobManager:
    """Class to manage the background jobs."""
    @staticmethod
    def auto_clockout_job():
        """Job to trigger the auto clockout process."""
        with app.app_context():
            try:
                # Create a single database connection to be reused
                db_connection = DatabaseConnection()
                database_names = CompanyHelpers.get_database_names()
                current_app.logger.info(f"Auto clockout job started for {len(database_names)} companies")

                # Process companies in batches to limit concurrent connections
                batch_size = 5
                for i in range(0, len(database_names), batch_size):
                    batch = database_names[i:i+batch_size]
                    current_app.logger.info(f"Processing batch {i//batch_size + 1} with {len(batch)} companies")

                    for database_name in batch:
                        try:
                            with db_connection.get_session(database_name) as db_session:
                                result = Attendance.auto_clockout(db_session)
                                if isinstance(result, tuple) and result[1]:
                                    message, auto_clocked_out_employees = result

                                    if auto_clocked_out_employees:
                                        company = CompanyHelpers.get_company_by_database_name(database_name)
                                        company_id = company.company_id

                                        # Get HR and supervisor emails
                                        users = [user.to_dict() for user in Company.get_users_for_company(company_id)]
                                        emails = [user['email'] for user in users if user['role'] in ['company_hr', 'hr']]

                                        company_users = User.get_users(db_session)
                                        emails.extend([user['email'] for user in company_users if user['role'] == 'supervisor'])

                                        subject = f"Auto Clockout - {company.company_name}"
                                        email_body = f"Employees auto clocked out: {auto_clocked_out_employees}"
                                        current_app.logger.info(f"Auto clockout job for {company.company_name}: {message}")

                                        if emails and auto_clocked_out_employees:
                                            """try:
                                                Auxillary.send_netpipo_email(subject, emails, email_body)
                                                current_app.logger.info(f"Auto clockout email sent to {emails}")
                                            except Exception as e:
                                                current_app.logger.error(f"Error sending email to {emails}: {str(e)}")
                                            """
                        except Exception as e:
                            current_app.logger.error(f"Error in auto clockout job for {database_name}: {str(e)}")
                            # Continue with next database instead of failing the entire job
                            continue

                current_app.logger.info("Auto clockout job completed successfully")
            except Exception as e:
                current_app.logger.error(f"Error in auto clockout job: {str(e)}")

    @staticmethod
    def send_previous_day_attendance_job():
        """Job to send the previous day's attendance to the company HR."""
        with app.app_context():
            try:
                # Create a single database connection to be reused
                db_connection = DatabaseConnection()
                database_names = CompanyHelpers.get_database_names()
                current_app.logger.info(f"Attendance report job started for {len(database_names)} companies")
                job_id = uuid.uuid4()
                current_app.logger.info(f"[{job_id}] Starting job for previous day attendance")

                previous_day = datetime.now() - timedelta(days=1)

                # Process companies in batches to limit concurrent connections
                batch_size = 5
                for i in range(0, len(database_names), batch_size):
                    batch = database_names[i:i+batch_size]
                    current_app.logger.info(f"Processing batch {i//batch_size + 1} with {len(batch)} companies")

                    for database_name in batch:
                        try:
                            with db_connection.get_session(database_name) as db_session:
                                # Get attendance data for the previous day
                                attendance_data = Attendance.get_attendance_by_date(db_session, previous_day.date())

                                # Skip if no attendance data
                                if not any(attendance_data.get(key) for key in ["clocked_in", "on_leave", "on_off"]):
                                    current_app.logger.info(f"No attendance data for {database_name} on {previous_day.date()}")
                                    continue

                                # Get company and user information
                                company = CompanyHelpers.get_company_by_database_name(database_name)
                                company_id = company.company_id

                                try:
                                    my_emails = UsersHelpers.get_company_and_central_users(
                                        db_session, company_id, roles=['supervisor', 'hr']
                                    )

                                    if not my_emails:
                                        current_app.logger.info(f"No recipients found for {company.company_name}")
                                        continue

                                    # Generate and send email
                                    subject = f"Attendance Records for {company.company_name} on {previous_day.strftime('%d/%m/%Y')}"
                                    email_body = JobManager.generate_attendance_email_body(previous_day, attendance_data)
                                    current_app.logger.info(f"Generated email body for {company.company_name} and emails: {my_emails}")

                                    sent = Auxillary.send_netpipo_email(subject, my_emails, email_body)
                                    current_app.logger.info(f"Email sent to {my_emails} - {sent}")

                                except Exception as e:
                                    current_app.logger.error(f"Error processing attendance for {company.company_name}: {e}")
                                    continue
                        except Exception as e:
                            current_app.logger.error(f"Error fetching attendance data for {database_name}: {e}")
                            continue

                current_app.logger.info("Attendance report job completed successfully")
            except Exception as e:
                current_app.logger.error(f"Error in attendance job: {str(e)}")


    @staticmethod
    def generate_attendance_email_body(previous_day, attendance_data):
        """Generate the email body with attendance details."""
        body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Poppins, sans-serif; color: #333; }}
                h3 {{ color: #007BFF; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 10px; text-align: left; }}
                th {{ background-color: #ebfaf0; color: #25a38b; }}
            </style>
        </head>
        <body>
            <p>Hello, Please find the attendance records for {previous_day.strftime('%d/%m/%Y')} below.</p>
        """

        sections = {
            "clocked_in": {
                "title": "Clocked In",
                "headers": ["employee_name", "time_in", "time_out", "total_duration"]
            },
            "on_leave": {
                "title": "On Leave",
                "headers": ["employee_name", "time_off_begin_date", "time_off_end_date"]
            },
            "on_off": {
                "title": "On Off",
                "headers": ["employee_name", "time_off_begin_date", "time_off_end_date"]
            }
        }

        for section, config in sections.items():
            if attendance_data.get(section):
                body += f"<h3>{config['title']}</h3><table><tr>"

                headers = config["headers"][:]  # Make a copy of the base headers to avoid modifying the original list

                # Handle dynamic headers for clocked-in employees
                if section == "clocked_in":
                    show_clockin_location_name = any(
                        record.get("clockin_location_name") not in [None, ""]
                        for record in attendance_data[section]
                    )

                    show_clockout_location_name = any(
                        record.get("clockout_location_name") not in [None, ""]
                        for record in attendance_data[section]
                    )

                    show_raw_clockin_location = any(
                        record.get("clockin_location") not in [None, ""] and
                        not JobManager.is_convertible_to_float(record.get("clockin_location"))
                        for record in attendance_data[section]
                    )

                    show_raw_clockout_location = any(
                        record.get("clockout_location") not in [None, ""] and
                        not JobManager.is_convertible_to_float(record.get("clockout_location"))
                        for record in attendance_data[section]
                    )

                    if show_clockin_location_name:
                        headers.append("clockin_location_name")

                    if show_clockout_location_name:
                        headers.append("clockout_location_name")

                    if show_raw_clockin_location:
                        headers.append("clockin_location")

                    if show_raw_clockout_location:
                        headers.append("clockout_location")

                # Generate table headers
                body += "".join(f"<th>{header.replace('_', ' ').title()}</th>" for header in headers)
                body += "</tr>"

                # Populate table rows
                for record in attendance_data[section]:
                    body += "<tr>"
                    for key in headers:
                        value = record.get(key, "N/A")

                        # Format dates for readability
                        if key in ["time_off_begin_date", "time_off_end_date"] and value != "N/A":
                            value = value

                        body += f"<td>{value}</td>"
                    body += "</tr>"
                body += "</table>"

        body += "</body></html>"
        return body


    @staticmethod
    def is_convertible_to_float(value):
        """Check if a value can be converted to a float (i.e., it's a coordinate)."""
        if value is None:
            return False
        try:
            float(value)
            return True
        except ValueError:
            return False


    @classmethod
    def send_email_reminder_to_customers(cls):
        """Send different email reminders to customers based on subscription or trial status."""
        with app.app_context():
            try:
                expiring_subs = {}
                expired_subs = {}
                expiring_trials = {}
                expired_trials = {}

                companies = Company.get_companies()
                # Use timezone-aware datetime to avoid deprecation warning
                current_date = datetime.now(datetime.timezone.utc).date()
                reminder_threshold = current_date + timedelta(days=7)  # 7-day reminder threshold

                current_app.logger.info(f"Email reminder job started for {len(companies)} companies")

                # Process companies in batches to reduce memory usage
                batch_size = 20
                for i in range(0, len(companies), batch_size):
                    batch = companies[i:i+batch_size]
                    current_app.logger.info(f"Processing batch {i//batch_size + 1} with {len(batch)} companies")

                    for company in batch:
                        try:
                            company_id = company['company_id']
                            company_name = company['company_name']

                            # Get users for this company
                            central_users = [user.to_dict() for user in Company.get_users_for_company(company_id)]

                            # Skip if no users
                            if not central_users:
                                current_app.logger.info(f"No users found for {company_name}")
                                continue

                            # Convert dates from string to date objects
                            subscription_end_date = company['subscription_end_period']
                            trial_until = company['trial_until']

                            if subscription_end_date:
                                try:
                                    subscription_end_date = datetime.strptime(subscription_end_date, "%d/%m/%Y %H:%M:%S").date()
                                except ValueError:
                                    current_app.logger.error(f"Invalid subscription date format for {company_name}: {subscription_end_date}")
                                    subscription_end_date = None

                            if trial_until:
                                try:
                                    trial_until = datetime.strptime(trial_until, "%d/%m/%Y %H:%M:%S").date()
                                except ValueError:
                                    current_app.logger.error(f"Invalid trial date format for {company_name}: {trial_until}")
                                    trial_until = None

                            # Categorizing users based on subscription/trial status
                            for user in central_users:
                                if user['role'] == 'hr':
                                    user_email = user['email']
                                    user_name = user['full_name']

                                    # Skip if no email
                                    if not user_email:
                                        continue

                                    # Expired subscription
                                    if subscription_end_date and subscription_end_date < current_date:
                                        expired_subs[user_email] = (user_name, company_name, subscription_end_date)

                                    # Subscription expiring soon
                                    elif subscription_end_date and current_date <= subscription_end_date <= reminder_threshold:
                                        expiring_subs[user_email] = (user_name, company_name, subscription_end_date)

                                    # Expired trial
                                    if trial_until and trial_until < current_date:
                                        expired_trials[user_email] = (user_name, company_name, trial_until)

                                    # Trial expiring soon
                                    elif trial_until and current_date <= trial_until <= reminder_threshold:
                                        expiring_trials[user_email] = (user_name, company_name, trial_until)
                        except Exception as e:
                            current_app.logger.error(f"Error processing company {company.get('company_name', 'Unknown')}: {str(e)}")
                            continue

                # Log summary of emails to be sent
                current_app.logger.info(f"Sending reminders: {len(expiring_subs)} expiring subscriptions, "
                                       f"{len(expired_subs)} expired subscriptions, "
                                       f"{len(expiring_trials)} expiring trials, "
                                       f"{len(expired_trials)} expired trials")

                # Send emails based on categories
                cls.send_reminders(expiring_subs, "Subscription Expiring Soon", "will expire on", "Renew Now!", "https://netpipo.com/login_user")
                cls.send_reminders(expired_subs, "Subscription Expired", "expired on", "Renew Subscription", "https://netpipo.com/login_user")
                cls.send_reminders(expiring_trials, "Trial Expiring Soon", "trial will end on", "Upgrade Now!", "https://netpipo.com/login_user")
                cls.send_reminders(expired_trials, "Trial Expired", "trial ended on", "Subscribe Now!", "https://netpipo.com/login_user")

                current_app.logger.info("Email reminder job completed successfully")
            except Exception as e:
                current_app.logger.error(f"Error sending email reminders: {e}")
    @classmethod
    def send_reminders(cls, users, subject_prefix, date_message, button_text, link):
        """Helper function to send emails based on user category."""
        for email, (user_name, company_name, end_date) in users.items():
            subject = f"{subject_prefix} - {company_name}"
            email_body = f"""
                    <h3>Dear {user_name},</h3>
                    <p>Your company's <strong>{company_name}</strong> {date_message} <strong>{end_date}</strong>.
                    To continue using our services without interruption, please take action now.</p>
                    <p><a href="{link}" class="button">{button_text}</a></p>
            """
            sent = Auxillary.send_netpipo_email(subject, [email], email_body)
            current_app.logger.info(f"Email sent to {email} - {sent}")

    @classmethod
    def deactivate_expired_contract_employee(cls):
        """Deactivate employees whose contract has ended."""
        db_connection = DatabaseConnection()
        with app.app_context():
            try:
                companies_db = CompanyHelpers.get_database_names()
                for database_name in companies_db:
                    with db_connection.get_session(database_name) as db_session:
                        try:
                            employees = Employee.get_employees(db_session)
                            # if a company has no employees, skip it
                            if not employees or len(employees) == 0:
                                continue
                            for employee in employees:
                                # first make sure the contract end date is not empty or None
                                if not employee['contract_end_date']:
                                    current_app.logger.info(f"Skipping employee {employee['first_name']} with no contract end date")
                                    continue
                                # check the type of contract end date
                                current_app.logger.info(f"Checking contract end date for employee {employee['first_name']} is {type(employee['contract_end_date'])}")

                                # check if the contract end date is a string and convert it to a date object
                                if isinstance(employee['contract_end_date'], str):
                                    try:
                                        employee['contract_end_date'] = datetime.strptime(employee['contract_end_date'], "%d/%m/%Y").date()
                                    except ValueError:
                                        current_app.logger.error(f"Invalid contract end date format for employee {employee['first_name']}: {employee['contract_end_date']}")
                                        continue
                                # compare the contract end date with the current date
                                comparison = employee['contract_end_date'] <= datetime.now().date()
                                current_app.logger.info(f"comparison for employee {employee['first_name']} is {comparison}")
                                if employee['contract_end_date'] and employee['contract_end_date'] < datetime.now().date():
                                    Employee.deactivate_or_activate_employee(db_session, employee['employee_id'], 'no')
                        except Exception as e:
                            current_app.logger.error(f"Error deactivating employees: {e}")
            except Exception as e:
                current_app.logger.error(f"Error deactivating employees: {e}")

