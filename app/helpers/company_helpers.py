"""This module contains helper methods for company operations."""
import logging
from app.models.central import Company
from flask import flash, current_app, session
from app import db
import os
import hashlib
from flask import jsonify 
from app.utils.db_connection import DatabaseConnection
import requests
from dotenv import load_dotenv
from app.helpers.auxillary import Auxillary
from collections import OrderedDict
class CompanyHelpers:
    """A class to provide helper methods for company operations."""

    @staticmethod
    def get_company_database_name(company_id):
        """Get the database name for a given company_id."""
        try:
            company = Company.query.get(company_id)
            if company is None:
                return None, "Company not found"
            return company.database_name
        except Exception as e:
            logging.error(f"Error retrieving company database name: {e}")
            return None, str(e)
        
    @staticmethod
    def get_company_by_database_name(database_name):
        """Get a company by database name."""
        try:
            company = Company.query.filter_by(database_name=database_name).first()
            return company
        except Exception as e:
            logging.error(f"Error getting company by database name: {e}")
            return None
        
    @staticmethod
    def get_database_name_by_tin(tin):
        """Get the database name for a given TIN."""
        try:
            company = Company.query.filter_by(company_tin=tin).first()
            if company is None:
                return None
            return company.database_name
        except Exception as e:
            logging.error(f"Error retrieving company database name: {e}")
            return None, str(e)
    @staticmethod   
    def get_companies():
        """Get all companies."""
        try:
            companies = Company.query.all()
            return companies
        except Exception as e:
            logging.error(f"Error getting companies: {e}")
            return None, str(e)
    @staticmethod
    def get_company_name(company_id):
        """Get the name of a company."""
        try:
            company = Company.query.get(company_id)
            return (company.company_name).capitalize()
        except Exception as e:
            logging.error(f"Error getting company name: {e}")
            return None, str(e)
        
    @staticmethod
    def get_database_names():
        """Get all database names."""
        try:
            companies = Company.query.all()
            database_names = [company.database_name for company in companies]
            return database_names
        except Exception as e:
            logging.error(f"Error getting database names: {e}")
            return []
        
    
    @staticmethod
    def get_departments(company_id):
        """Get all departments of a company."""
        try:
            company = Company.query.get(company_id)
            departments = company.departments
            return departments
        except Exception as e:
            logging.error(f"Error getting departments: {e}")
            return None, str(e)  

    @staticmethod
    def allowed_file(filename):
        """Check if a file is allowed."""
        ALLOWED_EXTENSIONS = {'jpg', 'jpeg', 'png', 'gif','tif'}
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
    
    @staticmethod
    def check_company_exists(name, tin, email, rssb_number, phone_number):
        """Check if a company already exists and return all fields with issues."""
        issues = {}
        try:
            if Company.query.filter_by(company_name=name).first():
                issues['company_name'] = 'Company with the same name already exists'
                current_app.logger.error('Company with the same name already exists')

            if Company.query.filter_by(company_tin=tin).first():
                issues['company_tin'] = 'Company with the same TIN already exists'
                current_app.logger.error('Company with the same TIN already exists')

            if Company.query.filter_by(email=email).first():
                issues['email'] = 'Company with the same email already exists'
                current_app.logger.error('Company with the same email already exists')

            if Company.query.filter_by(rssb_number=rssb_number).first():
                issues['rssb_number'] = 'Company with the same RSSB number already exists'
                current_app.logger.error('Company with the same RSSB number already exists')

            if Company.query.filter_by(phone_number=phone_number).first():
                issues['phone_number'] = 'Company with the same phone number already exists'
                current_app.logger.error('Company with the same phone number already exists')
            return issues
        except Exception as e:
            flash('Error checking company existence', 'danger')
            current_app.logger.error(f"Error checking company existence: {str(e)}")
            return {'error': 'Error checking company existence'}

        
    @staticmethod
    def register_company(new_company):
        """Register a company."""
        try:
            db.session.add(new_company)
            db.session.commit()
            return True
        except Exception as e:
            current_app.logger.error(f"Error registering company: {str(e)}")
            return False
        
    @staticmethod
    def get_company_tin(company_id):
        """Get the TIN of a company."""
        try:
            company = Company.query.get(company_id)
            return company.company_tin
        except Exception as e:
            logging.error(f"Error getting company TIN: {e}")
            return None, str(e)
        
    @staticmethod
    def get_company_api_key():
        """Get the API key of a company."""
        database_name = session.get('database_name')
        # Extract the company_tin from the database name because the first 9 characters are the company_tin
        company_tin = database_name[:9]
        print("Company TIN: ", company_tin)

        # Construct the API KEY based on the company_tin
        MICROSERVICE_KEY = os.getenv(f"{company_tin}_KEY")
        # Check if the Key is None
        if MICROSERVICE_KEY is None:
            return None
        return MICROSERVICE_KEY
    
    @staticmethod
    def get_company_compreface_api_key():
        """Get the API key of a company."""
        database_name = session.get('database_name')
        # Get the company
        company = Company.query.filter_by(database_name=database_name).first()
        if company is None:
            return None
        return company.compreface_api_key
    
    @staticmethod
    def get_employees_for_all_companies(companies_db):
        from app.models.company import Employee #Avoid circular input
        # Connect to the company database
        employee_list=[]
        db_connection = DatabaseConnection()
        
        for database_name in companies_db:
            with db_connection.get_session(database_name) as db_session:
                try:
                    employees = Employee.get_employees(db_session)
                    employee_list.extend(employees)
                    current_app.logger.info(f"Employees retrieved: {employees}")
                except Exception as e:
                    current_app.logger.error(f"Error retrieving employees: {str(e)}")
                    return []  # Returning empty list if error occurs
        
        return employee_list
    
    @staticmethod
    def generate_salt():
        """Generate a salt."""
        return os.urandom(16).hex()
    
    @staticmethod
    def hash_password(password, salt):
        """Hash a password."""
        salted_password = password + salt
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        return hashed_password
    
    @classmethod
    def process_and_send_employees(cls,database_name, company_id, device_sn):
        """
        Retrieves employees from the database, posts them to an endpoint, and sends them to the device.

        Args:
            database_name (str): Name of the database to connect to.
            company_id (str): ID of the company.
            device_sn (str): Device serial number.

        Returns:
            list: A list of successfully processed employee IDs.
        """
        from app.models.company import Employee
        # Load environment variables
        load_dotenv()
        successful_employees = []

        # Get the base URL from environment variables
        netpipo_base_url = os.getenv("NETPIPO_BASE_URL")

        # Connect to the company database
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            try:
                employees = Employee.get_employees(db_session)
                current_app.logger.info(f"Employees retrieved: {employees}")
            except Exception as e:
                current_app.logger.error(f"Error retrieving employees: {str(e)}")
                return successful_employees  # Returning empty list if error occurs

        if not employees or len(employees) == 0:
            current_app.logger.info("No employees found.")
            return successful_employees

        for employee in employees:
            employee_id = str(employee['employee_id'])  # Ensuring employee_id is a string
            roll_id = 0
            name = employee['full_name']
            
            # construct the employee data as a json object
            employee_data = {
                'employee_id': employee_id,
                'privilege': roll_id,
                'name': name,
                'company_id': company_id
            }

            url = f"{netpipo_base_url}/addEmployee"
            current_app.logger.info(f"Posting employee data: {employee_data} to {url}")

            # Post employee data to the endpoint
            try:
                response = requests.post(url, json=employee_data)
                response.raise_for_status()  # Raise an exception for HTTP errors
                response_data = response.json()
                current_app.logger.info(f"Response: {response_data}")
            except Exception as e:
                current_app.logger.error(f"Error posting employee data: {str(e)}")
                continue  # Skip to the next employee

            # Extract userId
            user_id = response_data.get('userId')
            if not user_id:
                current_app.logger.warning(f"No userId returned for employee {employee_id}")
                continue  # Skip sending to device

            # Send employee data to the device
            url2 = f"{netpipo_base_url}/setOneUserJson"
            payload = {
                "enrollId": user_id,
                "backupNum": -1,
                "deviceSn": device_sn
            }

            try:
                response2 = requests.post(url2, json=payload)
                response2.raise_for_status()
                current_app.logger.info(f"Response2: {response2.json()}")
                successful_employees.append(name)  # Add to successful list
            except Exception as e:
                current_app.logger.error(f"Error sending data to device: {str(e)}")

        return successful_employees  # Return list of successfully processed employees
    
    @classmethod
    def allowed_clockin_locations(cls, session, company_id):
        """
        Get the allowed clock-in locations for a company.

        Args:
            session: Database session
            company_id (uuid): The ID of the company.

        Returns:
            list: A list of dictionaries with 'latitude' and 'longitude' keys.
        """  
        locations = []
        from app.models.company import Site
        
        # Get the company headquarters' latitude and longitude
        company = Company.get_company_by_id(company_id)
        current_app.logger.info(f"Company: {company}")
        if company:
            if company.get('latitude') is not None and company.get('longitude') is not None:
                locations.append({"latitude": company['latitude'], "longitude": company['longitude']})
                current_app.logger.info(f"Company headquarters location: {locations}")

        # Get branch locations
        try:
            branches = Site.get_sites(session)
            if branches:
                branch_locations = [
                    {"latitude": branch.get("latitude"), "longitude": branch.get("longitude")}
                    for branch in branches
                    if branch.get("latitude") is not None and branch.get("longitude") is not None
                ]
                current_app.logger.info(f"Branch locations: {branch_locations}")    
                locations.extend(branch_locations)
                current_app.logger.info(f"Allowed clock-in locations: {locations}")

        except Exception as e:
            current_app.logger.error(f"Error retrieving branch locations: {e}")

        return locations
    
    @classmethod
    def notify_hr_if_no_locations(cls, company_id):
        """
        Checks if company locations are set. If not, notifies HR users via email.
        
        Args:
            company_id (int): The ID of the company.
        
        Returns:
            tuple: Response message and HTTP status code if no locations are found, otherwise None.
        """
        try:
            company_users = Company.get_users_for_company(company_id)
            current_app.logger.info(f"Company users: {company_users}")

            # Get HR users and their emails
            hr_users = [user for user in company_users if user.role == 'hr']
            hr_emails = [hr_user.email for hr_user in hr_users]
            current_app.logger.info(f"HR users: {hr_users}")
            current_app.logger.info(f"HR emails: {hr_emails}")
            company_name = cls.get_company_name(company_id)
    
            # Add your email
            my_email = "<EMAIL>"
            hr_emails.append(my_email)
            current_app.logger.info(f"HR emails after adding the email: {hr_emails}")

            # Email details
            subject = f"Company Locations not set for {company_name}"
            message = f"""
            Dear admin, employees are trying to clock in with your company {company_name} but the company locations have not been set.
            Please set the company locations in the system so that employees can clock in.
            You can set the company locations by logging in to the system and going to the settings section.
            """

            # Send email
            try:
                sent = Auxillary.send_netpipo_email(subject, hr_emails, message)
                current_app.logger.info(f"Email sent: {sent}")
            except Exception as e:
                current_app.logger.error(f"Error sending email: {e}")

        except Exception as e:
            current_app.logger.error(f"Error getting HR users: {e}")
        
        message = "No allowed locations found. Please contact HR so that they update the company location"

        return message




            