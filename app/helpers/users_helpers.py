import logging

from sqlalchemy import or_
from app.models.central import Company
from flask import flash, current_app, session
from app import db
from app.models.central import User
import os
import hashlib
from flask import jsonify 
from app.models.company import User as CompanyUser

class UsersHelpers:
    """A class to provide helper methods for user related operations."""

    @staticmethod
    def check_user_exists(username, email, phone):
        """Check if a user with the given username, email or phone exists."""
        issues = {}
        try:
            users = User.query.filter(
                or_(
                    User.username == username,
                    User.email == email,
                    User.phone_number == phone
                )
            ).all()
            
            if len(users) == 0:
                return None
            
            for user in users:
                if user.username == username:
                    issues['username'] = 'Username already exists'
                if user.email == email:
                    issues['email'] = 'Email already exists'
                if user.phone_number == phone:
                    issues['phone'] = 'Phone already exists'
                    
            return issues
        except Exception as e:
            flash('Error checking user existence', 'error')
            current_app.logger.error(f"Error checking user existence: {e}")
            raise
    
    @staticmethod  
    def get_company_and_central_users(db_session, company_id, roles=['supervisor', 'hr']):
        """Fetch users from both the company's database and the central database based on roles."""
        try:
            # Fetch users from the company's database
            company_users = CompanyUser.get_users(db_session)
            company_users_emails = {user['email'] for user in company_users if user['role'] in roles}

            # Fetch users from the central database
            central_users = [user.to_dict() for user in Company.get_users_for_company(company_id)]
            central_users_emails = {user['email'] for user in central_users if user['role'] in roles}

            # Merge unique emails
            unique_users_emails = company_users_emails.union(central_users_emails)

            return list(unique_users_emails)

        except Exception as e:
            current_app.logger.error(f"Error fetching users: {e}")
            return []