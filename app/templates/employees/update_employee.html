<!DOCTYPE html>
<html>
<head>
    <title>Update Employee</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('my_employees.employees_list') }}">
            <i class="fi fi-rr-angle-small-left icon primary"></i>
            <span>Back</span>
        </a>
    </div>
    <!-- Flash messages -->
    <div id="message-container"></div>
    <div id="passed-container"></div>
    <div class="form--container">
        <h1>Update Employee</h1>

        <!-- General validation error message section -->
        {% if form.errors %}
        <div class="alert alert-danger">
            <p>Please correct the errors below:</p>
            <ul>
                {% for field, errors in form.errors.items() %}
                    {% for error in errors %}
                        <li>{{ field }}: {{ error }}</li>
                    {% endfor %}
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <form action="{{ url_for('my_employees.update_employee', employee_id=employee['employee_id']) }}" method="POST">
            {{ form.hidden_tag() }}
            <div class="selective--div">
                <legend>Personal Information</legend>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.first_name.label }}
                        <div class="input-group-text">
                            {{ form.first_name(class="form-control") }}
                        </div>
                        {% for error in form.first_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.last_name.label }}
                        <div class="input-group-text">
                            {{ form.last_name(class="form-control") }}
                        </div>
                        {% for error in form.last_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.nid.label }}
                        <div class="input-group-text">
                            {{ form.nid(class="form-control") }}
                        </div>
                        {% for error in form.nid.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.gender.label }}
                        <div class="input-group-text">
                            {{ form.gender(class="form-control") }}
                        </div>
                        {% for error in form.gender.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.birth_date.label }}
                        <div class="input-group-text">
                            {{ form.birth_date(class="form-control") }}
                        </div>
                        {% for error in form.birth_date.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.marital_status.label }}
                        <div class="input-group-text">
                            {{ form.marital_status(class="form-control") }}
                        </div>
                        {% for error in form.marital_status.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.nsf.label }}
                        <div class="input-group-text">
                            {{ form.nsf(class="form-control") }}
                        </div>
                        {% for error in form.nsf.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="selective--div">
                <legend>Employee Information</legend>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.employee_tin.label }}
                        <div class="input-group-text">
                            {{ form.employee_tin(class="form-control") }}
                        </div>
                        {% for error in form.employee_tin.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.employee_type.label }}
                        <div class="input-group-text">
                            {{ form.employee_type(class="form-control") }}
                        </div>
                        {% for error in form.employee_type.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.department.label }}
                        <div class="input-group-text">
                            {{ form.department(class="form-control") }}
                        </div>
                        {% for error in form.department.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {% if sites %}
                        <label for="site_id">{{ form.site_id.label }}</label>
                        <div class="input-group-text">
                            <!-- Raw HTML select tag for site_id -->
                            <select name="site_id" id="site_id" class="form-control">
                                <option value="">Select Site</option>
                                {% for site in sites %}
                                <option value="{{ site['site_id'] }}" {% if form.site_id.data==site['site_id']
                                    %}selected{% endif %}>
                                    {{ site['site_name'] }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.is_active.label }}
                        <div class="input-group-text">
                            {{ form.is_active(class="form-control") }}
                        </div>
                        {% for error in form.is_active.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.is_brd_sponsored.label }}
                        <div class="input-group-text">
                            {{ form.is_brd_sponsored(class="form-control") }}
                        </div>
                        {% for error in form.is_brd_sponsored.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.attendance_applicable.label }}
                        <div class="input-group-text">
                            {{ form.attendance_applicable(class="form-control") }}
                        </div>
                        {% for error in form.attendance_applicable.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.job_title.label }}
                        <div class="input-group-text">
                            {{ form.job_title(class="form-control") }}
                        </div>
                        {% for error in form.job_title.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.hire_date.label }}
                        <div class="input-group-text">
                            {{ form.hire_date(class="form-control") }}
                        </div>
                        {% for error in form.hire_date.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <!-- Salary Type -->
                    <div class="form-group col-md-3">
                        {{ form.salary_type.label }}
                        <div class="input-group-text">
                            {{ form.salary_type(class="form-control") }}
                        </div>
                        {% for error in form.salary_type.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.salary_amount.label }}
                        <div class="input-group-text">
                            {{ form.salary_amount(class="form-control") }}
                        </div>
                        {% for error in form.salary_amount.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="selective--div">
                <legend>Allowances</legend>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.transport_allowance.label }}
                        <div class="input-group-text">
                            {{ form.transport_allowance(class="form-control") }}
                        </div>
                        {% for error in form.transport_allowance.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.housing_allowance.label }}
                        <div class="input-group-text">
                            {{ form.housing_allowance(class="form-control") }}
                        </div>
                        {% for error in form.housing_allowance.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.communication_allowance.label }}
                        <div class="input-group-text">
                            {{ form.communication_allowance(class="form-control") }}
                        </div>
                        {% for error in form.communication_allowance.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.over_time.label }}
                        <div class="input-group-text">
                            {{ form.over_time(class="form-control") }}
                        </div>
                        {% for error in form.over_time.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.other_allowance.label }}
                        <div class="input-group-text">
                            {{ form.other_allowance(class="form-control") }}
                        </div>
                        {% for error in form.other_allowance.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="selective--div">
                <legend>Bank Information</legend>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.bank_name.label }}
                        <div class="input-group-text">
                            {{ form.bank_name(class="form-control") }}
                        </div>
                        {% for error in form.bank_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.bank_account.label }}
                        <div class="input-group-text">
                            {{ form.bank_account(class="form-control") }}
                        </div>
                        {% for error in form.bank_account.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.branch_name.label }}
                        <div class="input-group-text">
                            {{ form.branch_name(class="form-control") }}
                        </div>
                        {% for error in form.branch_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.account_name.label }}
                        <div class="input-group-text">
                            {{ form.account_name(class="form-control") }}
                        </div>
                        {% for error in form.account_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="selective--div">
                <legend>Contact Information</legend>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.email.label }}
                        <div class="input-group-text">
                            {{ form.email(class="form-control") }}
                        </div>
                        {% for error in form.email.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="form-group col-md-3">
                        {{ form.phone.label }}
                        <div class="input-group-text">
                            {{ form.phone(class="form-control") }}
                        </div>
                        {% for error in form.phone.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="selective--div">
                <!-- Annual leave balance -->
                <legend>Leave Information</legend>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        {{ form.annual_leave_balance.label }}
                        <div class="input-group-text">
                            {{ form.annual_leave_balance(class="form-control") }}
                        </div>
                        {% for error in form.annual_leave_balance.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <!-- Extra leave days -->
                    <div class="form-group col-md-3">
                        {{ form.extra_leave_days.label }}
                        <div class="input-group-text">
                            {{ form.extra_leave_days(class="form-control") }}
                        </div>
                        {% for error in form.extra_leave_days.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="form-group">
                {{ form.submit(class='submit-btn') }}
            </div>
        </form>
    </div>
</body>
</html>
