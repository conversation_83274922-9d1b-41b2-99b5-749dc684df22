<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a href="{{ url_for('my_employees.employees_list') }}" class="btn-edit">
                <i class="fi fi-rr-list"></i> Employees
            </a>
            <div class="right-buttons-group">
                <a class="blue-button" href="{{ url_for('my_employees.register_employees_percentage') }}">
                    <i class="fi fi-rr-user-add"></i> EE(% Salary)
                </a>
                <a class="green-button" href="{{ url_for('my_employees.upload_employees') }}">
                    <i class="fi fi-rr-file-import"></i>Import
                </a>
            </div>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <h1>Register Employee - Fixed Salary</h1>
            <form action="{{ url_for('my_employees.register_employees') }}" method="post">
                {{ form.csrf_token }}
                {{ form.hidden_tag() }}
                <div class="selective--div">
                    <legend>Personal Information</legend>
                    <div class="form-row">
                        <!-- First Name -->
                        <div class="form-group">
                            <div class="row">
                                <label for="first_name">First Name: <span class="text-danger">*</span></label>
                                <i class="fas fa-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="First name is the given name (eg: Fred, Patrick, Alex)"></i>
                            </div>
                            <div class="input-group-text">
                                {{ form.first_name(class_="form-control", placeholder="Ex: John") }}
                            </div>
                        </div>
                        <!-- Last Name -->
                        <div class="form-group col-md-3">
                            <div class="row">
                                <label for="last_name">Last Name: <span class="text-danger">*</span></label>
                                <i class="fas fa-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Last name is the family name (eg: Muhire, Kabanda, Rugema, etc...)"></i>
                            </div>

                            <div class="input-group-text">
                                {{ form.last_name(class_="form-control", placeholder="ex: Mugisha") }}
                            </div>
                        </div>
                        <!-- NID -->
                        <div class="form-group col-md-3">
                            <label for="nid">NID: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.nid(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <!-- Gender -->
                        <div class="form-group col-md-3">
                            <label for="gender">Gender: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.gender(class="form-control") }}
                            </div>
                        </div>
                        <!-- Birth Date -->
                        <div class="form-group col-md-3">
                            <label for="birth_date">Birth Date: </label>
                            <div class="input-group-text">
                                {{ form.birth_date(class_="form-control") }}
                            </div>
                        </div>
                        {% if form.birth_date.errors %}
                        <div class="text-danger mt-1">
                            {% for error in form.birth_date.errors %}
                                <small>{{ error }}</small><br>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <!-- Marital Status -->
                        <div class="form-group col-md-3">
                            <label for="marital_status">Marital Status: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.marital_status(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <!-- RSSB Number -->
                        <div class="form-group col-md-3">
                            <label for="rssb_number">RSSB Number: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.rssb_number(class_="form-control") }}
                            </div>
                        </div>
                        <!-- Email -->
                        <div class="form-group col-md-3">
                            <label for="email">Email:</label>
                            <div class="input-group-text">
                                {{ form.email(class_="form-control") }}
                            </div>
                            <div class="text-danger mt-1">
                                {% for error in form.email.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        </div>
                        <!-- Phone -->
                        <div class="form-group col-md-3">
                            <label for="phone">Phone: </label>
                            <div class="input-group-text">
                                {{ form.phone(class_="form-control") }}
                            </div>
                        </div>
                    </div>

                </div>

                <div class="selective--div">
                    <legend>Employment Information</legend>
                    <div class="form-row">
                        <!-- Employee TIN -->
                        <div class="form-group col-md-3">
                            <label for="employee_tin">Employee TIN:</label>
                            <div class="input-group-text">
                                {{ form.employee_tin(class_="form-control") }}
                            </div>
                        </div>
                        <!-- Employee Type -->
                        <div class="form-group col-md-3">
                            <label for="employee_type">Employement Type: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.employee_type(class="form-control") }}
                            </div>
                        </div>
                        <!-- Department -->
                        <div class="form-group col-md-3">
                            <label for="department">Department:</label>
                            <div class="input-group-text">

                                {{ form.department(class_="form-control") }}
                            </div>
                        </div>
                        <!-- Job Title -->
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="job_title">Job Title: </label>
                            <div class="input-group-text">

                                {{ form.job_title(class_="form-control") }}
                            </div>
                        </div>

                        <!-- Hire Date -->
                        <div class="form-group col-md-3">
                            <label for="hire_date">Hire Date: </label>
                            <div class="input-group-text">
                                {{ form.hire_date(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="contract_end_date">Contract End Date:</label>
                            <div class="input-group-text">
                                {{ form.contract_end_date(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <!-- Salary Type -->
                        <div class="form-group col-md-3">
                            <label for="salary_type">salary_type: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.salary_type(class_="form-control") }}
                            </div>
                        </div>
                        <!-- Salary_amount-->
                        <div class="form-group col-md-3">
                            <label for="salary_amount">Salary Amount: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.salary_amount(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="selective--div">
                        <legend>Allowances</legend>
                        <div class="form-row">
                            <!-- Transport Allowance -->
                            <div class="form-group col-md-3">
                                <label for="transport_allowance">Transport Allowance:</label>
                                <div class="input-group-text">
                                    {{ form.transport_allowance(class_="form-control") }}
                                </div>
                            </div>

                            <!-- Housing Allowance -->
                            <div class="form-group col-md-3">
                                <label for="housing_allowance">Housing Allowance:</label>
                                <div class="input-group-text">
                                    {{ form.housing_allowance(class_="form-control") }}
                                </div>
                            </div>
                            <!-- Communication Allowance -->
                            <div class="form-group col-md-3">
                                <label for="communication_allowance">Communication Allowance:</label>
                                <div class="input-group-text">
                                    {{ form.communication_allowance(class_="form-control") }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <!-- Over Time -->
                            <div class="form-group col-md-3">
                                <label for="over_time">Over Time:</label>
                                <div class="input-group-text">

                                    {{ form.over_time(class_="form-control") }}
                                </div>
                            </div>
                            <!-- Other Allowance -->
                            <div class="form-group col-md-3">
                                <label for="other_allowance">Other Allowances:</label>
                                <div class="input-group-text">
                                    {{ form.other_allowance(class_="form-control") }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="selective--div">
                        <!-- bank name -->
                        <legend>Bank Details</legend>
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="bank_name">Bank Name:</label>
                                <div class="input-group-text">
                                    {{ form.bank_name(class="form-control") }}
                                </div>
                            </div>
                            <!-- account number -->
                            <div class="form-group col-md-3">
                                <label for="bank_account">Account Number:</label>
                                <div class="input-group-text">
                                    {{ form.bank_account(class_="form-control") }}
                                </div>
                            </div>
                            <!-- Branch Name-->
                            <div class="form-group col-md-3">
                                <label for="branch_name">Branch Name:</label>
                                <div class="input-group-text">
                                    {{ form.branch_name(class="form-control") }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <!-- Account Name-->
                            <div class="form-group col-md-3">
                                <label for="account_name">Account Name:</label>
                                <div class="input-group-text">
                                    {{ form.account_name(class_="form-control") }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="selective--div">
                        <!-- Annual leave balance -->
                        <legend>Leave Information</legend>
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="bank_name">Annual Leave Balance:</label>
                                <div class="input-group-text">
                                    {{ form.annual_leave_balance(class="form-control", value=0) }}
                                </div>
                            </div>
                            <!-- Extra leave days -->
                            <div class="form-group col-md-3">
                                <label for="bank_account">Extra Leave Days:</label>
                                <div class="input-group-text">
                                    {{ form.extra_leave_days(class_="form-control", value=0) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--
                <div class="selective--div">
                    <legend>Emergency Contact Info</legend>

                </div>-->
                <button type="submit" class="submit-btn">Register Employee</button>

            </form>
        </div>
    </div>
</div>
{% endblock %}
