<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1 class="dynamic-title">Bulk Update Employees</h1>
                <a class="btn-edit template-link" href="{{ url_for('my_employees.employees_list') }}">
                    <i class="fas fa-plus"></i> Employees list
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <div id="employee-modal">
                    <div class="text-box" style="margin: 1rem 5px">
                        <input type="text" name="search-param" id="search-param" placeholder="Search by, Name, Email ..."/>
                        <i class="fi fi-rr-search"></i>
                    </div>
                    <div class="btn-container">
                        <button class="panel-btn active" data-id="personal" style="position: sticky; left: 0;">Personal
                            Infromation</button>
                        <button class="panel-btn" data-id="employee">Employee Information</button>
                        <button class="panel-btn" data-id="allowance">Allowances</button>
                        <button class="panel-btn" data-id="banking">Banking Information</button>
                        <button class="panel-btn" data-id="contact">Contact Information</button>
                    </div>
                    <div class="message"></div>
                    <input type="text" name="csrf_token" id="csrf_token" value="{{csrf_token}}" hidden>
                    <div class="update-field">
                        <table>
                            <thead>
                                <tr class="table-columns"></tr>
                            </thead>
                            <tbody class="table-data"></tbody>
                        </table>
                    </div>
                </div>
            <script src="{{url_for('static', filename='scripts/employee_bulk_update.js')}}"></script>
        </div>
    </div>
    </div>
{% endblock %}