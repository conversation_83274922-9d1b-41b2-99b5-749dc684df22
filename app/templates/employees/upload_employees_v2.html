<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="dynamic-title">Bulk upload employees</h1>
            <a class="btn-edit template-link" href="{{ url_for('my_employees.employees_list') }}">
                <i class="fi fi-rr-list"></i> Employees list
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <form action="{{ url_for('my_employees.upload_employees') }}" method="post" enctype="multipart/form-data">
                {{ form.csrf_token }}
                <div class="form-row">
                    <div class="form-group">
                        <label for="file">Upload Employees       <span><a href="{{ url_for('employees.download_employees_template') }}" class="blue"><i class="fi fi-rr-inbox-in"> </i>Template</a></span> </label>
                        <div class="input-group-text">
                            <i class="fi fi-rr-folder-open"></i>
                            <input type="file" name="file" class="form-control" required/>
                        </div>
                    </div>
                 </div>
                <button type="submit" value="Upload" class="submit-btn">Upload</button>
            </form>
        </div>  
    </div>
</div>  
{% endblock %}
