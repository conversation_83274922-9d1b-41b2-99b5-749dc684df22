<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="text-center mb-4">Employees' Data</h1>
            <div class="right-buttons-group">
                <a class="primary-button" href="{{ url_for('my_employees.register_employees') }}">
                    <i class="fi fi-rr-user-add"></i> Employee
                </a>
                <a class="grey-button" href="{{ url_for('my_employees.get_inactive_employees') }}">
                    <i class="fi fi-rr-user-slash"></i>Inactive Employees
                </a>
                <a class="primary-button" href="{{ url_for('my_employees.upload_employees') }}">
                    <i class="fi fi-rr-upload"></i>Import
                </a>
                {% if employees %}
                    <a class="primary-button" href="{{ url_for('my_employees.render_bulk_update_template') }}">
                        <i class="fi fi-rr-user-pen"></i> Bulk edit
                    </a>
                {% else %}

                {% endif %}
            </div>
        </div>
        <div class="space-between">
            <div class="filter-container">
                <div class="page_rows">
                    <label for="rowsPerPageInput">Show:</label>
                    <select id="rowsPerPageInput">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
                <div class="page_search">
                    <label for="searchInput">Search:</label>
                    <input type="text" id="searchInput" placeholder="Search employees..." class="form-control">
                </div>
                <button class="toggle-filters-btn primary-button" id="toggleFiltersBtn">Show Filters</button>
                <div class="advanced-filters" id="advancedFilters">
                        <h3>Filter by:</h3>
                        <div class="underline"></div>
                        <div class="form-group">
                            <label for="employeeTypeFilter">Employment Type:</label>
                            <select id="employeeTypeFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="jobTitleFilter">Job Title:</label>
                            <select id="jobTitleFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="departmentFilter">Department:</label>
                            <select id="departmentFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="space-between">
                            <div class="form-group">
                                <label for="hireDateStart">Hire Date (Start):</label>
                                <input type="date" id="hireDateStart" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="hireDateEnd">Hire Date (End):</label>
                                <input type="date" id="hireDateEnd" class="form-control">
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
    <div class="dyn_container">
            <div class="table-responsive">
                <table id="employees_list">
                    <thead class="thead th-custom">
                        <tr>
                            <th scope="col"># </th>
                            <th scope="col">First Name</th>
                            <th scope="col">Last Name</th>
                            <th scope="col">NID</th>
                            <th scope="col">RSSB No</th>
                            <th scope="col">DOB</th>
                            <th scope="col">Marital Status</th>
                            <th scope="col">Gender</th>
                            <th scope="col">TIN</th>
                            <th scope="col">Employee Type</th>
                            <th scope="col">Net Salary</th>
                            <th scope="col">Gross Salary</th>
                            <th scope="col">Transport Allowance</th>
                            <th scope="col">Housing Allowance</th>
                            <th scope="col">Communication Allowance</th>
                            <th scope="col">Other Allowance</th>
                            <th scope="col">Email</th>
                            <th scope="col">Phone</th>
                            <th scope="col">Job Title</th>
                            <th scope="col">Department</th>
                            <th scope="col">Hire Date</th>
                            <th scope="col">Contract End Date</th>
                            <th>Ann Leave balance</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                            <tr>
                                <td>{{ loop.index }}</td> <!-- Indexing starts from 1 -->
                                <td>{{ employee.first_name }}</td>
                                <td>{{ employee.last_name }}</td>
                                <td>{{ employee.nid }}</td>
                                <td>{{ employee.nsf }}</td>
                                <td>{% if employee.birth_date %}
                                    {{ employee.birth_date }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{{ employee.marital_status }}</td>
                                <td>{{ employee.gender }}</td>
                                <td>
                                    {% if employee.employee_tin == 'nan' %}
                                        
                                    {% elif employee.employee_tin %}
                                        {{ employee.employee_tin }}
                                    {% else %}
                                        
                                    {% endif %}
                                </td>
                                <td>{{ employee.employee_type }}</td>
                                <td>{% if employee.net_salary %}
                                    {{ Auxillary.format_amount(employee.net_salary) }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.gross_salary %}
                                    {{ Auxillary.format_amount(employee.gross_salary) }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{{ Auxillary.format_amount(employee.transport_allowance) }}</td>
                                <td>{{ Auxillary.format_amount(employee.housing_allowance) }}</td>
                                <td>{{ Auxillary.format_amount(employee.communication_allowance) }}</td>
                                <td>{{ Auxillary.format_amount(employee.other_allowance) }}</td>
                                <td>{% if employee.email %}
                                    {{ employee.email }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.phone %}
                                    {{ employee.phone }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.job_title %}
                                    {{ employee.job_title }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.department %}
                                    {{ employee.department }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.hire_date %}
                                    {{ employee.hire_date }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.contract_end_date %}
                                    {{ employee.contract_end_date }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>
                                    {% if employee.annual_leave_balance is not none %}
                                        {{ "%02d"|format(employee.annual_leave_balance|int) }}
                                    {% else %}
                                        
                                    {% endif %}
                                </td>


                                <td>
                                    <div class="table-buttons">
                                        <a class="template-link btn-image" href="{{ url_for('my_employees.update_employee', employee_id=employee.employee_id) }}">
                                        <i class="fi fi-rr-edit green"></i>
                                        </a>
                                        {% if attendance_service %}
                                        <!--
                                            <a class="template-link btn-image" href="#" data-template-url="{{ url_for('attendance.create_subject', name=employee.employee_id ~ ' ' ~ employee.first_name ~ ' ' ~ employee.last_name) }}">
                                        
                                            <i class="fi fi-rr-add-image button orange"></i> 
                                            </a>
                                        -->
                                            {% else %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            <div id="pagination" class="pagination"></div>

            </div>

        </div>
</div>

{% endblock %}