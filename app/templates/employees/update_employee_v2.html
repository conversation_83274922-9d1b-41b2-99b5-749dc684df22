<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a href="{{ url_for('my_employees.employees_list') }}" class="btn-edit">
                <i class="fi fi-rr-list"></i> Employees
            </a>
            <a class="template-link btn-edit" href="">
                <i class="fi fi-rr-file-import"></i>Import
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <h1>Update Employee</h1>
            <form action="{{ url_for('my_employees.update_employee',employee_id=employee.employee_id) }}" method="POST">
                {{ form.hidden_tag() }}
                <div class="selective--div">
                    <legend>Personal Infromation</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.first_name.label }}
                            <div class="input-group-text">
                                {{ form.first_name(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.last_name.label }}
                            <div class="input-group-text">
                                {{ form.last_name(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.nid.label }}
                            <div class="input-group-text">
                                {{ form.nid(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.gender.label }}
                            <div class="input-group-text">
                                {{ form.gender(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.birth_date.label }}
                            <div class="input-group-text">
                                {{ form.birth_date(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.marital_status.label }}
                            <div class="input-group-text">
                                {{ form.marital_status(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.nsf.label }}
                            <div class="input-group-text">
                                {{ form.nsf(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="selective--div">
                    <legend>Employee Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.employee_tin.label }}
                            <div class="input-group-text">
                                {{ form.employee_tin(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.employee_type.label }}
                            <div class="input-group-text">
                                {{ form.employee_type(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.department.label }}
                            <div class="input-group-text">
                                {{ form.department(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {% if sites %}
                            <label for="site_id">{{ form.site_id.label }}</label>
                            <div class="input-group-text">
                                <!-- Raw HTML select tag for site_id -->
                                <select name="site_id" id="site_id" class="form-control">
                                    {% for site in sites %}
                                    <option value="{{ site['site_id'] }}" {% if form.site_id.data==site['site_id']
                                        %}selected{% endif %}>
                                        {{ site['site_name'] }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            {% endif %}
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.is_active.label }}
                            <div class="input-group-text">
                                {{ form.is_active(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.is_brd_sponsored.label }}
                            <div class="input-group-text">
                                {{ form.is_brd_sponsored(class="form-control") }}
                            </div>
                        </div>

                        <div class="form-group col-md-3">
                            {{ form.attendance_applicable.label }}
                            <div class="input-group-text">
                                {{ form.attendance_applicable(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.job_title.label }}
                            <div class="input-group-text">
                                {{ form.job_title(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.hire_date.label }}
                            <div class="input-group-text">
                                {{ form.hire_date(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.contract_end_date.label }}
                            <div class="input-group-text">
                                {{ form.contract_end_date(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <!-- Salary Type -->
                        <div class="form-group col-md-3">
                            {{ form.salary_type.label }}
                            <div class="input-group-text">
                                {{ form.salary_type(class="form-control", id="salary_type") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.salary_amount.label }}
                            <div class="input-group-text">
                                {{ form.salary_amount(class="form-control") }}
                            </div>
                        </div>
                    </div>

                </div>
                <div class="selective--div">
                    <legend>Allowances</legend>

                    <!-- Fixed Amounts Section -->
                    <div id="fixed-amounts-section">
                        <h6>Fixed Amounts</h6>
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                {{ form.transport_allowance.label }}
                                <div class="input-group-text">
                                    {{ form.transport_allowance(class="form-control") }}
                                </div>
                            </div>
                            <div class="form-group col-md-3">
                                {{ form.housing_allowance.label }}
                                <div class="input-group-text">
                                    {{ form.housing_allowance(class="form-control") }}
                                </div>
                            </div>
                            <div class="form-group col-md-3">
                                {% for error in form.communication_allowance.errors %}
                                [{{ error }}]
                                {% endfor %}
                                {{ form.communication_allowance.label }}
                                <div class="input-group-text">
                                    {{ form.communication_allowance(class="form-control") }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other Allowances -->
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {% for error in form.over_time.errors %}
                            [{{ error }}]
                            {% endfor %}
                            {{ form.over_time.label }}
                            <div class="input-group-text">
                                {{ form.over_time(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.other_allowance.label }}
                            <div class="input-group-text">
                                {{ form.other_allowance(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="selective--div">
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.bank_name.label }}
                            <div class="input-group-text">
                                {{ form.bank_name(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.bank_account.label }}
                            <div class="input-group-text">
                                {{ form.bank_account(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.branch_name.label }}
                            <div class="input-group-text">
                                {{ form.branch_name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.account_name.label }}
                            <div class="input-group-text">
                                {{ form.account_name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="selective--div">
                    <legend>Contact Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            {{ form.email.label }}
                            <div class="input-group-text">
                                {{ form.email(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            {{ form.phone.label }}
                            <div class="input-group-text">
                                {{ form.phone(class="form-control") }}
                            </div>
                            <div class="text-danger">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="selective--div">
                    <!-- Annual leave balance -->
                    <legend>Leave Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="bank_name">Annual Leave Balance:</label>
                            <div class="input-group-text">
                                {{ form.annual_leave_balance(class="form-control") }}
                            </div>
                        </div>
                        <!-- Extra leave days -->
                        <div class="form-group col-md-3">
                            <label for="bank_account">Extra Leave Days:</label>
                            <div class="input-group-text">
                                {{ form.extra_leave_days(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
                <button type="submit" class="submit-btn">Update</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
