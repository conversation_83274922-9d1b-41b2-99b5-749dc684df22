<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employees List</title>
</head>

<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('employees.register_employees') }}">
            <i class="fas fa-plus"></i> Add Employee
        </a>
    </div>
    <div>
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            <ul class="list-unstyled">
                {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                </li>
                {% endfor %}
            </ul>
            {% endif %}
            {% endwith %}
        </div>
        <h1 class="text-center mb-4">Employees' Leave Data Balances</h1>
        <div class="large--table">
            <div class="table-custom">
                <table class="table" id="employees_leave_balance_list">
                    <thead class="thead th-custom">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">First Name</th>
                            <th scope="col">Last Name</th>
                            <th scope="col">NID</th>
                            <th scope="col">RSSB No</th>
                            <th scope="col">DOB</th>
                            <th scope="col">Marital Status</th>
                            <th scope="col">Gender</th>
                            <th scope="col">Employee Type</th>
                            <th scope="col">Net Salary</th>
                            <th scope="col">Email</th>
                            <th scope="col">Phone</th>
                            <th scope="col">Job Title</th>
                            <th scope="col">Hire Date</th>
                            <th scope="col">Annual Leave Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr>
                            <td>{{ loop.index }}</td> <!-- Indexing starts from 1 -->
                            <td>{{ employee.first_name }}</td>
                            <td>{{ employee.last_name }}</td>
                            <td>{{ employee.nid }}</td>
                            <td>{{ employee.nsf }}</td>
                            <td>{% if employee.birth_date %}
                                {{ employee.birth_date }}
                                {% else %}

                                {% endif %}
                            </td>
                            <td>{{ employee.marital_status }}</td>
                            <td>{{ employee.gender }}</td>
                            <td>{{ employee.employee_type }}</td>
                            <td>{% if employee.net_salary %}
                                {{ Auxillary.format_amount(employee.net_salary) }}
                                {% else %}

                                {% endif %}
                            </td>
                            <td>{% if employee.email %}
                                {{ employee.email }}
                                {% else %}

                                {% endif %}
                            </td>
                            <td>{% if employee.phone %}
                                {{ employee.phone }}
                                {% else %}

                                {% endif %}
                            </td>
                            <td>{% if employee.job_title %}
                                {{ employee.job_title }}
                                {% else %}

                                {% endif %}
                            </td>
                            <td>{% if employee.hire_date %}
                                {{ employee.hire_date }}
                                {% else %}

                                {% endif %}
                            </td>
                            <td>{{ employee.annual_leave_balance }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
</body>

</html>