<!DOCTYPE html>
<html>
<head>
    <title>Upload Employees</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('employees.employees_list') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('employees.register_employees') }}">
            <i class="fas fa-plus"></i> Add Employee
        </a>
    </div>   
    <div class="real-form">
        <div id="message-container"></div>
        <div id="passed-container"></div>
        <div class="real-form"><h1 class="header-title">Upload bulk Employees</h1>
            <form action="{{ url_for('employees.upload_employees') }}" method="post" enctype="multipart/form-data">
                {{ form.csrf_token }}
                <div class="form-group">
                    <label for="file">Upload Employees</label>
                    <div class="input-group-text">
                        <span class="material-symbols-outlined icon">cloud_upload</span>
                        <input type="file" name="file" class="form-control" required/>
                    </div>
                    <input type="submit" value="Upload" class="btn-custom"/>
                </div>
            </form>
            <a href="{{ url_for('employees.download_employees_template') }}">Download Template</a>
        </div>  
    </div>
</body>
</html>