<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/employee_payslip.css') }}">
    <!--Google icons-->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
    <title>Employee Pay Slip</title>
</head>
<body>
    <div class="content">
        <!-- Add the Print Button -->
        <button class="print-button" onclick="printPayslip()">
            <i class="fa-solid fa-print"></i> Print
        </button>
        <div class="payslip--header">
            <div class="payslip">
                <h1>PAYSLIP</h1>
            </div>        
        </div>
            <div class="company--details">
                <table class="col--one">
                    <tr>
                        <th>
                           Name: 
                        </th>
                        <td>
                            {{ company_name }}
                        </td>
                    </tr>
                    <tr>
                        <th>TIN: </th>
                        <td>
                            {{ company_tin }}
                        </td>
                    </tr>
                    <tr>
                        <th>RSSB No: </th>
                        <td>{{ company_rssb}}</td>
                    </tr>
                </table>
                <table class="col--two">
                    <tr>
                        <th>Phone: </th>
                        <td>{{ company_phone }}</td>
                    </tr>
                    <tr>
                        <th>Email: </th>
                        <td>{{company_email}}</td>
                    </tr>
                    <tr>
                        <th>Address: </th>
                        <td>{{ company_address }}</td>
                    </tr>
                </table>
            </div>
        <div class="combined--tables">
            <table class="payslip--table">
            <tr>
                <th class="table-title" colspan="4">EMPLOYEE PAYSLIP FOR THE MONTH OF {{ month_name }} {{ month_year }} </th>
            </tr>
            <tr>
                <th>Employee Name</th>
                <td>{{ employee_name }}</td>
                <th>Bank name</th>
                <td>
                    <!-- if bank name is none display nothing. -->
                    {% if bank_name %}
                        {{ bank_name }}
                    {% else %}
                        
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Position</th>
                <td>{{ position }}</td>
                <th>Bank/Branch location</th>
                <td>
                    <!-- if branch name is none display nothing. -->
                    {% if bank_branch %}
                        {{ bank_branch }}
                    {% else %}
                        
                    {% endif %}
                </td>
            <tr>
                <th>Employee ID</th>
                <td>{{ id_number }}</td>
                <th>Bank Account No</th>
                <td>
                    <!-- if bank account is none display nothing. -->
                    {% if bank_account %}
                        {{ bank_account }}
                    {% else %}
                        
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Email</th>
                <td>
                    <!-- if email is none display nothing. -->
                    {% if email %}
                        {{ email }}
                    {% else %}
                        
                    {% endif %}
                </td>
                <th>Bank Account Name</th>
                <td>
                    <!-- If the account name is None display nothing. -->
                    {% if bank_name %}
                        {{ bank_name }}
                    {% else %}
                        
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Department</th>
                <td>
                    <!-- if department is none display nothing. -->
                    {% if department %}
                        {{ department }}
                    {% elif department == 'NaN' %}
                        N/A
                    {% else %}
                        
                    {% endif %}
                </td>
                <th>Currency</th>
                <td>RWF</td>
            </tr>
            <tr>
                <th>Telephone</th>
                <td>{{ phone }}</td>
                <th> Amount to Credit</th>
                <td>RWF {{ amount_to_credit }} </td>
            </tr>
            <tr>
                <th>Joining Date</th>
                <td>{{ joining_date }}</td>
                <th>Days Worked</th>
                <td>{{ days_worked }} </td>
            </tr>
            <tr>
                <th>RSSB Number</th>
                <td>{{ rssb_number }}</td>
                <th>Date from</th>
                <td>{{ day_one }}</td>
            </tr>
            <tr>
                <th>Location</th>
                <td>{{ address }}</td>
                <th>Date to</th>
                <td>{{ last_day_of_month }}</td>
            </tr>
        </table>
        <div class="space">
            <table class="payslip--table">
                <tr>
                    <th colspan="2">
                        Monthly Earnings
                    </th>
                    <th colspan="2">
                        Monthly Deductions
                    </th>
                </tr>
                <tr>
                    <th>
                        Basic Salary
                    </th>
                    <td>
                        RWF {{ basic_needed }} 
                    </td>
                    <th>
                        Pay as you Earn (PAYE)
                    </th>
                    <td>
                        RWF {{ paye }} 
                    </td>
                </tr>
                <tr>
                    <th>Transport Allowance</th>
                    <td>RWF {{ transport_allowance }}</td>
                    <th>
                        Pension Contribution
                    </th>
                    <td>
                        RWF {{ pension_ee_value }} 
                    </td>
                </tr>
                <tr>
                    <th>
                    </th>
                    <td>
                    </td>
                    <th>
                        Maternity Contribution
                    </th>
                    <td>
                    RWF {{ maternity_ee_value }} 
                    </td>
                </tr>
                <tr>
                    <th>
                    </th>
                    <td>
                    </td>
                    
                    <th>
                        Medical Contribution
                    </th>
                    <td>
                        RWF {{ medical_insurance_er }} 
                    </td>
                </tr>
                <tr>
                    <th>

                    </th>
                    <td>
                    </td>
                    <th>
                        RSSB CBHI
                    </th>
                    <td>
                        RWF {{ cbhi_value }} 
                    </td>
                </tr>
                <tr>
                    <th> Other Allowances
                    </th>
                    <td> RWF {{ allowances }} 
                    </td>
                    <th>
                    Other Deductions
                    </th>
                    <td>
                        RWF {{ other_deductions }} 
                    </td>
                </tr>
                <tr>
                    <th>
                        GROSS SALARY (GS)
                    </th>
                    <td>
                        RWF {{ gross_needed }} 
                    </td>
                    <th>
                        Total Payroll Deductions (TPD)
                    </th>
                    <td>
                        RWF {{ total_payroll_deductions }} 
                    </td>
            </table>
            <table class="payslip--table">
                <tr>
                    <th>Net Salary (NS) = (GS-TPD)</th>
                    <td>RWF {{ net_salary_val }} </td>
                </tr>
                
                <tr>
                    <td colspan="2"><strong>Amount in Words:</strong> {{ salary_in_words }} Rwandan Francs</td>
                </tr>
            </table>
        </div>
    </div>
        <div class="payslip--footer">
            <div class="footer--column-one">
                <h3>Employee:</h3>
                <p>{{ employee_name }} </td></p>
            </div>
            <div class="footer--column-two">
                <h3>Designation</h3>
                <p>{{ position }}</p>
            </div>
            <div class="footer--column-three">
                <h3>Signature and Date</h3>
                <p>____________________</p>
            </div>
        </div>
        <footer>
            <p id="disclaimer--content"> 
                <span id="disclaimer">Disclaimer:</span>This document {% if company_name  %}is a system-generated payslip from {{ company_name }} and {% else %} {% endif %} has been prepared in accordance with current Rwandan laws. If you find any errors, please contact our HR or finance department. Unauthorized disclosure, use, reproduction, sharing, or distribution of this document to third parties without prior written approval is prohibited . © {% if company_name  %} {{ company_name }} {% else %} {% endif %} , all rights reserved.
            </p>
        </footer>
    </div>
    <script>
        // Function to trigger print dialog
        function printPayslip() {
            window.print(); // This will open the browser's print dialog
        }
    </script>
</body>
</html>
