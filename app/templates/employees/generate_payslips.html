<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Payslips from Approved Payroll</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #17a2b8;
            color: white;
        }
        .btn-primary {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
        .btn-primary:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .alert-info {
            background-color: #e3f2fd;
            border-color: #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Generate Payslips from Approved Payroll</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('employees.generate_payslips_from_approved_payroll') }}">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="month">Month</label>
                                        <select class="form-control" id="month" name="month" required>
                                            <option value="1" {% if default_month == 1 %}selected{% endif %}>January</option>
                                            <option value="2" {% if default_month == 2 %}selected{% endif %}>February</option>
                                            <option value="3" {% if default_month == 3 %}selected{% endif %}>March</option>
                                            <option value="4" {% if default_month == 4 %}selected{% endif %}>April</option>
                                            <option value="5" {% if default_month == 5 %}selected{% endif %}>May</option>
                                            <option value="6" {% if default_month == 6 %}selected{% endif %}>June</option>
                                            <option value="7" {% if default_month == 7 %}selected{% endif %}>July</option>
                                            <option value="8" {% if default_month == 8 %}selected{% endif %}>August</option>
                                            <option value="9" {% if default_month == 9 %}selected{% endif %}>September</option>
                                            <option value="10" {% if default_month == 10 %}selected{% endif %}>October</option>
                                            <option value="11" {% if default_month == 11 %}selected{% endif %}>November</option>
                                            <option value="12" {% if default_month == 12 %}selected{% endif %}>December</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="year">Year</label>
                                        <input type="number" class="form-control" id="year" name="year" value="{{ default_year }}" min="2000" max="2100" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch mt-4">
                                            <input type="checkbox" class="custom-control-input" id="send_email" name="send_email">
                                            <label class="custom-control-label" for="send_email">Send Payslips via Email</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary">Generate Payslips</button>
                                    <a href="javascript:history.back()" class="btn btn-secondary">Back</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                {% if success %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h3 class="card-title">Process Summary</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <p>Payslip generation completed for {{ default_month }}/{{ default_year }}.</p>
                            <p>Check the flash messages above for detailed results.</p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="card mt-4">
                    <div class="card-header">
                        <h3 class="card-title">Instructions</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5>How to use this feature:</h5>
                            <ol>
                                <li>Select the month and year for which you want to generate payslips.</li>
                                <li>Check the "Send Payslips via Email" option if you want to automatically email the payslips to employees.</li>
                                <li>Click the "Generate Payslips" button to start the process.</li>
                                <li>The system will generate payslips for all employees with approved payroll data for the selected month.</li>
                                <li>If you selected the email option, payslips will be sent to employees who have an email address in the system.</li>
                            </ol>
                            <p><strong>Note:</strong> This process will only generate payslips for payroll records with a status of "Approved".</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
