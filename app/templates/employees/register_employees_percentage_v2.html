<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a href="{{ url_for('my_employees.employees_list') }}" class="btn-edit">
                <i class="fi fi-rr-list"></i> Employees
            </a>
            <div class="right-buttons-group">
                <a href="{{ url_for('my_employees.register_employees') }}" class="btn-edit">
                    <i class="fi fi-rr-user-add"></i>EE(Fixed Salary)
                </a>
            </div>
        </div>
    </div>
    <div class="dyn_container">
        <div class="grey-container">
            <h2 class="mb-4">Register Employee - Percentage Based Salary</h2>
            <ol>
                <li>Fill in the personal information section with the employee's details.</li>
                <li>Complete the employee information section, including TIN and department.</li>
                <li>Choose the salary type (Gross or Net) and enter the salary amount.</li>
                <li>Enter the basic salary percentage and other allowances as percentages of gross salary.</li>
                <li>Provide bank details for salary deposits.</li>
                <li>Review all information and submit the form to register the employee.</li>
                <li>Ensure that the total percentage of allowances does not exceed 100%.</li>
            </ol>
        </div>
        <div class="form--container">
            <h1>Register Employee - Percentage Based Salary</h1>
            <p class="text-info">This form is specifically for registering employees with percentage-based salary calculations.</p>
            <form action="{{ url_for('my_employees.register_employees_percentage') }}" method="POST">
                {{ form.hidden_tag() }}

                <!-- Personal Information -->
                <div class="selective--div">
                    <legend>Personal Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="first_name">First Name: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.first_name(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="last_name">Last Name: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.last_name(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="nid">NID: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.nid(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="rssb_number">RSSB Number: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.rssb_number(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="gender">Gender: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.gender(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="birth_date">Birth Date:</label>
                            <div class="input-group-text">
                                {{ form.birth_date(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="marital_status">Marital Status:</label>
                            <div class="input-group-text">
                                {{ form.marital_status(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employee Information -->
                <div class="selective--div">
                    <legend>Employee Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="employee_tin">Employee TIN:</label>
                            <div class="input-group-text">
                                {{ form.employee_tin(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="employee_type">Employee Type: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.employee_type(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="department">Department:</label>
                            <div class="input-group-text">
                                {{ form.department(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="job_title">Job Title:</label>
                            <div class="input-group-text">
                                {{ form.job_title(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="hire_date">Hire Date:</label>
                            <div class="input-group-text">
                                {{ form.hire_date(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="contract_end_date">Contract End Date:</label>
                            <div class="input-group-text">
                                {{ form.contract_end_date(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Salary Information -->
                <div class="selective--div">
                    <legend>Salary Information</legend>
                    <div class="alert alert-info">
                        <p><strong>Note:</strong> This form uses percentage-based salary calculations. You can enter either Gross Salary or Net Salary.</p>
                    </div>

                    <!-- Hidden field for calculation method -->
                    <input type="hidden" name="salary_calculation_method" value="percentage_based">

                    <div class="form-row">
                        <!-- Salary Type -->
                        <div class="form-group col-md-6">
                            <label for="salary_type">Salary Type: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                <select name="salary_type" id="salary_type" class="form-control">
                                    <option value="">Select Salary Type</option>
                                    <option value="gross_salary">Gross Salary</option>
                                    <option value="net_salary">Net Salary</option>
                                </select>
                            </div>
                            <small class="form-text text-muted">Choose whether you're entering gross or net salary</small>
                        </div>
                        <!-- Salary Amount -->
                        <div class="form-group col-md-6">
                            <label for="salary_amount"><span id="salary_amount_label">Salary Amount</span>: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.salary_amount(class_="form-control", id="salary_amount", placeholder="Enter salary amount") }}
                            </div>
                            <small class="form-text text-muted" id="salary_amount_help">Select salary type first</small>
                        </div>
                    </div>

                    <!-- Net Salary Information -->
                    <div id="net_salary_info" class="alert alert-warning" style="display: none;">
                        <p><strong>Net Salary Mode:</strong> The system will use goal seek to calculate the gross salary and components that result in your target net salary. Basic salary percentage is required for this calculation.</p>This form uses percentage-based salary
                    </div>
                </div>

                <!-- Percentage-based Allowances -->
                <div class="selective--div">
                    <legend>Salary Components (Percentage of Gross Salary)</legend>
                    <div class="alert alert-warning">
                        <p><strong>Important:</strong> Enter percentages for each component. Total should not exceed 100%.</p>
                    </div>

                    <div class="form-row">
                        <!-- Basic Salary Percentage -->
                        <div class="form-group col-md-3">
                            <label for="basic_salary_percentage">Basic Salary %: <span class="text-danger">*</span></label>
                            <div class="input-group-text">
                                {{ form.basic_salary_percentage(class_="form-control", id="basic_salary_percentage", placeholder="e.g., 70", required=True) }}
                            </div>
                        </div>

                        <!-- Transport Allowance Percentage -->
                        <div class="form-group col-md-3">
                            <label for="transport_allowance_percentage">Transport Allowance %:</label>
                            <div class="input-group-text">
                                {{ form.transport_allowance_percentage(class_="form-control", id="transport_allowance_percentage", placeholder="e.g., 15") }}
                            </div>
                        </div>

                        <!-- Housing Allowance Percentage -->
                        <div class="form-group col-md-3">
                            <label for="housing_allowance_percentage">Housing Allowance %:</label>
                            <div class="input-group-text">
                                {{ form.housing_allowance_percentage(class_="form-control", id="housing_allowance_percentage", placeholder="e.g., 10") }}
                            </div>
                        </div>

                        <!-- Communication Allowance Percentage -->
                        <div class="form-group col-md-3">
                            <label for="communication_allowance_percentage">Communication Allowance %:</label>
                            <div class="input-group-text">
                                {{ form.communication_allowance_percentage(class_="form-control", id="communication_allowance_percentage", placeholder="e.g., 5") }}
                            </div>
                        </div>
                    </div>

                    <!-- Real-time calculation display -->
                    <div id="calculation-preview" class="mt-3" style="display: none;">
                        <h6>Calculated Amounts:</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Basic Salary:</strong> <span id="calc-basic">0</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Transport:</strong> <span id="calc-transport">0</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Housing:</strong> <span id="calc-housing">0</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Communication:</strong> <span id="calc-communication">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other Allowances -->
                <div class="selective--div">
                    <legend>Other Allowances (Fixed Amounts)</legend>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="over_time">Over Time:</label>
                            <div class="input-group-text">
                                {{ form.over_time(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="other_allowance">Other Allowances:</label>
                            <div class="input-group-text">
                                {{ form.other_allowance(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Details -->
                <div class="selective--div">
                    <legend>Bank Details</legend>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="bank_name">Bank Name:</label>
                            <div class="input-group-text">
                                {{ form.bank_name(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="bank_account">Bank Account:</label>
                            <div class="input-group-text">
                                {{ form.bank_account(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="branch_name">Branch Name:</label>
                            <div class="input-group-text">
                                {{ form.branch_name(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="account_name">Account Name:</label>
                            <div class="input-group-text">
                                {{ form.account_name(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="selective--div">
                    <legend>Contact Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="email">Email:</label>
                            <div class="input-group-text">
                                {{ form.email(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="phone">Phone:</label>
                            <div class="input-group-text">
                                {{ form.phone(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Leave Information -->
                <div class="selective--div">
                    <legend>Leave Information</legend>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="annual_leave_balance">Annual Leave Balance:</label>
                            <div class="input-group-text">
                                {{ form.annual_leave_balance(class_="form-control") }}
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="extra_leave_days">Extra Leave Days:</label>
                            <div class="input-group-text">
                                {{ form.extra_leave_days(class_="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="submit-btn">Register Employee</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const salaryTypeField = document.getElementById('salary_type');
    const salaryAmountField = document.getElementById('salary_amount');
    const salaryAmountLabel = document.getElementById('salary_amount_label');
    const salaryAmountHelp = document.getElementById('salary_amount_help');
    const netSalaryInfo = document.getElementById('net_salary_info');
    const basicPercentageField = document.getElementById('basic_salary_percentage');
    const transportPercentageField = document.getElementById('transport_allowance_percentage');
    const housingPercentageField = document.getElementById('housing_allowance_percentage');
    const communicationPercentageField = document.getElementById('communication_allowance_percentage');
    const calculationPreview = document.getElementById('calculation-preview');

    const percentageFields = [
        basicPercentageField,
        transportPercentageField,
        housingPercentageField,
        communicationPercentageField
    ];

    // Handle salary type change
    function handleSalaryTypeChange() {
        const salaryType = salaryTypeField.value;

        if (salaryType === 'gross_salary') {
            salaryAmountLabel.textContent = 'Gross Salary Amount';
            salaryAmountHelp.textContent = 'Enter the gross salary amount';
            netSalaryInfo.style.display = 'none';
            calculationPreview.style.display = 'none';
        } else if (salaryType === 'net_salary') {
            salaryAmountLabel.textContent = 'Net Salary Amount';
            salaryAmountHelp.textContent = 'Enter the target net salary amount';
            netSalaryInfo.style.display = 'block';
            calculationPreview.style.display = 'none';
        } else {
            salaryAmountLabel.textContent = 'Salary Amount';
            salaryAmountHelp.textContent = 'Select salary type first';
            netSalaryInfo.style.display = 'none';
            calculationPreview.style.display = 'none';
        }

        // Clear salary amount when type changes
        salaryAmountField.value = '';
    }

    function calculateAndDisplay() {
        const salaryType = salaryTypeField.value;
        const salaryAmount = parseFloat(salaryAmountField.value) || 0;
        const basicPercentage = parseFloat(basicPercentageField.value) || 0;
        const transportPercentage = parseFloat(transportPercentageField.value) || 0;
        const housingPercentage = parseFloat(housingPercentageField.value) || 0;
        const communicationPercentage = parseFloat(communicationPercentageField.value) || 0;

        // Only show calculations for gross salary (net salary calculations happen on server)
        if (salaryType === 'gross_salary' && salaryAmount > 0) {
            // Calculate amounts
            const basicAmount = (salaryAmount * basicPercentage / 100).toFixed(2);
            const transportAmount = (salaryAmount * transportPercentage / 100).toFixed(2);
            const housingAmount = (salaryAmount * housingPercentage / 100).toFixed(2);
            const communicationAmount = (salaryAmount * communicationPercentage / 100).toFixed(2);

            // Update display
            document.getElementById('calc-basic').textContent = basicAmount;
            document.getElementById('calc-transport').textContent = transportAmount;
            document.getElementById('calc-housing').textContent = housingAmount;
            document.getElementById('calc-communication').textContent = communicationAmount;

            calculationPreview.style.display = 'block';
        } else if (salaryType === 'net_salary' && salaryAmount > 0) {
            // For net salary, show a message that calculation will happen on server
            document.getElementById('calc-basic').textContent = 'Will be calculated';
            document.getElementById('calc-transport').textContent = 'Will be calculated';
            document.getElementById('calc-housing').textContent = 'Will be calculated';
            document.getElementById('calc-communication').textContent = 'Will be calculated';
            calculationPreview.style.display = 'block';
        } else {
            calculationPreview.style.display = 'none';
        }
    }

    function validatePercentages() {
        const basicPercentage = parseFloat(basicPercentageField.value) || 0;
        const transportPercentage = parseFloat(transportPercentageField.value) || 0;
        const housingPercentage = parseFloat(housingPercentageField.value) || 0;
        const communicationPercentage = parseFloat(communicationPercentageField.value) || 0;

        const total = basicPercentage + transportPercentage + housingPercentage + communicationPercentage;

        // Remove existing warnings
        const existingWarning = document.getElementById('percentage-warning');
        if (existingWarning) {
            existingWarning.remove();
        }

        const existingRemaining = document.getElementById('remaining-percentage');
        if (existingRemaining) {
            existingRemaining.remove();
        }

        // Add warning if total exceeds 100%
        if (total > 100) {
            const warning = document.createElement('div');
            warning.id = 'percentage-warning';
            warning.className = 'alert alert-danger mt-2';
            warning.innerHTML = `<strong>Warning:</strong> Total percentages (${total.toFixed(2)}%) exceed 100%!`;
            calculationPreview.parentNode.appendChild(warning);
        }

        // Show remaining percentage
        const remaining = Math.max(0, 100 - total);
        const remainingDiv = document.createElement('div');
        remainingDiv.id = 'remaining-percentage';
        remainingDiv.className = remaining > 0 ? 'alert alert-success mt-2' : 'alert alert-info mt-2';
        remainingDiv.innerHTML = `<strong>Remaining:</strong> ${remaining.toFixed(2)}% available for allocation`;
        calculationPreview.parentNode.appendChild(remainingDiv);
    }

    // Add event listeners
    salaryTypeField.addEventListener('change', handleSalaryTypeChange);
    salaryAmountField.addEventListener('input', calculateAndDisplay);

    percentageFields.forEach(field => {
        if (field) {
            field.addEventListener('input', function() {
                calculateAndDisplay();
                validatePercentages();
            });
            field.addEventListener('blur', function() {
                calculateAndDisplay();
                validatePercentages();
            });
        }
    });

    // Form validation before submit
    document.querySelector('form').addEventListener('submit', function(e) {
        const salaryType = salaryTypeField.value;
        const basicPercentage = parseFloat(basicPercentageField.value) || 0;
        const transportPercentage = parseFloat(transportPercentageField.value) || 0;
        const housingPercentage = parseFloat(housingPercentageField.value) || 0;
        const communicationPercentage = parseFloat(communicationPercentageField.value) || 0;

        const total = basicPercentage + transportPercentage + housingPercentage + communicationPercentage;

        // Validate salary type selection
        if (!salaryType) {
            e.preventDefault();
            alert('Please select a salary type (Gross Salary or Net Salary).');
            return false;
        }

        // Validate basic salary percentage (required for both gross and net)
        if (basicPercentage <= 0) {
            e.preventDefault();
            alert('Basic Salary percentage is required and must be greater than 0%.');
            return false;
        }

        // For net salary, basic percentage is especially important
        if (salaryType === 'net_salary' && basicPercentage <= 0) {
            e.preventDefault();
            alert('Basic Salary percentage is required for net salary calculations.');
            return false;
        }

        // Validate total percentages
        if (total > 100) {
            e.preventDefault();
            alert(`Total percentages (${total.toFixed(2)}%) cannot exceed 100%.`);
            return false;
        }

        // Validate salary amount
        if (!salaryAmountField.value || parseFloat(salaryAmountField.value) <= 0) {
            e.preventDefault();
            const salaryLabel = salaryType === 'net_salary' ? 'Net salary' : 'Gross salary';
            alert(`${salaryLabel} amount is required and must be greater than 0.`);
            return false;
        }
    });

    // Initialize calculations if values are present
    calculateAndDisplay();
    validatePercentages();
});
</script>
{% endblock %}
