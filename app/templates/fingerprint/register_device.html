<!DOCTYPE html>
<html>
<head>
    <title>Register Device</title>
    <link rel="stylesheet" type="text/css" href="/css/fingerprint/register_device.css">
</head>
<body>
    <h1>Register Device</h1>
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul>
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <form method="POST" action="{{ url_for('fingerprint.register_device') }}">
        {{ form.hidden_tag() }}
        <div class="form-row">
            <div class="form-group col-md-6">
                {% for error in form.device_sn.errors %}
                    <span class="text-danger">[{{ error }}]</span>
                {% endfor %}
                {{ form.device_sn.label }}
                {{ form.device_sn(class="form-control", placeholder="Enter device serial number", required=True) }}
            </div>
            <div class="form-group col-md-6">
                {% for error in form.company_id.errors %}
                    <span class="text-danger">[{{ error }}]</span>
                {% endfor %}
                {{ form.company_id.label }}
                {{ form.company_id(class="form-control", required=True) }}
            </div>
        </div>
        <div class="form-group">
            {{ form.submit(class="btn btn-primary") }}
        </div>
    </form><br>
    <h1>Company Devices</h1>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Device Serial Number</th>
                <th>Company Name</th>
                <th>Registered At</th>
            </tr>
        </thead>
        <tbody>
            {% for company in companies %}
            <tr>
                <td>
                    {% for device in company.devices %}
                        {{ device.device_sn }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </td>
                <td>{{ company.company_name }}</td>
                <td>{{ company.created_at }}</td>
            </tr>
        {% endfor %}
        </tbody>
</body>
</html>
