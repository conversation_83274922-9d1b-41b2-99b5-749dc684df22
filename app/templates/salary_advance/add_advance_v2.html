<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="template-link btn-edit" href="{{ url_for('advance_requests_v2.view_all_advance_requests') }}">
                    <i class="fi fi-rr-list"></i> Advances
                </a>
            </div>
        </div>
        <div class="dyn_container">
            
            <div class="form--container">
                <h1>Record Salary Advance</h1>
                <div class="real--form">
                    <form method="post" action="{{ url_for('advance_requests_v2.add_advance') }}">
                        {{ form.csrf_token }}
                        <div class="form-row">
                            <!-- Employee Select Field -->
                            <div class="form-group col-sm-3">
                                <label for="employee_id">Employee</label>
                                {{ form.employee_id(class="form-control") }}
                            </div>
                            <!-- Description Field -->
                            <div class="form-group col-sm-3">
                                <label for="description" class="col-sm-6 col-form-label">Reason</label>
                                <div class="input-group-text">
                                    {{ form.reason(class="form-control", placeholder='Reason for the salary advance') }}
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <!-- Amount Field-->
                            <div class="form-group">
                                <label for="amount" class="col-sm-6 col-form-label">Amount</label>
                                <div class="input-group-text">
                                    {{ form.amount(class="form-control", placeholder="Amount to be advanced") }}
                                </div>
                            </div>
                            <!-- Advance Date Field -->
                            <div class="form-group">
                                <label for="advance_date" class="col-sm-6 col-form-label">Due date</label>
                                <div class="input-group-text">
                                    {{ form.advance_date(class="form-control") }}
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">Save</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

{% endblock %}