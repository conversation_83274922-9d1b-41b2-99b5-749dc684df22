<!DOCTYPE html>
<html>
<head>
    <title>Salary Advance</title>
    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('advance_requests.view_advance_requests') }}">
            <i class="fas fa-arrow-left"></i> Back
        </a>
    </div>
    <div class="real-form">
        <div class="form-error-messages">
            <div id="message-container"></div>
            <div id="passed-container"></div>
        </div>
        <h1>Salary Advance Request</h1>
        <form method="post" id="salary-advance-form">
            {{ form.csrf_token }}
            <div class="form-row">
                <div class="form-group">
                    <label for="amount">{{ form.amount.label }}</label>
                    <div class="input-group-text">
                        {{ form.amount(class="form-control", id="amount") }}
                    </div>
                </div>           
                <!-- Installment Amounts: Loop over the dynamic field list -->
                <div class="form-group">
                    <label for="installment_amounts">{{ form.installment_amounts.label }}</label>
                    <input type="hidden" id="installment-amounts-count" value="{{ form.installment_amounts|length }}">
                    <div id="installment-amounts-container">
                        {% for field in form.installment_amounts %}
                            <div class="input-group-text">
                                {{ field(class="form-control") }}
                            </div>
                        {% endfor %}
                    </div>
                    <button type="button" id="add-installment" class="btn btn-edit">Add Installment</button>
                </div>
            </div>

           

            <div class="form-group">
                {{ form.submit(class="btn-continue") }}
            </div>
        </form>
    </div>

<script src="{{ url_for('static', filename='scripts/salary_advance.js') }}"></script>
</body>
</html>
