<!DOCTYPE html>
<html>
<head>
    <title>Salary Advance Approvals</title>
</head>
<body>
    <div class="container">
        <h2>Salary Advance Approvals</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Employee Name</th>
                    <th>Amount</th>
                    <th>Approver</th>
                    <th>Approver Role</th>
                    <th>Remarks</th>
                    <th>Status</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in grouped_approvals %}
                    <tr>
                        <td rowspan="{{ employee.rowspan }}">{{ employee.full_name }}</td>
                        {% for request in employee.requests %}
                            {% if not loop.first %}<tr>{% endif %}
                            
                            <td rowspan="{{ request.rowspan }}">{{ request.amount }}</td>
                            {% for approval in request.approvals %}
                                {% if not loop.first %}<tr>{% endif %}
                                <td>{{ LeaveApproval.get_approver_name(approval.approver_id, approval.approver_role, db_session) }}</td>
                                <td>{{ approval.approver_role }}</td>
                                <td>{{ approval.remarks }}</td>
                                <td>{{ approval.status }}</td>
                                <td>{{ approval.created_at }}</td>

                                {% if not loop.last %}</tr>{% endif %}
                            {% endfor %}
                        {% endfor %}
                    </tr>
                {% endfor %}
            </tbody>
            
            
            
        </table>
    </div>
</body>
</html>
