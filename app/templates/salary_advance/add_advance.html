<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Salary Advances</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> Back
        </a>
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('advance_requests.view_advance_requests') }}">
            <i class="fas fa-list"></i> Salary Advances
        </a>
    </div>
    <div class="dynamic--form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <h1>Add Salary Advance</h1>
        <div class="real--form">
            <form method="post" action="{{ url_for('advance_requests.add_advance') }}">
                {{ form.csrf_token }}
                <div class="form-row">
                    <!-- Employee Select Field -->
                    <div class="form-group col-sm-3">
                        <label for="employee_select">Employee</label>
                        <select class="input-group-text form-control" name="employee_select" id="employee_select">
                            {% for employee in employees %}
                                <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                            {% endfor %}
                        </select>
                        {{ form.employee_id }}
                    </div>
                    <!-- Description Field -->
                    <div class="form-group col-sm-3">
                        <label for="description" class="col-sm-6 col-form-label">Reason</label>
                        <div class="input-group-text">
                            {{ form.reason(class="form-control", placeholder='Reason for the salary advance') }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <!-- Amount Field -->
                    <div class="form-group">
                        <label for="amount" class="col-sm-6 col-form-label">Amount</label>
                        <div class="input-group-text">
                            {{ form.amount(class="form-control", placeholder="Amount to be advanced") }}
                        </div>
                    </div>
                    <!-- Advance Date Field -->
                    <div class="form-group">
                        <label for="advance_date" class="col-sm-6 col-form-label">Advance Date</label>
                        <div class="input-group-text">
                            <span>
                                <span class="material-symbols-outlined icon">date_range</span>
                            </span>
                            {{ form.advance_date(class="form-control") }}
                        </div>
                    </div>
                </div>
                {{ form.submit(class="btn-custom") }}
            </form>
        </div>
    </div>
</body>
</html>
