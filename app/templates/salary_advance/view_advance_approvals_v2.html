<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Approved salary advances</h1>
                <div class="right-buttons-group">      
                    <a class="btn-edit" href="{{ url_for('advance_requests_v2.view_all_advance_requests') }}">
                        <i class="fi fi-rr-list"></i> Pending
                    </a>
                    <a class ="template-link btn-edit" href="{{ url_for('advance_requests_v2.add_advance') }}">
                        <i class="fi fi-rr-plus-small"></i> Requests
                    </a>
                </div>
            </div>
            <div class="filter-container">
                <div>
                    <label for="approvedAdvanceSearchInput">Search</label>
                    <input type="text" id="approvedAdvanceSearchInput" placeholder="Search by name, status..." class="form-control">
                </div>
                <button class="toggle-filters-btn" id="approvedAdvanceToggleFiltersBtn">Show Period Filters</button>
                <div class="advanced-filters" id="approvedAdvanceAdvancedFilters">
                    <h3>Filter by:</h3>
                    <div class="form-group">
                        <label for="approverFilter">Approved by</label>
                        <select id="approverFilter" class="form-select">
                            <option value="">All</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="approverRole">Approver Role</label>
                        <select id="approverRole" class="form-select">
                            <option value="">All</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="requestStatusFilter">Request Status</label>
                        <select id="requestStatusFilter" class="form-select">
                            <option value="">All</option>
                        </select>
                    </div>
                        </div>
                    </div>    
                </div>
            </div>
            <div class="dyn_container">
                <div class="container">
                    <table class="table table-bordered" id="approvedAdvanceRequests">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Employee Name</th>
                                <th>Amount</th>
                                <th>Approver</th>
                                <th>Approver Role</th>
                                <th>Remarks</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in grouped_approvals %}

                                <tr>
                                    <td rowspan="{{ employee.rowspan}}">{{ loop.index }}</td>
                                    <td rowspan="{{ employee.rowspan}}">{{ employee.full_name }}</td>
                                    {% for request in employee.requests %}
                                        {% if not loop.first %}<tr>{% endif %}
                                        {% for approval in request.approvals %}
                                            {% if not loop.first %}<tr>{% endif %}
                                        <td>{{ request.amount }}</td>
                                            <td>{{ LeaveApproval.get_approver_name(approval.approver_id, approval.approver_role, db_session) }}</td>
                                            <td>{{ approval.approver_role }}</td>
                                            <td>{{ approval.remarks }}</td>
                                            <td class="green-box"><span class="status-text">{{ approval.status|default('') }}</span></td>
                                            <td>{{ approval.created_at }}</td>
                                            {% if not loop.last %}</tr>{% endif %}
                                        {% endfor %}
                                    {% endfor %}
                                    </tr>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <div class="pagination" id="approvedAdvancePagination">
                </div>
            </div>
        </div>
{% endblock %}