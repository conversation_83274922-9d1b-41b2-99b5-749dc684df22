<!DOCTYPE html>
<html>
<head>
    <title>Salary Advance</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('advance_requests.view_advance_requests') }}">
            <i class="fas fa-arrow-left"></i> Back
        </a>
    </div>
    <div class="real-form">
        <div class="form-error-messages">
            <div id="message-container"></div>
            <div id="passed-container"></div>
        </div>
        <h1>Salary Advance Request</h1>
        <form method="POST" action="{{ url_for('advance_requests.apply_for_advance') }}" id="salary-advance-form">

            <div class="form-row">
                <!-- Amount Field -->
                <div class="form-group">
                    <label for="amount">Amount</label>
                    <div class="input-group-text">
                        <input type="number" class="form-control" id="amount" name="amount" placeholder="Enter amount" required>
                    </div>
                </div>

                <!-- Installment Amounts and Due Dates -->
                <div class="form-group">
                    <label for="installment_amounts">Installment Amounts and Due Dates</label>
                    <input type="hidden" id="installment-amounts-count" value="1">
                    <div id="installment-amounts-container">
                        <div class="installment-field input-group-text installment-row">
                            <input type="number" class="form-control installment-amount" name="installment_amounts[]" placeholder="Installment Amount" required>
                            <input type="date" class="form-control installment-due-date" name="due_dates[]" placeholder="Due Date" required>
                        </div>
                    </div>
                    <button type="button" id="add-installment" class="btn btn-edit">Add Installment</button>
                </div>
            </div>

            <!-- Reason Field -->
            <div class="form-group">
                <label for="reason">Reason</label>
                <div class="input-group-text">
                    <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="Reason for advance" required></textarea>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <button type="submit" class="btn-continue">Submit</button>
            </div>
        </form>
    </div>

<script src="{{ url_for('static', filename='scripts/salary_advance.js') }}"></script>
</body>
</html>
