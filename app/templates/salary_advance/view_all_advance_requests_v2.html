<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('advance_requests_v2.view_advance_approvals') }}">
                    <i class="fi fi-rr-list"></i> Approved
                </a>
                <a class="btn-edit" href="{{ url_for('advance_requests_v2.add_advance') }}">
                    <i class="fi fi-rr-plus-small"></i> Salary Advance
                </a>
            </div>
        </div>
    <div class="dyn_container">
        <h1>Pending advance requests</h1>

        {% if grouped_requests %}
            <table>
                <tr>
                    <th>Employee Name</th>
                    <th>Amount</th>
                    <th>Reason</th>
                    <th>Request Date</th>
                    <th>Status</th>
                    <th>Installment Plans</th>
                    <th>Actions</th>
                </tr>
                {% for employee_id, data in grouped_requests.items() %}
                    <tr>
                        <td rowspan="{{ data.requests|length }}">{{ data.full_name }}</td>
                        {% for request in data.requests %}
                            {% if not loop.first %}<tr>{% endif %}
                            <td>{{ request.amount }}</td>
                            <td>{{ request.reason }}</td>
                            <td>{{ request.created_at }}</td>
                            <td class="orange-box"><span class="status-text">{{ request.status }}</span> </td>
                            <td>
                                <table class="installment-table">
                                    <tr>
                                        <th>Installment</th>
                                        <th>Due Date</th>
                            
                                    </tr>
                                    {% for installment in request.installment_plans %}
                                    <tr>
                                        <td>{{ installment.planned_amount }}</td>
                                        <td>{{ installment.due_date.strftime('%d/%m/%Y') }}</td>
                                    </tr>
                                    {% endfor %}
                                </table>
                            </td>
                            <td>
                                <div class="table-buttons">
                                    <a  class="btn-edit" href="{{ url_for('advance_requests_v2.approve_advance_request', request_id=request.request_id) }}">
                                        <i class="fi fi-rr-checkbox"></i>
                                        <p>Approve</p>
                                    </a>
                                    <a class="btn-edit" href="{{ url_for('advance_requests_v2.update_advance_request', request_id=request.request_id) }}">
                                        <i class="fi fi-rr-pencil"></i>
                                        <p>Edit</p>
                                    </a>
                                </div>
                            </td>
                            {% if not loop.last %}</tr>{% endif %}
                        {% endfor %}
                    </tr>
                {% endfor %}
            </table>
        {% else %}
            <p>No salary advance requests found.</p>
        {% endif %}
    </div>
</div>
{% endblock %}
