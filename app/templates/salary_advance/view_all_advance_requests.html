<!DOCTYPE html>
<html>
<head>
    <title>View Advance Requests</title>
    <style>
        .installment-table {
            margin-top: 10px;
            border: 1px solid #ddd;
        }
        .installment-table th, .installment-table td {
            border: 1px solid #ddd;
            padding: 5px;
        }
    </style>
</head>
<body>
    <h1>View Advance Requests</h1>
    {% if grouped_requests %}
        <table border="1">
            <tr>
                <th>Employee Name</th>
                <th>Amount</th>
                <th>Reason</th>
                <th>Request Date</th>
                <th>Status</th>
                <th>Installment Plans</th>
                <th>Actions</th>
            </tr>
            {% for employee_id, data in grouped_requests.items() %}
                <tr>
                    <td rowspan="{{ data.requests|length }}">{{ data.full_name }}</td>
                    {% for request in data.requests %}
                        {% if not loop.first %}<tr>{% endif %}
                        <td>{{ request.amount }}</td>
                        <td>{{ request.reason }}</td>
                        <td>{{ request.created_at }}</td>
                        <td>{{ request.status }}</td>
                        <td>
                            <table class="installment-table">
                                <tr>
                                    <th>Installment</th>
                                    <th>Due Date</th>
                        
                                </tr>
                                {% for installment in request.installment_plans %}
                                <tr>
                                    <td>{{ installment.planned_amount }}</td>
                                    <td>{{ installment.due_date.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                {% endfor %}
                            </table>
                        </td>
                        <td>
                            <div class="table-buttons">
                                <a  class="template-link btn-image" href="#" data-template-url="{{ url_for('advance_requests.approve_advance_request', request_id=request.request_id) }}">
                                    <i class="fi fi-rr-checkbox"></i>
                                <p>Approve</p>
                                </a>
                                <a class="template-link btn-image" href="#" data-template-url="{{ url_for('advance_requests.update_advance_request', request_id=request.request_id) }}" ">
                                    Edit
                                </a>
                            </div>
                        </td>
                        {% if not loop.last %}</tr>{% endif %}
                    {% endfor %}
                </tr>
            {% endfor %}
        </table>
    {% else %}
        <p>No salary advance requests found.</p>
    {% endif %}
</body>
</html>
