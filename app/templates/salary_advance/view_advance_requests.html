<!DOCTYPE html>
<html>
<head>
    <title>View Advance Requests</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.employee_dashboard') }}">
            <i class="fas fa-arrow-left"></i> Back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('advance_requests.apply_for_advance') }}">
            <i class="fas fa-plus"></i> new request
        </a>
    </div>
    <h1>Advance Requests</h1>
    <table>
        <tr>
            <th>Amount</th>
            <th>Reason</th>
            <th>Due Date</th>
            <th>Request Date</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
        {% for advance_request in salary_advance_requests %}
            <tr>
                <td>{{ advance_request.amount }}</td>
                <td>{{ advance_request.reason }}</td>
                <td>{{ advance_request['due_date'] }}</td>
                <td>{{ advance_request.created_at }}</td>
                <td>{{ advance_request.status }}</td>
                <td>
                    <div class="table-buttons">
                        <a href="#" class="btn-image template-link" data-template-url="{{ url_for('advance_requests.update_advance_request', request_id=advance_request.request_id) }}">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a class="template-link btn-cancel" href="#" data-template-url="{{ url_for('advance_requests.delete_advance_request', request_id=advance_request.request_id) }}">
                            <i class="fi fi-rr-trash"></i>
                            <span>Delete</span>
                        </a>
                    </div>
                </td>
            </tr>
        {% endfor %}
    </table>
</body>
</html>