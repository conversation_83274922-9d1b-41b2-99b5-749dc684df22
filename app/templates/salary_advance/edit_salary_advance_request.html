

<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('advance_requests_v2.view_all_advance_requests') }}">
                    <i class="fi fi-rr-list"></i> Requests
                </a>
            </div>
        </div>
        <div class="dyn_container">
            
                <div class="form--container">
                    <h1>Edit salary Advance Request</h1>
                    <form method="POST" action="{{ url_for('advance_requests_v2.update_advance_request', request_id=salary_advance_request['request_id']) }}" id="salary-advance-form">
                        <!-- Amount Field -->
                         <div class="form-row">
                            <div class="form-group">
                                <label for="amount">Total Amount</label>
                                <input type="number" class="form-control" id="amount" name="amount" value="{{ salary_advance_request['amount'] }}" required>
                            </div>
                        </div>
                        <!-- Installments Section -->
                         <div class="form-row">
                            <div class="form-group" style="margin: 10px 0;">
                                <label for="installments">Installment Amounts</label>
                                <input type="hidden" id="installment-amounts-count" value="{{ installment_plans|length }}">
                            
                                <div id="installment-amounts-container" class="form-group">
                                    {% for installment in installment_plans %}
                                    <div class="input-group-text">
                                        <input type="number" class="form-control installment-amount" name="installment_amounts[]" 
                                            value="{{ installment['planned_amount'] }}" placeholder="Installment Amount" required>
                                        
                                        <!-- Display the date in YYYY-MM-DD (browser default) but allow user-friendly DD/MM/YYYY interaction -->
                                        <input type="date" class="form-control installment-due-date" name="due_dates[]" 
                                            value="{{ installment['due_date'].strftime('%Y-%m-%d') }}" required>
                                    </div>
                                    {% endfor %}
                                </div>
                                <div style="margin: 10px 0;"> 
                                    <button type="button" id="add-installment" class="btn-edit"><i class="fi fi-rr-plus-small"></i>  Installment</button>
                                </div>
                            </div>
                                
                            </div>
                            <button type="submit" class="submit-btn">Save</button>
                    </form>
                    
                </div>
            </div>
        </div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="{{ url_for('static', filename='scripts/salary_advance.js') }}"></script>
<script>
    // JavaScript for adding more installment fields dynamically
    $(document).ready(function() {
        let installmentCount = parseInt($("#installment-amounts-count").val());

        $("#add-installment").off("click").on("click", function() {
    installmentCount++;
    let newInstallment = `
        <div class="input-group-text">
            <input type="number" class="form-control installment-amount" name="installment_amounts[]" placeholder="Installment Amount" value="0" required>
            <input type="date" class="form-control installment-due-date" name="due_dates[]" required>
        </div>`;
    $("#installment-amounts-container").append(newInstallment);
});

    });
</script>

{% endblock %}