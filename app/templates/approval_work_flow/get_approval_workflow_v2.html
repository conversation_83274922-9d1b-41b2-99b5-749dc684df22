<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                    <i class="fi fi-rr-settings"></i> settings
                </a>
                <a class="btn-edit template-link" href="{{ url_for('approval_work_flow_v2.create_workflow') }}">
                    <i class="fi fi-rr-plus-small"></i>Workflow
                </a>
            </div>
        </div>
        <div class="dyn_container">
            
            <div class="form--container">
                {% if grouped_workflows %}
                    {% for approval_type, workflows in grouped_workflows.items() %}
                        <div class="workflow-group">
                            <h2>{{ approval_type }}</h2>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Role</th>
                                        <th>Sequence Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for workflow in workflows %}
                                        <tr>
                                            <td>{{ workflow.role }}</td>
                                            <td>{{ workflow.sequence_order }}</td>
                                            <td class="table-buttons">
                                                <a class="green icon" href="{{ url_for('approval_work_flow_v2.update_workflow', workflow_id=workflow.workflow_id) }}">
                                                    <i class="fi fi-rr-edit"></i>
                                                </a>
                                                <a class="red icon" href="{{ url_for('approval_work_flow_v2.delete_workflow', workflow_id=workflow.workflow_id) }}" onclick="return confirm('Are you sure you want to delete this workflow?');">
                                                    <i class="fi fi-rr-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endfor %}
                {% else %}
                    <p>No workflows found.</p>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

