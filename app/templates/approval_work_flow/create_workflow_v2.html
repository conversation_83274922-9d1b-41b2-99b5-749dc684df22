<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Create Workflow</h1>
                <a class="btn-edit template-link" href="{{ url_for('approval_work_flow_v2.get_approval_workflow') }}">
                    <i class="fi fi-rr-list"></i>Workflows
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="grey-container">
                <h2>Workflow Details</h2>
                <div class="upload-info">   
                    <ol>
                        <li><strong>Approval Type:</strong> Select the type of approval for the workflow.</li>
                        <li><strong>Role:</strong> Choose the role that will be responsible for this workflow, 1st approver, 2nd approver, etc.</li>
                        <li><strong>Order:</strong>  Define the order in which this workflow will be processed.</li>
                        <li>Click "Submit" to create the workflow.</li>
                    </ol>
                </div> 
            </div>
            <div class="form--container">
                {% if errors_messages %}
                    <div class="errors">
                        {% for message in errors_messages %}
                            <div class="alert alert-danger">{{ message }}</div>
                        {% endfor %}
                    </div>
                {% endif %}

                <form method="POST" action="{{ url_for('approval_work_flow_v2.create_workflow') }}">
                    {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.approval_type.label }}
                        <div class="input-group-text">
                            <i class="fas fa-check-circle"></i>
                            {{ form.approval_type(class="form-control") }}
                        </div>
                        {% for error in form.approval_type.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>

                    <div class="form-group">
                        {{ form.role.label }}
                        <div class="input-group-text">
                            <i class="fas fa-user-tag"></i>
                            {{ form.role(class="form-control") }}
                        </div>
                        {% for error in form.role.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>

                    <div class="form-group">
                        {{ form.sequence_order.label }}
                        <div class="input-group-text">
                            <i class="fas fa-sort-numeric-up"></i>
                            {{ form.sequence_order(class="form-control") }}
                        </div>
                        {% for error in form.sequence_order.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                    <button type="submit" class="submit-btn">Save</button>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
