<!DOCTYPE html>
<html>
<head>
    <title>Approval Work Flow</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('user_data.settings') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('approval_work_flow.create_workflow') }}">
            <i class="fas fa-plus"></i> Approval Work Flow
        </a>
    </div>
    <div class="container">
        <h1>Approval Work Flow</h1>
        <table class="table">
            <thead>
                <tr>
                    <th>Approval Work Flow Name</th>
                    <th>Role</th>
                    <th>Order</th>
                    <th>Created Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            {% for approval_type, workflows in grouped_workflows.items() %}
                <tr>
                    <td rowspan="{{ workflows|length }}">{{ approval_type }}</td>
                    {% for flow in workflows %}
                        {% if not loop.first %}</tr><tr>{% endif %}
                        <td>{{ flow.role }}</td>
                        <td>{{ flow.sequence_order }}</td>
                        <td>{{ flow.created_at }}</td>
                        <td>
                            <a class="template-link" href="#" data-template-url= "{{ url_for('approval_work_flow.update_workflow', workflow_id=flow.workflow_id) }}">Edit</a>
                            <a class="template-link" href="#" data-template-url="{{ url_for('approval_work_flow.delete_workflow', workflow_id=flow.workflow_id) }}" 
                            onclick="return confirm('Are you sure you want to delete this workflow?');">Delete</a>
                        </td>
                    {% endfor %}
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>
