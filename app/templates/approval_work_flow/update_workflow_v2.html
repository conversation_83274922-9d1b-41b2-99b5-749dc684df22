<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Update approval workflow</h1>
            <a class="btn-edit template-link" href="{{ url_for('approval_work_flow_v2.get_approval_workflow') }}">
                <i class="fi fi-rr-list"></i> Workflows
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <form method="POST" action="{{ url_for('approval_work_flow_v2.update_workflow', workflow_id=workflow_id) }}">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.approval_type.label }}
                        <div class="input-group-text">
                            <i class="fas fa-check-circle"></i>
                            {{ form.approval_type(class="form-control") }}
                        </div>
                        {% for error in form.approval_type.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>

                    <div class="form-group">
                        {{ form.role.label }}
                        <div class="input-group-text">
                            <i class="fas fa-user-tag"></i>
                            {{ form.role(class="form-control") }}
                        </div>
                        {% for error in form.role.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.sequence_order.label }}
                        <div class="input-group-text">
                            <i class="fas fa-sort-numeric-up"></i>
                            {{ form.sequence_order(class="form-control") }}
                        </div>
                        {% for error in form.sequence_order.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
                <button type="submit" class="submit-btn">Update</button>
            </form>
        </div>
    </div>
    </div>
{% endblock %}
