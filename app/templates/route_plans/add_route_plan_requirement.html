<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Route Plan Requirements</title>
    
</head>
<body>
    <h1>Add Route Plan Requirement</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul>
            {% for category, message in messages %}
                <li class="{{ category }}">{{ message }}</li>
            {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    
    <form method="post">
        {{ form.hidden_tag() }}
        
        <label for="route_path">Route Path:</label>
        {{ form.route_path() }}
        
        <label for="required_plan_id">Select Plan:</label>
        {{ form.required_plan_id() }}

        <label for="description">Description:</label>
        {{ form.description() }}
        
        {{ form.submit() }}
    </form>

    <h2>Plans and Their Accessible Routes</h2>
    
    <table>
        <thead>
            <tr>
                <th>Plan Name</th>
                <th>Routes</th>
            </tr>
        </thead>
        <tbody>
            {% for plan_name, routes in organized_route_plans.items() %}
                <tr>
                    <td><strong>{{ plan_name }}</strong></td>
                    <td>
                        <ul>
                            {% for route in routes %}
                                <li>
                                    <strong>Path:</strong> {{ route.route_path }}<br>
                                    {% if route.description %}
                                        <strong>Description:</strong> {{ route.description }}
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ul>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
