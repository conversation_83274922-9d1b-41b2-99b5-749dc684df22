<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Blog Category</title>
    <link rel="stylesheet" href="/static/styles/blog_form.css">
</head>
<body>
    <div class="all-containers">
        <div class="redirection-links">
            <a href="{{ url_for('blog_posts.add_blog_category') }}"> add new</a>
            <a href="{{ url_for('blog_posts.create_blog') }}"> create post</a>
        </div>
        <div class="form-container">
            <h1>Add Blog Category</h1>
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <ul>
                        {% for message in messages %}
                            <li>{{ message }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
            <form method="post">
                {{ form.csrf_token }}
                <div class="form-group">
                    <label for="name">{{ form.name.label }}</label>
                    {{ form.name(class="input") }}
                </div>
                <div class="form-group">
                    <label for="description">{{ form.description.label }}</label>
                    {{ form.description(class="input") }}
                </div>
                <div class="form-group">
                    {{ form.submit(class="btn-submit") }}
                </div>
            </form>
        </div>

        <div class="form-container">
            <h1>Available Categories</h1>
            <table class="tag-table">
                <tr>
                    <th>Category Name</th>
                    <th>Category Description</th>
                    <th>Actions</th>
                </tr>
                {% for category in blog_categories %}
                    <tr>
                        <td>{{ category.category_name }}</td>
                        <td>{{ category.category_description }}</td>
                        <td>
                            <a href="{{ url_for('blog_posts.update_blog_category', category_id=category.category_id) }}">Update</a>
                        </td>
                    </tr>
                {% endfor %}
            </table>
        </div>
    </div>
</body>
</html>
