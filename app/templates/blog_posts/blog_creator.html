<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Create Blog Post</title>
  <link rel="stylesheet" href="/static/styles/blog_form.css">
  <link href="https://cdn.quilljs.com/1.3.7/quill.snow.css" rel="stylesheet">
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@9"></script>
</head>
<body>
  <div class="form-container">
    <h1>Create a New Blog Post</h1>
    <form id="post-form">
      <div class="form-group">
        <label for="title">Title:</label>
        <input type="text" id="title" name="title" class="input" placeholder="Enter title" required>
      </div>
      <!-- Categories Selection -->
      <div class="form-group">
        <label for="categories">Select Categories (Ctrl + Right-click to select):
          <a href="{{ url_for('blog_posts.add_blog_category') }}"> add new</a>
        </label>

        <div id="categories-container" class="tag-input-container">
          <select id="categories-select" multiple>
            {% for category in blog_categories %}
              <option value="{{ category.category_id }}">{{ category.category_name }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
      <!-- Tags Selection -->
      <div class="form-group">
        <label for="tags">Select Tags(Ctrl + Right-click to select):
          <a href="{{ url_for('blog_posts.add_blogtag') }}"> add new</a>
        </label>
        <div id="tags-container" class="tag-input-container">
          <select id="tags-select" multiple>
            {% for tag in blog_tags %}
              <option value="{{ tag.tag_id }}">{{ tag.tag_name }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="form-group">
        <label for="content">Content:</label>
        <div id="editor" style="height: 200px;"></div>
      </div>
      <div class="form-group">
        <label for="file">Upload Image:</label>
        <input type="file" id="file" name="file">
        <button type="button" id="upload-button">Upload</button>
        <p id="upload-status"></p>
      </div>

      <div class="form-group">
        <button type="submit" class="btn-submit">Save Post</button>
      </div>
    </form>
  </div>

  <script src="https://cdn.quilljs.com/1.3.7/quill.min.js"></script>
  <script src="/static/scripts/create_blog.js"></script>
</body>
</html>
