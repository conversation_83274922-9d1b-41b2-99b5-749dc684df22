<!DOCTYPE html>
{% extends ('layouts/home_.html') %}
{% block content %}
<section class="max-w-4xl mx-auto p-4">
    <!-- Loop through blogs -->
    {% for blog in blogs[::-1] %}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div>
            <h1 class="text-xl font-medium text-gray-800">{{ blog.title }}</h1>
        </div>
        {% if blog.image_path %}
        <div class="my-4">
            <a href="{{ url_for('blog_posts.blog_details', blog_id=blog.blog_id) }}">
                <img class="w-full h-64 object-cover rounded-md" src="{{ blog.image_path }}" alt="Blog Image">

            </a>
        </div>
        {% endif %}
        <div class="text-gray-600">
            {{ blog.content[:50] | safe }} ...
        </div>
        <div class="mt-4">
            <a href="{{ url_for('blog_posts.blog_details', blog_id=blog.blog_id) }}" class="primary-background">Read More</a>
        </div>
        <div class="text-sm text-gray-500 mt-2">
            <span class="font-semibold">Published on:</span> {{ blog.created_at.strftime('%d %b %Y') }}
        </div>
    </div>
    {% endfor %}

    {% if not blogs %}
    <p class="text-center text-gray-500">No blogs available.</p>
    {% endif %}
</section>

{% endblock %}
