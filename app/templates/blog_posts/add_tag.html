<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Tag</title>
    <link rel="stylesheet" href="/static/styles/blog_form.css">
</head>
<body>
    <div class="all-containers">
        <div class="redirection-links">
            <a href="{{ url_for('blog_posts.add_blogtag') }}"> add new</a>
            <a href="{{ url_for('blog_posts.create_blog') }}"> create post</a>
        </div>
        <div class="form-container">
            <h1>Add Tag</h1>
            <form method="post">
                {{ form.csrf_token }}
                <div class="form-group">
                    <label for="name">{{ form.name.label }}</label>
                    {{ form.name(class="input") }}
                </div>
                <div class="form-group">
                    <label for="description">{{ form.description.label }}</label>
                    {{ form.description(class="input") }}
                </div>
                <div class="form-group">
                    {{ form.submit(class="btn-submit") }}
                </div>
            </form>
        </div>

        <div class="form-container">
            <h1>Available Tags</h1>
            <table class="tag-table">
                <tr>
                    <th>Tag Name</th>
                    <th>Tag Description</th>
                    <th>Actions</th>
                </tr>
                {% for tag in blog_tags %}
                    <tr>
                        <td>{{ tag.tag_name }}</td>
                        <td>{{ tag.tag_description }}</td>
                        <td>
                            <a href="{{ url_for('blog_posts.update_blogtag', tag_id=tag.tag_id) }}">Update</a>
                        </td>
                    </tr>
                {% endfor %}
            </table>
        </div>
    </div>
</body>
</html>
