<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agency Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .btn-group .btn {
            margin-right: 2px;
        }
    </style>
</head>
<body>
<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
        <a class="navbar-brand" href="#">NetPipo Agency</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="{{ url_for('agency.agency_dashboard') }}">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('agency.view_agencies') }}">Agencies</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('agency.agency_companies', agency_id=agency.agency_id) }}">Companies</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('agency.agency_users', agency_id=agency.agency_id) }}">Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('agency.agency_commissions', agency_id=agency.agency_id) }}">Commissions</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('user_data.logout') }}">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
                {% if session.username %}
                <li class="nav-item">
                    <span class="nav-link">
                        <i class="fas fa-user"></i> {{ session.username }}
                    </span>
                </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>

<div class="container mt-5">
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <div class="row">
        <div class="col-md-12">
            {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endwith %}

    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ agency.agency_name }} Dashboard</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('agency.agency_companies', agency_id=agency.agency_id) }}" class="btn btn-primary">
                <i class="fas fa-building"></i> Manage Companies
            </a>
            <a href="{{ url_for('agency.agency_users', agency_id=agency.agency_id) }}" class="btn btn-secondary">
                <i class="fas fa-users"></i> Manage Users
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Companies</h5>
                    <h3 class="card-text">{{ companies|length }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Commissions</h5>
                    <h3 class="card-text">{{ total_commissions|round(2) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Paid Commissions</h5>
                    <h3 class="card-text">{{ paid_commissions|round(2) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">Pending Commissions</h5>
                    <h3 class="card-text">{{ pending_commissions|round(2) }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Companies</h4>
                </div>
                <div class="card-body">
                    {% if companies %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Company Name</th>
                                    <th>Employees</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for company in companies %}
                                <tr>
                                    <td>{{ company.company_name }}</td>
                                    <td>{{ company.number_employee }}</td>
                                    <td>
                                        {% if company.has_access %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('agency.agency_companies', agency_id=agency.agency_id) }}" class="btn btn-primary">View All Companies</a>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        No companies found for this agency.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Recent Commissions</h4>
                </div>
                <div class="card-body">
                    {% if commissions %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Company</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for commission in commissions[:5] %}
                                <tr>
                                    <td>{{ commission.commission_date }}</td>
                                    <td>{{ commission.company_name }}</td>
                                    <td>{{ commission.amount|round(2) }}</td>
                                    <td>
                                        {% if commission.is_paid %}
                                        <span class="badge bg-success">Paid</span>
                                        {% else %}
                                        <span class="badge bg-warning text-dark">Pending</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('agency.agency_commissions', agency_id=agency.agency_id) }}" class="btn btn-primary">View All Commissions</a>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        No commissions found for this agency.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
    $(document).ready(function() {
        // Initialize DataTables if needed
        if ($('.table').length) {
            $('.table').DataTable({
                "order": [[0, "asc"]],
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });
</script>
</body>
</html>
