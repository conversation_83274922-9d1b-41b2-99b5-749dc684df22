{% extends 'base.html' %}

{% block title %}Assign Company to Agency{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Assign Company to Agency</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('agency.assign_company_to_agency') }}">
                        {{ form.csrf_token }}
                        
                        <div class="form-group mb-3">
                            <label for="agency">{{ form.agency.label }}</label>
                            {{ form.agency(class="form-select") }}
                            {% if form.agency.errors %}
                                <div class="text-danger">
                                    {% for error in form.agency.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="company">{{ form.company.label }}</label>
                            {{ form.company(class="form-select") }}
                            {% if form.company.errors %}
                                <div class="text-danger">
                                    {% for error in form.company.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group text-center">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('agency.view_agencies') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
