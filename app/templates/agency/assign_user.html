<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign User to Agency</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #0d6efd;
            color: white;
            padding: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .text-danger {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        .btn {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">Assign User to Agency</h3>
                    </div>
                    <div class="card-body">
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <!-- No debug information in production -->

                        <form method="POST" action="{{ url_for('agency.assign_user_to_agency') }}">
                            {{ form.csrf_token }}

                            <div class="form-group mb-3">
                                <label for="agency">{{ form.agency.label }}</label>
                                {{ form.agency(class="form-select") }}
                                {% if form.agency.errors %}
                                    <div class="text-danger">
                                        {% for error in form.agency.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="first_name">{{ form.first_name.label }}</label>
                                {{ form.first_name(class="form-control", placeholder="Enter first name") }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="last_name">{{ form.last_name.label }}</label>
                                {{ form.last_name(class="form-control", placeholder="Enter last name") }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="email">{{ form.email.label }}</label>
                                {{ form.email(class="form-control", placeholder="Enter email") }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">If the user already exists, they will be assigned to the agency. Otherwise, a new user will be created.</small>
                            </div>

                            <div class="form-group mb-3">
                                <label for="phone">{{ form.phone.label }}</label>
                                {{ form.phone(class="form-control", placeholder="Enter phone number") }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group text-center">
                                {{ form.submit(class="btn btn-primary") }}
                                <a href="{{ url_for('agency.view_agencies') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
