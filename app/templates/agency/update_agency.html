<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Agency</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #0d6efd;
            color: white;
            padding: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .text-danger {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        .btn {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">Update Agency</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('agency.update_agency', agency_id=agency.agency_id) }}">
                            {{ form.csrf_token }}

                            <div class="form-group mb-3">
                                <label for="agency_name">{{ form.agency_name.label }}</label>
                                {{ form.agency_name(class="form-control", placeholder="Enter agency name") }}
                                {% if form.agency_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.agency_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="agency_email">{{ form.agency_email.label }}</label>
                                {{ form.agency_email(class="form-control", placeholder="Enter agency email") }}
                                {% if form.agency_email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.agency_email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="agency_phone">{{ form.agency_phone.label }}</label>
                                {{ form.agency_phone(class="form-control", placeholder="Enter agency phone") }}
                                {% if form.agency_phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.agency_phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="agency_address">{{ form.agency_address.label }}</label>
                                {{ form.agency_address(class="form-control", placeholder="Enter agency address (optional)") }}
                                {% if form.agency_address.errors %}
                                    <div class="text-danger">
                                        {% for error in form.agency_address.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label for="commission_rate">{{ form.commission_rate.label }}</label>
                                {{ form.commission_rate(class="form-control", placeholder="Enter commission rate (%)") }}
                                {% if form.commission_rate.errors %}
                                    <div class="text-danger">
                                        {% for error in form.commission_rate.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">Enter a percentage value between 0 and 100.</small>
                            </div>

                            <div class="form-group mb-3">
                                <label for="is_active">{{ form.is_active.label }}</label>
                                {{ form.is_active(class="form-select") }}
                                {% if form.is_active.errors %}
                                    <div class="text-danger">
                                        {% for error in form.is_active.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-group text-center">
                                {{ form.submit(class="btn btn-primary") }}
                                <a href="{{ url_for('agency.view_agencies') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
