{% extends 'base.html' %}

{% block title %}Upload Agency Logo{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Upload Logo for {{ agency.agency_name }}</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12 text-center">
                            {% if agency.logo %}
                            <div class="mb-3">
                                <h5>Current Logo</h5>
                                <img src="{{ agency.logo }}" alt="Agency Logo" class="img-fluid" style="max-height: 150px;">
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                No logo has been uploaded yet.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <form method="POST" action="{{ url_for('agency.upload_agency_logo', agency_id=agency.agency_id) }}" enctype="multipart/form-data">
                        {{ form.csrf_token }}
                        
                        <div class="form-group mb-3">
                            <label for="logo">{{ form.logo.label }}</label>
                            {{ form.logo(class="form-control") }}
                            {% if form.logo.errors %}
                                <div class="text-danger">
                                    {% for error in form.logo.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Allowed file types: jpg, jpeg, png, gif</small>
                        </div>
                        
                        <div class="form-group text-center">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('agency.view_agencies') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
