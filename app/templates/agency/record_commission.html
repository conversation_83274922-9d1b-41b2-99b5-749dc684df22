{% extends 'base.html' %}

{% block title %}Record Commission{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Record Commission</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('agency.record_commission') }}">
                        {{ form.csrf_token }}
                        
                        <div class="form-group mb-3">
                            <label for="agency">{{ form.agency.label }}</label>
                            {{ form.agency(class="form-select") }}
                            {% if form.agency.errors %}
                                <div class="text-danger">
                                    {% for error in form.agency.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="company">{{ form.company.label }}</label>
                            {{ form.company(class="form-select") }}
                            {% if form.company.errors %}
                                <div class="text-danger">
                                    {% for error in form.company.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Note: The company must be associated with the selected agency.</small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="amount">{{ form.amount.label }}</label>
                            {{ form.amount(class="form-control", placeholder="Enter commission amount") }}
                            {% if form.amount.errors %}
                                <div class="text-danger">
                                    {% for error in form.amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="payment_reference">{{ form.payment_reference.label }}</label>
                            {{ form.payment_reference(class="form-control", placeholder="Enter payment reference (optional)") }}
                            {% if form.payment_reference.errors %}
                                <div class="text-danger">
                                    {% for error in form.payment_reference.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="description">{{ form.description.label }}</label>
                            {{ form.description(class="form-control", placeholder="Enter description (optional)") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group text-center">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('agency.view_agencies') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
