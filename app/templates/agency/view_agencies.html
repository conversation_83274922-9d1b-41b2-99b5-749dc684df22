<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Agencies</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .btn-group .btn {
            margin-right: 2px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>Agencies</h2>
            </div>
            <div class="col-md-4 text-end">
                {% if session.role == 'admin' %}
                <a href="{{ url_for('agency.register_agency') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Register New Agency
                </a>
                {% endif %}
            </div>
        </div>

        {% if agencies %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="agenciesTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Agency Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Commission Rate</th>
                                <th>Companies</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agency in agencies %}
                            <tr>
                                <td>{{ agency.agency_name }}</td>
                                <td>{{ agency.agency_email }}</td>
                                <td>{{ agency.agency_phone }}</td>
                                <td>{{ agency.commission_rate }}%</td>
                                <td>{{ agency.company_count }}</td>
                                <td>
                                    {% if agency.is_active %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('agency.update_agency', agency_id=agency.agency_id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('agency.upload_agency_logo', agency_id=agency.agency_id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-image"></i>
                                        </a>
                                        <a href="{{ url_for('agency.agency_users', agency_id=agency.agency_id) }}" class="btn btn-sm btn-secondary">
                                            <i class="fas fa-users"></i>
                                        </a>
                                        <a href="{{ url_for('agency.agency_companies', agency_id=agency.agency_id) }}" class="btn btn-sm btn-success">
                                            <i class="fas fa-building"></i>
                                        </a>
                                        <a href="{{ url_for('agency.agency_commissions', agency_id=agency.agency_id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-money-bill"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            No agencies found.
        </div>
        {% endif %}
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#agenciesTable').DataTable({
                "order": [[0, "asc"]],
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
