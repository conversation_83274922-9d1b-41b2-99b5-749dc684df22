<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add RSSB Contributions</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/taxforms.css') }}">
</head>
<body>
    <h1>Add RSSB Contributions</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul>
            {% for category, message in messages %}
                <li class="{{ category }}">{{ message }}</li>
            {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
     
    <form method="POST" action="{{ url_for('rssb.add_rssb_contributions')}}">
        {{ form.hidden_tag() }}
        <p>
            {{ form.contribution_name.label }}<br>
            {{ form.contribution_name(size=32) }}<br>
            {% for error in form.contribution_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.employee_rate.label }}<br>
            {{ form.employee_rate(size=32) }}<br>
            {% for error in form.employee_rate.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.employer_rate.label }}<br>
            {{ form.employer_rate(size=32) }}<br>
            {% for error in form.employer_rate.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.start_date.label }}<br>
            {{ form.start_date(size=32) }}<br>
            {% for error in form.start_date.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.end_date.label }}<br>
            {{ form.end_date(size=32) }}<br>
            {% for error in form.end_date.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>
    </form>
    <br>
    <h1>Available Contributions:</h1>
    <table>
        <thead>
            <tr>
                <th>Contribution Name</th>
                <th>Employee Rate %</th>
                <th>Employer Rate %</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Actions</th>
                
            </tr>
        </thead>
        <tbody>
            {% for contribution in rssb_contributions %}
            <tr>
                <td>{{ contribution.contribution_name }}</td>
                <td>{{ (contribution.employee_rate)}}</td>
                <td>{{ (contribution.employer_rate) }}</td>
                <td>{% if contribution.start_date %}
                    {{ contribution.start_date }}
                    {% else %}
                    {% endif %}
                </td>
                <td>{% if contribution.end_date %}
                    {{ contribution.end_date }}
                    {% else %}
                    Current
                    {% endif %}
                </td>
                <td>
                    <li><a href="{{ url_for('rssb.update_rssb_contributions', id=contribution.nsf_id) }}">Edit</a></li>
                    <li><a href="{{ url_for('rssb.delete_rssb_contributions', id=contribution.nsf_id) }}">Delete</a></li>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <br>
    <a href="{{ url_for('admin_data.admin_dashboard') }}">Back to  Dashboard</a>
</body>
</html>