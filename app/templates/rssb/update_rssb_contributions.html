<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Update RSSB Contributions</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/taxforms.css') }}">
</head>
<body>
    <h1>Update RSSB Contributions</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul>
            {% for category, message in messages %}
                <li class="{{ category }}">{{ message }}</li>
            {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
     
    <form method="POST">
        {{ form.hidden_tag() }}
        <p>
            {{ form.contribution_name.label }}<br>
            {{ form.contribution_name(size=32) }}<br>
            {% for error in form.contribution_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.employee_rate.label }}<br>
            {{ form.employee_rate(size=32) }}<br>
            {% for error in form.employee_rate.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.employer_rate.label }}<br>
            {{ form.employer_rate(size=32) }}<br>
            {% for error in form.employer_rate.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>  
    </form>
    </body>
</html>
            