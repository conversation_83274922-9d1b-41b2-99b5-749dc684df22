<!DOCTYPE html>
<html>

<head>
    <title>Edit Plans</title>
</head>

<body>
    <h1>Edit Plans</h1>
    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <ul class="list-unstyled">
        {% for category, message in messages %}
        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </li>
        {% endfor %}
    </ul>
    {% endif %}
    {% endwith %}
    <form method="post">
        {{ form.csrf_token }}
        {{ form.plan_name.label }}
        {{ form.plan_name }}
        <br>
        {{ form.description.label }}
        {{ form.description }}
        <br>
        {{ form.price.label }}
        {{ form.price }}
        <br>
        {{ form.num_employees.label }}
        {{ form.num_employees }}
        <br>
        {{ form.price_per_employee.label }}
        {{ form.price_per_employee }}
        <br>
        {{ form.submit }}
    </form>


</body>

</html>