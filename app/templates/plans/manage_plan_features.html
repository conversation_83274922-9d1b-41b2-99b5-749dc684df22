<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Features</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        h1 {
            background-color: #007BFF;
            color: white;
            padding: 20px;
            text-align: center;
        }
        h2 {
            margin-top: 40px;
            color: #333;
            text-align: center;
        }
        .container {
            width: 80%;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        form {
            margin-bottom: 30px;
        }
        label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        select, button {
            padding: 10px;
            width: 100%;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-size: 16px;
            margin-bottom: 15px;
        }
        select {
            width: auto;
        }
        button {
            width: auto;
            background-color: #28a745;
            color: white;
            border: none;
            cursor: pointer;
            padding: 10px 20px;
        }
        button:hover {
            background-color: #218838;
        }
        table {
            width: 100%;
            margin-top: 30px;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007BFF;
            color: white;
        }
        td {
            background-color: #f9f9f9;
        }
        ul {
            list-style-type: none;
            padding-left: 0;
        }
        li {
            margin-bottom: 10px;
        }
        .message {
            padding: 10px;
            background-color: #f8d7da;
            color: #721c24;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>

    <h1>Manage Features</h1>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="message {% if category == 'success' %}success{% else %}error{% endif %}">
                    <ul>
                    {% for category, message in messages %}
                        <li>{{ message }}</li>
                    {% endfor %}
                    </ul>
                </div>
            {% endif %}
        {% endwith %}

        <form method="POST">
            {{ form.hidden_tag() }}
            <label for="plan_id">Select Plan:</label>
            <select name="plan_id" id="plan_id" required>
                {% for plan in plans %}
                    <option value="{{ plan.plan_id }}">{{ plan.plan_name }}</option>
                {% endfor %}
            </select>

            <div>
                {{ form.feature_id.label }}: {{ form.feature_id() }}
            </div>

            <button type="submit">Add Feature</button>
        </form>

        <h2>Plans and Associated Features</h2>
        <table>
            <thead>
                <tr>
                    <th>Plan Name</th>
                    <th>Features</th>
                </tr>
            </thead>
            <tbody>
                {% for plan in plans %}
                    <tr>
                        <td>{{ plan['plan_name'] }}</td>
                        <td>
                            {% if plan['features'] %}
                                <ul>
                                    {% for feature in plan['features'] %}
                                        <li>
                                            {{ feature['feature_name'] }}
                                            
                                            <form action="{{ url_for('plans.remove_feature', plan_id=plan['plan_id'], feature_id=feature['id']) }}" method="POST" style="display:inline;">
                                                <button type="submit">Remove</button>
                                            </form>
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                No features assigned
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

</body>
</html>
