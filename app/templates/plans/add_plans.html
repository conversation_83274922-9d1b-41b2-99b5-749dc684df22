<!DOCTYPE html>
<html>

<head>
    <title>Add Plans</title>
</head>

<body>
    <h1>Add Plans</h1>
    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <ul class="list-unstyled">
        {% for category, message in messages %}
        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </li>
        {% endfor %}
    </ul>
    {% endif %}
    {% endwith %}
    <form method="post">
        {{ form.csrf_token }}
        {{ form.plan_name.label }}
        {{ form.plan_name }}
        <br>
        {{ form.description.label }}
        {{ form.description }}
        <br>
        {{ form.price.label }}
        {{ form.price }}
        <br>
        {{ form.num_employees.label }}
        {{ form.num_employees }}
        <br>
        {{ form.price_per_employee.label }}
        {{ form.price_per_employee }}
        <br>
        {{ form.submit }}
    </form>

    <h1>Plans</h1>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Plan Name</th>
                <th>Description</th>
                <th>Price</th>
                <th>Number of Employees</th>
                <th>Price Per Employee</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for plan in plans %}
            <tr>
                <td>{{ plan.plan_name.upper() }}</td>
                <td>{{ plan.description }}</td>
                <td>{{ plan.price }}</td>
                <td>{{ plan.num_of_employees }}</td>
                <td>{{ plan.price_per_employee }}</td>
                <td>
                    <a href="{{ url_for('plans.edit_plan', plan_id=plan.plan_id) }}">Edit</a>
                    <a href="{{ url_for('plans.delete_plan', plan_id=plan.plan_id) }}">Delete</a>

                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

</body>

</html>