<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="text-center mb-4">Inactive Employees' Data</h1>
            <a class="btn-edit" href="{{ url_for('my_employees.employees_list') }}">
                <i class="fi fi-rr-list"></i> Active Employees
            </a>
        </div>
        <div class="space-between">
            <div class="filter-container">
                <div class="page_rows">
                    <label for="rowsPerPageInput">Show:</label>
                    <select id="rowsPerPageInput">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                    </select>
                </div>
                <div class="page_search">
                    <label for="inactiveSearchInput" class="form-label">Search</label>
                    <input type="text" id="inactiveSearchInput" class="form-control" placeholder="Search by name, NID, RSSB No, etc.">
                </div>
                <button class="toggle-filters-btn" id="inactiveToggleFiltersBtn">Show filters</button>
                <div class="advanced-filters"  id="inactiveAdvancedFilters">
                        <h3>Filter by:</h3>
                        <div class="underline"></div>
                        <div class="form-group">
                            <label for="inactiveFilter">Employment Type:</label>
                            <select id="inactiveFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="jobTitleFilter">Job Title:</label>
                            <select id="jobTitleFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="inactiveDepartmentFilter">Department:</label>
                            <select id="inactiveDepartmentFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                </div>
            </div>
        </div>
    </div>
    <div class="dyn_container">
        {% if employees %}
        <div class="large--table">
            <div class="table-custom">
                <table id="inactive_employees">
                    <thead class="thead th-custom">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">First Name</th>
                            <th scope="col">Last Name</th>
                            <th scope="col">NID</th>
                            <th scope="col">RSSB No</th>
                            <th scope="col">DOB</th>
                            <th scope="col">Marital Status</th>
                            <th scope="col">Gender</th>
                            <th scope="col">Department</th>
                            <th scope="col">TIN</th>
                            <th scope="col">Employee Type</th>
                            <th scope="col">Net Salary</th>
                            <th scope="col">Gross Salary</th>
                            <th scope="col">Transport Allowance</th>
                            <th scope="col">Housing Allowance</th>
                            <th scope="col">Communication Allowance</th>
                            <th scope="col">Other Allowance</th>
                            <th scope="col">Email</th>
                            <th scope="col">Phone</th>
                            <th scope="col">Job Title</th>
                            <th scope="col">Hire Date</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                            <tr>
                                <td>{{ loop.index }}</td> <!-- Indexing starts from 1 -->
                                <td>{{ employee.first_name }}</td>
                                <td>{{ employee.last_name }}</td>
                                <td>{{ employee.nid }}</td>
                                <td>{{ employee.nsf }}</td>
                                <td>{% if employee.birth_date %}
                                    {{ employee.birth_date }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{{ employee.marital_status }}</td>
                                <td>{{ employee.gender }}</td>
                                <td>{% if employee.department %}{{ employee.department }} {%else%} N/A {%endif%}</td>
                                <td>
                                    {% if employee.employee_tin == 'nan' %}
                                        
                                    {% elif employee.employee_tin %}
                                        {{ employee.employee_tin }}
                                    {% else %}
                                        
                                    {% endif %}
                                </td>
                                <td>{{ employee.employee_type }}</td>
                                <td>{% if employee.net_salary %}
                                    {{ Auxillary.format_amount(employee.net_salary) }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.gross_salary %}
                                    {{ Auxillary.format_amount(employee.gross_salary) }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{{ Auxillary.format_amount(employee.transport_allowance) }}</td>
                                <td>{{ Auxillary.format_amount(employee.housing_allowance) }}</td>
                                <td>{{ Auxillary.format_amount(employee.communication_allowance) }}</td>
                                <td>{{ Auxillary.format_amount(employee.other_allowance) }}</td>
                                <td>{% if employee.email %}
                                    {{ employee.email }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.phone %}
                                    {{ employee.phone }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.job_title %}
                                    {{ employee.job_title }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{% if employee.hire_date %}
                                    {{ employee.hire_date }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="table-buttons">
                                        <a class="green" href="{{ url_for('my_employees.update_employee', employee_id=employee.employee_id) }}">
                                        <i class="fi fi-rr-edit"></i>
                                        </a>
                                        <!--
                                        {% if attendance_service %}
                                    
                                            <a class="template-link btn-image" href="{{ url_for('attendance.create_subject', name=employee.employee_id ~ ' ' ~ employee.first_name ~ ' ' ~ employee.last_name) }}">
                                            Image
                                            </a>
                                        
                                            {% else %}
                                        {% endif %}
                                        -->
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <div id="inactiveEmployeesPagination" class="pagination"></div>
            </div>
        </div>
        {% else %}
        <div class="form--container" style="text-align: center; padding: 20px;">
            <i class="fi fi-rr-drawer-empty mid-dark big"></i>
            <p>No inactive employees found.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}