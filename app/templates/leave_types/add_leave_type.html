<!DOCTYPE html>
<html>
<head>
    <title>Add Leave Type</title>
</head>
<body>
    <h1>Add Leave Type</h1>
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul>
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <form  method="post">
        {{ form.csrf_token }}
        <p>
            {{ form.name.label }}<br>
            {{ form.name }}
        </p>
        <p>
            {{ form.description.label }}<br>
            {{ form.description }}
        </p>
        <p>
            {{ form.submit }}
        </p>
    </form>
    <br>
    <h1>Leave Types</h1>
    <table border="1">
        <tr>
            <th>Name</th>
            <th>Description</th>
            <th>Actions</th>
        </tr>
        {% for leave_type in leave_types %}
            <tr>
                <td>{{ leave_type.name }}</td>
                <td>{{ leave_type.description }}</td>
                <td>
                    <a href="{{ url_for('leave_types.edit_leave_type', id=leave_type.id) }}">Edit</a>
                    <a href="{{ url_for('leave_types.delete_leave_type', id=leave_type.id) }}">Delete</a>                  
                </td>
            </tr>
        {% endfor %}
</body>
</html>