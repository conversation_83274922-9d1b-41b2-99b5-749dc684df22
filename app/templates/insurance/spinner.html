<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
    .loader {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        height: 100vh; /* Full viewport height for demo */
        }

        .loader span {
        width: 12px;
        height: 12px;
        background: #3498db;
        border-radius: 50%;
        animation: spin 1.2s infinite ease-in-out;
        }

        .loader span:nth-child(2) {
        animation-delay: 0.2s;
        }

        .loader span:nth-child(3) {
        animation-delay: 0.4s;
        }

        @keyframes spin {
        0%, 80%, 100% {
            transform: scale(0.3);
            opacity: 0.3;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
        }

        /* Hide loader by default */
        .loader.hidden {
        display: none;
        }
    </style>
</head>
<body>
    <div class="loader">
        <span></span>
        <span></span>
        <span></span>
    </div>
    <button onclick="toggleLoader()">Toggle Loader</button>
    <script>
        function toggleLoader() {
        const loader = document.querySelector('.loader');
        loader.classList.toggle('hidden');
        }
    </script>
</body>
</html>