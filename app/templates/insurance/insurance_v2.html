<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Insurance</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('insurance_v2.add_insurance') }}">
            <i class="fas fa-plus"></i> Add Insurance
        </a>
    </div>
    <div>
        <div class="dynamic--form">
            <div class="row">
                <h1>Insurance</h1>
                <i class="fas fa-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Only RAMA Insurance is considered."></i>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Insurance Name</th>
                        <th>Employee Rate</th>
                        <th>Employer Rate</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for insurance in insurances %}
                        <tr>
                            <td>{{ insurance.insurance_name.upper() }}</td>
                            <td>{{ insurance.employee_rate * 100 }}</td>
                            <td>{{ insurance.employer_rate * 100 }}</td>
                            <td>
                                <div class="table-buttons">
                                    <a class="template-link btn-image" href="#" data-template-url="{{ url_for('insurance_v2.update_insurance', insurance_id=insurance.insurance_id) }}"><i class="fi fi-rr-pencil"></i> edit</a>
                                <a class="btn-cancel template-link" href="#" data-template-url="{{ url_for('insurance_v2.delete_insurance', insurance_id=insurance.insurance_id) }}"><i class="fi fi-rr-trash"></i> Delete</a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>