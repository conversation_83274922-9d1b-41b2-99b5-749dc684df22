<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add Insurance</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
    </div>
        <div class="right-buttons-group">
            <div class="one-button">
                <button id="open-popup" class="btn-edit" onclick="displayPopup()"><i class="fas fa-plus"></i>Add insurance</button>
            </div>
        </div>
                <h1>Add Insurance</h1>
                <form method="POST" action="{{ url_for('insurance.add_insurance') }}">
                    {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {% for error in form.insurance_name.errors %}
                            <span>[{{ error }}]</span>
                        {% endfor %}
                        {{ form.insurance_name.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">local_hospital</span>
                            {{ form.insurance_name(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.employee_rate.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">person</span>
                            {{ form.employee_rate(class="form-control", placeholder="Ex: 7.5") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.employer_rate.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">business</span>
                            {{ form.employer_rate(class="form-control", placeholder="Ex: 7.5") }}
                        </div>
                    </div>
                    <div class="form-group">
                        <input id="submit" class="btn btn-custom" name="submit" type="submit" value="Add Insurance">
                    </div>
                </div>
            </form>
        <div class="dynamic--form">
        <h1>Available Insurances</h1>
        <div class="form-group">
            <table>
                <thead>
                    <tr>
                        <th>Insurance Name</th>
                        <th>Employee Rate</th>
                        <th>Employer Rate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for insurance in insurances_list %}
                    <tr>
                        <td>{{ insurance.insurance_name.upper() }}</td>
                        <td>{{ insurance.employee_rate * 100 }}%</td>
                        <td>{{ insurance.employer_rate * 100 }}%</td>
                        <td>
                            <div class="table-buttons">
                                <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('insurance.update_insurance', insurance_id=insurance.insurance_id) }}"><span class="material-symbols-outlined icon action">edit</span>Edit</a>
                            <a class="btn-delete" href="{{ url_for('insurance.delete_insurance', insurance_id=insurance.insurance_id) }}"><span class="material-symbols-outlined icon action">delete_forever</span>Delete</a>
                            </div>              
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/popup.js') }}"></script>
</body>
</html>
