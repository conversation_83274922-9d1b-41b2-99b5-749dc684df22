<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1 class="text-center mb-4">Upload company's logo</h1>
                <a class="btn-edit template-link" href="{{ url_for('user_data_v2.settings') }}">
                    <i class="fi fi-rr-settings"></i> Settings
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                {% if session.get('company_logo') %}
                <div class="preview-container">
                    <h5>Current Logo</h5>
                    <img src="{{ session.get('company_logo') }}" alt="Company Logo" class="logo-preview" id="current-logo">
                </div>
                {% endif %}
                <div class="grey-container">
                    <h5>Upload a new logo</h5>
                    <p>Upload a new logo for your company. The logo will be displayed on reports, dashboards, and other company materials.</p>
                    <p>Recommended size: 200x200 pixels. Supported formats: JPG, JPEG, PNG</p>
                </div>

                <form action="{{ url_for('company_data_v2.upload_company_logo') }}" method="post" enctype="multipart/form-data" id="logo-form">
                    {{ form.csrf_token }}
                    <div class="form-row">
                        <div class="file-input-container">
                            <input type="file" name="logo" id="logo-input" class="form-control" accept="image/jpeg,image/png,image/gif,image/tiff" />
                        </div>
                    </div>

                    <!-- New Logo Preview -->
                    <div class="preview-container" style="display: none;" id="new-logo-container">
                        <h5>New Logo Preview</h5>
                        <img src="#" alt="New Logo Preview" class="logo-preview" id="new-logo-preview">
                    </div>

                    <input class="submit-btn" type="submit" value="Upload Logo"/>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Preview the selected image before upload
        document.getElementById('logo-input').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('new-logo-preview');
                    preview.src = e.target.result;
                    document.getElementById('new-logo-container').style.display = 'block';
                }
                reader.readAsDataURL(file);
            }
        });

        // Handle form submission via AJAX
        document.getElementById('logo-form').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert(data.message);

                    // Update current logo if a URL was returned
                    if (data.url) {
                        const currentLogo = document.getElementById('current-logo');
                        if (currentLogo) {
                            currentLogo.src = data.url;
                        } else {
                            // Create current logo element if it doesn't exist
                            const container = document.createElement('div');
                            container.className = 'preview-container';
                            container.innerHTML = `
                                <h5>Current Logo</h5>
                                <img src="${data.url}" alt="Company Logo" class="logo-preview" id="current-logo">
                            `;
                            document.querySelector('.dynamic--form').insertBefore(
                                container,
                                document.querySelector('.upload-instructions')
                            );
                        }
                    }

                    // Hide the new logo preview
                    document.getElementById('new-logo-container').style.display = 'none';
                    document.getElementById('logo-input').value = '';
                } else {
                    // Show error message
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while uploading the logo.');
            });
        });
    </script>

{% endblock %}