<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('company_users_v2.company_users') }}">                
                    <i class="fi fi-rr-list"></i> Users
                </a>
            </div>
        </div>
    <div class="dyn_container">
        <div class="form--container">
        <h1>Add User</h1>
            <form method="post" action="{{ url_for('company_users_v2.add_company_user') }}">
                <div class="form-row">
                    <div class="form-group">
                        <label for="employee">Select Employee:</label>
                        <div class="input-group-text">
                            <select name="employee" id="employee" required class="form-select">
                                <option value="" disabled selected>Select an employee</option>
                                {% for employee in employees %}
                                    <option value="{{ employee.employee_id }}" >{{ employee.first_name }} {{ employee.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="role">Select Role:</label>
                        <div class="input-group-text">
                            <select name="role" id="role" required class="form-select">
                                <option value="" disabled selected>Select a role</option>
                                {% for role in roles %}
                                    <option value="{{ role.role_name }}">{{ role.role_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div> 
                <button type="submit" class="submit-btn">Add User</button>
        </form>
    </div>
    </div>
</div>
{% endblock %}
