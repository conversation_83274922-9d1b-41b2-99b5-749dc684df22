<!DOCTYPE html>
<html>

<head>
    <title>Edit Company Profile</title>
</head>

<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fa fa-arrow-left"></i> back
        </a>
    </div>
    <div class="dynamic--form">
        <h1 class="dynamic-title">Edit Company Profile</h1>
        <form action="{{ url_for('company_data.edit_company_profile') }}" method="POST">
            {{ form.csrf_token }}
            <div class="form-row">
                <div class="form-group">
                    <label for="company_name">Company Name</label>
                    <div class="input-group-text">
                        <i class="fas fa-building"></i>
                        {{ form.company_name(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="company_tin">Company Tin</label>
                    <div class="input-group-text">
                        <i class="fas fa-id-card"></i>
                        {{ form.company_tin(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="rssb_number">Rssb Number</label>
                    <div class="input-group-text">
                        <i class="fas fa-id-card"></i>
                        {{ form.rssb_number(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="company_type">Company Type</label>
                    <div class="input-group-text">
                        <i class="fas fa-building"></i>
                        <select id="company_type" name="company_type" class="form-control">
                            <option value="public">Public</option>
                            <option value="private">Private</option>
                            <option value="non_profit">Non Profit</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="phone_number">Phone Number</label>
                    <div class="input-group-text">
                        <i class="fas fa-phone"></i>
                        {{ form.phone_number(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-group-text">
                        <i class="fas fa-envelope"></i>
                        {{ form.email(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="number_employee">Number of Employees</label>
                    <div class="input-group-text">
                        <i class="fas fa-users"></i>
                        {{ form.number_employee(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="country">Country</label>
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.country(class="form-control") }}
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="province">Province</label>
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.province(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="district">District</label>
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.district(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="sector">Sector</label>
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.sector(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="cell">Cell</label>
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.cell(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="cell">Village</label>
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.village(class="form-control") }}
                    </div>
                </div>
            </div>
            <div class="border-bottom"></div>
            <div>
                <h1 style="margin: 20px 0; text-align: center;">Leave Settings</h1>
                <div class="form-row">
                    <div>
                        {{form.initial_qualification_period.label(class="leave-elements")}}
                        <div class="input-group-text">
                            <i class="fas fa-calendar-check"></i>
                            {{ form.initial_qualification_period(class="form-control") }}
                        </div>
                    </div>
                    <div>
                        {{ form.increment_policy.label(class="leave-elements")}}
                        <div class="form-group">
                            <div class="input-group-text">
                                <i class="fas fa-calendar-alt"></i>
                                {{ form.increment_policy(class="form-control", placeholder="Number of Years") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="border-bottom"></div>
            <button type="submit" class="btn btn-custom">Save</button>
    </div>
    </form>
    </div>
</body>

</html>