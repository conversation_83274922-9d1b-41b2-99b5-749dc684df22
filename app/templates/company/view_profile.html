<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>View Company Profile</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h1 {
            margin-top: 20px;
            color: #333;
        }

        .messages ul {
            list-style: none;
            padding: 0;
        }

        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        table {
            width: 100%;
            max-width: 1000px;
            margin: 20px 0;
            border-collapse: collapse;
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        table thead {
            background-color: #333;
            color: #fff;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
        }

        table tr {
            border-bottom: 1px solid #ddd;
        }

        table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        table tr:hover {
            background-color: #f1f1f1;
        }

        table a {
            text-decoration: none;
            color: #007bff;
        }

        table a:hover {
            text-decoration: underline;
        }

        a {
            display: inline-block;
            margin-top: 20px;
            text-decoration: none;
            color: #007bff;
            font-size: 16px;
        }

        a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            table {
                font-size: 14px;
            }

            table th, table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <h1>Company Profile</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="messages">
                <ul>
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
    
    <table>
        <thead>
            <tr>
                <th>Company Name</th>
                <th>TIN</th>
                <th>RSSB NO</th>
                <th>Phone</th>
                <th>Email</th>
                <th>Address</th>
                <th>Country</th>
                <th>Number of Employees</th>
                <th>Company Type</th>
                <th>Plan</th>
                <th>Created Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for data in company %}
            <tr>
                <td>{{ data['company_name'] }}</td>
                <td>{{ data['company_tin'] }}</td>
                <td>{{ data['rssb_number'] }}</td>
                <td>{{ data['phone_number'] }}</td>
                <td>{{ data['email'] }}</td>
                <td>{{ data['address'] }}</td>
                <td>{{ data['country'] }}</td>
                <td>{{ data['number_employee'] }}</td>
                <td>{{ data['company_type'] }}</td>
                <td>{{ data['plan'].plan_name }}</td>
                <td>{{ data['created_at'] }}</td>
                <td>
                    <a href="{{ url_for('company_data.update_company_profile', company_id=data['company_id']) }}">Edit</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <a href="{{ url_for('admin_data.admin_dashboard') }}">Back to Dashboard</a>
</body>
</html>
