<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block title %}Login{% endblock %}

{% block content %}
<section class="bg-gray-100 flex items-center justify-center min-h-screen flex-col">
    <div class="flash--messages mb-4 w-full max-w-md">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul class="list-unstyled">
            {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </li>
            {% endfor %}
            </ul>
        {% endif %}
        {% endwith %}
    </div>
        <h1 class="dark">Reset Password</h1>
        <form action="{{url_for('company_users.reset_user_password')}}" method="post" class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            {{ form.hidden_tag() }}

                    {{ form.email.label(class="block dark font-bold") }}
                    {{ form.email(class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200 mb-3", placeholder="Enter your email") }}
                    {% for error in form.email.errors %}
                    <span style="color: red;">[{{ error }}]</span>
                    {% endfor %}
                    {{ form.company_tin.label(class="block dark font-bold") }}
                    {{ form.company_tin(class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200 mb-3", placeholder="Enter you company TIN") }}
                    {% for error in form.company_tin.errors %}
                    <span style="color: red;">[{{ error }}]</span>
                    {% endfor %}

                {{ form.submit(class="primary-background mt-2") }}
                <a href="{{ url_for('company_users.login_company_users') }} " class="text-blue-600 hover:underline mt-2">
                    <i class="fi fi-arrow-left"></i>
                    <p class="text-blue-500">Back to Login</p> 
                </a>
    
        </form>

</section>
{% endblock %}