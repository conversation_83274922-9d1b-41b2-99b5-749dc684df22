<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Delete Company</title>
    <script>
        function confirmDeletion(event) {
            event.preventDefault();  // prevent form submit for now

            const warningMessage = 
                "WARNING: Deleting this company will delete all its users and its database. This action is irreversible.\n\n" +
                "Type 'delete' to confirm:";

            const userInput = window.prompt(warningMessage);

            if (userInput && userInput.trim().toLowerCase() === 'delete') {
                // User confirmed, submit the form
                event.target.form.submit();
            } else {
                alert('Deletion cancelled. You must type "delete" to confirm.');
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <h1>Delete a Company</h1>
        <form method="POST" action="{{ url_for('company_data_v2.delete_company') }}">
            <label for="company_id">Select Company:</label>
            <select name="company_id" id="company_id" required>
                {% for company in companies %}
                    <option value="{{ company.company_id }}">{{ company.company_name }}</option>
                {% endfor %}
            </select>
            <br><br>
            <!-- Attach the confirmDeletion handler to the button -->
            <button type="submit" class="btn btn-danger" onclick="confirmDeletion(event);">Delete Company</button>
        </form>
    </div>
</body>
</html>
