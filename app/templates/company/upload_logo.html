<!DOCTYPE html>
<html>
<head>
    <link href="{{ url_for('static', filename='styles/auth_forms.css') }}" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <title>Upload Company Logo</title>
    <style>
        .logo-preview {
            max-width: 200px;
            max-height: 200px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        .preview-container {
            margin-bottom: 20px;
            text-align: center;
        }
        .file-input-container {
            margin-bottom: 20px;
        }
        .upload-instructions {
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fa fa-arrow-left"></i> back
        </a>
    </div>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul class="list-unstyled">
            {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </li>
            {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <div class="dynamic--form">
        <h1 class="header-title">Upload Company Logo</h1>

        <!-- Current Logo Preview -->
        {% if session.get('company_logo') %}
        <div class="preview-container">
            <h5>Current Logo</h5>
            <img src="{{ session.get('company_logo') }}" alt="Company Logo" class="logo-preview" id="current-logo">
        </div>
        {% endif %}

        <!-- Upload Form -->
        <div class="upload-instructions">
            <p>Upload a new logo for your company. The logo will be displayed on reports, dashboards, and other company materials.</p>
            <p>Recommended size: 200x200 pixels. Supported formats: JPG, JPEG, PNG, GIF, TIF</p>
        </div>

        <form action="{{ url_for('company_data.upload_company_logo') }}" method="post" enctype="multipart/form-data" id="logo-form">
            {{ form.csrf_token }}
            <div class="file-input-container">
                <input type="file" name="logo" id="logo-input" class="form-control" accept="image/jpeg,image/png,image/gif,image/tiff" />
            </div>

            <!-- New Logo Preview -->
            <div class="preview-container" style="display: none;" id="new-logo-container">
                <h5>New Logo Preview</h5>
                <img src="#" alt="New Logo Preview" class="logo-preview" id="new-logo-preview">
            </div>

            <input class="btn-custom" type="submit" value="Upload Logo"/>
        </form>
    </div>

    <script>
        // Preview the selected image before upload
        document.getElementById('logo-input').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('new-logo-preview');
                    preview.src = e.target.result;
                    document.getElementById('new-logo-container').style.display = 'block';
                }
                reader.readAsDataURL(file);
            }
        });

        // Handle form submission via AJAX
        document.getElementById('logo-form').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert(data.message);

                    // Update current logo if a URL was returned
                    if (data.url) {
                        const currentLogo = document.getElementById('current-logo');
                        if (currentLogo) {
                            currentLogo.src = data.url;
                        } else {
                            // Create current logo element if it doesn't exist
                            const container = document.createElement('div');
                            container.className = 'preview-container';
                            container.innerHTML = `
                                <h5>Current Logo</h5>
                                <img src="${data.url}" alt="Company Logo" class="logo-preview" id="current-logo">
                            `;
                            document.querySelector('.dynamic--form').insertBefore(
                                container,
                                document.querySelector('.upload-instructions')
                            );
                        }
                    }

                    // Hide the new logo preview
                    document.getElementById('new-logo-container').style.display = 'none';
                    document.getElementById('logo-input').value = '';
                } else {
                    // Show error message
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while uploading the logo.');
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>