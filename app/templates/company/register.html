<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block title %}Company Registration{% endblock %}
{% block content %}
<div class="bg-gray-100 flex items-center justify-center min-h-screen flex-col">
    <div class="flash--messages mb-4 w-full max-w-lg">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul class="list-unstyled">
            {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </li>
            {% endfor %}
            </ul>
        {% endif %}
        {% endwith %}
    </div>
    <div class="flex items-center justify-between">
        <div class="max-w-lg p-8 items-center">
            <img src="{{ url_for('static', filename='images/system_images/hero_image.svg') }}" alt="Company Registration" class="w-60 m-0">
            <div class="flex flex-col gap-2">
                <h1 class="text-bold primary">NETPIPO</h1>
                <h1 class="text-2xl font-medium primary">Welcome to our platform</h1>
                <p class="text-gray-500">Register your company with us and enjoy the benefits of our services.</p>
                <p class="animated-line"></p>
                <p class="right-line"></p>
            </div>
        </div>
        <div class="max-w-lg">
            <h1 class="text-2xl font-medium text-center mb-6 primary">Create a new company</h1>
            <form id="multiStepForm" method="POST" action="/register_company">
                {{ form.csrf_token }}
                <div class="progress-bar mb-6">
                    <div class="step active"></div>
                    <div class="step"></div>
                    <div class="step"></div>
                    <div class="step"></div>
                </div>
                <div class="form-step active" id="step-1">
                    <div class="mb-4">
                        <label for="company_name" class="block font-medium text-gray-500 mb-2">Company Name <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                                <i class="fi fi-rr-user p-2 text-gray-400"></i>
                            <input id="company_name" name="company_name" required type="text" class="form-control w-full px-3 py-2 focus:outline-none text-sm" placeholder="Registered name">
                        </div>
                        <div id="company_name-error" class="error-message"></div>
                        <div id="company_name-success" class="success-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="company_tin" class="block font-medium text-gray-500 mb-2">Company TIN <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-hastag p-2 text-gray-400"></i>                        
                            <input id="company_tin" name="company_tin" required type="number" class="form-control w-full px-3 py-2 focus:outline-none  text-sm" placeholder="Company TIN">
                        </div>
                        <div id="company_tin-error" class="error-message"></div>
                        <div id="company_tin-success" class="success-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="rssb_number" class="block font-medium text-gray-500 mb-2">Company RSSB Number <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-id-badge p-2 text-gray-400"></i>                        
                            <input id="rssb_number" name="rssb_number" required type="text" class="form-control w-full px-3 py-2 focus:outline-none max-w-lg text-sm" placeholder="312166860000B">
                        </div>
                        <div id="rssb_number-error" class="error-message"></div>
                        <div id="rssb_number-success" class="success-message"></div>
                    </div>
                    <button type="button" class="btn btn-custom btn-block bg-white p-2 primary text-white py-2 rounded-lg hover:bg-green-100 hover:text-green-500 text-left" onclick="nextStep()">Next</button>
                </div>
                <div class="form-step" id="step-2">
                    <div class="mb-4">
                        <label for="email" class="block text-gray-500 font-medium mb-2">Email <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-envelopes p-2 text-gray-400"></i>                        
                            <input id="email" name="email" required type="email" class="form-control w-full px-3 py-2 focus:outline-none text-sm" placeholder="<EMAIL>">
                        </div>
                        <div id="email-error" class="error-message"></div>
                        <div id="email-success" class="success-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="number_of_employees" class="block text-gray-500 font-medium mb-2">Number of Employees <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-employees p-2 text-gray-400"></i>                        
                            <input id="number_of_employees" name="number_of_employees" required type="number" class="form-control w-full px-3 py-2 focus:outline-none text-sm" placeholder="10">
                        </div>
                        <div id="number_of_employees-error" class="error-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="phone_number" class="block text-gray-500 font-medium mb-2">Phone Number <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-circle-phone-flip p-2 text-gray-400"></i>                        
                            <input id="phone_number" name="phone_number" required type="text" class="form-control w-full px-3 py-2 focus:outline-none text-sm" placeholder="0700000000">
                        </div>
                        <div id="phone_number-error" class="error-message"></div>
                        <div id="phone_number-success" class="success-message"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <button type="button" class="btn btn-custom btn-block bg-gray-200 text-gray-400 p-2 rounded-lg hover:bg-gray-300 text-gray-500" onclick="prevStep()">Back</button>
                        <button type="button" class="btn btn-custom btn-block bg-white text-white p-2 primary rounded-lg hover:bg-green-200 hover:text-green-500" onclick="nextStep()">Next</button>
                    </div>
                </div>
                <div class="form-step" id="step-3">
                    <div class="mb-4">
                        <label for="country" class="block text-gray-500 font-medium mb-2">Country <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-flag p-2 text-gray-400"></i>                        
                            <input id="country" name="country" required type="text" class="form-control w-full px-3 py-2 focus:outline-none" value="Rwanda" readonly>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="province" class="block text-gray-500 font-medium mb-2">Province <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-map-location-track p-2 text-gray-400"></i>                        
                            <select id="province" name="province" class="form-control w-full px-3 py-2 focus:outline-none">
                                <option value="" disabled selected>Select Province</option>
                                {% for province in rwanda_data.keys() %}
                                    <option value="{{ province }}">{{ province }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div id="province-error" class="error-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="district" class="block text-gray-500 font-medium mb-2">District <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-map-marker p-2 text-gray-400"></i>                        
                            <select id="district" name="district" class="form-control w-full px-3 py-2 focus:outline-none">
                                <option value="" disabled selected>Select District</option>
                            </select>
                        </div>
                        <div id="district-error" class="error-message"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <button type="button" class="btn btn-custom btn-block bg-gray-200 text-gray-400 p-2 rounded-lg hover:bg-gray-300 text-gray-500" onclick="prevStep()">Back</button>
                        <button type="button" class="btn btn-custom btn-block bg-white text-white p-2 primary rounded-lg hover:bg-green-200 hover:text-green-500" onclick="nextStep()">Next</button>
                    </div>
                </div>
                <div class="form-step" id="step-4">
                    <div class="mb-4">
                        <label for="sector" class="block text-gray-500 font-medium mb-2">Sector <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-location-arrow p-2 text-gray-400"></i>                        
                            <select id="sector" name="sector" class="form-control w-full px-3 py-2 focus:outline-none">
                                <option value="" disabled selected>Select Sector</option>
                            </select>
                        </div>
                        <div id="sector-error" class="error-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="cell" class="block text-gray-500 font-medium mb-2">Cell <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-map-marker-check p-2 text-gray-400"></i>                        
                            <select id="cell" name="cell" class="form-control w-full px-3 py-2 focus:outline-none">
                                <option value="" disabled selected>Select Cell</option>
                            </select>
                        </div>
                        <div id="cell-error" class="error-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="village" class="block text-gray-500 font-medium mb-2">Village <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-location-crosshairs p-2 text-gray-400"></i>                        
                            <select id="village" name="village" class="form-control w-full px-3 py-2 focus:outline-none">
                                <option value="" disabled selected>Select Village</option>
                            </select>
                        </div>
                        <div id="village-error" class="error-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="company_type" class="block text-gray-500 font-medium mb-2">Company Type <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-tc-building p-2 text-gray-400"></i>                        
                            <select id="company_type" name="company_type" class="form-control w-full px-3 py-2 focus:outline-none">
                                <option value="public">Public</option>
                                <option value="private">Private</option>
                                <option value="non-profit">Non-Profit</option>
                            </select>
                        </div>
                        <div id="company_type-error" class="error-message"></div>
                    </div>
                    <div class="mb-4">
                        <label for="plan_id" class="block text-gray-500 font-medium mb-2">Select Plan <span class="text-red-500">*</span></label>
                        <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                            <i class="fi fi-rr-sort-amount-up p-2 text-gray-400"></i>                        
                            <span id="plan_id" name="plan_id" class="form-control w-full px-3 py-2 focus:outline-none">
                                {{ form.plan_id(class="form-control w-full px-3 py-2 focus:outline-none") }}
                            </span>
                        </div>
                        <div id="plan_id-error" class="error-message"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <button type="button" class="btn btn-custom btn-block bg-gray-200 text-gray-400 p-2 rounded-lg hover:bg-gray-300 text-gray-500" onclick="prevStep()">Back</button>
                        <button type="button" class="btn btn-custom btn-block bg-white text-white p-2 primary rounded-lg hover:bg-green-200 hover:text-green-500" onclick="nextStep()">Next</button>
                    </div>
                </div>
                <div class="form-step" id="step-5">
                    <p class="mb-4">Please review your information before submitting.</p>
                    
                    <div class="flex items-center justify-between mb-4">
                        <button type="button" class="btn btn-custom btn-block bg-gray-200 text-gray-400 p-2 rounded-lg hover:bg-gray-300 text-gray-500" onclick="prevStep()">Back</button>
                        <button type="submit" class="btn btn-custom btn-block bg-white text-white p-2 primary rounded-lg hover:bg-green-200 hover:text-green-500">Register</button>
                    </div>
                    </div>
            </form>
        </div>
    <div>
</div>

<script>
    const rwandaData = {{ rwanda_data | tojson }};

    document.getElementById('province').addEventListener('change', function() {
        const province = this.value;
        const districtSelect = document.getElementById('district');
        districtSelect.innerHTML = '<option value="" disabled selected>Select District</option>';
        document.getElementById('sector').innerHTML = '<option value="" disabled selected>Select Sector</option>';
        document.getElementById('cell').innerHTML = '<option value="" disabled selected>Select Cell</option>';
        document.getElementById('village').innerHTML = '<option value="" disabled selected>Select Village</option>';

        if (province && rwandaData[province]) {
            const districts = rwandaData[province];
            for (let district in districts) {
                districtSelect.innerHTML += `<option value="${district}">${district}</option>`;
            }
        }
    });

    document.getElementById('district').addEventListener('change', function() {
        const province = document.getElementById('province').value;
        const district = this.value;
        const sectorSelect = document.getElementById('sector');
        sectorSelect.innerHTML = '<option value="" disabled selected>Select Sector</option>';
        document.getElementById('cell').innerHTML = '<option value="" disabled selected>Select Cell</option>';
        document.getElementById('village').innerHTML = '<option value="" disabled selected>Select Village</option>';

        if (province && district && rwandaData[province][district]) {
            const sectors = rwandaData[province][district];
            for (let sector in sectors) {
                sectorSelect.innerHTML += `<option value="${sector}">${sector}</option>`;
            }
        }
    });

    document.getElementById('sector').addEventListener('change', function() {
        const province = document.getElementById('province').value;
        const district = document.getElementById('district').value;
        const sector = this.value;
        const cellSelect = document.getElementById('cell');
        cellSelect.innerHTML = '<option value="" disabled selected>Select Cell</option>';
        document.getElementById('village').innerHTML = '<option value="" disabled selected>Select Village</option>';

        if (province && district && sector && rwandaData[province][district][sector]) {
            const cells = rwandaData[province][district][sector];
            for (let cell in cells) {
                cellSelect.innerHTML += `<option value="${cell}">${cell}</option>`;
            }
        }
    });

    document.getElementById('cell').addEventListener('change', function() {
        const province = document.getElementById('province').value;
        const district = document.getElementById('district').value;
        const sector = document.getElementById('sector').value;
        const cell = this.value;
        const villageSelect = document.getElementById('village');
        villageSelect.innerHTML = '<option value="" disabled selected>Select Village</option>';

        if (province && district && sector && cell && rwandaData[province][district][sector][cell]) {
            const villages = rwandaData[province][district][sector][cell];
            villages.forEach(function(village) {
                villageSelect.innerHTML += `<option value="${village}">${village}</option>`;
            });
        }
    });

    let currentStep = 1;

    function showStep(step) {
        document.querySelectorAll('.form-step').forEach(function(stepElement) {
            stepElement.classList.remove('active');
        });
        document.querySelectorAll('.progress-bar .step').forEach(function(stepElement, index) {
            stepElement.classList.remove('active');
            if (index < step - 1) {
                stepElement.classList.add('active');
            }
        });
        document.getElementById('step-' + step).classList.add('active');
    }

    function nextStep() {
        if (validateStep(currentStep)) {
            if (currentStep < 5) {
                currentStep++;
                showStep(currentStep);
            }
        } else {
            document.querySelector('.btn.btn-custom.bg-blue-600').disabled = true;
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    }

    function validateStep(step) {
        let isValid = true;
        switch (step) {
            case 1:
                isValid = validateCompanyName() && validateCompanyTin() && validateRssbNumber();
                break;
            case 2:
                isValid = validateEmail() && validateNumberOfEmployees() && validatePhoneNumber();
                break;
            case 3:
                isValid = validateProvince() && validateDistrict();
                break;
            case 4:
                isValid = validateSector() && validateCell() && validateVillage() && validateCompanyType() && validatePlanId();
                break;
            default:
                isValid = true;
        }
        return isValid;
    }

    function validateCompanyName() {
        const companyName = document.getElementById('company_name').value;
        const errorMessage = document.getElementById('company_name-error');
        const successMessage = document.getElementById('company_name-success');
        if (companyName.trim() === '') {
            errorMessage.classList.add('active');
            successMessage.classList.remove('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            successMessage.classList.add('active');
            return true;
        }
    }

    function validateCompanyTin() {
        const companyTin = document.getElementById('company_tin').value;
        const errorMessage = document.getElementById('company_tin-error');
        const successMessage = document.getElementById('company_tin-success');
        if (companyTin.trim() === '') {
            errorMessage.classList.add('active');
            successMessage.classList.remove('active');
            return false;
        }
        else if (companyTin.length !== 9) {
            errorMessage.classList.add('active');
            errorMessage.textContent = 'Company TIN must be 9 digits.';
            successMessage.classList.remove('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            successMessage.classList.add('active');
            return true;
        }
    }

    function validateRssbNumber() {
        const rssbNumber = document.getElementById('rssb_number').value;
        const errorMessage = document.getElementById('rssb_number-error');
        const successMessage = document.getElementById('rssb_number-success');
        if (rssbNumber.trim() === '') {
            errorMessage.classList.add('active');
            successMessage.classList.remove('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            successMessage.classList.add('active');
            return true;
        }
    }

    function validateEmail() {
        const email = document.getElementById('email').value;
        const errorMessage = document.getElementById('email-error');
        const successMessage = document.getElementById('email-success');
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(email)) {
            errorMessage.classList.add('active');
            successMessage.classList.remove('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            successMessage.classList.add('active');
            return true;
        }
    }

    function validateNumberOfEmployees() {
        const numberOfEmployees = document.getElementById('number_of_employees').value;
        const errorMessage = document.getElementById('number_of_employees-error');
        if (numberOfEmployees.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validatePhoneNumber() {
        const phoneNumber = document.getElementById('phone_number').value;
        const errorMessage = document.getElementById('phone_number-error');
        const successMessage = document.getElementById('phone_number-success');
        const phonePattern = /^\d{10}$/;
        if (!phonePattern.test(phoneNumber)) {
            errorMessage.classList.add('active');
            successMessage.classList.remove('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            successMessage.classList.add('active');
            successMessage.textContent = 'Phone number is valid.';
            return true;
        }
    }

    function validateProvince() {
        const province = document.getElementById('province').value;
        const errorMessage = document.getElementById('province-error');
        if (province.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validateDistrict() {
        const district = document.getElementById('district').value;
        const errorMessage = document.getElementById('district-error');
        if (district.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validateSector() {
        const sector = document.getElementById('sector').value;
        const errorMessage = document.getElementById('sector-error');
        if (sector.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validateCell() {
        const cell = document.getElementById('cell').value;
        const errorMessage = document.getElementById('cell-error');
        if (cell.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validateVillage() {
        const village = document.getElementById('village').value;
        const errorMessage = document.getElementById('village-error');
        if (village.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validateCompanyType() {
        const companyType = document.getElementById('company_type').value;
        const errorMessage = document.getElementById('company_type-error');
        if (companyType.trim() === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    function validatePlanId() {
        const planId = document.getElementById('plan_id').value;
        const errorMessage = document.getElementById('plan_id-error');
        if (planId === '') {
            errorMessage.classList.add('active');
            return false;
        } else {
            errorMessage.classList.remove('active');
            return true;
        }
    }

    // Add event listeners for real-time validation
    document.getElementById('company_name').addEventListener('input', validateCompanyName);
    document.getElementById('company_tin').addEventListener('input', validateCompanyTin);
    document.getElementById('rssb_number').addEventListener('input', validateRssbNumber);
    document.getElementById('email').addEventListener('input', validateEmail);
    document.getElementById('number_of_employees').addEventListener('input', validateNumberOfEmployees);
    document.getElementById('phone_number').addEventListener('input', validatePhoneNumber);
    document.getElementById('province').addEventListener('input', validateProvince);
    document.getElementById('district').addEventListener('input', validateDistrict);
    document.getElementById('sector').addEventListener('input', validateSector);
    document.getElementById('cell').addEventListener('input', validateCell);
    document.getElementById('village').addEventListener('input', validateVillage);
    document.getElementById('company_type').addEventListener('input', validateCompanyType);
    document.getElementById('plan_id').addEventListener('input', validatePlanId);

    // Add event listeners for AJAX validation
    document.getElementById('company_name').addEventListener('blur', checkCompanyExists);
    document.getElementById('company_tin').addEventListener('blur', checkCompanyExists);
    document.getElementById('email').addEventListener('blur', checkCompanyExists);
    document.getElementById('rssb_number').addEventListener('blur', checkCompanyExists);
    document.getElementById('phone_number').addEventListener('blur', checkCompanyExists);

    function checkCompanyExists() {
        const companyName = document.getElementById('company_name').value;
        const companyTin = document.getElementById('company_tin').value;
        const email = document.getElementById('email').value;
        const rssbNumber = document.getElementById('rssb_number').value;
        const phoneNumber = document.getElementById('phone_number').value;

        // Show loading indicator
        document.getElementById('company_name-error').textContent = 'Checking...';
        document.getElementById('company_tin-error').textContent = 'Checking...';
        document.getElementById('email-error').textContent = 'Checking...';
        document.getElementById('rssb_number-error').textContent = 'Checking...';
        document.getElementById('phone_number-error').textContent = 'Checking...';

        fetch('/check_company_exists', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrf_token]').value
            },
            body: JSON.stringify({
                company_name: companyName,
                company_tin: companyTin,
                email: email,
                rssb_number: rssbNumber,
                phone_number: phoneNumber
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                const issues = data.issues;
                Object.keys(issues).forEach(field => {
                    const errorMessageElement = document.getElementById(field + '-error');
                    const successMessageElement = document.getElementById(field + '-success');
                    if (errorMessageElement) {
                        errorMessageElement.classList.add('active');
                        errorMessageElement.textContent = issues[field];
                        if (successMessageElement) {
                            successMessageElement.classList.remove('active');
                        }
                    }
                });
            } else {
                
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    showStep(currentStep);
</script>


{% endblock %}
