<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                    <i class="fi fi-rr-settings"></i>Settings
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1 class="dynamic-title">Edit Company Profile</h1>
                <form action="{{ url_for('company_data_v2.edit_company_profile') }}" method="POST">
                    {{ form.csrf_token }}
                    <div class="form-row">
                        <div class="form-group">
                            <label for="company_name">Company Name</label>
                            <div class="input-group-text">
                                <i class="fas fa-building"></i>
                                {{ form.company_name(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="company_tin">Company Tin</label>
                            <div class="input-group-text">
                                <i class="fas fa-id-card"></i>
                                {{ form.company_tin(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="rssb_number">Rssb Number</label>
                            <div class="input-group-text">
                                <i class="fas fa-id-card"></i>
                                {{ form.rssb_number(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="company_type">Company Type</label>
                            <div class="input-group-text">
                                <i class="fas fa-building"></i>
                                <select id="company_type" name="company_type" class="form-control">
                                    <option value="public">Public</option>
                                    <option value="private">Private</option>
                                    <option value="non_profit">Non Profit</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone_number">Phone Number</label>
                            <div class="input-group-text">
                                <i class="fas fa-phone"></i>
                                {{ form.phone_number(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <div class="input-group-text">
                                <i class="fas fa-envelope"></i>
                                {{ form.email(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="number_employee">Number of Employees</label>
                            <div class="input-group-text">
                                <i class="fas fa-users"></i>
                                {{ form.number_employee(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="country">Country</label>
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.country(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="province">Province</label>
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.province(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="district">District</label>
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.district(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="sector">Sector</label>
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.sector(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="cell">Cell</label>
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.cell(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="cell">Village</label>
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.village(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="border-bottom"></div>
                    <div>
                        <h1>Leave Settings</h1>
                        <div class="form-row">
                            <div>
                                {{form.initial_qualification_period.label(class="leave-elements")}}
                                <div class="input-group-text">
                                    <i class="fas fa-calendar-check"></i>
                                    {{ form.initial_qualification_period(class="form-control") }}
                                </div>
                            </div>
                            <div>
                                {{ form.increment_policy.label(class="leave-elements")}}
                                <div class="form-group">
                                    <div class="input-group-text">
                                        <i class="fas fa-calendar-alt"></i>
                                        {{ form.increment_policy(class="form-control", placeholder="Number of Years") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h1>RAMA Settings</h1>
                        <div class="form-row">
                            <div>
                                {{form.rama_applicable.label(class="leave-elements")}}
                                <div class="input-group-text">
                                    <i class="fas fa-calendar-check"></i>
                                    {{ form.rama_applicable(class="form-control") }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="border-bottom"></div>
                    <button type="submit" class="submit-btn">Save</button>
                </form>
            </div>
        </div>
    </div>
{% endblock %}