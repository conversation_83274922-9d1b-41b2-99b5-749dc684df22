<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <style>
        #map { height: 400px; width: 100%; }
        #search-box { width: 100%; padding: 8px; margin-bottom: 10px; }
    </style>
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Set the Company Location</h1>
                <a class="template-link btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                    <i class="fi fi-rr-settings"></i> Settings
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <h1><i class="fi fi-rr-land-layer-location"></i> {% if location %} {{location}} {%else%} none {% endif%}</h1>
            <div class="grey-container">
                <div class="upload-info">
                    <h5>Company Location Guidelines</h5>
                    <ol>
                        <li>Search for your company location using the search box.</li>
                        <li>Click on the map to set the exact location.</li>
                        <li>The latitude and longitude will be automatically filled in.</li>
                        <li>Drag the marker to adjust the location if needed.</li>
                        <li>Click "Save Location" to store the coordinates.</li>
                    </ol>
                </div>
            </div>            
            <input type="text" id="search-box" placeholder="Search (KG 123 St)" class="form-control">
            <button onclick="searchLocation()" class="submit-light-btn">Search</button>
            
            <div class="flex-container">
                <div id="map"></div>
                <div class="white-container">
                    <form action="{{ url_for('company_data_v2.company_location') }}" method="POST">

                            <div class="form-row">
                                <div class="form-group">
                                    <label>Latitude:</label>
                                    <input type="text" id="latitude" name="latitude" readonly required class="form-control"><br>
                                </div>
                                <div class="form-group">
                                    <label>Longitude:</label>
                                    <input type="text" id="longitude" name="longitude" readonly required class="form-control"><br>
                                </div>
                            </div>
                        <button type="submit" class="submit-btn">Save Location</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        let map = L.map('map').setView([-1.9501, 30.0588], 12);  // Default: Kigali, Rwanda
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', { attribution: '© OpenStreetMap' }).addTo(map);

        let marker = L.marker([-1.9501, 30.0588], { draggable: true }).addTo(map);

        // Click to update latitude & longitude
        map.on('click', function(e) {
            document.getElementById('latitude').value = e.latlng.lat;
            document.getElementById('longitude').value = e.latlng.lng;
            marker.setLatLng(e.latlng);
        });

        function searchLocation() {
            let query = document.getElementById('search-box').value;
            if (!query) {
                alert("Please enter a location to search.");
                return;
            }

            let url = `https://nominatim.openstreetmap.org/search?format=json&q=${query}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.length > 0) {
                        let lat = data[0].lat;
                        let lon = data[0].lon;
                        
                        map.setView([lat, lon], 15);  // Zoom in to the location
                        marker.setLatLng([lat, lon]);
                        document.getElementById('latitude').value = lat;
                        document.getElementById('longitude').value = lon;
                    } else {
                        alert("Location not found. Try a different search term.");
                    }
                })
                .catch(error => console.error("Error fetching location:", error));
        }
    </script>
{% endblock %}
