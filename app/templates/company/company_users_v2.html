<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a href="{{ url_for('user_data_v2.settings') }}" class="template-link btn-edit">
                    <i class="fi fi-rr-list"></i> Settings
                </a>
                <a class="btn-edit" href="{{ url_for('company_users_v2.add_company_user') }}">
                    <i class="fi fi-rr-plus-small"></i> User
                </a>
            </div>
        </div>
    <div class="dyn_container">
        <div class="grey-container">
            <p>Manage your users here. You can add, or delete users as needed.</p>
            <div class="upload-info">
            <ol>
                <li>To add a user, click on the "User" button.</li>
                <li>The user will receive an email with the login credentials</li>
                <li>From there, the user can log in and have access to his portal, where he can upload documents, request leave, advances and other functionalities are available there.</li>
                <li>To delete a user, click on the "Delete" link next to the user you want to remove.</li>
            </ol>
            </div>
        </div>
        <div class="form--container">
            <h1>Users</h1>

            <div>
                <table class="no-vertical-border">
                    <thead class="thead th-custom">
                        <tr>
                            <th scope="col">Username</th>
                            <th scope="col">Email</th>
                            <th scope="col">Phone</th>
                            <th scope="col">Role</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.phone_number }}</td>
                                <td>{{ user.role }}</td>
                                <td>        
                                    <a class="red" href="{{ url_for('company_users_v2.delete_company_user', user_id=user.user_id) }}"
                                    onclick="return confirm('Are you sure you want to delete this user?');">
                                        <i class="fi fi-rr-trash"></i>
                                    Delete</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
