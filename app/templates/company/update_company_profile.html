<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Company Profile</title>
</head>
<body>
    <form method="POST">
        {{ form.csrf_token }}
        <h4>Update Company Profile</h4>
        <div>
            {{ form.company_name.label }}
            {{ form.company_name }}
            {% for error in form.company_name.errors %}
                <small class="text-danger">{{ error }}</small>
            {% endfor %}
        </div>
       <div>
        {{ form.company_tin.label }}
        {{ form.company_tin }}
        {% for error in form.company_tin.errors %}
            <small class="text-danger">{{ error }}</small>
        {% endfor %}
       </div>
        <div>
            {{ form.rssb_number.label }}
            {{ form.rssb_number }}
            {% for error in form.rssb_number.errors %}
                <small class="text-danger">{{ error }}</small>
            {% endfor %}
        </div>
        
        <div>
            {{ form.email.label }}
            {{ form.email }}
            {% for error in form.email.errors %}
                <small class="text-danger">{{ error }}</small>
            {% endfor %}
        </div>
        <div>
            {{ form.phone_number.label }}
            {{ form.phone_number }}
            {% for error in form.phone_number.errors %}
                <small class="text-danger">{{ error }}</small>
            {% endfor %}
        </div>
        <div>
            {{ form.face_api_key.label }}
            {{ form.face_api_key }}
            {% for error in form.face_api_key.errors %}
                <small class="text-danger">{{ error }}</small>
            {% endfor %}
        </div>
                     
        <div>
            {{ form.submit }}
        </div>
</body>
</html>