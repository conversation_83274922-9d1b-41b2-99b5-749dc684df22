<!DOCTYPE html>
<html>
<head>
    <title>Add Company User</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class ="template-link btn-edit" href="#" data-template-url="{{ url_for('company_users.company_users') }}">                
            <i class="fas fa-list"></i> Users
        </a>
    </div>
    <div id="message-container"></div>
    <div id="passed-container"></div>
    <div class="real-form">
        <div id="message-container"></div>
        <div id="passed-container"></div>
        <h1>Add User</h1>
            <form method="post" action="{{ url_for('company_users.add_company_user') }}">
                <div class="form-row">
                    <div class="form-group">
                        <label for="employee">Select Employee:</label><br>
                        <div class="input-group-text">
                            <i class="fas fa-user"></i>
                            <select name="employee" id="employee" required>
                                <option value="" disabled selected>Select an employee</option>
                                {% for employee in employees %}
                                    <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="role">Select Role:</label>
                        <div class="input-group-text">
                            <i class="fas fa-user-tag"></i>
                            <select name="role" id="role" required>
                                <option value="" disabled selected>Select a role</option>
                                {% for role in roles %}
                                    <option value="{{ role.role_name }}">{{ role.role_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div> 
                <button type="submit" class="btn-custom">Add User</button></p>
        </form>
    </div>
</body>
</html>
