<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block content %}

<section class="bg-gray-100 flex items-center justify-center min-h-screen flex-col">
    <div class="flash--messages mb-4 w-full max-w-md">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul class="list-unstyled">
            {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </li>
            {% endfor %}
            </ul>
        {% endif %}
        {% endwith %}
    </div> 
        <h1 class="dark">Login to Self-service portal</h1>
        <form  method="post" action="{{ url_for('company_users.login_company_users') }}" class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            {{ form.hidden_tag() }}
            {{ form.username.label(class="block dark font-bold") }}
            {{ form.username(class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200 mb-3", placeholder="Enter your email") }}
            {% for error in form.username.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
            {{ form.password.label(class="block dark font-bold") }}
                {{ form.password(class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200 mb-3", placeholder="Enter your password") }}
            {% for error in form.password.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
            {{ form.company_tin.label(class="block dark font-bold") }}
                {{ form.company_tin(class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200 mb-3", placeholder="Enter your company's TIN") }}
            {% for error in form.company_tin.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
            {{ form.submit(class="w-full mt-2 primary-background flex items-center justify-center") }}
            <a href="{{ url_for('company_users.reset_user_password') }}" class="text-blue-600 hover:underline">Forgot Password ?</a>

        </form>
</section>
{% endblock %}