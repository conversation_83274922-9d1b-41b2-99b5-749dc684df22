<!DOCTYPE html>
<html>
<head>
    <title>Company Users</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('company_users.add_company_user') }}">
            <i class="fas fa-user"></i> Create user
        </a>
    </div>
    <div class="dynamic--form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <h1>Users</h1>
        <div class="table">
            <div>
                <table class="table">
                    <thead class="thead th-custom">
                        <tr>
                            <th scope="col">Username</th>
                            <th scope="col">Email</th>
                            <th scope="col">Phone</th>
                            <th scope="col">Role</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.phone_number }}</td>
                                <td>{{ user.role }}</td>
                                <td>        
                                    <a class="template-link" href="#" data-template-url="{{ url_for('company_users.delete_company_user', user_id=user.user_id) }}">Delete</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>