<!DOCTYPE html>
<html>
<head>
    <title>Create Chart of Account</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
    </div>
    <div class="dynamic--form">
        <h1>Create Chart of Account</h1>
        <form method="post" action="{{ url_for('quickbooks_v2.create_account') }}">
            {{ form.csrf_token }}
            <div class="form-group">
                {{ form.name.label }} 
                <div class="input-group-text">
                    <i class="fas fa-file-invoice"></i>
                    {{ form.name(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.description.label }} 
                <div class="input-group-text">
                    <i class="fas fa-info-circle"></i>
                    {{ form.description(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.account_type.label }} 
                <div class="input-group-text">
                    <i class="fas fa-list"></i>
                    {{ form.account_type(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.account_subtype.label }} 
                <div class="input-group-text">
                    <i class="fas fa-list-alt"></i>
                    {{ form.account_subtype(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.submit(class="btn-edit") }}
            </div>
        </form>
    </div>
</body>
</html>
