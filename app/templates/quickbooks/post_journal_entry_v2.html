<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Post Journal Entry</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Bootstrap for basic styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .vendor-dropdown.d-none {
            display: none;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <h2 class="mb-4">Map Journal Entry Accounts</h2>
        {% with flashes = get_flashed_messages() %}
            {% if flashes %}
                <div class="alert alert-info">
                    {% for message in flashes %}
                        {{ message }}
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <form method="POST">
            <!-- Journal entry memo field -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="journal_memo">Journal Entry Memo</label>
                        <input type="text" name="journal_memo" id="journal_memo" class="form-control"
                               value="Payroll Journal Entry" placeholder="Enter a memo for this journal entry">
                    </div>
                </div>
            </div>
            <table class="table table-bordered bg-white">
                <thead class="table-light">
                    <tr>
                        <th>Account</th>
                        <th>Debit</th>
                        <th>Credit</th>
                        <th>QuickBooks Account & Description</th>
                        <th>Location</th>
                        <th>Department</th>
                        <th>Vendor (if applicable)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for name, values in account_totals.items() %}
                        <tr>
                            <td>{{ name }}</td>
                            <td>{{ values.DR | round(2) }}</td>
                            <td>{{ values.CR | round(2) }}</td>
                            <td>
                                <!-- Account select -->
                                <select name="{{ name }}" class="form-select account-select" required>
                                    <option value="">-- Select Account --</option>
                                    {% for acc in account_choices %}
                                    <option value="{{ acc.Id }}" data-type="{{ acc.AccountType }}">
                                        {{ acc.Name }} | {{ acc.AccountType }} | {{ acc.Currency }}
                                    </option>
                                    {% endfor %}
                                </select>

                                <!-- Description input -->
                                <input type="text" name="desc_{{ name }}" class="form-control mt-2"
                                       placeholder="Description (optional)" value="{{ name }}">
                            </td>
                            <td>
                                <!-- Location select for this line -->
                                <select name="location_{{ name }}" class="form-select">
                                    <option value="">-- Select Location --</option>
                                    {% for location in locations %}
                                    <option value="{{ location.Id }}">{{ location.Name }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <!-- Department select for this line -->
                                <select name="department_{{ name }}" class="form-select">
                                    <option value="">-- Select Department --</option>
                                    {% for department in departments %}
                                    <option value="{{ department.Id }}">{{ department.Name }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td>
                                <!-- Vendor select -->
                                <select name="vendor_{{ name }}" class="form-select vendor-dropdown">
                                    <option value="">-- Select Vendor --</option>
                                    {% for vendor in vendors %}
                                    <option value="{{ vendor.Id }}">{{ vendor.DisplayName }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-light fw-bold">
                    <tr>
                        <td>Totals</td>
                        <td id="total-debit">0.00</td>
                        <td id="total-credit">0.00</td>
                        <td>
                            <div id="balance-status" class="alert alert-warning mb-0 py-1">
                                Journal entry is not balanced
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>

            <div class="mt-4 d-flex align-items-center">
                <button type="submit" id="submit-button" class="btn btn-primary" disabled>Post Journal Entry</button>
                <div id="balance-message" class="ms-3 text-danger">
                    Debits and credits must be equal before posting
                </div>
            </div>
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Toggle Vendor Dropdown Based on Account Type and Calculate Totals -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const selects = document.querySelectorAll(".account-select");
            const submitButton = document.getElementById("submit-button");
            const balanceStatus = document.getElementById("balance-status");
            const balanceMessage = document.getElementById("balance-message");

            // Function to calculate and update totals
            function updateTotals() {
                let totalDebit = 0;
                let totalCredit = 0;

                // Get all rows in the table body
                const rows = document.querySelectorAll("tbody tr");

                rows.forEach(row => {
                    const cells = row.querySelectorAll("td");
                    if (cells.length >= 3) {
                        // Get debit and credit values from the cells
                        const debitText = cells[1].textContent.trim();
                        const creditText = cells[2].textContent.trim();

                        // Parse values, handling empty strings
                        const debit = debitText ? parseFloat(debitText) : 0;
                        const credit = creditText ? parseFloat(creditText) : 0;

                        // Add to totals
                        totalDebit += debit;
                        totalCredit += credit;
                    }
                });

                // Round to 2 decimal places to avoid floating point issues
                totalDebit = Math.round(totalDebit * 100) / 100;
                totalCredit = Math.round(totalCredit * 100) / 100;

                // Update the total displays
                document.getElementById("total-debit").textContent = totalDebit.toFixed(2);
                document.getElementById("total-credit").textContent = totalCredit.toFixed(2);

                // Check if debits and credits are balanced
                const isBalanced = totalDebit === totalCredit && totalDebit > 0;

                // Update UI based on balance status
                if (isBalanced) {
                    balanceStatus.className = "alert alert-success mb-0 py-1";
                    balanceStatus.textContent = "Journal entry is balanced";
                    balanceMessage.style.display = "none";
                    submitButton.disabled = false;
                } else {
                    balanceStatus.className = "alert alert-warning mb-0 py-1";
                    balanceStatus.textContent = "Journal entry is not balanced";
                    balanceMessage.style.display = "block";
                    submitButton.disabled = true;
                }
            }

            // Calculate totals on page load
            updateTotals();

            // Toggle vendor dropdown based on account type
            selects.forEach(select => {
                select.addEventListener("change", function () {
                    const selectedOption = this.options[this.selectedIndex];
                    const accountType = selectedOption.getAttribute("data-type");
                    const td = this.closest("td");
                    const vendorSelect = td.querySelector(".vendor-dropdown");

                    if (accountType === "AccountsPayable") {
                        vendorSelect.classList.remove("d-none");
                        vendorSelect.setAttribute("required", "required");
                    } else {
                        vendorSelect.classList.add("d-none");
                        vendorSelect.removeAttribute("required");
                        vendorSelect.value = "";
                    }

                    // Recalculate totals when account selection changes
                    updateTotals();
                });
            });
        });
    </script>
</body>
</html>
