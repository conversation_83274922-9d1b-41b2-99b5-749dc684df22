<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    {% for category, message in messages %}
      <div class="alert alert-{{ 'success' if category == 'success' else 'danger' if category == 'danger' else 'warning' }} alert-dismissible fade show" role="alert">
        <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' if category == 'danger' else 'info-circle' }} me-2"></i>
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    {% endfor %}
  {% endif %}
{% endwith %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-unlink me-2"></i>
                        Disconnect QuickBooks Integration
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="text-danger mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Warning: This action cannot be undone
                            </h5>

                            <p class="mb-3">
                                Disconnecting QuickBooks will permanently remove the integration between your HRMS system and QuickBooks Online.
                            </p>

                            <div class="alert alert-warning" role="alert">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>
                                    What will happen when you disconnect:
                                </h6>
                                <ul class="mb-0">
                                    <li>All stored QuickBooks access tokens will be revoked</li>
                                    <li>Your QuickBooks authorization will be cleared from our system</li>
                                    <li>You will no longer be able to sync payroll data to QuickBooks</li>
                                    <li>Journal entries and account creation features will be disabled</li>
                                    <li>You can reconnect at any time by going through the authorization process again</li>
                                </ul>
                            </div>

                            <p class="text-muted mb-4">
                                <strong>Note:</strong> This will not affect any data that has already been synced to QuickBooks.
                                Your existing journal entries and accounts in QuickBooks will remain intact.
                            </p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="p-3">
                                <i class="fas fa-plug text-danger" style="font-size: 4rem; opacity: 0.3;"></i>
                                <p class="text-muted mt-3">
                                    <small>QuickBooks Integration</small>
                                </p>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-0 text-muted">
                                <small>Are you sure you want to proceed with disconnecting QuickBooks?</small>
                            </p>
                        </div>
                        <div>
                            <a href="{{ url_for('admin_data.dashboard') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>
                                Cancel
                            </a>
                            <form method="post" style="display: inline;">
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to disconnect QuickBooks? This action cannot be undone.')">
                                    <i class="fas fa-unlink me-1"></i>
                                    Disconnect QuickBooks
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Need Help?
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Reconnecting Later</h6>
                            <p class="text-muted small">
                                If you need to reconnect QuickBooks in the future, you can do so by going to
                                <strong>Settings → Integrations → QuickBooks</strong> and clicking "Connect to QuickBooks".
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Having Issues?</h6>
                            <p class="text-muted small">
                                If you're experiencing problems with the QuickBooks integration, consider contacting
                                support before disconnecting, as there may be alternative solutions.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.btn {
    border-radius: 6px;
}

.alert {
    border-radius: 8px;
}
</style>
{% endblock %}
