{% block content %}
    <meta charset="UTF-8">
    <title>Payroll Calculation Results</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='payroll/salary_calculator.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/2.5.0/remixicon.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <div class="bg-container">
        <h1 class="mb-4">
            {% if employee_type == 'consultant' %}
                WHT Calculation Results
            {% else %}
                Payroll Calculation Results
            {% endif %}
        </h1>
        <div class="printable-area">
            <table class="table table-striped table-bordered">
                <thead class="thead">
                    <tr>
                        <th colspan="2">{{ employee_name }}</th>
                        
                    </tr>
                    <tr>
                        <th colspan="2">{{ employee_type }} Employee</th>
                    </tr>
                    <tr>
                        <th>Details</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        {% if employee_type == 'consultant' %}
                            <td>WHT (Withholding Tax)</td>
                        {% else %}
                            <td>PAYE (Pay As You Earn)</td> 
                        {% endif %}
                        <td>RWF {{ paye }}</td>
                    </tr>
                    {% if employee_type == 'consultant' %}

                    {% else %}
                        <tr>
                            <td>Total Pension</td>
                            <td>RWF {{ total_pension_value }}</td>
                        </tr>
                        
                        
                            <tr>
                                <td>Total Maternity</td>
                                <td>RWF {{ total_maternity_value }}</td>
                            </tr>
                        
                        <tr>
                            <td>CBHI (Community-Based Health Insurance)</td>
                            <td>RWF {{ cbhi_value }}</td>
                        </tr>
                        <tr>
                            <td>Medical Insurance ({{ medical_total }}%)</td>
                            <td>RWF {{ medical_insurance_value }}</td>
                        </tr>
                    {% endif %}
                    <tr>
                        <td>Net Salary</td>
                        <td>RWF {{ net_salary_value }}</td>
                    </tr>
                    <tr>
                        <td>Other Deductions from Net</td>
                        <td>RWF {{ other_deductions }}</td>
                    </tr>
                    <tr>
                        <td>Net to pay</td>
                        <td>RWF {{ monthly_pyt }}</td>
                    </tr>
                </tbody>
                <tfoot class="font-weight-bold">
                    <tr>
                        <td>Total Staff Cost</td>
                        <td>RWF {{ total_payroll_summary }}</td>
                    </tr>
                </tfoot>
            </table>
            {% if  employee_type  == 'consultant' %}

            {% else %}
                <table class="table table-striped table-bordered mt-4">
                    <thead class="thead">
                        <tr>
                            <th colspan="2">Payroll Details</th>
                        </tr>
                        <tr>
                            <th>Details</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        
                        <tr>
                            <td>Gross Salary</td>
                            <td>RWF {{ gross_needed }}</td>
                        </tr>
                        </tr>
                        <tr>
                            <td>Pension Employer Contribution (5%)</td>
                            <td>RWF {{ pension_er_value }}</td>
                        </tr>
                        <tr>
                            <td>Maternity Employer Contribution (0.3%)</td>
                            <td>RWF {{ maternity_er_value }}</td>
                        </tr>
                        <tr>
                            <td>Medical Insurance ER </td>
                            <td>RWF {{ medical_insurance_er }}</td>
                        </tr>
                    </tbody>
                    <tfoot class="font-weight-bold">
                        <tr>
                            <td>Total Staff Cost</td>
                            <td>RWF {{ total_payroll_details }}</td>
                        </tr>
                    </tfoot>
                </table>
            {% endif %}
        </div>
        <div class="btn-pack">
            <button class="btn-print" onclick="window.print()">Print</button>
            <a href="{{ url_for('payroll.payroll_calculator') }}" class="btn-back">Back</a>  
        </div>      
    </div>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
{% endblock %}