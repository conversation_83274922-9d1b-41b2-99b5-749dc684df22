<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll History</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-filters.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<body>
    <div class="dynamic--form">
        <div class="dynamic--buttons">
            <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
                <i class="fas fa-arrow-left"></i> back
            </a>
        </div>
        <h1>Payroll History</h1>
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">x</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>

        <!-- Advanced Filtering Section -->
        <div class="filter-section">
            <button class="btn-edit" id="toggle-history-filters" onclick="$('#history-filter-form').slideToggle()">
                <i class="fas fa-filter"></i> Toggle Advanced Filters
            </button>

            <form id="history-filter-form" class="filter-form" style="display: none;">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="filter-employee-name">Employee Name:</label>
                        <input type="text" id="filter-employee-name" class="form-control" placeholder="Search by name">
                    </div>

                    <div class="filter-group">
                        <label for="filter-start-date">Start Date:</label>
                        <input type="date" id="filter-start-date" class="form-control">
                    </div>

                    <div class="filter-group">
                        <label for="filter-end-date">End Date:</label>
                        <input type="date" id="filter-end-date" class="form-control">
                    </div>
                </div>

                <div class="filter-row">
                    <div class="filter-group">
                        <label for="filter-min-salary">Min Net Salary:</label>
                        <input type="number" id="filter-min-salary" class="form-control" placeholder="Min amount">
                    </div>

                    <div class="filter-group">
                        <label for="filter-max-salary">Max Net Salary:</label>
                        <input type="number" id="filter-max-salary" class="form-control" placeholder="Max amount">
                    </div>
                </div>

                <div class="filter-buttons">
                    <button type="button" id="apply-history-filters" class="btn-edit">
                        <i class="fas fa-check"></i> Apply Filters
                    </button>
                    <button type="button" id="reset-history-filters" class="btn-cancel">
                        <i class="fas fa-redo"></i> Reset Filters
                    </button>
                </div>
            </form>
        </div>
        <br>
        <div class="large--table">
            <table id="payroll_history">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>B.Salary</th>
                        <th>TR.Allowance</th>
                        <th>H.Allowance</th>
                        <th>G.Salary</th>
                        <th>ER Pension</th>
                        <th>EE Pension</th>
                        <th>Total Pension</th>
                        <th>ER Martenity</th>
                        <th>EE Martenity</th>
                        <th>Total Martenity</th>
                        <th>PAYEE</th>
                        <th>CBHI</th>
                        <th>Total Deductions</th>
                        <th>Other Deductions</th>
                        <th>BRD Deductions</th>
                        <th>Advances</th>
                        <th>Reimbursements</th>
                        <th>Net Salary</th>
                        <th>Net Pay</th>

                        <th>Payroll Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payroll in payrolls %}
                        <tr>
                            <td>{{ payroll.employee_name }}</td>
                            <td>{{ payroll.basic_salary }}</td>
                            <td>{{ payroll.transport_allowance }}</td>
                            <td>{{ payroll.house_allowance }}</td>
                            <td>{{ payroll.gross_salary }}</td>
                            <td>{{ payroll.employer_pension }}</td>
                            <td>{{ payroll.employee_pension }}</td>
                            <td>{{ payroll.total_pension }}</td>
                            <td>{{ payroll.employer_maternity }}</td>
                            <td>{{ payroll.employee_maternity }}</td>
                            <td>{{ payroll.total_maternity }}</td>
                            <td>{{ payroll.payee }}</td>
                            <td>{{ payroll.cbhi }}</td>
                            <td>{{ payroll.total_deductions }}</td>
                            <td>{{ payroll.other_deductions }}</td>
                            <td>{{ payroll.brd_deductions }}</td>
                            <td>{{ payroll.advance }}</td>
                            <td>{{ payroll.reimbursement }}</td>
                            <td>{{ payroll.net_salary }}</td>
                            <td>{{ payroll.net_pay }}</td>
                            <td>{{ payroll.pay_date }}</td>
                            <td>{{ payroll.status }}</td>
                            <td>
                                <a href="{{ url_for('payroll_summary.generate_payslip_from_history', payroll_id=payroll.payroll_id) }}" class="btn-edit" title="Generate Payslip">
                                    <i class="fas fa-file-pdf"></i> Payslip
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/payroll-history-filters.js') }}"></script>
</body>
</html>
