<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Payroll Approval - {{ summary.period_display }}</h1>
                <div class="right-buttons-group">
                    <a class="btn-edit" href="{{ url_for('payroll_approval.bulk_payroll_approval')}}">
                        <i class="fi fi-rr-arrow-left"></i>Back to Bulk Approval
                    </a>
                    <a class="btn-edit" href="{{ url_for('payroll_approval.pending_payroll')}}">
                        <i class="fi fi-rr-list"></i>Individual Approvals
                    </a>
                </div>
            </div>
        </div>
        <div class="dyn_container">
            {% if payrolls and payrolls|length > 0 %}
                <!-- Summary Card -->
                <div class="summary-card">
                    <h3>Comprehensive Payroll Summary for {{ summary.period_display }}</h3>

                    <!-- Main Summary -->
                    <div class="summary-section">
                        <h4>Overview</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Total Employees:</span>
                                <span class="summary-value">{{ summary.total_employees }}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Gross Salary:</span>
                                <span class="summary-value gross">{{ "{:,.0f}".format(summary.total_gross_salary) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Deductions:</span>
                                <span class="summary-value deduction">{{ "{:,.0f}".format(summary.total_deductions) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Net To Pay:</span>
                                <span class="summary-value highlight">{{ "{:,.0f}".format(summary.total_net_to_pay) }} RWF</span>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Breakdown -->
                    <div class="summary-section">
                        <h4>Salary Components</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Basic Salary:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_basic_salary) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Transport Allowance:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_transport_allowance) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Housing Allowance:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_housing_allowance) }} RWF</span>
                            </div>
                        </div>
                    </div>

                    <!-- Deductions Breakdown -->
                    <div class="summary-section">
                        <h4>Deductions Breakdown</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Total Pension (ER + EE):</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_pension) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Maternity (ER + EE):</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_maternity) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total PAYE:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_paye) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total CBHI:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_cbhi) }} RWF</span>
                            </div>
                        </div>
                    </div>

                    <!-- Net Salary Adjustments -->
                    <div class="summary-section">
                        <h4>Net Salary Adjustments</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Net Salary (Before Adjustments):</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_net_salary) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Other Deductions:</span>
                                <span class="summary-value deduction">-{{ "{:,.0f}".format(summary.total_other_deductions) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Salary Advances:</span>
                                <span class="summary-value deduction">-{{ "{:,.0f}".format(summary.total_advances) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">BRD Deductions:</span>
                                <span class="summary-value deduction">-{{ "{:,.0f}".format(summary.total_brd_deductions) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Reimbursements:</span>
                                <span class="summary-value gross">+{{ "{:,.0f}".format(summary.total_reimbursements) }} RWF</span>
                            </div>
                            <div class="summary-item final-amount">
                                <span class="summary-label"><strong>Final Net To Pay:</strong></span>
                                <span class="summary-value highlight"><strong>{{ "{:,.0f}".format(summary.total_net_to_pay) }} RWF</strong></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Action Form -->
                <div class="bulk-action-card">
                    <h4>Bulk Actions</h4>
                    <form id="bulkApprovalForm" method="POST" action="{{ url_for('payroll_approval.bulk_approve_payroll') }}">
                        <input type="hidden" name="year" value="{{ year }}">
                        <input type="hidden" name="month" value="{{ month }}">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="remarks">Remarks (Optional):</label>
                                <textarea name="remarks" id="remarks" class="form-control" rows="3" 
                                         placeholder="Enter any remarks for this bulk approval..."></textarea>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button type="button" class="btn-approve" onclick="submitBulkAction('approve')">
                                <i class="fi fi-rr-check"></i> Approve All ({{ summary.total_employees }} payrolls)
                            </button>
                            <button type="button" class="btn-reject" onclick="submitBulkAction('reject')">
                                <i class="fi fi-rr-cross"></i> Reject All ({{ summary.total_employees }} payrolls)
                            </button>
                        </div>
                        
                        <input type="hidden" name="action" id="actionInput">
                    </form>
                </div>

                <!-- Payroll Details Table -->
                <div class="payroll-details">
                    <h4>Detailed Payroll Information</h4>
                    <div class="table-responsive">
                        <table id="payroll_table" class="detailed-payroll-table">
                            <thead>
                                <tr>
                                    <th rowspan="2">#</th>
                                    <th rowspan="2">Employee Name</th>
                                    <th rowspan="2">Job Title</th>
                                    <th colspan="4">Salary & Allowances</th>
                                    <th colspan="6">Deductions</th>
                                    <th colspan="7">Summary & Adjustments</th>
                                    <th rowspan="2">Pay Date</th>
                                </tr>
                                <tr>
                                    <!-- Salary & Allowances -->
                                    <th>Basic Salary</th>
                                    <th>Transport Allow.</th>
                                    <th>Housing Allow.</th>
                                    <th>Gross Salary</th>
                                    <!-- Deductions -->
                                    <th>ER Pension</th>
                                    <th>EE Pension</th>
                                    <th>ER Maternity</th>
                                    <th>EE Maternity</th>
                                    <th>PAYE</th>
                                    <th>CBHI</th>
                                    <!-- Summary -->
                                    <th>Total Deductions</th>
                                    <th>Net Salary</th>
                                    <th>Other Deductions</th>
                                    <th>Advances</th>
                                    <th>Reimbursements</th>
                                    <th>BRD Deductions</th>
                                    <th>Net To Pay</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payroll in payrolls %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td class="employee-name">{{ payroll.employee_name }}</td>
                                        <td>{{ payroll.job_title or 'N/A' }}</td>
                                        <!-- Salary & Allowances -->
                                        <td class="amount">{{ "{:,.0f}".format(payroll.basic_salary or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.transport_allowance or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.housing_allowance or 0) }}</td>
                                        <td class="amount gross">{{ "{:,.0f}".format(payroll.gross_salary or 0) }}</td>
                                        <!-- Deductions -->
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employer_pension or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employee_pension or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employer_maternity or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employee_maternity or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.payee or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.cbhi or 0) }}</td>
                                        <!-- Summary -->
                                        <td class="amount deduction">{{ "{:,.0f}".format(payroll.total_deductions or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.net_salary or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.other_deductions or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.advance or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.reimbursement or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.brd_deductions or 0) }}</td>
                                        <td class="amount net">{{ "{:,.0f}".format((payroll.net_salary or 0) - (payroll.other_deductions or 0) + (payroll.reimbursement or 0) - (payroll.brd_deductions or 0) - (payroll.advance or 0)) }}</td>
                                        <td>{{ payroll.pay_date.strftime('%d/%m/%Y') if payroll.pay_date else 'N/A' }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="totals-row">
                                    <td colspan="3"><strong>TOTALS</strong></td>
                                    <!-- Salary & Allowances Totals -->
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_basic_salary) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_transport_allowance) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_housing_allowance) }}</td>
                                    <td class="amount total gross">{{ "{:,.0f}".format(summary.total_gross_salary) }}</td>
                                    <!-- Deductions Totals -->
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employer_pension) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employee_pension) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employer_maternity) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employee_maternity) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_paye) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_cbhi) }}</td>
                                    <!-- Summary Totals -->
                                    <td class="amount total deduction">{{ "{:,.0f}".format(summary.total_deductions) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_net_salary) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_other_deductions) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_advances) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_reimbursements) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_brd_deductions) }}</td>
                                    <td class="amount total net">{{ "{:,.0f}".format(summary.total_net_to_pay) }}</td>
                                    <td><strong>{{ summary.total_employees }} employees</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-content">
                        <i class="fi fi-rr-check-circle big-icon"></i>
                        <h3>No Pending Payrolls</h3>
                        <p>There are no pending payrolls for {{ summary.period_display }}.</p>
                        <a href="{{ url_for('payroll_approval.bulk_payroll_approval') }}" class="btn-primary">
                            <i class="fi fi-rr-arrow-left"></i> Back to Bulk Approval
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modalTitle">Confirm Action</h4>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="modalMessage">Are you sure you want to perform this action?</p>
                <div class="modal-summary">
                    <p><strong>Period:</strong> {{ summary.period_display }}</p>
                    <p><strong>Total Payrolls:</strong> {{ summary.total_employees }}</p>
                    <p><strong>Total Net To Pay:</strong> {{ "{:,.0f}".format(summary.total_net_to_pay) }} RWF</p>
                    <p><strong>Total Gross Salary:</strong> {{ "{:,.0f}".format(summary.total_gross_salary) }} RWF</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="button" id="confirmButton" class="btn-primary" onclick="confirmAction()">Confirm</button>
            </div>
        </div>
    </div>

    <script>
        let currentAction = '';
        
        function submitBulkAction(action) {
            currentAction = action;
            const actionWord = action === 'approve' ? 'approve' : 'reject';
            const actionWordCap = action === 'approve' ? 'Approve' : 'Reject';
            
            document.getElementById('modalTitle').textContent = `Confirm ${actionWordCap} Action`;
            document.getElementById('modalMessage').textContent = 
                `Are you sure you want to ${actionWord} all {{ summary.total_employees }} payrolls for {{ summary.period_display }}?`;
            
            const confirmButton = document.getElementById('confirmButton');
            confirmButton.textContent = `${actionWordCap} All`;
            confirmButton.className = action === 'approve' ? 'btn-approve' : 'btn-reject';
            
            document.getElementById('confirmationModal').style.display = 'block';
        }
        
        function confirmAction() {
            document.getElementById('actionInput').value = currentAction;
            document.getElementById('bulkApprovalForm').submit();
        }
        
        function closeModal() {
            document.getElementById('confirmationModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('confirmationModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>

    <style>
        .summary-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card h3 {
            margin: 0 0 20px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .summary-section {
            margin-bottom: 25px;
        }

        .summary-section h4 {
            margin: 0 0 15px 0;
            color: #6c757d;
            font-size: 16px;
            font-weight: 600;
            border-left: 4px solid #17a2b8;
            padding-left: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .summary-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .summary-value {
            font-weight: bold;
            color: #495057;
        }
        
        .summary-value.highlight {
            color: #28a745;
            font-size: 1.1em;
            font-weight: 700;
        }

        .summary-value.gross {
            color: #155724;
            font-weight: 600;
        }

        .summary-value.deduction {
            color: #721c24;
            font-weight: 600;
        }

        .summary-item.final-amount {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            grid-column: 1 / -1;
        }

        .summary-item.final-amount .summary-label,
        .summary-item.final-amount .summary-value {
            font-size: 1.1em;
        }
        
        .bulk-action-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .bulk-action-card h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }
        
        .btn-approve {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: background 0.2s;
        }
        
        .btn-approve:hover {
            background: #218838;
        }
        
        .btn-reject {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: background 0.2s;
        }
        
        .btn-reject:hover {
            background: #c82333;
        }
        
        .payroll-details {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }

        .payroll-details h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }

        .table-responsive {
            overflow-x: auto;
            margin-top: 15px;
        }

        .detailed-payroll-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            min-width: 1400px;
        }

        .detailed-payroll-table th {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: center;
            font-weight: 600;
            color: #495057;
            white-space: nowrap;
            font-size: 11px;
        }

        .detailed-payroll-table th[colspan] {
            background: #e9ecef;
            font-weight: 700;
            color: #343a40;
        }

        .detailed-payroll-table td {
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: center;
            white-space: nowrap;
        }

        .detailed-payroll-table .employee-name {
            text-align: left;
            font-weight: 500;
            max-width: 120px;
            white-space: normal;
            word-wrap: break-word;
        }

        .detailed-payroll-table .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .detailed-payroll-table .amount.gross {
            background: #e8f5e8;
            font-weight: 600;
            color: #155724;
        }

        .detailed-payroll-table .amount.deduction {
            background: #f8d7da;
            color: #721c24;
        }

        .detailed-payroll-table .amount.net {
            background: #d1ecf1;
            font-weight: 600;
            color: #0c5460;
        }

        .detailed-payroll-table tbody tr:hover {
            background: #f5f5f5;
        }

        .detailed-payroll-table tbody tr:nth-child(even) {
            background: #fafafa;
        }

        .detailed-payroll-table tbody tr:nth-child(even):hover {
            background: #f0f0f0;
        }

        .detailed-payroll-table tfoot {
            background: #343a40;
            color: white;
        }

        .detailed-payroll-table .totals-row td {
            background: #343a40 !important;
            color: white !important;
            font-weight: 600;
            border-color: #495057;
        }

        .detailed-payroll-table .totals-row .amount.total {
            font-family: 'Courier New', monospace;
            font-weight: 700;
        }

        .detailed-payroll-table .totals-row .amount.gross {
            background: #28a745 !important;
        }

        .detailed-payroll-table .totals-row .amount.deduction {
            background: #dc3545 !important;
        }

        .detailed-payroll-table .totals-row .amount.net {
            background: #007bff !important;
        }
        
        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h4 {
            margin: 0;
            color: #495057;
        }
        
        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }
        
        .close:hover {
            color: #000;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .modal-summary p {
            margin: 5px 0;
        }
        
        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-content {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .big-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
{% endblock %}
