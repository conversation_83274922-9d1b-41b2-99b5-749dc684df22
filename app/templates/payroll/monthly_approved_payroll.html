<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Approved Payroll - {{ summary.period_display }}</h1>
                <div class="right-buttons-group">
                    <a class="btn-edit" href="{{ url_for('payroll_approval.approved_payroll_history')}}">
                        <i class="fi fi-rr-arrow-left"></i>Back to History
                    </a>
                    <a class="btn-edit" href="{{ url_for('payroll_approval.bulk_payroll_approval')}}">
                        <i class="fi fi-rr-clock"></i>Pending Approvals
                    </a>
                </div>
            </div>
        </div>
        <div class="dyn_container">
            {% if payrolls and payrolls|length > 0 %}
                <!-- Status Banner -->
                <div class="status-banner approved">
                    <div class="status-content">
                        <i class="fi fi-rr-check-circle status-icon"></i>
                        <div class="status-text">
                            <h3>Payroll Approved & Completed</h3>
                            <p>This payroll for {{ summary.period_display }} has been successfully approved and processed.</p>
                        </div>
                    </div>
                </div>

                <!-- Approval Summary -->
                {% if approval_summary %}
                <div class="approval-summary-card">
                    <h4>Approval Summary</h4>
                    <div class="approval-grid">
                        {% for approver_name, details in approval_summary.items() %}
                            <div class="approval-item">
                                <div class="approver-info">
                                    <i class="fi fi-rr-user-check approval-icon"></i>
                                    <div class="approver-details">
                                        <span class="approver-name">{{ approver_name }}</span>
                                        <span class="approver-role">{{ details.role }}</span>
                                    </div>
                                </div>
                                <div class="approval-stats">
                                    <span class="approval-count">{{ details.count }} payroll(s) approved</span>
                                    <span class="approval-date">{{ details.latest_date.strftime('%d/%m/%Y %H:%M') if details.latest_date else 'N/A' }}</span>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Summary Card -->
                <div class="summary-card">
                    <h3>Comprehensive Payroll Summary for {{ summary.period_display }}</h3>

                    <!-- Main Summary -->
                    <div class="summary-section">
                        <h4>Overview</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Total Employees:</span>
                                <span class="summary-value">{{ summary.total_employees }}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Gross Salary:</span>
                                <span class="summary-value gross">{{ "{:,.0f}".format(summary.total_gross_salary) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Deductions:</span>
                                <span class="summary-value deduction">{{ "{:,.0f}".format(summary.total_deductions) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Net To Pay:</span>
                                <span class="summary-value highlight">{{ "{:,.0f}".format(summary.total_net_to_pay) }} RWF</span>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Breakdown -->
                    <div class="summary-section">
                        <h4>Salary Components</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Basic Salary:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_basic_salary) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Transport Allowance:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_transport_allowance) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Housing Allowance:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_housing_allowance) }} RWF</span>
                            </div>
                        </div>
                    </div>

                    <!-- Deductions Breakdown -->
                    <div class="summary-section">
                        <h4>Deductions Breakdown</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Total Pension (ER + EE):</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_pension) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Maternity (ER + EE):</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_maternity) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total PAYE:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_paye) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total CBHI:</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_cbhi) }} RWF</span>
                            </div>
                        </div>
                    </div>

                    <!-- Net Salary Adjustments -->
                    <div class="summary-section">
                        <h4>Net Salary Adjustments</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Net Salary (Before Adjustments):</span>
                                <span class="summary-value">{{ "{:,.0f}".format(summary.total_net_salary) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Other Deductions:</span>
                                <span class="summary-value deduction">-{{ "{:,.0f}".format(summary.total_other_deductions) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Salary Advances:</span>
                                <span class="summary-value deduction">-{{ "{:,.0f}".format(summary.total_advances) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">BRD Deductions:</span>
                                <span class="summary-value deduction">-{{ "{:,.0f}".format(summary.total_brd_deductions) }} RWF</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Reimbursements:</span>
                                <span class="summary-value gross">+{{ "{:,.0f}".format(summary.total_reimbursements) }} RWF</span>
                            </div>
                            <div class="summary-item final-amount">
                                <span class="summary-label"><strong>Final Net To Pay:</strong></span>
                                <span class="summary-value highlight"><strong>{{ "{:,.0f}".format(summary.total_net_to_pay) }} RWF</strong></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payroll Details Table -->
                <div class="payroll-details">
                    <h4>Detailed Payroll Information</h4>
                    <div class="table-responsive">
                        <table id="payroll_table" class="detailed-payroll-table">
                            <thead>
                                <tr>
                                    <th rowspan="2">#</th>
                                    <th rowspan="2">Employee Name</th>
                                    <th rowspan="2">Job Title</th>
                                    <th colspan="4">Salary & Allowances</th>
                                    <th colspan="6">Deductions</th>
                                    <th colspan="7">Summary & Adjustments</th>
                                    <th colspan="2">Preparation Details</th>
                                    <th colspan="3">Approval Details</th>
                                    <th rowspan="2">Pay Date</th>
                                </tr>
                                <tr>
                                    <!-- Salary & Allowances -->
                                    <th>Basic Salary</th>
                                    <th>Transport Allow.</th>
                                    <th>Housing Allow.</th>
                                    <th>Gross Salary</th>
                                    <!-- Deductions -->
                                    <th>ER Pension</th>
                                    <th>EE Pension</th>
                                    <th>ER Maternity</th>
                                    <th>EE Maternity</th>
                                    <th>PAYE</th>
                                    <th>CBHI</th>
                                    <!-- Summary -->
                                    <th>Total Deductions</th>
                                    <th>Net Salary</th>
                                    <th>Other Deductions</th>
                                    <th>Advances</th>
                                    <th>Reimbursements</th>
                                    <th>BRD Deductions</th>
                                    <th>Net To Pay</th>
                                    <!-- Preparation Details -->
                                    <th>Prepared By</th>
                                    <th>Prepared Date</th>
                                    <!-- Approval Details -->
                                    <th>Approved By</th>
                                    <th>Approval Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payroll in payrolls %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td class="employee-name">{{ payroll.employee_name }}</td>
                                        <td>{{ payroll.job_title or 'N/A' }}</td>
                                        <!-- Salary & Allowances -->
                                        <td class="amount">{{ "{:,.0f}".format(payroll.basic_salary or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.transport_allowance or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.housing_allowance or 0) }}</td>
                                        <td class="amount gross">{{ "{:,.0f}".format(payroll.gross_salary or 0) }}</td>
                                        <!-- Deductions -->
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employer_pension or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employee_pension or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employer_maternity or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.employee_maternity or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.payee or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.cbhi or 0) }}</td>
                                        <!-- Summary -->
                                        <td class="amount deduction">{{ "{:,.0f}".format(payroll.total_deductions or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.net_salary or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.other_deductions or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.advance or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.reimbursement or 0) }}</td>
                                        <td class="amount">{{ "{:,.0f}".format(payroll.brd_deductions or 0) }}</td>
                                        <td class="amount net">{{ "{:,.0f}".format((payroll.net_salary or 0) - (payroll.other_deductions or 0) + (payroll.reimbursement or 0) - (payroll.brd_deductions or 0) - (payroll.advance or 0)) }}</td>
                                        <!-- Preparation Details -->
                                        <td class="prepared-cell">
                                            <span class="prepared-name">{{ payroll.prepared_by or 'Unknown' }}</span>
                                        </td>
                                        <td class="prepared-date">{{ payroll.prepared_date or 'N/A' }}</td>
                                        <!-- Approval Details -->
                                        <td class="approver-cell">
                                            {% if payroll.approval_history and payroll.approval_history|length > 0 %}
                                                {% for approval in payroll.approval_history %}
                                                    <div class="approver-info">
                                                        <span class="approver-name">{{ approval.approver_name }}</span>
                                                        <span class="approver-role">({{ approval.approver_role }})</span>
                                                        <span class="approval-date-inline">{{ approval.approval_date }}</span>
                                                    </div>
                                                    {% if not loop.last %}<hr class="approval-separator">{% endif %}
                                                {% endfor %}
                                            {% else %}
                                                <div class="approver-info">
                                                    <span class="approver-name">{{ payroll.approver_name }}</span>
                                                    <span class="approver-role">({{ payroll.approver_role }})</span>
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td class="approval-date">
                                            {% if payroll.approval_history and payroll.approval_history|length > 1 %}
                                                <span class="multiple-approvals">{{ payroll.approval_history|length }} approvals</span>
                                            {% else %}
                                                {{ payroll.approval_date }}
                                            {% endif %}
                                        </td>
                                        <td class="actions-cell">
                                            {% if payroll.approval_id %}
                                                <a href="{{ url_for('payroll_approval.payroll_approval_details', payroll_id=payroll.payroll_id) }}"
                                                   class="btn-view-details" title="View Approval Details">
                                                    <i class="fi fi-rr-eye"></i>
                                                </a>
                                            {% else %}
                                                <span class="no-details">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ payroll.pay_date.strftime('%d/%m/%Y') if payroll.pay_date else 'N/A' }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="totals-row">
                                    <td colspan="3"><strong>TOTALS</strong></td>
                                    <!-- Salary & Allowances Totals -->
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_basic_salary) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_transport_allowance) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_housing_allowance) }}</td>
                                    <td class="amount total gross">{{ "{:,.0f}".format(summary.total_gross_salary) }}</td>
                                    <!-- Deductions Totals -->
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employer_pension) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employee_pension) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employer_maternity) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_employee_maternity) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_paye) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_cbhi) }}</td>
                                    <!-- Summary Totals -->
                                    <td class="amount total deduction">{{ "{:,.0f}".format(summary.total_deductions) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_net_salary) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_other_deductions) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_advances) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_reimbursements) }}</td>
                                    <td class="amount total">{{ "{:,.0f}".format(summary.total_brd_deductions) }}</td>
                                    <td class="amount total net">{{ "{:,.0f}".format(summary.total_net_to_pay) }}</td>
                                    <!-- Preparation Details Totals -->
                                    <td colspan="2"><strong>All Prepared</strong></td>
                                    <!-- Approval Details Totals -->
                                    <td colspan="3"><strong>All Approved</strong></td>
                                    <td><strong>{{ summary.total_employees }} employees</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Export Actions -->
                <div class="export-actions-card">
                    <h4>Export Options</h4>
                    <div class="export-buttons">
                        <a href="{{ url_for('payroll_approval.export_approved_payroll_excel', year=selected_year, month=selected_month) }}"
                           class="btn-export excel-export" title="Export detailed payroll data to Excel">
                            <i class="fi fi-rr-file-excel"></i> Export to Excel
                        </a>
                        <!-- PDF Export - Commented out -->
                        {#
                        <a href="{{ url_for('payroll_approval.export_approved_payroll_pdf', year=selected_year, month=selected_month) }}"
                           class="btn-export pdf-export" title="Export payroll summary to PDF">
                            <i class="fi fi-rr-file-pdf"></i> Export to PDF
                        </a>
                        #}
                        <!-- Print Button - Commented out -->
                        {#
                        <button type="button" class="btn-export print-export" onclick="window.print()" title="Print this page">
                            <i class="fi fi-rr-print"></i> Print Summary
                        </button>
                        #}
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-content">
                        <i class="fi fi-rr-document big-icon"></i>
                        <h3>No Approved Payrolls</h3>
                        <p>There are no approved payrolls for {{ summary.period_display }}.</p>
                        <a href="{{ url_for('payroll_approval.approved_payroll_history') }}" class="btn-primary">
                            <i class="fi fi-rr-arrow-left"></i> Back to History
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <style>
        .status-banner {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        
        .status-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-icon {
            font-size: 32px;
            color: #28a745;
        }
        
        .status-text h3 {
            margin: 0 0 5px 0;
            color: #155724;
            font-size: 18px;
        }
        
        .status-text p {
            margin: 0;
            color: #155724;
            opacity: 0.8;
        }
        
        .summary-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card h3 {
            margin: 0 0 20px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .summary-section {
            margin-bottom: 25px;
        }

        .summary-section h4 {
            margin: 0 0 15px 0;
            color: #6c757d;
            font-size: 16px;
            font-weight: 600;
            border-left: 4px solid #17a2b8;
            padding-left: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .summary-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .summary-value {
            font-weight: bold;
            color: #495057;
        }
        
        .summary-value.highlight {
            color: #28a745;
            font-size: 1.1em;
            font-weight: 700;
        }

        .summary-value.gross {
            color: #155724;
            font-weight: 600;
        }

        .summary-value.deduction {
            color: #721c24;
            font-weight: 600;
        }

        .summary-item.final-amount {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            grid-column: 1 / -1;
        }

        .summary-item.final-amount .summary-label,
        .summary-item.final-amount .summary-value {
            font-size: 1.1em;
        }

        .approval-summary-card {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }

        .approval-summary-card h4 {
            margin: 0 0 15px 0;
            color: #155724;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .approval-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .approval-item {
            background: white;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .approver-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .approval-icon {
            font-size: 20px;
            color: #28a745;
        }

        .approver-details {
            display: flex;
            flex-direction: column;
        }

        .approver-name {
            font-weight: 600;
            color: #155724;
        }

        .approver-role {
            font-size: 12px;
            color: #6c757d;
            text-transform: capitalize;
        }

        .approval-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            text-align: right;
        }

        .approval-count {
            font-weight: 500;
            color: #155724;
            font-size: 14px;
        }

        .approval-date {
            font-size: 12px;
            color: #6c757d;
        }
        
        .export-actions-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .export-actions-card h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .export-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn-export {
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 14px;
            min-width: 140px;
            justify-content: center;
        }

        .excel-export {
            background: #28a745;
            color: white;
        }

        .excel-export:hover {
            background: #218838;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .pdf-export {
            background: #dc3545;
            color: white;
        }

        .pdf-export:hover {
            background: #c82333;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .print-export {
            background: #6c757d;
            color: white;
        }

        .print-export:hover {
            background: #545b62;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
        }

        .btn-export i {
            font-size: 16px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-content {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .big-icon {
            font-size: 64px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .empty-content h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .empty-content p {
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: background 0.2s;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }

        .payroll-details {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .payroll-details h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }

        .table-responsive {
            overflow-x: auto;
            margin-top: 15px;
        }

        .detailed-payroll-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            min-width: 1400px;
        }

        .detailed-payroll-table th {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: center;
            font-weight: 600;
            color: #495057;
            white-space: nowrap;
            font-size: 11px;
        }

        .detailed-payroll-table th[colspan] {
            background: #e9ecef;
            font-weight: 700;
            color: #343a40;
        }

        .detailed-payroll-table td {
            border: 1px solid #dee2e6;
            padding: 6px 4px;
            text-align: center;
            font-size: 11px;
        }

        .detailed-payroll-table .employee-name {
            text-align: left;
            font-weight: 500;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .detailed-payroll-table .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .detailed-payroll-table .amount.gross {
            background-color: #d4edda;
            color: #155724;
            font-weight: 600;
        }

        .detailed-payroll-table .amount.deduction {
            background-color: #f8d7da;
            color: #721c24;
            font-weight: 600;
        }

        .detailed-payroll-table .amount.net {
            background-color: #cce5ff;
            color: #004085;
            font-weight: 700;
        }

        .detailed-payroll-table .amount.total {
            background-color: #e2e3e5;
            font-weight: 700;
            color: #383d41;
        }

        .detailed-payroll-table .totals-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .detailed-payroll-table .totals-row td {
            border-top: 2px solid #495057;
            font-weight: 600;
        }

        .approver-cell {
            text-align: left !important;
            max-width: 120px;
        }

        .approver-cell .approver-info {
            display: flex;
            flex-direction: column;
        }

        .approver-cell .approver-name {
            font-weight: 500;
            color: #155724;
            font-size: 11px;
        }

        .approver-cell .approver-role {
            font-size: 10px;
            color: #6c757d;
            text-transform: capitalize;
        }

        .approval-date {
            font-size: 11px;
            color: #155724;
            font-weight: 500;
        }

        .actions-cell {
            text-align: center;
        }

        .btn-view-details {
            background: #17a2b8;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            text-decoration: none;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 3px;
            transition: background 0.2s;
        }

        .btn-view-details:hover {
            background: #138496;
            color: white;
            text-decoration: none;
        }

        .no-details {
            color: #6c757d;
            font-size: 11px;
            font-style: italic;
        }

        .prepared-cell {
            text-align: left !important;
            max-width: 100px;
        }

        .prepared-name {
            font-weight: 500;
            color: #007bff;
            font-size: 11px;
        }

        .prepared-date {
            font-size: 11px;
            color: #007bff;
            font-weight: 500;
        }

        .approval-separator {
            margin: 4px 0;
            border: none;
            border-top: 1px solid #e9ecef;
        }

        .approval-date-inline {
            display: block;
            font-size: 10px;
            color: #6c757d;
            margin-top: 2px;
        }

        .multiple-approvals {
            font-size: 11px;
            color: #28a745;
            font-weight: 600;
            background: #d4edda;
            padding: 2px 6px;
            border-radius: 3px;
        }

        @media (max-width: 768px) {
            .detailed-payroll-table {
                font-size: 10px;
            }

            .detailed-payroll-table th,
            .detailed-payroll-table td {
                padding: 4px 2px;
            }
        }
    </style>

    <script>
        // Add loading states for export buttons
        document.addEventListener('DOMContentLoaded', function() {
            const exportButtons = document.querySelectorAll('.excel-export, .pdf-export');

            exportButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fi fi-rr-spinner"></i> Generating...';
                    this.style.pointerEvents = 'none';

                    // Reset button after 3 seconds (in case of slow download)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.pointerEvents = 'auto';
                    }, 3000);
                });
            });
        });
    </script>
{% endblock %}
