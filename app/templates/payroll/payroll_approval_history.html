<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll Approval History</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/payroll.js') }}"></script>
</head>
<body>
    <div class="container">
        <h1>Payroll Approval History</h1>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Employee Name</th>
                    <th>Payroll Period</th>
                    <th>Status</th>
                    <th>Approved By</th>
                    <th>Approval Date</th>
                </tr>
            </thead>
            <tbody>
                {% for record in approval_history %}
                <tr>
                    <td>{{ record.employee_name }}</td>
                    <td>{{ record.payroll_period }}</td>
                    <td>{{ record.status }}</td>
                    <td>{{ record.approved_by }}</td>
                    <td>{{ record.approval_date }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if approval_history|length == 0 %}
        <p>No approval history available.</p>
        {% endif %}
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            // Additional JavaScript can be added here
        });
    </script>
</body>
</html>