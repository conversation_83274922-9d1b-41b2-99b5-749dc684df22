<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Save Payroll</h1>
            <a class="btn-edit template-link" href="{{ url_for('payroll_summary_v2.payroll_summary') }}">
                <i class="fi fi-rr-calculator"></i> Payroll
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="confirmation-container">
            <div class="pay-date-info">
                <h3>Payroll Save Confirmation</h3>
                <div class="info-card">
                    <div class="info-item">
                        <span class="info-label">Pay Date:</span>
                        <span class="info-value">{{ pay_date.strftime('%d/%m/%Y') if pay_date else 'Not Set' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Pay Period:</span>
                        <span class="info-value">{{ pay_date.strftime('%B %Y') if pay_date else 'Not Set' }}</span>
                    </div>
                </div>
                <p class="confirmation-text">
                    You are about to save all generated payroll data for <strong>{{ pay_date.strftime('%B %Y') if pay_date else 'the selected period' }}</strong>.
                    This will create payroll records for all employees with the pay date of <strong>{{ pay_date.strftime('%d/%m/%Y') if pay_date else 'Not Set' }}</strong>.
                </p>
            </div>

            <div class="action-buttons">
                <form method="POST" action="{{ url_for('payroll_summary_v2.add_payroll') }}" style="display: inline;">
                    <button type="submit" class="submit-btn confirm-btn">
                        <i class="fi fi-rr-check"></i> Confirm & Save Payroll
                    </button>
                </form>
                <a href="{{ url_for('payroll_summary_v2.payroll_summary') }}" class="cancel-btn">
                    <i class="fi fi-rr-cross"></i> Cancel
                </a>
            </div>
        </div>
    </div>

    <style>
        .confirmation-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 30px;
        }

        .pay-date-info h3 {
            color: #495057;
            margin-bottom: 20px;
            text-align: center;
        }

        .info-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
        }

        .info-value {
            font-weight: 700;
            color: #007bff;
            font-size: 1.1em;
        }

        .confirmation-text {
            text-align: center;
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            align-items: center;
        }

        .confirm-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background 0.2s;
        }

        .confirm-btn:hover {
            background: #218838;
        }

        .cancel-btn {
            background: #6c757d;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background 0.2s;
        }

        .cancel-btn:hover {
            background: #545b62;
            color: white;
            text-decoration: none;
        }
    </style>
</div>
{% endblock %}