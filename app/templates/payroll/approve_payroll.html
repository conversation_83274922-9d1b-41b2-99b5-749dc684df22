<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Approve Payroll</h1>
                <a class="btn-edit" href="{{ url_for('payroll_approval.pending_payroll')}}">
                    <i class="fi fi-rr-list"></i>Pending payrolls
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <form  method="POST">
                    {{ form.hidden_tag() }}
                    {{ form.csrf_token }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.status.label }}
                        {{ form.status(class='form-row') }}
                    </div>
                    <div class="form-group">
                        {{ form.remarks.label }}
                        {{ form.remarks(class="form-control") }}
                    </div>
                </div>
                    <button type="submit" class="submit-btn">Submit</button>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
