<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Approved Payroll History</h1>
                <div class="right-buttons-group">
                    <a class="btn-edit" href="{{ url_for('payroll_approval.bulk_payroll_approval')}}">
                        <i class="fi fi-rr-clock"></i>Pending Approvals
                    </a>
                    <a class="btn-edit" href="{{ url_for('payroll_summary_v2.payroll_summary') }}" class="template-link">
                        <i class="fi fi-rr-calculator-money"></i> Generate Payroll
                    </a>
                </div>
            </div>
        </div>
        <div class="dyn_container">
            {% if available_periods and available_periods|length > 0 %}
                <div class="info-card">
                    <h3>Select a Month/Year to View Approved Payrolls</h3>
                    <p>Choose a period below to view detailed information about all approved payrolls for that month.</p>
                </div>
                
                <div class="periods-grid">
                    {% for period in available_periods %}
                        <div class="period-card approved">
                            <div class="period-header">
                                <h3>{{ period.period_display }}</h3>
                                <span class="payroll-count approved">{{ period.payroll_count }} approved</span>
                            </div>
                            <div class="period-details">
                                <p><strong>Month:</strong> {{ period.month_name }}</p>
                                <p><strong>Year:</strong> {{ period.year }}</p>
                                <p><strong>Approved Payrolls:</strong> {{ period.payroll_count }}</p>
                                <p><strong>Status:</strong> <span class="status-approved">✓ Completed</span></p>
                            </div>
                            <div class="period-actions">
                                <a href="{{ url_for('payroll_approval.monthly_approved_payroll', year=period.year, month=period.month) }}" 
                                   class="btn-primary">
                                    <i class="fi fi-rr-eye"></i> View Details
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-content">
                        <i class="fi fi-rr-document big-icon"></i>
                        <h3>No Approved Payrolls</h3>
                        <p>There are currently no approved payrolls in the system.</p>
                        <div class="empty-actions">
                            <a href="{{ url_for('payroll_approval.bulk_payroll_approval') }}" class="btn-primary">
                                <i class="fi fi-rr-clock"></i> View Pending Approvals
                            </a>
                            <a href="{{ url_for('payroll_summary_v2.payroll_summary') }}" class="btn-secondary">
                                <i class="fi fi-rr-calculator-money"></i> Generate Payroll
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <style>
        .info-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .info-card p {
            margin: 0;
            color: #6c757d;
        }
        
        .periods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .period-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .period-card.approved {
            border-left: 4px solid #28a745;
        }
        
        .period-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .period-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .period-header h3 {
            margin: 0;
            color: #495057;
        }
        
        .payroll-count {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .payroll-count.approved {
            background: #28a745;
            color: white;
        }
        
        .period-details p {
            margin: 5px 0;
            color: #6c757d;
        }
        
        .status-approved {
            color: #28a745;
            font-weight: bold;
        }
        
        .period-actions {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: background 0.2s;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-left: 10px;
            transition: background 0.2s;
        }
        
        .btn-secondary:hover {
            background: #545b62;
            color: white;
            text-decoration: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-content {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .big-icon {
            font-size: 64px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .empty-content h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .empty-content p {
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .empty-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }
    </style>
{% endblock %}
