<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll Summary</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i>back
        </a>
        <div class="right-buttons-group">
            <button class="btn-edit" id="open-payroll-popup" onclick="displayPayrollPopup()">
                <i class="fi fi-rr-process"></i>
                </span>Payroll</button>
            <button class="btn-edit" id="open-popup" onclick="displayPopup()">
                <i class="fi fi-rr-download"></i>
                </span>Export</button>
            <div class="right-btns">
                <div class="the-dots btn-edit">
                    <p>More</p>
                    <i class="fi fi-rr-angle-small-down" id="action-dots"></i>
                </div>
                <div class="action--links action--except" id="action-links">
                    <ul>
                        <li>
                            <a class="template-link" href="#" data-template-url="{{ url_for('payroll_summary.add_payroll') }}" class="save-btn">
                                <span class="material-symbols-outlined icon">add</span>
                                <p>Add Payroll</p>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('payroll_summary.payroll_summary_text') }}" class="validate-btn">
                                <span class="material-symbols-outlined icon">download</span>
                                <p>Unified Permanent EE TXT</p>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('payroll_summary.payroll_summary_excel') }}" class="delete-btn">
                                <span class="material-symbols-outlined icon">download</span>
                                <p>Payroll Summary Excel</p>
                            </a>
                        </li>
                        <li>
                            <a class="template-link" data-template-url="{{ url_for('employees.employee_payslip') }}" class="delete-btn">
                                <span class="material-symbols-outlined icon">download</span>
                                <p>All Payslips</p>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('payroll_summary.download_payroll_payments') }}" class="delete-btn">
                                <span class="material-symbols-outlined icon">download</span>
                                <p>Download Payroll Payments</p>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('payroll_summary.download_journal_entry') }}" class="delete-btn">
                                <span class="material-symbols-outlined icon">download</span>
                                <p>Download Journal Entry</p>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('quickbooks.post_journal_entry') }}" class="delete-btn">
                                <span class="material-symbols-outlined icon">download</span>
                                <p>Post Journal Entry</p>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div>
            <h1>Payroll Summary for {{ pay_date.strftime('%B %Y') }}</h1>
            <!-- Flash messages -->
            <div class="container mt-4">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <ul class="list-unstyled">
                        {% for category, message in messages %}
                            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                            </li>
                        {% endfor %}
                        </ul>
                    {% endif %}
                {% endwith %}
            </div>
            </div>
                <div class="mypopup" id="payroll-popup">
                    <div class="popup-content">
                        <h2>Compute Payroll</h2>
                        <form method="POST">
                            {{ form.csrf_token }}
                            <div class="form-row">
                                <div class="form-group">
                                    {{ form.pay_date.label }}
                                    <div class="input-group-text">
                                        <span class="material-symbols-outlined icon">date_range</span>
                                        {{ form.pay_date(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                            {% if attendance_service %}
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="attendance_service">Do you consider Time Attendance ?</label>
                                    <!-- Timesheet Applicable Select Field -->
                                    <div class="input-group-text">
                                        <i class="fi fi-rr-alarm-clock"></i>
                                        <select name="timesheet_applicable" class="form-control">
                                            <option value="">Choose your answer</option>
                                            <option value="yes">Yes</option>
                                            <option value="no">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <div class="form-group">
                                {{ form.submit(class="btn btn-continue") }}
                            </div>

                        </form>

                        <button id="close-payroll-popup" class="btn-cancel" onclick="closePayrollPopup()">Close</button>

                    </div>
                </div>

            <div class="large--table">
                <div class="table-custom">
                    <table class="table table-striped table-bordered" id="payroll_summ">
                        <thead class="thead th-custom">
                            <tr>
                                <th scope="col">No</th>
                                <th scope="col">First Name</th>
                                <th scope="col">Last Name</th>
                                <th scope="col">Title</th>
                                <th scope="col">Employee Type</th>
                                <th scope="col">Basic Salary</th>
                                <th scope="col">Trans. Allowance</th>
                                <th scope="col">Other Allowances</th>
                                <th scope="col">Gross Salary</th>
                                <th scope="col">PAYE</th>
                                <th scope="col">Pension EE</th>
                                <th scope="col">Pension ER</th>
                                <th scope="col">Total Pension</th>
                                <th scope="col">Maternity EE</th>
                                <th scope="col">Maternity ER</th>
                                <th scope="col">Total Maternity</th>
                                <th scope="col">Medicare EE</th>
                                <th scope="col">T. Deductions</th>
                                <th scope="col">Net BCBHI</th>
                                <th scope="col">CBHI</th>
                                <th scope="col">Net CBHI</th>
                                <th scope="col">Other Deductions From Net</th>
                                <th scope="'col">Advances</th>
                                <th scope="col">Reimbusements</th>
                                <th scope="col">BRD Deductions</th>
                                <th scope="col">Net Salary</th>
                                <th scope="col">Net To Pay</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for emp_data in employees_with_deductions %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ emp_data.employee.first_name }}</td>
                                    <td>{{ emp_data.employee.last_name }}</td>
                                    <td>{{ emp_data.employee.job_title }}</td>
                                    <td>{{ emp_data.employee.employee_type }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.basic_needed) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.employee.transport_allowance) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.employee.allowances) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.gross_needed) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.paye) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.pension_ee_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.pension_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.pension_ee_value + emp_data.pension_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.maternity_ee_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.maternity_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.maternity_ee_value + emp_data.maternity_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.rama_ee) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.total_deductions_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_bcbhi) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.cbhi_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_cbhi) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.total_deductions) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.salary_advance) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.total_reimbursements) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.brd_deduction) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_salary_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_salary_value - emp_data.total_deductions + emp_data.total_reimbursements - emp_data.brd_deduction - emp_data.salary_advance)  }}</td>
                                    <td>
                                        <div class="table-buttons">
                                            <a class="template-link btn-image" href="#" data-template-url="{{ url_for('employees.update_employee', employee_id=emp_data.employee.employee_id) }}"><i class="fi fi-rr-pencil"></i> Edit</a>
                                            <a target="_blank" class="template-link btn-image" href="#" data-template-url="{{ url_for('employees.single_employee_payslip', employee_id=emp_data.employee.employee_id) }}" class="btn btn-info"><i class="fi fi-rr-legal"></i> Payslip</a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="5">Totals:</td>
                                <td>{{ Auxillary.format_amount(total_basic_salary | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_transport_allowance | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount( total_other_allowances | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_gross | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_paye | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_pension_ee | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_pension_er | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_pension | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_maternity_ee | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_maternity_er | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_maternity | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_rama | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_payroll_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_nbcbhi | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_cbhi_value | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_net_cbhi | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_advances | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_reimbursements | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_brd_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_net_salary | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_net_salary_after_deductions | default(0)) }}</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="mypopup" id="popup">
                <div class="popup-content">
                    <h2>Export Payroll Summary</h2>
                    <p>Choose the format you want to export the payroll summary in:</p>
                    <ul class="listed-links">
                        <li><a href="{{ url_for('payroll_summary.download_payroll_summary') }}" class="btn btn-success">Download Payroll summary Excel</a></li>
                        <li><a href="{{ url_for('payroll_summary.payroll_summary_excel') }}" class="btn btn-success">Download Unified Payee Excel</a></li>
                        <li><a href="{{ url_for('payroll_summary.payroll_summary_text') }}" class="btn btn-success">Download Unified Payee TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.maternity_anneex_excel') }}" class="btn btn-success">Download Maternity Annexture Excel</a></li>
                        <li><a href="{{ url_for('payroll_summary.maternity_anneex_txt') }}" class="btn btn-success">Download Maternity Annexture TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.pension_anneex_excel') }}" class="btn btn-success">Download Pension Annexture Excel</a></li>
                        <li><a href="{{ url_for('payroll_summary.pension_anneex_txt') }}" class="btn btn-success">Download Pension Annexture TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.pension_anneex_ver30') }}" class="btn btn-success">Download Pension excel 3.0</a></li>
                        <li><a href="{{ url_for('payroll_summary.pension_anneex_ver3') }}" class="btn btn-success">Download Pension txt 3.0</a></li>
                        <li><a href="{{ url_for('payroll_summary.cbhi_anneex_excel') }}" class="btn btn-success">Download CBHI Annexture Excel</a></li>
                        <li><a href="{{ url_for('payroll_summary.cbhi_anneex_txt') }}" class="btn btn-success">Download CBHI Annexture TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.paye_anneex_excel') }}" class="btn btn-success">Download PAYE Annexture Excel</a></li>
                        <li><a href="{{ url_for('payroll_summary.paye_permanent_anneex_txt') }}" class="btn btn-success">Download PAYE Permanent Annexture TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.paye_casual_anneex_txt') }}" class="btn btn-success">Download PAYE Casual Annexture TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.paye_second_employee_anneex_txt') }}" class="btn btn-success">Download PAYE Second Employee Annexture TXT</a></li>
                        <li><a href="{{ url_for('payroll_summary.ishema') }}" class="btn btn-success">Download Ishema Template</Template></a></li>
                    </ul>
                <button id="close-button" class="btn-cancel" onclick="closePopup()">Close</button>
            </div>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/popups.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
</body>
</html>
