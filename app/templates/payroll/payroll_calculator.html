{% extends 'layouts/home.html' %}
{% block content %}
<title>Payroll Calculator</title>
    <div class="form-container">
        <h2>Payroll Calculator</h2>
        <p>Fill in the form below to Compute The Payroll</p>
        <form action="{{ url_for('payroll.payroll_calculator') }}" method="post">
            {{ form.csrf_token() }}
            <div class="selective--div">
                <legend>Employee Information</legend>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="employee_name" >Employee Name <span class="text-danger">*</span></label>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">person</span>
                            <input class="form-control" id="employee_name" name="employee_name" placeholder="eg: <PERSON>nan<PERSON>" required type="text" value="" style="font-style: italic;">
                        </div>
                        {% for error in form.employee_name.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        {{ form.employee_type.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">wc</span>
                            <select class="form-control" id="employee_type" name="employee_type" required, onchange="showContent()">
                            <option value="permanent">Permanent Employee</option>
                            <option value="consultant">Consultant (Man-power)</option>
                            <option value="casual">Casual Employee</option>
                            <option value="second_employee">Second Employee</option>
                        </select>
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.id_number.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">badge</span>
                            {{ form.id_number(class="form-control") }}
                        </div>
                        {% for error in form.id_number.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <span class="form-item">
                        {{ form.day_one.label(class="form-label") }} <span class="text-danger"> *</span>
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">calendar_today</span>
                            {{ form.day_one(class="form-control") }}
                        </div>
                        {% for error in form.day_one.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        <span class="form-item">
                        {{ form.pay_date.label(class="form-label") }} <span class="text-danger"> *</span>
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">calendar_today</span>
                            {{ form.pay_date(class="form-control") }}
                        </div>
                        {% for error in form.pay_date.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        <span class="form-item">
                            {{ form.net_salary.label(class="form-label") }} (ex: 100000) <span class="text-danger"> *</span>
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">money</span>
                            <input class="form-control" id="net_salary" name="net_salary" 
                            placeholder="Agreed Net Salary after taxes" style="font-style: italic;">
                        </div>
                        {% for error in form.net_salary.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        
                        {{ form.rssb_number.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">badge</span>
                            {{ form.rssb_number(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.address.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">home</span>
                            {{ form.address(class="form-control") }}
                        </div>
                        {% for error in form.address.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        {{ form.department.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">business</span>
                            {{ form.department(class="form-control") }}
                        </div>
                        {% for error in form.department.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.position.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">work</span>
                            {{ form.position(class="form-control") }}
                        </div>
                        {% for error in form.position.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        {{ form.bank_name.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">account_balance</span>
                            {{ form.bank_name(class="form-control") }}
                        </div>
                        {% for error in form.bank_name.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        {{ form.bank_account.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">account_balance</span>
                            {{ form.bank_account(class="form-control") }}
                        </div>
                        {% for error in form.bank_account.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.bank_branch.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">account_balance</span>
                            {{ form.bank_branch(class="form-control") }}
                        </div>
                        {% for error in form.bank_branch.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        {{ form.account_number.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">account_balance</span>
                            {{ form.account_number(class="form-control") }}
                        </div>
                        {% for error in form.account_number.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        <span class="form-item">
                            {{ form.joining_date.label() }} <span class="text-danger"> *</span>
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">calendar_today</span>
                            {{ form.joining_date(class="form-control") }}
                        </div>
                        {% for error in form.joining_date.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <span class="form-item">
                            {{ form.deductions.label(class="form-label") }} (ex: Salary Advance, etc)
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">money</span>
                            {{ form.deductions(class="form-control") }}
                        </div>
                        {% for error in form.deductions.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            <div id="other-content" class="general-content">
                <div class="form-row">
                    
                    <div class="form-group">
                        {{ form.transport_allowance.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">directions_car</span>
                            {{ form.transport_allowance(class="form-control") }}
                        </div>
                            {% for error in form.transport_allowance.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        <span class="form-item">
                            {{ form.other_allowances.label(class="form-label") }} (Summation) 
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">money</span>
                            {{ form.other_allowances(class="form-control") }}
                        </div>
                        {% for error in form.other_allowances.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        <span class="form-item">
                            {{ form.medical_insurance.label(class="form-label") }} (ex: RAMA: 7.5)
                        </span>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">local_hospital</span>
                            {{ form.medical_insurance(class="form-control") }}
                        </div>
                        {% for error in form.medical_insurance.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
                <div class="form-row">
                    
                    <div class="form-group">
                        {{ form.email.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">email</span>
                            {{ form.email(class="form-control") }}
                        </div>
                                              {% for error in form.email.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                    <div class="form-group">
                        {{ form.phone.label(class="form-label") }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">phone</span>
                            {{ form.phone(class="form-control") }}
                        </div>
                        {% for error in form.phone.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="selective--div">
                <legend>Company Information</legend>
                <div class="form-row">                
                    <div class="form-group">
                        {{ form.company_name.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">business</span>
                            {{ form.company_name(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.company_tin.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">badge</span>
                            {{ form.company_tin(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.company_rssb.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">badge</span>
                            {{ form.company_rssb(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.company_address.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">home</span>
                            {{ form.company_address(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.company_phone.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">phone</span>
                            {{ form.company_phone(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.company_email.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">email</span>
                            {{ form.company_email(class="form-control") }}
                        </div>
                    </div>

                    
                </div>
            </div>
            <div id="consultant" class="general-content">
                <div class="form-row">
                </div>
            </div>
            <div class="form-group">
                {{ form.submit(class="btn btn-custom") }}
            </div>
        </form>
    </div>
{% endblock %}
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="{{ url_for('static', filename='js/tooltip.js') }}"></script>
    <script src="{{ url_for('static', filename='js/hide_show.js') }}"></script>

