<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Pending Payroll (Waiting for approval)</h1>
                <div class="right-buttons-group">
                    <a href="{{ url_for('payroll_approval.bulk_payroll_approval') }}" class="btn-edit">
                        <i class="fi fi-rr-check-double"></i> Bulk Approval
                    </a>
                    <a href="{{ url_for('payroll_approval.approved_payroll_history') }}" class="btn-edit">
                        <i class="fi fi-rr-check-circle"></i> Approved History
                    </a>
                    <a href="{{ url_for('payroll_summary_v2.payroll_summary') }}" class="template-link btn-edit">
                        <i class="fi fi-rr-calculator-money"></i> Payroll Summary
                    </a>
                </div>
            </div>
        </div>
        <div class="dyn_container">
            
            <div class="large--table">
                <table id="payroll_history">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>B.Salary</th>
                            <th>TR.Allowance</th>
                            <th>H.Allowance</th>
                            <th>G.Salary</th>
                            <th>ER Pension</th>
                            <th>EE Pension</th>
                            <th>Total Pension</th>
                            <th>ER Maternity</th>
                            <th>EE Maternity</th>
                            <th>Total Maternity</th>
                            <th>PAYEE</th>
                            <th>CBHI</th>
                            <th>Total Deductions</th>
                            <th>Other Deductions</th>
                            <th>BRD Deductions</th>
                            <th>Advances</th>
                            <th>Reimbursements</th>
                            <th>Net Salary</th>
                            <th>Net Pay</th>
                            <th>Payroll Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if payrolls and payrolls|length > 0 %}
                        {% for payroll in payrolls %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ payroll.employee_name }}</td>
                                <td>{{ payroll.basic_salary }}</td>
                                <td>{{ payroll.transport_allowance }}</td>
                                <td>{{ payroll.house_allowance }}</td>
                                <td>{{ payroll.gross_salary }}</td>
                                <td>{{ payroll.employer_pension }}</td>
                                <td>{{ payroll.employee_pension }}</td>
                                <td>{{ payroll.total_pension }}</td>
                                <td>{{ payroll.employer_maternity }}</td>
                                <td>{{ payroll.employee_maternity }}</td>
                                <td>{{ payroll.total_maternity }}</td>
                                <td>{{ payroll.payee }}</td>
                                <td>{{ payroll.cbhi }}</td>
                                <td>{{ payroll.total_deductions }}</td>
                                <td>{{ payroll.other_deductions }}</td>
                                <td>{{ payroll.brd_deductions }}</td>
                                <td>{{ payroll.advance }}</td>
                                <td>{{ payroll.reimbursement }}</td>
                                <td>{{ payroll.net_salary }}</td>
                                <td>{{ payroll.net_pay }}</td>
                                <td>{{ payroll.pay_date }}</td>
                                <td>{{ payroll.status }}</td>
                                <td>
                                    <a class="blue icon" href="{{ url_for('payroll_approval.approve_payroll', payroll_id=payroll.payroll_id) }}">
                                        <i class="fi fi-rr-dot-pending"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                        {% else %}
                        <tr style="text-align: center">
                            <td colspan="24">
                                <div class="empty-state-inline">
                                    <h3 class="mid-dark">No pending payrolls</h3>
                                    <i class="fi fi-rr-drawer-empty mid-dark big"></i>
                                    <p class="light">Please process the payroll and save it for approval <a href="{{ url_for('payroll_summary_v2.payroll_summary') }}" class="blue bold">here</a></p>
                                    <p class="light">Or use <a href="{{ url_for('payroll_approval.bulk_payroll_approval') }}" class="blue bold">bulk approval</a> for faster processing</p>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            
        </div>
    </div>
    </div>

    <style>
        .right-buttons-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .empty-state-inline {
            padding: 40px 20px;
        }

        .empty-state-inline h3 {
            margin-bottom: 15px;
        }

        .empty-state-inline .big {
            font-size: 48px;
            margin: 20px 0;
        }

        .empty-state-inline p {
            margin: 10px 0;
        }
    </style>

{% endblock %}