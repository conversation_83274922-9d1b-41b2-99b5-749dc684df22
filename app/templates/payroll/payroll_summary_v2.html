<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Payroll Summary for {{ pay_date.strftime('%B %Y') }}</h1>
                <div class="right-buttons-group">
                    <button class="primary-button" id="open-payroll-popup" onclick="displayPayrollPopup()">
                        <i class="fi fi-rr-calculator-money"></i>
                        Payroll
                    </button>
                    <button class="green-button" id="open-popup" onclick="displayPopup()">
                        <i class="fi fi-rr-download"></i>
                        Export
                    </button>
                    <button class="primary-button" onclick="actionPopup()">
                        <i class="fi fi-rr-angle-small-down"></i>
                        more
                    </button>
                </div>
            </div>
            <div class="filter-container">
                <div class="page_rows">
                    <label for="rowsPerPageInput" >Show</label>
                    <select id="rowsPerPageInput">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
                <div>
                    <label for="payrollSearchInput">Search:</label>
                    <input type="text" id="payrollSearchInput" placeholder="Search employees..." class="form-control">
                </div>
                <button class="toggle-filters-btn primary-button" id="payrollToggleFiltersBtn">Show Filters</button>
                <div class="advanced-filters" id="payrollAdvancedFilters">
                        <div class="form-group">
                            <label for="employeeTypeFilter">Employment Type:</label>
                            <select id="employeeTypeFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="departmentFilter">Department:</label>
                            <select id="departmentFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="jobTitleFilter">Job title:</label>
                            <select id="jobTitleFilter" class="form-select">
                                <option value="">All</option>
                            </select>
                        </div>
                </div>
            </div>
        </div>
        <div class="dyn_container">
                <div class="mypopup right" id="action-popup">
                    <div class="popup-content">
                        <div class="grey-container">
                        <h1>Payroll Actions</h1>
                            <p>Choose the action you want to perform:</p>
                        </div>
                        <ul class="two-columns">
                            <li>
                                <div class="icon">
                                    <a class="template-link" href="{{ url_for('payroll_summary_v2.add_payroll') }}">
                                        <p>Save Payroll</p>
                                        <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/folder-file.png" alt="Payroll Icon">
                                    </a>
                                </div>
                            </li>
                            <li>
                                <div class="icon">
                                    <a class="template-link" href="{{ url_for('my_employees.employee_payslip') }}">
                                        <p>All Payslips</p>
                                        <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/payslips.png" alt="Payslip Icon">
                                    </a>
                                </div>
                            </li>
                            <li>
                                <div class="icon">
                                    <a href="{{ url_for('quickbooks.post_journal_entry') }}">
                                        <p>Post Journal</p>
                                        <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/Quickbooks_ec1d84d76b.png" alt="Payslip Icon">
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="mypopup" id="payroll-popup">
                    <div class="popup-content">
                        <div class="heading grey-container">
                            <h1>Compute Payroll</h1>
                            <ol>
                                <li>Select the <strong>Pay Date</strong> for which you want to compute the payroll.</li>
                                <li>If applicable, select whether you consider Time Attendance.</li>
                                <li>Click on the <strong>Generate</strong> button to compute the payroll.</li>
                            </ol>
                        </div>
                        <form method="POST">
                            {{ form.csrf_token }}
                            <div class="form-row">
                                <div class="form-group">
                                    {{ form.pay_date.label }}
                                    <div class="input-group-text">
                                        {{ form.pay_date(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                            {% if attendance_service %}
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="attendance_service">Do you consider Time Attendance ?</label>
                                    <!-- Timesheet Applicable Select Field -->
                                    <div class="input-group-text">
                                        <select name="timesheet_applicable" class="form-select">
                                            <option value="">Choose your answer</option>
                                            <option value="yes">Yes</option>
                                            <option value="no">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            <div class="form-buttons">
                                <button type="submit" class="btn submit-btn blue-button">Generate</button>
                                <button type="button" class="btn grey-button" onclick="closePayrollPopup()">Cancel</button>
                            </div>
                        </form>

                        <button id="close-payroll-popup" class="red red-box close" onclick="closePayrollPopup()">&times;</button>

                    </div>
                </div>

                <div class="table-custom">
                    <table class="large-table" id="payroll_summary">
                        <thead class="thead th-custom">
                            <tr>
                                <th scope="col">No</th>
                                <th scope="col">First Name</th>
                                <th scope="col">Last Name</th>
                                <th scope="col">Category</th>
                                <th scope="col">Employee Type</th>
                                <th scope="col">Department</th>
                                <th scope="col">Basic Salary</th>
                                <th scope="col">Trans. Allowance</th>
                                <th scope="col">Other Allowances</th>
                                <th scope="col">Gross Salary</th>
                                <th scope="col">PAYE</th>
                                <th scope="col">Pension EE</th>
                                <th scope="col">Pension ER</th>
                                <th scope="col">Total Pension</th>
                                <th scope="col">Maternity EE</th>
                                <th scope="col">Maternity ER</th>
                                <th scope="col">Total Maternity</th>
                                <th scope="col">Medicare EE</th>
                                <th scope="col">Medicare ER</th>
                                <th scope="col">Total Medicare</th>
                                <th scope="col">T. Deductions</th>
                                <th scope="col">Net BCBHI</th>
                                <th scope="col">CBHI</th>
                                <th scope="col">Net CBHI</th>
                                <th scope="col">Other Deductions From Net</th>
                                <th scope="'col">Advances</th>
                                <th scope="col">Reimbusements</th>
                                <th scope="col">BRD Deductions</th>
                                <th scope="col">Net Salary</th>
                                <th scope="col">Net To Pay</th>
                                <th scope="col">Total Staff Cost</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for emp_data in employees_with_deductions %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ emp_data.employee.first_name }}</td>
                                    <td>{{ emp_data.employee.last_name }}</td>
                                    <td>{{ emp_data.employee.job_title }}</td>
                                    <td>{{ emp_data.employee.employee_type }}</td>
                                    <td>{% if emp_data.employee.department %}  {{ emp_data.employee.department }} {%else%} N/A {% endif %} </td>
                                    <td>{{ Auxillary.format_amount(emp_data.basic_needed) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.employee.transport_allowance) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.employee.allowances) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.gross_needed) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.paye) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.pension_ee_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.pension_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.pension_ee_value + emp_data.pension_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.maternity_ee_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.maternity_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.maternity_ee_value + emp_data.maternity_er_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.rama_ee) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.rama_er | default(0)) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.rama_ee + (emp_data.rama_er | default(0))) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.total_deductions_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_bcbhi) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.cbhi_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_cbhi) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.total_deductions) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.salary_advance) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.total_reimbursements) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.brd_deduction) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_salary_value) }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.net_salary_value - emp_data.total_deductions + emp_data.total_reimbursements - emp_data.brd_deduction - emp_data.salary_advance)  }}</td>
                                    <td>{{ Auxillary.format_amount(emp_data.gross_needed + emp_data.rama_er + emp_data.pension_er_value + emp_data.maternity_er_value) }}</td>
                                
                                    <!-- Actions -->
                                    <td>
                                        <div class="table-buttons">
                                            <a class="green icon" href="{{ url_for('my_employees.update_employee', employee_id=emp_data.employee.employee_id) }}"><i class="fi fi-rr-edit"></i></a>
                                            <a class="orange icon" href="{{ url_for('my_employees.single_employee_payslip', employee_id=emp_data.employee.employee_id) }}"><i class="fi fi-rr-legal"></i> Payslip</a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        
                        
                            <tr style="font-weight: bold;">
                                <td colspan="6"></td>
                                <td>{{ Auxillary.format_amount(total_basic_salary | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_transport_allowance | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount( total_other_allowances | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_gross | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_paye | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_pension_ee | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_pension_er | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_pension | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_maternity_ee | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_maternity_er | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_maternity | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_rama_ee | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_rama_er | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_rama | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_payroll_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_nbcbhi | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_cbhi_value | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_net_cbhi | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_advances | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_reimbursements | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_brd_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_net_salary | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_net_salary_after_deductions | default(0)) }}</td>
                                <td>{{ Auxillary.format_amount(total_staff_cost | default(0)) }}</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <div id="payrollPagination" class="pagination"></div>
            </div>
            <div class="mypopup" id="popup">
                <div class="popup-content">
                    <div class="grey-container">
                        <h1>Download Payroll Summary</h1>
                        <p>Choose the format in which you want to export the payroll summary in:</p>
                        
                        <p style="font-size: 10!important; font-weight: 300;"><span><i class="fi fi-rr-triangle-warning orange"></i> </span> Note: The payroll summary exported does't respect the filters.</p>

                        <div class="icons">
                            <div class="icon">
                                <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/excel_file.png" alt="Excel Icon">
                                <p>Excel</p>
                            </div>
                            <div class="icon">
                                <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/txt-file.png" alt="Text Icon">
                                <p>Text</p>
                            </div>
                        </div>
                    </div>
                        <ul class="two-columns">
                            <div class="listed-links">
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.download_journal_entry') }}" class="green green-box">
                                        <span>Journal Entries</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.download_payroll_summary') }}" class="green green-box"><span>Payroll summary Excel</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.payroll_summary_excel') }}" class="green green-box"><span>Unified Payee Excel</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.maternity_anneex_excel') }}" class="green green-box"><span>Maternity Annexture Excel</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.pension_anneex_excel') }}" class="green green-box"><span>Pension Annexture Excel</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.pension_anneex_ver30') }}" class="green green-box" ><span>Pension excel 3.0</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.cbhi_anneex_excel') }}" class="green green-box"><span>CBHI Annexture Excel</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.paye_anneex_excel') }}" class="green green-box"><span>PAYE Annexture Excel</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.ishema') }}" class="green green-box"><span>Ishema Template</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.download_payroll_payments') }}" class="green green-box">

                                        <span>Payroll Payments</span>
                                    </a>
                                </li>

                            </div>
                            <div class="listed-links">
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.pension_anneex_ver3') }}" class="blue blue-box"><span>Pension txt 3.0</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.paye_permanent_anneex_txt') }}" class="blue blue-box"><span>PAYE Permanent Annexture TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.paye_casual_anneex_txt') }}" class="blue blue-box"><span>PAYE Casual Annexture TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.cbhi_anneex_txt') }}" class="blue blue-box"><span>CBHI Annexture TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.payroll_summary_text') }}" class="blue blue-box"><span>Unified Payee TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.paye_second_employee_anneex_txt') }}" class="blue blue-box"><span>PAYE Second Employee Annexture TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.pension_anneex_txt') }}" class="blue blue-box"><span>Pension Annexture TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.maternity_anneex_txt') }}" class="blue blue-box"><span>Maternity Annexture TXT</span></a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.payroll_summary_text') }}" class="blue blue-box">
                                        <span>Unified Permanent EE TXT</span>
                                    </a>
                                </li>
                            </div>
                        </ul>

                <button id="close-button" class="red red-box top-right" onclick="closePopup()">&times;</button>
            </div>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/popups.js') }}"></script>

{% endblock %}
