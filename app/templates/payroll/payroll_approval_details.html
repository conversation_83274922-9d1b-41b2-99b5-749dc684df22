<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Payroll Approval Details</h1>
                <div class="right-buttons-group">
                    <a class="btn-edit" href="javascript:history.back()">
                        <i class="fi fi-rr-arrow-left"></i>Back
                    </a>
                    <a class="btn-edit" href="{{ url_for('payroll_approval.approved_payroll_history')}}">
                        <i class="fi fi-rr-list"></i>Approved History
                    </a>
                </div>
            </div>
        </div>
        <div class="dyn_container">
            <!-- Payroll Information -->
            <div class="payroll-info-card">
                <h3>Payroll Information</h3>
                <div class="payroll-details-grid">
                    <div class="detail-item">
                        <span class="detail-label">Employee:</span>
                        <span class="detail-value">{{ payroll.employee_name }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Job Title:</span>
                        <span class="detail-value">{{ payroll.job_title or 'N/A' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Pay Date:</span>
                        <span class="detail-value">{{ payroll.pay_date.strftime('%d/%m/%Y') if payroll.pay_date else 'N/A' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Gross Salary:</span>
                        <span class="detail-value highlight">{{ "{:,.0f}".format(payroll.gross_salary or 0) }} RWF</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Net Salary:</span>
                        <span class="detail-value highlight">{{ "{:,.0f}".format(payroll.net_salary or 0) }} RWF</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value status-approved">{{ payroll.status }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Prepared By:</span>
                        <span class="detail-value">{{ payroll.prepared_by or 'Unknown' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Prepared Date:</span>
                        <span class="detail-value">{{ payroll.prepared_date or 'N/A' }}</span>
                    </div>
                </div>
            </div>

            <!-- Approval Timeline -->
            <div class="approval-timeline-card">
                <h3>Approval Timeline</h3>
                {% if approval_history and approval_history|length > 0 %}
                    <div class="timeline">
                        {% for approval in approval_history %}
                            <div class="timeline-item {{ 'approved' if approval.status == 'Approved' else 'rejected' if approval.status == 'Rejected' else 'pending' }}">
                                <div class="timeline-marker">
                                    {% if approval.status == 'Approved' %}
                                        <i class="fi fi-rr-check-circle"></i>
                                    {% elif approval.status == 'Rejected' %}
                                        <i class="fi fi-rr-cross-circle"></i>
                                    {% else %}
                                        <i class="fi fi-rr-clock"></i>
                                    {% endif %}
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <h4>{{ approval.status }} by {{ approval.approver_name }}</h4>
                                        <span class="timeline-date">{{ approval.created_at.strftime('%d/%m/%Y %H:%M') if approval.created_at else 'N/A' }}</span>
                                    </div>
                                    <div class="timeline-details">
                                        <p><strong>Role:</strong> {{ approval.approver_role }}</p>
                                        <p><strong>Remarks:</strong> {{ approval.remarks }}</p>
                                        {% if approval.updated_at and approval.updated_at != approval.created_at %}
                                            <p><strong>Last Updated:</strong> {{ approval.updated_at.strftime('%d/%m/%Y %H:%M') }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="no-approvals">
                        <i class="fi fi-rr-document big-icon"></i>
                        <h4>No Approval Records</h4>
                        <p>No detailed approval records found for this payroll.</p>
                    </div>
                {% endif %}
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons-card">
                <div class="action-buttons">
                    <button type="button" class="btn-export" onclick="printDetails()">
                        <i class="fi fi-rr-print"></i> Print Details
                    </button>
                    <button type="button" class="btn-export" onclick="exportDetails()">
                        <i class="fi fi-rr-file-excel"></i> Export Details
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .payroll-info-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .payroll-info-card h3 {
            margin: 0 0 20px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .payroll-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .detail-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .detail-value {
            font-weight: bold;
            color: #495057;
        }
        
        .detail-value.highlight {
            color: #28a745;
            font-size: 1.1em;
        }
        
        .status-approved {
            color: #28a745;
            background: #d4edda;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .approval-timeline-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .approval-timeline-card h3 {
            margin: 0 0 20px 0;
            color: #495057;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 5px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .timeline-item.approved .timeline-marker {
            background: #28a745;
        }
        
        .timeline-item.rejected .timeline-marker {
            background: #dc3545;
        }
        
        .timeline-item.pending .timeline-marker {
            background: #ffc107;
            color: #212529;
        }
        
        .timeline-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .timeline-header h4 {
            margin: 0;
            color: #495057;
            font-size: 16px;
        }
        
        .timeline-date {
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
        }
        
        .timeline-details p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        
        .no-approvals {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .no-approvals .big-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .action-buttons-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn-export {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: background 0.2s;
        }
        
        .btn-export:hover {
            background: #138496;
        }
    </style>

    <script>
        function printDetails() {
            window.print();
        }
        
        function exportDetails() {
            // Placeholder for export functionality
            alert('Export functionality would be implemented here');
        }
    </script>
{% endblock %}
