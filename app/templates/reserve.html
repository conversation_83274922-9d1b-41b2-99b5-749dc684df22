<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register Employees</title>
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='styles/auth_forms.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
</head>
<body>
    <div class="container big-container">
        <!--Flash messages-->
        <div class="row justify-content-center">
            <div class="col-md-12">
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-md-12 mt-3 real-form">
                <form method="POST" action="/register_employees" class="form-horizontal">
                    {{ form.csrf_token }}
                    <h4 class="text-center">Register Employee</h4>
                    <fieldset class="border p-2">
                        <legend class="w-auto">Personal Information</legend>
                        <div class="form-row">
                            <!-- First Name -->
                            <div class="form-group col-md-4">
                                <label for="first_name" class="col-sm-6 col-form-label">First Name</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">person</span>
                                        </span>
                                        <input id="first_name" name="first_name" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                            <!-- Last Name -->
                            <div class="form-group col-md-4">
                                <label for="last_name" class="col-sm-6 col-form-label">Last Name</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">person</span>
                                        </span>
                                        <input id="last_name" name="last_name" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                            <!-- NID -->
                            <div class="form-group col-md-4">
                                <label for="nid" class="col-sm-6 col-form-label">NID</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">badge</span>
                                        </span>
                                        <input id="nid" name="nid" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <!-- RSSB Number -->
                            <div class="form-group col-md-4">
                                <label for="rssb_number" class="col-sm-6 col-form-label">RSSB Number</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">assignment_ind</span>
                                        </span>
                                        <input id="rssb_number" name="rssb_number" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="rssb_number" class="col-sm-6 col-form-label">Date of Hire</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">Event</span>
                                        </span>
                                        <input id="hire_date" name="hire_date" required type="date" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <!--Demographic information-->
                    <fieldset class="border p-2">
                        <legend class="w-auto">Demographic Information</legend>
                        <div class="form-row">
                            <!-- Birth Date -->
                            <div class="form-group col-md-4">
                                <label for="birth_date" class="col-sm-6 col-form-label">Birth Date</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">calendar_today</span>
                                        </span>
                                        <input id="birth_date" name="birth_date" required type="date" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                            <!-- Marital Status -->
                            <div class="form-group col-md-4">
                                <label for="marital_status" class="col-sm-6 col-form-label">Marital Status</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">family_restroom</span>
                                        </span>
                                        <select id="marital_status" name="marital_status" required class="form-control">
                                            <option value="single">Single</option>
                                            <option value="married">Married</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Gender -->
                            <div class="form-group col-md-4">
                                <label for="gender" class="col-sm-6 col-form-label">Gender</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">wc</span>
                                        </span>
                                        <select id="gender" name="gender" required class="form-control">
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <!--Employment information-->
                    <fieldset class="border p-2">
                        <legend class="w-auto">Employment Information</legend>
                        <div class="form-row">
                            <!--Job Tittle-->
                            <div class="form-group col-md-3">
                                <label for="employee_type" class="col-sm-6 col-form-label">Job Title</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">handyman</span>
                                        </span>
                                        <input id="job_title" name="job_title" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>

                            <!-- Employee TIN -->
                            <div class="form-group col-md-3">
                                <label for="employee_tin" class="col-sm-6 col-form-label">Employee TIN</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">receipt_long</span>
                                        </span>
                                        <input id="employee_tin" name="employee_tin" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                            <!-- Employee Type -->
                            <div class="form-group col-md-3">
                                <label for="employee_type" class="col-sm-8 col-form-label">Employment Type</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">work</span>
                                        </span>
                                        <select id="employee_type" name="employee_type" required class="form-control">
                                            <option value="permanent">Permanent</option>
                                            <option value="casual">Casual</option>
                                            <option value="second_employee">Second Employee</option>
                                            <option value="consultant">Consultant</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- Net Salary -->
                            <div class="form-group col-md-3">
                                <label for="net_salary" class="col-sm-6 col-form-label">Net Salary</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">payments</span>
                                        </span>
                                        <input id="net_salary" name="net_salary" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <!--Allowances-->
                    <fieldset class="border p-2">
                        <legend class="w-auto">Allowances</legend>
                        <div class="form-row">
                            <!-- Transport Allowance -->
                            <div class="form-group col-md-3">
                                <label for="transport_allowance" class="col-sm-8 col-form-label">Transport Allowance</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">directions_bus</span>
                                        </span>
                                        <input id="transport_allowance" name="transport_allowance" type="text" class="form-control" value="0.0">
                                    </div>
                                </div>
                            </div>
                            <!-- Housing Allowance -->
                            <div class="form-group col-md-3">
                                <label for="housing_allowance" class="col-sm-8 col-form-label">Housing Allowance</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">roofing</span>
                                        </span>
                                        <input id="housing_allowance" name="housing_allowance" type="number" class="form-control" value="0.0">
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Communication Allowance -->
                            <div class="form-group col-md-3">
                                <label for="communication_allowance" class="col-sm-10 col-form-label">Communication Allowance</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">phone_iphone</span>
                                        </span>
                                        <input id="communication_allowance" name="communication_allowance" type="text" class="form-control" value="0.0">
                                    </div>
                                </div>
                            </div>
                            <!-- Other Allowance -->
                            <div class="form-group col-md-3">
                                <label for="communication_allowance" class="col-sm-8 col-form-label">Other Allowances</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">more_horiz</span>
                                        </span>
                                        <input id="communication_allowance" name="communication_allowance" type="number" class="form-control" value="0.0">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="communication_allowance" class="col-sm-8 col-form-label">Over-Time</label>
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">schedule</span>
                                        </span>
                                        <input id="communication_allowance" name="communication_allowance" type="number" class="form-control" value="0.0">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset class="border p-2">
                        <legend>Contact information</legend>
                        <div class="form-row">
                            <!-- Phone Number -->
                            <div class="form-group">
                                <label for="phone_number" class="col-sm-6 col-form-label">Phone Number</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">phone</span>
                                        </span>
                                        <input id="phone_number" name="phone_number" required type="text" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                            <!-- Email Address -->
                            <div class="form-group">
                                <label for="email" class="col-sm-6 col-form-label">Email Address</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <span class="material-symbols-outlined icon">email</span>
                                        </span>
                                        <input id="email" name="email" required type="email" class="form-control" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <div class="form-group text-center">
                        <div class="col-sm-12 mt-3">
                            <button type="submit" class="btn btn-custom">Register Employee</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
