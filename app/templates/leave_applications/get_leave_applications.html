<!DOCTYPE html>
<html>
<head>
    <title>View Leave Applications</title>
</head>
<body>
    <h1>View Leave Applications</h1>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="container">
        <table id="leave-application" class="table">
            <thead>
                <tr>
                    <th>Employee Name</th>
                    <th>Leave Type</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Reason</th>
                    <th>Application Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for leave_application in leave_applications %}
                    <tr>
                        <td>{{ leave_application.employee_name.upper() }}</td>
                        <td>{{ leave_application.leave_type }}</td>
                        <td>{{ leave_application.time_off_begin_date }}</td>
                        <td>{{ leave_application.time_off_end_date }}</td>
                        <td>{{ leave_application.reason }}</td>
                        <td>{{ leave_application.created_at }}</td>
                        <td>{{ leave_application.status }}</td>
                        <td>
                            <a  class="template-link btn-image" href="#" data-template-url="{{ url_for('leave_applications.approve_leave_application', leave_application_id=leave_application.leave_id) }}">
                                <i class="fi fi-rr-checkbox"></i>
                                <p>Approve</p>
                            </a>
                        </td>                   
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
    </body>
</html>
