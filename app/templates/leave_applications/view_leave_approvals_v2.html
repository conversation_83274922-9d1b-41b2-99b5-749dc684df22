<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leave Approvals</title>
</head>
<body>
    <div>
        <div>
            <div>
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <ul>
                        {% for category, message in messages %}
                            <li>{{ message }}</li>
                        {% endfor %}
                        </ul>
                    {% endif %}
                {% endwith %}
            </div>

            <div>
                <h2>Leave Approvals</h2>
                <a href="{{ url_for('leave_applications_v2.get_leave_application_for_employees') }}">Back to Leave Applications</a>

                <table border="1">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Leave Type</th>
                            <th>Approver</th>
                            <th>Approver Role</th>
                            <th>Decision</th>
                            <th>Remarks</th>
                            <th>Approval Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for approval in leave_approvals %}
                        <tr>
                            <td>{{ approval.employee_name }}</td>
                            <td>{{ approval.leave_type }}</td>
                            <td>{{ approval.approver_id }}</td>
                            <td>{{ approval.approver_role }}</td>
                            <td>{{ approval.approval }}</td>
                            <td>{{ approval.remarks }}</td>
                            <td>{{ approval.created_at }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
