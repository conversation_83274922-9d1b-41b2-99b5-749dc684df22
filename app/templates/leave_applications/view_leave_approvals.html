<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <title>View Leave Approvals</title>
</head>
<body>
    <h1>Leave Approvals</h1>
    <!--FLash messages-->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="container">
        <div>
            <div>
                <table id="leave-approvals" class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Employee</th>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Approved By</th>
                            <th>Approver Role</th>
                            <th>Status</th>
                            <th>Remarks</th>
                            <th>Approval Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave_application in leave_approvals %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    {{ leave_application['employee_name'].upper() }}
                                </td>
                                <td>{{ leave_application.leave_type }}</td>
                                <td>{{ leave_application.time_off_begin_date }}</td>
                                <td>{{ leave_application.time_off_end_date }}</td>
                                <td>{{  LeaveApproval.get_approver_name(leave_application.approver_id, leave_application.approver_role, db_session) }}
                                </td>
                                <td>{% if leave_application.approver_role %}
                                    {{ leave_application.approver_role.upper() }}
                                    {% else %}
                                    
                                    {% endif %}
                                </td>
                                <td>{{ leave_application.status }}</td>
                                <td>{{ leave_application.remarks }}</td>
                                <td>{{ leave_application.created_at }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>    
                </table>
            </div>
        </div>
    </div>
    
    <script>
        $(document).ready(function() {
            // check if the table exists
            if (!$.fn.DataTable.isDataTable('#leave-approvals')) {
                $('#leave-approvals').DataTable({
                    "paging": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "pageLength": 10
                    });
            }
        });
    </script>
</body>
</html>