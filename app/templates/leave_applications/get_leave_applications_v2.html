<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Leave Applications</h1>
                <a class="template-link btn-edit" href="{{ url_for('attendance_v2.record_leave_or_off')}}">
                    <i class="fi fi-rr-plus-small"></i> Leave/Off
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="table-container">
                <h2>All Employee Leave Applications</h2>
                
                <table id="leaveTable" class="display">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Applied On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leave_applications %}
                        <tr>
                            <td>{{ leave.employee_name }}</td>
                            <td>{{ leave.leave_type }}</td>
                            <td>{{ leave.time_off_begin_date }}</td>
                            <td>{{ leave.time_off_end_date }}</td>
                            <td>{{ leave.reason }}</td>
                            <td class="status-{{ leave.status.lower() }}">{{ leave.status }}</td>
                            <td>{{ leave.created_at }}</td>
                            <td>
                                {% if leave.status == 'pending' %}
                                <a href="{{ url_for('leave_applications_v2.approve_leave_application', leave_application_id=leave.leave_id) }}" class="blue">Review</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}