<!DOCTYPE html>
<html>
<head>
    <title>View Leave Applications</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
</head>
<body>
    <h1>View Leave Applications</h1>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="container">
        <div>
            <div>
                <table id="leave-application" class="table">
                    <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Reason</th>
                            <th>Application Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for leave_application in leave_applications %}
                        <tr>
                            <td>{{ leave_application.leave_type }}</td>
                            <td>{{ leave_application.time_off_begin_date }}</td>
                            <td>{{ leave_application.time_off_end_date }}</td>
                            <td>{{ leave_application.reason }}</td>
                            <td>{{ leave_application.created_at }}</td>
                            <td>{{ leave_application.status }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            // check if the table exists
            if (!$.fn.DataTable.isDataTable('#leave-application')) {
                $('#leave-application').DataTable({
                    "paging": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "pageLength": 10
                    });
            }
        });
    </script>
</body>
</html>