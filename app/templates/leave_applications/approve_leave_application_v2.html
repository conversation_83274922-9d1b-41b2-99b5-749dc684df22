<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Review leave application</h1>
                <a class="template-link btn-edit" href="{{ url_for('leave_applications_v2.get_leave_application_for_employees') }}">
                    <i class="fi fi-rr-list"></i> Leave Applications
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="">
                
                    
                    <div class="leave-details">
                        <h3>Leave Application Details</h3>
                        <p><strong>Employee:</strong> {{ leave_application.employee_name }}</p>
                        <p><strong>Leave Type:</strong> {{ leave_application.leave_type }}</p>
                        <p><strong>Start Date:</strong> {{ leave_application.time_off_begin_date }}</p>
                        <p><strong>End Date:</strong> {{ leave_application.time_off_end_date }}</p>
                        <p><strong>Reason:</strong> {{ leave_application.reason }}</p>
                        <p><strong>Status:</strong> {{ leave_application.status }}</p>
                        <p><strong>Applied On:</strong> {{ leave_application.created_at }}</p>
                    </div>
                <div class="form--container">
                    <form method="POST" action="{{ url_for('leave_applications_v2.approve_leave_application', leave_application_id=leave_application.leave_id) }}">
                        {{ form.csrf_token }}
                        <div class="form-row">
                            <div class="form-group">
                                <label for="approval">Decision</label>
                                {{ form.approval(class="form-control") }}
                                {% if form.approval.errors %}
                                    <div class="error-message">
                                        {% for error in form.approval.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="remarks">Remarks</label>
                                {{ form.remarks(class="form-control", rows=4) }}
                                {% if form.remarks.errors %}
                                    <div class="error-message">
                                        {% for error in form.remarks.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <button type="submit" class="submit-btn">Submit</button>
                            <a href="{{ url_for('leave_applications_v2.get_leave_application_for_employees') }}" class="red red-box">Cancel</a>
                        </div>
                    </form>
                </div>
                
        </div> 
    </div>
    </div>
{% endblock %}