<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='images/system_images/Favicon_s.png') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Leave Applications</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/popups.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <style>
        .table-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
        .status-approved {
            color: #28a745;
            font-weight: bold;
        }
        .status-rejected {
            color: #dc3545;
            font-weight: bold;
        }
        .btn-apply {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        .btn-apply:hover {
            background-color: #0069d9;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar-container no-print">
            <img src="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}" alt="netpipo logo" class="d-logo">
            <div class="sidebar">
                <div class="side-buttons">
                    <p>Menu</p>
                    <ul>
                        <li class="">
                            <a href="/employee_dashboard">
                                <i class="fi fi-rr-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="header no-print">
            <div class="company-info no-print">
                <i class="fi fi-rr-building"></i>
                <p>{{company}}</p>
                <i class="fi fi-rr-angle-small-down"></i>
            </div>
            <div class="search-bar text-box no-print">
                <input type="text" placeholder="Search" id="search-input" aria-label="Search">
                <i class="fi fi-rr-search"></i>
                <div class="search-results" id="search-results" style="display: none;"></div>
            </div>
            <div class="employee-info">
                <div class="user--info">
                    <i class="fi fi-rr-user icon primary"></i>
                    <p>{{ first_name }} {{ last_name }}, </p>
                    <div class="user--role">
                        <div class="role">
                            <p>{{ role }}</p>
                        </div>
                    </div>
                    <div class="user--profile">
                        <i class="fi fi-rr-angle-small-down user-profile-toggle" aria-expanded="false" aria-controls="user-profile-menu"></i>
                        <ul class="user-profile-menu" style="display: none;">
                            <li>
                                <a class="template-link" href="{{ url_for('user_data.update_profile') }}">
                                    <span class="material-symbols-outlined">manage_accounts</span>
                                    <span>Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('user_data.logout') }}">
                                    <span class="material-symbols-outlined">logout</span>
                                    <span>Logout</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="notification">
                    <i class="fi fi-rr-bell"></i>
                    <div class="red-dot"></div>
                </div>
            </div>
        </div>
        <div class="dynamic-container">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <ul class="list-unstyled">
                        {% for category, message in messages %}
                            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                            </li>
                        {% endfor %}
                        </ul>
                    {% endif %}
                {% endwith %}
            </div>
            
            <div class="table-container">
                <h2>My Leave Applications</h2>
                <a href="{{ url_for('leave_applications_v2.apply_for_leave') }}" class="btn-apply">Apply for Leave</a>
                
                <table id="leaveTable" class="display">
                    <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Applied On</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leave_applications %}
                        <tr>
                            <td>{{ leave.leave_type }}</td>
                            <td>{{ leave.start_date }}</td>
                            <td>{{ leave.end_date }}</td>
                            <td>{{ leave.reason }}</td>
                            <td class="status-{{ leave.status.lower() }}">{{ leave.status }}</td>
                            <td>{{ leave.created_at }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="footer no-print">
            <p>Netpipo &copy; 2025</p>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#leaveTable').DataTable({
                "order": [[5, "desc"]], // Sort by applied date (newest first)
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
