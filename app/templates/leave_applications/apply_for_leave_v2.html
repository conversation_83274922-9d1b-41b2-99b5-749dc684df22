<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='images/system_images/Favicon_s.png') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply for Leave</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/popups.css') }}">
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <style>
        .form-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .error-message {
            color: red;
            font-size: 14px;
            margin-top: 5px;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar-container no-print">
            <img src="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}" alt="netpipo logo" class="d-logo">
            <div class="sidebar">
                <div class="side-buttons">
                    <p>Menu</p>
                    <ul>
                        <li class="">
                            <a href="/employee_dashboard">
                                <i class="fi fi-rr-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="header no-print">
            <div class="company-info no-print">
                <i class="fi fi-rr-building"></i>
                <p>{{company}}</p>
                <i class="fi fi-rr-angle-small-down"></i>
            </div>
            <div class="search-bar text-box no-print">
                <input type="text" placeholder="Search" id="search-input" aria-label="Search">
                <i class="fi fi-rr-search"></i>
                <div class="search-results" id="search-results" style="display: none;"></div>
            </div>
            <div class="employee-info">
                <div class="user--info">
                    <i class="fi fi-rr-user icon primary"></i>
                    <p>{{ first_name }} {{ last_name }}, </p>
                    <div class="user--role">
                        <div class="role">
                            <p>{{ role }}</p>
                        </div>
                    </div>
                    <div class="user--profile">
                        <i class="fi fi-rr-angle-small-down user-profile-toggle" aria-expanded="false" aria-controls="user-profile-menu"></i>
                        <ul class="user-profile-menu" style="display: none;">
                            <li>
                                <a class="template-link" href="{{ url_for('user_data.update_profile') }}">
                                    <span class="material-symbols-outlined">manage_accounts</span>
                                    <span>Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('user_data.logout') }}">
                                    <span class="material-symbols-outlined">logout</span>
                                    <span>Logout</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="notification">
                    <i class="fi fi-rr-bell"></i>
                    <div class="red-dot"></div>
                </div>
            </div>
        </div>
        <div class="dynamic-container">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <ul class="list-unstyled">
                        {% for category, message in messages %}
                            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                            </li>
                        {% endfor %}
                        </ul>
                    {% endif %}
                {% endwith %}
            </div>
            
            <div class="form-container">
                <h2>Apply for Leave</h2>
                <form method="POST" action="{{ url_for('leave_applications_v2.apply_for_leave') }}">
                    {{ form.csrf_token }}
                    <div class="form-group">
                        <label for="leave_type">Leave Type</label>
                        {{ form.leave_type(class="form-control") }}
                        {% if form.leave_type.errors %}
                            <div class="error-message">
                                {% for error in form.leave_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label for="start_date">Start Date</label>
                        {{ form.start_date(class="form-control", type="date") }}
                        {% if form.start_date.errors %}
                            <div class="error-message">
                                {% for error in form.start_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label for="end_date">End Date</label>
                        {{ form.end_date(class="form-control", type="date") }}
                        {% if form.end_date.errors %}
                            <div class="error-message">
                                {% for error in form.end_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label for="reason">Reason</label>
                        {{ form.reason(class="form-control", rows=4) }}
                        {% if form.reason.errors %}
                            <div class="error-message">
                                {% for error in form.reason.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn-primary">Submit</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="footer no-print">
            <p>Netpipo &copy; 2025</p>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
</body>
</html>
