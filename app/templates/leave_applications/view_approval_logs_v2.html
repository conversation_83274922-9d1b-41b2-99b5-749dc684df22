<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Approved Leave applications</h1>
                <a href="{{ url_for('leave_applications_v2.get_leave_application_for_employees') }}" class="btn-edit">
                    <i class="fi fi-rr-list"></i> Leave Applications
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h2>Approval Logs</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Leave ID</th>
                            <th>Employee</th>
                            <th>Leave Type</th>
                            <th>Period</th>
                            <th>Approver Role</th>
                            <th>Approver Name</th>
                            <th>Decision</th>
                            <th>Timestamp</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for group in grouped_logs %}
                            {% for approver in group.approvers %}
                                <tr>
                                    {% if loop.first %}
                                        <td rowspan="{{ group.approvers|length }}">
                                            <a href="{{ url_for('leave_applications_v2.approve_leave_application', leave_application_id=group.application_id) }}" title="View Leave Application Details" class="leave-id-link">
                                                {{ group.display_id }}
                                            </a>
                                        </td>
                                        <td rowspan="{{ group.approvers|length }}">{{ group.employee_name }}</td>
                                        <td rowspan="{{ group.approvers|length }}">{{ group.leave_type }}</td>
                                        <td rowspan="{{ group.approvers|length }}">{{ group.start_date }} - {{ group.end_date }}</td>
                                    {% endif %}
                                    <td>{{ approver.approver_role }}</td>
                                    <td>{{ approver.approver_name }}</td>
                                    <td 
                                        {% if approver.status == 'approved' %} class="green" {% elif approver.status == 'rejected' %} class="red" {% else %} class="orange" {% endif %}>
                                        {{ approver.status }}
                                    </td>
                                        
                                    
                                    <td>{{ approver.created_at }}</td>
                                </tr>
                            {% endfor %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}