<!DOCTYPE html>
<html>
<head>
    <title>Approve Leave Application</title>
</head>
<body>
    <div class="real-form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <h1>Approve Leave Application</h1>
        <form  method="POST">
            {{ form.csrf_token }}
            <div class="form-row">
                <div class="form-group">
                    {{ form.approval.label }} {{ form.approval(class="form-control") }}
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    {{ form.remarks.label }} 
                    <div class="input-group-text">
                        {{ form.remarks }}
                    </div>
                </div>
            </div>
            <button type="submit" class="btn-edit">Submit</button>
        </form>
    </div>
</body>

</html>