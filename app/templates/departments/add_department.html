<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/tables.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <title>Add Department</title>
</head>
<body>
        <div class="dynamic--buttons">
            <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('departments.departments') }}">
                <i class="fas fa-arrow-left"></i> back
            </a>
            <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('departments.departments') }}">
                <i class="fas fa-list"></i> Departments
            </a>
        </div>
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul class="list-unstyled">
            {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </li>
            {% endfor %}
            </ul>
            {% endif %}
        {% endwith %}
        <div class="real-form">

        <h1>Add Department</h1>
        <div class="real--form">
            <form class="particular--form" action="{{ url_for('departments.add_department') }}" method="POST">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        <label for="department_name">Department Name</label>
                        <div class="input-group-text">
                            {{ form.department_name(class="form-control") }}
                        </div>
                    </div>
                </div>

                {{ form.submit(class="btn-custom") }}
            </form>
        </div>
    </div>   
</html>