<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Departments</h1>
                <a class="template-link btn-edit" href="{{ url_for('departments_v2.add_department') }}">
                    <i class="fi fi-rr-plus"></i> Department
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div>
                <div>
                    <div class="form--container">
                        <table id="departments-table" class="no-vertical-border">
                            <thead>
                                <tr>
                                    <th>Department Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if departments %}
                                    {% for department in departments %}
                                        <tr>
                                            <td>{{ department.department_name.upper() }}</td>
                                            <td>
                                                <div class="table-buttons">
                                                    <a class="green" href="{{ url_for('departments_v2.update_department', department_id=department.department_id) }}"><i class="fi fi-rr-edit"></i></a>
                                                <a class="red" href="{{ url_for('departments_v2.delete_department', department_id=department.department_id) }}"
                                                    onclick="return confirm('Are you sure you want to delete {{ department.department_name }} this department?');">
                                                    <i class="fi fi-rr-trash">
                                                </i></a>
                                                </div>
                                            </td>

                                        </tr>
                                    {% endfor %}
                                {% else %}
                                <tr style="text-align: center">
                                    <td colspan="6">
                                        <h3 class="mid-dark">No department found</h3>
                                        <i class="fi fi-rr-drawer-empty mid-dark big"></i>
                                        <p class="light">Please add a new department to see it <a href="{{ url_for('departments_v2.add_department') }}" class="blue bold">here</a></p>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}