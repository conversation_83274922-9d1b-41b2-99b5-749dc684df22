<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/tables.css') }}">
    <title>Departments</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('departments.add_department') }}">
            <i class="fas fa-plus"></i> Add department
        </a>
    </div>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="container">
        <div>
            <div>
                <div class="dynamic--form">
                    <h1>Departments</h1>
                    <table id="departments-table" class="table">
                        <thead>
                            <tr>
                                <th>Department Name</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for department in departments %}
                                <tr>
                                    <td>{{ department.department_name.upper() }}</td>
                                    <td>
                                        <div class="table-buttons">
                                            <a class="template-link btn-image" href="#" data-template-url= "{{ url_for('departments.update_department', department_id=department.department_id) }}"><i class="fi fi-rr-pencil"></i> Edit</a>
                                        <a class="btn-cancel" href="{{ url_for('departments.delete_department', department_id=department.department_id) }}"><i class="fi fi-rr-trash"></i> Delete</a>
                                        </div>              
                                    </td>
            
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
</body>
</html>