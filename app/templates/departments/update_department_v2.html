<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a class="template-link btn-edit" href="{{ url_for('departments_v2.departments') }}">
                <i class="fi fi-rr-list"></i> Departments
            </a>

        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <h1 class="header-title">Update Department</h1>
            <form method="POST">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {% for error in form.department_name.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        {{ form.department_name.label(class="form-label mt-2") }}
                        <div class="input-group-text">
                            {{ form.department_name(class="form-control") }}
                        </div>
                    </div>
                </div>   
                {{ form.submit(class="submit-btn") }}
            </form>
        </div>
    </div>
</div>
{% endblock %}
