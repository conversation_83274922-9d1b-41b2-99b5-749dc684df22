<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leave Records</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.supervisor_dashboard') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('attendance.record_leave_or_off') }}">
            <i class="fas fa-plus"></i> Leave | Off
        </a>
    </div>
    <div class="container">
        <div>
            <div>
                <h1>Leave records</h1>

                <table id="leave-table" class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Employee Name</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Remarks</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leave_records %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ Employee.get_employee_by_their_id(db_session, leave['employee_id'])['full_name'] }}</td>
                                <td>{% if Site.get_site_by_id(db_session, Employee.get_employee_by_their_id(db_session, leave['employee_id'])['site_id']) %}
                                    {{ Site.get_site_by_id(db_session, Employee.get_employee_by_their_id(db_session, leave['employee_id'])['site_id']).site_name }}</td>
                                    {% else %}
                                    N/A
                                    {% endif %}
                                <td>{{leave['work_status']}}</td>
                                <td>{{ leave['time_off_begin_date']}}</td>
                                <td>{{ leave['time_off_end_date']}}</td>
                                <td>{% if leave['remarks'] %}
                                    {{ leave['remarks']}}
                                    {% else %}
                                    N/A
                                    {% endif %}
                                </td>
                                <td>
                                    <a class="template-link btn-image" href="#" data-template-url="{{ url_for('attendance.update_leave_record', attendance_id=leave['attendance_id']) }}">
                                        <i class="fas fa-edit"></i>
                                        Edit
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>

</body>
</html>