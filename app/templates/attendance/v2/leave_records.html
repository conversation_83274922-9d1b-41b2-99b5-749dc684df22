<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Leave | Off Records</h1>
                <a class="template-link btn-edit" href="{{ url_for('attendance_v2.record_leave_or_off') }}">
                    <i class="fi fi-rr-plus-small"></i> Leave | Off
                </a>
            </div>
        </div>
    <div class="dyn_container">
        <div>
            <div>
                <table id="leave-records-table" class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Employee name</th>
                            <th>Work Status</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Remarks</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in leave_records %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ entry.employee_name }}</td>
                            <td>{{ entry.work_status }}</td>
                            <td>{{ entry.time_off_begin_date }}</td>
                            <td>{{ entry.time_off_end_date }}</td>
                            <td>{{ entry.remarks }}</td>
                            <td>
                                <a href="#" class="template-link btn primary-button" data-template-url="{{ url_for('attendance_v2.update_leave_record', attendance_id=entry.attendance_id) }}">Edit</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    </div>
{% endblock %}