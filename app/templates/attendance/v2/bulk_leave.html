<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <title>Bulk | Leave</title>
    <style>
        .leave-container {
            width: 95%;
            margin: 10px auto;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th,
        td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 5px 15px;
        }

        tr:nth-child(even) {
            background-color: #f3f0f0;
        }

        tr input,
        select {
            padding: inherit;
            border: none;
            background-color: inherit;
            width: 98%;
        }

        .confirm-col {
            cursor: pointer;
        }

        .confirm-col:hover {
            color: #25a38b;
            text-decoration: underline;
        }

        .active-offs {
            background-color: rgb(235, 250, 237);
            display: flex;
            border-radius: 3px;
            align-items: center;
            justify-content: space-between;
            color: #25a38b;
            font-weight: 700;
        }

        #search-box {
            width: 50%;
            margin: 10px 0;
            padding: 5px 10px;
            font-size: 20px;
        }
    </style>
</head>

<body>
    <div class="leave-container">
        <input type="text" name="search-param" id="search-box"
            placeholder="Type in first name/last name/email to search employee">
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Full Name</th>
                    <th>Email</th>
                    <th>Work Status</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Total Days</th>
                    <th>Active Leaves/Offs</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
    <script>
        // Modified version of bulk_leave.js with v2 API endpoints
        document.addEventListener('DOMContentLoaded', function() {
            const searchBox = document.getElementById('search-box');
            const tableBody = document.querySelector('tbody');
            let employees = [];

            // Fetch employees from the server
            fetch('/v2/api/employees')
                .then(response => response.json())
                .then(data => {
                    employees = data;
                    renderEmployees(employees);
                })
                .catch(error => console.error('Error fetching employees:', error));

            // Search functionality
            searchBox.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredEmployees = employees.filter(employee => {
                    const fullName = `${employee.first_name} ${employee.last_name}`.toLowerCase();
                    const email = employee.email ? employee.email.toLowerCase() : '';
                    return fullName.includes(searchTerm) || email.includes(searchTerm);
                });
                renderEmployees(filteredEmployees);
            });

            // Render employees in the table
            function renderEmployees(employeesToRender) {
                tableBody.innerHTML = '';
                employeesToRender.forEach((employee, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${employee.first_name} ${employee.last_name}</td>
                        <td>${employee.email || 'N/A'}</td>
                        <td>
                            <select class="work-status">
                                <option value="leave">Leave</option>
                                <option value="off">Off</option>
                                <option value="sick">Sick</option>
                                <option value="maternity">Maternity</option>
                                <option value="paternity">Paternity</option>
                            </select>
                        </td>
                        <td><input type="date" class="start-date"></td>
                        <td><input type="date" class="end-date"></td>
                        <td class="total-days">0</td>
                        <td class="confirm-col" data-employee-id="${employee.employee_id}">Confirm</td>
                    `;
                    tableBody.appendChild(row);

                    // Add event listeners for date inputs
                    const startDateInput = row.querySelector('.start-date');
                    const endDateInput = row.querySelector('.end-date');
                    const totalDaysCell = row.querySelector('.total-days');

                    [startDateInput, endDateInput].forEach(input => {
                        input.addEventListener('change', () => {
                            if (startDateInput.value && endDateInput.value) {
                                const startDate = new Date(startDateInput.value);
                                const endDate = new Date(endDateInput.value);
                                const diffTime = Math.abs(endDate - startDate);
                                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                                totalDaysCell.textContent = diffDays;
                            }
                        });
                    });

                    // Add event listener for confirm button
                    const confirmCol = row.querySelector('.confirm-col');
                    confirmCol.addEventListener('click', function() {
                        const employeeId = this.getAttribute('data-employee-id');
                        const workStatus = row.querySelector('.work-status').value;
                        const startDate = row.querySelector('.start-date').value;
                        const endDate = row.querySelector('.end-date').value;

                        if (!startDate || !endDate) {
                            alert('Please select both start and end dates.');
                            return;
                        }

                        // Send data to server
                        fetch('/v2/api/record_leave', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                employee_id: employeeId,
                                work_status: workStatus,
                                time_off_begin_date: startDate,
                                time_off_end_date: endDate
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert('Leave recorded successfully!');
                                // Clear inputs
                                row.querySelector('.start-date').value = '';
                                row.querySelector('.end-date').value = '';
                                totalDaysCell.textContent = '0';
                            } else {
                                alert('Error: ' + data.error);
                            }
                        })
                        .catch(error => {
                            console.error('Error recording leave:', error);
                            alert('An error occurred while recording leave.');
                        });
                    });
                });
            }
        });
    </script>
</body>

</html>
