<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Void Attendance</h1>
            <a class ="template-link btn-edit" href="{{ url_for('attendance_v2.view_voided_attendance') }}">                
                <i class="fi fi-rr-list"></i> voided attendance
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <form method="POST" action="{{ url_for('attendance_v2.void_attendance_record', attendance_id=attendance_id) }}">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    {{ form.status.label }}
                    <div class="input-group-text">
                        <i class="fas fa-user"></i>
                        {{ form.status(class="form-control") }}
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.reason.label }}
                        <div class="input-group-text">
                            <i class="fas fa-user"></i>
                            {{ form.reason(class="form-control") }}
                        </div>
                    </div>
                </div>
                <button type="submit" class="submit-btn">Void</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
