<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
<style>
    @media print {
    @page {
        size: landscape;
        margin: 1cm;
    }
    body * {
        visibility: hidden; 
    }
    #attendanceTable, #attendanceTable * {
        visibility: visible;
    }
    #attendanceTable {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
    }
    .table-title{
        display: block;
        text-align: center;
        margin-bottom: 20px;
    }
    /* Adjust table styling for print */
    #attendanceTable tbody td {
        font-size: 12px;
        padding: 8px;
    }
    #attendanceTable thead th{
        font-size: 14px;
        padding: 10px;
        border-right: 1px solid #d4e9eb;
        border-left: 1px solid #d4e9eb;
    }

    body{
        background-color: #fff;
    }

    /* Hide action buttons in print view */
    #attendanceTable td:last-child, #attendanceTable th:nth-child(7) {
        display: none;
    }
    #attendanceTable th:last-child, #attendanceTable td:nth-child(7) {
        display: none;
    }

    #attendanceTable th:first-child, #attendanceTable td:first-child {
        padding: 5px;
        max-width: 10px!important;
        margin: 0;
    }

    #attendanceTable tr {
        page-break-inside: avoid;
    }

    /*force background color for print*/
    *{
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact !important; /* Standard */
    }

}
</style>
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Attendance records</h1>
            <div class="right-buttons-group">
                <a class="btn-edit" href="{{ url_for('attendance_v2.view_voided_attendance') }}">
                    <i class="fi fi-rr-list"></i> Voided Attendance
                </a>
                <a class="btn-edit" href="{{ url_for('attendance_v2.clockin_employee_list') }}">
                    <i class="fi fi-rr-tap"></i> Manual Attendance
                </a>
            </div>
        </div>
         <div class="space-between">
            <div class="att-filter-container">
                <div class="page_rows">
                    <label for="rowsPerPageInput">Show:</label>
                    <select id="rowsPerPageInput">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select>
                </div>
                <div class="flex page_search">
                    <div>
                        <label for="attFilterSearchInput">Search</label>
                        <input type="text" id="attFilterSearchInput" placeholder="Search by name, status..." class="form-control">
                    </div>
                <button class="toggle-filters-btn primary-button" id="attFilterToggleBtn">Show Filters</button>
                </div>
                <div class="advanced-filters" id="attFilterAdvanced" style="display: none;">
                    <h3>Filter by:</h3>
                    <div class="form-group">
                        <label for="attFilterPeriodSelect">Period:</label>
                        <select id="attFilterPeriodSelect" class="form-select">
                            <option value="">All</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="quarterly">Quarterly</option>
                            <option value="yearly">Yearly</option>
                            <option value="custom">Custom</option>
                        </select>
                    </div>
                    <div class="form-group" id="attSingleDateGroup">
                        <label for="attFilterDate">Select Date:</label>
                        <input type="date" id="attFilterDate" class="form-control">
                    </div>
                    <div class="form-group" id="attQuarterYearGroup" style="display: none;">
                        <label for="attFilterQuarterYear">Select Quarter/Year:</label>
                        <select id="attFilterQuarterYear" class="form-select"></select>
                    </div>
                    <div class="form-group" id="attCustomDateGroup" style="display: none;">
                        <label for="attFilterStartDate">Start Date:</label>
                        <input type="date" id="attFilterStartDate" class="form-control">
                        <label for="attFilterEndDate">End Date:</label>
                        <input type="date" id="attFilterEndDate" class="form-control">
                    </div>
                </div>
                <button id="printAttendanceBtn" class="green-button"><i class="fi fi-rr-print"></i>Print</button>
            </div>
        </div>
    </div>
<!-- Existing table remains unchanged -->

</div>
    <div>
        <div class="table-title" style="display: none">
            <h2>Attendance Records</h2>
        </div>
        <table class="small-table" id="attendanceTable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Employee name</th>
                    <th>Time In</th>
                    <th>Clockin Location</th>
                    <th>Time Out</th>
                    <th>Clockout Location</th>
                    <th>status</th>
                    <th>Total Duration</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for entry in attendance %}
                <tr>
                    <td style="max-width: 10px!important">{{ loop.index }}</td>
                    <td>{{ entry.employee_name }}</td>
                    <td>{{ entry.time_in }}</td>
                    <td>{% if entry.clockin_location_name != None %} 
                        {{ entry.clockin_location_name }}
                        {% elif entry.clockin_location != None %}
                        {{ entry.clockin_location }}
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    <td>{% if entry.time_out == None %} - {% else %}
                        {{ entry.time_out }}
                        {% endif %}
                    </td>
                    <td>{% if entry.clockout_location_name != None %} 
                        {{ entry.clockout_location_name }}
                        {% elif entry.clockout_location != None %}
                        {{ entry.clockout_location }}
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    <td>{{ entry.recorgnition_status }}</td>
                    <td>{% if entry.total_duration == None %} - {% else %}
                        {{ entry.total_duration }}
                        {% endif %}
                    </td>
                    <td>
                        <a class="green" href="{{ url_for('attendance_v2.void_attendance_record', attendance_id=entry.attendance_id) }}"><i class="fi fi-rr-edit"></i></a>
                    </td>                                    
                </tr>
                {% endfor %}
                {% for entry in field_attendance %}
                <tr>
                    <td>{{ loop.index + attendance|length }}</td>
                    <td>{{ entry.employee_name }}</td>
                    <td>
                        {% if entry.field_clockin != None %}
                            {{ entry.field_in }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if entry.field_in_location_name != None %}
                            {{ entry.field_in_location_name }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if entry.field_out != None %}
                            {{ entry.field_out }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if entry.field_out_location_name != None %}
                            {{ entry.field_out_location_name }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>Field Attendance</td>
                    <td>{% if entry.total_duration %}{{ entry.total_duration }}{% else %} - {% endif %}</td>
                    <td>
                        <a class="green" href="{{ url_for('attendance_v2.void_attendance_record', attendance_id=entry.attendance_id) }}">Edit</a>
                    </td>                                    
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <div id="attFilterPagination" class="pagination"></div>
    </div>
<script src="{{ url_for('static', filename='scripts/filter_attendance.js') }}"></script>
{% endblock %}
