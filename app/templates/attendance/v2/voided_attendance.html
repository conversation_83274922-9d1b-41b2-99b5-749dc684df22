<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Voided attendance records</h1>
            <a class="btn-edit" href="{{ url_for('attendance_v2.attendance_records') }}">
                <i class="fi fi-rr-list"></i> Attendance Records
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <div class="container">
                <div>
                    <div>
                        <table id="attendance-table" class="table">
                            <thead>
                                <tr>
                                    <th>Employee name</th>
                                    <th>Attendance Date</th>
                                    <th>Time In</th>
                                    <th>Time Out</th>
                                    <th>Status</th>
                                    <th>Total Duration</th>
                                    <th>Reason</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in voided_attendance %}
                                <tr>
                                    <td>{{ entry.employee_name }}</td>
                                    <td>{{ entry.created_at }}</td>
                                    <td>{{ entry.time_in }}</td>
                                    <td>{% if entry.time_out == None %}  {% else %}
                                        {{ entry.time_out }}
                                        {% endif %}
                                    </td>
                                    <td>{{ entry.recorgnition_status }}</td>
                                    <td>{% if entry.total_duration == None %}  {% else %}
                                        {{ entry.total_duration }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ entry.void_reason }}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('attendance_v2.void_attendance_record', attendance_id=entry.attendance_id) }}" class="green"><i class="fi fi-rr-edit"></i></a>
                                    </td>                                    
                                
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
       
{% endblock %}
