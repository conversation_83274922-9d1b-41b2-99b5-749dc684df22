<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="template-link btn-edit" href="{{ url_for('attendance_v2.get_leave_records') }}">
                    <i class="fi fi-rr-list"></i> Leave/Off
                </a>
            </div>
        </div>
        <div class="dyn_container">
        <div class="form--container">
            <h1>Record Leave or Off</h1>
            <form action="{{ url_for('attendance_v2.record_leave_or_off') }}" method="post">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        <label for="employee_id">Select Employee</label>
                        <div class="input-group-text">
                            <i class="fas fa-user"></i>
                            <select name="employee_id" id="employee_id" class="form-control">
                                <option value="" selected disabled>Select Employee</option>
                                {% for employee in employees %}
                                    <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{employee.last_name}}</option>
                                {% endfor %}
                            </select>
                            <!-- Capture the employee_id as a hidden field -->
                            <input type="hidden" name="employee_id" value="{{ employee_id }}">
                        </div>
                    </div>
                    <div class="form-group">
                            {{ form.time_off_begin_date.label }}
                        <div class="input-group-text">
                            <i class="fas fa-calendar-alt"></i>
                            {{ form.time_off_begin_date(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.time_off_end_date.label }}
                        <div class="input-group-text">
                            <i class="fas fa-calendar-alt"></i>
                            {{ form.time_off_end_date(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.work_status.label }}
                        <div class="input-group-text">
                            <i class="fa-regular fa-newspaper"></i>
                            {{ form.work_status(class="form-control") }}
                        </div>
                    </div>
                </div>
                <button type="submit" class="submit-btn">
                    <i class="fi fi-rr-check"></i> Submit
                </button>
            </form>
        </div>
    </div>
    </div>
{% endblock %}