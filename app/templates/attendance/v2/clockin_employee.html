<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Manual attendance</h1>
            <a class="btn-edit" href="{{ url_for('attendance_v2.attendance_records') }}">
                <i class="fi fi-rr-list"></i> Attendance Records
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <table>
            <tr>
                <th>Employee Name</th>
                <th>Actions</th>
            </tr>
            {% for employee in employees %}
            <tr>
                <td>{{ employee.first_name }} {{ employee.last_name }}</td>
                <td class="green-box">
                    <a href="{{ url_for('attendance_v2.clockin_employee', employee_id=employee.employee_id) }}"
                    onclick="return confirm('Are you sure you want to clock in this employee?');"
                    >
                        <i class="fi fi-rr-dot-circle green"></i> <span class="status-text">attend</span>
                    </a>
                </td>
            </tr>
            {% endfor %}
        </table>
    </div>
</div>
    <script src="{{ url_for('static', filename='scripts/pops.js') }}"></script>
{% endblock %}