<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clockout</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/video.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
</head>
<body>
    <div class="real-form video-frame">
        <h1><i class="fas fa-sign-out-alt"></i> Clockout</h1>
        <!-- Webcam feed -->
        <div class="vid-container">
            <div class="scanner-line"></div>
            <video id="videoElement"autoplay></video>
        </div>
        <button id="captureButton"><i class="fas fa-camera"></i> Scan Me</button>
        <i class="fas fa-check" id="checkButton" style="display: none;"></i>
        <i class="fas fa-xmark" id="crossButton" style="display: none;"></i>
        <i class="fas fa-info" id="infoButton" style="display: none;"></i>
        <!-- Canvas for processing image capture -->
        <canvas id="canvas"></canvas>
        <!-- Display recognition results -->
        <div id="result"></div>
        <div class="decision-buttons">
            <a href="{{ url_for('attendance_v2.clockin') }}" id="clockInButton"><i class="fas fa-sign-in-alt"></i> Clock In</a>
            <a href="{{ url_for('attendance_v2.clockout') }}" id="clockOutButton"><i class="fas fa-sign-out-alt"></i> Clock Out</a>
        </div>
    </div>
    <script>
        // Store session data in localStorage
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Create a session data object with necessary information
                const sessionData = {
                    'company_id': '{{ session.company_id }}',
                    'database_name': '{{ session.database_name }}',
                    'user_id': '{{ session.user_id }}',
                    'role': '{{ session.role }}'
                };

                // Store it in localStorage
                localStorage.setItem('sessionData', JSON.stringify(sessionData));
                console.log('Session data stored in localStorage:', sessionData);
            } catch (e) {
                console.error('Error storing session data:', e);
            }
        });
    </script>
    <script>
        // Modified version of clock_out.js with v2 API endpoint
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector("#videoElement");
            const captureButton = document.querySelector("#captureButton");
            const canvas = document.querySelector("#canvas");
            const resultDiv = document.querySelector("#result");
            const checkButton = document.querySelector("#checkButton");
            const crossButton = document.querySelector("#crossButton");
            const infoButton = document.querySelector("#infoButton");

            let stream = null;

            // Function to start the webcam
            function startWebcam() {
                navigator.mediaDevices.getUserMedia({ video: true })
                    .then(function(mediaStream) {
                        stream = mediaStream;
                        video.srcObject = stream;
                    })
                    .catch(function(err) {
                        console.log("Error accessing webcam: " + err);
                        resultDiv.innerHTML = "Error accessing webcam. Please make sure your camera is connected and permissions are granted.";
                    });
            }

            // Start webcam when page loads
            startWebcam();

            // Function to get geolocation
            function getLocation() {
                return new Promise((resolve, reject) => {
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            position => resolve({
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude
                            }),
                            error => reject(error)
                        );
                    } else {
                        reject(new Error("Geolocation is not supported by this browser."));
                    }
                });
            }

            // Function to stop the webcam
            function stopWebcam() {
                if (stream) {
                    const tracks = stream.getTracks();
                    tracks.forEach(track => track.stop());
                    stream = null;
                }
            }

            // Capture button click event
            captureButton.addEventListener("click", async function() {
                try {
                    // Get location first
                    const location = await getLocation();
                    
                    // Then capture image
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const context = canvas.getContext('2d');
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // Show loading state
                    captureButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                    captureButton.disabled = true;

                    // Convert the canvas content to a Blob
                    canvas.toBlob(function(blob) {
                        const formData = new FormData();
                        formData.append("image", blob, "employee_image.jpg");
                        formData.append("latitude", location.latitude);
                        formData.append("longitude", location.longitude);
                        
                        // Get session data from localStorage
                        try {
                            const sessionData = localStorage.getItem('sessionData');
                            if (sessionData) {
                                formData.append("session_data", sessionData);
                            }
                        } catch (e) {
                            console.error('Error retrieving session data:', e);
                        }

                        // Send the form data to the Flask backend
                        fetch('/v2/api/clockout', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => {
                            if (response.redirected) {
                                window.location.href = response.url;
                                return;
                            }
                            return response.json();
                        })
                        .then(result => {
                            if (!result) return; // Handle redirect case
                            
                            // Reset button
                            captureButton.innerHTML = '<i class="fas fa-camera"></i> Scan Me';
                            captureButton.disabled = false;

                            // Display result
                            if (result.error) {
                                resultDiv.innerHTML = `<div class="error-message">${result.error}</div>`;
                                crossButton.style.display = "inline-block";
                                checkButton.style.display = "none";
                                infoButton.style.display = "none";
                            } else {
                                resultDiv.innerHTML = `<div class="success-message">${result.message || JSON.stringify(result)}</div>`;
                                checkButton.style.display = "inline-block";
                                crossButton.style.display = "none";
                                infoButton.style.display = "none";
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            resultDiv.innerHTML = `<div class="error-message">Error: ${error.message}</div>`;
                            captureButton.innerHTML = '<i class="fas fa-camera"></i> Scan Me';
                            captureButton.disabled = false;
                            infoButton.style.display = "inline-block";
                            checkButton.style.display = "none";
                            crossButton.style.display = "none";
                        });
                    }, 'image/jpeg');
                } catch (error) {
                    console.error('Error:', error);
                    resultDiv.innerHTML = `<div class="error-message">Error: ${error.message}</div>`;
                    captureButton.innerHTML = '<i class="fas fa-camera"></i> Scan Me';
                    captureButton.disabled = false;
                }
            });
        });
    </script>
</body>
</html>
