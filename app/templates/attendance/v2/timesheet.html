<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div style="text-align: center;" class="company">
                <h2>{% if company_name %}{{ company_name.upper() }}{% else %}COMPANY{% endif %} EMPLOYEE ATTENDANCE REPORT - {{ current_month_name.upper()}} {{ current_year }} </h2>
            </div>
            <div class="dynamic--buttons">
                <h3>Timesheet</h3>
                <div class="right-buttons-group"> 
                    <button class="blue-button" onclick="timeSheetPopup()">
                        <i class="fi fi-rr-calendar"></i>
                        Timesheet
                    </button>
                    <a href="{{ url_for('attendance.export_timesheet_excel') }}" class="green-button">
                    <i class="fi fi-rr-download"></i>
                    Excel
                    </a>
                </div>
            </div>

            <div class="filter-container">
                <div class="page_rows">
                    <label for="rowsPerPageInput">Show:</label>
                    <select id="rowsPerPageInput">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
                <div>
                    <label for="timesheetSearchInput">Search</label>
                    <input type="text" id="timesheetSearchInput" placeholder="Search by name, status..." class="form-control">
                </div>
                <button class="toggle-filters-btn primary-button" id="timesheetToggleFiltersBtn">Show Filters</button>
                <div class="advanced-filters" id="timesheetAdvancedFilters">
                    <h3>Filter by:</h3>
                    <div class="form-group">
                        <label for="timesheetlocationFilter">Location:</label>
                        <select id="timesheetlocationFilter" class="form-select">
                            <option value="">All</option>
                            {% for site in sites %}
                                <option value="{{ site.id }}">{{ site.site_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

        </div>

    <div class="dyn_container">
            <div style="text-align: center;" class="no-print">
                <!--
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fa fa-print"></i>
                Print
            </button>
            -->

        </div>
        <div class="print">
            <!-- Form to select the period -->
             <div class="mypopup" id="timesheet-popup">
                <div class="popup-content">
                    <div style="text-align: center;">
                        <form method="POST" action="{{ url_for('attendance_v2.timesheet') }}">
                            {{ form.hidden_tag() }}
                            <!-- Period field -->
                            <div class="no-print">
                                {{ form.period.label }} <br>
                                {{ form.period(class="form-control") }}
                            <!-- Submit button -->
                            <div class="form-buttons">
                                <button type="submit" class="submit-btn blue-button">
                                    <i class="fi fi-rr-check"></i>
                                    Generate
                                </button>
                                <button type="button" class="submit-btn grey-button" onclick="closeTimeSheetPopup()">
                                    Cancel
                                </button>
                            </div>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
            <table id="timesheet">
                <thead>
                    <tr>
                        <th rowspan="2">#</th>
                        <th rowspan="2" style="min-width: 200px;">EMPLOYEE NAME</th>
                        <th rowspan="2">LOCATION</th>

                        <!-- Loop through days of the month -->
                        {% for day in range(1, total_days_in_month + 1) %}
                            <th rowspan="2">{{ day }}</th>
                        {% endfor %}

                        <th colspan="5">Total</th>
                        <th colspan="2">Salary</th>
                    </tr>
                    <tr>
                        <!-- Empty cells to align Worked, Off, A, L to the right of the days -->
                        {% for day in range(1, total_days_in_month + 1) %}
                        {% endfor %}
                        <th colspan="1">Days Worked</th>
                        <th colspan="1">Hours worked</th>
                        <th colspan="1">Off</th>
                        <th colspan="1">A</th>
                        <th colspan="1">L</th>
                        <th colspan="1">Days Payable</th>
                        <th colspan="1">Net</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees_attendance %}
                        <tr>
                            <td class="texts">{{ loop.index }}</td> <!-- Use loop.index for numbering -->
                            <td class="texts">{{ employee.employee.first_name }} {{ employee.employee.last_name }}</td>
                            <td class="texts">{% if employee.employee.site_id %}
                                {{ Site.get_site_by_id(db_session, employee.employee.site_id).site_name }}</td>
                                {% else %}
                                N/A
                                {% endif %}
                            <!-- Loop through the pre-processed attendance status -->
                            {% for status in employee.attendance_status %}
                                <td class="status"><div class="status-text">{{ status }}</div></td>
                            {% endfor %}
                            <td>{{ employee.days_worked }}</td> <!-- Worked -->
                            <td>{{ employee.total_hours_worked }}</td> <!-- Hours worked -->
                            <td>{{ employee.days_off_count }}</td> <!-- Off -->
                            <td>{{ employee.days_absent }}</td> <!-- Absent (A) -->
                            <td>{{employee.days_leave}}</td> <!-- Leave (L) -->
                            <td>{{ employee.paid_days }}</td> <!-- Days Payable -->
                            <td>{{ employee.applicable_net_salary }}</td> <!-- Net -->
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            <div class="pagination" id="timesheetPagination"></div>
                <!-- Pagination controls can be added here if needed -->
        </div>
        </div>
    </div>
        <script src="{{ url_for('static', filename='scripts/timesheet.js') }}"></script>
        {% endblock %}

