<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Attendance</title>
    <style>
        /* Optional styles */
        video {
            width: 100%;
            max-width: 500px;
            border: 2px solid black;
        }
        canvas {
            display: none;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid black;
        }
    </style>
</head>
<body>
    <h1>Capture Attendance</h1>

    <!-- Webcam feed -->
    <video id="videoElement" autoplay></video>
    
    <!-- Capture button -->
    <button id="captureButton">Capture Image</button>

    <!-- Canvas for processing image capture -->
    <canvas id="canvas"></canvas>

    <!-- Display recognition results -->
    <div id="result"></div>

    <script>
        const video = document.querySelector("#videoElement");

        // Access the webcam
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                video.srcObject = stream;

                // Capture the image and send it to the backend
                document.querySelector("#captureButton").addEventListener("click", function() {
                    const canvas = document.querySelector("#canvas");
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const context = canvas.getContext('2d');
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // Convert the canvas content to a Blob and append it to FormData
                    canvas.toBlob(function(blob) {
                        const formData = new FormData();
                        formData.append("image", blob, "employee_image.jpg");  // Use a valid filename with extension

                        // Send the form data to the Flask backend
                        fetch('/add_attendance', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(result => {
                            // Display result in the 'result' div
                            const resultDiv = document.querySelector("#result");
                            resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                            
                            // Stop the webcam after capturing the image
                            stopWebcam(stream);
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                    }, 'image/jpeg');  // Ensure the correct MIME type is used (image/jpeg)
                });
            })
            .catch(function(err) {
                console.log("Error: " + err);
            });

        // Function to stop the webcam
        function stopWebcam(stream) {
            const tracks = stream.getTracks();
            tracks.forEach(track => track.stop());
        }
    </script>
</body>
</html>
