<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leave Management</title>
</head>
<body>
<div class="dynamic--buttons">
    <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('attendance.get_leave_records') }}">
        <i class="fas fa-arrow-left"></i> Back
    </a>
</div>
  <div class="dynamic--form">
    <h1>Record Leave or Off</h1>
    <form action="{{ url_for('attendance.record_leave_or_off') }}" method="post">
        {{ form.hidden_tag() }}
        <div class="form-row">
            <div class="form-group">
                <label for="employee_id">Select Employee</label>
                <div class="input-group-text">
                    <i class="fas fa-user"></i>
                    <select name="employee_id" id="employee_id" class="form-control">
                        <option value="" selected disabled>Select Employee</option>
                        {% for employee in employees %}
                            <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{employee.last_name}}</option>
                        {% endfor %}
                    </select>
                    <!-- Capture the employee_id as a hidden field -->
                    <input type="hidden" name="employee_id" value="{{ employee_id }}">
                </div>
            </div>
            <div class="form-group">
                    {{ form.time_off_begin_date.label }}
                <div class="input-group-text">
                    <i class="fas fa-calendar-alt"></i>
                    {{ form.time_off_begin_date(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.time_off_end_date.label }}
                <div class="input-group-text">
                    <i class="fas fa-calendar-alt"></i>
                    {{ form.time_off_end_date() }}
                </div>
            </div>
            <div class="form-group">
                {{ form.work_status.label }}
                <div class="input-group-text">
                    <i class="fa-regular fa-newspaper"></i>
                    {{ form.work_status(class="form-control") }}
                </div>
            </div>
        </div>
        {{ form.submit(class="action-button") }}
    </form>
</div>
</body>

</html>