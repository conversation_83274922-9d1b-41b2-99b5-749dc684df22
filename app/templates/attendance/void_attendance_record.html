<!DOCTYPE html>
<html>
<head>
    <title>Void Attendance Record</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('attendance.attendance_records') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class ="template-link btn-edit" href="#" data-template-url="{{ url_for('attendance.view_voided_attendance') }}">                <i class="fas fa-list"></i> voided attendance
        </a>
    </div>
    <div class="dynamic--form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <h1>Void Attendance Record</hh1>
        <form  method="post">
            {{ form.csrf_token }}
            <div class="form-row">
                <div class="form-group">
                    {{ form.status.label }}
                    <div class="input-group-text">
                        <i class="fas fa-user"></i>
                        {{ form.status(class="form-control") }}
                    </div>
                </div>
                <div class="form-group">
                    {{ form.reason.label }}
                    <div class="input-group-text">
                        <i class="fas fa-user"></i>
                        {{ form.reason(class="form-control") }}
                    </div>
                </div>
            </div>
                {{ form.submit(class="btn btn-custom") }}
            </div>
            
        </form>
    </div>

</body>
</html>