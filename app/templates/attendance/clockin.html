<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clockin</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/video.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
</head>
<body>

        <div class="real-form video-frame">
            <h1><i class="fas fa-sign-in-alt"></i> Clock In</h1>
            <!-- Webcam feed -->
            <div class="vid-container">
                <div class="scanner-line"></div>
                <video id="videoElement"autoplay></video>
            </div>
            <!-- Capture button -->
            <button id="captureButton" class="captureButton"><i class="fas fa-camera"></i> Scan Me</button>
            <i class="fas fa-check" id="checkButton" style="display: none;"></i>
            <i class="fas fa-xmark" id="crossButton" style="display: none;"></i>
            <i class="fas fa-info" id="infoButton" style="display: none;"></i>
            <!-- Canvas for processing image capture -->
            <canvas id="canvas"></canvas>
            <!-- Display recognition results -->
            <div id="result"></div>
            <div class="decision-buttons">
                <a  href="{{ url_for('attendance.clockin') }}" id="clockInButton"><i class="fas fa-sign-in-alt"></i> Clock In</a>
                <a  href="{{ url_for('attendance.clockout') }}" id="clockOutButton"><i class="fas fa-sign-out-alt"></i> Clock Out</a>
            </div>
        </div>
        <script>
            // Store session data in localStorage
            document.addEventListener('DOMContentLoaded', function() {
                try {
                    // Create a session data object with necessary information
                    const sessionData = {
                        'company_id': '{{ session.company_id }}',
                        'database_name': '{{ session.database_name }}',
                        'user_id': '{{ session.user_id }}',
                        'role': '{{ session.role }}'
                    };

                    // Store it in localStorage
                    localStorage.setItem('sessionData', JSON.stringify(sessionData));
                    console.log('Session data stored in localStorage:', sessionData);
                } catch (e) {
                    console.error('Error storing session data:', e);
                }
            });
        </script>
        <script src="{{ url_for('static', filename='scripts/clock_in.js') }}"></script>
    </body>
</html>
