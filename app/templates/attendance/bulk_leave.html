<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <title>Bulk | Leave</title>
    <style>
        .leave-container {
            width: 95%;
            margin: 10px auto;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th,
        td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 5px 15px;
        }

        tr:nth-child(even) {
            background-color: #f3f0f0;
        }

        tr input,
        select {
            padding: inherit;
            border: none;
            background-color: inherit;
            width: 98%;
        }

        .confirm-col {
            cursor: pointer;
        }

        .confirm-col:hover {
            color: #25a38b;
            text-decoration: underline;
        }

        .active-offs {
            background-color: rgb(235, 250, 237);
            display: flex;
            border-radius: 3px;
            align-items: center;
            justify-content: space-between;
            color: #25a38b;
            font-weight: 700;
        }

        #search-box {
            width: 50%;
            margin: 10px 0;
            padding: 5px 10px;
            font-size: 20px;
        }
    </style>
</head>

<body>
    <div class="leave-container">
        <input type="text" name="search-param" id="search-box"
            placeholder="Type in first name/last name/email to search employee">
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Full Name</th>
                    <th>Email</th>
                    <th>Work Status</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Total Days</th>
                    <th>Active Leaves/Offs</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
    <script src="{{url_for('static', filename='scripts/bulk_leave.js')}}"></script>
</body>

</html>