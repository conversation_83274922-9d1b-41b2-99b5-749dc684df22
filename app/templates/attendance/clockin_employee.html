<!DOCTYPE html>
<html>
<head>
    <title>Manual Clock-In</title>
</head>
<body>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">×</button>
                        </li>
                    {% endfor %}
                </ul>
                <!-- Include the external JavaScript file -->
                <script src="{{ url_for('static', filename='scripts/alerts.js') }}"></script>
            {% endif %}
        {% endwith %}
    </div>
    <h1>Clock In Employee</h1>
    <table>
        <tr>
            <th>Employee Name</th>
            <th>Actions</th>
        </tr>
        {% for employee in employees %}
        <tr>
            <td>{{ employee.first_name }} {{ employee.last_name }}</td>
            <td>
                <a class="template-link primary-button table-button" data-template-url="{{ url_for('attendance.clockin_employee', employee_id=employee.employee_id) }}"><i class="fi fi-rr-dot-circle"></i><p>Clock In</p></a>
            </td>
        </tr>
        {% endfor %}
    </table>
    <script src="{{ url_for('static', filename='scripts/pops.js') }}"></script>

</body>
</html>
