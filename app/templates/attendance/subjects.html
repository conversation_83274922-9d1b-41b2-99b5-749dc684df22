<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <title>Employees with Saved faces</title>
</head>
<body>
    <h1>Employees with No Pictures</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <ul>
          {% for category, message in messages %}
            <li class="{{ category }}">{{ message }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}
   <table id="list_employees" class="table">
    <thead>
        <tr>
            <th>#</th>
            <th>Name</th>
            <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for name in employees %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ name['full_name'] }}</td>
                <td>
                  <div class="table-buttons">
                    <a class="template-link btn-image" href="#" data-template-url="{{ url_for('attendance.create_subject', name=name.employee_id ~ ' ' ~ name.first_name ~ ' ' ~ name.last_name) }}">
                      <i class="fi fi-rr-add-image"></i> Picture
                    </a>
                  </div>
                </td>
            </tr>
        {% endfor %}
      </tbody>
    </table>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
</body>
</html>