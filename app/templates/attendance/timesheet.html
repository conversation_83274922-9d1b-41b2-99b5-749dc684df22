<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Attendance</title>
    <!--fa icons-->
</head>
<body>
    <!-- Add a link to download the timesheet
    <div style="text-align: center;">
        <a href="{{ url_for('attendance.download_timesheet') }}" class="btn btn-primary">Download Timesheet</a>
    </div>
    -->
    <!--Print and Export Buttons-->
    <div style="text-align: center;" class="no-print">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fa fa-print"></i>
            Print
        </button>
        <a href="{{ url_for('attendance.export_timesheet_excel') }}" class="btn btn-success">
            <i class="fa fa-file-excel-o"></i>
            Export to Excel
        </a>
    </div>
    <div class="print">
        <div style="text-align: center;" class="company">
            <h2>{% if company_name %}{{ company_name.upper() }}{% else %}COMPANY{% endif %}</h2>
            <p><strong>MONTH:</strong> {{ current_month_name.upper()}} {{ current_year }}</p>
        </div>
        <!-- Form to select the period -->
        <div style="text-align: center;">
            <form method="POST" action="{{ url_for('attendance.timesheet') }}">
                {{ form.hidden_tag() }}
                <!-- Period field -->
                <div class="no-print">
                    {{ form.period.label }} <br>
                    {{ form.period(class="form-control") }}


                <!-- Submit button -->
                <div style="margin-top: 10px;">
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </div>
            </form>
        </div>
        <div class="company">
            <h3 style="text-align: center;">MONTHLY ATTENDANCE RECORDS</h3>
        </div>
        <table>
            <thead>
                <tr>
                    <th rowspan="2">#</th>
                    <th rowspan="2">EMPLOYEE NAME</th>
                    <th rowspan="2">LOCATION</th>

                    <!-- Loop through days of the month -->
                    {% for day in range(1, total_days_in_month + 1) %}
                        <th rowspan="2">{{ day }}</th>
                    {% endfor %}

                    <th colspan="5">Total</th> <!-- Spans across the Worked, Off, A, L columns -->
                </tr>
                <tr>
                    <!-- Empty cells to align Worked, Off, A, L to the right of the days -->
                    {% for day in range(1, total_days_in_month + 1) %}
                    {% endfor %}
                    <th colspan="1">Days Worked</th>
                    <th colspan="1">Hours worked</th>
                    <th colspan="1">Off</th>
                    <th colspan="1">A</th>
                    <th colspan="1">L</th>
                    <th colspan="1">Days Payable</th>
                    <th colspan="1">Net</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees_attendance %}
                    <tr>
                        <td class="texts">{{ loop.index }}</td> <!-- Use loop.index for numbering -->
                        <td class="texts">{{ employee.employee.first_name }} {{ employee.employee.last_name }}</td>
                        <td class="texts">{% if employee.employee.site_id %}
                            {{ Site.get_site_by_id(db_session, employee.employee.site_id).site_name }}</td>
                            {% else %}
                            N/A
                            {% endif %}
                        <!-- Loop through the pre-processed attendance status -->
                        {% for status in employee.attendance_status %}
                            <td class="status">{{ status }}</td>
                        {% endfor %}
                        <td>{{ employee.days_worked }}</td> <!-- Worked -->
                        <td>{{ employee.total_hours_worked }}</td> <!-- Hours worked -->
                        <td>{{ employee.days_off_count }}</td> <!-- Off -->
                        <td>{{ employee.days_absent }}</td> <!-- Absent (A) -->
                        <td>{{employee.days_leave}}</td> <!-- Leave (L) -->
                        <td>{{ employee.paid_days }}</td> <!-- Days Payable -->
                        <td>{{ employee.applicable_net_salary }}</td> <!-- Net -->
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="{{ url_for('static', filename='scripts/timesheet.js') }}"></script>
</body>
</html>
