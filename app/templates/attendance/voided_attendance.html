<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employees Attendance Records</title>

    <!-- jQuery (required for DataTables) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables CSS and JS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('attendance.attendance_records') }}">
            <i class="fas fa-arrow-left"></i> Attendance Records
        </a>
    </div>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="dynamic--form">
        <h1>Voided Attendance Records</h1>
        <div class="container">
            <div>
                <div>
                    <table id="attendance-table" class="table">
                        <thead>
                            <tr>
                                <th>Employee name</th>
                                <th>Attendance Date</th>
                                <th>Time In</th>
                                <th>Time Out</th>
                                <th>Status</th>
                                <th>Total Duration</th>
                                <th>Reason</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in voided_attendance %}
                            <tr>
                                <td>{{ entry.employee_name }}</td>
                                <td>{{ entry.created_at }}</td>
                                <td>{{ entry.time_in }}</td>
                                <td>{% if entry.time_out == None %}  {% else %}
                                    {{ entry.time_out }}
                                    {% endif %}
                                </td>
                                <td>{{ entry.recorgnition_status }}</td>
                                <td>{% if entry.total_duration == None %}  {% else %}
                                    {{ entry.total_duration }}
                                    {% endif %}
                                </td>
                                <td>
                                    {{ entry.void_reason }}
                                </td>
                                <td>
                                    <a href="{{ url_for('attendance.void_attendance_record', attendance_id=entry.attendance_id) }}" class="btn btn-primary">Edit</a>
                                </td>                                    
                            
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Initialize DataTables -->
    <script>
        $(document).ready(function() {
            $('#attendance-table').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10
            });
        });
    </script>
</body>
</html>