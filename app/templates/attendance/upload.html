<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Employee Picture</title>
</head>
<body>
  <div class="dynamic--buttons">
    <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('employees.employees_list') }}">
        <i class="fas fa-arrow-left"></i> back
    </a>
  </div>  
  <div class="dynamic--form"> 
      <h1>Upload Employee Picture</h1>
      <form action="{{ url_for('attendance.create_subject') }}" method="post" enctype="multipart/form-data">
          {{ form.hidden_tag() }} <!-- CSRF token -->
          
          <!-- Hidden input to store the employee's name passed from the URL -->
          <input type="hidden" name="name" value="{{ request.args.get('name') }}">
          <div class="form-group">
              {{ form.image.label }}
              <div class="input-group-text">
                {{ form.image() }} 
              </div>
          </div>
          {{ form.submit(class="btn-custom") }} 
      </form>
    </div>
  </body>
</html>
