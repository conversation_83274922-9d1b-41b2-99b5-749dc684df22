<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employees Attendance Records</title>

    <!-- jQuery (required for DataTables) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables CSS and JS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <form  action="{{ url_for('attendance.attendance_records') }}" method="POST">
        {{ form.hidden_tag() }}
        <div class="form-group">
            {{ form.start_period.label(class="form-label") }}
            {{ form.start_period(class="form-control") }}
        </div>
        <div class="form-group">
            {{ form.end_period.label(class="form-label") }}
            {{ form.end_period(class="form-control") }}
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-primary">Filter</button>
        </div>
    </form>
            
    
        <h1>Employees Attendance Records</h1>
        <div class="container">
            <div>
                <div>
                    <table id="attendance-table" class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Employee name</th>
                                <th>Time In</th>
                                <th>Clockin Location</th>
                                <th>Time Out</th>
                                <th>Clockout Location</th>
                                <th>Attendance Type</th>
                                <th>Total Duration</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in attendance %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ entry.employee_name }}</td>
                                <td>{{ entry.time_in }}</td>
                                <td>{% if entry.clockin_location_name != None %} 
                                    {{ entry.clockin_location_name }}
                                    {% elif entry.clockin_location != None %}
                                    {{ entry.clockin_location }}
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{% if entry.time_out == None %}  {% else %}
                                    {{ entry.time_out }}
                                    {% endif %}
                                </td>
                                <td>{% if entry.clockout_location_name != None %} 
                                    {{ entry.clockout_location_name }}
                                    {% elif entry.clockout_location != None %}
                                    {{ entry.clockout_location }}
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ entry.recorgnition_status }}</td>
                                <td>{% if entry.total_duration == None %}  {% else %}
                                    {{ entry.total_duration }}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="template-link" data-template-url="{{ url_for('attendance.void_attendance_record', attendance_id=entry.attendance_id) }}" class="btn btn-primary">Edit</a>
                                </td>                                    
                            
                            </tr>
                            {% endfor %}
                            {% for entry in field_attendance %}
                            <tr>
                                <td>{{ loop.index + attendance|length }}</td>
                                <td>{{ entry.employee_name }}</td>
                                <td>
                                    {% if entry.field_clockin !=None %}
                                        {{ entry.field_in }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if entry.field_in_location_name !=None %}
                                        {{ entry.field_in_location_name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if entry.field_out !=None %}
                                        {{ entry.field_out }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if entry.field_out_location_name !=None %}
                                        {{ entry.field_out_location_name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>Field Attendance</td>
                                <td>{% if entry.total_duration %}{{ entry.total_duration }}{% else %} - {% endif %}</td>
                                <td>
                                    <a href="#" class="template-link" data-template-url="{{ url_for('attendance.void_attendance_record', attendance_id=entry.attendance_id) }}" class="btn btn-primary">Edit</a>
                                </td>                                    
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <script src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
</body>
</html>
