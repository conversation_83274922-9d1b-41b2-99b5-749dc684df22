<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/video.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">

    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>

    <title>Upload or Capture Employee Picture</title>
</head>
<body>

  <div class="real-form"> 
      <h1>Upload or Capture Employee Picture</h1>
      <form id="uploadForm" action="{{ url_for('attendance.create_subject') }}" method="post" enctype="multipart/form-data">
        
          {{ form.hidden_tag() }} <!-- CSRF token -->
          
          <!-- Hidden input to store the employee's name passed from the URL -->
          <input type="hidden" name="name" value="{{ request.args.get('name') }}">

          <!-- Option to capture image using camera -->
          <div class="vid-container" id="cameraSection" style="display: none;">
              <video id="video" width="300" height="200" autoplay></video>
              <button type="button" class="btn btn-capture captureButton" onclick="captureImage()"><i class="fas fa-camera"></i> Capture</button>
          </div>

          <!-- Display the captured image preview -->
          <div id="capturedImagePreview" style="display: none;">
              <h2>Captured Image Preview</h2>
              <img id="capturedImage" width="300" height="200">
              <button type="button" class="btn btn-upload-capture btn-custom" onclick="uploadCapturedImage()">Upload Image</button>
          </div>

          <!-- Option to upload an image file -->
          <div id="uploadSection">
              <div class="form-group">
                  {{ form.image.label }}
                  <div class="input-group-text">
                    {{ form.image() }} 
                  </div>
              </div>
          </div>

          <!-- Toggle Button to Switch Between Capture and Upload -->
          <button type="button" class="btn btn-toggle" onclick="toggleUploadCapture()">
            <i class="fas fa-sync-alt"></i>    
            Switch to Capture Image
        </button>

          {{ form.submit(class="btn btn-custom", id="submit_one") }} 
      </form>
  </div>
  <div>
    {% if role == "hr" %}
        <a href="{{ url_for('admin_data.dashboard') }}" class="action-link">
            <i class="fas fa-arrow-left"></i>
             Back to Dashboard</a>
    {% else %}
        <a href="{{ url_for('admin_data.supervisor_dashboard') }}" class="action-link">
            <i class="fas fa-arrow-left"></i>
             Back to Dashboard</a>
    {% endif %}
  </div>

  <canvas id="canvas" style="display: none;"></canvas>

  <script>
      const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const cameraSection = document.getElementById('cameraSection');
      const uploadSection = document.getElementById('uploadSection');
      const capturedImagePreview = document.getElementById('capturedImagePreview');
      const submitButton = document.getElementById('submit_one');
      const capturedImage = document.getElementById('capturedImage');
      let isCaptureMode = false;
      let capturedBlob = null;  // Store the captured image blob
      var role = document.querySelector('.role');
        var roleContent = role.querySelector('p');
        var roleText = roleContent.innerText;
        console.log(roleText);
      // Toggle between Upload and Capture modes
      function toggleUploadCapture() {
          isCaptureMode = !isCaptureMode;
          if (isCaptureMode) {
              startCamera();
              cameraSection.style.display = 'block';
              uploadSection.style.display = 'none';
              capturedImagePreview.style.display = 'none';
              document.querySelector(".btn-toggle").innerText = "Switch to Upload Image";
          } else {
              stopCamera();
              cameraSection.style.display = 'none';
              uploadSection.style.display = 'block';
              capturedImagePreview.style.display = 'none';
              document.querySelector(".btn-toggle").innerText = "Switch to Capture Image";
          }
      }

      // Start the camera
      function startCamera() {
          navigator.mediaDevices.getUserMedia({ video: true })
              .then(stream => {
                  video.srcObject = stream;
              })
              .catch(error => {
                  console.error("Error accessing camera:", error);
                  alert("Camera access is required to use the capture feature.");
              });
      }

      // Stop the camera
      function stopCamera() {
          let stream = video.srcObject;
          if (stream) {
              stream.getTracks().forEach(track => track.stop());
          }
      }

      // Capture the image from the camera
      function captureImage() {
          const context = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          context.drawImage(video, 0, 0, canvas.width, canvas.height);

          canvas.toBlob(blob => {
              capturedBlob = blob;  // Store the blob for upload
              displayCapturedImage(blob);
          }, 'image/png');
      }

      // Display the captured image in the preview section
      function displayCapturedImage(blob) {
          const imageUrl = URL.createObjectURL(blob);
          capturedImage.src = imageUrl;
          submitButton.style.display = 'none';
          capturedImagePreview.style.display = 'block';  // Show the captured image preview
      }
      // Upload the captured image
    
      async function uploadCapturedImage() {
        if (capturedBlob) {
            const formData = new FormData(document.getElementById('uploadForm'));
            formData.set('image', capturedBlob, 'captured.png');  // Replacing the file input with the captured image

            try {
                const response = await fetch("{{ url_for('attendance.create_subject') }}", {
                    method: 'POST',
                    body: formData, 
                });
                const result = await response.json();
                console.log(result);
                // stop the camera  
                stopCamera();
                alert(result.message || "Image uploaded successfully.");
                if (["hr", "accountant", "manager", "company_hr"].includes(roleText.toLowerCase())) {
                    window.location.href = '/hr_dashboard';
                } else {
                    window.location.href = '/supervisor_dashboard';
                }
            } catch (error) {
                console.error("Upload failed:", error);
                alert("Error While uploading the Image try again")
            }
        } else {
            alert("No image captured to upload.");
            
        }
    }

  </script>
</body>
</html>