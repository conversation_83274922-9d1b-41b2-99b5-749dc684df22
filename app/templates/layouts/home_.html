<!DOCTYPE html>
<html>
<head>
    <title>{{ title }} | HR and Payroll Solution in Rwanda</title>
    <!--Preconnects-->
    <link rel="preload" href="{{ url_for('static', filename='images/system_images/hero_image.svg') }}" as="image" type="image/svg+xml">
    <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <link rel="icon" href="{{ url_for('static', filename='images/system_images/Favicon_s.png') }}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/system_images/touch-icon.png')}}">
    <meta charset="utf-8">
    <meta name="author" content="Netpipo">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="NETPIPO is a leading payroll and HR management solution in Rwanda, designed to automate payroll processes and simplify HR tasks for businesses of all sizes.">
    <meta property="og:title" content="Netpipo HR + Payroll Solution in Rwanda">
    <meta property="og:description" content="NETPIPO is a leading payroll and HR management solution in Rwanda, designed to automate payroll processes and simplify HR tasks for businesses of all sizes.">
    <meta property="og:image" content="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Netpipo">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@netpipo">
    <meta name="twitter:creator" content="@netpipo">
    <meta name="twitter:title" content="Netpipo HR + Payroll Solution">
    <meta name="twitter:description" content="NETPIPO is a leading payroll and HR management solution in Rwanda, designed to automate payroll processes and simplify HR tasks for businesses of all sizes.">
    <!--Styling-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/form_messages.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/pricing.css') }}">
    <!--Third parties-->    
    <!--Google search console-->
    <meta name="google-site-verification" content="ZhYONfvUR83lNlVd1geMSbjjB9VYWYWQ8VEqiSUArDo"/>
    <!--Google recaptcha-->
</head>
<body class="">
    <div class="container mx-auto max-w-screen-xl">
        
        <header class="flex justify-between items-center px-4 ">

            <div class="logo">
                <a href="/">
                    <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/netpipo_logo_v2.png" alt="netpipo logo" class="w-32 min-w-[50px] min-h-[50px]">
                </a>
            </div>
            <div class="nav flex items-center font-bold dark hidden md:flex md:gap-7">
                <div class="flex flex-row gap-4 desktop-nav">
                    <a href="/home">Home</a>
                    <a href="/about">About</a>
                    <a href="/pricing">Pricing</a>
                    <a href="/contact">Contact</a>
                    <a href="/blog_page">Blog</a>
                </div>
                
                <!-- More Button Dropdown -->
                <div class="relative inline-block text-left ml-4">
                    <div>
                        <div class="flex flex-row justify-center items-center cursor-pointer" id="more-menu">
                            <p>More</p>
                            <i class="fi fi-rr-caret-down"></i>                        
                        </div>
                    </div>
                    <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" id="more-dropdown" role="menu" aria-orientation="vertical" aria-labelledby="more-menu">
                        <div class="" role="none">
                            <a href="/docs" class="block p-4 text-sm min-dark hover:bg-gray-100 hover:text-gray-900" role="menuitem">User guide</a>
                            <a href="/features" class="block p-4 text-sm min-dark hover:bg-gray-100 hover:text-gray-900" role="menuitem">All features</a>
                            <!--<a href="/payroll_calculator" class="block p-4 text-sm min-dark hover:bg-gray-100 hover:text-gray-900" role="menuitem">Payroll calculator</a>-->
                        </div>
                    </div>
                </div>

                <!-- Account Button Dropdown -->
                <div class="relative inline-block text-left ">
                    <div>
                        <div class="icon" id="account-menu">
                            <i class="fi fi-rr-user"></i>
                        </div>
                    </div>
                    <div class="origin-top-right absolute left-3 top-5 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" id="account-dropdown" role="menu" aria-orientation="vertical" aria-labelledby="account-menu">
                        <div class="" role="none">
                            <a href="/register_user" class="block px-4 p-4 text-sm min-dark  hover:bg-gray-100 hover:text-gray-900" role="menuitem">Sign up</a>
                            <a href="/login_user" class="block px-4 p-4 text-sm min-dark  hover:bg-gray-100 hover:text-gray-900" role="menuitem">Sign in</a>
                            <a href="{{ url_for('company_users.login_company_users') }}" class="block p-4 text-sm min-dark hover:bg-gray-100 hover:text-gray-900" role="menuitem">Self-service</a>
                        </div>
                    </div>
                </div>

            </div>
            <div class="flex flex-col gap-4">
                <div class="flex flex-row items-center justify-between">
                    <div class="logo-phone hidden">
                        <a href="/">
                            <img src="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}" alt="netpipo logo" class="w-20 min-w-[50px] min-h-[50px]">
                        </a>
                    </div>
                    <!-- Mobile Menu Button -->
                    <div class="md:hidden flex items-center justify-end mt-2 mr-5">
                        <button id="mobile-menu-btn" class="text-gray-700 focus:outline-none">
                            <i class="fi fi-rr-menu-burger text-2xl"></i>
                        </button>
                    </div>
                    <!--Logo-->
                </div>
                <!-- Mobile Navigation -->
                <nav id="mobile-menu" class="hidden flex-col items-center gap-4 mt-4 text-gray-700 md:hidden">
                    <div class="flex flex-col w-screen">
                        <a href="/home" class="hover:text-green-400">Home</a>
                        <a href="/about" class="hover:text-green-400">About</a>
                        <a href="/features" class="hover:text-green-400">Features</a>
                        <a href="/pricing" class="hover:text-green-400">Pricing</a>
                        <a href="/contact" class="hover:text-green-400">Contact</a>
                        <a href="/blog_page" class="hover:text-green-400">Blog</a>
                    </div>
                    <!-- Mobile "More" Dropdown -->
                    <div>
                        <button id="mobile-more-menu" class="flex items-center gap-2">
                            More <i class="fi fi-rr-angle-down"></i>
                        </button>
                        <div id="mobile-more-dropdown" class="hidden mt-2">
                            <a href="/docs" class="block px-4 py-2 text-gray-600 hover:bg-gray-100">User guide</a>
                            <a href="/features" class="block px-4 py-2 text-gray-600 hover:bg-gray-100">All features</a>
                            <!--<a href="/payroll_calculator" class="block px-4 py-2 text-gray-600 hover:bg-gray-100">Payroll calculator</a>-->
                        </div>
                    </div>

                    <!-- Mobile "Account" Dropdown -->
                    <div>
                        <button id="mobile-account-menu" class="flex items-center gap-2 ">
                            Account <i class="fi fi-rr-angle-down"></i>
                        </button>
                        <div id="mobile-account-dropdown" class="hidden mt-2">
                            <a href="/register_user" class="block px-4 py-2 text-gray-600 hover:bg-gray-100">Sign up</a>
                            <a href="/login_user" class="block px-4 py-2 text-gray-600 hover:bg-gray-100">Sign in</a>
                            <a href="{{ url_for('company_users.login_company_users') }}" class="block p-4 text-sm min-dark hover:bg-gray-100 hover:text-gray-900" role="menuitem">Self-service</a>
                        </div>
                    </div>
                </nav>
            </div>

        </header>

        <main class="main">
        <section id="subscription-popup" class="hidden w-screen h-screen sticky top-4 flex inset-0 items-center justify-center bg-gray-900 bg-opacity-50 z-50">
            <div class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full text-center relative">
                <button id="close-popup" class="absolute top-0 right-2 text-red-500 hover:text-red-200 text-3xl">&times;</button>
                <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/netpipo_logo_v2.png" alt="Netpipo logo" class="w-24 mx-auto mb-4">  
                <h2 class="text-xl font-bold mb-4 mid-dark mt-6">Stay compliant and informed!</h2>
                <p class="text-gray-600 mb-4">Sign up for our newsletter to receive tax updates, industry-leading payroll advice, and HR best practices. </p>
                <form id="subscription-popup-form">
                    <input type="email" id="popup-email" class="w-full p-2 border  rounded mb-4 focus:outline-none focus:ring-2 focus-within:ring-green-200" placeholder="Enter your email" required />
                    <button type="submit" class="w-full primary-background p-2 rounded hover:bg-blue-700">Subscribe</button>
                </form>
            </div>
        </section>

        <article class="relative">
            {% block content %}
            {% endblock %}
        </article>
        </main>
    </div>
    <button id="scrollToTopBtn" class="scroll-to-top-btn">
        <i class="fi fi-rr-arrow-up"></i>
    </button>  
    <footer class="">
        <div class="mx-auto w-full max-w-screen-xl">
            <div class="grid grid-cols-2 gap-8 py-6 lg:py-8 md:grid-cols-4">
                <div>
                    <h2 class="mb-6 text-sm font-semibold uppercase dark:text-white">Company</h2>
                    <ul class="dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="/about">About us</a>
                        </li>
                        <li class="mb-4">
                            <a href="/blog_page">Blog</a>
                        </li>
                        <li class="mb-4">
                            <a href="/terms">Terms and Conditions</a>
                        </li>
                        <li class="mb-4">
                            <a href="/privacy">Privacy Policy</a>
                        </li>
                        <li class="mb-4">
                            <a href="/quickbooks_connect">Connect to QuickBooks</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold uppercase dark:text-white">Services</h2>
                    <ul class="dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="/features">Payroll Management</a>
                        </li>
                        <li class="mb-4">
                            <a href="/features">HR Management</a>
                        </li>
                        <li class="mb-4">
                            <a href="/about">Community</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold uppercase dark:text-white">RRA</h2>
                    <ul class="dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="https://etax.rra.gov.rw/" target="_blank">Payroll tax declaration</a>
                        </li>
                        <li class="mb-4">
                            <a href="https://www.rra.gov.rw/en/useful-links/brochures" target="_blank">Tax Brochures</a>
                        </li>
                        <li class="mb-4">
                            <a href="https://www.rra.gov.rw/en/publications?tx_news_pi1%5Baction%5D=detail&tx_news_pi1%5Bcontroller%5D=News&tx_news_pi1%5Bnews%5D=1104&cHash=1d11517584d86df3897425857116f4d6" target="_blank">Tax Handbook</a>
                        </li>
                        <li class="mb-4">
                            <a href="https://www.rra.gov.rw/en/taxes-fees/domestic-taxes/withholding-taxes" target="_blank">Withholding Taxes</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold uppercase dark:text-white">RSSB</h2>
                    <ul class="dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="https://etax.rra.gov.rw/" target="_blank">Pay employee contributions</a>
                        </li>
                        <li class="mb-4">
                            <a href="https://online.rssb.rw/index1152.php?menu=employer" target="_blank">Apply for Employee Number</a>
                        </li>
                        <li class="mb-4">
                            <a href="https://www.imisanzu.rssb.rw/" target="_blank">Access your contributions</a>
                        </li>
                        <li class="mb-4">
                            <a href="https://etax.rra.gov.rw/tccCertiScreen#" target="_blank">RSSB Clearance certificate</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <section>
            <div class="max-w-screen-md mx-auto px-6 text-center">
                <h2>Subscribe to Netpipo newsletter</h2>
                <p>Get the latest payroll tax updates and exclusive content directly to your inbox.</p>
                <form id="subscription-footer-form" class="mt-6 flex flex-col sm:flex-row gap-3">
                    <input type="email" name="email" id="footer-email" placeholder="Enter your email address" required
                        class="w-full sm:auto flex-1 py-3 border rounded-lg focus:ring focus:ring-blue-100 outline-none p-6 text-gray-800"
                    >
                    <button type="submit" class="bg-white primary px-5 py-3 rounded-lg">Subscribe</button>
                </form>
            </div>
        </section>
        <div class="max-w-screen-md mx-auto py-4 text-center">
            <p>© 2025 NETPIPO. All rights reserved.</p>
        </div>
    </footer>  
<script src="{{ url_for('static', filename='scripts/pricing.js') }}"></script>
<script>
function toggleDropdown(buttonId, dropdownId) {
    document.getElementById(buttonId).addEventListener('click', function(event) {
        event.stopPropagation();
        const dropdown = document.getElementById(dropdownId);
        dropdown.classList.toggle('hidden');
    });

    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById(dropdownId);
        if (!dropdown.contains(event.target) && event.target.id !== buttonId) {
            dropdown.classList.add('hidden');
        }
    });

    document.querySelectorAll(`#${dropdownId} a`).forEach(function(item) {
        item.addEventListener('click', function() {
            document.getElementById(dropdownId).classList.add('hidden');
        });
    });
}

// Initialize dropdowns
toggleDropdown('account-menu', 'account-dropdown');
toggleDropdown('more-menu', 'more-dropdown');

// Mobile dropdowns
// Toggle Mobile Menu
document.getElementById('mobile-menu-btn').addEventListener('click', function () {
    document.getElementById('mobile-menu').classList.toggle('hidden');// toggle the hidden class
    // replace the haburger with a cross sign
    const icon = document.querySelector('#mobile-menu-btn i');
    icon.classList.toggle('fi-rr-menu-burger');
    icon.classList.toggle('fi-rr-cross-small');

    // show the mobile logo
    document.querySelector('.logo-phone').classList.toggle('hidden');
    
});

// Function to Toggle Dropdowns
function toggleDropdown(buttonId, dropdownId) {
    const button = document.getElementById(buttonId);
    const dropdown = document.getElementById(dropdownId);

    // Fetch all dropdowns ending with "-dropdown"
    const allDropdowns = document.querySelectorAll('[id$="-dropdown"]');

    button.addEventListener('click', function (event) {
        event.stopPropagation(); // Prevents the event from bubbling up the DOM tree

        // Close all other dropdowns
        allDropdowns.forEach(function (otherDropdown) {
            if (otherDropdown.id !== dropdownId) {
                otherDropdown.classList.add('hidden');
            }
        });

        // Toggle the current dropdown
        dropdown.classList.toggle('hidden');
    });

    // Close the dropdown when the user clicks outside the dropdown
    document.addEventListener('click', function (event) {
        if (!dropdown.contains(event.target) && event.target.id !== buttonId) {
            dropdown.classList.add('hidden');
        }
    });

    // Close the dropdown when a dropdown item is clicked
    document.querySelectorAll(`#${dropdownId} a`).forEach(function (item) {
        item.addEventListener('click', function () {
            dropdown.classList.add('hidden');
        });
    });
}


// Initialize Mobile Dropdowns
toggleDropdown('mobile-account-menu', 'mobile-account-dropdown');
toggleDropdown('mobile-more-menu', 'mobile-more-dropdown');

</script>
<script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/66d86a7e50c10f7a00a3f52d/1i6ulvvt4';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
</script>
<script src="{{ url_for('static', filename='scripts/subs.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='scripts/header.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='scripts/back-to-top.js')}}"></script> 
<script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<!--Google analytics-->
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-QHJBRK9MP7"></script>
<script>
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

gtag('config', 'G-QHJBRK9MP7');
</script>
<!-- Clarity analytics -->
<script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "o4482shycr");
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollToPlugin.min.js"></script>

<!--<script>
function getPositions() {
    if (window.innerWidth < 768) { // Mobile Screens
        return [
            { x: 100, y: 30, scale: 1.2 },  
            { x: 100, y: -50, scale: 1.2 },  
            { x: 100, y: 30, scale: 1.2 },
            // other items
        ];
    } else { // Larger Screens
        return [
            { x: 100, y: 50, scale: 2 },    
            { x: 100, y: -100, scale: 2 }, 
            { x: 100, y: 50, scale: 1.8 }   
        ];
    }
}


// Function to apply animations
function applyAnimations() {
    const positions = getPositions();

    gsap.utils.toArray(".hero_image").forEach((image, index) => {
        gsap.fromTo(image,
            { x: 0, y: 0, scale: 1, opacity: 0 }, 
            { 
                x: positions[index].x,
                y: positions[index].y,
                scale: positions[index].scale, 
                duration: 2,
                ease: "power2.out",
                opacity: 1,
                delay: index * 0.5 
            }
        );
    });

    const heroImages = document.querySelectorAll(".hero_image");

    heroImages.forEach((image, index) => {
        const textElement = image.querySelector(".hero-text");
        image.addEventListener("mouseenter", () => {
            gsap.to(image, {
                flex: 2,
                scale: positions[index].scale + 0.5, 
                duration: 0.5,
                ease: "power2.out",
                zIndex: 1
            });

            if (textElement) {
                gsap.fromTo(textElement, {
                    opacity: 1,
                    duration: 0.5,
                    y: 0,
                    x: 0,
                    ease: "power2.out"
                }, {
                    opacity: 1,
                    y: -5,
                    x: 0,
                    duration: 1,
                    ease: "power2.out"
                }
            
            );
            }

            heroImages.forEach((otherImage) => {
                if (otherImage !== image) {
                    gsap.to(otherImage, {
                        flex: 1,
                        opacity: 0.2,
                        duration: 0.5,
                        ease: "power2.out"
                    });
                }
            });
            const otherTextElements = document.querySelectorAll(".hero-text");
            otherTextElements.forEach((otherTextElement) => {
                if (otherTextElement !== textElement) {
                    gsap.to(otherTextElement, {
                        opacity: 0,
                        duration: 0.5,
                        ease: "power2.out"
                    });
                }
            });
        // make the rest of the screen darker
        gsap.to("body", {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            duration: 0.5,
            ease: "power2.out"
        });
        
        });

        image.addEventListener("mouseleave", () => {
            gsap.to(heroImages, {
                flex: 1,
                opacity: 1,
                scale: (i) => positions[i].scale, 
                duration: 0.5,
                ease: "power2.out",
                zIndex: 0
            });
            const textElements = document.querySelectorAll(".hero-text");
            textElements.forEach((textElement) => {
                gsap.to(textElement, {
                    opacity: 0,
                    duration: 0.5,
                    y: 0,
                    ease: "power2.out"
                });
            });
        });
    });
}

// Apply animations on load
applyAnimations();

// Reapply animations on resize
window.addEventListener("resize", applyAnimations);


</script>-->
</body>
</html>