<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet"
        href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='images/system_images/Favicon_s.png') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>    
    <link rel="stylesheet" href="{{ url_for('static', filename='css/popups.css') }}">
</head>
<body>
        <!-- Full-screen loader -->
    <div id="page-loader" class="page-loader">
        <div class="loader">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <p>Please wait while we're processing your data</p>
    </div>
    
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="dashboard">
        <div class="sidebar-container no-print">
            <img src="{{ url_for('static', filename='images/system_images/netpipo_logo_v2.png') }}" alt="netpipo logo"
                class="d-logo">
            <div class="sidebar">
                {% if role in ['hr', 'company_hr', 'accountant', 'manager'] %}
                <div class="side-buttons">
                    <p>Menu</p>
                    <ul>
                        <li class="">
                            <a href="/hr_dashboard">
                                <i class="fi fi-rr-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="employees">
                                    <i class="fi fi-rr-employees"></i>
                                    <span>Employees</span>
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="employees">
                                <li>
                                    <a href="{{ url_for('my_employees.register_employees') }}">
                                        <span>Add Employee</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('my_employees.employees_list') }}">
                                        <span>Active Employees</span>
                                    </a>
                                </li>
                                
                                <li>
                                    <a href="{{ url_for('my_employees.get_inactive_employees') }}">
                                        <span>Inactive Employees</span>
                                    </a>
                                </li>
                                
                            </div>
                        </div>
                        <li>
                            <a href="{{ url_for('departments_v2.departments') }}">
                                <i class="fi fi-rr-department-structure"></i>
                                <span>Departments</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="side-buttons">
                    <p>Payroll &amp; Benefits</p>
                    <ul>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="payroll">
                                    <i class="fi fi-rr-calculator-money"></i>
                                    <span>Payroll</span>
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="payroll">
                                <li>
                                    <a href="{{ url_for('payroll_summary_v2.payroll_summary') }}">
                                        <span>Process Payroll</span>
                                    </a>
                                </li>
                                <!--
                                <li>
                                    <a href="#">
                                        <span>Payroll Reports</span>
                                    </a>
                                </li>
                                -->
                                <li>
                                    <a href="{{ url_for('payroll_approval.bulk_payroll_approval')}}">
                                        <span>Pending payrolls</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('payroll_approval.approved_payroll_history')}}">
                                        <span>Approved payrolls</span>
                                    </a>
                                </li>
                            </div>
                        </div>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="benefits">
                                    <i class="fi fi-rr-money-bills-simple"></i>
                                    <span>Adjustments</span>
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="benefits">
                                <li>
                                    <a href="{{ url_for('reimbursements_v2.reimbursements') }}">
                                        <span>Reimbursements</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('deductions_v2.deductions') }}">
                                        <span>Other deductions</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('advance_requests_v2.view_advance_approvals') }}">
                                        <span>Advances</span>
                                    </a>
                                </li>
                            </div>
                        </div>
                    </ul>
                </div>
                {% if attendance_service %}

                <div class="side-buttons">
                    <p>Attendance &amp; Leave</p>
                    <ul>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="attendance">
                                    <i class="fi fi-rr-invite-alt"></i>
                                    Attendance
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="attendance">
                                <!--
                                <li>
                                    <a href="#">
                                        Clock In
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        Clock Out
                                    </a>
                                </li>
                                -->
                                
                                <li>
                                    <a href="{{ url_for('attendance_v2.attendance_records') }}">
                                        Attendance records
                                    </a>
                                </li>
                                
                                <li>
                                    <a href="/v2/timesheet" data-feature="timesheet">
                                        Timesheet
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('shifts_v2.view_shifts') }}">
                                        Shifts
                                    </a>
                                </li>
                            </div>
                        </div>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="leave">
                                    <i class="fi fi-rr-padlock-check"></i>
                                    Leave
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="leave">
                                <li>
                                    <a href="{{ url_for('leave_applications_v2.get_leave_application_for_employees') }}">
                                        Leave Requests
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('leave_applications_v2.view_approval_logs') }}">
                                        Leave Approved
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('attendance_v2.get_leave_records') }}">
                                        Leave Records
                                    </a>
                                </li>
                            </div>
                        </div>
                    </ul>
                </div>
                {% endif %}
                <div class="side-buttons">
                    <p>Documents (Files)</p>
                    <ul>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="documents">
                                    <i class="fi fi-rr-invite-alt"></i>
                                    Documents
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="documents">
                                <li>
                                    <a href="{{ url_for('document_v2.view_documents') }}">
                                        All files
                                    </a>
                                </li>
                            </div>
                        </div>
                    </ul>
                </div>
                <div class="side-buttons">
                    <p>System Settings</p>
                    <ul>
                        <li>
                            <a href="{{ url_for('user_data_v2.settings') }}">
                                <i class="fi fi-rr-settings"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        {% elif role == 'employee' %}
        <div class="side-buttons">
            <p>Menu</p>
            <ul>
                <li>
                    <a href="{{ url_for('admin_data.employee_dashboard') }}">
                        <i class="fi fi-rr-home"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
            </ul>
            {% if attendance_service %}
                <div class="side-buttons">
                    <p>Attendance</p>
                    <ul>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="attendance">
                                    <i class="fi fi-rr-invite-alt"></i>
                                    Attendance
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="attendance">
                                <li>
                                    <a href="{{ url_for('attendance.clockin') }}">
                                        Clock In
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('attendance.clockout') }}">
                                        Clock Out
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('field.field_clockin') }}">
                                        Field Clock In
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('field.field_clockout') }}">
                                        Field Clock Out
                                    </a>
                                </li>
                            </div>
                            </div>
                        </ul>
                    </div>
                {% endif %}
            <div class="side-buttons">
                <p>Leave</p>
                <ul>
                    <div class="feature-content">
                        <li>
                            <a href="#" data-feature="leave">
                                <i class="fi fi-rr-padlock-check"></i>
                                Leave Management
                                <i class="fi fi-rr-angle-small-down"></i>
                            </a>
                        </li>
                        <div class="feature-items" data-feature="leave">
                            <li>
                                <a href="{{ url_for('leave_applications.view_leave_applications') }}">
                                    Leave Requests
                                </a>
                            </li>
                            <li>
                                <a href="#">
                                    Leave Approved
                                </a>
                            </li>
                        </div> 
                    </div>
                </ul>
            </div>
            <div class="side-buttons">
                <p>Payroll &amp; Benefits</p>
                <ul>
                    <div class="feature-content">
                        <li>
                            <a href="#" data-feature="EE Benefits">
                                <i class="fi fi-rr-money-bills-simple"></i>
                                Benefits
                                <i class="fi fi-rr-angle-small-down"></i>
                            </a>
                        </li>
                        <div class="feature-items" data-feature="EE Benefits">
                            <li>
                                <a href="#">
                                    Reimbursements
                                </a>
                            </li>
                            <li>
                                <a href="#">
                                    Other deductions
                                </a>
                            </li>
                            <li>
                                <a href="#">
                                    Advances
                                </a>
                            </li>
                        </div>
                    </div>
                </ul>
                </div>
            <div class="side-buttons">
            <p>Documents (Files)</p>
            <ul>
                <div class="feature-content">
                    <li>
                        <a href="#" data-feature="documents">
                            <i class="fi fi-rr-invite-alt"></i>
                            Documents
                            <i class="fi fi-rr-angle-small-down"></i>
                        </a>
                    </li>
                    <div class="feature-items" data-feature="documents">
                        <li>
                            <a href="{{ url_for('document.view_documents') }}">
                                All files
                            </a>
                        </li>
                    </div>
                </div>
            </ul>
            </div>
        {% else %}
        <p>Coming soon</p>
        {% endif %}
        </div>
        <div class="header no-print">
            <div class="company-info no-print">
                <button id="toggle-sidebar" class="control-btn" title="Toggle Sidebar">
                    <i class="fi fi-rr-sidebar"></i>
                </button>
                
                <p>{{company_name}}</p>
                <i class="fi fi-rr-angle-small-down switch-company-toggle"></i>
                <div class="switch-company-form" style="display: none;">
                    <form method="POST" action="{{ url_for('company_data_v2.switch_company') }}">
                        <div class="form-row">
                            <div class="form-group">
                                <select name="company_id" class="form-select" required>
                                    <option value="" disabled selected>Select Company</option>
                                    {% for company in companies %}
                                    <div class="input-text-group">
                                        <option value="{{ company.company_id }}" class="form-control">{{ company.company_name }}
                                        </option>
                                    </div>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="submit-btn">
                                    Switch
                                </button>
                            </div>
                        </div>
                        <div class="action-links flex">
                            <a href="{{ url_for('company_data_v2.register') }}">
                            <i class="fi fi-rr-plus"></i> 
                                new
                            </a>
                    
                            <a href="{{ url_for('user_data.transfer_account_ownership') }}">
                                <i class="fi fi-rr-calendar-shift-swap"></i>
                                Transfer
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            <div class="search-bar text-box no-print">
                <input type="text" placeholder="Search" id="search-input" aria-label="Search">
                <i class="fi fi-rr-search"></i>
                <div class="search-results" id="search-results" style="display: none;"></div>
            </div>
            <div class="header-controls">
                <button id="toggle-fullscreen" class="control-btn" title="Toggle Fullscreen">
                    <i class="fi fi-rr-expand"></i>
                </button>
                <button id="toggle-dark-mode" class="control-btn" title="Toggle Dark Mode">
                    <i class="fi fi-rr-moon"></i>
                </button>
            </div>
            <div class="employee-info">
                <div class="user--info">
                    <i class="fi fi-rr-user icon primary"></i>
                    <p class="user-det">{{ first_name }} {{ last_name }}, </p>
                    <div class="user--role">
                        <div class="role">
                            <p>{{ role }}</p>
                        </div>
                    </div>
                    <div class="user--profile">
                        <i class="fi fi-rr-angle-small-down user-profile-toggle" aria-expanded="false" aria-controls="user-profile-menu"></i>
                        <ul class="user-profile-menu" style="display: none;">
                            <div class="on_smaller_screens">
                                <p>{{ first_name }} {{ last_name }}, </p>
                            </div>
                            <li>
                                <a href="{{ url_for('user_data_v2.user_profile') }}" class="flex">
                                    <i class="fi fi-rr-admin-alt"></i>
                                    <span>Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('user_data.logout') }}" class="flex">
                                    <i class="fi fi-rr-power"></i>
                                    <span>Logout</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="notification">
                    <!--
                    <i class="fi fi-rr-bell"></i>
                    <div class="red-dot"></div>
                    -->
                </div>
            </div>
        </div>
        <div class="dynamic-container">
            {% block content %}
            {% endblock %}
        </div>
        <div class="footer no-print">
            <p>Netpipo &copy; 2025</p>
        </div>
    </div>
    </div>
    <div id="timeoutPopup" class="popup-timeout" style="display: none;">
        <div class="popup-content-timeout">
            <h1>Session Timeout Warning</h1>
            <p>Your session will expire in <span id="countdown">30</span> seconds due to inactivity.</p>
            <div class="decisive-btn">
                <button id="stayLoggedIn" class="btn-continue">Stay Logged In</button>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/popupps.js') }}"></script>
    <!-- New script for loader -->
    <script>
        window.onload = function () {
            const loader = document.getElementById('page-loader');
            if (loader) {
                loader.style.display = 'none';
            }
        };
    </script>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/filters.js') }}"></script>
</body>
</html>