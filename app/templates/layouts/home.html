<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--fas fa icons-->
    <!--Tailwind css-->
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/loading.css') }}">
    <link rel="stylesheet" href= "{{ url_for('static', filename='styles/homepage.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/form_messages.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Icons+Outlined:opsz,wght@0..1,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/loading_overlay.js') }}"></script>
    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
    <title>Payroll and HR Management Software in Rwanda | Netpipo {{ tittle }}</title>
    <link rel="icon" href="{{ url_for('static', filename='images/system_images/logo.png') }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/pricing.css') }}">
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <!--Google search console-->
    <meta name="google-site-verification" content="ZhYONfvUR83lNlVd1geMSbjjB9VYWYWQ8VEqiSUArDo"/>
    <!--Google recaptcha-->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <!--Google analytics-->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QHJBRK9MP7"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-QHJBRK9MP7');
    </script>
    <!-- Clarity analytics -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "o4482shycr");
    </script>    
</head>
<body>
    <header class="heading" style="z-index: 10000;">
        <div class="big--heading">
            <nav>
                <div class="nav--logo">
                    <a href="{{ url_for('pages.home') }}"><img src="{{ url_for('static', filename='images/system_images/logo.png') }}" alt="logo"></a>
                    <h1>NETPIPO</h1>
                </div>
                <ul class="nav--links">
                    <li class="link"><a href="{{ url_for('pages.home') }}">Home</a></li>
                    <li class="link"><a href="{{ url_for('pages.about') }}">About us</a></li>
                    <li class="link"><a href="{{ url_for('pages.features') }}">Key features</a></li>
                    <li class="link"><a href="{{ url_for('pages.pricing') }}">Pricing</a></li>
                    <li class="link"><a href="{{ url_for('pages.docs') }}">User guide</a></li>
                    <li class="link"><a href="{{ url_for('payroll.payroll_calculator') }}">Payroll Calculator</a></li>
                    <li class="link"><a href="{{ url_for('pages.job_openings') }}">Jobs</a></li>
                    <li class="link"><a href="{{ url_for('pages.contact') }}">Contact us</a></li>
                    <li class="link"><a href="{{ url_for('blog_posts.blog_page') }}">Blog</a></li>
                <div class="nav--auth">
                    <li class="link signup"><a href="{{ url_for('user_data.register') }}">Sign up</a></li>
                    <li class="link login"><a href="{{ url_for('user_data.login') }}">Login</a></li>
                    <li class="link login"><a href="{{ url_for('company_users.login_company_users') }}">Self Service</a></li>
                </div>
                </ul>
            </nav>
        </div>
        <div class="top--nav">
            <div class="nav--logo">
                <a href="{{ url_for('pages.home') }}"><img src="{{ url_for('static', filename='images/system_images/logo.png') }}" alt="logo"></a>
                <h1>NETPIPO</h1>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <ul class="topnav" id="topnav">
                <div class="close-btn" id="close-btn">&times;</div>
                <li class="link"><a href="{{ url_for('pages.home') }}">Home</a></li>
                <li class="link"><a href="{{ url_for('pages.about') }}">About us</a></li>
                <li class="link"><a href="{{ url_for('pages.features') }}">Key features</a></li>
                <li class="link"><a href="{{ url_for('pages.contact') }}">Contact us</a></li>
                <li class="link"><a href="{{ url_for('pages.docs') }}">Documentation</a></li>
                <li class="link"><a href="{{ url_for('pages.job_openings') }}">Jobs</a></li>
                <li class="link"><a href="{{ url_for('blog_posts.blog_page') }}">Blog</a></li>
                <li class="link"><a href="{{ url_for('user_data.register') }}">Sign up</a></li>
                <li class="link"><a href="{{ url_for('user_data.login') }}">Login</a></li>
                <li class="link login"><a href="{{ url_for('company_users.login_company_users') }}">Self Service</a></li>
            </ul>
        </div>
    </header>
    <div class="middle--contents">
        {% block content %}
        {% endblock %}
    </div>
    <footer id="footer">
        <div class="contacts">
            <table>
                <thead aria-colspan="2">
                    <th>
                    </th>
                </thead>
                <tr>
                    <th>Company</th>
                    <th>Services</th>
                    <th>Support</th>
                </tr>
                <tr>
                    <td><a href="{{ url_for('pages.about') }}">About us</a></td>
                    <td><a href="{{ url_for('user_data.register') }}">Payroll Management</a></td>
                    <td><a href="{{ url_for('pages.contact') }}">Contact us</a></td>
                </tr>
                <tr>
                    <td><a href="https://www.linkedin.com/company/netpipo/" target="_blank">Careers</a></td>
                    <td><a href="{{ url_for('user_data.register') }}" target="_blank">HR Management</a></td>
                    <!--
                    <td><a href="#">FAQ</a></td>
                    -->
                </tr>
                <tr>
                    <td><a href="https://www.facebook.com/profile.php?id=61566453392067&mibextid=ZbWKwL" target="_blank">Blog</a></td>
                    <!--<td><a href="#">Recruitment</a></td>-->
                    <td><a href="{{ url_for('user_data.login') }}" target="_blank">Community</a></td>
                </tr>
                <tr>
                    <!--<td><a href="#">Testimonials</a></td>-->
                    <!--<td><a href="#">Employer of records</a></td>-->
                    <td><a href="https://www.facebook.com/profile.php?id=61566453392067&mibextid=ZbWKwL" target="_blank">Product updates</a></td>
                </tr>
            </table>
            <table>
                <thead aria-colspan="2">
                    <th>Useful links</th>
                </thead>
                <tr>
                    <th>RRA</th>
                    <th>RSSB</th>
                </tr>
                <tr>
                    <td><a href="https://etax.rra.gov.rw/">Payroll tax declaration</a></td>
                    <td><a href="https://etax.rra.gov.rw/">Pay employee contributions</a></td>
                </tr>
                <tr>
                    <td><a href="https://www.rra.gov.rw/en/useful-links/brochures">Tax Brochures</a></td>
                    <td><a href="https://online.rssb.rw/index1152.php?menu=employer">Apply for Employee Number</a></td>
                </tr>
                <tr>
                    <td><a href="https://www.rra.gov.rw/en/publications?tx_news_pi1%5Baction%5D=detail&tx_news_pi1%5Bcontroller%5D=News&tx_news_pi1%5Bnews%5D=1104&cHash=1d11517584d86df3897425857116f4d6">Tax Handbook</a></td>
                    <td><a href="https://www.imisanzu.rssb.rw/">Access your contributions</a></td>
                </tr>
                <tr>
                    <td><a href="https://www.rra.gov.rw/en/taxes-fees/domestic-taxes/withholding-taxes">Withholding Taxes</a></td>
                    <td><a href="https://etax.rra.gov.rw/tccCertiScreen#">RSSB Clearance certificate</a></td>
                </tr>

            </table>
            <table>
                <tr>
                    <th>Legal</th>
                    <th>Connect</th>
                </tr>
                <tr>
                    <td><a href="#" target="_blank">Affiliates and partners</a></td>
                    <td><a href="https://www.facebook.com/profile.php?id=61566453392067&mibextid=ZbWKwL" target="_blank"><i class="fa-brands fa-facebook"></i>Facebook</a></td>
                </tr>
                <tr>
                    <td><a href="{{ url_for('pages.pricing') }}" target="_blank">Prices and offers</a></td>
                    <td><a href="https://www.linkedin.com/company/netpipo/" target="_blank"><i class="fa-brands fa-linkedin"></i>LinkedIn</a></td>
                </tr>
                <tr>
                    <td><a href="{{ url_for('pages.privacy') }}" target="_blank">Privacy Policy</a></td>
                    <td><a href="https://x.com/netpipo_hq" target="_blank"><i class="fa-brands fa-x-twitter"></i>Twitter</a></td>
                </tr>
                <tr>
                    <td><a href="#" target="_blank">Terms of Service</a></td>
                    <td><a href="https://www.instagram.com/netpipo_hq/" target="_blank"><i class="fa-brands fa-instagram"></i>Instagram</a></td>
                </tr>
            </table>
        </div>
        <div class="footer--contents" style="border-top: 1px solid #fff; width: 100%; padding: 10px; display:flex; justify-content: center; font-weight: 800;">  
            <p style="color: white;">&copy; 2025 netpipo, POWERED by ACR. All Rights Reserved.</p>
        </div>
    </footer>
<script>
document.addEventListener("DOMContentLoaded", function() {
    const textElement = document.getElementById('text');
    textElement.style.display='none';
    const text = textElement.innerText;
    textElement.innerHTML = ''; // Clear the paragraph

    text.split(' ').forEach((word, wordIndex) => {
        const wordSpan = document.createElement('span'); // Create a container for each word
        word.split('').forEach((char, charIndex) => {
            textElement.style.display='block';

            const charSpan = document.createElement('span');
            charSpan.textContent = char;
            charSpan.style.animationDelay = `${(wordIndex * 0.2) + (charIndex * 0.02)}s`; // Adjust delay time for each word and character
            wordSpan.appendChild(charSpan);
        });

        textElement.appendChild(wordSpan);
        textElement.appendChild(document.createTextNode(' ')); // Add space between words
    });
});

</script>
<script>
    // JavaScript to toggle the nav menu and close button
    const hamburger = document.getElementById('hamburger');
    const topnav = document.getElementById('topnav');
    const closeBtn = document.getElementById('close-btn');

    hamburger.addEventListener('click', () => {
        topnav.classList.toggle('active'); // Toggles menu visibility
    });

    closeBtn.addEventListener('click', () => {
        topnav.classList.remove('active'); // Closes the menu when close button is clicked
    });
</script>
<script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/66d86a7e50c10f7a00a3f52d/1i6ulvvt4';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
</script>
<script src="{{ url_for('static', filename='scripts/pricing.js') }}"></script>
<!--
<script>
    document.addEventListener('keydown', function(event) {
        if (event.key === 'F12' || (event.ctrlKey && event.shiftKey && event.key === 'I')) {
            event.preventDefault();
        }
    });

    document.addEventListener('contextmenu', function(event) {
        event.preventDefault();
    });
</script>
-->
</body>
</html>

