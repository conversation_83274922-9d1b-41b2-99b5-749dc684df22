<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='styles/auth_forms.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <title>Update Deductions</title>
</head>
<body>
    <div class="dynamic--form">
        <h1>Update Deductions</h1>
        <form method="POST">
            {{ form.hidden_tag() }}
            <div class="form-group">
                {% for error in form.description.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
                {{ form.description.label(class="form-label") }}
                <div class="input-group-text">
                    <span class="material-symbols-outlined">local_hospital</span>
                    {{ form.description(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {% for error in form.deduction_amount.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
                {{ form.deduction_amount.label(class="form-label") }}
                <div class="input-group-text">
                    <span class="material-symbols-outlined">payment</span>
                    {{ form.deduction_amount(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {% for error in form.deduction_date.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
                {{ form.deduction_date.label(class="form-label") }}
                <div class="input-group-text">
                    <span class="material-symbols-outlined">date_range</span>
                    {{ form.deduction_date(class="form-control") }}
                </div>

            </div>
            <div class="form-group">
                {{ form.submit(class="btn-custom") }}
            </div>
        </form>
        <a href="{{ url_for('admin_data.dashboard') }}">Back to Dashboard</a>
    </div>
</body>
</html>
