
<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Deductions</h1>
            <a class ="template-link btn-edit" href="{{ url_for('deductions_v2.add_deductions') }}" >
                <i class="fi fi-rr-plus-small"></i> Deductions
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="large--table">
        <table class="table">
            <thead class="thead th-custom">
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Employee</th>
                    <th scope="col">Description</th>
                    <th scope="col">Deduction Amount</th>
                    <th scope="col">Deduction Date</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
                <tbody>
                    {% if deductions_list %}
                        {% for deduction in deductions_list %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ deduction.employee_name }}</td>
                            <td>{{ deduction.description }}</td>
                            <td>{{ Auxillary.format_amount(deduction.deduction_amount) }}</td>
                            <td>{{ deduction.deduction_date }}</td>
                            <td>
                                <div class="table-buttons">
                                    <a class="green" href="{{url_for('deductions_v2.update_deductions', deduction_id=deduction.deduction_id)}}"><i class="fi fi-rr-edit"></i></a>
                                    <a class="red" 
                                        href="{{url_for('deductions_v2.delete_deductions', deduction_id=deduction.deduction_id)}}"
                                        onclick="return confirm('Are you sure you want to delete this deduction?');">
                                        <i class="fi-rr-trash"></i>
                                    </a>
                                </div>              
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                    <tr style="text-align: center;">
                        
                        <td colspan="6">
                            <h3 class="mid-dark">No deductions found</h3>
                            <i class="fi fi-rr-drawer-empty mid-dark big"></i>
                            <p class="light">Please add a deduction to see it <a href="{{ url_for('deductions_v2.add_deductions') }}" class="blue bold">here</a></p>
                        </td>
                    </tr> 
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}