<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>List of Deductions</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{url_for('deductions_v2.deductions')}}">
            <i class="fas fa-list"></i> Deductions
        </a>
    </div>
    <h1>List of Deductions</h1>
    <table border="1">
        <thead>
            <tr>
                <th>Deduction ID</th>
                <th>Employee ID</th>
                <th>Description</th>
                <th>Deduction Amount</th>
                <th>Deduction Date</th>
            </tr>
        </thead>
        <tbody>
            {% for deduction in deductions %}
            <tr>
                <td>{{ deduction.deduction_id }}</td>
                <td>{{ deduction.employee_id }}</td>
                <td>{{ deduction.description }}</td>
                <td>{{ deduction.deduction_amount }}</td>
                <td>{{ deduction.deduction_date }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
