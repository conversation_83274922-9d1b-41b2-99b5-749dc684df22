<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class ="template-link btn-edit" href="#" data-template-url="{{ url_for('deductions.deductions') }}">                <i class="fas fa-list"></i> Deductions
        </a>
    </div>
    <div class="dynamic--form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
            <h1>Add new Deductions</h1>
                <div class="real--form">
                    <form method="post" action="{{ url_for('deductions.add_deductions')}}">
                        {{ form.csrf_token }}
                        <div class="form-row">
                                <div class="form-group col-sm-3">
                                    <label for="employee_select">Employee</label>
                                    <select class="input-group-text form-control" name="employee_select" id="employee_select">
                                        {% for employee in employees %}
                                            <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                    {{ form.employee_id }}
                                </div>
                                <div class="form-group col-sm-3">
                                        <label for="description" class="col-sm-6 col-form-label" >Description</label>
                                        <div class="input-group-text">
                                        {{ form.description(class="form-control", placeholder='The cause of deduction') }} 
                                        </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="amount" class="col-sm-6 col-form-label">Amount</label>
                                    <div class="input-group-text">
                                        {{ form.amount(class="form-control",placeholder="Amount given to the employee") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="deduction_date" class="col-sm-6 col-form-label">Deduction Date</label>
                                    <div class="input-group-text">
                                            <span>
                                                <span class="material-symbols-outlined icon">date_range</span>
                                            </span>
                                        {{ form.deduction_date(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                        {{ form.submit(class="btn-custom") }}

                    </form>
            </div>
        </div>
</body>
</html>