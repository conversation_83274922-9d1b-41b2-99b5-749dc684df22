<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='styles/tables.css') }}" rel="stylesheet">
    <title>New Deductions</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{url_for('deductions.add_deductions')}}">
            <i class="fas fa-plus"></i> New deduction
        </a>
    </div>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div>
    <div>
        <div class="dynamic--form">
            <div class="row">
                <h1>Deductions</h1>
                <i class="fas fa-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="The amount given to the employee, which will be deducted from their take-home pay in the same month the deduction was applied."></i>
            </div>
        <table class="table">
            <thead class="thead th-custom">
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Employee</th>
                    <th scope="col">Description</th>
                    <th scope="col">Deduction Amount</th>
                    <th scope="col">Deduction Date</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            
                <tbody>
                    {% if deductions_list %}
                        {% for deduction in deductions_list %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ deduction.employee_name }}</td>
                            <td>{{ deduction.description }}</td>
                            <td>{{ Auxillary.format_amount(deduction.deduction_amount) }}</td>
                            <td>{{ deduction.deduction_date }}</td>
                            <td>
                                <div class="table-buttons">
                                    <a class="template-link btn-image" href="#" data-template-url="{{url_for('deductions.update_deductions', deduction_id=deduction.deduction_id)}}"><i class="fi fi-rr-pencil"></i> Edit</a>
                                    <a class="btn-cancel" href="{{url_for('deductions.delete_deductions', deduction_id=deduction.deduction_id)}}"><i class="fi-rr-trash"></i> Delete</a>
                                </div>              
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                    <tr style="text-align: center;">
                        <td colspan="6">No deductions found</td>
                    </tr> 
                    {% endif %}
                </tbody>
 
        </table>
    </div>
    </div>
</div>
        <script>  
        document.getElementById('employee_select').addEventListener('change', function() {
            document.getElementById('employee_id').value = this.value;
        });
        </script>
        <script src="{{ url_for('static', filename='scripts/popup.js') }}"></script>
        
</body>
</html>