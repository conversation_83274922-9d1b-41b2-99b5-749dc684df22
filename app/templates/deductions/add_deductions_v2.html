<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('deductions_v2.deductions')}}" >
                    <i class="fi fi-rr-list"></i> Deductions
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>Add new Deductions</h1>
                <div class="real--form">
                    <form method="post" action="{{ url_for('deductions_v2.add_deductions')}}">
                        {{ form.csrf_token }}
                        <div class="form-row">
                                <div class="form-group col-sm-3">
                                    <label for="employee_select">Employee</label>
                                    <select class="input-group-text form-control" name="employee_select" id="employee_select">
                                        {% for employee in employees %}
                                            <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                    {{ form.employee_id }}
                                </div>
                                <div class="form-group col-sm-3">
                                        <label for="description" class="col-sm-6 col-form-label" >Description</label>
                                        <div class="input-group-text">
                                        {{ form.description(class="form-control", placeholder='The cause of deduction') }} 
                                        </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="amount" class="col-sm-6 col-form-label">Amount</label>
                                    <div class="input-group-text">
                                        {{ form.amount(class="form-control",placeholder="Amount given to the employee") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="deduction_date" class="col-sm-6 col-form-label">Deduction Date</label>
                                    <div class="input-group-text">
                                        {{ form.deduction_date(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="submit-btn">Save</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
