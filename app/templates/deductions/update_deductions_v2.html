<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('deductions_v2.deductions')}}" >
                    <i class="fi fi-rr-list"></i> Deductions
                </a>
                <a class ="template-link btn-edit" href="{{ url_for('deductions_v2.add_deductions')}}" >
                    <i class="fi fi-rr-plus-small"></i> Deductions
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>Update Deductions</h1>
                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="form-group">
                        {% for error in form.description.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {{ form.description.label(class="form-label") }}
                        <div class="input-group-text">
                            {{ form.description(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {% for error in form.deduction_amount.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {{ form.deduction_amount.label(class="form-label") }}
                        <div class="input-group-text">
                            {{ form.deduction_amount(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {% for error in form.deduction_date.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {{ form.deduction_date.label(class="form-label") }}
                        <div class="input-group-text">
                            {{ form.deduction_date(class="form-control") }}
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        {{ form.submit(class="submit-btn") }}</div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}