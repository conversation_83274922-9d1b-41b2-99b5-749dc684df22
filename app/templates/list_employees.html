<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    
    <title>Employees List</title>
    <!-- Include Bootstrap CSS or other stylesheets here if needed -->
    <div class="dynamic--form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                        {% for category, message in messages %}
                            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                            </li>
                        {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <h1 class="text-center">Employees' Data</h1>
        <div class="large--table">
            <div>
                <table class="table" id="list_employees">
                    <thead class="thead th-custom">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">First Name</th>
                            <th scope="col">Last Name</th>
                            <th scope="col">NID</th>
                            <th scope="col">Phone</th>
                            <th scope="col">Picture</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                            <tr>
                                <td>{{ loop.index }}</td> <!-- Indexing starts from 1 -->
                                <td>{{ employee.first_name }}</td>
                                <td>{{ employee.last_name }}</td>
                                <td>{{ employee.nid }}</td>
                                <td>{{ employee.phone }}</td>
                                <td>
                                    {% if employee.image_url %}
                                        <!-- Display employee image if available -->
                                    <div class="round-container">                               
                                        <img src="{{ url_for('attendance.proxy_image', image_url=employee.image_url) }}" alt="Image of {{ employee.first_name }}">
                                    </div> 
                                        {% else %}
                                        N/A <!-- Display 'N/A' if no image is available -->
                                    {% endif %}                           
                                </td>
                                <td>
                                    <div class="table-buttons">
                                        {% if attendance_service %}
                                            <!-- Link to create subject page with employee name -->
                                            <a class="template-link btn-image" href="#" data-template-url="{{ url_for('attendance.create_subject', name=employee.employee_id ~ ' ' ~ employee.first_name ~ ' ' ~ employee.last_name) }}">
                                                <i class="fi fi-rr-add-image"></i>
                                                Picture</a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
{% endblock %}
