<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
{% block content %}
<section class="relative w-full max-w-4xl mx-auto p-6">
    <!-- Background Image -->
    <div class="">
        <!-- Glassmorphism Form Overlay -->
        <div class="flex justify-center items-center ">
            <form method="POST" action="{{ url_for('pages.contact') }}" 
                  class="w-full max-w-lg p-6 glass-container">
                {{ form.hidden_tag() }}
                {{ form.csrf_token() }}
                <h1 class="text-2xl font-semibold dark text-center">Get in Touch</h1>
                <p class="dark text-center text-sm mb-4">Fill out the form below to send us a message</p>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul>
                    {% for category, message in messages %}
                        <li class="p-3 mb-2 dark rounded {{ 'bg-green-500' if category == 'success' else 'bg-red-500' }}">
                            {{ message }}
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
                {% endwith %}
                <div class="space-y-4 dark">
                    <div class="dark">
                        {{ form.name.label(class="block text-sm ") }}
                        {{ form.name(class="w-full p-2 mt-1 bg-white/20 border border-white/50 rounded-lg dark placeholder-gray-200 focus:ring focus:ring-blue-300") }}
                    </div>

                    <div class="dark">
                        {{ form.email.label(class="block text-sm ") }}
                        {{ form.email(class="w-full p-2 mt-1 bg-white/20 border border-white/50 rounded-lg dark placeholder-gray-200 focus:ring focus:ring-blue-300") }}
                    </div>

                    <div class="dark">
                        {{ form.phone.label(class="block text-sm ") }}
                        {{ form.phone(class="w-full p-2 mt-1 bg-white/20 border border-white/50 rounded-lg dark placeholder-gray-200 focus:ring focus:ring-blue-300") }}
                    </div>

                    <div class="dark">
                        {{ form.message.label(class="block text-sm ") }}
                        {{ form.message(class="w-full p-2 mt-1 bg-white/20 border border-white/50 rounded-lg dark placeholder-gray-200 h-32 focus:ring focus:ring-blue-300") }}
                    </div>

                    <!-- reCAPTCHA -->
                    <div class="mt-4 max-w-full">
                        <div class="g-recaptcha" data-sitekey="{{ captcha_public_key }}"></div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="w-full p-3 primary bg-white rounded-lg hover:ring-opacity-5 transition">
                        Send Message
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- Contact Info -->
    <div class="mt-8 text-left space-y-6">
        <div class="flex items-center justify-center space-x-2">
            <i class="fi fi-rr-marker"></i>           
            <h5 class="font-semibold">Address</h5>
            <p class="text-gray-600">2 KG 167 Street, Kimironko Near Simba Super Market</p>
        </div>

        <div class="flex items-center justify-center space-x-2 dark">
             <i class="fi fi-rr-mobile-button"></i>            
            <h5 class="font-semibold">Phone</h5>
            <p class="text-gray-600">+250 787 028 385</p>
        </div>
        <div class="flex items-center justify-center space-x-2 dark">
            <i class="fi fi-rr-newsletter-subscribe"></i>            
            <h5 class="font-semibold">Email</h5>
            <p class="text-gray-600"><EMAIL></p>
        </div>
        <!-- Social Media Links -->
        <div class="flex justify-center space-x-4 text-gray-500 text-2xl mt-4">
            <a href="https://www.facebook.com/profile.php?id=61566453392067&mibextid=ZbWKwL" target="_blank" class="hover:text-blue-600"><i class="fa-brands fa-facebook"></i></a>
            <a href="https://www.linkedin.com/company/netpipo/" target="_blank" class="hover:text-blue-600"><i class="fa-brands fa-linkedin"></i></a>
            <a href="https://x.com/netpipo_hq" target="_blank" class="hover:text-blue-600"><i class="fa-brands fa-x-twitter"></i></a>
            <a href="https://www.instagram.com/netpipo_hq/" target="_blank" class="hover:text-blue-600"><i class="fa-brands fa-instagram"></i></a>
        </div>
    </div>
</section>

{% endblock %}

