<!DOCTYPE html>
{% extends 'layouts/home.html' %}
{% block content %}
        <div class="docs--content">
            <h1>NETPIPO Documentation</h1>
            <h2>1. Introduction to Netpipo</h2>
            <p>NETPIPO is a cloud-based HR and payroll management system that helps businesses of all sizes in Rwanda automate their payroll processes, ensure compliance with tax regulations, and simplify HR management. Our innovative platform is designed to streamline payroll operations, reduce errors, and save time, allowing businesses to focus on growth and success.</p>
            <h2>2. Key-features</h2>
            <ol>
                <li>Employees Management</li>
                <li>Auto-generate Payroll lists</li>
                <li>Payroll Tax Calculations</li>
                <li>Monthly Payroll Tax Summary report</li>
                <li>Automatic RRA PAYE Annexure generation</li>
                <li>RSSB: Pension, Maternity, RAMA, CBHI Annexures</li>
                <li>Other deductions(Advances and Installments)</li>
                <li>Reimbursements Management</li>
                <li>Medical insurance Management</li>
            </ol>
            <h2>3. Getting Started</h2>
            <div class="docs-container">
                <div class="contents-container">
                    <h3>Sign up Process</h3>
                    <p>To get started with NETPIPO, you need to sign up for an account. Follow these steps to create an account:</p>
                    <ol>
                        <li>Visit the NETPIPO website at <a href="{{ url_for('pages.home') }}">www.netpipo.com</a></li>
                        <li>Navigate to the sign up page <a href="{{ url_for('user_data.register') }}">Sign up</a></li>
                        <li>Fill in the required information, including your name, email address,phone number, and password</li>
                        <li>Click on the "Register button to create your account</li>
                        <li>Check your email for a verification link and click on it to activate your account</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/signup.png') }}" alt="sign up page photo" loading="lazy">
                </div>
            </div>
            <div class="docs-container">
                <div class="contents-container">
                    <h3>Login Process</h3>
                    <h4>Step 1</h4>
                    <p>Once you have created an account, you can log in to the NETPIPO platform using your email address and password. Follow these steps to log in:</p>
                    <ol>
                        <li>Visit the NETPIPO website at <a href="{{ url_for('user_data.login') }}">Login</a></li>
                        <li>Enter your email address and password in the login form</li>
                        <li>Click on the "Login" button to access your account</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/login.png') }}" alt="login" loading="lazy">
                </div>
            </div>
            <h4>2-Factor Authentication</h4>
            <p>NETPIPO uses two-factor authentication to enhance the security of your account. you will need an authenticator app downloaded</p>
            <ol>
                <li>Download an authenticator app like Google Authenticator on your phone</li>
                <li>open the app and Click on the plus icon</li>
                <li>Scan the QR code displayed on the screen</li>
                <li>Enter the code generated by the authenticator</li>
                <li>Click on the "Verify" button to complete the 2-factor authentication process</li>
            </ol>
            <div class="docs-container">
                <div class="image-container">
                    <h2>STEP 1</h2>
                    <img src="{{ url_for('static', filename='images/system_images/add_netpipo.png') }}" alt="2fa" loading="lazy">
                </div>
                <div class="image-container">
                    <h2>STEP 2</h2>
                    <img src="{{ url_for('static', filename='images/system_images/scan_the_qr_code.png') }}" alt="2fa" loading="lazy">
                </div>
            </div>
            <h2>4. Company registration</h2>
            <p>Before you can start using NETPIPO to manage your payroll and HR processes, you need to register your company on the platform. Follow these steps to register your company:</p>
            <div class="docs-container">
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/register_company.png') }}" alt="company registration" loading="lazy">
                </div>
                <div class="contents-container">
                    <ol>
                        <li>After verifying your OTP code, you will be redirected to register company if you do not have one created</li>
                        <li>Fill in the required information, including your company name, address, and tax identification number, etc...</li>
                        <li>Click on the "Register" button to complete the registration process</li>
                    </ol>
                </div>
            </div>
            <h2>5. Dashboard Overview</h2>
            <p>Once you have logged in to your NETPIPO account, and you have a company registered, you will be taken to the dashboard, where you can access all the features and functionalities of the platform. The dashboard provides an overview of your payroll processes, employee data, and tax compliance status, allowing you to manage your HR and payroll operations efficiently.</p>
            <div class="docs-container">
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/dashboard.png') }}" alt="dashboard" loading="lazy">
                </div>
                <div class="contents-container">
                    <p>The dashboard displays key information such as:</p>
                    <ul>
                        <li>Employees Number</li>
                        <li>Payroll Expense</li>
                        <li>Payroll &amp; Contributions</li>
                        <li>Net Salaries</li>
                        <li>Gross Salaries</li>
                        <li>Total Staff cost</li>
                    </ul>
                </div>
            </div>
            <h2>6. Managing Settings</h2>
            <p>All the information that was saved during signing up, and registering company are displayed in the settings panel</p>
            <ol>
                <li>Navigate to the sidebar</li>
                <li>Select settings</li>
                <li>Update your information</li>
                <li>You can update the company settings too</li>
                <li>Upload company logo</li>
                <li>create company's departments</li>
                <li>
                    <h3>Create/Manage Users</h3>
                    <p>NETPIPO allows you to create and manage user accounts for your employees. Follow these steps to create and manage users:</p>
                    <ol>
                        <li>Navigate to the settings</li>
                        <li>Click on the "Users"</li>
                        <li>Click on the "Add User" button</li>
                        <li>Fill in the required information, including the user's name, email address, and role</li>
                        <li>Click on the "Add User" button to create the user account</li>
                    </ol>
                </li>
                <li>
                    <h3>Other Users</h3>
                    <p>NETPIPO allows you to create other users who will have access to the platform through their own dashboards. Follow these steps to create other users:</p>
                    <ol>
                        <li>Navigate to the settings</li>
                        <li>Under Attendance Management Section</li>
                        <li>Click on the "Users" button that will redirect you to the page where you can view/edit/delete the existing users or create new users</li>
                        <li>Click on create new user button</li>
                        <li>Fill in the required information, including the user's name, email address, and role</li>
                    </ol>
                </li>
                <li>
                    <h3>Approval workflow</h3>
                    <p>NETPIPO allows you to set up an approval workflow for payroll processing and leave management. Follow these steps to set up an approval workflow:</p>
                    <ol>
                        <li>Navigate to the settings</li>
                        <li>Under the Approval Workflow Section</li>
                        <li>Click on the "Add Approval Workflow" button</li>
                        <li>Fill in the required information, including the approver's name, and role</li>
                        <li>Click on the "Add Approval Workflow" button to set up the approval workflow</li>
                    </ol>
                </li>
            </ol>
            <div class="image-container">
                <img src="{{ url_for('static', filename='images/system_images/settings.png') }}" alt="settings" loading="lazy">
            </div>
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>7. Employee Resgistration</h2>
                    <h3>Single Employee registration</h3>
                    <p>Before you can start processing payroll, you need to add employees to the platform. Follow these steps to register employees:</p>
                    <ol>
                        <li>Click on the "Employees" tab on the dashboard</li>
                        <li>Click on the "Add Employee" button</li>
                        <li>Fill in the required information, including the employee's name, email address, phone number, and job title</li>
                        <li>Click on the "Register Employee" button to add the employee to the system</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/register_employee.png') }}" alt="employee registration" loading="lazy">
                </div>
                <div class="contents-container">
                    <h3>Multiple Employee registration</h3>
                    <p>If you have a large number of employees to register, you can use the bulk upload feature to add multiple employees to the platform at once. Follow these steps to register multiple employees:</p>
                    <ol>
                        <li>Click on the "Employees" tab on the dashboard</li>
                        <li>Click on the "Bulk Upload" button</li>
                        <li>Download the employee template file</li>
                        <li>Fill in the required information for each employee in the template file</li>
                        <li>Upload the completed template file to add multiple employees to the system</li>
                    </ol>
                </div>
                <div class="image-container-small">
                    <img src="{{ url_for('static', filename='images/system_images/upload_bulk_employees_netpipo.png') }}" alt="bulk upload" loading="lazy">
                </div>
            </div>
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>8. Processing Payroll</h2>
                    <p>Once you have registered employees on the platform, you can start processing payroll. Follow these steps to process payroll:</p>
                    <ol>
                        <li>Click on the "Payroll" tab on the dashboard</li>
                        <li>Click on the "Process Payroll" button</li>
                        <div class="image-container-small">
                            <img src="{{ url_for('static', filename='images/system_images/payroll_button_netpipo.png') }}" alt="process payroll" loading="lazy">
                        </div>
                        <li>Click on the "Payroll button"</li>
                        <li>Select the last date of the month that you want to process the payroll for</li>
                        <li>If your plan supports attendance management you will have to select if time attendance shall affect the payroll</li>
                        <li>Click on the "Generate" button to generate the payroll list</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/payroll_processing_netpipo.png') }}" alt="process payroll" loading="lazy">
                </div>
                <div class="contents-container">
                    <h2>9. Exporting annexures</h2>
                    <p>NETPIPO allows you to generate and export/download annexures for tax compliance and reporting purposes. Follow these steps to export annexures:</p>
                    <ol>
                        <li>Click on the "Export" while you are on the payroll summary panel</li>
                        <li>Select the type of annexure you want to export, such as RRA PAYE Annexure or RSSB Annexure</li>
                        <li>make sure that the pay period and the employees you want to include in the annexure are correctly recorded</li>
                        <li>Click on the "Export Annexure" button to generate and download the annexure</li>
                    </ol>
                    <p>for more activities like add payroll or save completed payroll, downloading Unified permanent Employees annexure, payroll summary and all Employees payslips</p>
                    <ol>
                        <li>Click on the "More" button</li>
                        <li>Select the activity you want to perform</li>
                    </ol>
                </div>
            </div>
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>10. Reimbursements</h2>
                    <p>NETPIPO allows you to manage employee reimbursements for expenses incurred during work. Follow these steps to manage reimbursements:</p>
                    <ol>
                        <li>Click on the "Reimbursements" tab on the dashboard</li>
                        <li>Click on the record Reimbursement" button</li>
                        <li>Fill in the required information, including the employee's name, the amount of the reimbursement, and the reason for the reimbursement</li>
                        <li>Click on the "Add Reimbursement" button to record the reimbursement</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/reimbursements_netpipo.png') }}" alt="reimbursements" loading="lazy">
                </div>
            </div>
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>11. Deductions</h2>
                    <p>Netpipo was built with the feature to manage the deductions upon net salary (net to pay) and those that can affect the gross salary</p>
                    <ol>
                        <li>Click on the "Deductions" tab on the dashboard</li>
                        <li>Click on the "+ New Deduction" button</li>
                        <li>Fill in the required information, including the employee's name, the amount of the deduction, and the reason for the deduction</li>
                        <li>Click on the "Add Deduction" button to record the deduction</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/deductions_netpipo.png') }}" alt="deductions" loading="lazy">
                </div>
            </div>
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>12. Medical Insurance</h2>
                    <p>if your company pays for employees' insurance NETPIPO allows you to manage employee medical insurance details, including coverage and contribution information. Follow these steps to manage medical insurance:</p>
                    <p><i class="fas fa-warning"></i>only RAMA insurance that is issued by the government of Rwanda, is being deducted from the gross salary</p>
                    <ol>
                        <li>Click on the "Medical Insurance" tab on the dashboard</li>
                        <li>Click on the "Add Medical Insurance" button</li>
                        <li>Fill in the required information, including the employee's name, the insurance provider, and the coverage details</li>
                        <li>Click on the "Add Medical Insurance" button to record the insurance details</li>
                    </ol>
                </div>
                <div class="image-container">
                    <img src="{{ url_for('static', filename='images/system_images/how_to_add_insurance_netpipo.png') }}" alt="medical insurance" loading="lazy">
                </div>
            </div>
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>13. Departments</h2>
                    <p>NETPIPO allows you to create departments within your company to organize employees and manage payroll processes more efficiently. Follow these steps to create departments:</p>
                    <ol>
                        <li>Click on the "Departments" tab on the dashboard</li>
                        <li>Click on the "Add Department" button</li>
                        <li>Fill in the department name</li>
                        <li>Click on the "Add Department" button to create the department</li>
                    </ol>
                </div>
            </div>
            <!--Attendance-->
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>14. Attendance</h2>
                        <div class="spaace">
                            <h3>1. Create Subject</h3>
                            <p>NETPIPO allows you to manage employee attendance and track working hours. Follow these steps to manage attendance:</p>
                            <ol>
                                <li>Make sure that your plan has the feature of attendance.</li>
                                <li>First and foremost, you need to have HR or supervisor role.</li>
                                <li>In the dashboard, click on employees list, in the action column, click on the image icon to create the employee's image subject.</li>
                                <div class="image-container">
                                    <img src="{{ url_for('static', filename='images/system_images/Subjects.png') }}" alt="create subject" loading="lazy">
                                </div>
                                <li>You are redirected to the page where you will select to upload the employee's image file, or take a snapshot right away, and save the Image</li>
                                <div class="image-container-small">
                                    <img src="{{ url_for('static', filename='images/system_images/create_subject.png') }}" alt="create subject" loading="lazy">
                                </div>
                                <li>Navigate to the sidebar</li>
                                <li>Click on the "Attendance" tab</li>
                                <div class="image-container-small">
                                    <img src="{{ url_for('static', filename='images/system_images/attendance-button.png') }}" alt="create subject" loading="lazy">
                                </div>
                                <ol>
                                    <li>Click on the "Clock in" button to Scan the employee face and click on capture</li>
                                    <li>Click on the "Clock out" button to Scan the employee face and click on capture</li>
                                    <li>Click on the "Attendance records" tab to view the attendance history</li>
                                    <li>Click on Timesheet to view the employee's monthly attendance history</li>
                                </ol>
                            </ol>
                            
                        </div>
                </div>
            </div>
            <!--Leave management-->
            <div class="main-doc-content">
                <h2>15. Leave Management</h2>
                <div class="accordion">
                    <div class="accordion-item" data-id="1">
                      <h2 class="accordion-header">HR</h2>
                        <div class="accordion-content">
                            <div class="image-container">
                                <img src="{{ url_for('static', filename='images/system_images/human-resources.png') }}" alt="leave management" loading="lazy">
                            </div>
                            <p>NETPIPO allows you to manage employee leave requests and track leave balances. Follow these steps to manage leave:</p>
                            <ol>
                                <li>Click on the "Leave" tab on the dashboard</li>
                                <li>Click on "Leave requests" to view and approve the requested leave applications</li>
                                <li>Click on "Leave approvals" to view the approved leave applications</li>
                                <li>Click on "Leave records" to view the leave history of employees</li>
                            </ol>
                        </div>
                    </div>
                    <div class="accordion-item" data-id="2">
                      <h2 class="accordion-header">Supervisor</h2>
                        <div class="accordion-content">
                            <div class="image-container">
                                <img src="{{ url_for('static', filename='images/system_images/supervisor.png') }}" alt="leave management" loading="lazy">
                            </div>
                            <p>Netpipo allows your supervisor to view and approve the leave requests of the employees. Follow these steps to manage leave:</p>
                            <ol>
                                <li>Click on the "Leave" tab on the dashboard</li>
                                <li>Click on "Leave requests" to view and approve the requested leave applications</li>
                                <li>Click on "Leave approvals" to view the approved leave applications</li>
                                <li>Click on "Leave records" to view the leave history of employees</li>
                            </ol>
                        </div>
                    <div class="accordion-item" data-id="3">
                      <h2 class="accordion-header">Employee</h2>
                        <div class="accordion-content">
                            <div class="image-container">
                                <img src="{{ url_for('static', filename='images/system_images/employees.png') }}" alt="leave management" loading="lazy">
                            </div>
                            <p>Netpipo allows your employees to request leave</p>
                            <ol>
                                <li>Click on the "Leave" tab on the dashboard</li>
                                <li>Click on "Request leave" to request leave</li>
                                <li>Fill in the required information, including the leave type, start date, end date, and reason for the leave</li>
                                <li>Click on the "Submit" button to submit the leave request</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!--Multiple Companies,a tip for individuals or firms that handles payroll preparation for more than one companies-->
            <div class="main-doc-content">
                <div class="contents-container">
                    <h2>16. Multiple Companies</h2>
                    <p>If you are an individual or firm that handles payroll preparation for more than one company, NETPIPO allows you to manage multiple companies from a single account. Follow these steps to manage multiple companies:</p>
                    <ol>
                        <li>Click on the "Switch Company" button on the dashboard</li>
                        <li>Select the company you want to switch to from the drop-down menu</li>
                        <li>You will be redirected to the dashboard of the selected company</li>
                    </ol>
                </div>
                <div class="image-container-small">
                    <img src="{{ url_for('static', filename='images/system_images/switch_company.png') }}" alt="switch company" loading="lazy">
                </div>
            </div>
            <div class="main-doc-content">
                <div class="notice">
                    <div class="notice-header">
                        <h2>17. Support</h2>
                    </div>
                    <p>If you need more information, guidance, or system training feel free to reach to our support team by sending us a message through our <a href="{{ url_for('pages.contact') }}"> contact form</a> or call +*********** 385</p>
                </div>
            </div>
        </div>
    </div>

{% endblock %}