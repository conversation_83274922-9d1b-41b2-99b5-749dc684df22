<!DOCTYPE html>
<html>
<head>
    <title>Add Approval Type</title>
</head>
<body>
    <h1>Add Approval Type</h1>
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul>
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <form  method="post">
       {{ form.csrf_token }}
        <p>
            {{ form.name.label }}<br>
            {{ form.name }}
        </p>
        <p>
            {{ form.description.label }}<br>
            {{ form.description }}
        </p>
        
        <p>
            {{ form.submit }}
        </p>
    </form>
    <h1>Availabe Types</h1>
    <table border="1">
        <tr>
            <th>Name</th>
            <th>Description</th>
            <th>Actions</th>
        </tr>
        {% for approval_type in approval_types %}
            <tr>
                <td>{{ approval_type.approval_type }}</td>
                <td>{{ approval_type.description }}</td>
                <td>
                    <a href="{{ url_for('approval_types.update_approval_type', id=approval_type.id) }}">Update</a> 
                    <a href="{{ url_for('approval_types.delete_approval_type', id=approval_type.id) }}">Delete</a>                  
                </td>
            </tr>
        {% endfor %}
    </table>
</body>
</html>