<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
                <div class="dynamic--buttons">
                    <a class="btn-edit template-link" href="{{ url_for('company_locations_v2.view_locations') }}">
                        <i class="fi fi-rr-list"></i> locations
                    </a>
                </div>
        </div>
    <div class="dyn_container">
            <div class="form--container">
                <h1>Update Location</h1>
                <form method="post">
                    {{ form.hidden_tag() }}
                    
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.site_name.label }}
                            <div class="input-group-text">
                                <i class="fas fa-building"></i>
                                {{ form.site_name(class="form-control") }}
                            </div>
                            {% for error in form.site_name.errors %}
                                <span style="color: red;">[{{ error }}]</span>
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            {{ form.location.label }}
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.location(class="form-control") }}
                            </div>
                            {% for error in form.location.errors %}
                                <span style="color: red;">[{{ error }}]</span>
                            {% endfor %}
                        </div>
                    </div>

                        {{ form.submit(class="submit-btn") }}
                </form>
            </div>
        </div>
    </div>
{% endblock %}
