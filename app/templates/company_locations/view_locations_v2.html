<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>sites Locations available</h1>
                <a class="template-link btn-edit" href="{{ url_for('company_locations_v2.add_locations') }}">
                    <i class="fi fi-rr-plus-small"></i> Add Location
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                    <table>
                        <tr>
                            <th>#</th>
                            <th>Location</th>
                            <th>Site Name</th>
                            <th>Actions</th>
                        </tr>
                        {% if sites %}
                            {% for location in sites %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ location.location }}</td>
                                    <td>{{ location.site_name }}</td>
                                    <td>
                                        <a href="{{url_for('company_locations_v2.update_location', site_id=location.site_id)}}" class="green"><i class="fi fi-rr-edit"></i></a>
                                    </td>
                                </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="3">No locations found</td>
                            </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
{% endblock %}


