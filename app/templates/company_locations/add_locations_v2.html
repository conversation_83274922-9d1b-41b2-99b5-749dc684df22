<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <style>
        #map { height: 200px; width: 100%; margin-top: 15px; }
        .input-group-text { display: flex; align-items: center; }
    </style>
        <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Set new site locations</h1>
                <a class="template-link btn-edit" href="{{ url_for('company_locations_v2.view_locations') }}">
                    <i class="fi fi-rr-list"></i> Locations
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="grey-container">
                <div class="upload-info">
                    <h5>Site Location Guidelines</h5>
                    <ol>
                        <li>Enter the site name, Enter the address (e.g., Kigali, Kicukiro, Gatenga)</li>
                        <li>Search for your site location using the search box.</li>
                        <li>Click on the map to set the exact location.</li>
                        <li>The latitude and longitude will be automatically filled in.</li>
                        <li>Drag the marker to adjust the location if needed.</li>
                        <li>Click "Save Location" to store the coordinates.</li>
                    </ol>
                </div>
            </div>

            <div class="form--container">
                <h1>Add new location</h1>
                <form action="{{ url_for('company_locations_v2.add_locations') }}" method="post">
                    {{ form.hidden_tag() }}

                    <div class="form-row">
                        <div class="form-group">
                            {{ form.site_name.label }}
                            <div class="input-group-text">
                                <i class="fas fa-building"></i>
                                {{ form.site_name(class="form-control", placeholder="Site name") }}
                            </div>
                            {% for error in form.site_name.errors %}
                            <span style="color: red;">[{{ error }}]</span>
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            {{ form.location.label }}
                            <div class="input-group-text">
                                <i class="fas fa-map"></i>
                                {{ form.location(class="form-control", placeholder="Address: ex: Kigali, Kicukiro, Gatenga") }}
                            </div>
                            {% for error in form.location.errors %}
                            <span style="color: red;">[{{ error }}]</span>
                            {% endfor %}
                        </div>
                    </div>

                    <h3>Choose Location on Map</h3>
                    <div class="flex-container">
                        <input type="text" id="search-box" placeholder="Search (KG 123 St)" class="form-control" style="margin-top: 10px;">
                        <button type="button" onclick="searchLocation()" class="submit-btn"><i class="fi fi-rr-search"></i>Search </button>
                    </div>

                    <div id="map"></div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="latitude">Latitude:</label>
                            {{ form.latitude(class="form-control", id="latitude", readonly="readonly") }}
                        </div>
                        <div class="form-group">
                            <label for="longitude">Longitude:</label>
                            {{ form.longitude(class="form-control", id="longitude", readonly="readonly") }}
                        </div>
                    </div>

                    {{ form.submit(class="submit-btn") }}
                </form>
            </div>
        </div>
            <script>
                let map = L.map('map').setView([-1.9501, 30.0588], 12);  // Default: Kigali, Rwanda
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', { attribution: '© OpenStreetMap' }).addTo(map);

                let marker = L.marker([-1.9501, 30.0588], { draggable: true }).addTo(map);

                function updateLatLng(lat, lng) {
                    document.getElementById('latitude').value = lat;
                    document.getElementById('longitude').value = lng;
                    marker.setLatLng([lat, lng]);
                }

                // Click to update latitude & longitude
                map.on('click', function(e) {
                    updateLatLng(e.latlng.lat, e.latlng.lng);
                });

                // Dragging marker updates coordinates
                marker.on('dragend', function(e) {
                    let pos = marker.getLatLng();
                    updateLatLng(pos.lat, pos.lng);
                });

                function searchLocation() {
                    let query = document.getElementById('search-box').value;
                    if (!query) {
                        alert("Please enter a location to search.");
                        return;
                    }

                    let url = `https://nominatim.openstreetmap.org/search?format=json&q=${query}`;

                    fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            if (data.length > 0) {
                                let lat = data[0].lat;
                                let lon = data[0].lon;
                                map.setView([lat, lon], 15);
                                updateLatLng(lat, lon);
                            } else {
                                alert("Location not found. Try a different search term.");
                            }
                        })
                        .catch(error => console.error("Error fetching location:", error));
                }
            </script>
        </div>
{% endblock %}