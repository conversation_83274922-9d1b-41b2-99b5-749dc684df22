<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Locations</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <style>
        #map { height: 400px; width: 100%; margin-top: 15px; }
        .input-group-text { display: flex; align-items: center; }
    </style>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('company_locations.view_locations') }}">                
            <i class="fas fa-list"></i> Locations
        </a>
    </div>

    <div class="dynamic--form">
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>

        <h1>Add new location</h1>
        <form action="{{ url_for('company_locations.add_locations') }}" method="post">
            {{ form.hidden_tag() }}

            <div class="form-row">
                <div class="form-group">
                    {{ form.site_name.label }}
                    <div class="input-group-text">
                        <i class="fas fa-building"></i>
                        {{ form.site_name(class="form-control", placeholder="Site name") }}
                    </div>
                    {% for error in form.site_name.errors %}
                    <span style="color: red;">[{{ error }}]</span>
                    {% endfor %}
                </div>

                <div class="form-group">
                    {{ form.location.label }}
                    <div class="input-group-text">
                        <i class="fas fa-map"></i>
                        {{ form.location(class="form-control", placeholder="Address: ex: Kigali, Kicukiro, Gatenga") }}
                    </div>
                    {% for error in form.location.errors %}
                    <span style="color: red;">[{{ error }}]</span>
                    {% endfor %}
                </div>
            </div>

            <!-- Map for selecting latitude & longitude -->
            <h3>Choose Location on Map</h3>
            <input type="text" id="search-box" placeholder="Search (KG 123 St)">
            <button type="button" onclick="searchLocation()">Search</button>

            <div id="map"></div>

            <div class="form-row">
                <div class="form-group">
                    <label for="latitude">Latitude:</label>
                    {{ form.latitude(class="form-control", id="latitude", readonly="readonly") }}
                </div>
                <div class="form-group">
                    <label for="longitude">Longitude:</label>
                    {{ form.longitude(class="form-control", id="longitude", readonly="readonly") }}
                </div>
            </div>

            {{ form.submit(class="btn-edit") }}
        </form>
    </div>

    <script>
        let map = L.map('map').setView([-1.9501, 30.0588], 12);  // Default: Kigali, Rwanda
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', { attribution: '© OpenStreetMap' }).addTo(map);

        let marker = L.marker([-1.9501, 30.0588], { draggable: true }).addTo(map);

        function updateLatLng(lat, lng) {
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;
            marker.setLatLng([lat, lng]);
        }

        // Click to update latitude & longitude
        map.on('click', function(e) {
            updateLatLng(e.latlng.lat, e.latlng.lng);
        });

        // Dragging marker updates coordinates
        marker.on('dragend', function(e) {
            let pos = marker.getLatLng();
            updateLatLng(pos.lat, pos.lng);
        });

        function searchLocation() {
            let query = document.getElementById('search-box').value;
            if (!query) {
                alert("Please enter a location to search.");
                return;
            }

            let url = `https://nominatim.openstreetmap.org/search?format=json&q=${query}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.length > 0) {
                        let lat = data[0].lat;
                        let lon = data[0].lon;
                        map.setView([lat, lon], 15);
                        updateLatLng(lat, lon);
                    } else {
                        alert("Location not found. Try a different search term.");
                    }
                })
                .catch(error => console.error("Error fetching location:", error));
        }
    </script>
</body>
</html>
