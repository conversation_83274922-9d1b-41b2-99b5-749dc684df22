<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Location</title>
</head>
<body>
    <h1>Update Location</h1>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul>
                {% for category, message in messages %}
                    <li class="{{ category }}">{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}

    <form method="post">
        {{ form.hidden_tag() }}
        
        <p>
            {{ form.site_name.label }}<br>
            {{ form.site_name(size=32, class="form-control") }}<br>
            {% for error in form.site_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>

        <p>
            {{ form.location.label }}<br>
            {{ form.location(size=32, class="form-control") }}<br>
            {% for error in form.location.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>

        <p>
            {{ form.submit(class="btn btn-primary") }}  <!-- Ensure the submit field is rendered properly -->
        </p>
    </form>
</body>
</html>
