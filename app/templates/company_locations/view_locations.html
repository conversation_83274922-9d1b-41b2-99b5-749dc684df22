<!DOCTYPE html>
<html>
<head>
    <title>View Locations</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('company_locations.add_locations') }}">
            <i class="fas fa-plus"></i> Add Location
        </a>
    </div>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="dynamic--form">
        <h1>Locations</h1>
        <table>
            <tr>
                <th>Location</th>
                <th>Site Name</th>
                <th>Actions</th>
            </tr>
            {% if sites %}
                {% for location in sites %}
                    <tr>
                        <td>{{ location.location }}</td>
                        <td>{{ location.site_name }}</td>
                        <td>
                            <a href="{{url_for('company_locations.update_location', site_id=location.site_id)}}">Edit</a>
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="3">No locations found</td>
                </tr>
            {% endif %}
        </table>

    </div>
</body>
</html>