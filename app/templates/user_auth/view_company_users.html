<!DOCTYPE html>
<html>
<head>
    <title>View {{ company_name }} Users</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('user_data.register_company_users') }}">
            <i class="fas fa-plus"></i> Add User
        </a>
    </div>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div class="dynamic--form">
        <h1>{{ company_name }} Users</h1>
        <table>
            <tr>
                <th>Name</th>
                <th>Username</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Role</th>
                <th>Actions</th>
            </tr>
            {% for user in users %}
                <tr>
                    <td>{{ user.full_name }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.phone_number }}</td>
                    <td>{{ user.role }}</td>
                    <td>
                        <a class="template-link btn-image" href="#" data-template-url="{{ url_for('user_data.update_company_user', id=user.user_id) }}">Edit</a>
                    </td>
                        
                </tr>
            {% endfor %}
        </table>
    </div>
    
</body>
</html>