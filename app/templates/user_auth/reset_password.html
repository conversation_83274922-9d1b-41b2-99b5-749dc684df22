<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block title %}Login{% endblock %}

{% block content %}
<section class="bg-gray-100 flex items-center justify-center min-h-screen flex-col">
    <div class="flash--messages mb-4 w-full max-w-md">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul class="list-unstyled">
            {% for category, message in messages %}
                <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </li>
            {% endfor %}
            </ul>
        {% endif %}
        {% endwith %}
    </div>
    <h1 class="dark">Reset Password</h1>
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <form method="post">
            {{ form.hidden_tag() }}
            {% for error in form.email.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
            {{ form.email.label(class="block dark font-bold mb-2") }}
                {{ form.email(class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200", placeholder="Enter your email") }}

            {{ form.submit(class="w-full primary-background mt-2") }}
        </form>
    </div>
</section>
{% endblock %}