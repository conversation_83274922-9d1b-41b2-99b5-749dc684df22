<!DOCTYPE html>
<html>
<head>
    <title>Register Company Users</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fas fa-arrow-left"></i> Settings       </a>
        <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('user_data.view_company_users') }}">
            <i class="fas fa-plus"></i> Add User
        </a>
    </div>
    <div class="real-form">
        <h1>Register Company Users</h1>
    <form  method="post">
        {{ form.csrf_token }}
        <div class="form-row">
            <div class="form-group">
                {{ form.first_name.label }}
                <div class="input-group-text">
                    {{ form.first_name (class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.last_name.label }}
                <div class="input-group-text">
                    {{ form.last_name (class="form-control") }}
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                {{ form.email.label }}
                <div class="input-group-text">
                    {{ form.email (class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.phone.label }}
                <div class="input-group-text">
                    {{ form.phone (class="form-control") }}
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                {{ form.company.label }}
                <div class="input-group-text">
                    {{ form.company (class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.role.label }}
                <div class="input-group-text">
                    {{ form.role (class="form-control") }}
                </div>
            </div>
        </div>
        <div class="form-row">
            {{ form.submit (class="btn-continue") }}
        </div>
    </form>
</div>
    
</body>

</html>