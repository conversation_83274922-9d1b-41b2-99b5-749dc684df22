<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add User</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        h4 {
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }
        h1 {
            margin-bottom: 40%;
           
            color: #333;
            
        }

        form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
        }

        form div {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
        }

        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="password"]:focus,
        select:focus {
            outline: none;
            border-color: #00ff6a;
            box-shadow: 0 0 5px rgba(25, 212, 72, 0.5);
        }

        button[type="submit"] {
            width: 100%;
            padding: 10px;
            background-color: #2ec78c;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        button[type="submit"]:hover {
            background-color: #0056b3;
        }

        .messages ul {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }

        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        @media (max-width: 768px) {
            form {
                padding: 15px;
            }

            input, select, button {
                font-size: 14px;
            }
        }
        a {
            text-decoration: none;
            color: #26b645;
            font-size: 18px;
            margin-top: 39%;
            margin-right: 5%;
        }
    </style>
</head>
<body>
    <h1>Add Users</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="messages">
                <ul>
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
    
    <form action="{{ url_for('user_data.add_user') }}" method="post">
        {{ form.csrf_token }}
        <div>
            <label for="username">Username</label>
            <input id="username" name="username" required type="text" value="" placeholder="Username">
        </div>
        <div>
            <label for="first_name">First Name</label>
            <input id="first_name" name="first_name" required type="text" value="" placeholder="John">
        </div>
        <div>
            <label for="last_name">Last Name</label>
            <input id="last_name" name="last_name" required type="text" value="" placeholder="Doe">
        </div>
        <div>
            <label for="email">Email</label>
            <input id="email" name="email" required type="email" value="" placeholder="<EMAIL>">
        </div>
        <div>
            <label for="password">Password</label>
            <input id="password" name="password" required type="password" value="" placeholder="Password">
        </div>
        <div>
            <label for="phone_number">Phone Number</label>
            <input id="phone_number" name="phone_number" required type="text" value="" placeholder="0781234567">
        </div>
        <div>
            <label for="role">Role</label>
            <select id="role" name="role" required>
                <option value="admin">Admin</option>
                <option value="hr">HR</option>
            </select>
        </div>

        <button type="submit">Add User</button>
    </form><br>

    <a href="{{ url_for('admin_data.admin_dashboard') }}">Back to Dashboard</a>
</body>
</html>
