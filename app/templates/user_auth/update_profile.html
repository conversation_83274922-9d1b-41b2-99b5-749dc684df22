<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="{{url_for('static', filename='styles/form_flash_messages.css')}}">
    <title>Update Profile</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fa fa-arrow-left"></i> back
        </a>
    </div>
    <div class="real-form">
    <h1>Update Profile</h1>
    <form action="{{url_for('user_data.update_profile')}}" method="post">
        {{ form.hidden_tag() }}
        <div class="form-row">
            <div class="form-group">
                {{ form.username.label }}
                <div class="input-group-text">
                    <i class="fas fa-user"></i>
                    {{ form.username(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.first_name.label }}
                <div class="input-group-text">
                    <i class="fas fa-user"></i>
                    {{ form.first_name(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.last_name.label }}
                <div class="input-group-text">
                    <i class="fas fa-user"></i>
                    {{ form.last_name(class="form-control") }}
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                {{ form.email.label }}
                <div class="input-group-text">
                    <i class="fas fa-envelope"></i>
                    {{ form.email(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                {{ form.phone_number.label }}
                <div class="input-group-text">
                    <i class="fas fa-phone"></i>
                    {{ form.phone_number(class="form-control") }}
                </div>
            </div>
            <div class="form-group">
                <label for="">Change Password</label>
                <div class="input-group-text">
                    <i class="fas fa-lock"></i>
                    <a class = "template-link" href="{{ url_for('user_data.update_password') }}" data-template-url = "{{ url_for('user_data.update_password') }}">
                        *******
                    </a>
                </div>
            </div>
        </div>
            {{ form.submit(class="btn-custom") }}
    </div>
    </form>  
</div>
</body>
</html>