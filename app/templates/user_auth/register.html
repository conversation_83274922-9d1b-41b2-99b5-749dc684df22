<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block content %}
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<div class="bg-gray-100 flex items-center justify-center min-h-screen flex-col">
    <div class="flash--messages mb-4 w-full max-w-lg">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <ul class="list-unstyled">
            {% for category, message in messages %}
            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </li>
            {% endfor %}
        </ul>
        {% endif %}
        {% endwith %}
    </div>
    <div class="p-8 w-full max-w-lg bg-white rounded-lg shadow-lg">
        <h1 class="text-2xl font-bold text-center mb-6 primary ">Create a new account</h1>
        <form id="multiStepForm" action="/register_user" method="post" class="form-horizontal">
            {{ form.csrf_token }}
            <div class="progress-bar">
                <div class="step active"></div>
                <div class="step"></div>
                <div class="step"></div>
                <div class="step"></div>
            </div>
            <div class="form-step active" id="step-1">
                <div class="mb-4">
                    <label for="username" class="block text-gray-700 font-bold mb-2">Username <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-chalkboard-user p-2 text-gray-400"></i>
                        <input id="username" name="username" required type="text"
                            class="form-control w-full px-3 py-2 focus:outline-none" placeholder="username">
                    </div>
                    <div id="username-error" class="error-message">Please enter a valid username.</div>
                </div>
                <div class="mb-4">
                    <label for="first_name" class="block text-gray-700 font-bold mb-2">First Name <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-user p-2 text-gray-400"></i>
                        <input id="first_name" name="first_name" required type="text"
                            class="form-control w-full px-3 py-2 focus:outline-none" placeholder="Patrick">
                    </div>
                    <div id="first_name-error" class="error-message">Please enter your first name.</div>
                </div>
                <div class="mb-4">
                    <label for="last_name" class="block text-gray-700 font-bold mb-2">Last Name <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-user p-2 text-gray-400"></i>
                        <input id="last_name" name="last_name" required type="text"
                            class="form-control w-full px-3 py-2 focus:outline-none" placeholder="Muvunyi">
                    </div>
                    <div id="last_name-error" class="error-message">Please enter your last name.</div>
                </div>
                <button type="button"
                    class="btn btn-custom btn-block border p-2 primary-background rounded-lg hover:ring-opacity-50"
                    onclick="nextStep()">Next</button>
            </div>
            <div class="form-step" id="step-2">
                <div class="mb-4">
                    <label for="email" class="block text-gray-700 font-bold mb-2">Email <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-envelopes p-2 text-gray-400"></i>
                        <input id="email" name="email" required type="email"
                            class="form-control w-full px-3 py-2 focus:outline-none" placeholder="<EMAIL>">
                    </div>
                    <div id="email-error" class="error-message">Please enter a valid email address.</div>
                </div>
                <div class="mb-4">
                    <label for="password" class="block text-gray-700 font-bold mb-2">Password <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-lock p-2 text-gray-400"></i>
                        <input id="password" maxlength="20" minlength="6" name="password" required type="password"
                            class="form-control w-full px-3 py-2 focus:outline-none">
                    </div>
                    <div id="password-error" class="error-message">Password must be between 6 and 20 characters.</div>
                </div>
                <div class="mb-4">
                    <label for="confirm_password" class="block text-gray-700 font-bold mb-2">Confirm Password <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-lock p-2 text-gray-400"></i>
                        <input id="confirm_password" name="confirm_password" required type="password"
                            class="form-control w-full px-3 py-2 focus:outline-none">
                    </div>
                    <div id="confirm_password-error" class="error-message">Passwords do not match.</div>
                </div>
                <div class="flex items-center justify-between">
                    <button type="button"
                        class="btn btn-custom btn-block bg-gray-200 border text-gray-500 p-2 rounded-lg hover:bg-gray-700 mt-2"
                        onclick="prevStep()">Back</button>
                    <button type="button"
                        class="btn btn-custom btn-block primary-background  p-2 rounded-lg hover:ring-opacity-50 bg-white"
                        onclick="nextStep()">Next</button>
                </div>
            </div>
            <div class="form-step" id="step-3">
                <div class="mb-4">
                    <label for="phone_number" class="block text-gray-700 font-bold mb-2">Phone Number <span
                            class="text-red-500">*</span></label>
                    <div class="flex items-center border rounded-lg focus-within:ring-2 focus-within:ring-green-200">
                        <i class="fi fi-rr-circle-phone-flip p-2 text-gray-400"></i>
                        <input id="phone_number" name="phone_number" required type="text"
                            class="form-control w-full px-3 py-2 focus:outline-none" placeholder="0781234567">
                    </div>
                    <div id="phone_number-error" class="error-message">Please enter a valid phone number.</div>
                </div>
                <div class="mb-4">
                    <label class="form-check-label flex items-center">
                        <input type="checkbox" class="form-check-input mr-2" id="termsCheck" required>
                        I agree to the &nbsp;<a href="{{ url_for('pages.terms') }}"
                            class="text-blue-600 hover:underline" target="_blank"> Terms of Use</a>&nbsp;and&nbsp;<a
                            href="{{ url_for('pages.privacy') }}" class="text-blue-600 hover:underline" target="_blank">
                            Privacy Policy</a>
                    </label>
                    <div id="termsCheck-error" class="error-message">You must agree to the terms and conditions.</div>
                </div>

                <div class="g-recaptcha mb-4" data-sitekey="{{ captcha_public_key }}"></div>
                <div id="recaptcha-error" class="error-message">Please complete the reCAPTCHA.</div>
                <div class="flex items-center justify-between">
                    <button type="button"
                        class="btn btn-custom btn-block bg-gray-200 border text-gray-500 p-2 rounded-lg hover:bg-gray-700 mt-2"
                        onclick="prevStep()">Back</button>
                    <button type="button"
                        class="btn btn-custom btn-block primary-background p-2 rounded-lg hover:ring-opacity-50 bg-white"
                        onclick="nextStep()">Next</button>
                </div>
            </div>
            <div class="form-step" id="step-4">
                <p class="mb-4">Please review your information before submitting.</p>
                <div class="flex items-center justify-between">
                    <button type="button"
                        class="btn btn-custom btn-block bg-gray-200 border text-gray-500 py-2 rounded-lg hover:bg-gray-700 mt-2"
                        onclick="prevStep()">Back</button>
                    <button type="submit"
                        class="btn btn-custom btn-block bg-green-600 text-white py-2 rounded-lg hover:bg-green-700">Register</button>
                </div>
            </div>
        </form>
        <p class="text-center mt-4 text-gray-600">Already have an account? <a href="{{ url_for('user_data.login') }}"
                class="text-blue-600 hover:underline">Login</a></p>
    </div>

    <script>
        let currentStep = 1;

        function showStep(step) {
            document.querySelectorAll('.form-step').forEach(function (stepElement) {
                stepElement.classList.remove('active');
            });
            document.querySelectorAll('.progress-bar .step').forEach(function (stepElement, index) {
                stepElement.classList.remove('active');
                if (index < step - 1) {
                    stepElement.classList.add('active');
                }
            });
            document.getElementById('step-' + step).classList.add('active');
        }

        function nextStep() {
            if (validateStep(currentStep)) {
                if (currentStep < 4) {
                    currentStep++;
                    showStep(currentStep);
                }
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        function validateStep(step) {
            let isValid = true;
            switch (step) {
                case 1:
                    isValid = validateUsername() && validateFirstName() && validateLastName();
                    break;
                case 2:
                    isValid = validateEmail() && validatePassword() && validateConfirmPassword();
                    break;
                case 3:
                    isValid = validatePhoneNumber() && validateTermsCheck();
                    // it exist before (validateRecaptcha)
                    break;
                default:
                    isValid = true;
            }
            return isValid;
        }

        function validateUsername() {
            const username = document.getElementById('username').value;
            const errorMessage = document.getElementById('username-error');
            if (username.trim() === '') {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validateFirstName() {
            const firstName = document.getElementById('first_name').value;
            const errorMessage = document.getElementById('first_name-error');
            if (firstName.trim() === '') {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validateLastName() {
            const lastName = document.getElementById('last_name').value;
            const errorMessage = document.getElementById('last_name-error');
            if (lastName.trim() === '') {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validateEmail() {
            const email = document.getElementById('email').value;
            const errorMessage = document.getElementById('email-error');
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(email)) {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validatePassword() {
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('password-error');
            if (password.length < 6 || password.length > 20) {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validateConfirmPassword() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const errorMessage = document.getElementById('confirm_password-error');
            if (password !== confirmPassword) {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validatePhoneNumber() {
            const phoneNumber = document.getElementById('phone_number').value;
            const errorMessage = document.getElementById('phone_number-error');
            const phonePattern = /^\d{10}$/;
            if (!phonePattern.test(phoneNumber)) {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validateTermsCheck() {
            const termsCheck = document.getElementById('termsCheck').checked;
            const errorMessage = document.getElementById('termsCheck-error');
            if (!termsCheck) {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        function validateRecaptcha() {
            const recaptchaResponse = grecaptcha.getResponse();
            const errorMessage = document.getElementById('recaptcha-error');
            if (recaptchaResponse.length === 0) {
                errorMessage.classList.add('active');
                return false;
            } else {
                errorMessage.classList.remove('active');
                return true;
            }
        }

        showStep(currentStep);
    </script>
</div>
{% endblock %}