{% extends 'layouts/home.html' %}
{% block title %}Set up otp{% endblock %}

{% block content %}
<div class="loading">
    <!-- Loading overlay with dots -->
    <div id="loading-overlay">
        <p>Please wait</p>
        <div class="loading-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
    </div>
    <div class="container big-container">
        <div class="flash--messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </li>
                {% endfor %}
                </ul>
                {% endif %}
            {% endwith %}
        </div> 
        <div class="real-form">
            <h1 class="header-title">VERIFY OTP</h1>
            <p>Enter the OTP Code from your authenticator app</p>
            <form method="POST" action="{{ url_for('user_data.verify_otp') }}">
                {{ form.hidden_tag() }}
                <div class="form-group">
                    <div class="input-group-text">
                        {% for error in form.otp.errors %}
                            <span>[{{ error }}]</span>
                        {% endfor %}
                        <span class="material-symbols-outlined icon">key</span>
                        {{ form.otp(class='form-control') }}
                    </div>
                </div>
                <div class="form-group">
                    <input id="submit" class="btn btn-custom" name="submit" type="submit" value="Verify OTP">
                    <p class="btn-edit"><a href="{{ url_for('user_data.setup_otp') }}">Reset OTP</a></p>

                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/66d86a7e50c10f7a00a3f52d/1i6ulvvt4';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
</script>
{% endblock %}
