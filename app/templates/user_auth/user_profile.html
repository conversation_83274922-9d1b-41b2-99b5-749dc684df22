<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1 class="dynamic-title">User's Info</h1>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <div class="profile-header">
                    <div class="profile-image">
                        {% if user.profile_image %}
                            <img src="{{ url_for('static', filename='images/' + user.profile_image) }}" alt="Profile Image">
                        {% else %}
                            <img src="{{ url_for('static', filename='images/system_images/woman.png') }}" alt="Default Profile Image">
                        {% endif %}
                    </div>
                    <div class="profile-info">
                        <h1>{{ user.first_name }} {{ user.last_name }}</h1>
                    </div>
                </div>
                <div class="profile-content">
                    <div class="section">
                        <h2>{{ user.first_name }} {{ user.last_name }}</h2>
                        <p><strong>Email: </strong>{{ user.email }}</p>
                        <p><strong>Phone: </strong>{{ user.phone_number }}</p>
                        <p><strong>Username: </strong>{{ user.username }}</p>
                        <p><strong>Role: </strong>{{ user.role }}</p>
                        <div class="action-links">
                            <a class="template-link" href="{{ url_for('user_data_v2.update_profile') }}">
                        <i class="fi fi-rr-edit"></i>
                            Update
                        </a>
                        </div>
                    </div>
                    <div class="section">
                        <h2>Password</h2>
                        <p class="text-muted">******</p>
                        <div class="action-links">
                        <a class="template-link" href="{{ url_for('user_data_v2.update_password') }}" data-template-url="{{ url_for('user_data.update_password') }}">
                            <i class="fi fi-rr-edit"></i>
                            Change Password
                        </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>  
    </div>
{% endblock %}
