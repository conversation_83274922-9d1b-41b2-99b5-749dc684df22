<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <style>
        .danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .active {
            display: block;
            margin-top: 50px;
        }

        .not-active {
            display: none;
        }
    </style>

    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                    <i class="fi fi-rr-settings"></i>Settings
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="container form--container">
                <div class="real-form active">
                    <div style="text-align: center;">
                        <h1>Transfer Account Ownership</h1>
                        <p style="margin-top: -20px; margin-bottom: 20px;">Fill information.</p>
                    </div>
                    <form method="post" id="initial-step-form">
                        <input type="text" name="csrf_token" value="{{ csrf_token }}" hidden>
                        <div class="error" style="color: red; margin: 10px 0;"></div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="">First Name</label>
                                <div class="input-group-text">
                                    <input class="form-control" type="text" id="first_name" name="first_name"
                                        placeholder="eg: Kalisa" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Last Name</label>
                                <div class="input-group-text">
                                    <input class="form-control" type="text" id="last_name" name="last_name"
                                        placeholder="eg: John" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="">Email</label>
                                <div class="input-group-text">
                                    <input class="form-control" type="email" id="email" name="email"
                                        placeholder="eg: <EMAIL>" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Phone Number</label>
                                <div class="input-group-text">
                                    <input class="form-control" type="text" id="phone" name="phone" placeholder="eg: 0781234567"
                                        required>
                                </div>
                            </div>
                            <input type="text" name="initial-step-form" hidden>
                        </div>
                        <div style="display: flex; justify-content: end;">
                            <button class="next-btn" type="submit">Next ⇥</button>
                        </div>
                    </form>
                </div>
                <div class="real-form not-active" style="margin-top: 50px; padding: 20px">
                    <div style="text-align: center;">
                        <h1>Transfer Account Ownership</h1>
                        <p style="margin-top: -20px; margin-bottom: 20px;">Choose company to transfer.</p>
                        <div class="error" style="color: red; margin: 10px 0;"></div>
                    </div>
                    <div style="border-bottom: 2px solid black;"></div>
                    <div style="text-align: center;">
                        <h2>Summary</h2>
                        <h3>You are transfering company to:</h3>

                        <div class="user-info" style="margin-left: 0px;">
                            <p>First Name: <span style="opacity: .7" id="reciever-first-name">${firstName}</span></p>
                            <p>Last Name: <span style="opacity: .7" id="reciever-last-name">${lastName}</span></p>
                            <p>Email: <span style="opacity: .7" id="reciever-email">${email}</span></p>
                        </div>
                    </div>
                    <div style="border-bottom: 2px solid black;"></div>
                    <div class="">
                        <form method="post">
                            {{ transfer_form.csrf_token }}

                            <div class="form-row">
                                <div class="form-group">
                                    {{ transfer_form.company.label }}
                                    <div class="input-group-text">
                                        {{ transfer_form.company(class="form-select") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <input type="text" name="final-step-form" hidden>
                                    <br>
                                    <button class="submit-btn" type="submit">Confirm Transfer</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let container = document.querySelector(".container")
        const nextBtn = document.querySelector(".next-btn")
        const initialStepForm = document.querySelector("#initial-step-form")
        const realForms = document.querySelectorAll(".real-form")
        const csrfToken = document.querySelector('[name=csrf_token]')
        const recieverFirstNameElement = document.querySelector('#reciever-first-name')
        const recieverLastNameElement = document.querySelector('#reciever-last-name')
        const recieverEmailElement = document.querySelector('#reciever-email')
        const currentForm = document.querySelector('[data-current-form]')

        /* Submit data to the backend */
        initialStepForm.addEventListener("submit", async (e) => {
            e.preventDefault()
            const firstName = document.querySelector("#first_name").value.trim()
            const lastName = document.querySelector("#last_name").value.trim()
            const email = document.querySelector("#email").value.trim()
            const phone = document.querySelector("#phone").value.trim()

            if (firstName === "" || lastName === "" || email === "" || phone === "") {
                const error = document.querySelector(".error")
                error.textContent = "Please fill all fields"
                return
            }

            // Send data to the backend
            try {
                const formData = new FormData();
                formData.append("first_name", firstName);
                formData.append("last_name", lastName);
                formData.append("email", email);
                formData.append("phone", phone);
                formData.append("current_form", "initial-step-form");

                const response = await fetch("/transfer_account", {
                    method: "POST",
                    body: formData,
                    headers: {
                        'X-CSRFToken': csrfToken.value
                    }
                })
                const data = await response.json()
                console.log(data)
                if (response.ok) {
                    // hide first form and show second form
                    realForms[0].classList.remove("active") // i can even delete it (idea)
                    realForms[0].classList.add("not-active")
                    realForms[1].classList.remove("not-active")
                    realForms[1].classList.add("active")

                    // modify default data
                    recieverFirstNameElement.innerHTML = data.data.first_name
                    recieverLastNameElement.innerHTML = data.data.last_name
                    recieverEmailElement.innerHTML = data.data.email
                }
                if (!response.ok) {
                    const errorMessage = data.error
                    alert(errorMessage)
                }
            } catch (error) {
                console.log(error)
            } finally { }

        })
        setTimeout(() => {
            const flashMessage = document.getElementById("flash-message");
            if (flashMessage) {
                flashMessage.style.display = "none"; // Hide the message
            }
        }, 5000);





        function confirmTransfer(e) {
            e.preventDefault()
            console.log("Confirming transfer...")
        }
        function getUserInfo() {
            const userInfoString = getCookieByName("User")
            const userInfo = JSON.parse(decodeURIComponent(userInfoString))

            firstName = userInfo.first_name
            lastName = userInfo.last_name
            email = userInfo.email
        }

        function handleNextStep() {
            // const error = document.querySelector(".error")
            // const isCreated = getCookieByName("User")
            // if (!isCreated) {
            //     error.innerHTML = "Failed to create user, try again!"
            //     // Clear message after 3 seconds
            //     if (error.innerHTML.length > 5) {
            //         setTimeout(() => {
            //             error.innerHTML = null
            //         }, 4000)
            //     }
            // } else {
            //     // Populate user info
            //     getUserInfo()
            //     // render that form
            //     renderTransferOwnerShip()
            // }
        }

        function getCookieByName(name) {
            const cookieString = document.cookie;
            const cookies = cookieString.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.startsWith(name + '=')) {
                    return cookie.substring(name.length + 1);
                }
            }
            return null;
        }

    </script>
{% endblock %}
