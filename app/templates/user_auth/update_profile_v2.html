<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="dynamic-title">Update Profile</h1>
            <a class="btn-edit template-link" href="{{ url_for('user_data_v2.settings') }}">
                <i class="fi fi-rr-settings"></i> Settings
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <form action="{{url_for('user_data_v2.update_profile')}}" method="post">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.username.label }}
                        <div class="input-group-text">
                            <i class="fas fa-user"></i>
                            {{ form.username(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.first_name.label }}
                        <div class="input-group-text">
                            <i class="fas fa-user"></i>
                            {{ form.first_name(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.last_name.label }}
                        <div class="input-group-text">
                            <i class="fas fa-user"></i>
                            {{ form.last_name(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.email.label }}
                        <div class="input-group-text">
                            <i class="fas fa-envelope"></i>
                            {{ form.email(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.phone_number.label }}
                        <div class="input-group-text">
                            <i class="fas fa-phone"></i>
                            {{ form.phone_number(class="form-control") }}
                        </div>
                    </div>
                    
                </div>
                    <button type="submit" class="submit-btn">Update</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
