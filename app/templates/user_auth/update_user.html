<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Update User</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/user_auth.css') }}">
</head>
<body>
    <h1>Update User</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="messages">
                <ul>
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
     
    <form method="POST" action="{{ url_for('user_data.update_user', id=user.user_id) }}">
        {{ form.hidden_tag() }}
        <p>
            {{ form.username.label }}<br>
            {{ form.username(size=32) }}<br>
            {% for error in form.username.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.email.label }}<br>
            {{ form.email(size=32) }}<br>
            {% for error in form.email.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.phone_number.label }}<br>
            {{ form.phone_number(size=32) }}<br>
            {% for error in form.phone_number.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.role.label }}<br>
            {{ form.role(size=32) }}<br>
            {% for error in form.role.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.password.label }}<br>
            {{ form.password(size=32) }}<br>
            {% for error in form.password.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>
    </form>
</body>
</html>
