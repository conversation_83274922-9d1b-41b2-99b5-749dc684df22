<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="dynamic-title">Change Password</h1>
            <a class="btn-edit template-link" href="{{ url_for('user_data_v2.user_profile') }}">
                <i class="fi fi-rr-user"></i>Profile
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <form method="POST" action="{{ url_for('user_data_v2.update_password') }}">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.old_password.label }}
                        <div class="input-group-text">
                            {{ form.old_password(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group"> 
                        {{ form.new_password.label }}
                        <div class="input-group-text">
                            {{ form.new_password(class="form-control") }}
                        </div>
                    </div>             
                    <div class="form-group">
                        {{ form.confirm_password.label }}
                        <div class="input-group-text">
                            {{ form.confirm_password(class="form-control") }}
                        </div>
                    </div>
                </div>
                <button type="submit" class="submit-btn">Change Password</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}