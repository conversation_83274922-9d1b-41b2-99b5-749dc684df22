<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>View Users</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        h1 {
            margin-top: 20px;
            color: #333;
        }

        .messages ul {
            list-style: none;
            padding: 0;
        }

        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        table {
            width: 100%;
            max-width: 900px;
            margin: 20px 0;
            border-collapse: collapse;
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        table thead {
            background-color: #333;
            color: #fff;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
        }

        table tr {
            border-bottom: 1px solid #ddd;
        }

        table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        table tr:hover {
            background-color: #f1f1f1;
        }

        table a {
            text-decoration: none;
            color: #007bff;
        }

        table a:hover {
            text-decoration: underline;
        }

        a {
            display: inline-block;
            margin-top: 20px;
            text-decoration: none;
            color: #007bff;
            font-size: 16px;
        }

        a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            table {
                font-size: 14px;
            }
            table th, table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <h1>View Users</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="messages">
                <ul>
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
    
    <table>
        <thead>
            <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Names</th>
                <th>Phone</th>
                <th>Role</th>
                <th>Company</th>
                <th>Created at</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
                <tr>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.full_name}} </td>
                    <td>
                        {% if user.phone_number %}
                            {{ user.phone_number }}
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td>{{ user.role }}</td>
                    <td>
                        {% if user.company_name %}
                            {{ user.company_name }}
                        {% else %}
                            N/A
                        {% endif %}
                    <td>{{ user.created_at }}</td>
                    <td>
                        <a href="{{ url_for('user_data.update_user', id=user.user_id) }}">Update</a>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>

    <a href="{{ url_for('admin_data.admin_dashboard') }}">Back to Dashboard</a>
</body>
</html>
