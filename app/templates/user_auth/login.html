<!DOCTYPE html>
{% extends 'layouts/home_.html' %}
{% block content %}
        <div class="bg-gray-100 flex items-center justify-center min-h-screen flex-col">
            <div class="flash--messages mb-4 w-full max-w-md">
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
                {% endwith %}
            </div>
            <h1 class="dark">Login</h1>
            <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            <form action="/login_user" method="post">
                {{ form.csrf_token }}
                <div class="mb-4">
                    <label for="text" class="block dark font-bold mb-2">Username</label>
                    <input type="text" id="username" name="username" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200" placeholder="Enter your email" required>
                </div>
                <div class="mb-6">
                    <label for="password" class="block dark font-bold mb-2">Password</label>
                    <input type="password" id="password" name="password" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus-within:ring-green-200" placeholder="Enter your password" required>
                </div>
                <div class="flex items-center justify-between mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox h-5 w-5 text-blue-600">
                        <span class="ml-2 dark">Remember me</span>
                    </label>
                    <a href="{{ url_for('user_data.reset_password') }}" class="text-blue-600 hover:underline">Forgot password ?</a>
                </div>
                <button type="submit" class="w-full primary-background  py-2 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus-within:ring-green-200 focus:ring-opacity-50">Login</button>
            </form>
        </div>
    </div>
{% endblock %}
