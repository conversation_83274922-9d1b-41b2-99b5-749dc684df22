<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Update Company User ({{user.full_name}})</h1>
            <a class="btn-edit" href="{{ url_for('user_data_v2.view_company_users') }}">
                <i class="fi fi-rr-list"></i> Users
            </a>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <form method="POST" action="{{ url_for('user_data_v2.update_company_user', id=user.user_id) }}">
                    {{ form.csrf_token }}
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name">First Name</label>
                            <div class="input-group-text">
                                {{ form.first_name(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="last_name">Last Name</label>
                            <div class="input-group-text">
                                {{ form.last_name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username</label>
                            <div class="input-group-text">
                                {{ form.username(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <div class="input-group-text">
                                {{ form.email(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone_number">Phone Number</label>
                            <div class="input-group-text">
                                {{ form.phone_number(class="form-control") }}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="role">Role</label>
                            <div class="input-group-text">
                                {{ form.role(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="Company">Company</label>
                            <div class="input-group-text">
                                {{ form.company(class="form-control") }}
                            </div>
                        </div>
                    </div>
                        <button type="submit" class="submit-btn">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}