<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <title>Change Password</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('user_data.settings') }}">
            <i class="fa fa-arrow-left"></i> back
        </a>
    </div>
    <div class="real-form">
        <div class="flash-messages">

        <h1>Change Password</h1>
        <!-- Flash messages -->
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <div class="real--form">
            <form method="POST" action="{{ url_for('user_data.update_password') }}">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.old_password.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">lock</span>
                            {{ form.old_password(class="form-control") }}
                        </div>
                    <div class="form-group">
                        {{ form.new_password.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">lock</span>
                            {{ form.new_password(class="form-control") }}
                        </div>
                    </div>             
                    <div class="form-group">
                        {{ form.confirm_password.label }}
                        <div class="input-group-text">
                            <span class="material-symbols-outlined icon">lock</span>
                            {{ form.confirm_password(class="form-control") }}
                        </div>
                    </div>
                    </div>
                </div>
                {{ form.submit(class="btn-custom") }}
            </form>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>