<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-arrow-left"></i> back
        </a>
    </div>
    <div class="dynamic--form">
        <!-- Flash messages -->
        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>
        <div class="header--contents">
            <i class="fas fa-cogs"></i>
            <h1 class="dynamic-title">Settings</h1>
        </div>
        <div class="inside--container">
                <div class="inside--subcontainer">
                    <h1>Company Info</h1>
                    <a class="template-link" href="{{ url_for('company_data.edit_company_profile') }}" data-template-url="{{ url_for('company_data.edit_company_profile') }}">
                        <i class="fas fa-edit"></i>
                        Update</a>
                </div>
                <table>
                    <tr>
                        <td>Company Name</td>
                        <td>{{ company.company_name }}</td>
                    </tr>-
                    <tr>
                        <td>Company Tin</td>
                        <td>{{ company.company_tin }}</td>
                    </tr>
                    <tr>
                        <td>Rssb Number</td>
                        <td>{{ company.rssb_number }}</td>
                    </tr>
                    <tr>
                        <td>Plan</td>
                        <td>{{ company.plan.plan_name }}</td>
                    </tr>
                    <tr>
                        <td>Subscription Details</td>
                        <td>
                            <div class="inside--subcontainer">
                                {% if company.to_dict()['is_subscribed']  %}
                                    End date: {{ company.to_dict()['subscription_end_period'][0:10]}}
                                {% elif company.to_dict()['is_on_trial'] %}
                                    Trial end date: {{company.to_dict()['trial_until'][0:10]}}
                                {% else %}
                                    Not subscribed
                                {% endif %}
                                <a class="template-link" href="#" data-template-url="{{ url_for('irembo.create_invoice') }}">
                                    <i class="fas fa-plus"></i>
                                    Subscribe
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Company Logo</td>

                        <td>
                            <div class="inside--subcontainer">
                                
                            <a class="template-link" href="#" data-template-url="{{ url_for('company_data.upload_company_logo') }}">
                                <i class="fas fa-upload"></i>
                                Upload Company logo
                            </a>
                            </div>
                            
                        </td>
                    </tr>
                    <tr>
                        <td>Company location</td>

                        <td>
                            <div class="inside--subcontainer">
                                
                            <a class="template-link" href="#" data-template-url="{{ url_for('company_data.company_location') }}">
                                <i class="fas fa-edit"></i>
                                Update Company location
                            </a>
                            </div>
                            
                        </td>
                    </tr>
                    <tr>
                        <td>Company Type</td>
                        <td>{{ company.company_type }}</td>
                    </tr>
                    <tr>
                        <td>Phone Number</td>
                        <td>{{ company.phone_number }}</td>
                    </tr>
                    <tr>
                        <td>Email</td>
                        <td>{{ company.email }}</td>
                    </tr>
                    <tr>
                        <td>Number of Employees</td>
                        <td>{{ company.number_employee }}</td>
                    </tr>
                    <tr>
                        <td>Company Address</td>
                        <td>{{ company.country }}, {{ company.province }}, {{ company.district }}, {{ company.sector }}, {{ company.cell }}, {{ company.village }}</td>
                    </tr>
                    <tr>
                        <td> Departments</td>
                        <td>
                            <div class="inside--subcontainer">
                                <!--loop through departments-->
                            {% if departments %}
                                {% for department in departments %}
                                    {{ department.department_name }}, 
                                {% endfor %}
                            {% else %}
                                No departments
                            {% endif %}
                                <a class="template-link" href="{{ url_for('departments.add_department') }}" data-template-url="{{ url_for('departments.add_department') }}">
                                    <i class="fas fa-plus"></i>
                                    Add Department
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Location</td>
                        <td>
                            <div class="inside--subcontainer">
                            {% if sites %}
                                {% for site in sites %}
                                    {{ site.site_name }}, 
                                {% endfor %}
                            {% else %}
                                No Location found
                            {% endif %}
                            <a class="template-link" href="#" data-template-url="{{ url_for('company_locations.add_locations') }}">
                                <i class="fas fa-plus"></i>
                                Add Location
                            </a>
                        </div>
                        </td>
                    </tr>
                    {% if role.lower() in ['hr', 'company_hr'] %}                        
                    <tr>
                        <td>Users</td>
                        <td>
                            <div class="inside--subcontainer">
                                <a class="template-link" href="#" data-template-url="{{ url_for('user_data.view_company_users') }}">
                                    <i class="fas fa-users"></i>
                                    Users
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Approval Workflow</td>
                        <td>
                            <div class="inside--subcontainer">
                                <a class="template-link" href="#" data-template-url="{{ url_for('approval_work_flow.get_approval_workflow') }}">
                                    <i class="fi fi-rr-master-plan"></i>
                                    Approval Workflow
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                </table>
                {% if attendance_service %}
                    <div class="inside--subcontainer">
                        <h1>Attendance Management Settings</h1>
                    </div>
                    <table>
                        <tr>
                            <td>Users</td>
                            <td>
                                <div class="inside--subcontainer">
                                    <a class="template-link" href="#" data-template-url="{{ url_for('company_users.company_users') }}">
                                        <i class="fas fa-users"></i>
                                        Users
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </table>
                {% endif %}
                <div class="inside--subcontainer">
                    <h1 class="text-2xl font-bold mb-4">Integrations</h1>
                    <a href="{{ url_for('quickbooks.get_auth_url') }}">
                        <i class="fi fi-rr-master-plan text-xl"></i>
                        Connect to QuickBooks
                    </a>
                </div>            
        </div>  
    </div>  
</body>
</html>