{% extends 'layouts/home.html' %}
{% block title %}Set up otp{% endblock %}

{% block content %}
<div class="loading">
    <!-- Loading overlay with dots -->
    <div id="loading-overlay">
        <p>Please wait</p>
        <div class="loading-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
    </div>
    <div class="container">
        <div class="real-form">
            <h1 class="header-title">Setup Two-Factor Authentication (2FA)</h1>
            <p>Scan the QR code below with your authenticator app to set up 2FA:</p><br>
            <div class="otp_container">
                <img src="{{ url_for('static', filename='images/' + email + '.png') }}" alt="QR Code">
            </div>
            <!-- Information about using authenticator app -->
            <p>You can use any authenticator app of your choice.</p>
            <p>If you don't have one, you can use <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank">Google Authenticator</a>.</p>
            
            <!-- Link to enter OTP on a different form -->
            <p>After scanning the QR code, click below to verify your OTP Code</p> 
            <a class = "btn-custom" href="{{ url_for('user_data.verify_otp') }}">Verify</a>
            </div>
        </div>    
</div>
<script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/66d86a7e50c10f7a00a3f52d/1i6ulvvt4';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
</script>
{% endblock %}