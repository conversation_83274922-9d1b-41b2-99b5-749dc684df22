<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Settings</h1>
            </div>
        </div>
        <div class="dyn_container">
                <div class="form--container">
                        <div class="inside--subcontainer">
                            <h1>Company Info</h1>
                            <p>
                                <div class="action-links">
                                    <a class="#" href="{{ url_for('company_data_v2.edit_company_profile') }}">
                                        <i class="fi fi-rr-edit"></i>
                                        Update
                                    </a>
                                </div>
                            </p>
                        </div>
                        <div class="company-profile-container">
                    <div class="profile-header">
                        {% if company_logo %}
                            <img src="{{ company_logo }}" alt="Company Logo">
                        {% else %}
                            <div class="action-links">
                                <div class="flex-container">
                                <a href="{{ url_for('company_data_v2.upload_company_logo') }}"><i class="fi fi-rr-upload"></i> Upload</a>
                                <p>No logo uploaded</p>
                                </div>
                            </div>
                        {% endif %}
                        <h1>{{ company.company_name }}</h1>
                    </div>
                    <div class="profile-content">
                        <div class="section">
                            <h2>Company Details</h2>
                            <p><strong>TIN:</strong> {{ company.company_tin }}</p>
                            <p><strong>RSSB Number:</strong> {{ company.rssb_number }}</p>
                            <p><strong>Company Type:</strong> {{ company.company_type }}</p>
                        </div>
                        <div class="section">
                            <h2>Contact Information</h2>
                            <p><strong>Phone:</strong> {{ company.phone_number }}</p>
                            <p><strong>Email:</strong> {{ company.email }}</p>
                            <p><strong>Address:</strong> {{ company.country }}, {{ company.province }}, {{ company.district }}, {{ company.sector }}, {{ company.cell }}, {{ company.village }}</p>
                        </div>
                        <div class="section">
                            <h2>Subscription</h2>
                            {% if company.to_dict()['is_subscribed'] %}
                                <p><strong>Subscription Plan:</strong> {{ company.plan.plan_name }}</p>
                                <p><strong>Subscription End Date:</strong> {{ company.to_dict()['subscription_end_period'][0:10] }}</p>
                            {% elif company.to_dict()['is_on_trial'] %}
                                <p><strong>Trial End Date:</strong> {{ company.to_dict()['trial_until'][0:10] }}</p>
                            {% else %}
                                <p>Not subscribed</p>
                            {% endif %}
                            <div class="action-links">
                                <a href="{{ url_for('irembo_v2.create_invoice') }}"><i class="fi fi-rr-vote-yea"></i> Subscribe</a>
                            </div>
                        </div>
                        <div class="section">
                            <h2>Logo</h2>
                            {% if company_logo %}
                                Logo uploaded
                            {% else %}
                                <p>No logo uploaded</p>
                            {% endif %}
                            
                            <div class="action-links">
                                <a href="{{ url_for('company_data_v2.upload_company_logo') }}"><i class="fi fi-rr-upload"></i> 
                                    {{ 'Change' if company_logo else 'Upload' }}
                                </a>
                            </div>
                            </div>
                        </div>
                        <div class="section">
                            <h2>Departments</h2>
                            {% if departments %}
                                <p>{{ departments|map(attribute='department_name')|join(', ') }}</p>
                            {% else %}
                                <p>No departments</p>
                            {% endif %}
                            <div class="action-links">
                                <a href="{{ url_for('departments_v2.add_department') }}"><i class="fi fi-rr-rectangle-history-circle-plus"></i> Add Department</a>
                            </div>
                        </div>
                        <div class="section">
                            <h2>Administration</h2>
                            {% if role.lower() in ['hr', 'company_hr'] %}
                                <div class="action-links">
                                    <a href="{{ url_for('user_data_v2.view_company_users') }}"><i class="fi fi-rr-people-roof"></i>Admin Users</a>
                                    <a href="{{ url_for('approval_work_flow_v2.get_approval_workflow') }}"><i class="fi fi-rr-master-plan"></i> Approval Workflow</a>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    </div>
                </div>
                        
                        <div class="form--container">
                            <div class="inside--subcontainer">
                                <h1>Attendance Management Settings</h1>
                            </div>
                            {% if attendance_service %}
                            <div class="profile-header" style="text-align: left;">
                                <div class="grey-container">
                                    <p>Attendance guidelines</p>
                                    <div class="upload-info">
                                        <ol>
                                            <li>start by giving access to your employees to be able to access their employee portal</li>
                                            <li>If you company is enabled to use the self-clockin service, you will also have to set up your company headquarter's location</li>
                                            <li>If you have more than one location, you will have to set up the locations of your branches</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            <div class="profile-content">
                                 <div class="section">
                                    <h2>Employees</h2>
                                    <p><strong>Self-service accounts:</strong> Employees &amp; supervisors</p>
                                    <p><strong>Number of Employees:</strong> {{ company.number_employee }}</p> 
                                    <div class="action-links">
                                        <a class="#" href="{{ url_for('company_users_v2.company_users') }}">
                                                <i class="fi fi-rr-users-alt"></i> Users
                                            </a>
                                     </div>
                                 </div>
                                 <div class="section">
                                    <h2>Company location</h2>
                                    <p><strong>Headquarter:</strong> {{ company.company_name }}</p>
                                    <div class="action-links">
                                        <a class="#" href="{{ url_for('company_data.company_location') }}">
                                            <i class="fi fi-rr-pencil"></i>
                                            location
                                        </a>
                                    </div>
                                 </div>
                                <div class="section">
                                    <h2>Sites locations (Branches)</h2>
                                    {% if sites %}
                                        <p>{{ sites|map(attribute='site_name')|join(', ') }}</p>
                                    {% else %}
                                        <p>No sites locations found</p>
                                    {% endif %}
                                    <div class="action-links">
                                        <a class="#" href="{{ url_for('company_locations_v2.add_locations') }}">
                                            <i class="fi fi-rr-map-marker-home"></i>
                                            Add site
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                        {% else %}
                        <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 10px;">
                            <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/exclamation.png" alt="Attendance Management" class="xxsml">
                            <h3 class="red bold">Not enabled</h3>
                        </div>
                            <br>
                                <p >Attendance management service is not enabled for your company</p>
                                <p>To enable it, please Upgrade to a plan that includes attendance management</p>
                                <br>
                                <div class="action-links">
                                    <a href="{{ url_for('irembo_v2.create_invoice') }}">
                                        <i class="fi fi-rr-vote-yea"></i>
                                        Subscribe
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                        </div>
                        <div class="form--container">
                            <div class="inside--subcontainer">
                                <h1>Integrations</h1>
                            </div>
                            <div class="profile-header" style="text-align: left;">
                                <div class="grey-container">
                                    <p>Integrations guidelines (QuickBooks)</p>
                                    <div class="upload-info">
                                        <ol>
                                            <li>Start by creating an account on QuickBooks</li>
                                            <li>Then, you will be redirected to QuickBooks to authorize the connection</li>
                                            <li>Once authorized, you will log back to your Netpipo account</li>
                                            <li>Finally, after processing your payroll data, you can continue to post the journal entries to your QBO account</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            <div class="profile-content">
                                <div class="section">
                                    <div class="cont_x">
                                        <img src="https://netpipo.ams3.cdn.digitaloceanspaces.com/netpipo/app_files/Quickbooks_ec1d84d76b.png" alt="QuickBooks" class="xsml">
                                        <h2>QuickBooks</h2>
                                    </div> 
                                    
                                    {% if quickbooks_connected %}
                                        <p class="green"><strong>Status:</strong> Connected</p>
                                        <div class="action-links">
                                            <a href="{{ url_for('quickbooks_v2.disconnect') }}">
                                                <i class="fi fi-rr-plug-disconnect"></i>
                                                Disconnect
                                            </a>
                                        </div>
                                    {% else %}
                                        <p class="red"><strong>Status:</strong> Not connected</p>
                                        <div class="action-links">
                                            <a href="{{ url_for('quickbooks.get_auth_url') }}">
                                                <i class="fi fi-rr-plug-connection"></i>
                                                Connect
                                            </a>                                           
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
{% endblock %}
