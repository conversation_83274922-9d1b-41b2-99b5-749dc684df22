<!DOCTYPE html>
<html>
<head>
    <title>Update Company User</title>
</head>
    <body>
        <div class="real-form">
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
                    <i class="fas fa-home"></i> back
                </a>
            </div>
            <h1>Update Company User</h1>
            <form method="POST">
                {{ form.csrf_token }}
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">person</span>
                            {{ form.first_name(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">person</span>
                            {{ form.last_name(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">person</span>
                            {{ form.username(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">person</span>
                            {{ form.email(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone_number">Phone Number</label>
                        <div class="input-group-text">
                            <span class="material-symbols-outlined">phone</span>
                            {{ form.phone_number(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="role">Role</label>
                        <div class="input-group-text">
                            {{ form.role(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="Company">Company</label>
                        <div class="input-group-text">
                            {{ form.company(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-edit">Update</button>
                </div>
            </form>
        </div>
    </body>
</html>