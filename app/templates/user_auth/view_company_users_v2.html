<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="dynamic-title">Company Users</h1>
            <a class="template-link btn-edit" href="{{ url_for('user_data_v2.register_company_users') }}">
                <i class="fi fi-rr-plus-small"></i> Add User
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <table>
                <tr>
                    <th>Name</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Role</th>
                    <th>Actions</th>
                </tr>
                {% for user in users %}
                    <tr>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.phone_number }}</td>
                        <td>{{ user.role }}</td>
                        <td>
                            <a class="green" href="{{ url_for('user_data_v2.update_company_user', id=user.user_id) }}"><i class="fi fi-rr-edit"></i></a>
                        </td>
                            
                    </tr>
                {% endfor %}
            </table>
        </div>
    </div>
</div>
{% endblock %}