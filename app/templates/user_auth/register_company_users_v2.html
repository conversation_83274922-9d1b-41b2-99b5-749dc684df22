<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1 class="dynamic-title">Register Company Users</h1>
            <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('user_data_v2.view_company_users') }}">
                <i class="fi fi-rr-list"></i> Users
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <form action="{{ url_for('user_data_v2.register_company_users') }}" method="post">
                {{ form.csrf_token }}
                <div class="form-row">
                    <div class="form-group">
                        {{ form.first_name.label }}
                        <div class="input-group-text">
                            {{ form.first_name (class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.last_name.label }}
                        <div class="input-group-text">
                            {{ form.last_name (class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.email.label }}
                        <div class="input-group-text">
                            {{ form.email (class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.phone.label }}
                        <div class="input-group-text">
                            {{ form.phone (class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {{ form.company.label }}
                        <div class="input-group-text">
                            {{ form.company (class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form.role.label }}
                        <div class="input-group-text">
                            {{ form.role (class="form-control") }}
                        </div>
                    </div>
                </div>
                <button type="submit" class="submit-btn">Register</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}