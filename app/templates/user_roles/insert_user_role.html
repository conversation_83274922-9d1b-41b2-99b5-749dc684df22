<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Insert User Role</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        h1 {
            color: #333;
        }
        .messages ul {
            list-style-type: none;
            padding: 0;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .messages .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .messages .success {
            background-color: #d4edda;
            color: #155724;
        }
        form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        form p {
            margin-bottom: 15px;
        }
        form label {
            font-weight: bold;
            color: #555;
        }
        form input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        form input[type="submit"] {
            padding: 10px 20px;
            background-color: #007bff;
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
        }
        form input[type="submit"]:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        table th, table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background-color: #007bff;
            color: white;
        }
        table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        table tr:hover {
            background-color: #f1f1f1;
        }
    </style>
</head>
<body>
    <h1>Insert User Role</h1>
    <div class="messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul>
                {% for category, message in messages %}
                    <li class="{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>

    <form method="POST", action="{{ url_for('user_role.insert_user_role') }}">
        {{ form.hidden_tag() }}
        <p>
            {{ form.role_name.label }}<br>
            {{ form.role_name(size=32) }}<br>
            {% for error in form.role_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>
    </form>
<br>
<h1>Available Roles</h1>
    <table>
        <thead>
            <tr>
                
                <th>Role</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for role in user_roles %}
            <tr>
                
                <td>{{ role.role_name }}</td>
                <td>
                    <li><a href="{{ url_for('user_role.update_user_role', id=role.role_id) }}">Edit</a></li>
                    <li><a href="{{ url_for('user_role.delete_user_role', id=role.role_id) }}">Delete</a></li>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <br>
    <a href="{{ url_for('admin_data.admin_dashboard') }}">Back to  Dashboard</a>
</body>
</html>