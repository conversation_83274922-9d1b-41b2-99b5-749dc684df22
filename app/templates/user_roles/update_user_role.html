<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Update User Role</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        h1 {
            color: #333;
        }
        .messages ul {
            list-style-type: none;
            padding: 0;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .messages .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .messages .success {
            background-color: #d4edda;
            color: #155724;
        }
        form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        form p {
            margin-bottom: 15px;
        }
        form label {
            font-weight: bold;
            color: #555;
        }
        form input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        form input[type="submit"] {
            padding: 10px 20px;
            background-color: #007bff;
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Update User Role</h1>
    <div class="messages">
        <ul>
            {% for message in messages %}
                <li class="{{ message[0] }}">{{ message[1] }}</li>
            {% endfor %}
        </ul>
    </div>
    <form method="POST" action="{{ url_for('user_role.update_user_role', id=user_role.role_id) }}">
        {{ form.hidden_tag() }}
        <p>
            {{ form.role_name.label }}<br>
            {{ form.role_name(size=32) }}<br>
            {% for error in form.role_name.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>
    </form>
</body>
</html>