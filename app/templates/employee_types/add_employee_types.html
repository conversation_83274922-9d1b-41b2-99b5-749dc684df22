<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add Employee Type</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/employee_types.css') }}">
</head>
<body>
    <h1>Add Employee Type</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="messages">
                <ul>
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
    
    <form method="POST", action="{{ url_for('employee_types.add_employee_types') }}">
        {{ form.hidden_tag() }}
        <p>
            {{ form.employee_type_name.label }}<br>
            {{ form.employee_type_name(size=32) }}<br>
            {% for error in form.employee_type_name.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
      
        <p>{{ form.submit() }}</p>
    </form>
    <br>
    <h1>Available Types: </h1>
    <table>
        <thead>
            <tr>
                <th>Employee Type</th>
                <th>Actions</th>
                
            </tr>
        </thead>
        <tbody>
            {% for employee_type in employee_types %}
                <tr>
                    <td>{{ employee_type.employee_type_name }}</td>
                    <td>
                        <a href="{{ url_for('employee_types.update_employee_type', employee_type_id=employee_type.employee_type_id) }}">Update</a>
                        <a href="{{ url_for('employee_types.delete_employee_type', employee_type_id=employee_type.employee_type_id) }}">Delete</a>
                    </td>
                    
                </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
