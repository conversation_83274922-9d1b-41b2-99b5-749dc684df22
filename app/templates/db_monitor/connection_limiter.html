{% extends 'db_monitor/base.html' %}

{% block title %}Connection Limiter Statistics{% endblock %}

{% block page_title %}Connection Limiter Statistics{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fa fa-shield"></i> Connection Limiter Statistics
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Max Concurrent Connections</h5>
                                <h2 class="display-4">{{ limiter_stats.max_connections }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Current Connections</h5>
                                <h2 class="display-4">{{ limiter_stats.current_connections }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Available Connections</h5>
                                <h2 class="display-4">{{ limiter_stats.available_connections }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h4>Connection Limiter Usage</h4>
                        <div class="progress">
                            {% set usage = (limiter_stats.current_connections / limiter_stats.max_connections) * 100 %}
                            {% set color = 'bg-success' if usage < 50 else ('bg-warning' if usage < 80 else 'bg-danger') %}
                            <div class="progress-bar {{ color }}" role="progressbar" 
                                 style="width: {{ usage }}%;" 
                                 aria-valuenow="{{ usage }}" aria-valuemin="0" aria-valuemax="100">
                                {{ "%.1f"|format(usage) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-history"></i> Connection Limiter History
            </div>
            <div class="card-body">
                <canvas id="limiterHistoryChart" width="400" height="300"></canvas>
                
                <div class="table-responsive mt-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Total Connections Created</td>
                                <td>{{ limiter_stats.total_connections_created }}</td>
                            </tr>
                            <tr>
                                <td>Total Connections Released</td>
                                <td>{{ limiter_stats.total_connections_released }}</td>
                            </tr>
                            <tr>
                                <td>Total Wait Time (seconds)</td>
                                <td>{{ "%.2f"|format(limiter_stats.total_wait_time) }}</td>
                            </tr>
                            <tr>
                                <td>Average Wait Time (seconds)</td>
                                <td>{{ "%.4f"|format(limiter_stats.average_wait_time) }}</td>
                            </tr>
                            <tr>
                                <td>Max Wait Time (seconds)</td>
                                <td>{{ "%.4f"|format(limiter_stats.max_wait_time) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-database"></i> Database vs. Limiter
            </div>
            <div class="card-body">
                <canvas id="comparisonChart" width="400" height="300"></canvas>
                
                <div class="table-responsive mt-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Connection Limiter</th>
                                <th>Database</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Max Connections</td>
                                <td>{{ limiter_stats.max_connections }}</td>
                                <td>{{ database_stats.max_connections }}</td>
                            </tr>
                            <tr>
                                <td>Current Connections</td>
                                <td>{{ limiter_stats.current_connections }}</td>
                                <td>{{ database_stats.current_connections }}</td>
                            </tr>
                            <tr>
                                <td>Usage Percentage</td>
                                <td>{{ "%.1f"|format((limiter_stats.current_connections / limiter_stats.max_connections) * 100) }}%</td>
                                <td>{{ "%.1f"|format(database_stats.usage_percentage) }}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-info-circle"></i> Connection Limiter Information
            </div>
            <div class="card-body">
                <p>This page shows statistics from the application's connection limiter, which helps prevent database connection exhaustion.</p>
                <p>Key information:</p>
                <ul>
                    <li><strong>Max Concurrent Connections</strong>: The maximum number of concurrent database connections allowed by the limiter.</li>
                    <li><strong>Current Connections</strong>: The number of connections currently in use.</li>
                    <li><strong>Available Connections</strong>: The number of connections still available.</li>
                    <li><strong>Wait Time</strong>: Statistics about how long requests have waited for a connection.</li>
                </ul>
                <p class="text-muted">The connection limiter helps prevent the "too many clients" error by limiting the number of concurrent database connections.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Create charts for connection limiter
    document.addEventListener('DOMContentLoaded', function() {
        // Limiter history chart
        var historyCtx = document.getElementById('limiterHistoryChart').getContext('2d');
        var historyData = {
            labels: ['Created', 'Released', 'Current'],
            datasets: [{
                label: 'Connection Counts',
                data: [
                    {{ limiter_stats.total_connections_created }},
                    {{ limiter_stats.total_connections_released }},
                    {{ limiter_stats.current_connections }}
                ],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(23, 162, 184, 0.7)',
                    'rgba(0, 123, 255, 0.7)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(23, 162, 184, 1)',
                    'rgba(0, 123, 255, 1)'
                ],
                borderWidth: 1
            }]
        };
        var historyChart = new Chart(historyCtx, {
            type: 'bar',
            data: historyData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Connection Limiter History'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Comparison chart
        var comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
        var comparisonData = {
            labels: ['Max Connections', 'Current Connections'],
            datasets: [
                {
                    label: 'Connection Limiter',
                    data: [
                        {{ limiter_stats.max_connections }},
                        {{ limiter_stats.current_connections }}
                    ],
                    backgroundColor: 'rgba(0, 123, 255, 0.7)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Database',
                    data: [
                        {{ database_stats.max_connections }},
                        {{ database_stats.current_connections }}
                    ],
                    backgroundColor: 'rgba(40, 167, 69, 0.7)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1
                }
            ]
        };
        var comparisonChart = new Chart(comparisonCtx, {
            type: 'bar',
            data: comparisonData,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Connection Limiter vs. Database'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
    
    // Auto-refresh the page every 30 seconds
    setTimeout(function() {
        window.location.reload();
    }, 30000);
</script>
{% endblock %}
