<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Database Monitor{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .navbar {
            margin-bottom: 20px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
            background-color: #f1f5f9;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .progress {
            height: 25px;
        }
        .progress-bar {
            line-height: 25px;
            font-weight: bold;
        }
        .badge {
            font-size: 0.9em;
        }
        .badge-active {
            background-color: #28a745;
        }
        .badge-idle {
            background-color: #17a2b8;
        }
        .badge-transaction {
            background-color: #fd7e14;
        }
        .badge-error {
            background-color: #dc3545;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .refresh-btn {
            margin-bottom: 20px;
        }
        .stats-card {
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .connection-query {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .timestamp {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark rounded">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fa fa-database"></i> Database Monitor
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'overview' %}active{% endif %}" href="{{ url_for('db_monitor.monitor_db_connections_html') }}">
                                <i class="fa fa-tachometer"></i> Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'stats' %}active{% endif %}" href="{{ url_for('db_monitor.get_db_connection_stats_html') }}">
                                <i class="fa fa-bar-chart"></i> Connection Stats
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'details' %}active{% endif %}" href="{{ url_for('db_monitor.get_db_connection_details_html') }}">
                                <i class="fa fa-list"></i> Connection Details
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'limiter' %}active{% endif %}" href="{{ url_for('db_monitor.get_connection_limiter_stats_html') }}">
                                <i class="fa fa-shield"></i> Connection Limiter
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'pgbouncer' %}active{% endif %}" href="{{ url_for('db_monitor.get_pgbouncer_stats_html') }}">
                                <i class="fa fa-exchange"></i> PgBouncer Stats
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <div class="row">
            <div class="col-12">
                <h1 class="mt-4 mb-4">{% block page_title %}Database Monitor{% endblock %}</h1>
                
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="timestamp">
                        Last updated: {{ timestamp }}
                    </div>
                    <a href="{{ request.path }}" class="btn btn-primary refresh-btn">
                        <i class="fa fa-refresh"></i> Refresh
                    </a>
                </div>
                
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
