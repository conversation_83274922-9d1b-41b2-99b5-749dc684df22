{% extends 'db_monitor/base.html' %}

{% block title %}Database Connection Details{% endblock %}

{% block page_title %}Database Connection Details{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fa fa-list"></i> Connection Details for {{ database }}
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> Showing {{ connection_count }} active connections to the {{ database }} database.
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>PID</th>
                                <th>Username</th>
                                <th>Application</th>
                                <th>Client Address</th>
                                <th>State</th>
                                <th>Started</th>
                                <th>State Changed</th>
                                <th>Query</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for conn in connections %}
                            <tr>
                                <td>{{ conn.pid if conn.pid is not none else 'N/A' }}</td>
                                <td>{{ conn.usename if conn.usename is not none else 'N/A' }}</td>
                                <td>{{ conn.application_name if conn.application_name is not none else 'N/A' }}</td>
                                <td>{{ conn.client_addr if conn.client_addr is not none else 'local' }}</td>
                                <td>
                                    {% set state = conn.state if conn.state is not none else 'unknown' %}
                                    {% set badge_class = 'bg-success' if state == 'active' else ('bg-info' if state == 'idle' else ('bg-warning' if state == 'idle in transaction' else 'bg-secondary')) %}
                                    <span class="badge {{ badge_class }}">{{ state }}</span>
                                </td>
                                <td>
                                    {% if conn.backend_start is defined and conn.backend_start is not none %}
                                        {% if conn.backend_start is string %}
                                            {{ conn.backend_start }}
                                        {% else %}
                                            {{ conn.backend_start.strftime('%Y-%m-%d %H:%M:%S') }}
                                        {% endif %}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if conn.state_change is defined and conn.state_change is not none %}
                                        {% if conn.state_change is string %}
                                            {{ conn.state_change }}
                                        {% else %}
                                            {{ conn.state_change.strftime('%Y-%m-%d %H:%M:%S') }}
                                        {% endif %}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td class="connection-query">
                                    {% if conn.query is defined and conn.query is not none and conn.query|string|trim != '' %}
                                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#queryModal{{ conn.pid }}">
                                        View Query
                                    </button>

                                    <!-- Modal for query -->
                                    <div class="modal fade" id="queryModal{{ conn.pid }}" tabindex="-1" aria-labelledby="queryModalLabel{{ conn.pid }}" aria-hidden="true">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="queryModalLabel{{ conn.pid }}">Query for PID {{ conn.pid }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <pre><code>{{ conn.query|string }}</code></pre>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">No query</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-info-circle"></i> Connection Information
            </div>
            <div class="card-body">
                <p>This page shows detailed information about each active connection to the {{ database }} database.</p>
                <p>Key information:</p>
                <ul>
                    <li><strong>PID</strong>: The process ID of the connection.</li>
                    <li><strong>Username</strong>: The database user for the connection.</li>
                    <li><strong>Application</strong>: The name of the application making the connection.</li>
                    <li><strong>State</strong>: The current state of the connection (active, idle, idle in transaction, etc.).</li>
                    <li><strong>Query</strong>: The current or last executed query for this connection.</li>
                </ul>
                <p class="text-muted">For connection statistics, check the Connection Stats tab in the navigation bar.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Auto-refresh the page every 30 seconds
    setTimeout(function() {
        window.location.reload();
    }, 30000);
</script>
{% endblock %}
