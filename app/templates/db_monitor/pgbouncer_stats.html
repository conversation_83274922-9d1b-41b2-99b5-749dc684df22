{% extends 'db_monitor/base.html' %}

{% block title %}PgBouncer Statistics{% endblock %}

{% block page_title %}PgBouncer Statistics{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fa fa-exchange"></i> PgBouncer Connection Pools
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i> {{ error }}
                    {% if connection_info %}
                    <hr>
                    <p><strong>Connection Details:</strong> {{ connection_info }}</p>
                    <p class="mt-2">
                        <strong>Troubleshooting Tips:</strong>
                        <ul>
                            <li>Verify PgBouncer is running on the specified host and port</li>
                            <li>Check that the user has access to the PgBouncer admin console</li>
                            <li>Ensure PgBouncer is configured correctly with the 'pgbouncer' admin database</li>
                        </ul>
                    </p>
                    {% endif %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> Showing {{ pools|length }} connection pools from PgBouncer.
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Database</th>
                                <th>User</th>
                                <th>Client Connections</th>
                                <th>Server Connections</th>
                                <th>Pool Mode</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for pool in pools %}
                            <tr>
                                <td>
                                    {{ pool.database }}
                                    {% if pool.database == central_db %}
                                    <span class="badge bg-info">Central</span>
                                    {% endif %}
                                </td>
                                <td>{{ pool.user }}</td>
                                <td>
                                    <div>Active: <strong>{{ pool.cl_active }}</strong></div>
                                    <div>Waiting: {{ pool.cl_waiting }}</div>
                                </td>
                                <td>
                                    <div>Active: <strong>{{ pool.sv_active }}</strong></div>
                                    <div>Idle: {{ pool.sv_idle }}</div>
                                    <div>Used: {{ pool.sv_used }}</div>
                                </td>
                                <td>{{ pool.pool_mode }}</td>
                                <td>
                                    {% set total_clients = pool.cl_active + pool.cl_waiting %}
                                    {% set total_servers = pool.sv_active + pool.sv_idle + pool.sv_used %}
                                    {% if total_clients > 0 and total_servers > 0 %}
                                        <span class="badge bg-success">Active</span>
                                    {% elif total_clients > 0 %}
                                        <span class="badge bg-warning">Clients Only</span>
                                    {% elif total_servers > 0 %}
                                        <span class="badge bg-info">Servers Only</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-bar-chart"></i> Client Connections
            </div>
            <div class="card-body">
                <canvas id="clientConnectionsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-bar-chart"></i> Server Connections
            </div>
            <div class="card-body">
                <canvas id="serverConnectionsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-info-circle"></i> PgBouncer Information
            </div>
            <div class="card-body">
                <p>This page shows statistics from PgBouncer, the connection pooler used to manage database connections.</p>
                <p>Key information:</p>
                <ul>
                    <li><strong>Client Connections</strong>: Connections from your application to PgBouncer.</li>
                    <li><strong>Server Connections</strong>: Connections from PgBouncer to PostgreSQL.</li>
                    <li><strong>Pool Mode</strong>: How PgBouncer manages connections (transaction, session, or statement).</li>
                </ul>
                <p class="text-muted">PgBouncer helps prevent the "too many clients" error by pooling database connections.</p>

                <div class="alert alert-warning">
                    <i class="fa fa-lightbulb-o"></i> <strong>Tip:</strong> The number of server connections should be much lower than client connections. This indicates that connection pooling is working effectively.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Create charts for PgBouncer stats
    document.addEventListener('DOMContentLoaded', function() {
        {% if not error and pools %}
        // Client connections chart
        var clientCtx = document.getElementById('clientConnectionsChart').getContext('2d');
        var clientData = {
            labels: [{% for pool in pools %}'{{ pool.database }}',{% endfor %}],
            datasets: [
                {
                    label: 'Active Clients',
                    data: [{% for pool in pools %}{{ pool.cl_active }},{% endfor %}],
                    backgroundColor: 'rgba(40, 167, 69, 0.7)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Waiting Clients',
                    data: [{% for pool in pools %}{{ pool.cl_waiting }},{% endfor %}],
                    backgroundColor: 'rgba(255, 193, 7, 0.7)',
                    borderColor: 'rgba(255, 193, 7, 1)',
                    borderWidth: 1
                }
            ]
        };
        var clientChart = new Chart(clientCtx, {
            type: 'bar',
            data: clientData,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Client Connections by Database'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Connections'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Database'
                        }
                    }
                }
            }
        });

        // Server connections chart
        var serverCtx = document.getElementById('serverConnectionsChart').getContext('2d');
        var serverData = {
            labels: [{% for pool in pools %}'{{ pool.database }}',{% endfor %}],
            datasets: [
                {
                    label: 'Active Servers',
                    data: [{% for pool in pools %}{{ pool.sv_active }},{% endfor %}],
                    backgroundColor: 'rgba(0, 123, 255, 0.7)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Idle Servers',
                    data: [{% for pool in pools %}{{ pool.sv_idle }},{% endfor %}],
                    backgroundColor: 'rgba(23, 162, 184, 0.7)',
                    borderColor: 'rgba(23, 162, 184, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Used Servers',
                    data: [{% for pool in pools %}{{ pool.sv_used }},{% endfor %}],
                    backgroundColor: 'rgba(108, 117, 125, 0.7)',
                    borderColor: 'rgba(108, 117, 125, 1)',
                    borderWidth: 1
                }
            ]
        };
        var serverChart = new Chart(serverCtx, {
            type: 'bar',
            data: serverData,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Server Connections by Database'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Connections'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Database'
                        }
                    }
                }
            }
        });
        {% endif %}
    });

    // Auto-refresh the page every 30 seconds
    setTimeout(function() {
        window.location.reload();
    }, 30000);
</script>
{% endblock %}
