{% extends 'db_monitor/base.html' %}

{% block title %}Database Connection Overview{% endblock %}

{% block page_title %}Database Connection Overview{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fa fa-tachometer"></i> Connection Summary
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Databases Monitored</h5>
                                <h2 class="display-4">{{ results|length }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Active Connections</h5>
                                <h2 class="display-4">
                                    {% set total_active = 0 %}
                                    {% for result in results %}
                                        {% set total_active = total_active + result.active_connections %}
                                    {% endfor %}
                                    {{ total_active }}
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-database"></i> Database Connection Details
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Database</th>
                                <th>Active Connections</th>
                                <th>Connection Usage</th>
                                <th>Connection States</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in results %}
                            <tr>
                                <td>
                                    <strong>{{ result.database }}</strong>
                                    {% if result.database == central_db %}
                                    <span class="badge bg-info">Central</span>
                                    {% endif %}
                                </td>
                                <td>{{ result.active_connections }}</td>
                                <td>
                                    {% if result.stats and result.stats.usage_percentage is defined %}
                                    <div class="progress">
                                        {% set usage = result.stats.usage_percentage|float %}
                                        {% set color = 'bg-success' if usage < 50 else ('bg-warning' if usage < 80 else 'bg-danger') %}
                                        <div class="progress-bar {{ color }}" role="progressbar" 
                                             style="width: {{ usage }}%;" 
                                             aria-valuenow="{{ usage }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ "%.1f"|format(usage) }}%
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ result.stats.current_connections }} / {{ result.stats.max_connections }}</small>
                                    {% else %}
                                    <span class="text-muted">No data available</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if result.stats and result.stats.states %}
                                    <div>
                                        {% for state, count in result.stats.states.items() %}
                                        {% set badge_class = 'bg-success' if state == 'active' else ('bg-info' if state == 'idle' else ('bg-warning' if state == 'idle in transaction' else 'bg-secondary')) %}
                                        <span class="badge {{ badge_class }} me-1">{{ state }}: {{ count }}</span>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <span class="text-muted">No data available</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-info-circle"></i> Connection Information
            </div>
            <div class="card-body">
                <p>This page shows an overview of database connections across your multi-tenant system. The data is refreshed each time you load the page.</p>
                <p>Key information:</p>
                <ul>
                    <li><strong>Active Connections</strong>: The number of active connections to each database.</li>
                    <li><strong>Connection Usage</strong>: The percentage of maximum allowed connections currently in use.</li>
                    <li><strong>Connection States</strong>: The state of each connection (active, idle, idle in transaction, etc.).</li>
                </ul>
                <p class="text-muted">For more detailed information, check the other tabs in the navigation bar.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Auto-refresh the page every 30 seconds
    setTimeout(function() {
        window.location.reload();
    }, 30000);
</script>
{% endblock %}
