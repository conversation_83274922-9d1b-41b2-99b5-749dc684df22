{% extends 'db_monitor/base.html' %}

{% block title %}Database Connection Statistics{% endblock %}

{% block page_title %}Database Connection Statistics{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fa fa-bar-chart"></i> Connection Statistics for {{ database }}
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Current Connections</h5>
                                <h2 class="display-4">{{ stats.current_connections }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Maximum Connections</h5>
                                <h2 class="display-4">{{ stats.max_connections }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Usage Percentage</h5>
                                <h2 class="display-4">{{ "%.1f"|format(stats.usage_percentage) }}%</h2>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h4>Connection Usage</h4>
                        <div class="progress">
                            {% set usage = stats.usage_percentage|float %}
                            {% set color = 'bg-success' if usage < 50 else ('bg-warning' if usage < 80 else 'bg-danger') %}
                            <div class="progress-bar {{ color }}" role="progressbar" 
                                 style="width: {{ usage }}%;" 
                                 aria-valuenow="{{ usage }}" aria-valuemin="0" aria-valuemax="100">
                                {{ "%.1f"|format(usage) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-tasks"></i> Connection States
            </div>
            <div class="card-body">
                <canvas id="statesChart" width="400" height="300"></canvas>
                
                <div class="table-responsive mt-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>State</th>
                                <th>Count</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for state, count in stats.states.items() %}
                            <tr>
                                <td>
                                    {% set badge_class = 'bg-success' if state == 'active' else ('bg-info' if state == 'idle' else ('bg-warning' if state == 'idle in transaction' else 'bg-secondary')) %}
                                    <span class="badge {{ badge_class }}">{{ state }}</span>
                                </td>
                                <td>{{ count }}</td>
                                <td>{{ "%.1f"|format((count / stats.current_connections) * 100) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fa fa-database"></i> Connections by Database
            </div>
            <div class="card-body">
                <canvas id="databasesChart" width="400" height="300"></canvas>
                
                <div class="table-responsive mt-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Database</th>
                                <th>Connections</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for db, count in stats.databases.items() %}
                            <tr>
                                <td>
                                    {{ db }}
                                    {% if db == database %}
                                    <span class="badge bg-info">Current</span>
                                    {% endif %}
                                </td>
                                <td>{{ count }}</td>
                                <td>{{ "%.1f"|format((count / stats.current_connections) * 100) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Create charts for connection states and databases
    document.addEventListener('DOMContentLoaded', function() {
        // Connection states chart
        var statesCtx = document.getElementById('statesChart').getContext('2d');
        var statesData = {
            labels: [{% for state in stats.states.keys() %}'{{ state }}',{% endfor %}],
            datasets: [{
                label: 'Connection States',
                data: [{% for count in stats.states.values() %}{{ count }},{% endfor %}],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.7)',  // green for active
                    'rgba(23, 162, 184, 0.7)', // cyan for idle
                    'rgba(253, 126, 20, 0.7)', // orange for idle in transaction
                    'rgba(108, 117, 125, 0.7)' // gray for others
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(23, 162, 184, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(108, 117, 125, 1)'
                ],
                borderWidth: 1
            }]
        };
        var statesChart = new Chart(statesCtx, {
            type: 'pie',
            data: statesData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'Connection States'
                    }
                }
            }
        });
        
        // Databases chart
        var dbCtx = document.getElementById('databasesChart').getContext('2d');
        var dbData = {
            labels: [{% for db in stats.databases.keys() %}'{{ db }}',{% endfor %}],
            datasets: [{
                label: 'Connections by Database',
                data: [{% for count in stats.databases.values() %}{{ count }},{% endfor %}],
                backgroundColor: [
                    'rgba(0, 123, 255, 0.7)',
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(23, 162, 184, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(220, 53, 69, 0.7)',
                    'rgba(108, 117, 125, 0.7)'
                ],
                borderColor: [
                    'rgba(0, 123, 255, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(23, 162, 184, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(108, 117, 125, 1)'
                ],
                borderWidth: 1
            }]
        };
        var dbChart = new Chart(dbCtx, {
            type: 'pie',
            data: dbData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'Connections by Database'
                    }
                }
            }
        });
    });
    
    // Auto-refresh the page every 30 seconds
    setTimeout(function() {
        window.location.reload();
    }, 30000);
</script>
{% endblock %}
