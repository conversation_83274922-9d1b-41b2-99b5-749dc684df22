<!DOCTYPE html>
<html>
<head>
    <title>Add BRD Rate</title>
</head>
<body>
    <h1>Add BRD Deductions</h1>
   {% with messages = get_flashed_messages() %}
    {% if messages %}
        <ul>
            {% for message in messages %}
                <li>{{ message }}</li>
            {% endfor %}
        </ul>
    {% endif %}
    {% endwith %}
    <form method="post">
        {{ form.csrf_token }}
        {{ form.deduction_rate.label }}<br>
        {{ form.deduction_rate(size=32) }}<br>
        {% for error in form.deduction_rate.errors %}
            <span style="color: red;">[{{ error }}]</span>
        {% endfor %}
        <br>
        {{ form.submit() }}
    </form>
    <h1>BRD Rates</h1>
    <table border="1">
        <tr>
            <th>Rate %</th>
            <th>Actions</th>
        </tr>
            <tr>
                <td>{% if brd_rates['deduction_rate'] %}{{ brd_rates['deduction_rate']* 100 }}{% endif %}</td>
                   
                <td>
                    <a href="{{ url_for('brd_deductions.update_brd_rate',id=brd_rates['id']) }}">Update</a><br>
                    <a href="{{ url_for('brd_deductions.delete_brd_rate',id=brd_rates['id']) }}">Delete</a>
                </td>
            </tr>
        
</body>
</html>