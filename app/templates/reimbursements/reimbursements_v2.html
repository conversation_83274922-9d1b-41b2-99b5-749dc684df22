<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Reimbursements</h1>
                <a class ="template-link btn-edit" href="{{ url_for('reimbursements_v2.add_reimbursements') }}" >
                    <i class="fi fi-rr-plus-small"></i> Reimbursement
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="large--table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Employee</th>
                        <th>Description</th>
                        <th>Amount</th>
                        <th>Reimbursement Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for reimbursement in reimbursements %}
                        <tr>
                            <td>{{ Employee.get_employee_by_id(db_session, reimbursement.employee_id).full_name }} </td>
                            <td>{{ reimbursement.description }}</td>
                            <td>{{ Auxillary.format_amount(reimbursement.reimbursement_amount) }}</td>
                            <td>{{ reimbursement.reimbursement_date }}</td>
                            <td>
                                <div class="table-buttons">
                                    <a class="template-link btn-edit" href="{{ url_for('reimbursements_v2.update_reimbursement', reimbursement_id=reimbursement.reimbursement_id) }}"><i class="fi fi-rr-pencil"></i> Edit</a>
                                <a class="template-link btn-delete" href="{{ url_for('reimbursements_v2.delete_reimbursement', reimbursement_id=reimbursement.reimbursement_id) }}"
                                onclick="return confirm('Are you sure you want to delete this reimbursement?');" >
                                <i class="fi fi-rr-trash"></i> Delete</a>
                                </div>              
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}