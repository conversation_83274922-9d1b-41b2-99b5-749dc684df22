<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Add Insurance</title>
    <!--boostrap css-->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/forms.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <title>Update Reimbursement</title>
</head>
<body>
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="alert-container">
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            {% endfor %}
        </div>
    {% endif %}
    {% endwith %}
    <div class="real-form">
            <h1 class="header-title center">Update Reimbursement</h1>
        <form method="POST">
            {{ form.hidden_tag() }}
            <div class="form-row">
                <div class="form-group">
                    {{ form.description.label }}
                    <div class="input-group-text">
                        <span class="material-symbols-outlined">description</span>
                        {{ form.description(class="form-control") }}
                    </div>
                </div>
            </div>
            
                <div class="form-group">
                    {{ form.reimbursement_amount.label }}
                    <div class="input-group-text">
                        <span class="material-symbols-outlined">attach_money</span>
                        {{ form.reimbursement_amount(class="form-control") }}
                    </div>
                </div>
            
                <div class="form-group">
                    {{ form.reimbursement_date.label() }}
                    <div class="input-group-text">
                        <span class="material-symbols-outlined">date_range</span>
                        {{ form.reimbursement_date(class="form-control") }}
                    </div>
                </div>
            
            <div>
                {{ form.submit(class="btn-custom") }}
            </div>
            <a class="right" href="{{ url_for('admin_data.dashboard') }}">Back</a>
        </form>
    </div>
</body>

</html>