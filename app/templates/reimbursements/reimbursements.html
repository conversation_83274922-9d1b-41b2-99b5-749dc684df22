<!DOCTYPE html>
<html>
<head>
    <link href="{{ url_for('static', filename='styles/tables.css') }}" rel="stylesheet">
    <title>Reimbursements</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{ url_for('reimbursements.add_reimbursements') }}">
            <i class="fas fa-plus"></i> record reimbursements
        </a>
    </div>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <div>
    <div>
        <div class="dynamic--form">
            <div class="row">
                <h1>Reimbursements</h1>
                <i class="fas fa-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="The amount reimbursed to the employee, which will be added to their take-home pay in the same month the reimbursement was processed."></i>
            </div>
        <table>
            <thead>
                <tr>
                    <th>Employee</th>
                    <th>Description</th>
                    <th>Amount</th>
                    <th>Reimbursement Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for reimbursement in reimbursements %}
                    <tr>
                        <td>{{ Employee.get_employee_by_id(db_session, reimbursement.employee_id).full_name }} </td>
                        <td>{{ reimbursement.description }}</td>
                        <td>{{ Auxillary.format_amount(reimbursement.reimbursement_amount) }}</td>
                        <td>{{ reimbursement.reimbursement_date }}</td>
                        <td>
                            <div class="table-buttons">
                                <a class="template-link btn-edit" href="#" data-template-url="{{ url_for('reimbursements.update_reimbursement', reimbursement_id=reimbursement.reimbursement_id) }}"><i class="fi fi-rr-pencil"></i> Edit</a>
                            <a class="template-link btn-delete" href="#" data-template-url="{{ url_for('reimbursements.delete_reimbursement', reimbursement_id=reimbursement.reimbursement_id) }}"><i class="fi fi-rr-trash"></i> Delete</a>
                            </div>              
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</html>