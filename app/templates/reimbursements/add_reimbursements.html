<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <title>Add Reimbursements</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        
        <a class ="template-link btn-edit" href="#" data-template-url="{{ url_for('reimbursements.reimbursements') }}">
            <i class="fas fa-list"></i> Reimbursements
        </a>
    </div>
    <div class="dynamic--form">
        
            <h1 class="header-title">Add Reimbursements</h1>
            <!-- Flash messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
            {% endwith %}
            <div class="real--form">
            <form method="post" action="{{ url_for('reimbursements.add_reimbursements') }}">
            {{ form.csrf_token }}
                <div class="form-row">
                    <div class="form-group">
                        <label for="employee_select">Employee</label>
                    <select name="employee_select" id="employee_select" class="input-group-text form-control">
                        <option value="" disabled selected>Select Employee</option>
                        {% for employee in employees %}
                            <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                        {% endfor %}
                    </select>
                        {{ form.employee_id }}
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <div class="input-group-text">
                            {{ form.description(class="form-control") }}
                        </div>
                    </div>   
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="reimbursement_amount">Amount</label>
                        <div class="input-group-text">
                            {{ form.reimbursement_amount(class="form-control") }}
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="reimbursement_date">Reimbursement Date</label>
                        <div class="input-group-text">
                            {{ form.reimbursement_date(class="form-control") }}
                        </div>
                    </div>             
                </div>
                {{ form.submit(class="btn-custom") }}
        </form>
        </div>
    </div> 
    </div> 
    
</html>