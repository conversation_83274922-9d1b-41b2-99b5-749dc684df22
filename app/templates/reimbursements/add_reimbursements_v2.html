<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('reimbursements_v2.reimbursements') }}">
                    <i class="fi fi-rr-list"></i> Reimbursements
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1 class="header-title">Add Reimbursements</h1>
                    <div class="real--form">
                        <form method="post" action="{{ url_for('reimbursements_v2.add_reimbursements') }}">
                        {{ form.csrf_token }}
                        <div class="form-row">
                            <div class="form-group">
                                <label for="employee_select">Employee</label>
                                    <select name="employee_select" id="employee_select" class="input-group-text form-control">
                                        <option value="" disabled selected>Select Employee</option>
                                        {% for employee in employees %}
                                            <option value="{{ employee.employee_id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                    {{ form.employee_id }}
                            </div>
                            <div class="form-group">
                                <label for="reimbursement_date">Reimbursement Date</label>
                                <div class="input-group-text">
                                    {{ form.reimbursement_date(class="form-control") }}
                                </div>
                            </div>  
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="reimbursement_amount">Amount</label>
                                <div class="input-group-text">
                                    {{ form.reimbursement_amount(class="form-control") }}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="description">Description</label>
                                <div class="input-group-text">
                                    {{ form.description(class="form-control") }}
                                </div>
                            </div>   
           
                        </div>
                        <button type="submit" class="submit-btn">Save</button>
                </form>
                </div>
            </div> 
        </div>
</div>
{% endblock %}