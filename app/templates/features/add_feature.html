<!DOCTYPE html>
<html>
<head>
    <title>Add Feature</title>
</head>
<body>
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul>
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <h1>Add Feature</h1>
    <form  method="post">
        {{ form.csrf_token }}
        <p>
            {{ form.feature_name.label }}<br>
            {{ form.feature_name(size=32) }}
        </p>
        <p>
            {{ form.description.label }}<br>
            {{ form.description(size=32) }}
        </p>
        <p>
            {{ form.submit }}
        </p>
    </form>
    <br>
    <h1>Features</h1>
    <table>
        <tr>
            <th>Feature Name</th>
            <th>Description</th>
            <th>Actions</th>
        </tr>
        {% for feature in features %}
            <tr>
                <td>{{ feature.feature_name }}</td>
                <td>{{ feature.description }}</td>
                <td>
                    <a href="{{ url_for('features.update_feature', feature_id=feature.id) }}">Edit</a>
                    <a href="{{ url_for('features.delete_feature', feature_id=feature.id) }}">Delete</a>
            </tr>
        {% endfor %}
    </table>
</body>

</html>