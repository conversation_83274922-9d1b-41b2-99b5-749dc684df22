<!DOCTYPE html>
<html>
<head>
    <title>Edit Feature</title>
</head>
<body>
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul>
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <h1>Edit Feature</h1>
    <form  method="post">
        {{ form.csrf_token }}
        <p>
            {{ form.feature_name.label }}<br>
            {{ form.feature_name(size=32) }}
        </p>
        <p>
            {{ form.description.label }}<br>
            {{ form.description(size=32) }}
        </p>
        <p>
            {{ form.submit }}
        </p>
    </form>
    <br>
   
</html>