<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Update Second Employee Tax Bracket</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/taxforms.css') }}">
</head>
<body>
    <h1>Update Second Employee Tax Bracket</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="messages">
                <ul>
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    {% endwith %}
    
    <form method="POST">
        {{ form.hidden_tag() }}
        <p>
            {{ form.lower_bound.label }}<br>
            {{ form.lower_bound(size=32) }}<br>
            {% for error in form.lower_bound.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.upper_bound.label }}<br>
            {{ form.upper_bound(size=32) }}<br>
            {% for error in form.upper_bound.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.rate.label }}<br>
            {{ form.rate(size=32) }}<br>
            {% for error in form.rate.errors %}
                <span>[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>
    </form>
    </body>
</html>