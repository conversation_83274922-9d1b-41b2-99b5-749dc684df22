<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add Consultant Tax Bracket</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/taxforms.css') }}">
</head>
<body>
    <h1>Add Consultant Tax Bracket</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <ul>
            {% for category, message in messages %}
                <li class="{{ category }}">{{ message }}</li>
            {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    
    <form method="POST" action="{{ url_for('consultant.add_consultant_tax_bracket') }}">
        {{ form.hidden_tag() }}
        <p>
            {{ form.lower_bound.label }}<br>
            {{ form.lower_bound(size=32) }}<br>
            {% for error in form.lower_bound.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.upper_bound.label }}<br>
            {{ form.upper_bound(size=32) }}<br>
            {% for error in form.upper_bound.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>
            {{ form.rate.label }}<br>
            {{ form.rate(size=32) }}<br>
            {% for error in form.rate.errors %}
                <span style="color: red;">[{{ error }}]</span>
            {% endfor %}
        </p>
        <p>{{ form.submit() }}</p>
    </form>
    <h1>Available Consultant Tax Brackets</h1>
    <table>
        <thead>
            <tr>
                <th>Lower Bound</th>
                <th>Upper Bound</th>
                <th>Rate %</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for bracket in tax_brackets %}
            <tr>
                <td>{{ bracket.lower_bound }}</td>
                <td>{{ bracket.upper_bound }}</td>
                <td>{{ (bracket.rate) * 100 }}</td>
                <td><a href="{{ url_for('consultant.update_consultant_tax_bracket', id=bracket.bracket_id) }}">Edit</a></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <br>
    <a href="{{ url_for('admin_data.admin_dashboard') }}">Back to dashboard</a>
</body>
</html>
