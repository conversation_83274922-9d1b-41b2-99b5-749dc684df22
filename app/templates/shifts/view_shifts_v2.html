<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Working Shifts</h1>
                <a class="btn-edit" href="{{url_for('shifts_v2.create_shift')}}">
                    <i class="fi fi-rr-plus-small"></i> Shift
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <table>
                <tr>
                    <th>Shift Name</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>Shift Duration</th>
                    <th>Auto Clockout hours</th>
                    <th>Actions</th>
                </tr>
                {% for shift in shifts %}
                    <tr>
                        <td>{{ shift.name }}</td>
                        <td>{{ shift.start_time }}</td>
                        <td>{{ shift.end_time }}</td>
                        <td>{{ shift.shift_duration }} hrs</td>
                        <td>{{ shift.auto_clock_out_hours }}</td>
                        <td>
                            <div class="table-buttons">
                            <a class="green" href="{{ url_for('shifts_v2.update_shift', shift_id=shift.shift_id) }}">
                                <i class="fi fi-rr-edit"></i>
                            </a>
                            <a class="red" href="{{ url_for('shifts_v2.delete_shift', shift_id=shift.shift_id) }}"
                            onclick="return confirm('Are you sure you want to delete this shift?');">
                                <i class="fi fi-rr-trash"></i>
                            </a>
                        </div>
                        </td>
                    </tr>
                {% endfor %}
            </table>
        </div>
    </div>
{% endblock %}
