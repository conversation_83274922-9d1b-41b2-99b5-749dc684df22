<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a class="template-link btn-edit" href="{{ url_for('shifts_v2.view_shifts') }}">
                <i class="fi fi-rr-list"></i> Shifts
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <h1 class="header-title">Update Shift</h1>
            <form method="POST">
                {{ form.hidden_tag() }}
                <div class="form-row">
                    <div class="form-group">
                        {% for error in form.name.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        {{ form.name.label(class="form-label mt-2") }}
                        <div class="input-group-text">
                            {{ form.name(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {% for error in form.start_time.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        {{ form.start_time.label(class="form-label mt-2") }}
                        <div class="input-group-text">
                            {{ form.start_time(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {% for error in form.end_time.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        {{ form.end_time.label(class="form-label mt-2") }}
                        <div class="input-group-text">
                            {{ form.end_time(class="form-control") }}
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        {% for error in form.auto_clock_out_hours.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        {{ form.auto_clock_out_hours.label(class="form-label mt-2") }}
                        <div class="input-group-text">
                            {{ form.auto_clock_out_hours(class="form-control") }}
                        </div>
                    </div>
                </div>
                {{ form.submit(class="submit-btn") }}
            </form>
        </div>
    </div>
</div>
{% endblock %}  
