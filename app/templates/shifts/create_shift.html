<!DOCTYPE html>
<html>
<head>
    <title>Create Shift</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-arrow-back"></i> back
        </a>
        <a class ="template-link btn-edit" href="#" data-template-url="{{ url_for('shifts.view_shifts') }}">                
            <i class="fas fa-list"></i> Shifts
        </a>
    </div>
    <div class="real-form">
        <h1>Add Shift</h1>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul>
                {% for category, message in messages %}
                    <li class="{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        <form  method="post">
            {{ form.hidden_tag() }}
            <div class="form-row">
                <div class="form-group">
                    {{ form.name.label }}
                    <div class="input-group-text">
                        <i class="material-symbols-outlined icon">work</i>
                        {{ form.name(class="form-control") }}
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    {{ form.start_time.label }}
                    <div class="input-group-text">
                        <i class="material-symbols-outlined icon">schedule</i>
                        {{ form.start_time(class="form-control") }}
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    {{ form.end_time.label }}
                    <div class="input-group-text">
                        <i class="material-symbols-outlined icon">schedule</i>
                        {{ form.end_time(class="form-control") }}
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    {{ form.auto_clock_out_hours.label }}
                    <div class="input-group-text">
                        <i class="material-symbols-outlined icon">schedule</i>
                        {{ form.auto_clock_out_hours(class="form-control") }}
                    </div>
                </div>
            </div>
            {{ form.submit(class="btn-custom") }}
        </form>
    </div>
</body>

</html>