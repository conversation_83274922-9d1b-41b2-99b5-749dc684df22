<!DOCTYPE html>
<html>
<head>
    <title>View Shifts</title>
</head>
<body>
    <div class="dynamic--buttons">
        <a class="btn-edit" href="{{ url_for('admin_data.dashboard') }}">
            <i class="fas fa-home"></i> back
        </a>
        <a class="btn-edit template-link" href="#" data-template-url="{{url_for('shifts.create_shift')}}">
            <i class="fas fa-plus"></i> New Shift
        </a>
    </div>
    <h1>View Shifts</h1>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="list-unstyled">
                {% for category, message in messages %}
                    <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                    </li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>
    <table>
        <tr>
            <th>Shift Name</th>
            <th>Start Time</th>
            <th>End Time</th>
            <th>Shift Duration</th>
            <th>Auto Clockout hours</th>
            <th>Actions</th>
        </tr>
        {% for shift in shifts %}
            <tr>
                <td>{{ shift.name }}</td>
                <td>{{ shift.start_time }}</td>
                <td>{{ shift.end_time }}</td>
                <td>{{ shift.shift_duration }} hrs</td>
                <td>{{ shift.auto_clock_out_hours }}</td>
                <td>
                    <a class="template-link" href="#" data-template-url="{{ url_for('shifts.delete_shift', shift_id=shift.shift_id) }}">Delete</a>

                </td>
            </tr>
        {% endfor %}
    </table>
</body>
</html>