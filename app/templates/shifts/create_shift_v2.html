<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <a class ="template-link btn-edit" href="{{ url_for('shifts_v2.view_shifts') }}">
                    <i class="fi fi-rr-list"></i> Shifts
                </a>
            </div>
        </div>
        <div class="dyn_container">
            <div class="form--container">
                <h1>Add Shift</h1>
                <form  method="post">
                    {{ form.hidden_tag() }}
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.name.label }}
                            <div class="input-group-text">
                                {{ form.name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.start_time.label }}
                            <div class="input-group-text">
                                {{ form.start_time(class="form-control") }}
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            {{ form.end_time.label }}
                            <div class="input-group-text">
                                {{ form.end_time(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.auto_clock_out_hours.label }}
                            <div class="input-group-text">
                                {{ form.auto_clock_out_hours(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="submit-btn">Add Shift</button>
                </form>
            </div>
        </div>
    </div>
{% endblock %}