<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <h1>{{company_name}} Documents</h1>
            <div class="dynamic--buttons">
                <a class="btn-edit" href="{{ url_for('document_v2.upload_documents') }}" class="btn inverted-primary-button me-2">
                    <i class="fas fa-upload"></i> Upload Document
                </a>
                {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                <a class="btn-edit" href="{{ url_for('document_v2.upload_employee_document') }}" class="btn secondary-button">
                    <i class="fas fa-user-plus"></i> Upload for Employee
                </a>
                {% endif %}
            </div>
        </div>
        <div class="dyn_container">
            <div>
                {% if role in ['employee', 'supervisor'] %}
                    <div class="alert alert-info info" style="text-align: left;">
                        <i class="fas fa-info-circle"></i> You are viewing your personal documents. Only you and HR staff can see these documents.
                    </div>
                {% elif role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                    <div class="blue bg-blue box blue-outline" style="text-align: left; padding:10px 20px; border-radius: 10px">
                        <i class="fas fa-info-circle"></i> As {{ role }}, you can view all company documents and employee documents. Use the filters below to narrow your search.
                    </div>
                {% endif %}
            </div>
            <div class="form--container">
                <!-- Filter Section -->
                <div class="filter-section">
                    <h2>Filter Documents</h2>
                    <form id="filter-form" class="row g-3" action="{{ url_for('document_v2.view_documents') }}" method="get">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="document-type" class="form-label">Owner</label>
                                <select id="document-type" class="form-control" name="document_type">
                                    {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                                        <option value="" {% if not document_type_filter %}selected{% endif %}>All Types</option>
                                        <option value="company" {% if document_type_filter == 'company' %}selected{% endif %}>Company</option>
                                        <option value="employee" {% if document_type_filter == 'employee' %}selected{% endif %}>Employee</option>
                                    {% else %}
                                        <!-- Employees can only see their own documents -->
                                        <option value="employee" selected>My Documents</option>
                                    {% endif %}
                                </select>
                            </div>
                        
                        {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                        <div class="col-md-3">
                            <label for="employee-filter" class="form-label">Employee</label>
                            <select id="employee-filter" class="form-select" name="employee_id">
                                <option value="">All Employees</option>
                                {% for employee in employees %}
                                <option value="{{ employee.id }}" {% if employee_filter == employee.id|string %}selected{% endif %}>{{ employee.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}
                        <div class="col-md-{% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}3{% else %}4{% endif %}">
                            <label for="uploader" class="form-label">Uploaded By</label>
                            <input type="text" id="uploader" class="form-control" name="uploader" placeholder="Search by uploader" value="{{ uploader_filter or '' }}">
                        </div>
                        <div class="col-md-{% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}3{% else %}4{% endif %}">
                            <label for="date-filter" class="form-label">Date Range</label>
                            <select id="date-filter" class="form-select" name="date_range">
                                <option value="" {% if not date_filter %}selected{% endif %}>All Time</option>
                                <option value="today" {% if date_filter == 'today' %}selected{% endif %}>Today</option>
                                <option value="week" {% if date_filter == 'week' %}selected{% endif %}>This Week</option>
                                <option value="month" {% if date_filter == 'month' %}selected{% endif %}>This Month</option>
                            </select>
                        </div>
                        </div>
                        <div class="flex-container">
                            <button type="submit" class="submit-btn">Apply Filter</button>
                            <a href="{{ url_for('document_v2.view_documents') }}" class="mute-box">Reset</a>
                        </div>
                    </form>
                </div>
        </div>
        <!-- Documents Table -->
        <div class="document-table">
            <table class="table table-striped table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>File Name</th>
                        <th>Label</th>
                        <th>Owner</th>
                        {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                        <th>Employee</th>
                        {% endif %}
                        <th>Uploaded By</th>
                        <th>Date Uploaded</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="document-list">
                    {% for document in documents %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ document.file_name }}</td>
                        <td>
                            {% if document.file_label %}
                                <span class="document-label">{{ document.file_label }}</span>
                            {% else %}
                                <span class="text-muted">No label</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge {% if document.document_type == 'company' %}inverted-primary-button{% else %}inverted-secondary-button{% endif %}">
                                {{ document.document_type|capitalize }}
                            </span>
                        </td>
                        {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                        <td>
                            {% if document.document_type == 'employee' and document.employee_name %}
                                <a href="{{ url_for('document_v2.view_documents', document_type='employee', employee_id=document.employee_id) }}" class="blue">
                                    {{ document.employee_name }}
                                </a>
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        {% endif %}
                        <td>{{ document.uploaded_by }}</td>
                        <td>{{ document.uploaded_at }}</td>
                        <td class="document-actions">
                            <a href="{{ url_for('document_v2.download_document_route', document_id=document.document_id) }}" class="btn btn-sm primary-button">
                                <i class="fas fa-download"></i> Download
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add a message if no documents are found
            const documentList = document.getElementById('document-list');
            if (documentList.children.length === 0) {
                const table = documentList.closest('table');
                const noDocsMessage = document.createElement('div');
                noDocsMessage.className = 'alert alert-info mt-3';
                let uploadMessage = 'No documents found. Try adjusting your filters or <a href="{{ url_for("document_v2.upload_documents") }}">upload a new document</a>';
                {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                uploadMessage += ' or <a href="{{ url_for("document_v2.upload_employee_document") }}">upload for an employee</a>';
                {% endif %}
                uploadMessage += '.';
                noDocsMessage.innerHTML = uploadMessage;
                table.parentNode.appendChild(noDocsMessage);
                table.style.display = 'none';
            }

            // Add a count of documents found
            const docCount = document.createElement('div');
            docCount.className = 'text-muted mb-2';
            docCount.textContent = `Found ${documentList.children.length} document(s)`;
            documentList.closest('.document-table').prepend(docCount);
        });
    </script>
</div>
{% endblock %}
