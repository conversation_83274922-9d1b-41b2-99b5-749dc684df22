<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{company_name}} Documents</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .filter-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .document-label {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            background-color: #e9ecef;
            font-size: 0.85rem;
            margin-right: 5px;
        }
        .document-table {
            margin-top: 20px;
        }
        .document-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>{{company_name}} Documents</h1>
            <div>
                <a href="#" class="template-link btn inverted-primary-button me-2" data-template-url="{{ url_for('document.upload_documents') }}">
                    <i class="fas fa-upload"></i> Upload Document
                </a>
                {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                <a href="#" data-template-url="{{ url_for('document.upload_employee_document') }}" class="template-link btn secondary-button">
                    <i class="fas fa-user-plus"></i> Upload for Employee
                </a>
                {% endif %}
            </div>
        </div>

        <div class="container mt-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <ul class="list-unstyled">
                    {% for category, message in messages %}
                        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">&times;</button>
                        </li>
                    {% endfor %}
                    </ul>
                {% endif %}
            {% endwith %}
        </div>

        {% if role in ['employee', 'supervisor'] %}
            <div class="alert alert-info" style="text-align: left;">
                <i class="fas fa-info-circle"></i> You are viewing your personal documents. Only you and HR staff can see these documents.
            </div>
        {% elif role in ['hr', 'manager', 'accountant', 'company_hr'] %}
            <div class="secondary-button" style="text-align: left; padding:10px 20px; border-radius: 10px">
                <i class="fas fa-info-circle"></i> As {{ role }}, you can view all company documents and employee documents. Use the filters below to narrow your search.
            </div>
        {% endif %}

        <!-- Filter Section -->
        <div class="filter-section">
            <h5>Filter Documents</h5>
            <form id="filter-form" class="row g-3" action="{{ url_for('document.view_documents') }}" method="get">
                <div class="col-md-3">
                    <label for="document-type" class="form-label">Document Type</label>
                    <select id="document-type" class="form-select" name="document_type">
                        {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                            <option value="" {% if not document_type_filter %}selected{% endif %}>All Types</option>
                            <option value="company" {% if document_type_filter == 'company' %}selected{% endif %}>Company</option>
                            <option value="employee" {% if document_type_filter == 'employee' %}selected{% endif %}>Employee</option>
                        {% else %}
                            <!-- Employees can only see their own documents -->
                            <option value="employee" selected>My Documents</option>
                        {% endif %}
                    </select>
                </div>
                {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                <div class="col-md-3">
                    <label for="employee-filter" class="form-label">Employee</label>
                    <select id="employee-filter" class="form-select" name="employee_id">
                        <option value="">All Employees</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" {% if employee_filter == employee.id|string %}selected{% endif %}>{{ employee.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                <div class="col-md-{% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}3{% else %}4{% endif %}">
                    <label for="uploader" class="form-label">Uploaded By</label>
                    <input type="text" id="uploader" class="form-control" name="uploader" placeholder="Search by uploader" value="{{ uploader_filter or '' }}">
                </div>
                <div class="col-md-{% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}3{% else %}4{% endif %}">
                    <label for="date-filter" class="form-label">Date Range</label>
                    <select id="date-filter" class="form-select" name="date_range">
                        <option value="" {% if not date_filter %}selected{% endif %}>All Time</option>
                        <option value="today" {% if date_filter == 'today' %}selected{% endif %}>Today</option>
                        <option value="week" {% if date_filter == 'week' %}selected{% endif %}>This Week</option>
                        <option value="month" {% if date_filter == 'month' %}selected{% endif %}>This Month</option>
                    </select>
                </div>
                <div class="col-12 mt-3">
                    <button type="submit" class="btn inverted-primary-button">Apply Filter</button>
                    <a class="template-link btn btn-outline-secondary" data-template-url="{{ url_for('document.view_documents') }}">Reset</a>
                </div>
            </form>
        </div>

        <!-- Documents Table -->
        <div class="document-table">
            <table class="table table-striped table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>File Name</th>
                        <th>Label</th>
                        <th>Document Type</th>
                        {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                        <th>Employee</th>
                        {% endif %}
                        <th>Uploaded By</th>
                        <th>Date Uploaded</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="document-list">
                    {% for document in documents %}
                    <tr>
                        <th>{{ loop.index }}</th>
                        <td>{{ document.file_name }}</td>
                        <td>
                            {% if document.file_label %}
                                <span class="document-label">{{ document.file_label }}</span>
                            {% else %}
                                <span class="text-muted">No label</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge {% if document.document_type == 'company' %}inverted-primary-button{% else %}inverted-secondary-button{% endif %}">
                                {{ document.document_type|capitalize }}
                            </span>
                        </td>
                        {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                        <td>
                            {% if document.document_type == 'employee' and document.employee_name %}
                                <a href="{{ url_for('document.view_documents', document_type='employee', employee_id=document.employee_id) }}">
                                    {{ document.employee_name }}
                                </a>
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        {% endif %}
                        <td>{{ document.uploaded_by }}</td>
                        <td>{{ document.uploaded_at }}</td>
                        <td class="document-actions">
                            <a href="{{ url_for('document.download_document_route', document_id=document.document_id) }}" class="btn btn-sm primary-button">
                                <i class="fas fa-download"></i> Download
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add a message if no documents are found
            const documentList = document.getElementById('document-list');
            if (documentList.children.length === 0) {
                const table = documentList.closest('table');
                const noDocsMessage = document.createElement('div');
                noDocsMessage.className = 'alert alert-info mt-3';
                let uploadMessage = 'No documents found. Try adjusting your filters or <a href="{{ url_for("document.upload_documents") }}">upload a new document</a>';
                {% if role in ['hr', 'manager', 'accountant', 'company_hr'] %}
                uploadMessage += ' or <a href="{{ url_for("document.upload_employee_document") }}">upload for an employee</a>';
                {% endif %}
                uploadMessage += '.';
                noDocsMessage.innerHTML = uploadMessage;
                table.parentNode.appendChild(noDocsMessage);
                table.style.display = 'none';
            }

            // Add a count of documents found
            const docCount = document.createElement('div');
            docCount.className = 'text-muted mb-2';
            docCount.textContent = `Found ${documentList.children.length} document(s)`;
            documentList.closest('.document-table').prepend(docCount);
        });
    </script>
</body>
</html>