<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="dyn_header">
            <div class="dynamic--buttons">
                <h1>Upload Document for Employee</h1>
                <a href="{{ url_for('document_v2.view_documents') }}" class="btn-edit">
                    <i class="fi fi-rr-list"></i> View All Documents
                </a>
            </div>
            <div class="dyn_container">
                <div class="grey-container">
                    <div class="upload-info">
                        <h5>Employee Document Upload Guidelines</h5>
                        <ol>
                            <li>Select the employee from the dropdown list</li>
                            <li>Maximum file size: 10MB</li>
                            <li>Supported formats: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</li>
                            <li>Add a descriptive label to make the document easier to find</li>
                            <li>Your name and role will be recorded with the upload</li>
                            <li>The document will be associated with the selected employee</li>
                            <li>Only HR, managers, and the employee can view their documents</li>
                        </ol>
                    </div>
                </div>

                <div class="form--container">
                    <form method="POST" enctype="multipart/form-data" id="upload-form">
                        {{ form.hidden_tag() }}
                    <div class="form-row">
                        <div class="form-group">
                            {{ form.employee_id.label(class="form-label") }}
                            {{ form.employee_id(class="form-select") }}
                            {% if form.employee_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.employee_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <!--
                        <div class="file-preview" id="file-preview-container">
                            <i class="fas fa-file-upload fa-3x text-muted"></i>
                            <p class="mt-2 text-muted">Drag & drop files here or click to browse</p>
                            <img id="preview-image" class="preview-image" src="#" alt="File preview">
                            <div id="file-info" class="file-info">
                                <span id="file-name"></span>
                                <span id="file-size" class="text-muted"></span>
                            </div>
                        </div>
                        -->

                        <div class="form-group">
                            {{ form.file_label.label(class="form-label") }}
                            {{ form.file_label(class="form-control", placeholder="e.g., Contract, ID Card, Certificate") }}
                            {% if form.file_label.errors %}
                                <div class="text-danger">
                                    {% for error in form.file_label.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                        <div class="form-group">
                            {{ form.file.label(class="form-label") }}
                            {{ form.file(class="form-control", id="file-input") }}
                            {% if form.file.errors %}
                                <div class="text-danger">
                                    {% for error in form.file.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div style="margin-top: 10px;">
                            <button type="submit" class="submit-btn">{{ form.submit.label.text }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://kit.fontawesome.com/521075170e.js" crossorigin="anonymous"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const fileInput = document.getElementById('file-input');
                const previewContainer = document.getElementById('file-preview-container');
                const previewImage = document.getElementById('preview-image');
                const fileInfo = document.getElementById('file-info');
                const fileName = document.getElementById('file-name');
                const fileSize = document.getElementById('file-size');

                // Handle drag and drop
                previewContainer.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    previewContainer.style.borderColor = '#007bff';
                });

                previewContainer.addEventListener('dragleave', function() {
                    previewContainer.style.borderColor = '#dee2e6';
                });

                previewContainer.addEventListener('drop', function(e) {
                    e.preventDefault();
                    previewContainer.style.borderColor = '#dee2e6';

                    if (e.dataTransfer.files.length) {
                        fileInput.files = e.dataTransfer.files;
                        updateFilePreview(e.dataTransfer.files[0]);
                    }
                });

                previewContainer.addEventListener('click', function() {
                    fileInput.click();
                });

                fileInput.addEventListener('change', function() {
                    if (fileInput.files.length) {
                        updateFilePreview(fileInput.files[0]);
                    }
                });

                function updateFilePreview(file) {
                    // Show file info
                    fileName.textContent = file.name;
                    fileSize.textContent = formatFileSize(file.size);
                    fileInfo.style.display = 'block';

                    // Show preview for images
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImage.src = e.target.result;
                            previewImage.style.display = 'block';
                        }
                        reader.readAsDataURL(file);
                    } else {
                        previewImage.style.display = 'none';
                    }
                }

                function formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            });
        </script>
    </div>
{% endblock %}