<!DOCTYPE html>
<html>
<head>
    <title>View Feedback</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            margin: 20px;
        }
        
        h1 {
            text-align: center;
            color: #333;
        }

        table {
            width: 80%;
            margin: 20px auto;
            border-collapse: collapse;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }

        th {
            background-color: #4CAF50;
            color: white;
            text-transform: uppercase;
        }

        td {
            font-size: 14px;
            color: #333;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        p {
            text-align: center;
            color: #666;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>View Feedback</h1>

    {% if feedbacks %}
        <table>
            <tr>
                <th>Subject</th>
                <th>Message</th>
                <th>Created At</th>
            </tr>

            {% for feedback in feedbacks %}
                {% if feedback %}
                    {% for item in feedback %}
                    <tr>
                        <td>{{ item['subject'] }}</td>
                        <td>{{ item['message'] }}</td>
                        <td>{{ item['created_at'] }}</td>
                    </tr>
                    {% endfor %}
                {% endif %}
            {% endfor %}
        </table>
    {% else %}
        <p>No feedback available</p>
    {% endif %}

    <p><a href="{{ url_for('admin_data.admin_dashboard') }}">Back to Admin Dashboard</a></p>

</body>
</html>
