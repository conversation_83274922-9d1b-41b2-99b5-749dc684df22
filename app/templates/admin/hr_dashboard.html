<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet"
        href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='images/system_images/Favicon_s.png') }}">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet' href='https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-solid-chubby/css/uicons-solid-chubby.css">
</head>
<body>
    <div class="dashboard">
        <div class="sidebar-container">
            <img src="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}" alt="netpipo logo"
                class="d-logo">
            <div class="sidebar">
                <div class="side-buttons">
                    <p>Menu</p>
                    <ul>
                        <li class="">
                            <a href="/hr_dashboard">
                                <i class="fi fi-rr-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="employees">
                                    <i class="fi fi-rr-employees"></i>
                                    <span>Employees</span>
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="employees">
                                <li>
                                    <a class="template-link" a href="#"
                                    data-template-url="{{ url_for('employees.register_employees') }}">                                        
                                    <i class="fi fi-rr-user-add"></i>
                                        <span>Add Employee</span>
                                    </a>
                                </li>
                                <li>
                                    <a class="template-link" href="#" data-template-url= "{{ url_for('employees.employees_list') }}">
                                        <i class="fi fi-rr-user"></i>
                                        <span>All Employees</span>
                                    </a>
                                </li>
                                <li>
                                    <a class=" push--up template-link" href="{{ url_for('employees.get_inactive_employees') }}"
                                    data-template-url="{{ url_for('employees.get_inactive_employees') }}">
                                        <i class="fi fi-rr-user-lock"></i>
                                        <span>Inactive Employees</span>
                                    </a>
                                </li>
                            </div>
                        </div>
                        <li>
                            <a class="template-link" href="#" data-template-url="{{ url_for('departments.departments') }}">
                                <i class="fi fi-rr-department-structure"></i>
                                <span>Departments</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="side-buttons">
                    <p>Payroll &amp; Benefits</p>
                    <ul>
                        <div class="feature-content">
                            <li>
                                <a href="/payroll" data-feature="payroll">
                                    <i class="fi fi-rr-calculator-money"></i>
                                    <span>Payroll</span>
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="payroll">
                                <li>
                                    <a href="#">
                                        <i class="fi fi-rr-calculator-simple"></i>
                                        <span>Process Payroll</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fi fi-rr-file"></i>
                                        <span>Payroll Reports</span>
                                    </a>
                                </li>
                            </div>
                        </div>
                        <div class="feature-content">
                            <li>
                                <a href="#" data-feature="benefits">
                                    <i class="fi fi-rr-money-bills-simple"></i>
                                    <span>Adjustments</span>
                                    <i class="fi fi-rr-angle-small-down"></i>
                                </a>
                            </li>
                            <div class="feature-items" data-feature="benefits">
                                <li>
                                    <a href="#">
                                        <i class="fi fi-rr-add"></i>
                                        <span>Reimbursements</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fi fi-rr-comment-minus"></i>
                                        <span>Other deductions</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fi fi-rr-money"></i>
                                        <span>Advances</span>
                                    </a>
                                </li>
                            </div>
                        </div>
                        <li>
                            <a href="#">
                                <i class="fi fi-rr-doctor"></i>
                                <span>Medical Insurance</span>
                            </a>
                        </li>
                    </ul>
                </div>
                {% if attendance_service %}
                    <div class="side-buttons">
                        <p>Attendance &amp; Leave</p>
                        <ul>
                            <div class="feature-content">
                                <li>
                                    <a href="#" data-feature="attendance">
                                        <i class="fi fi-rr-invite-alt"></i>
                                        Attendance
                                        <i class="fi fi-rr-angle-small-down"></i>
                                    </a>
                                </li>
                                <div class="feature-items" data-feature="attendance">
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-sign-in-alt"></i>
                                            Clock In
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-sign-out-alt"></i>
                                            Clock Out
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-alarm-clock"></i>
                                            Attendance records
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-list-check"></i>
                                            Timesheet
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-calendar"></i>
                                            Shifts
                                        </a>
                                    </li>
                                </div>
                            </div>
                            <div class="feature-content">
                                <li>
                                    <a href="#" data-feature="leave">
                                        <i class="fi fi-rr-padlock-check"></i>
                                        Leave
                                        <i class="fi fi-rr-angle-small-down"></i>
                                    </a>
                                </li>
                                <div class="feature-items" data-feature="leave">
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-email-pending"></i>
                                            Leave Requests
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-memo-circle-check"></i>
                                            Leave Approvals
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-poll-h"></i>
                                            Leave Records
                                        </a>
                                    </li>
                                </div>
                            </div>
                        </ul>
                    </div>
                    {% endif %}
                    <div class="side-buttons">
                        <p>Documents (Files)</p>
                        <ul>
                            <div class="feature-content">
                                <li>
                                    <a href="#" data-feature="documents">
                                        <i class="fi fi-rr-invite-alt"></i>
                                        Documents
                                        <i class="fi fi-rr-angle-small-down"></i>
                                    </a>
                                </li>
                                <div class="feature-items" data-feature="documents">
                                    <li>
                                        <a href="#">
                                            <i class="fi fi-rr-sign-in-alt"></i>
                                            All files
                                        </a>
                                    </li>
                                </div>
                            </div>
                        </ul>
                    </div>
                <div class="side-buttons">
                    <p>System Settings</p>
                    <ul>
                        <li>
                            <a href="#">
                                <i class="fi fi-rr-settings"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="header">
            <div class="company-info">
                <i class="fi fi-rr-building"></i>
                <p>Company name</p>
                <i class="fi fi-rr-angle-small-down"></i>
            </div>
            <div class="search-bar text-box">
                <input type="text" placeholder="Search">
                <i class="fi fi-rr-search"></i>
            </div>
            <div class="employee-info">
                <i class="fi fi-rr-user icon primary"></i>
                <div class="user--info">
                    <p>{{ first_name }} {{ last_name }}, </p>
                    <div class="user--role">
                        <div class="role">
                            <p>{{ role }}</p>
                        </div>
                    </div>
                </div>
                <i class="fi fi-rr-angle-small-down"></i>
                <div class="notification">
                    <i class="fi fi-rr-bell"></i>
                    <div class="red-dot"></div>
                </div>
            </div>
        </div>
        <div class="dynamic-container">
        <div class="all-card--wrappers">
            <div class="row-data first-row">
                <div class="comb-cards">
                    <div class="dsh-card employees">
                        <div class="card-title">
                            <p><strong>Employees</strong></p>
                            <i class="fi fi-rr-employees"></i>
                        </div>
                        <div class="card-data">
                            <p>{{ no_employee }}</p>
                        </div>
                        <div class="card-desc">
                            <p><strong>Total number of</strong><br>registered employees</p>
                        </div>
                    </div>
                    <div class="mult-cards">
                        <div class="short-card">
                            <div class="card-data active">
                                <i class="fi fi-rr-user-check"></i>
                            </div>
                            <div class="card-title">
                                <p><strong>Active</strong></p>
                                <p>10</p>
                            </div>
                        </div>
                        <div class="short-card inactive">
                            <div class="card-data inactive">
                                <i class="fi fi-rr-user-lock"></i>
                            </div>
                            <div class="card-title">
                                <p><strong>Inactive</strong></p>
                                <p>2</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="comb-cards">
                    <div class="dsh-card">
                        <div class="card-title">
                            <p><strong>Attendance</strong></p>
                            <i class="fi fi-rr-invite-alt"></i>
                        </div>

                        <div class="card-desc">
                            <p>Tracks the number of employees present, absent, on leave, and on off</p>
                        </div>
                    </div>
                    <div class="mult-cards">
                        <div class="short-card">
                            <div class="card-data">
                                <i class="fi fi-rr-calendar-check"></i>
                            </div>
                            <div class="card-title">
                                <p><strong>Present</strong></p>
                                <p>{{num_present}}</p>
                            </div>
                        </div>
                        <div class="short-card">
                            <div class="card-data">
                                <i class="fi fi-rr-calendar-xmark"></i>
                            </div>
                            <div class="card-title">
                                <p><strong>Absent</strong></p>
                                <p>{{num_absent}}</p>
                            </div>
                        </div>
                        <div class="short-card">
                            <div class="card-data">
                                <i class="fi fi-rr-bag-shopping-minus"></i>
                            </div>
                            <div class="card-title">
                                <p><strong>On Leave</strong></p>
                                <p>{{num_leave}}</p>
                            </div>
                        </div>
                        <div class="short-card">
                            <div class="card-data">
                                <i class="fi fi-rr-calendar-xmark"></i>
                            </div>
                            <div class="card-title">
                                <p><strong>On Off</strong></p>
                                <p>{{num_off}}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flexed">
                    <div class="dsh-card">
                        <div class="card-title">
                            <p><strong>Leave requests</strong></p>
                            <i class="fi fi-rr-bubble-discussion"></i>
                        </div>
                        <div class="card-data">
                            <p>{{num_leave_requests}}</p>
                        </div>
                        <div class="card-desc">
                            <p><strong>Number of</strong><br>pending leave requests</p>
                        </div>
                    </div>
                    <div class="dsh-card">
                        <div class="card-title">
                            <p><strong>Advance requests</strong></p>
                            <i class="fi fi-rr-bubble-discussion"></i>
                        </div>
                        <div class="card-data">
                            <p>{{num_pending_advance_requests}}</p>
                        </div>
                        <div class="card-desc">
                            <p><strong>Number of</strong><br>pending advance requests</p>
                        </div>
                    </div>

                </div>
            </div>
            <div class="row-data second-row">
                <div class="{{ 'card--wrapper-3' if attendance_service else 'card--wrapper-3-limit'}} top-card-wrapper">
                    <div class="card-title">
                        <p><strong>Journal entries</strong></p>
                        <i class="fi fi-rr-e-learning"></i>
                    </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Details</th>
                                        <th>Credit</th>
                                        <th>Debit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Gross Salaries</td>
                                        <td>RWF {{ total_gross }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Pension Expense({{ pension_er_rate * 100 }}%)</td>
                                        <td>RWF {{ total_pension_er }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Maternity Expense({{ maternity_er_rate * 100 }}%)</td>
                                        <td>RWF {{ total_maternity_er }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Medical Contribution-ER({{ employee_insurance * 100 }}%)</td>
                                        <td>RWF {{ total_rama_ee }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Salary Advances</td>
                                        <td>RWF</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Net Salaries</td>
                                        <td>RWF {{ total_net_salary }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Pension Payable({{ (pension_er_rate + pension_ee_rate) * 100 }}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_pension }}</td>
                                    </tr>
                                    <tr>
                                        <td>Maternity Payable({{ (maternity_er_rate + maternity_ee_rate) * 100 }}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_maternity }}</td>
                                    </tr>
                                    <tr>
                                        <td>Medical Contribution Payable({{ (employee_insurance + employer_insurance) * 100 }}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_insurance }}</td>
                                    </tr>
                                    <tr>
                                        <td>CBHI Payable({{ cbhi_ee_rate * 100 }}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_cbhi }}</td>
                                    </tr>
                                    <tr>
                                        <td>Paye Payable</td>
                                        <td></td>
                                        <td>RWF {{ total_payee }}</td>
                                    </tr>
                                    <tr>
                                        <td>Other Deductions</td>
                                        <td></td>
                                        <td>RWF</td>
                                    </tr>
                                    <tr>
                                        <td>TOTAL</td>
                                        <td>RWF {{ total_credits }}</td>
                                        <td>RWF {{ total_debits }}</td>
                                    </tr>
                                </tbody>
                            </table>
                    </div>
                </div>
                <div
                    class="{{'card--wrapper-4' if attendance_service else 'card--wrapper-4-limit'}} global-card-wrapper">
                    <div class="card-title">
                        <p><strong>Payroll tax summary</strong></p>
                        <button class="btn primary-btn" id="btn-download-payslip"><a class="template-link" href="#"
                            data-template-url="{{ url_for('payroll_summary.payroll_summary') }}">Process</a></button>
                        <i class="fi fi-rr-e-learning"></i>
                    </div>

                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Details</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>PAYE</td>
                                        <td>RWF {{ total_payee }}</td>
                                    </tr>
                                    <tr>
                                        <td>CBHI Payable({{ cbhi_ee_rate * 100 }}%)</td>
                                        <td>RWF {{ total_cbhi }}</td>
                                    </tr>
                                    <tr>
                                        <td>Maternity Payable({{ maternity_er_rate * 100 }}%)</td>
                                        <td>RWF {{ total_maternity_er }}</td>
                                    </tr>
                                    <tr>
                                        <td>Pension Payable({{ (pension_er_rate + pension_ee_rate) * 100 }}%)</td>
                                        <td>RWF {{ total_pension }}</td>
                                    </tr>
                                    <tr>
                                        <td>Rama Payable({{ (employee_insurance + employer_insurance) * 100 }}%)</td>
                                        <td>RWF {{ total_insurance }}</td>
                                    </tr>
                                    <tr>
                                        <td>Total</td>
                                        <td>RWF</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dynamic-content">

        </div>
        <div class="footer">
            <p>Netpipo &copy; 2025</p>
        </div>
    </div>
    <div id="subPopup" class="popup" style="display: none;">
        <div class="popup-content">
            <h1>Upgrade your Subscription Plan</h1>
            <p>You need to Upgrade your subscription plan to access this feature.</p>
            <div class="decisive-btn">
                <a href="{{ url_for('pages.pricing') }}" class="btn-continue" target="_blank">View Plans</a>
                <a href="{{ url_for('admin_data.dashboard') }}" class="btn-cancel" id="closePopup">Close</a>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/load_contents_dyn.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/dashboard.js') }}"></script>
</body>
</html>