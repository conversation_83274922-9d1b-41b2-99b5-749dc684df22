<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
        <div class="ls_container">
            <div class="dyn_header">
                <h1>Welcome, {{ last_name }}</h1>
            </div>
            <div class="dyn_container">
            <div class="row-data first-row">
                <div class="comb-cards">
                    <div class="dsh-card employees">
                        <div class="card-title">
                            <p><strong>Employees</strong></p>
                            <i class="fi fi-rr-employees"></i>
                        </div>
                        <div class="card-data">
                            <p>{{ no_employee }}</p>
                        </div>
                        <div class="card-desc">
                            <p><strong>Total number of</strong><br>registered employees</p>
                        </div>
                    </div>
                    <div class="mult-cards">
                        <a href="{{ url_for('my_employees.employees_list') }}" >
                            <div class="short-card primary-hover">
                                <div class="card-data active">
                                    <i class="fi fi-rr-user-check"></i>
                                </div>
                            
                                <div class="card-title">
                                    <p style="font-weight: bold">{{num_active_employees}}</p>
                                    <p>Active</p>

                                </div>
                            </div>
                        </a>
                        <a href="{{ url_for('my_employees.get_inactive_employees') }}" >
                            
                            <div class="short-card inactive grey-hover">
                                <div class="card-data inactive">
                                    <i class="fi fi-rr-user-lock"></i>
                                </div>
                                <div class="card-title">
                                    <p style="font-weight: bold">{{num_inactive_employees}}</p>

                                    <p>Inactive</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="comb-cards">
                    <div class="dsh-card">
                        <div class="card-title">
                            <p><strong>Attendance</strong></p>
                            <i class="fi fi-rr-invite-alt"></i>
                        </div>

                        <div class="card-desc">
                            <p>Tracks the number of employees present, absent, on leave, and on off</p>
                        </div>
                    </div>
                    <div class="mult-cards">
                    <a href="{{ url_for('attendance_v2.list_daily_attendance') }}">
                        <div class="short-card light-green">
                            <div class="card-data">
                                <i class="fi fi-rr-calendar-check"></i>
                            </div>
                            <div class="card-title">
                                <p style="font-weight: 600">{{num_present}}</p>
                                <p>Present</p>
                            </div>
                        </div>
                    </a>
                    <!--
                    <a href="#">
                        <div class="short-card red-hover">
                            <div class="card-data">
                                <i class="fi fi-rr-calendar-xmark"></i>
                            </div>
                            <div class="card-title">
                                <p style="font-weight: 600">{{num_absent}}</p>
                                <p>Absent</p>
                            </div>
                        </div>
                    </a>
                    -->
                    <a href="{{ url_for('leave_applications_v2.view_approval_logs') }}">
                        <div class="short-card blue-hover">
                            <div class="card-data">
                                <i class="fi fi-rr-bag-shopping-minus"></i>
                            </div>
                            <div class="card-title">
                                <p style="font-weight: 600">{{num_leave}}</p>

                                <p>Leave</p>
                            </div>
                        </div>
                    </a>
                    </div>
                </div>
                <div class="flexed">
                    <a href="{{ url_for('leave_applications_v2.get_leave_application_for_employees') }}">
                        <div class="dsh-card">
                            <div class="card-title">
                                <p><strong>Leave requests</strong></p>
                                <i class="fi fi-rr-bubble-discussion"></i>
                            </div>
                            <div class="card-data">
                                <p>{{num_leave_requests}}</p>
                            </div>
                            <div class="card-desc">
                                <p><strong>Number of</strong><br>pending leave requests</p>
                            </div>
                        </div>
                    </a>
                    <a href="#">
                        <div class="dsh-card">
                        
                            <div class="card-title">
                                <p><strong>Advance requests</strong></p>
                                <i class="fi fi-rr-bubble-discussion"></i>
                            </div>
                            <div class="card-data">
                                <p>{{num_pending_advance_requests}}</p>
                            </div>
                            <div class="card-desc">
                                <p><strong>Number of</strong><br>pending advance requests</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row-data second-row">
                <div class="{{ 'card--wrapper-3' if attendance_service else 'card--wrapper-3-limit'}} top-card-wrapper">
                    <div class="card-title">
                        <p><strong>Journal entries</strong></p>
                        <i class="fi fi-rr-e-learning"></i>
                    </div>
                        <div class="table-container">
                            <table class="no-vertical-border">
                                <thead>
                                    <tr>
                                        <th>Details</th>
                                        <th>Debit</th>
                                        <th>Credit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Gross Salaries</td>
                                        <td>RWF {{ total_gross }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Pension Expense({{ Auxillary.format_decimal((pension_er_rate + occupational_hazard_rate) * 100) }}%)</td>
                                        <td>RWF {{ total_pension_er }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Maternity Expense({{Auxillary.format_decimal(maternity_er_rate * 100) }}%)</td>
                                        <td>RWF {{ total_maternity_er }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Medical Contribution-ER({{ Auxillary.format_decimal(employer_insurance * 100) }}%)</td>
                                        <td>RWF {{ total_rama_ee }}</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Net Salaries</td>
                                        <td></td>
                                        <td>RWF {{ total_net_salary }}</td>
                                    </tr>
                                    <tr>
                                        <td>Pension Payable({{ Auxillary.format_decimal((pension_er_rate + occupational_hazard_rate + pension_ee_rate) * 100)}}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_pension }}</td>
                                    </tr>
                                    <tr>
                                        <td>Maternity Payable({{ Auxillary.format_decimal((maternity_er_rate + maternity_ee_rate) * 100 )}}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_maternity }}</td>
                                    </tr>
                                    <tr>
                                        <td>Medical Contribution Payable({{ Auxillary.format_decimal((employee_insurance + employer_insurance) * 100 )}}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_insurance }}</td>
                                    </tr>
                                    <tr>
                                        <td>CBHI Payable({{ Auxillary.format_decimal(cbhi_ee_rate * 100 )}}%)</td>
                                        <td></td>
                                        <td>RWF {{ total_cbhi }}</td>
                                    </tr>
                                    <tr>
                                        <td>Paye Payable</td>
                                        <td></td>
                                        <td>RWF {{ total_payee }}</td>
                                    </tr>
                                    <tr>
                                        <td>Salary Advances</td>
                                        <td></td>
                                        <td>RWF</td>
                                    </tr>
                                    <tr>
                                        <td>Other Deductions</td>
                                        <td></td>
                                        <td>RWF</td>
                                    </tr>
                                    <tr class="tfooter">
                                        <td>TOTAL</td>
                                        <td>RWF {{ total_debits }}</td>
                                        <td>RWF {{ total_credits }}</td>
                                    </tr>
                                </tbody>
                            </table>
                    </div>
                </div>
                <div
                    class="{{'card--wrapper-4' if attendance_service else 'card--wrapper-4-limit'}} global-card-wrapper">
                    <div class="card-title">
                        <p><strong>Payroll tax summary</strong></p>
                        <button class="btn primary-btn">
                            <a href="{{ url_for('payroll_summary_v2.payroll_summary') }}">
                            Process</a></button>
                        <i class="fi fi-rr-e-learning"></i>
                    </div>
                        <div class="table-container">
                            <table class="no-vertical-border">
                                <thead>
                                    <tr>
                                        <th>Details</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>PAYE</td>
                                        <td>RWF {{ total_payee }}</td>
                                    </tr>
                                    <tr>
                                        <td>CBHI Payable({{ Auxillary.format_decimal(cbhi_ee_rate * 100) }}%)</td>
                                        <td>RWF {{ total_cbhi }}</td>
                                    </tr>
                                    <tr>
                                        <td>Maternity Payable({{ Auxillary.format_decimal((maternity_er_rate + maternity_ee_rate) * 100) }}%)</td>
                                        <td>RWF {{ total_maternity }}</td>
                                    </tr>
                                    <tr>
                                        <td>Pension Payable({{ Auxillary.format_decimal((pension_er_rate + occupational_hazard_rate + pension_ee_rate) * 100 )}}%)</td>
                                        <td>RWF {{ total_pension }}</td>
                                    </tr>
                                    <tr>
                                        <td>Rama Payable({{ Auxillary.format_decimal((employee_insurance + employer_insurance) * 100) }}%)</td>
                                        <td>RWF {{ total_insurance }}</td>
                                    </tr>
                                    <tr class="tfooter">
                                        <td>Total</td>
                                        <td>RWF {{ total_contributions_payable }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
{% endblock %}
