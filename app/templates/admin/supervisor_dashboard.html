<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.25">
    <!--Icon-->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/logo.png') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.js"></script>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/dashboards.css') }}">
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/tables.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/spinner.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/popup.css') }}">

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!--boostrap to hide the flash message-->
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/feedback.css') }}">
    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    <script src="{{ url_for('static', filename='scripts/popup.js') }}"></script>
    <!--google icons-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"/>
    <!--fas fa icon-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <title>Supervisor Dashboard</title>
    <!--icon-->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/net_fav.png') }}">
    <!-- Clarity tracking code -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "o4482shycr");
    </script>
    <!--Google analytics-->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QHJBRK9MP7"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-QHJBRK9MP7');
    </script>
</head>
<body>
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <ul class="list-unstyled">
        {% for category, message in messages %}
            {% if category == 'success' %}
            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </li>
            {% elif category == 'error' %}
            <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert" style="display: none;">{{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </li>
            {% endif %}
        {% endfor %}
        </ul>
        {% endif %}
    {% endwith %}
    <div class="max-container">
        <div class="dashboard-header">
            <div class="header--wrapper main-class">
                <div class="logo_icon">
                    <div class="netpipo_logo">
                        <img src="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}" alt="logo">
                    </div>
                    <div class="icons">
                        <a href="javascript:void(0);" class="icon_a" onclick="displayHide()">
                            <i class="bx bx-menu"></i>
                        </a>
                    </div>
                </div>
                <div class="header-title">
                    <div class="company--logo">
                        {% if company_data.logo %}
                            {% if 'digitaloceanspaces.com' in company_data.logo %}
                                <!-- Use the full URL directly from DigitalOcean Spaces -->
                                <img src="{{ company_data.logo }}" alt="Company Logo">
                            {% else %}
                                <!-- Fallback for legacy logos stored on disk -->
                                <img src="{{ url_for('static', filename='uploads/logos/' + company_data.logo) }}" alt="Company Logo">
                            {% endif %}
                        {% else %}
                            <p></p>
                        {% endif %}
                    </div>
                        <h3 class="main-title">{{ company.upper() }} </h3>
                        {% if role =='HR' %}
                        <a class="template-link btn-image" href="#" data-template-url="{{ url_for('company_data.switch_company') }}"><i class="fi fi-rr-toggle-on"></i>Switch</a>
                    {% endif %}
                </div>
                <div class="user--info">
                    <div class="other-icons">
                        <!--feedback popup-->
                    </div>
                    <div class="user-details " id="user-details">
                        <i id="user" class="bx bxs-user-circle"></i>
                    </div>
                    <div class="action--links right-sidebar" id="action--links">
                        <div class="user--data">
                            <div class="user--name">
                                <i class="fi fi-rr-circle-user"></i>
                                <p>{{ user }}</p>
                            </div>
                        </div>
                        <div class="user--role">
                            <div class="role">
                                <p>{{ role }}</p>
                            </div>
                        </div>
                        <ul>
                            <li>
                                <a href="{{ url_for('company_users.logout_company_users') }}">
                                    <span class="material-symbols-outlined">logout</span>
                                    <p>Logout</p>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="sidebar" id="item">
            <ul class="menu">
                <li>
                    <a href="{{ url_for('admin_data.supervisor_dashboard') }}">
                        <i class="fi fi-rr-house-chimney"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" data-template-url="{{ url_for('attendance.clockin') }}">
                        <i class="fi fi-rr-enter"></i>
                        <span>Clock In</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" data-template-url="{{ url_for('attendance.clockout') }}">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Clock Out</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" href="#" data-template-url="{{ url_for('employees.list_employees') }}">
                        <i class="fi fi-rr-employees"></i>
                        <span>Employees</span>
                    </a>
                </li>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-briefcase-arrow-right"></i>
                            <span>Attendance</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#" data-template-url="{{ url_for('attendance.attendance_records') }}">
                            <i class="fi fi-rr-memo-pad"></i>
                            <span>Attendance Records</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('attendance.list_daily_attendance') }}">
                            <i class="fi fi-rr-memo-pad"></i>
                            <span>Daily Attendance</span>
                        </a>
                        <a href="#" class="template-link" data-template-url="{{ url_for('attendance.list_employee_subjects') }}">
                            <i class="fi fi-rr-images"></i>
                            <span>Pending Images</span>
                        </a>
                    </div>
                </div>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-house-leave"></i>
                            <span>Leave</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#" data-template-url="{{ url_for('leave_applications.get_leave_application_for_employees') }}">
                            <i class="fi fi-rr-email-pending"></i>
                            <span>Leave Requests</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('leave_applications.view_leave_approvals') }}">
                            <i class="fi fi-rr-memo-circle-check"></i>
                            <span>Leave Approvals</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('attendance.get_leave_records') }}">
                            <i class="fi fi-rr-poll-h"></i>
                            <span>Leave Records</span>
                        </a>
                    </div>
                </div>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-money"></i>
                            <span>Advance</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#" data-template-url="{{ url_for('advance_requests.view_all_advance_requests') }}">
                            <i class="fi fi-rr-money"></i>
                            <span>Advance Requests</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('advance_requests.view_advance_approvals') }}">
                            <i class="fi fi-rr-money-check"></i>
                            <span>Advance Approvals</span>
                        </a>

                    </div>

                </div>

            </ul>
        </div>
        <div class="main--content">
            <div class="grid-cards-container all-card--wrappers main-class">
                <div class="first-card">
                    <a class="template-link" href="#" data-template-url="{{ url_for('employees.list_employees')}}">
                        <div class="superv">
                            <h3 class="card--title">
                                Employees
                            </h3>
                            <i class="bx bxs-group"></i>
                        </div>
                        <div class="grid-card">
                            <p class="grid-digit">{{ no_employee }}</p>
                        </div>
                    </a>
                </div>
                <div class="second-card">
                    <a href="#">
                        <div class="superv">
                            <h3 class="card--title">
                                Present
                            </h3>
                            <i class="fi fi-rr-check"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{num_attendance}}</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="third-card">
                    <div class="">
                        <div class="clock-container">
                            <h3>
                                {{ current_date }}
                            </h3>
                            <i class="fi fi-rr-time-twenty-four"></i>
                            <div id="clock">--:--:--</div>
                        </div>
                    </div>
                </div>
                <div class="fourth-card">
                    <a href="#">
                        <div class="superv">
                            <h3 class="card--title">
                                Absentees
                            </h3>
                            <i class="bx bx-history"></i>
                        </div>
                        <div class="card--body">
                            <p class="grid-digit">{{ absentees}}</p>
                        </div>
                    </a>
                </div>
                <div class="fifth-card">
                    <a href="#">
                        <div class="superv">
                            <h3 class="card--title">
                                Leave
                            </h3>
                            <i class="fi fi-rr-calendar-clock"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{num_leave}}</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="sixth-card">
                    <a href="#">
                        <div class="superv">
                            <h3 class="card--title">
                                Off
                            </h3>
                            <i class="bx bx-time"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{num_off}}</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="dynamic-content">
                <!--the spinner-->
                <div id="loader" class="spinner"></div>
                <!--flash messages container-->
            </div>
        </div>
        <div class="footer">
            <div class="footer--content">
                <p>&copy; 2025 netpipo. Powered by ACR, All rights reserved.</p>
            </div>
        </div>
    </div>
    <div id="subPopup" class="popup" style="display: none;">
        <div class="popup-content">
            <h1>Upgrade your Subscription Plan</h1>
            <p>You need to Upgrade your subscription plan to access this feature.</p>
            <div class="decisive-btn">
                <a href="{{ url_for('pages.pricing') }}" class="btn-continue" target="_blank">View Plans</a>
                <a href="{{ url_for('admin_data.supervisor_dashboard') }}" class="btn-cancel" id="closePopup">Close</a>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/load_contents_dyn.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/display_hide.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='scripts/digital_clock.js') }}"></script>
</body>
</html>