<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.25">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.js"></script>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/dashboards.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/tables.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/spinner.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/feedback.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/graphs.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/popup.css')}}">
    <script src="{{ url_for('static', filename='scripts/buttons.js') }}"></script>
    <link rel="stylesheet"
        href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
    <!--google icons-->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <!--fas fa icon-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <title>HR-Dashboard</title>
    <!--icon-->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/net_fav.png') }}">
    <!-- Clarity tracking code -->
    <script type="text/javascript">
        (function (c, l, a, r, i, t, y) {
            c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
            t = l.createElement(r); t.async = 1; t.src = "https://www.clarity.ms/tag/" + i;
            y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", "o4482shycr");
    </script>
    <!--Google analytics-->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QHJBRK9MP7"></script>
    <script src="{{ url_for('static', filename='scripts/google-analytics.js') }}"></script>
</head>

<body>
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <ul class="list-unstyled">
        {% for category, message in messages %}
        {% if category == 'success' %}
        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </li>
        {% elif category == 'danger' %}
        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </li>
        {% endif %}
        {% endfor %}
    </ul>
    {% endif %}
    {% endwith %}
    <div class="max-container">
        <div class="dashboard-header">
            <div class="header--wrapper main-class">
                <div class="logo_icon">
                    <div class="netpipo_logo">
                        <img src="{{ url_for('static', filename='images/system_images/net_logo_sas.png') }}" alt="logo">
                    </div>
                    <div class="icons">
                        <a href="javascript:void(0);" class="icon_a" onclick="displayHide()">
                            <i class="bx bx-menu"></i>
                        </a>
                    </div>
                </div>
                <div class="header-title">
                    <div class="company--logo">
                        {% if company_data.logo %}
                        {% if 'digitaloceanspaces.com' in company_data.logo %}
                        <!-- Use the full URL directly from DigitalOcean Spaces -->
                        <img src="{{ company_data.logo }}" alt="Company Logo">
                        {% else %}
                        <!-- Fallback for legacy logos stored on disk -->
                        <img src="{{ url_for('static', filename='uploads/logos/' + company_data.logo) }}"
                            alt="Company Logo">
                        {% endif %}
                        {% else %}
                        <p></p>
                        {% endif %}
                    </div>
                    <h3 class="main-title">{{ company }}</h3>
                    {% if role =='HR' %}
                    <a class="template-link btn-image" href="#"
                        data-template-url="{{ url_for('company_data.switch_company') }}"><i
                            class="fi fi-rr-toggle-on"></i>Switch</a>
                    {% endif %}
                </div>
                <div class="user--info">
                    <div class="other-icons">
                        <div class="feedback--container btn-cancel">
                            <a class="template-link" href="#"
                                data-template-url="{{ url_for('feedback.add_feedback') }}">
                                <p>Feedback</p>
                                <i class="bx bx-message"></i>
                            </a>
                        </div>
                    </div>
                    <div class="user-details " id="user-details">
                        <i id="user" class="bx bxs-user-circle"></i>
                    </div>
                    <div class="action--links right-sidebar" id="action--links">
                        <!--settings-->
                        <div class="user--data">
                            <div class="user--name">
                                <i class="fi fi-rr-circle-user"></i>
                                <p>{{ first_name }} {{ last_name }}</p>
                            </div>
                        </div>
                        <div class="user--role">
                            <div class="role">
                                <p>{{ role }}</p>
                            </div>
                        </div>
                        <ul>
                            <li>
                                <a class="template-link" href="#"
                                    data-template-url="{{ url_for('user_data.update_profile') }}">
                                    <span class="material-symbols-outlined">
                                        manage_accounts
                                    </span>
                                    <span>Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('user_data.logout') }}">
                                    <span class="material-symbols-outlined">logout</span>
                                    <p>Logout</p>
                                </a>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="sidebar" id="item">
            <ul class="menu">
                <li class="active">
                    <a href="{{ url_for('admin_data.dashboard') }}">
                        <i class="fi fi-rr-house-chimney"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-employees"></i>
                            <span>Employees</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" a href="#"
                            data-template-url="{{ url_for('employees.register_employees') }}">
                            <i class="fi fi-rr-user-add"></i>
                            <span>New Employee</span>
                        </a>
                        <a class=" push--up template-link" href="#" data-template-url= "{{ url_for('employees.employees_list') }}"
                            data-template-url="{{ url_for('employees.employees_list') }}">
                            <i class="fi fi-rr-employees"></i>
                            <span>All Employees</span>
                        </a>
                        <a class=" push--up template-link" href="{{ url_for('employees.get_inactive_employees') }}"
                            data-template-url="{{ url_for('employees.get_inactive_employees') }}">
                            <i class="fi fi-rr-employees"></i>
                            <span>Inactive Employees</span>
                        </a>
                    </div>
                </div>
                <div class="feature-content" id="sub-menu">
                    <!--payroll-data-->
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-calculator-money"></i>
                            <span>Payroll</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">

                        <a class="push--down template-link" href="#"
                            data-template-url="{{ url_for('payroll_summary.payroll_summary') }}">
                            <i class="fi fi-rr-calculator-simple"></i>
                            <span>Payroll</span>
                        </a>

                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('payroll_approval.bulk_payroll_approval') }}">
                            <i class="fi fi-rr-hourglass-end"></i>
                            <span>Pending Payroll</span>
                        </a>

                        <a class="push--up template-link" href="#"
                            data-template-url="{{ url_for('payroll_summary.payroll_summary_history') }}">
                            <i class="bx bx-history"></i>
                            <span>Payroll History</span>
                        </a>

                    </div>
                </div>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="bx bx-money"></i>
                            <span>Adjustments</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#" data-template-url="{{ url_for('deductions.deductions') }}">
                            <i class="fi fi-rr-comment-minus"></i>
                            <span>Deductions</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('reimbursements.reimbursements') }}">
                            <i class="fi fi-rr-add"></i>
                            <span>Reimbursements</span>
                        </a>
                    </div>
                </div>
                <li>
                    <a class="template-link" href="#" data-template-url="{{ url_for('insurance.insurance') }}">
                        <i class="fi fi-rr-doctor"></i>
                        <span>Medical Insurance</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" href="#" data-template-url="{{ url_for('departments.departments') }}">
                        <i class="fi fi-rr-department-structure"></i> <span>Departments</span>
                    </a>
                </li>
                {% if attendance_service %}
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Attendance</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#" data-template-url="{{ url_for('attendance.clockin') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Clock-In</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('attendance.clockout') }}">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Clock-Out</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('attendance.clockin_employee_list') }}">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Manual Clock-In</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('attendance.attendance_records') }}">
                            <i class="fi fi-rr-alarm-clock"></i>
                            <span>Attendance Records</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('attendance.list_daily_attendance') }}">
                            <i class="fi fi-rr-memo-pad"></i>
                            <span>Daily Attendance</span>
                        </a>
                        <a href="{{ url_for('attendance.timesheet') }}" target="_blank">
                            <i class="fi fi-rr-list-check"></i>
                            <span>Timesheet</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('shifts.view_shifts')}}">
                            <i class="fi fi-rr-time"></i>
                            <span>Shifts</span>
                        </a>
                    </div>
                </div>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-house-leave"></i>
                            <span>Leave</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('leave_applications.get_leave_application_for_employees') }}">
                            <i class="fi fi-rr-email-pending"></i>
                            <span>Leave Requests</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('leave_applications.view_leave_approvals') }}">
                            <i class="fi fi-rr-memo-circle-check"></i>
                            <span>Leave Approvals</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('attendance.get_leave_records') }}">
                            <i class="fi fi-rr-poll-h"></i>
                            <span>Leave Records</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('employees.employee_leave_balance_list') }}">
                            <i class="fi fi-rr-poll-h"></i>
                            <span>Leave balances</span>
                        </a>
                    </div>
                </div>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-money"></i>
                            <span>Advances</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('advance_requests.view_all_advance_requests') }}">
                            <i class="fi fi-rr-hand-holding-box"></i>
                            <span>Requests</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('advance_requests.view_advance_approvals') }}">
                            <i class="fi fi-rr-warranty"></i>
                            <span>Approved</span>
                        </a>
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('advance_requests.add_advance') }}">
                            <i class="fi fi-rr-warranty"></i>
                            <span>Add Salary Advance</span>
                        </a>
                    </div>
                </div>
                {% endif %}
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-document-gear"></i>
                            <span>Documents</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sub-links">
                        <a class="template-link" href="#"
                            data-template-url="{{ url_for('document.upload_documents') }}">
                            <i class="fi fi-rr-upload"></i>
                            <span>Upload Document</span>
                        </a>
                        <a class="template-link" href="#" data-template-url="{{ url_for('document.view_documents') }}">
                            <i class="fi fi-rr-audit"></i>
                            <span>View Documents</span>
                        </a>
                    </div>
                </div>
                <li>
                    <a class="template-link" href="{{ url_for('user_data.settings') }}"
                        data-template-url="{{ url_for('user_data.settings') }}">
                        <i class="fi fi-rr-settings"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="main--content">
            <div class="main-class">
                <div class="card--container">
                    <div class="all-card--wrappers">
                        <div
                            class="{{ 'card--wrapper-1' if attendance_service else 'card--wrapper-1-limit'}} top-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Employees</h3>
                                <i class="bx bxs-group"></i>
                            </div>
                            <div class="card--body">
                                <h1 class="card--digits-big">{{ no_employee }}</h1>
                                <div class="ee_links">
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('employees.register_employees') }}">
                                        <i class="fi fi-rr-user-add"></i>
                                        Add
                                    </a>
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('employees.employees_list') }}">
                                        <i class="fi fi-rr-eye"></i>
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% if attendance_service %}
                        <div class="card--wrapper-7 top-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Leave</h3>
                                <i class="fi fi-rr-house-leave"></i>
                            </div>
                            <div class="card--body">
                                <h1 class="card--digits-big">{{ num_leave_requests }}</h1>
                                <div class="ee_links">
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('leave_applications.get_leave_application_for_employees') }}">
                                        <i class="fi fi-rr-email-pending"></i>
                                        Requests
                                    </a>
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('leave_applications.view_leave_approvals') }}">
                                        <i class="fi fi-rr-memo-circle-check"></i>
                                        Approvals
                                    </a>
                                </div>
                            </div>

                        </div>
                        <div class="card--wrapper-8 top-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Advances</h3>
                                <i class="fi fi-rr-hand-holding-box"></i>
                            </div>
                            <div class="card--body">
                                <h1 class="card--digits-big">{{ num_pending_advance_requests }}</h1>
                                <div class="ee_links">
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('advance_requests.view_all_advance_requests') }}">
                                        <i class="fi fi-rr-hand-holding-box"></i>
                                        Requests
                                    </a>
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('advance_requests.view_advance_approvals') }}">
                                        <i class="fi fi-rr-warranty"></i>
                                        Approvals
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card--wrapper-9 top-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Daily Attendance</h3>
                                <i class="fi fi-rr-calendar"></i>
                            </div>
                            <div class="card--body">
                                <div class="attendance-status">
                                    <ul>
                                        <li class="present">
                                            <a href="#" class="template-link"
                                                data-template-url="{{ url_for('attendance.list_daily_attendance') }}">
                                                <i class="fi fi-rr-user-trust"></i>
                                                <div class="text">
                                                    <p>Present</p>
                                                    <p>{{ num_present }}</p>
                                                </div>
                                            </a>
                                        </li>
                                        <li class="absent">
                                            <i class="fi fi-rr-pen-clip-slash"></i>
                                            <div class="text">
                                                <p>Absent</p>
                                                <p>{{ num_absent }}</p>
                                            </div>
                                        </li>
                                        <li class="leave">
                                            <i class="fi fi-rr-house-leave"></i>
                                            <div class="text">
                                                <p>Leave</p>
                                                <p>{{ num_leave }}</p>
                                            </div>
                                        </li>
                                        <li class="off">
                                            <i class="fi fi-rr-do-not-disturb"></i>
                                            <div class="text">
                                                <p>Off</p>
                                                <p>{{num_off}}</p>
                                            </div>
                                        </li>
                                    </ul>
                                </div>

                            </div>
                        </div>
                        <div class="card--wrapper-10 top-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Documents</h3>
                                <i class="fi fi-rr-document"></i>
                            </div>
                            <div class="card--body">
                                <h1 class="card--digits-big">{{ documents_count|default(0) }}</h1>
                                <div class="ee_links">
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('document.upload_documents') }}">
                                        <i class="fi fi-rr-upload"></i>
                                        Upload
                                    </a>
                                    <a class="template-link" href="#"
                                        data-template-url="{{ url_for('document.view_documents') }}">
                                        <i class="fi fi-rr-eye"></i>
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        <div
                            class="{{ 'card--wrapper-3' if attendance_service else 'card--wrapper-3-limit'}} top-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Journal Entries</h3>

                                <i class="fi fi-rr-e-learning"></i>

                            </div>
                            <div class="card--body many">
                                <ul>
                                    <li class="card--heading">

                                        <p>Details</p>
                                        <p class="right">Debit(Dr)</p>
                                        <p class="right">Credit(Cr)</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Gross Salaries</h4>
                                        <p class="card--details gross">RWF {{total_gross}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Pension Expense({{(pension_er_rate) * 100}}%)</h4>
                                        <p class="card--details total_pension">RWF {{total_pension_er}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Maternity Expense({{maternity_er_rate * 100}} %)</h4>
                                        <p class="card--details maternity_er">RWF {{total_maternity_er}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Medical Contribution-ER({{employee_insurance * 100}}
                                            %)</h4>
                                        <p class="card--details insurance_total">RWF {{total_rama_ee}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Salary Advances</h4>
                                        <p class="card--details cbhi_er right"> RWF</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Net Salaries</h4>
                                        <p class="card--details net_value right">RWF {{total_net_salary}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Pension Payable({{(pension_er_rate+pension_ee_rate) *
                                            100}} %)</h4>
                                        <p class="card--details pension_er right">RWF {{total_pension}}</p>
                                    </li>

                                    <li>
                                        <h4 class="card--details">Maternity Payable({{(maternity_er_rate +
                                            maternity_ee_rate) * 100}}%)</h4>
                                        <p class="card--details maternity_total right">RWF {{total_maternity}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Medical Contribution Payable({{(employee_insurance +
                                            employer_insurance) * 100}}%)</h4>
                                        <p class="card--details insurance_total right">RWF {{total_insurance}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">CBHI Payable({{(cbhi_ee_rate ) * 100}}%)</h4>
                                        <p class="card--details total right">RWF {{total_cbhi}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Paye Payable</h4>
                                        <p class="card--details paye right">RWF {{total_payee}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Other Deductions</h4>
                                        <p class="card--details total right"> RWF</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">TOTAL</h4>
                                        <p class="card--details total right"> RWF {{ total_debits }}</p>
                                        <p class="card--details total right">RWF {{ total_credits }}</p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div
                            class="{{'card--wrapper-4' if attendance_service else 'card--wrapper-4-limit'}} global-card-wrapper">
                            <div class="card--header">
                                <h3 class="card--title">Payroll Tax Summary

                                </h3>
                                <a class="template-link click" href="#"
                                    data-template-url="{{ url_for('payroll_summary.payroll_summary') }}">Re-calculate</a>
                                <i class="fi fi-rr-calculator-simple"></i>
                            </div>
                            <div class="card--body">
                                <ul>
                                    <li class="card--heading">
                                        <p>Details</p>
                                        <p class="right">Total</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">PAYE</h4>
                                        <p class="card--details gross">RWF {{ total_payee }}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">CBHI Payable({{ cbhi_ee_rate * 100 }}%)</h4>
                                        <p class="card--details total_pension">RWF {{total_cbhi}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Maternity Payable({{maternity_er_rate * 100}} %)</h4>
                                        <p class="card--details maternity_er">RWF {{total_maternity_er}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Pension Payable({{(pension_er_rate+pension_ee_rate) *
                                            100}} %)</h4>
                                        <p class="card--details pension_er right">RWF {{total_pension}}</p>
                                    </li>
                                    <li>
                                        <h4 class="card--details">Rama Payable({{(employee_insurance +
                                            employer_insurance) * 100}} %)</h4>
                                        <p class="card--details insurance_total right">RWF {{total_insurance}}</p>
                                        <!--Total-->
                                    <li>
                                        <h4 class="card--details">Total</h4>
                                        <p class="card--details net_value right"> RWF</p>
                                </ul>
                            </div>
                        </div>
                        <div
                            class="{{'card--wrapper-6' if attendance_service else 'card--wrapper-6-limit'}} global-card-wrapper">
                            <canvas id="myChart" style="width: fit-content;"></canvas>
                        </div>
                    </div>

                </div>
            </div>
            <div class="dynamic-content">
                <!--the spinner-->
                <div id="loader" class="spinner"></div>
                <!--flash messages container-->
            </div>
        </div>
        <div class="footer">
            <div class="footer--content">
                <p>&copy; 2025 netpipo. Powered by ACR, All rights reserved.</p>
            </div>
        </div>
    </div>
    <div id="subPopup" class="popup" style="display: none;">
        <div class="popup-content">
            <h1>Upgrade your Subscription Plan</h1>
            <p>You need to Upgrade your subscription plan to access this feature.</p>
            <div class="decisive-btn">
                <a href="{{ url_for('pages.pricing') }}" class="btn-continue" target="_blank">View Plans</a>
                <a href="{{ url_for('admin_data.dashboard') }}" class="btn-cancel" id="closePopup">Close</a>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/load_contents_dyn.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/display_hide.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = 'https://embed.tawk.to/66d86a7e50c10f7a00a3f52d/1i6ulvvt4';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='scripts/graphs.js') }}"></script>
</body>

</html>