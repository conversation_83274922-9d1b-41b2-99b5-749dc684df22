<!DOCTYPE html>
<html>

<head>
    <title>Employee Dashboard</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.25">
    <!--Icon-->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/net_fav.png') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.js"></script>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/dashboards.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/messages_v2.css') }}">
    <link rel="stylesheet"
        href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/auth_forms.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/tables.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/spinner.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/popup.css') }}">
    <!--Jquery libraries-->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
    <!--boostrap to hide the flash message-->
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/feedback.css') }}">
    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    <script src="{{ url_for('static', filename='scripts/popup.js') }}"></script>
    <!--google icons-->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <!--fas fa icon-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <title>Supervisor Dashboard</title>
    <!--icon-->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/net_fav.png') }}">
    <!-- Clarity tracking code -->
    <script type="text/javascript">
        (function (c, l, a, r, i, t, y) {
            c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
            t = l.createElement(r); t.async = 1; t.src = "https://www.clarity.ms/tag/" + i;
            y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", "o4482shycr");
    </script>
    <!--Google analytics-->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QHJBRK9MP7"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-QHJBRK9MP7');
    </script>
</head>

<body>
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <ul class="list-unstyled">
        {% for category, message in messages %}
        {% if category == 'success' %}
        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </li>
        {% elif category == 'danger' %}
        <li class="alert alert-{{ category }} alert-dismissible fade show" role="alert">{{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </li>
        {% endif %}
        {% endfor %}
    </ul>
    {% endif %}
    {% endwith %}
    <div class="max-container">
        <div class="dashboard-header">
            <div class="header--wrapper main-class">
                <div class="header-title">
                    <div class="icons">
                        <a href="javascript:void(0);" class="icon_a" onclick="displayHide()">
                            <i class="bx bx-menu"></i>
                        </a>
                    </div>
                </div>
                <div class="user--info">
                    <div class="other-icons">
                        <!--feedback popup-->
                    </div>
                    <div class="user-details " id="user-details">
                        <i id="user" class="bx bxs-user-circle"></i>
                    </div>
                    <div class="action--links right-sidebar" id="action--links">
                        <div class="user--data">
                            <div class="user--name">
                                <i class="fi fi-rr-circle-user"></i>
                                <p>{{ employee_name.upper() }}</p>
                            </div>
                        </div>
                        <div class="user--role">
                            <div class="role">
                                <p>{{ role }}</p>
                            </div>
                        </div>
                        <!--settings-->
                        <ul>
                            <li>
                                <a href="{{ url_for('company_users.logout_company_users') }}">
                                    <span class="material-symbols-outlined">logout</span>
                                    <p>Logout</p>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="sidebar" id="item">
            <ul class="menu">
                <li class="active">
                    <a href="{{ url_for('admin_data.employee_dashboard') }}">
                        <i class="fi fi-rr-house-chimney"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <div class="feature-content" id="sub-menu">
                    <li>
                        <a href="#">
                            <i class="fi fi-rr-calendar"></i>
                            <span>Attendance</span>
                            <i class="bx bx-chevron-down drop-it"></i>
                        </a>
                    </li>
                    <div class="feature-items" id="sublinks">
                        <a class="template-link" href="" data-template-url="{{ url_for('attendance.clockin') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Clock in</span>
                        </a>
                        <a href="#" class="template-link" data-template-url="{{ url_for('attendance.clockout') }}">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Clock out</span>
                        </a>
                        <a href="#" class="template-link" data-template-url="{{ url_for('field.field_clockin') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Field in</span>
                        </a>
                        <a href="#" class="template-link" data-template-url="{{ url_for('field.field_clockout') }}">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Field out</span>
                        </a>
                    </div>
                </div>
                <li>
                    <a class="template-link" href="#"
                        data-template-url="{{ url_for('leave_applications.apply_for_leave') }}">
                        <i class="fi fi-rr-house-leave"></i>
                        <span>Leave</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" href="#"
                        data-template-url="{{ url_for('leave_applications.view_leave_applications') }}">
                        <i class="fi fi-rr-calendar"></i>
                        <span>Leave Records</span>
                    </a>
                </li>
                <li>
                    <a class="template-link"
                        data-template-url="{{ url_for('advance_requests.view_advance_requests') }}">
                        <i class="fi fi-rr-money"></i>
                        <span>Advances</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" data-template-url="{{ url_for('document.view_documents') }}">
                        <i class="fi fi-rr-file-search"></i>
                        <span>My Documents</span>
                    </a>
                </li>
                <li>
                    <a class="template-link" data-template-url="{{ url_for('document.upload_documents') }}">
                        <i class="fi fi-rr-upload"></i>
                        <span>Upload Document</span>
                    </a>
                </li>
            </ul>
            <div class="dashboard-footer">
                <a href="{{ url_for('user_data.logout') }}">
                    <i class="bx bx-log-out"></i>
                    <span>Logout</span>
                </a>
                <a href="{{ url_for('pages.docs') }}" target="_blank">
                    <i class="bx bx-book"></i>
                    <span>User Guide</span>
                </a>
            </div>
        </div>
        <div class="main--content">
            <div class="grid-cards-container all-card--wrappers main-class">
                <div class="first-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                {% if net_salary %}
                                Net Pay
                                {% elif gross_salary %}
                                Gross salary
                                {% elif total_staff_cost %}
                                Total Staff Cost
                                {% endif %}
                            </h3>
                            <i class="fi fi-rr-wallet-arrow"></i>
                        </div>
                        <div class="grid-card">
                            <p class="grid-digit">Rwf {% if net_salary %}
                                {{ Auxillary.format_amount(net_salary) }}
                                {% elif gross_salary %}
                                {{ Auxillary.format_amount(gross_salary) }}
                                {% elif total_staff_cost %}
                                {{ Auxillary.format_amount(total_staff_cost) }}
                                {% endif %}
                            </p>
                        </div>
                    </a>
                </div>
                <div class="second-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Monthly Attendance
                            </h3>
                            <i class="fi fi-rr-clock"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ attendance_count }}
                                    {% if attendance_count == 1 %} Day
                                    {% else %} Days
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="fourth-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Advances
                            </h3>
                            <i class="fi fi-rr-money"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">
                                    {% if salary_advance_balance %}
                                    {{ Auxillary.format_amount(salary_advance_balance) }}
                                    {% else %}
                                    0 RWF
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="fifth-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Leave requests
                            </h3>
                            <i class="fi fi-rr-calendar"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ leave_pending }}</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="sixth-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Annual Leave Balance
                            </h3>
                            <i class="fi fi-rr-calendar"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ annual_leave_balance }}</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="seventh-card">
                    <a class="template-link" href="#" data-template-url="{{ url_for('document.view_documents') }}">
                        <div class="">
                            <h3 class="card--title">
                                My Documents
                            </h3>
                            <i class="fi fi-rr-document"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ employee_documents_count|default(0) }}</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="dynamic-content">
                <!--the spinner-->
                <div id="loader" class="spinner"></div>
                <!--flash messages container-->
            </div>
        </div>
        <div class="footer">
            <div class="footer--content">
                <p>&copy; 2025 netpipo. Powered by ACR, All rights reserved.</p>
            </div>
        </div>
    </div>
    <div id="subPopup" class="popup" style="display: none;">
        <div class="popup-content">
            <h1>Upgrade your Subscription Plan</h1>
            <p>You need to Upgrade your subscription plan to access this feature.</p>
            <div class="decisive-btn">
                <a href="{{ url_for('pages.pricing') }}" class="btn-continue" target="_blank">View Plans</a>
                <a href="{{ url_for('admin_data.employee_dashboard') }}" class="btn-cancel" id="closePopup">Close</a>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='scripts/load_contents_dyn.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/display_hide.js') }}"></script>
    <script src="{{ url_for('static', filename='scripts/flash_messages.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='scripts/digital_clock.js') }}"></script>
</body>

</html>