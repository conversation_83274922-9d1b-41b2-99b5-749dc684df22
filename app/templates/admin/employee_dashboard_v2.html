<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
    <div class="ls_container">
        <div class="main--content">
            <div class="grid-cards-container all-card--wrappers main-class">
                <div class="first-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                {% if net_salary %}
                                Net Pay
                                {% elif gross_salary %}
                                Gross salary
                                {% elif total_staff_cost %}
                                Total Staff Cost
                                {% endif %}
                            </h3>
                            <i class="fi fi-rr-wallet-arrow"></i>
                        </div>
                        <div class="grid-card">
                            <p class="grid-digit">Rwf {% if net_salary %}
                                {{ Auxillary.format_amount(net_salary) }}
                                {% elif gross_salary %}
                                {{ Auxillary.format_amount(gross_salary) }}
                                {% elif total_staff_cost %}
                                {{ Auxillary.format_amount(total_staff_cost) }}
                                {% endif %}
                            </p>
                        </div>
                    </a>
                </div>
                <div class="second-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Monthly Attendance
                            </h3>
                            <i class="fi fi-rr-clock"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ attendance_count }}
                                    {% if attendance_count == 1 %} Day
                                    {% else %} Days
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="fourth-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Advances
                            </h3>
                            <i class="fi fi-rr-money"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">
                                    {% if salary_advance_balance %}
                                    {{ Auxillary.format_amount(salary_advance_balance) }}
                                    {% else %}
                                    0 RWF
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="fifth-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Leave requests
                            </h3>
                            <i class="fi fi-rr-calendar"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ leave_pending }}</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="sixth-card">
                    <a href="#">
                        <div class="">
                            <h3 class="card--title">
                                Annual Leave Balance
                            </h3>
                            <i class="fi fi-rr-calendar"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ annual_leave_balance }}</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="seventh-card">
                    <a class="template-link" href="#" data-template-url="{{ url_for('document.view_documents') }}">
                        <div class="">
                            <h3 class="card--title">
                                My Documents
                            </h3>
                            <i class="fi fi-rr-document"></i>
                        </div>
                        <div class="">
                            <div class="grid-card">
                                <p class="grid-digit">{{ employee_documents_count|default(0) }}</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="dynamic-content">
                <!--the spinner-->
                <div id="loader" class="spinner"></div>
                <!--flash messages container-->
            </div>
        </div>
    </div>
    
{% endblock %}