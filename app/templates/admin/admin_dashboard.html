<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | NETPIPO</title>
    <!-- External CSS -->
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/admin_dashboard.css') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/net_fav.png') }}">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo-container">
            <img src="{{ url_for('static', filename='images/system_images/logo.png') }}" alt="NETPIPO Logo">
            <h1>NETPIPO Admin</h1>
        </div>
        <div class="user-info">
            <p>Hello, {{ user }}</p>
            <a href="{{ url_for('user_data.logout') }}" class="logout-btn">
                <i class="fi fi-rr-sign-out"></i> Logout
            </a>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="menu-category">
                <h3 class="category-title"><i class="fi fi-rr-user"></i> User Management</h3>
                <ul class="menu-list">
                    <li class="menu-item"><a href="{{ url_for('user_data.add_user') }}" class="menu-link"><i class="fi fi-rr-user-add"></i> Register User</a></li>
                    <li class="menu-item"><a href="{{ url_for('user_data.view_users') }}" class="menu-link"><i class="fi fi-rr-users"></i> View Users</a></li>
                    <li class="menu-item"><a href="{{ url_for('user_role.insert_user_role') }}" class="menu-link"><i class="fi fi-rr-user-time"></i> Add User Role</a></li>
                </ul>
            </div>

            <div class="menu-category">
                <h3 class="category-title"><i class="fi fi-rr-building"></i> Company Management</h3>
                <ul class="menu-list">
                    <li class="menu-item"><a href="{{ url_for('company_data.view_profile') }}" class="menu-link"><i class="fi fi-rr-building"></i> View Companies</a></li>
                    <li class="menu-item"><a href="{{ url_for('fingerprint.register_device') }}" class="menu-link"><i class="fi fi-rr-fingerprint"></i> Register Device to Company</a></li>
                    <li class="menu-item"><a href="{{ url_for('admin_data.manage_subscriptions') }}" class="menu-link"><i class="fi fi-rr-calendar-clock"></i> Manage Subscriptions</a></li>
                    <li class="menu-item"><a href="{{ url_for('company_data_v2.delete_company') }}" class="menu-link"><i class="fi fi-rr-trash"></i> Delete Company</a></li>
                </ul>
            </div>

            <div class="menu-category">
                <h3 class="category-title"><i class="fi fi-rr-money-check"></i> Tax & Payroll</h3>
                <ul class="menu-list">
                    <li class="menu-item"><a href="{{ url_for('taxbracket.insert_tax_bracket') }}" class="menu-link"><i class="fi fi-rr-file-invoice"></i> Insert Permanent Employee Tax Bracket</a></li>
                    <li class="menu-item"><a href="{{ url_for('second_employee.insert_second_employee_tax_bracket') }}" class="menu-link"><i class="fi fi-rr-file-invoice-dollar"></i> Register Second Employee Tax Bracket</a></li>
                    <li class="menu-item"><a href="{{ url_for('casuals.insert_casuals_tax_bracket') }}" class="menu-link"><i class="fi fi-rr-receipt"></i> Add Casuals Tax Rates</a></li>
                    <li class="menu-item"><a href="{{ url_for('consultant.add_consultant_tax_bracket') }}" class="menu-link"><i class="fi fi-rr-document"></i> Add Consultant Tax Rates</a></li>
                    <li class="menu-item"><a href="{{ url_for('rssb.add_rssb_contributions') }}" class="menu-link"><i class="fi fi-rr-hand-holding-usd"></i> Add RSSB Contributions</a></li>
                    <li class="menu-item"><a href="{{ url_for('brd_deductions.add_brd_deductions') }}" class="menu-link"><i class="fi fi-rr-bank"></i> Add BRD Rate</a></li>
                </ul>
            </div>

            <div class="menu-category">
                <h3 class="category-title"><i class="fi fi-rr-settings"></i> System Settings</h3>
                <ul class="menu-list">
                    <li class="menu-item"><a href="{{ url_for('employee_types.add_employee_types') }}" class="menu-link"><i class="fi fi-rr-user-add"></i> Add Employee Type</a></li>
                    <li class="menu-item"><a href="{{ url_for('features.add_feature') }}" class="menu-link"><i class="fi fi-rr-apps-add"></i> Add Feature</a></li>
                    <li class="menu-item"><a href="{{ url_for('plans.add_plans') }}" class="menu-link"><i class="fi fi-rr-layers"></i> Add Plan</a></li>
                    <li class="menu-item"><a href="{{ url_for('plans.assign_features') }}" class="menu-link"><i class="fi fi-rr-link"></i> Assign Features</a></li>
                    <li class="menu-item"><a href="{{ url_for('route_plan_requirements.add_route_plan_requirement') }}" class="menu-link"><i class="fi fi-rr-lock"></i> Restrict plans to Routes</a></li>
                    <li class="menu-item"><a href="{{ url_for('leave_types.add_leave_type') }}" class="menu-link"><i class="fi fi-rr-calendar"></i> Add Leave Type</a></li>
                    <li class="menu-item"><a href="{{ url_for('approval_types.add_approval_type') }}" class="menu-link"><i class="fi fi-rr-checkbox"></i> Add Approval Type</a></li>
                </ul>
            </div>

            <div class="menu-category">
                <h3 class="category-title"><i class="fi fi-rr-tools"></i> Utilities</h3>
                <ul class="menu-list">
                    <li class="menu-item"><a href="{{ url_for('db_backup.get_dump') }}" class="menu-link"><i class="fi fi-rr-database"></i> Download Database Backups</a></li>
                    <li class="menu-item"><a href="{{ url_for('db_monitor.monitor_db_connections_html') }}" class="menu-link"><i class="fi fi-rr-stats"></i> Monitor Database Connections</a></li>
                    <li class="menu-item"><a href="{{ url_for('feedback.view_feedback') }}" class="menu-link"><i class="fi fi-rr-comment"></i> View Feedback</a></li>
                    <li class="menu-item"><a href="{{ url_for('blog_posts.create_blog') }}" class="menu-link"><i class="fi fi-rr-edit"></i> Create Blog Post</a></li>
                </ul>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="dashboard-container">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="messages">
                            <ul>
                                {% for category, message in messages %}
                                    <li class="{{ category }}">{{ message }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                {% endwith %}

                <h2 class="welcome-message">Welcome to the Admin Dashboard</h2>

                <div class="card-container">
                    <div class="card">
                        <h3 class="card-header">User Management</h3>
                        <ul class="menu-list">
                            <li class="menu-item"><a href="{{ url_for('user_data.add_user') }}" class="menu-link"><i class="fi fi-rr-user-add"></i> Register User</a></li>
                            <li class="menu-item"><a href="{{ url_for('user_data.view_users') }}" class="menu-link"><i class="fi fi-rr-users"></i> View Users</a></li>
                        </ul>
                    </div>

                    <div class="card">
                        <h3 class="card-header">Company Management</h3>
                        <ul class="menu-list">
                            <li class="menu-item"><a href="{{ url_for('company_data.view_profile') }}" class="menu-link"><i class="fi fi-rr-building"></i> View Companies</a></li>
                            <li class="menu-item"><a href="{{ url_for('fingerprint.register_device') }}" class="menu-link"><i class="fi fi-rr-fingerprint"></i> Register Device</a></li>
                            <li class="menu-item"><a href="{{ url_for('admin_data.manage_subscriptions') }}" class="menu-link"><i class="fi fi-rr-calendar-clock"></i> Manage Subscriptions</a></li>
                        </ul>
                    </div>

                    <div class="card">
                        <h3 class="card-header">System Settings</h3>
                        <ul class="menu-list">
                            <li class="menu-item"><a href="{{ url_for('features.add_feature') }}" class="menu-link"><i class="fi fi-rr-apps-add"></i> Add Feature</a></li>
                            <li class="menu-item"><a href="{{ url_for('plans.add_plans') }}" class="menu-link"><i class="fi fi-rr-layers"></i> Add Plan</a></li>
                        </ul>
                    </div>

                    <div class="card">
                        <h3 class="card-header">Utilities</h3>
                        <ul class="menu-list">
                            <li class="menu-item"><a href="{{ url_for('db_backup.get_dump') }}" class="menu-link"><i class="fi fi-rr-database"></i> Database Backups</a></li>
                            <li class="menu-item"><a href="{{ url_for('db_monitor.monitor_db_connections_html') }}" class="menu-link"><i class="fi fi-rr-stats"></i> Monitor DB</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
