<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Company Subscriptions</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/flaticon/uicons-regular-rounded/css/uicons-regular-rounded.css') }}">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/subscription_management.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles/tables.css') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/system_images/net_fav.png') }}">
</head>
<body>
    <div class="container">
        <h1 class="page-title">Manage Company Subscriptions</h1>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="card">
            <div class="card-header">
                <h3><i class="fi fi-rr-calendar-clock"></i> Manual Subscription Management</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin_data.manage_subscriptions') }}">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.company_id.label(class="form-control-label") }}
                                {{ form.company_id(class="form-control") }}
                                {% if form.company_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.company_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.plan_id.label(class="form-control-label") }}
                                {{ form.plan_id(class="form-control") }}
                                {% if form.plan_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.plan_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.num_days.label(class="form-control-label") }}
                                {{ form.num_days(class="form-control", placeholder="e.g. 30 for 1 month, 365 for 1 year") }}
                                {% if form.num_days.errors %}
                                    <div class="text-danger">
                                        {% for error in form.num_days.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.amount.label(class="form-control-label") }}
                                {{ form.amount(class="form-control", placeholder="Payment amount") }}
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        {{ form.payment_method.label(class="form-control-label") }}
                        {{ form.payment_method(class="form-control") }}
                        {% if form.payment_method.errors %}
                            <div class="text-danger">
                                {% for error in form.payment_method.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">A transaction ID will be automatically generated for this payment</small>
                    </div>

                    <div class="form-group">
                        {{ form.notes.label(class="form-control-label") }}
                        {{ form.notes(class="form-control", rows=3, placeholder="Any additional notes about this subscription payment") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group button-group">
                        <div class="button-container">
                            <div class="button-group-left">
                                <a href="{{ url_for('admin_data.admin_dashboard') }}" class="btn secondary-button">
                                    <i class="fi fi-rr-angle-left"></i> Back to Dashboard
                                </a>
                                {{ form.submit(class="btn primary-button") }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if recent_payments %}
        <div class="card">
            <div class="card-header">
                <h3><i class="fi fi-rr-money-check"></i> Recent Subscription Payments</h3>
            </div>
            <div class="card-body">
                <div class="payments-table-container">
                    <table id="payments-table" class="table table-striped display" style="width:100%">
                        <thead class="thead th-custom">
                            <tr>
                                <th>Company</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Transaction ID</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td>{{ payment.company_name }}</td>
                                <td>{{ payment.amount }}</td>
                                <td>{{ payment.payment_method }}</td>
                                <td>{{ payment.transaction_id }}</td>
                                <td>{{ payment.paid_at }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="card-header">
                <h3><i class="fi fi-rr-info"></i> No Recent Payments</h3>
            </div>
            <div class="card-body">
                <p>There are no recent subscription payments to display.</p>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='scripts/datatables.js') }}"></script>
</body>
</html>
