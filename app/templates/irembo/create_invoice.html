<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pay Subscription</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            text-align: center;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            background: white;
            padding: 20px;
            margin: auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        h2 {
            color: #535353;
        }

        p {
            font-size: 18px;
            margin-bottom: 15px;
        }

        strong {
            color: #28a745;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        label {
            font-weight: bold;
            text-align: left;
            display: block;
            margin-bottom: 5px;
        }

        input[type="number"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            text-align: center;
        }

        button {
            background: #25a38b;
            color: white;
            padding: 12px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: 0.3s;
        }

        button:hover {
            background: #25a38b;
        }
        .row-col {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            flex-direction: column;
        }
        .row{
            display: flex;
        }
        .rows{
            display: flex;
            flex-wrap: wrap
        }

        @media (max-width: 600px) {
            .container {
                width: 90%;
                padding: 15px;
            }

            input[type="number"] {
                font-size: 14px;
                padding: 8px;
            }

            button {
                width: 100%;
                padding: 15px;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>Pay Subscription</h2>
        <p>Monthly Subscription: <strong>RWF {{ price }}</strong></p>
        <form method="POST">
            <label for="quantity">Number of Months:</label>
            <input type="number" id="quantity" name="quantity" value="1" min="1" required>
            <!-- Option to change the plan with radio buttons -->
            <div class="row-col">
                <label for="change_plan">Change Plan?</label>
                    <div class="rows">
                    <div class="row">
                        <input type="radio" id="yes" name="different_plan" value="yes">
                        <label for="yes">Yes</label>
                    </div>
                    <div class="row">
                        <input type="radio" id="no" name="different_plan" value="no" checked>
                        <label for="no">No</label>
                    </div>
                </div>
            </div>
            <button type="submit">Proceed</button>
        </form>    
    </div>

</body>
</html>
