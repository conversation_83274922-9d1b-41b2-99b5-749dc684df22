<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <h1>Upgrade to Premium Plan</h1>
            <a class="btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                <i class="fi fi-rr-settings"></i> Settings
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="grey-container">
            <div>
            <h2>Netpipo Subscription (Via IremboPay)</h2> 
            <p>Read more about Netpipo plans and pricing: <a href="https://www.netpipo.com/pricing" target="_blank" class="blue">Netpipo Plans</a></p>
            </div>
            <ol>
                <li>Choose the number of months you want to subscribe for.</li>
                <li>Click on "Proceed" to continue to the payment page.</li>
                <li>Complete the payment process on IremboPay.</li>
                <li>Once payment is successful, you will receive a confirmation email.</li>
                <li>Your subscription will be activated immediately after payment confirmation.</li>
                <li>You can manage your subscription from your account settings.</li>
                <li>If you have any issues, please contact support.</li>
            </ol>
        </div>
        <div class="form--container">
            <div class="form-row">
                <p>Monthly Subscription: <strong>RWF {{ price }}</strong></p>
            </div>
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="quantity">Number of Months:</label>
                        <input type="number" id="quantity" name="quantity" value="1" min="1" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="change_plan">Change Plan?</label>
                            <div class="rows">
                            <div class="row">
                                <input type="radio" id="yes" name="different_plan" value="yes">
                                <label for="yes">Yes</label>
                            </div>
                            <div class="row">
                                <input type="radio" id="no" name="different_plan" value="no" checked>
                                <label for="no">No</label>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="submit" class="submit-btn">Proceed</button>
            </form>    
        </div>
    </div>
</div>
{% endblock %}

