<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pay Subscription</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            text-align: center;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            background: white;
            padding: 20px;
            margin: auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        h2 {
            color: #5a5a5a;
        }

        p {
            font-size: 18px;
            margin-bottom: 15px;
        }

        strong {
            color: #28a38b;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        label {
            font-weight: bold;
            text-align: left;
            display: block;
            margin-bottom: 5px;
        }
        .select-plan {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .select-plan option {
            padding: 10px;
            font-size: 16px;
        }
        .select-plan option:hover {
            background-color: #f1f1f1;
        }
        .select-plan option:focus {
            background-color: #e2e2e2;
        }
        input[type="number"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            text-align: center;
        }

        button {
            background: #28a38b;
            color: white;
            padding: 12px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: 0.3s;
        }

        button:hover {
            background: #177967;
        }

        @media (max-width: 600px) {
            .container {
                width: 90%;
                padding: 15px;
            }

            input[type="number"] {
                font-size: 14px;
                padding: 8px;
            }

            button {
                width: 100%;
                padding: 15px;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>Pay Subscription</h2>
    
        <form method="POST">
            <label for="plan_id">Select Plan:</label>
            <select class="select-plan" id="plan_id" name="plan_id" required>
                {% for plan in plans %}
                    <option value="{{ plan.plan_id }}" data-price="{{ plan.price }}">
                        {{ plan.plan_name }} - RWF {{ plan.price }}
                    </option>
                {% endfor %}
            </select>
    
            <label for="quantity">Number of Months:</label>
            <input type="number" id="quantity" name="quantity" value="1" min="1" required>
    
            <button type="submit">Proceed</button>
        </form>    
    </div>
    
    <script>
        document.getElementById("plan_id").addEventListener("change", function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.getAttribute("data-price");
            document.querySelector("h2").innerHTML = `Pay Subscription - RWF ${price}`;
        });
    </script>
    
</body>
</html>