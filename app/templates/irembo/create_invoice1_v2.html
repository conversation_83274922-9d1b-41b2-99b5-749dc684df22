
<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a class="template-link btn-edit" href="{{ url_for('irembo_v2.create_invoice') }}">
                <i class="fi fi-rr-arrow-left"></i> Back
            </a>
            <a class="btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                <i class="fi fi-rr-settings"></i> Settings
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="grey-container">
            <h2>Change subscription plan</h2>
            <ol>
                <li>Before proceeding, please ensure you have the necessary permissions to change the subscription plan.</li>
                <li>Select the desired subscription plan from the dropdown menu.</li>
                <li>Specify the number of months you wish to subscribe for.</li>
                <li>Click on "Proceed" to continue to the payment page.</li>
                <li>Complete the payment process on IremboPay.</li>
                <li>Once payment is successful, you will receive a confirmation email.</li>
            </ol>
        </div>
        <div class="form--container">
            <h1>Change subscription plan</h1>
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="plan_id">Select Plan:</label>
                        <select class="form-select" id="plan_id" name="plan_id" required>
                            {% for plan in plans %}
                                <option value="{{ plan.plan_id }}" data-price="{{ plan.price }}">
                                    {{ plan.plan_name }} - RWF {{ plan.price }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Number of Months:</label>
                        <input type="number" id="quantity" name="quantity" value="1" min="1" required class="form-control">
                    </div>
                </div>
                <button type="submit" class="submit-btn">Proceed</button>
            </form>    
        </div>
    </div>
</div>
    <script>
        document.getElementById("plan_id").addEventListener("change", function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.getAttribute("data-price");
            document.querySelector("h2").innerHTML = `Pay Subscription - RWF ${price}`;
        });
    </script>
{% endblock %}