<!DOCTYPE html>
{% extends "layouts/hr_dashboard.html" %}
{% block content %}
<script src="https://dashboard.irembopay.com/assets/payment/inline.js"></script>
<div class="ls_container">
    <div class="dyn_header">
        <div class="dynamic--buttons">
            <a class="template-link btn-edit" href="{{ url_for('irembo_v2.create_invoice') }}">
                <i class="fi fi-rr-vote-yea"></i>Subscription
            </a>
            <a class="template-link btn-edit" href="{{ url_for('user_data_v2.settings') }}">
                <i class="fi fi-rr-settings"></i>Settings
            </a>
        </div>
    </div>
    <div class="dyn_container">
        <div class="form--container">
            <h1>Pay Subscription</h1>
            <h2>Subscription Payment Details</h2>
            <p><strong>Reference Number:</strong> {{ reference_number }}</p>
            <p><strong>Customer Name:</strong> {{ customer_name }}</p>
            <p><strong>Customer Email:</strong> {{ customer_email }}</p>
            <p><strong>Customer Phone:</strong> {{ customer_phone }}</p>
            <p><strong>Subscription Duration:</strong> {{ quantity }} month(s)</p>
            <p><strong>Monthly Subscription Fee:</strong> RWF {{ Auxillary.format_amount(unit_price) }} </p>
            <p><strong>Total Amount Due:</strong> RWF {{ Auxillary.format_amount(total_price) }}</p>
            <button class="payment-button submit-btn" onclick="makePayment('{{ reference_number }}')">Proceed to Payment</button>
        </div>
    </div>
    <script>
        function makePayment(invoiceNumber) {
            IremboPay.initiate({
                publicKey: "pk_live_96d9ed080ad6416b820ec36e572c1019",
                invoiceNumber: invoiceNumber,
                locale: IremboPay.locale.EN,
                callback: (err, resp) => {
                    if (!err) {
                        alert("Payment successful!");
                        console.log("Success:", resp);
                        // Send payment confirmation to the backend
                        fetch('/v2/payment_confirmation', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(resp)
                        })
                        .then(response => response.json())
                        .then(data => console.log("Backend Response:", data))
                        .catch(error => console.error("Backend Error:", error));
                        // Redirect to dashboard
                        window.location.href = "/hr_dashboard";
                    } else {
                        alert("Payment failed: " + err.message);
                        console.error("Error:", err);
                    }
                }
            });
        }
    </script>

    </div>
</div>
{% endblock %}