<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payments</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        
        h1 {
            color: #333;
        }

        table {
            width: 90%;
            margin: 20px auto;
            border-collapse: collapse;
            background: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }

        th {
            background: #007BFF;
            color: white;
        }

        tr:hover {
            background: #f1f1f1;
        }

        td {
            color: #333;
        }

        @media (max-width: 768px) {
            table {
                width: 100%;
            }
        }
    </style>
</head>
<body>

    <h1>Payments Made by Customers</h1>

    <table>
        <thead>
            <tr>
                <th>Company Name</th>
                <th>Company Representative</th>
                <th>Amount</th>
                <th>Payment Method</th>
                <th>Payment Date</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in payments %}
                <tr>
                    <td>{{ payment.company_name }}</td>
                    <td>{{ payment.paid_by }}</td>
                    <td>RWF {{ payment.amount | round(2) }}</td>
                    <td>{{ payment.payment_method }}</td>
                    <td>{{ payment.paid_at }}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>

</body>
</html>
