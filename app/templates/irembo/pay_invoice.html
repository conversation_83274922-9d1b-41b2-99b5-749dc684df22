<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Payment Details</title>
    <script src="https://dashboard.irembopay.com/assets/payment/inline.js"></script> 
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            text-align: center;
            padding: 20px;
        }

        .container {
            max-width: 500px;
            background: white;
            padding: 20px;
            margin: auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        h2 {
            color: #007BFF;
        }

        p {
            font-size: 16px;
            margin: 10px 0;
        }

        strong {
            color: #555;
        }

        .payment-button {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            transition: 0.3s;
        }

        .payment-button:hover {
            background: #218838;
        }

        @media (max-width: 600px) {
            .container {
                width: 90%;
                padding: 15px;
            }

            p {
                font-size: 14px;
            }

            .payment-button {
                width: 100%;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Subscription Payment Details</h2>
        <p><strong>Reference Number:</strong> {{ reference_number }}</p>
        <p><strong>Customer Name:</strong> {{ customer_name }}</p>
        <p><strong>Customer Email:</strong> {{ customer_email }}</p>
        <p><strong>Customer Phone:</strong> {{ customer_phone }}</p>
        <p><strong>Subscription Duration:</strong> {{ quantity }} month(s)</p>
        <p><strong>Monthly Subscription Fee:</strong> RWF {{ Auxillary.format_amount(unit_price) }} </p>
        <p><strong>Total Amount Due:</strong> RWF {{ Auxillary.format_amount(total_price) }}</p>
        <button class="payment-button" onclick="makePayment('{{ reference_number }}')">Proceed to Payment</button>
    </div>
    <script>
        function makePayment(invoiceNumber) {
            IremboPay.initiate({
                publicKey: "pk_live_96d9ed080ad6416b820ec36e572c1019",
                invoiceNumber: invoiceNumber,
                locale: IremboPay.locale.EN,
                callback: (err, resp) => {
                    if (!err) {
                        alert("Payment successful!");
                        console.log("Success:", resp);
                        // Send payment confirmation to the backend
                        fetch('/payment_confirmation', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(resp)
                        })
                        .then(response => response.json())
                        .then(data => console.log("Backend Response:", data))
                        .catch(error => console.error("Backend Error:", error));
                        // Reload and redirect to /hr_dashboard
                        location.reload(true);

                    } else {
                        alert("Payment failed: " + err.message);
                        console.error("Error:", err);
                    }
                }
            });
        }
    </script>
</body>
</html>
