<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RSSB Contributions</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h4>RSSB Contributions</h4>
    <table>
        <thead>
            <tr>
                <th>Employee</th>
                <th>Basic Salary</th>
                <th>Gross Salary</th>
                <th>PAYE</th>
                <th>Net Pay</th>
                <th>Contribution Name</th>
                <th>Employee Contribution</th>
                <th>Employer Contribution</th>
                
            </tr>
        </thead>
        <tbody>
            {% for contribution in contributions_data %}
                <tr>
                    <td rowspan="{{ contribution.contributions|length }}">{{ contribution.employee.first_name }} {{ contribution.employee.last_name }}</td>
                    <td rowspan="{{ contribution.contributions|length }}">{{ contribution.employee.basic_salary }}</td>
                    <td rowspan="{{ contribution.contributions|length }}">{{ contribution.employee.gross_salary }}</td>
                    <td rowspan="{{ contribution.contributions|length }}">{{ contribution.paye }}</td>
                    <td rowspan="{{ contribution.contributions|length }}">{{ contribution.net_pay}}</td>
                    <td>{{ contribution.contributions[0].contribution_name }}</td>
                    <td>{{ contribution.contributions[0].employee_contribution_amount }}</td>
                    <td>{{ contribution.contributions[0].employer_contribution_amount }}</td>
                </tr>
                {% for detail in contribution.contributions[1:] %}
                    <tr>
                        <td>{{ detail.contribution_name }}</td>
                        <td>{{ detail.employee_contribution_amount }}</td>
                        <td>{{ detail.employer_contribution_amount }}</td>
                    </tr>
                {% endfor %}
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
