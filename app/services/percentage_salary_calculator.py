"""
Percentage-based salary calculation service.

This module provides functionality to calculate salary components based on percentages
of gross salary, supporting both fixed amounts and percentage-based calculations.
"""

from decimal import Decimal
from flask import current_app
from app.helpers.auxillary import Auxillary
from app.routes.payroll.goal_Seek_mine import SalaryCalculator
from app.models.central import NsfContributions
from app.models.company import Insurance
import numpy as np


class PercentageBasedSalaryCalculator:
    """Service class for percentage-based salary calculations."""

    @staticmethod
    def validate_percentages(percentages):
        """
        Validate that percentages are within acceptable ranges and total doesn't exceed 100%.

        Args:
            percentages (dict): Dictionary containing percentage values

        Returns:
            tuple: (is_valid, error_message)
        """
        total_percentage = Decimal('0')

        for key, value in percentages.items():
            if value is not None:
                if value < 0 or value > 100:
                    return False, f"{key.replace('_', ' ').title()} must be between 0 and 100%"
                total_percentage += Decimal(str(value))

        if total_percentage > 100:
            return False, f"Total percentages ({total_percentage}%) cannot exceed 100%"

        return True, None

    @staticmethod
    def calculate_components_from_gross(gross_salary, percentages):
        """
        Calculate salary components from gross salary and percentages.

        Args:
            gross_salary (Decimal): The gross salary amount
            percentages (dict): Dictionary containing percentage values

        Returns:
            dict: Dictionary containing calculated component amounts
        """
        if not gross_salary:
            return {
                'basic_salary': None,
                'transport_allowance': None,
                'housing_allowance': None,
                'communication_allowance': None
            }

        gross_decimal = Decimal(str(gross_salary))

        # Calculate each component
        basic_salary = None
        transport_allowance = None
        housing_allowance = None
        communication_allowance = None

        if percentages.get('basic_salary_percentage'):
            basic_salary = gross_decimal * (Decimal(str(percentages['basic_salary_percentage'])) / 100)
            basic_salary = Auxillary.round_to_decimal(basic_salary)

        if percentages.get('transport_allowance_percentage'):
            transport_allowance = gross_decimal * (Decimal(str(percentages['transport_allowance_percentage'])) / 100)
            transport_allowance = Auxillary.round_to_decimal(transport_allowance)

        if percentages.get('housing_allowance_percentage'):
            housing_allowance = gross_decimal * (Decimal(str(percentages['housing_allowance_percentage'])) / 100)
            housing_allowance = Auxillary.round_to_decimal(housing_allowance)

        if percentages.get('communication_allowance_percentage'):
            communication_allowance = gross_decimal * (Decimal(str(percentages['communication_allowance_percentage'])) / 100)
            communication_allowance = Auxillary.round_to_decimal(communication_allowance)

        current_app.logger.info(f"Calculated components from gross {gross_salary}: "
                               f"basic={basic_salary}, transport={transport_allowance}, "
                               f"housing={housing_allowance}, communication={communication_allowance}")

        return {
            'basic_salary': basic_salary,
            'transport_allowance': transport_allowance,
            'housing_allowance': housing_allowance,
            'communication_allowance': communication_allowance
        }

    @staticmethod
    def calculate_gross_from_basic(basic_salary, basic_salary_percentage):
        """
        Calculate gross salary from basic salary and percentage.

        Args:
            basic_salary (Decimal): The basic salary amount
            basic_salary_percentage (Decimal): The percentage of gross that basic salary represents

        Returns:
            Decimal: The calculated gross salary
        """
        if not basic_salary or not basic_salary_percentage:
            return None

        basic_decimal = Decimal(str(basic_salary))
        percentage_decimal = Decimal(str(basic_salary_percentage))

        if percentage_decimal == 0:
            return None

        gross_salary = basic_decimal / (percentage_decimal / 100)
        return Auxillary.round_to_decimal(gross_salary)

    @staticmethod
    def calculate_remaining_allowances(gross_salary, percentages, fixed_allowances=None):
        """
        Calculate remaining allowances that are not percentage-based.

        Args:
            gross_salary (Decimal): The gross salary amount
            percentages (dict): Dictionary containing percentage values
            fixed_allowances (dict): Dictionary containing fixed allowance amounts

        Returns:
            dict: Dictionary containing all allowance amounts
        """
        if fixed_allowances is None:
            fixed_allowances = {}

        # Calculate percentage-based components
        calculated_components = PercentageBasedSalaryCalculator.calculate_components_from_gross(
            gross_salary, percentages
        )

        # Merge with fixed allowances (fixed takes precedence)
        result = {
            'transport_allowance': fixed_allowances.get('transport_allowance') or calculated_components.get('transport_allowance') or 0,
            'housing_allowance': fixed_allowances.get('housing_allowance') or calculated_components.get('housing_allowance') or 0,
            'communication_allowance': fixed_allowances.get('communication_allowance') or calculated_components.get('communication_allowance') or 0,
            'basic_salary': calculated_components.get('basic_salary') or 0
        }

        return result

    @staticmethod
    def get_calculation_summary(gross_salary, percentages, calculation_method='percentage_based'):
        """
        Get a summary of salary calculations for display purposes.

        Args:
            gross_salary (Decimal): The gross salary amount
            percentages (dict): Dictionary containing percentage values
            calculation_method (str): The calculation method used

        Returns:
            dict: Summary of calculations
        """
        if calculation_method != 'percentage_based':
            return {}

        components = PercentageBasedSalaryCalculator.calculate_components_from_gross(
            gross_salary, percentages
        )

        total_calculated = sum([
            components.get('basic_salary') or 0,
            components.get('transport_allowance') or 0,
            components.get('housing_allowance') or 0,
            components.get('communication_allowance') or 0
        ])

        return {
            'gross_salary': gross_salary,
            'basic_salary': components.get('basic_salary'),
            'transport_allowance': components.get('transport_allowance'),
            'housing_allowance': components.get('housing_allowance'),
            'communication_allowance': components.get('communication_allowance'),
            'total_calculated': total_calculated,
            'remaining_amount': Decimal(str(gross_salary)) - Decimal(str(total_calculated)) if gross_salary else 0,
            'calculation_method': calculation_method
        }

    @staticmethod
    def calculate_gross_from_net_percentage_based(net_salary, percentages, company_tax_config):
        """
        Calculate gross salary and percentage-based components from net salary using goal seek.

        Args:
            net_salary (Decimal): Target net salary
            percentages (dict): Percentage breakdown for components
            company_tax_config (dict): Tax rates and deduction configuration

        Returns:
            dict: Contains gross_salary, components, and calculation details
        """
        try:
            current_app.logger.info(f"Starting goal seek for net salary: {net_salary}")
            current_app.logger.info(f"Percentages: {percentages}")
            current_app.logger.info(f"Tax config: {company_tax_config}")

            # Calculate smart initial guess for basic salary
            # This improves convergence speed and reliability
            initial_guess = Decimal('1.0')  # Fallback if smart guess fails
            current_app.logger.info(f"Fallback initial guess for basic salary: {initial_guess}")

            # Create calculator instance with percentage-based logic
            calculator = PercentageBasedGoalSeeker(
                percentages=percentages,
                **company_tax_config
            )

            # Use goal seek to find the gross salary
            current_app.logger.info("Starting goal seek calculation...")
            result = calculator.goalseek(net_salary, initial_guess)  # Pass Decimal directly
            current_app.logger.info(f"Goal seek result: {result}")

            # Extract results from goal seek
            gross_salary = result[1]

            # Calculate final components using the calculated gross
            components = PercentageBasedSalaryCalculator.calculate_components_from_gross(
                gross_salary, percentages
            )

            current_app.logger.info(f"Final gross salary: {gross_salary}")
            current_app.logger.info(f"Calculated components: {components}")

            return {
                'gross_salary': Decimal(str(gross_salary)),
                'basic_salary': components.get('basic_salary'),
                'transport_allowance': components.get('transport_allowance'),
                'housing_allowance': components.get('housing_allowance'),
                'communication_allowance': components.get('communication_allowance'),
                'net_salary': Decimal(str(result[-1])),  # final net from goal seek
                'calculation_method': 'percentage_based_from_net',
                'goal_seek_iterations': 'completed'
            }

        except Exception as e:
            current_app.logger.error(f"Error in goal seek calculation: {e}")
            raise Exception(f"Failed to calculate gross from net salary: {str(e)}")

    @staticmethod
    def get_company_tax_config(db_session, employee_type='permanent'):
        """
        Get company tax configuration for goal seek calculations.

        Args:
            db_session: Database session for company-specific data
            employee_type (str): Type of employee (permanent, casual, etc.)

        Returns:
            dict: Tax configuration parameters
        """
        try:
            current_app.logger.info(f"Getting tax config for employee type: {employee_type}")

            # Default rates
            pension_ee_rate = Decimal('0.06')  # Updated to 6% as per Rwanda 2025
            pension_er_rate = Decimal('0.06')  # Updated to 6% as per Rwanda 2025
            maternity_ee_rate = Decimal('0.003')  # 0.3%
            maternity_er_rate = Decimal('0.003')  # 0.3%
            rama_contributions_rate = Decimal('0.005')  # 0.5% for CBHI
            rama_er_rate = Decimal('0')
            cbhi_rate = Decimal('0.005')  # 0.5% CBHI rate

            # Get RSSB contributions (pension and maternity)
            try:
                from datetime import datetime
                current_date = datetime.now().date()

                contributions = NsfContributions.get_nsf_contributions()
                current_app.logger.info(f"Retrieved NSF contributions: {contributions}")

                for contribution in contributions:
                    # Handle both dictionary and object formats
                    if hasattr(contribution, 'contribution_name'):
                        # Object format
                        contribution_name = contribution.contribution_name.lower()
                        employee_rate = contribution.employee_rate
                        employer_rate = contribution.employer_rate
                        start_date = contribution.start_date
                        end_date = contribution.end_date
                    else:
                        # Dictionary format
                        contribution_name = contribution['contribution_name'].lower()
                        employee_rate = contribution['employee_rate']
                        employer_rate = contribution['employer_rate']
                        start_date = contribution['start_date']
                        end_date = contribution['end_date']

                    # Check if this contribution is currently active
                    is_active = start_date <= current_date and (end_date is None or current_date <= end_date)

                    if is_active:
                        if contribution_name == 'pension':
                            pension_ee_rate = Decimal(str(employee_rate))
                            pension_er_rate = Decimal(str(employer_rate))
                            current_app.logger.info(f"Using active pension rate: {pension_ee_rate}% (period: {start_date} to {end_date})")
                        elif contribution_name == 'maternity':
                            maternity_ee_rate = Decimal(str(employee_rate))
                            maternity_er_rate = Decimal(str(employer_rate))
                            current_app.logger.info(f"Using active maternity rate: {maternity_ee_rate}% (period: {start_date} to {end_date})")

                current_app.logger.info(f"Final rates from NSF - Pension EE: {pension_ee_rate}, "
                                      f"Pension ER: {pension_er_rate}, Maternity EE: {maternity_ee_rate}, "
                                      f"Maternity ER: {maternity_er_rate}")

            except Exception as e:
                current_app.logger.error(f"Error getting NSF contributions: {e}")

            # Get insurance rates (RAMA and CBHI) from company insurance
            try:
                insurance = Insurance.get_insurances(db_session)
                current_app.logger.info(f"Retrieved insurance data: {insurance}")

                if insurance and len(insurance) > 0:
                    for ins in insurance:
                        insurance_name = ins.get("insurance_name", "").lower()
                        if insurance_name == "rama":
                            rama_contributions_rate = Decimal(str(ins["employee_rate"]))
                            rama_er_rate = Decimal(str(ins["employer_rate"]))
                        elif insurance_name == "cbhi" or insurance_name == "community based health insurance":
                            cbhi_rate = Decimal(str(ins["employee_rate"]))

                current_app.logger.info(f"Updated insurance rates - RAMA Employee: {rama_contributions_rate}, "
                                      f"RAMA Employer: {rama_er_rate}, CBHI: {cbhi_rate}")

            except Exception as e:
                current_app.logger.error(f"Error getting insurance rates: {e}")

            config = {
                'other_deductions': Decimal('0'),  # No other deductions during registration
                'contributions_rate': cbhi_rate,  # CBHI rate for net salary calculation
                'pension_ee_rate': pension_ee_rate,
                'pension_er_rate': pension_er_rate,
                'maternity_ee_rate': maternity_ee_rate,
                'maternity_er_rate': maternity_er_rate,
                'rama_contributions_rate': rama_contributions_rate,  # RAMA rate (separate from CBHI)
                'rama_er_rate': rama_er_rate,
                'cbhi_rate': cbhi_rate,  # Explicit CBHI rate
                'employee_type': employee_type
            }

            current_app.logger.info(f"Final tax config: {config}")
            return config

        except Exception as e:
            current_app.logger.error(f"Error getting company tax config: {e}")
            # Return default configuration with correct Rwanda rates
            return {
                'other_deductions': Decimal('0'),
                'contributions_rate': Decimal('0.005'),  # 0.5% CBHI
                'pension_ee_rate': Decimal('0.06'),  # 6% pension
                'pension_er_rate': Decimal('0.06'),  # 6% pension
                'maternity_ee_rate': Decimal('0.003'),  # 0.3% maternity
                'maternity_er_rate': Decimal('0.003'),  # 0.3% maternity
                'rama_contributions_rate': Decimal('0.005'),  # 0.5% CBHI (fallback)
                'rama_er_rate': Decimal('0'),
                'cbhi_rate': Decimal('0.005'),  # 0.5% CBHI
                'employee_type': employee_type
            }


class PercentageBasedGoalSeeker(SalaryCalculator):
    """Custom goal seeker that works with percentage-based salary components."""

    def __init__(self, percentages, **kwargs):
        """
        Initialize with percentage configuration.

        Args:
            percentages (dict): Percentage breakdown for salary components
            **kwargs: Other tax configuration parameters
        """
        # Store cbhi_rate separately since SalaryCalculator doesn't accept it
        self.cbhi_rate = kwargs.pop('cbhi_rate', Decimal('0.005'))

        # Convert all kwargs to Decimal to ensure consistency
        converted_kwargs = {}
        for key, value in kwargs.items():
            if key == 'employee_type':
                converted_kwargs[key] = value  # Keep string as is
            elif isinstance(value, Decimal):
                converted_kwargs[key] = value  # Already Decimal
            else:
                converted_kwargs[key] = Decimal(str(value))  # Convert to Decimal

        current_app.logger.info(f"Converted kwargs: {converted_kwargs}")
        current_app.logger.info(f"CBHI rate: {self.cbhi_rate}")

        # Initialize with zero allowances since we'll calculate them from percentages
        super().__init__(
            allowances=Decimal('0'),  # Will be calculated from percentage
            transport_allowance=Decimal('0'),  # Will be calculated from percentage
            **converted_kwargs
        )
        self.percentages = percentages
        current_app.logger.info(f"Initialized PercentageBasedGoalSeeker with percentages: {percentages}")

    def gross_salary(self, basic_salary):
        """
        Override to calculate gross from basic using percentage method.
        Also updates allowances based on calculated gross salary.

        Args:
            basic_salary: The basic salary amount

        Returns:
            Decimal: Calculated gross salary
        """
        try:
            current_app.logger.info(f"Calculating gross from basic salary: {basic_salary}")
            current_app.logger.info(f"Using percentages: {self.percentages}")

            # Ensure basic_salary is Decimal
            if isinstance(basic_salary, np.ndarray):
                basic_salary = basic_salary.item()
            basic_decimal = Decimal(str(basic_salary)) if not isinstance(basic_salary, Decimal) else basic_salary

            # Calculate gross from basic salary percentage
            if self.percentages.get('basic_salary_percentage'):
                basic_percentage = Decimal(str(self.percentages['basic_salary_percentage']))

                if basic_percentage == 0:
                    current_app.logger.error("Basic salary percentage cannot be zero")
                    raise ValueError("Basic salary percentage cannot be zero")

                gross = basic_decimal / (basic_percentage / 100)
                current_app.logger.info(f"Calculated gross salary: {gross} (type: {type(gross)})")

                # CRITICAL FIX: Update allowances based on calculated gross salary
                self._update_allowances_from_gross(gross)

                # Ensure we return a Decimal
                return gross if isinstance(gross, Decimal) else Decimal(str(gross))
            else:
                current_app.logger.warning("No basic salary percentage provided, using fallback method")
                # Fallback: if no basic percentage, assume basic = gross (100%)
                return basic_decimal

        except Exception as e:
            current_app.logger.error(f"Error calculating gross salary: {e}")
            raise

    def _update_allowances_from_gross(self, gross_salary):
        """
        Update allowances based on calculated gross salary and percentages.
        This ensures the parent SalaryCalculator uses correct allowance values.

        Args:
            gross_salary (Decimal): The calculated gross salary
        """
        try:
            gross_decimal = Decimal(str(gross_salary)) if not isinstance(gross_salary, Decimal) else gross_salary

            # Calculate allowances from percentages
            transport_allowance = Decimal('0')
            housing_allowance = Decimal('0')
            communication_allowance = Decimal('0')

            if self.percentages.get('transport_allowance_percentage'):
                transport_allowance = gross_decimal * (Decimal(str(self.percentages['transport_allowance_percentage'])) / 100)
                transport_allowance = Auxillary.round_to_decimal(transport_allowance)

            if self.percentages.get('housing_allowance_percentage'):
                housing_allowance = gross_decimal * (Decimal(str(self.percentages['housing_allowance_percentage'])) / 100)
                housing_allowance = Auxillary.round_to_decimal(housing_allowance)

            if self.percentages.get('communication_allowance_percentage'):
                communication_allowance = gross_decimal * (Decimal(str(self.percentages['communication_allowance_percentage'])) / 100)
                communication_allowance = Auxillary.round_to_decimal(communication_allowance)

            # Update parent class allowances (used in pension, maternity, RAMA calculations)
            self.transport_allowance = transport_allowance
            self.allowances = housing_allowance + communication_allowance  # Non-transport allowances

            current_app.logger.info(f"Updated allowances: transport={self.transport_allowance}, other_allowances={self.allowances}")

        except Exception as e:
            current_app.logger.error(f"Error updating allowances: {e}")
            # Set to zero if calculation fails
            self.transport_allowance = Decimal('0')
            self.allowances = Decimal('0')

    def calculate_smart_initial_guess(self, target_net_salary, percentages):
        """Calculate a better initial guess for basic salary based on target net salary."""
        try:
            target_net_decimal = Decimal(str(target_net_salary))
            current_app.logger.info(f"Calculating smart initial guess for target net: {target_net_decimal}")

            # Rough estimate: assume taxes/deductions are about 25-35% of gross
            estimated_gross = target_net_decimal * Decimal('1.35')
            current_app.logger.info(f"Estimated gross salary: {estimated_gross}")

            # If we have basic salary percentage, estimate basic salary
            if percentages.get('basic_salary_percentage'):
                basic_percentage = percentages['basic_salary_percentage']
                estimated_basic = estimated_gross * (basic_percentage / 100)
                smart_guess = max(estimated_basic, Decimal('10000'))  # Minimum reasonable guess
                current_app.logger.info(f"Smart guess based on {basic_percentage}% basic: {smart_guess}")
                return smart_guess

            # Fallback: assume basic is about 60% of gross
            fallback_guess = estimated_gross * Decimal('0.6')
            smart_guess = max(fallback_guess, Decimal('10000'))
            current_app.logger.info(f"Smart guess using 60% fallback: {smart_guess}")
            return smart_guess

        except Exception as e:
            current_app.logger.error(f"Error calculating smart initial guess: {e}")
            # Return a reasonable default
            return max(Decimal(str(target_net_salary)) * Decimal('0.8'), Decimal('10000'))

    def validate_solution(self, solution, target_net_salary, tolerance_percent=1.0):
        """Validate that the goal seek solution is within acceptable tolerance."""
        try:
            target_net_decimal = Decimal(str(target_net_salary))
            solution_decimal = Decimal(str(solution))

            # Calculate actual net salary with the solution
            _, _, _, _, _, _, _, _, _, _, _, actual_net = self.calculate_all(solution_decimal)

            # Calculate error percentage
            error = abs(actual_net - target_net_decimal)
            error_percentage = float(error / target_net_decimal) * 100

            current_app.logger.info(f"Solution validation: target={target_net_decimal}, actual={actual_net}, error={error_percentage:.3f}%")

            is_valid = error_percentage <= tolerance_percent
            if not is_valid:
                current_app.logger.warning(f"Solution validation failed: error {error_percentage:.3f}% exceeds tolerance {tolerance_percent}%")

            return is_valid, error_percentage

        except Exception as e:
            current_app.logger.error(f"Error validating solution: {e}")
            return False, 100.0

    def goalseek(self, target_net_salary, initial_guess):
        """
        Enhanced goalseek with better tolerance, smart initial guess, and convergence validation.

        Args:
            target_net_salary: Target net salary (can be float or Decimal)
            initial_guess: Initial guess for basic salary (will be improved automatically)

        Returns:
            Tuple of calculated values
        """
        import scipy.optimize as opt

        # Ensure target is Decimal for precision
        target_net_decimal = Decimal(str(target_net_salary))
        current_app.logger.info(f"Starting enhanced goal seek for target net salary: {target_net_decimal}")

        # Calculate smart initial guess
        smart_guess = self.calculate_smart_initial_guess(target_net_salary, self.percentages)
        current_app.logger.info(f"Using smart initial guess: {smart_guess} (original: {initial_guess})")

        # Set reasonable bounds for basic salary (10K to 10M RWF)
        min_basic = Decimal('10000')
        max_basic = Decimal('10000000')

        iteration_count = 0

        def equation_to_solve(basic_salary):
            nonlocal iteration_count
            iteration_count += 1

            # Ensure basic salary stays within reasonable bounds
            basic_clamped = max(float(min_basic), min(float(max_basic), basic_salary[0]))
            basic_salary_decimal = Decimal(str(basic_clamped))

            try:
                _, _, _, _, _, _, _, _, _, _, _, net_salary_value = self.calculate_all(basic_salary_decimal)

                # Calculate difference
                difference = net_salary_value - target_net_decimal
                error_pct = abs(float(difference) / float(target_net_decimal)) * 100

                # Log progress every 10 iterations or when close to solution
                if iteration_count % 10 == 0 or error_pct < 1.0:
                    current_app.logger.debug(
                        f"Goal seek iteration {iteration_count}: basic={basic_salary_decimal}, "
                        f"net_calc={net_salary_value}, target={target_net_decimal}, "
                        f"error={float(difference):.2f} ({error_pct:.3f}%)"
                    )

                # Convert result to float for fsolve
                return float(difference)

            except Exception as e:
                current_app.logger.error(f"Error in goal seek iteration {iteration_count}: {e}")
                # Return a large error to guide the algorithm away from this point
                return float(target_net_decimal)

        try:
            # Use improved tolerance and more iterations
            current_app.logger.info("Starting goal seek with fsolve algorithm...")
            info = opt.fsolve(equation_to_solve, float(smart_guess), xtol=1e-6, maxfev=2000, full_output=True)
            solution, _, ier, mesg = info

            current_app.logger.info(f"Goal seek completed after {iteration_count} iterations")
            current_app.logger.info(f"Convergence info: ier={ier}, message='{mesg}'")

            # Check convergence
            if ier != 1:
                current_app.logger.warning(f"Goal seek may not have converged properly: {mesg}")
                # Continue anyway but log the warning

            # Validate the solution
            solution_decimal = Decimal(str(solution[0]))
            is_valid, error_percentage = self.validate_solution(solution[0], target_net_salary, tolerance_percent=2.0)

            if not is_valid:
                current_app.logger.warning(f"Solution has {error_percentage:.3f}% error, but proceeding")

            # Ensure solution is within bounds
            if solution_decimal < min_basic or solution_decimal > max_basic:
                current_app.logger.warning(f"Solution {solution_decimal} is outside reasonable bounds [{min_basic}, {max_basic}]")
                solution_decimal = max(min_basic, min(max_basic, solution_decimal))
                current_app.logger.info(f"Clamped solution to: {solution_decimal}")

            # Calculate final results
            gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value = self.calculate_all(solution_decimal)

            current_app.logger.info(f"Final goal seek results: basic={solution_decimal}, gross={gross}, net={net_salary_value}")
            current_app.logger.info(f"Final error: {float(net_salary_value - target_net_decimal):.2f} ({abs(float(net_salary_value - target_net_decimal) / float(target_net_decimal) * 100):.3f}%)")

            return solution_decimal, gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value

        except Exception as e:
            current_app.logger.error(f"Goal seek failed with error: {e}")
            current_app.logger.error(f"Target: {target_net_decimal}, Initial guess: {smart_guess}")
            raise Exception(f"Goal seek calculation failed: {str(e)}")
