import psycopg2
from psycopg2 import sql
from sqlalchemy import create_engine
from sqlalchemy.exc import ProgrammingError, SQLAlchemyError
from dotenv import load_dotenv
from flask import current_app, session
import os
from app.models.company import DynamicBase

load_dotenv()

def create_postgres_db(database_name):
    # Database connection parameters (from environment variables or default values)
    db_host = os.getenv('DB_HOST')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_port = "5432"
    
    # Connect to the default database
    try:
        conn = psycopg2.connect(
            host=db_host,
            user=db_user,
            password=db_password,
            port=db_port,
            database='postgres'  # Connect to the default 'postgres' database
        )
        conn.autocommit = True  # Needed to execute CREATE DATABASE command
        cursor = conn.cursor()

        
        # Use sql.Identifier to safely format the database name
        cursor.execute(sql.SQL("CREATE DATABASE {}").format(sql.Identifier(database_name)))
        current_app.logger.info(f"Database '{database_name}' created successfully.")
                           
    except psycopg2.Error as e:
        current_app.logger.error(f"Error while creating database '{database_name}': {e}")

    except Exception as e:
        current_app.logger.error(f"An error occurred while creating database {database_name}: {str(e)}")
            
    finally:
        # Clean up the connection and cursor
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def create_database(database_name):
    try:
        create_postgres_db(database_name)
        current_app.logger.info(f"Database '{database_name}' created successfully.")
                   
        # Get database password from environment variables
        pwd = os.getenv('DB_PASSWORD')
        db_user = os.getenv('DB_USER')
        db_host = os.getenv('DB_HOST')
        if not pwd:
            current_app.logger.error("Database password not found in environment variables.")
            raise ValueError("Database password not found in environment variables.")
        
        # Connect to the newly created database and create tables
        engine = create_engine(f'postgresql://{db_user}:{pwd}@{db_host}/{database_name}')
        DynamicBase.metadata.create_all(engine)
        current_app.logger.info(f"Tables created successfully in database '{database_name}'.")
        
    except (ProgrammingError, SQLAlchemyError) as e:
        current_app.logger.error(f"Failure with SQLAlchemy: {str(e)}")
    except Exception as e:
        current_app.logger.error(f"Something went wrong with error message: {str(e)}")
