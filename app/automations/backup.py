import os
import subprocess
import logging
import zipfile
from dotenv import load_dotenv
from datetime import datetime
from app.helpers.company_helpers import CompanyHelpers
from app import app
from flask import current_app
from app.helpers.auxillary import Auxillary


load_dotenv()

def create_backup(database_name, backup_dir, db_user, db_password, db_host):
    """Creates a dump of the specified database."""
    try:
        # Create a timestamped filename
        current_date = datetime.now().strftime('%d-%m-%Y')
        backup_filename = f"{backup_dir}/{database_name}-{current_date}.sql"
        compressed_backup_filename = f"{backup_filename}.tar.gz"

        # Step 1: Dump the PostgreSQL database
        current_app.logger.info(f"Creating backup for database: {database_name}")
        dump_command = ["pg_dump", "-U", db_user, "-h", db_host, "-F", "c", "-f", backup_filename, database_name]
        
        # Set environment variable PGPASSWORD to avoid password prompt
        env = os.environ.copy()
        env["PGPASSWORD"] = db_password
        
        # Run the dump command
        subprocess.run(dump_command, check=True, env=env)
        current_app.logger.info(f"Backup file saved to {backup_filename}")

        # Step 2: Compress the SQL backup file
        current_app.logger.info(f"Compressing backup file...")
        subprocess.run(["tar", "-czf", compressed_backup_filename, backup_filename], check=True)
        current_app.logger.info(f"Compressed backup file saved to {compressed_backup_filename}")

        # Step 3: Optionally remove the original .sql file
        os.remove(backup_filename)
        current_app.logger.info(f"Removed original backup file {backup_filename}")
        return compressed_backup_filename
        
    except subprocess.CalledProcessError as e:
        current_app.logger.error(f"Error creating backup: {e}")
    except Exception as e:
        current_app.logger.error(f"An unexpected error occurred: {e}")


def backup_all_databases():
    """Fetches all company databases and creates a single zip file containing all backups."""
    backup_dir = os.getenv('BACKUP_PATH')
    db_password = os.getenv('DB_PASSWORD')
    db_user = os.getenv('DB_USER')
    db_host = os.getenv('DB_HOST')

    # Ensure backup directory exists
    os.makedirs(backup_dir, exist_ok=True)

    current_date = datetime.now().strftime('%d-%m-%Y')
    zip_filename = f"{backup_dir}/all_backups-{current_date}.zip"

    with app.app_context():
        try:
            # Fetch company database names
            company_databases = CompanyHelpers.get_database_names()
            if not company_databases:
                current_app.logger.error("No company databases found.")
                return
            
            # Create a zip file to store all backups
            with zipfile.ZipFile(zip_filename, 'w') as backup_zip:
                # Create a backup for each database
                for db_name in company_databases:
                    current_app.logger.info(f"Creating backup for database: {db_name}")
                    backup_file = create_backup(db_name, backup_dir, db_user, db_password, db_host)
                    if backup_file:
                        backup_zip.write(backup_file, os.path.basename(backup_file))
                        os.remove(backup_file)  # Remove the individual compressed file after adding to the zip
                        current_app.logger.info(f"Removed compressed backup file {backup_file}")
                
                # Create a backup for the main database
                main_db_name = os.getenv('DB_NAME')
                current_app.logger.info(f"Creating backup for main database: {main_db_name}")
                main_backup_file = create_backup(main_db_name, backup_dir, db_user, db_password, db_host)
                if main_backup_file:
                    backup_zip.write(main_backup_file, os.path.basename(main_backup_file))
                    #os.remove(main_backup_file)  # Remove the individual compressed file after adding to the zip
                    current_app.logger.info(f"Removed compressed backup file {main_backup_file}")

            current_app.logger.info(f"Backup process completed. All backups stored in {zip_filename}")
            return zip_filename

        except Exception as e:
            current_app.logger.error(f"An unexpected error occurred: {e}")

def send_backup_email():
    """Sends an email with the backup file as an attachment."""
    email = "<EMAIL>"
    subject = "Database Backup"
    message = "Attached is the latest database backup."
    backup_file = backup_all_databases()

    if backup_file:
        print("backup file: ", backup_file)
        Auxillary.send_netpipo_email_attachment(email, subject, message, backup_file)
        os.remove(backup_file)
        current_app.logger.info("Backup email sent successfully.")
        return True
    else:
        current_app.logger.error("Error sending backup email.")
        return False

    

if __name__ == "__main__":
    backup_all_databases()
