import os
import face_recognition

class FaceRecognition:
    # Known faces located in /home/<USER>/acr_hrms/app/data/known_faces
    def __init__(self, known_faces_dir):
        self.known_faces_dir = known_faces_dir
        self.known_face_encodings = []
        self.known_face_names = []
        self.load_known_faces()

    def load_known_faces(self):
        # Debug: Check if the directory exists
        if not os.path.exists(self.known_faces_dir):
            print(f"Directory {self.known_faces_dir} does not exist.")
            return
        
        # Debug: List all files in the directory
        files = os.listdir(self.known_faces_dir)
        print(f"Found {len(files)} files in {self.known_faces_dir}")
        
        for filename in files:
            image_path = os.path.join(self.known_faces_dir, filename)
            # Debug: Print the image path being processed
            print(f"Processing file: {image_path}")
            
            try:
                image = face_recognition.load_image_file(image_path)
                # Debug: Print if the image is successfully loaded
                print(f"Loaded image: {image_path}")
                
                encodings = face_recognition.face_encodings(image)
                if encodings:
                    encoding = encodings[0]
                    self.known_face_encodings.append(encoding)
                    self.known_face_names.append(os.path.splitext(filename)[0])
                    # Debug: Print the success of encoding
                    print(f"Encoded {filename}")
                else:
                    # Debug: No faces found in the image
                    print(f"No faces found in {filename}")
                    
            except Exception as e:
                # Debug: Print error if loading or encoding fails
                print(f"Error processing {filename}: {e}")

    def recognize_faces(self, image_path):
        unknown_image = face_recognition.load_image_file(image_path)
        unknown_encodings = face_recognition.face_encodings(unknown_image)

        for unknown_encoding in unknown_encodings:
            results = face_recognition.compare_faces(self.known_face_encodings, unknown_encoding)
            print("Results:", results)
            if True in results:
                match_index = results.index(True)
                print("Match Index:", match_index)
                name = self.known_face_names[match_index]
                return f"Recognized: {name}"
            else:
                return "Unknown person"

# Usage Example:
if __name__ == "__main__":
    face_recognition_service = FaceRecognition("/home/<USER>/acr_hrms/app/data/known_faces")
    result = face_recognition_service.recognize_faces("/home/<USER>/acr_hrms/app/data/unknown_faces/missing.jpeg")
    print(result)
