"""
Database connection monitoring utilities.

This module provides functions to monitor database connections and diagnose connection issues.
"""
from sqlalchemy import text
import logging
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

def get_active_connections(engine):
    """Get the number of active connections to the database.

    Args:
        engine: SQLAlchemy engine instance

    Returns:
        int: Number of active connections
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT count(*) FROM pg_stat_activity
                WHERE datname = current_database()
            """))
            return result.scalar()
    except Exception as e:
        logger.error(f"Error getting active connections: {str(e)}")
        return -1

def get_connection_details(engine):
    """Get detailed information about active connections.

    Args:
        engine: SQLAlchemy engine instance

    Returns:
        list: List of dictionaries with connection details
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT
                    pid,
                    datname,
                    usename,
                    application_name,
                    client_addr,
                    backend_start,
                    state,
                    state_change,
                    query
                FROM pg_stat_activity
                WHERE datname = current_database()
            """))

            # Handle different SQLAlchemy versions and row types
            connections = []
            for row in result:
                try:
                    # First try the dict() method (works in most SQLAlchemy versions)
                    connections.append(dict(row))
                except Exception as dict_error:
                    # If dict() fails, try to manually create a dictionary
                    logger.info(f"Using manual dictionary creation for row: {dict_error}")
                    conn_dict = {}
                    try:
                        # Try accessing as a mapping
                        for key in ['pid', 'datname', 'usename', 'application_name', 'client_addr',
                                   'backend_start', 'state', 'state_change', 'query']:
                            try:
                                conn_dict[key] = row[key]
                            except (KeyError, TypeError):
                                # Try attribute access
                                try:
                                    conn_dict[key] = getattr(row, key, None)
                                except Exception:
                                    conn_dict[key] = None
                    except Exception as row_error:
                        # Last resort: try to access by index if column order is known
                        logger.warning(f"Error processing row by keys: {row_error}")
                        try:
                            conn_dict = {
                                'pid': row[0],
                                'datname': row[1],
                                'usename': row[2],
                                'application_name': row[3],
                                'client_addr': row[4],
                                'backend_start': row[5],
                                'state': row[6],
                                'state_change': row[7],
                                'query': row[8]
                            }
                        except Exception as idx_error:
                            logger.error(f"Failed to process row by index: {idx_error}")
                            # Skip this row if all methods fail
                            continue

                    connections.append(conn_dict)

            return connections
    except Exception as e:
        logger.error(f"Error getting connection details: {str(e)}")
        return []

def get_connection_stats(engine):
    """Get statistics about database connections.

    Args:
        engine: SQLAlchemy engine instance

    Returns:
        dict: Dictionary with connection statistics
    """
    try:
        with engine.connect() as conn:
            # Get max connections setting
            max_conn_result = conn.execute(text("SHOW max_connections"))
            max_connections = max_conn_result.scalar()

            # Get current connection count
            conn_count_result = conn.execute(text("""
                SELECT count(*) FROM pg_stat_activity
                WHERE datname IS NOT NULL
            """))
            connection_count = conn_count_result.scalar()

            # Get connection states
            states_result = conn.execute(text("""
                SELECT state, count(*)
                FROM pg_stat_activity
                WHERE datname IS NOT NULL
                GROUP BY state
            """))
            states = {row[0] if row[0] else 'null': row[1] for row in states_result}

            # Get connection by database
            db_result = conn.execute(text("""
                SELECT datname, count(*)
                FROM pg_stat_activity
                WHERE datname IS NOT NULL
                GROUP BY datname
            """))
            databases = {row[0]: row[1] for row in db_result}

            return {
                'max_connections': max_connections,
                'current_connections': connection_count,
                'usage_percentage': (connection_count / int(max_connections)) * 100 if max_connections else 0,
                'states': states,
                'databases': databases
            }
    except Exception as e:
        logger.error(f"Error getting connection stats: {str(e)}")
        return {}

def log_connection_status(engine):
    """Log the current database connection status.

    Args:
        engine: SQLAlchemy engine instance
    """
    try:
        stats = get_connection_stats(engine)

        if stats:
            logger.info(f"DB Connection Status: {stats['current_connections']}/{stats['max_connections']} "
                       f"({stats['usage_percentage']:.1f}% used)")

            if stats['usage_percentage'] > 80:
                logger.warning(f"High database connection usage: {stats['usage_percentage']:.1f}%")

            logger.info(f"Connection states: {stats['states']}")
            logger.info(f"Connections by database: {stats['databases']}")
    except Exception as e:
        logger.error(f"Error logging connection status: {str(e)}")

def monitor_connections(db_connection, database_name):
    """Monitor connections for a specific database.

    Args:
        db_connection: DatabaseConnection instance
        database_name: Name of the database to monitor

    Returns:
        dict: Connection statistics
    """
    try:
        engine = db_connection._get_engine(database_name)
        stats = get_connection_stats(engine)
        active = get_active_connections(engine)

        logger.info(f"Database {database_name}: {active} active connections")

        return {
            'database': database_name,
            'active_connections': active,
            'stats': stats
        }
    except Exception as e:
        logger.error(f"Error monitoring connections for {database_name}: {str(e)}")
        return {
            'database': database_name,
            'error': str(e)
        }
