"""
Connection limiter to prevent too many database connections.

This module provides a global semaphore to limit the number of concurrent database connections.
"""
import threading
import time
import logging
from contextlib import contextmanager
import os
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

# Global semaphore to limit concurrent database connections
# Default to 50 connections max, but can be overridden with environment variable
MAX_CONCURRENT_CONNECTIONS = int(os.getenv('MAX_DB_CONNECTIONS', '50'))
connection_semaphore = threading.Semaphore(MAX_CONCURRENT_CONNECTIONS)

# Track current connection count for monitoring
current_connections = 0
connection_lock = threading.Lock()

# Track connection statistics for monitoring
total_connections_created = 0
total_connections_released = 0
total_wait_time = 0.0
max_wait_time = 0.0
wait_times = []

@contextmanager
def limit_connections(database_name=None, timeout=5):
    """Context manager to limit the number of concurrent database connections.

    Args:
        database_name: Optional name of the database for logging
        timeout: Maximum time to wait for a connection in seconds

    Yields:
        bool: True if a connection was acquired, False if timed out

    Usage:
        with limit_connections('my_db') as acquired:
            if acquired:
                # Perform database operations
            else:
                # Handle connection limit reached
    """
    global current_connections, total_connections_created, total_connections_released, total_wait_time, max_wait_time, wait_times

    # Try to acquire the semaphore with timeout
    start_time = time.time()
    acquired = False

    while time.time() - start_time < timeout:
        if connection_semaphore.acquire(blocking=False):
            acquired = True
            break
        time.sleep(0.1)  # Small sleep to prevent CPU spinning

    wait_time = time.time() - start_time

    if acquired:
        # Update statistics
        with connection_lock:
            total_connections_created += 1
            total_wait_time += wait_time
            if wait_time > max_wait_time:
                max_wait_time = wait_time
            wait_times.append(wait_time)

            # Increment connection counter
            current_connections += 1
            conn_count = current_connections

        db_info = f" for {database_name}" if database_name else ""
        logger.debug(f"Acquired database connection{db_info}. Current connections: {conn_count}/{MAX_CONCURRENT_CONNECTIONS}")

        try:
            yield True
        finally:
            # Release the semaphore and decrement counter
            with connection_lock:
                current_connections -= 1
                total_connections_released += 1
                conn_count = current_connections

            connection_semaphore.release()
            logger.debug(f"Released database connection{db_info}. Current connections: {conn_count}/{MAX_CONCURRENT_CONNECTIONS}")
    else:
        # Could not acquire semaphore within timeout
        logger.warning(f"Connection limit reached ({MAX_CONCURRENT_CONNECTIONS}). Could not acquire connection{' for ' + database_name if database_name else ''}.")
        yield False

def get_connection_stats():
    """Get current connection statistics.

    Returns:
        dict: Dictionary with connection statistics
    """
    with connection_lock:
        # Calculate average wait time
        average_wait_time = 0.0
        if wait_times:
            average_wait_time = sum(wait_times) / len(wait_times)

        # Limit the size of wait_times list to prevent memory growth
        if len(wait_times) > 1000:
            # Keep only the most recent 1000 wait times
            del wait_times[:-1000]

        return {
            'current_connections': current_connections,
            'max_connections': MAX_CONCURRENT_CONNECTIONS,
            'available_connections': MAX_CONCURRENT_CONNECTIONS - current_connections,
            'usage_percentage': (current_connections / MAX_CONCURRENT_CONNECTIONS) * 100 if MAX_CONCURRENT_CONNECTIONS > 0 else 0,
            'total_connections_created': total_connections_created,
            'total_connections_released': total_connections_released,
            'total_wait_time': total_wait_time,
            'average_wait_time': average_wait_time,
            'max_wait_time': max_wait_time
        }
