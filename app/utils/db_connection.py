from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from dotenv import load_dotenv
import os
import logging
import time
from app.utils.connection_limiter import limit_connections

load_dotenv()

class DatabaseConnection:
    """Database connection class with connection pooling.

    This class maintains a dictionary of engine instances for different databases
    to enable connection pooling and reduce the number of connections.
    """
    # Class-level dictionary to store engine instances for different databases
    _engines = {}

    def __init__(self, db_user=os.getenv('DB_USER'), db_password=os.getenv('DB_PASSWORD'),
                 db_host=os.getenv('DB_HOST'), db_port=os.getenv('DB_PORT', '5432')):
        self.db_user = db_user
        self.db_password = db_password
        self.db_host = db_host
        self.db_port = db_port
        self.logger = logging.getLogger(__name__)

    def _get_engine(self, database_name):
        """Get or create an engine for the specified database.

        Args:
            database_name: str: The name of the database to connect to.

        Returns:
            engine: sqlalchemy.engine.Engine: The engine instance.
        """
        # Create a unique key for this database connection
        key = f"{self.db_host}_{database_name}"

        # If we don't have an engine for this database yet, create one
        if key not in DatabaseConnection._engines:
            try:
                # Create engine with connection pooling configuration
                engine = create_engine(
                    f'postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{database_name}',
                    poolclass=QueuePool,
                    pool_size=2,  # Reduced from 5 to minimize connections
                    max_overflow=3,  # Reduced from 10 to minimize connections
                    pool_timeout=10,  # Reduced timeout to fail faster if no connections available
                    pool_recycle=300,  # Recycle connections after 5 minutes to release them sooner
                    pool_pre_ping=True,  # Check connection validity before using it
                    echo_pool=True  # Log pool events for debugging
                )
                DatabaseConnection._engines[key] = engine
                self.logger.info(f"Created new engine for database: {database_name}")
            except Exception as e:
                self.logger.error(f"Error creating engine for database {database_name}: {str(e)}")
                raise

        return DatabaseConnection._engines[key]

    @contextmanager
    def get_session(self, database_name):
        """Provide a transactional scope around a series of operations.

        Args:
            database_name: str: The name of the database to connect to.

        Returns:
            session: sqlalchemy.orm.session.Session: The session object.

        Raises:
            ConnectionError: If unable to acquire a connection due to connection limits
        """
        # Use the connection limiter to prevent too many connections
        with limit_connections(database_name, timeout=10) as acquired:
            if not acquired:
                self.logger.error(f"Failed to acquire connection for {database_name} - connection limit reached")
                raise ConnectionError(f"Connection limit reached for database {database_name}")

            engine = self._get_engine(database_name)
            # Create a scoped session to ensure thread safety
            session_factory = sessionmaker(bind=engine)
            Session = scoped_session(session_factory)
            session = Session()

            try:
                yield session
                session.commit()
            except Exception as e:
                session.rollback()
                self.logger.error(f"Database error with {database_name}: {str(e)}")
                raise e
            finally:
                # Ensure we close the session and remove it from the registry
                try:
                    session.close()
                except Exception as close_error:
                    self.logger.warning(f"Error closing session for {database_name}: {str(close_error)}")

                try:
                    Session.remove()  # Remove the session from the registry
                except Exception as remove_error:
                    self.logger.warning(f"Error removing session for {database_name}: {str(remove_error)}")
