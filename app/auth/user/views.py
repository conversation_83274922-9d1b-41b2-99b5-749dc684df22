# app.user.views.py
import json
from urllib.parse import quote
from flask import Blueprint, render_template, redirect, url_for, flash, session, request
from .forms import SignupForm, LoginForm, UpdateUserForm, VerifyOtpForm, ResetPasswordForm, UserProfileForm
import logging
from app import db, mail
from app.models.central import User, Company, UserRole
from app.models.company import Site
from app.helpers.company_helpers import CompanyHelpers
from app.decorators.admin_decorator import admin_required
from app.decorators.hr_decorator import hr_required
from app.helpers.auxillary import Auxillary
from flask import current_app
import binascii
from app.routes.token.token_manager import TokenManager
from .forms import UpdatePasswordForm, RegisterUserForm, RegisterCompanyUserForm, UpdateCompanyUserForm, AccountTransferForm
from flask import jsonify
from app.models.company import Departments
from app.utils.db_connection import DatabaseConnection
from datetime import datetime
import os
import requests
from app.decorators.role_decorator import role_required
from app.helpers.users_helpers import UsersHelpers
from app.api_helpers.ApiHelpers import ApiHelper

user_data_bp = Blueprint('user_data', __name__)


@user_data_bp.route('/check_user_exists', methods=['POST'])
def check_user_exists():
    """Check if a user exists."""
    data = request.get_json()
    username = data.get('username')
    email = data.get('email')
    phone = data.get('phone')
    try:
        issues = UsersHelpers.check_user_exists(username, email, phone)
        if issues:
            return jsonify({'success': False, 'issues': issues})
        else:
            return jsonify({'success': True})
    except Exception as e:
        logging.error(f"Error checking user existence: {e}")
        return jsonify({'error': 'something went wrong'})


@user_data_bp.route('/register_user', methods=['GET', 'POST'])
def register():
    form = SignupForm()
    # Fetch the reCAPTCHA public and private keys
    captcha_public_key = os.getenv('RECAPTCHA_PUBLIC_KEY')
    captcha_private_key = os.getenv('RECAPTCHA_PRIVATE_KEY')
    print("captcha_public_key: ", captcha_public_key)
    print("captcha_private_key: ", captcha_private_key)
    if request.method == 'POST':
        if not form.validate_on_submit():
            # show the validation errors raised
            for field, errors in form.errors.items():
                for error in errors:
                    flash(f'{field}: {error}', 'danger')
            message = 'Validation error'
            current_app.logger.error(message)
            flash("Validation error", 'danger')
            return redirect(url_for('user_data.register'))
        # verify reCaptcha
        try:
            if captcha_public_key and captcha_private_key:
                # Get the reCAPTCHA response from the form
                recaptcha_response = request.form.get('g-recaptcha-response')
                print(f"reCAPTCHA Response: {recaptcha_response}")
                # Perform reCAPTCHA verification
                verification_response = requests.post(
                    'https://www.google.com/recaptcha/api/siteverify',
                    data={
                        'secret': captcha_private_key,
                        'response': recaptcha_response
                    }
                )
                result = verification_response.json()
                print(f"reCAPTCHA Verification Result: {result}")
                if not result.get('success'):
                    flash("reCAPTCHA verification failed", 'danger')
                    current_app.logger.error("reCAPTCHA verification failed")
                    return redirect(url_for('user_data.register'))
        except Exception as e:
            flash("An error occurred", 'danger')
            current_app.logger.error(f"Error verifying reCAPTCHA: {str(e)}")
        
        username = form.username.data.strip()
        email = form.email.data.strip()
        password = form.password.data.strip()
        phone_number = form.phone_number.data.strip()
        first_name = form.first_name.data.strip()
        last_name = form.last_name.data.strip()

        if any([User.check_user_existence('username', username),
                User.check_user_existence('email', email),
                User.check_user_existence('phone_number', phone_number)]):
            return redirect(url_for('user_data.register'))

        try:
            role = 'hr'
            register = User.register_user(
                username, email, role, password, phone_number, first_name, last_name)
            session['username'] = form.username.data
            if register:
                token_manager = TokenManager()
                token = token_manager.generate_confirmation_token(email)
                confirm_url = url_for(
                    'token.confirm_email', token=token, _external=True)
                body = f"""<h3>Welcome {first_name},</h3>
                        <br>Please confirm your email by clicking on the following link: {confirm_url}"""
                subject = "Confirm your email"
                sent = Auxillary.send_netpipo_email(subject, email, body)
                flash('Please check your email to confirm your account. If the email is not in your inbox, kindly check your spam folder.', 'success')
                return redirect(url_for('user_data.login'))
            return redirect(url_for('user_data.login'))
        except Exception as e:
            message = f"Registration failed with error: {str(e)}"
            current_app.logger.error(message)
            flash("registration failed", 'danger')
            return redirect(url_for('user_data.register'))
    try:
        return render_template('user_auth/register.html', form=form, captcha_public_key=captcha_public_key)
    except Exception as e:
        message = f"Error rendering the register.html template: {str(e)}"
        current_app.logger.error(message)
        flash("An error occurred", 'danger')
        return redirect(url_for('user_data.login'))


@user_data_bp.route('/update_user/<uuid:id>', methods=['GET', 'POST'])
@admin_required
def update_user(id):
    user = User.query.get_or_404(id)
    form = UpdateUserForm(obj=user)
    if request.method == 'GET':
        # Ensure the password field is not pre-filled with the current password
        # We don't want the admin to see the current password.
        form.password.data = ''

    if form.validate_on_submit():
        print("Form Data:", form.data)
        try:
            user.username = form.username.data.strip()
            user.email = form.email.data.strip()
            user.role = form.role.data.strip()
            # Update password only if it's provided
            if form.password.data:
                user.password = form.password.data
            user.phone_number = form.phone_number.data
            try:
                if User.update_user(id, user.username, user.email, user.role, user.password, user.phone_number):
                    flash('User updated successfully', 'success')
                    return redirect(url_for('user_data.view_users'))
                else:
                    flash('User not updated', 'danger')
                    return redirect(url_for('user_data.view_users'))
            except Exception as e:
                current_app.logger.error(f"Error updating user: {str(e)}")
                flash('An error occurred', 'danger')
                return redirect(url_for('user_data.view_users'))
        except Exception as e:
            current_app.logger.error(f"Error updating user: {str(e)}")
            flash('An error occurred', 'danger')
            return redirect(url_for('user_data.view_users'))

    return render_template('user_auth/update_user.html', form=form, user=user)


@user_data_bp.route('/update_profile', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def update_profile():
    user_id = session.get('user_id')
    user = User.query.get_or_404(user_id)
    form = UserProfileForm(obj=user)

    if request.method == 'POST':
        if not form.validate_on_submit():
            # show the validation errors raised
            for field, errors in form.errors.items():
                for error in errors:
                    flash(f'{field}: {error}', 'danger')
            message = 'Validation error'
            current_app.logger.error(message)
            return jsonify({'success': False, 'message': 'Validation error'}), 400

        data = request.form
        user.username = data.get('username').strip()
        user.email = data.get('email').strip()
        user.phone_number = data.get('phone_number').strip()
        first_name = data.get('first_name').strip()
        last_name = data.get('last_name').strip()

        # Update the information in the session.
        session['username'] = user.username
        session['email'] = user.email
        session['first_name'] = first_name
        session['last_name'] = last_name
        try:
            if User.update_profile(user_id, user.username, user.email, user.phone_number, first_name, last_name):
                flash('Profile updated successfully', 'success')
                # Clear the session after successful update
                session.pop('updating_profile', None)
                return jsonify({'success': True, 'message': 'Profile updated successfully'}), 200
            else:
                flash('Profile not updated', 'danger')
                current_app.logger.error("Profile not updated.")
                return jsonify({'success': False, 'message': 'Profile not updated'}), 400
        except Exception as e:
            flash('An error occurred', 'danger')
            current_app.logger.error(f"Error updating profile: {str(e)}")
            return jsonify({'success': False, 'message': 'An error occurred'}), 500
    try:
        return render_template('user_auth/update_profile.html', form=form, user=user)
    except Exception as e:
        current_app.logger.error(
            f"Error rendering the update_profile.html template: {str(e)}")
        flash('An error occurred', 'danger')
        return jsonify({'success': False, 'message': 'An error occurred'}), 500


@user_data_bp.route('/update_password', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def update_password():
    user_id = session.get('user_id')
    user = User.query.get_or_404(user_id)
    form = UpdatePasswordForm()

    if request.method == 'POST':
        if not form.validate_on_submit():
            # show the validation errors raised
            error_messages = []
            for field, errors in form.errors.items():
                error_messages.extend(errors)
            if error_messages:
                return jsonify({'success': False, 'messages': error_messages}), 400
            message = 'Validation error'
            current_app.logger.error(message)
            return jsonify({'success': False, 'message': 'Validation error'}), 400
        data = request.form
        old_password = data.get('old_password').strip()
        new_password = data.get('new_password').strip()
        confirm_password = data.get('confirm_password').strip()

        try:
            # Check if the old password is correct
            checked = user.check_old_password(user_id, old_password)
        except Exception as e:
            flash('An error occurred', 'danger')
            current_app.logger.error(f"Error checking old password: {str(e)}")
            return jsonify({'success': False, 'message': 'An error occurred'}), 500

        if not checked:
            flash('Old password is incorrect', 'danger')
            current_app.logger.error("Old password is incorrect.")
            return jsonify({'success': False, 'message': 'Old password is incorrect', 'category': 'danger'}), 400

        if new_password != confirm_password:
            flash('Passwords do not match', 'danger')
            current_app.logger.error("Passwords do not match.")
            return jsonify({'success': False, 'message': 'Passwords do not match', 'category': 'danger'}), 400

        user.password = new_password

        try:
            email = user.email
            if User.reset_password(email, new_password):
                category = 'success'
                flash('Password updated successfully', 'success')
                return jsonify({'success': True, 'message': 'Password updated successfully', 'category': category}), 200
            else:
                flash('Password not updated', 'danger')
                current_app.logger.error("Password not updated.")
        except Exception as e:
            flash('An error occurred', 'danger')
            current_app.logger.error(f"Error updating password: {str(e)}")
            return jsonify({'success': False, 'message': 'An error occurred'}), 500
    try:
        return render_template('user_auth/update_password.html', form=form, user=user)
    except Exception as e:
        current_app.logger.error(
            f"Error rendering the update_password.html template: {str(e)}")
        flash('An error occurred', 'danger')
        return jsonify({'success': False, 'message': 'An error occurred'}), 500


@user_data_bp.route('/view_users', methods=['GET'])
@admin_required
def view_users():
    """View all users."""
    try:
        users = User.get_users()
        print("Users:", users)
        try:
            for user in users:
                first_name = user.first_name
                last_name = user.last_name
                company_name = user.company_name

        except Exception as e:
            print("Error getting first name and last name:", str(e))

        return render_template('user_auth/view_users.html', users=users)
    except Exception as e:
        current_app.logger.error(f"Error getting users: {str(e)}")
        flash('An error occurred', 'danger')
        return redirect(url_for('admin_data.dashboard'))


@user_data_bp.route('/login_user', methods=['GET', 'POST'])
def login():
    current_app.logger.info("User is trying to login")
    form = LoginForm()
    if form.validate_on_submit():
        username = form.username.data.strip()
        password = form.password.data.strip()

        try:
            current_app.logger.info("Trying to get user information")
            user = User.get_user_by_username(username)
        except Exception as e:
            current_app.logger.error(f"error while getting user: {str(e)}")
            flash('Invalid username or password', 'danger')
            return redirect(url_for('user_data.login'))
        current_app.logger.info(f"User fetched: {user}")
        if user is None:
            flash('Invalid username or password', 'danger')
            current_app.logger.error("Invalid username or password.")
            return redirect(url_for('user_data.login'))
        print("Username: ", username)
        print("User Email: ", user.email)
        
        role = user.role
        # Initialize session variables
        session['role'] = user.role
        session['username'] = username
        session['first_name'] = user.first_name
        session['last_name'] = user.last_name

        # Set the session as permanent
        session.permanent = True
        # Initialize the last_activity timestamp
        session['last_activity'] = datetime.now().isoformat()
        check = user.login_user(username, password)
        # Verify user credentials
        if user.login_user(username, password):
            # Check if the user is active
            if not user.is_active:
                flash("Please confirm your email to activate your account", 'danger')
                return redirect(url_for('user_data.login'))

            # Check if user is an agency user
            if user.role == 'agency':
                # Set necessary session variables for agency users
                session['email'] = user.email
                session['user_id'] = user.user_id

                # Check OTP setup
                if user.otp_key is None:
                    flash("Please setup OTP", 'info')
                    return redirect(url_for('user_data.setup_otp'))
                else:
                    try:
                        return redirect(url_for('agency.agency_dashboard'))
                    except Exception as e:
                        current_app.logger.error(
                            f"Error redirecting to agency dashboard: {str(e)}")
                        flash("An error occurred", 'danger')
                        return redirect(url_for('user_data.login'))

            # Check the user's association with companies
            if user.companies:
                if user.role in ['hr', 'accountant', 'manager', 'company_hr']:
                    
                    
                    try:
                        companies = Company.get_companies_for_user(user.user_id)
                        current_app.logger.info(f"Companies for user {user.user_id}: {companies}")
                    except Exception as e:
                        current_app.logger.error(
                            f"Error fetching companies for user {user.user_id}: {str(e)}")
                        flash("An error occurred while fetching companies", 'danger')
                        return redirect(url_for('user_data.login'))
                    # The user is associated with at least one company
                    # Retrieve the first associated company
                    company = user.companies[0]
                    company_id = company.company_id
                    session['company_id'] = company.company_id
                    session['company_name'] = company.company_name
                    session['database_name'] = company.database_name
                    session['company_plan_id'] = company.plan_id
                    session['company_plan'] = company.plan.plan_name
                    session['plan_price'] = company.plan.price
                    session['email'] = user.email
                    session['user_id'] = user.user_id
                    session['company'] = company
                    session['has_access'] = company.to_dict()['has_access']
                    # Add company logo to session
                    session['company_logo'] = company.logo
                    session['companies'] = companies
                    # Check OTP setup
                    if user.otp_key is None:
                        flash("Please setup OTP", 'info')
                        return redirect(url_for('user_data.setup_otp'))
                    else:
                        # return redirect(url_for('user_data.verify_otp'))
                        try:
                            return redirect(url_for('admin_data.dashboard'))
                        except Exception as e:
                            current_app.logger.error(
                                f"Error redirecting to dashboard: {str(e)}")
                            flash("An error occurred", 'danger')
                            return redirect(url_for('user_data.login'))

            else:
                # User is not associated with any company
                if user.role == 'hr':
                    flash('Account created successfully', 'success')
                    flash('Please register your company', 'info')
                    return redirect(url_for('company_data.register'))
                elif user.role == 'admin':
                    # Set necessary session variables for admin users
                    session['email'] = user.email
                    session['user_id'] = user.user_id
                    current_app.logger.info(f"Admin user logged in with user_id: {user.user_id}")
                    return redirect(url_for('admin_data.admin_dashboard'))
        else:
            flash('Invalid login credentials', 'danger')
            return redirect(url_for('user_data.login'))

    return render_template('user_auth/login.html', form=form)


@user_data_bp.route('/logout_user', methods=['GET'])
def logout():
    """Logout a user."""
    session.clear()
    flash("Logged out successfully", 'success')
    return redirect(url_for('user_data.login'))


@user_data_bp.route('/setup_otp', methods=['GET'])
def setup_otp():
    """Setup OTP for a user."""
    try:
        user = User.query.filter_by(username=session.get('username')).first()
        email = user.email
        message = f"User exists and his/her email is {email}"
    except Exception as e:
        message = f"User does not exist. error: {str(e)}"
        flash("Oops! The user does not exist", 'danger')
        current_app.logger.error(message)
        return redirect(url_for('user_data.login'))
    if user:
        # Generate QR code for OTP setup
        otp_key, qr_image = Auxillary.generate_totp_qr(email)

        # Save the OTP key in the database
        saved = User.update_otp_key(otp_key, email)
        message = f"otp_key and qr image generated and saved with a message : {saved}"
        if saved:
            flash("OTP setup successful", 'success')
            # Display the QR code image
            return render_template('user_auth/setup_otp.html', qr_image=qr_image, email=email)
        else:
            flash("OTP setup failed", 'danger')
            current_app.logger.error(
                "Error with generating and and saving of otp info.")
            return redirect(url_for('user_data.login'))
    else:
        flash("User does not exist", 'danger')
        current_app.logger.error("The user does not exist.")
        return redirect(url_for('user_data.login'))


@user_data_bp.route('/verify_otp', methods=['POST', 'GET'])
def verify_otp():
    """Verify OTP for a user."""
    form = VerifyOtpForm()

    if form.validate_on_submit():
        try:
            otp = form.otp.data
        except Exception as e:
            message = f"Error getting otp from the form: {str(e)}"
            flash("An error occurred", 'danger')
            current_app.logger.error(message)
            return redirect(url_for('user_data.login'))
        try:
            email = session.get('email')
            message = f"email retrieved from session:  {email}"
            user = User.get_user_by_email(email)
            message = f"user {user}"
        except Exception as e:
            message = f"an error occured during retiaval of user from the database. error: {str(e)}"
            flash("An error occurred", 'danger')
            current_app.logger.error(message)
            return redirect(url_for('user_data.login'))
        if user:
            try:
                verified = Auxillary.verify_totp(otp, user.otp_key)
                message = f"message for verification of OTP: {verified}"
            except binascii.Error as e:
                current_app.logger.error(
                    f"OTP Key error: {user.otp_key} is not a valid base32 string")
                flash("An error occurred with OTP verification", 'danger')
                return redirect(url_for('user_data.login'))

            if verified:
                # point the user to the right dashboard
                if user.role == 'admin':
                    flash("OTP verification successful", 'success')
                    return redirect(url_for('admin_data.admin_dashboard'))
                elif user.role == 'agency':
                    flash("OTP verification successful", 'success')
                    # Check if the user is trying to update the profile
                    updating_profile = session.get('updating_profile')
                    if updating_profile == "profile updating":
                        # Set the session key to user authenticated
                        session['updating_profile'] = "user authenticated"
                        return redirect(url_for('user_data.update_profile'))
                    try:
                        return redirect(url_for('agency.agency_dashboard'))
                    except Exception as e:
                        current_app.logger.error(
                            f"Error redirecting to agency dashboard: {str(e)}")
                        flash("An error occurred", 'danger')
                        return redirect(url_for('user_data.login'))
                elif user.role in ['hr', 'accountant', 'manager', 'company_hr']:
                    flash("OTP verification successful", 'success')
                    # Check if the user is trying to update the profile
                    updating_profile = session.get('updating_profile')
                    if updating_profile == "profile updating":
                        # Set the session key to user authenticated
                        session['updating_profile'] = "user authenticated"
                        return redirect(url_for('user_data.update_profile'))
                    try:
                        return redirect(url_for('admin_data.dashboard'))
                    except Exception as e:
                        current_app.logger.error(
                            f"Error redirecting to hr dashboard: {str(e)}")
                        flash("An error occurred", 'danger')
                        return redirect(url_for('user_data.login'))
            else:
                flash("OTP verification failed", 'danger')
                current_app.logger.error("OTP Verification failed. ")
                return redirect(url_for('user_data.verify_otp'))
        else:
            flash("User does not exist", 'danger')
            current_app.logger.error("User does not exist.")
            return redirect(url_for('user_data.login'))
    return render_template('user_auth/verify_otp.html', form=form)


@user_data_bp.route('/reset_password', methods=['GET', 'POST'])
def reset_password():
    """Reset a user's password."""
    title = "Reset Password"
    form = ResetPasswordForm()
    if form.validate_on_submit():
        email = form.email.data.strip()
        try:
            user = User.get_user_by_email(email)
        except Exception as e:
            current_app.logger.error(
                f'Exception occured while retrieving user info: {str(e)}')
            flash(f"The user with the provided email: {email} does not exist.")
            return redirect(url_for('user_data.reset_password'))

        if user:
            temporary_password = Auxillary.random_password()
            updated = user.reset_password(email, temporary_password)
            if not updated:
                flash("Password reset failed", 'danger')
                current_app.logger.error("Password reset failed.")
                return redirect(url_for('user_data.login'))

            recipients = email
            body = f"""
            <h3>Hi {user.last_name},</h3>
            <p>Your password has been reset. Your temporary password is: <strong> {temporary_password}</strong></p>"""
            subject = "Password Reset Notification"
            try:
                Auxillary.send_netpipo_email(subject, recipients, body)
                flash("Password reset successful. A temporary password has been sent to your email. If the email is not in your inbox, kindly check your spam folder", 'success')
                return redirect(url_for('user_data.login'))
            except Exception as e:
                flash("An error occurred", 'danger')
                current_app.logger.error(f"Error sending email: {str(e)}")
                return redirect(url_for('user_data.login'))
        else:
            flash("User does not exist", 'danger')
            current_app.logger.error("User does not exist.")
            return redirect(url_for('user_data.login'))
    return render_template('user_auth/reset_password.html', form=form, title=title)


@user_data_bp.route('/add_user', methods=['GET', 'POST'])
@admin_required
def add_user():
    """Add a user."""
    form = RegisterUserForm()
    if form.validate_on_submit():
        username = form.username.data.strip()
        email = form.email.data.strip()
        password = form.password.data.strip()
        phone_number = form.phone_number.data.strip()
        first_name = form.first_name.data.strip()
        last_name = form.last_name.data.strip()
        role = form.role.data.strip()
        # Check if the user already exists
        if any([User.check_user_existence('username', username),
                User.check_user_existence('email', email),
                User.check_user_existence('phone_number', phone_number)]):
            flash('User already exists', 'danger')
            current_app.logger.error("User already exists.")
            return redirect(url_for('user_data.add_user'))
        try:
            user = User.register_user(
                username, email, role, password, phone_number, first_name, last_name)
            if user:
                flash('User added successfully', 'success')
                return redirect(url_for('user_data.view_users'))
            else:
                flash('User not added', 'danger')
                current_app.logger.error("User not added.")
                return redirect(url_for('user_data.add_user'))
        except Exception as e:
            flash('An error occurred', 'danger')
            current_app.logger.error(f"Error adding user: {str(e)}")
            return redirect(url_for('user_data.add_user'))
    return render_template('user_auth/add_user.html', form=form)

# settings route


@user_data_bp.route('/settings', methods=['GET'])
def settings():
    """Fetch user details"""
    user_id = session.get('user_id')
    attendance_service = session.get('attendance_service')
    print("attendance_service: ", attendance_service)
    if user_id is None:
        flash("Please login to access this page", 'danger')
        return redirect(url_for('user_data.login'))
    user = User.query.get_or_404(user_id)
    """fetch company details"""
    company = Company.query.get_or_404(session.get('company_id'))
    """Fetch departments"""
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error(
            "An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))

    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            departments = Departments.get_departments(db_session)
            print("departments: ", departments)
            if not departments:
                # flash('No departments found!', 'danger')
                current_app.logger.error('No departments found!')
                departments = []
        except Exception as e:
            flash('An error occurred while fetching departments!', 'danger')
            current_app.logger.error(
                f'An error occurred while fetching departments: {str(e)}')
        try:
            sites = Site.get_sites(db_session)
            if not sites:
                # flash('No sites found!', 'danger')
                current_app.logger.error('No sites found!')
                sites = []
        except Exception as e:
            current_app.logger.error(
                'Error rendering view_locations.html: %s', e)
            return jsonify({'message': 'Error rendering view_locations.html'}),

    role = session.get('role')

    return render_template('user_auth/settings.html', company=company,
                           user=user, departments=departments,
                           attendance_service=attendance_service,
                           sites=sites, role=role)


@user_data_bp.route('/register_company_users', methods=['GET', 'POST'])
@role_required('hr')
def register_company_users():
    """Register users for a company."""
    form = RegisterCompanyUserForm()

    # Retrieve the user_id from the session
    user_id = session.get('user_id')

    try:
        # get the companies associated with the user
        companies = Company.get_companies_for_user(user_id)
        if not companies:
            current_app.logger.error('No companies found!')
            companies = []
    except Exception as e:
        current_app.logger.error('Error fetching companies: %s', e)
        companies = []

    # Populate the company choices
    form.company.choices = [
        (company.company_id, company.company_name) for company in companies]

    try:
        # Retrieve available roles from the database
        roles = UserRole.get_user_roles()
        if not roles:
            current_app.logger.error('No roles found!')
            roles = []
    except Exception as e:
        current_app.logger.error('Error fetching roles: %s', e)
        roles = []

    try:
        # Populate the role choices and restrict 'admin', employee, and supervisor roles from being populated
        form.role.choices = [(role['role_name'], role['role_name']) for role in roles if role['role_name'] not in [
            'admin', 'employee', 'supervisor', 'hr']]
    except Exception as e:
        current_app.logger.error('Error populating role choices: %s', e)
        form.role.choices = []

    if form.validate_on_submit():
        email = form.email.data.strip()
        phone_number = form.phone.data.strip()
        first_name = form.first_name.data.strip()
        last_name = form.last_name.data.strip()
        role = form.role.data.strip()
        company = form.company.data
        username = email
        password = Auxillary.random_password()

        try:
            # check if the user with the provided email exists
            existing_user = User.get_user_by_email(email)
            if existing_user is not None:
                current_app.logger.error(
                    "User with the provided email already exists.")
                existing_user_id = existing_user.user_id
                # get the company_id of the selected company
                company_id = form.company.data
                # asign the user to the selected company
                try:
                    asigned = User.assign_user_a_company(
                        existing_user_id, company_id)
                    if asigned:
                        flash('User added successfully', 'success')
                        # send an email to the user telling them they have been added to the company
                        # We do not need to send them a token or a random passowrd. They will login with their
                        # existing credentials
                        company_name = Company.get_company_by_id(company_id)
                        name = company_name['company_name']
                        body = f"""
                        <h3>Hello {existing_user.to_dict()['full_name']},</h3>
                        <p>You have been added to netpipo.com to access {name} data </p>
                        <p>Please login as usual </p>
                        """
                        subject = f"User Added to {name}"
                        try:
                            sent = Auxillary.send_netpipo_email(subject, email, body)
                        except Exception as e:
                            current_app.logger.error(
                                f"Error sending email: {str(e)}")
                        return jsonify({'success': True, 'message': 'User added successfully'}), 200
                    else:
                        current_app.logger.error(
                            "User not assigned to the company.")
                        flash('User not added', 'danger')
                        return jsonify({'success': False, 'message': 'User not added'}), 400
                except Exception as e:
                    current_app.logger.error(
                        f"Error assigning user to the company: {str(e)}")
                    flash('An error occurred', 'danger')
                    return jsonify({'success': False, 'message': 'An error occurred'}), 400

            user = User.register_company_user(
                username, email, role, password, phone_number, first_name, last_name, company)
            if user:
                token_manager = TokenManager()
                token = token_manager.generate_confirmation_token(email)
                confirm_url = url_for(
                    'token.confirm_email', token=token, _external=True)
                company_name = Company.get_company_by_id(company)
                name = company_name['company_name']
                body = f"""
                <h3>Hello {first_name},</h3>
                <p>Welcome to the {name} platform. Your account has been created successfully.</p>
                <p>Your login credentials are:</p>
                <p>Username: <strong>{username}</strong></p>
                <p>Password: <strong>{password}</strong></p>
                <p>Please confirm your email by clicking on the following link: {confirm_url}</p>
                """
                subject = "Account Confirmation"
                try:
                    sent = Auxillary.send_netpipo_email(subject, email, body)
                except Exception as e:
                    current_app.logger.error(f"Error sending email: {str(e)}")
                    flash('An error occurred', 'danger')
                    return redirect(url_for('user_data.register_company_users'))
                flash('User added successfully', 'success')
                return jsonify({'success': True, 'message': 'User added successfully'}), 200
            else:
                flash('User not added', 'danger')
                current_app.logger.error("User not added.")
                return redirect(url_for('user_data.register_company_users'))
        except Exception as e:
            flash('An error occurred', 'danger')
            current_app.logger.error(f"Error adding user: {str(e)}")
            return redirect(url_for('user_data.register_company_users'))
    try:
        return render_template('user_auth/register_company_users.html', form=form)
    except Exception as e:
        current_app.logger.error(
            f"Error rendering company user registration: {str(e)}")
        return jsonify({'message': 'An error occurred'}), 500


@user_data_bp.route('/view_company_users', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def view_company_users():
    """View all users associated with a company."""
    company_id = session.get('company_id')
    company_name = session.get('company_name')
    try:
        users = User.get_company_users(company_id)
        return render_template('user_auth/view_company_users.html', users=users, company_name=company_name)
    except Exception as e:
        current_app.logger.error(f"Error getting users: {str(e)}")
        flash('An error occurred', 'danger')
        return redirect(url_for('user_data.settings'))


@user_data_bp.route('/update_company_user/<uuid:id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def update_company_user(id):
    """Update a company user."""
    user = User.get_user_by_id(id)
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('user_data.view_company_users'))

    # Initialize the form with user data
    form = UpdateCompanyUserForm(obj=user)

    try:
        # Fetch companies associated with the logged-in user
        user_id = session.get('user_id')
        companies = Company.get_companies_for_user(user_id)
        form.company.choices = [
            (company.company_id, company.company_name) for company in companies]
        if not companies:
            current_app.logger.error("No companies found for the user.")
    except Exception as e:
        current_app.logger.error(f"Error fetching companies: {e}")
        form.company.choices = []

    try:
        # Fetch user roles, excluding specific ones
        roles = UserRole.get_user_roles()
        form.role.choices = [
            (role['role_name'], role['role_name'])
            for role in roles if role['role_name'] not in ['admin', 'employee', 'supervisor', 'hr']
        ]
        if not roles:
            current_app.logger.error("No roles found in the database.")
    except Exception as e:
        current_app.logger.error(f"Error fetching roles: {e}")
        form.role.choices = []

    if request.method == 'POST':
        # Check if form validation fails
        if not form.validate_on_submit():
            current_app.logger.error("Form validation failed.")

            # Initialize an empty list of errors
            my_errors = []
            # Log validation errors for each field
            for field, errors in form.errors.items():
                for error in errors:
                    my_errors.append(f"{field}: {error}")
            message = f"Validation error: {my_errors}"
            current_app.logger.error(message)
            return jsonify({'success': False, 'message': 'Validation error', 'errors': my_errors}), 400

        # Handle successful form submission
        try:
            username = form.username.data.strip()
            email = form.email.data.strip()
            role = form.role.data.strip()
            phone = form.phone_number.data.strip()
            company = form.company.data
            first_name = form.first_name.data.strip()
            last_name = form.last_name.data.strip()
            # Update the user in the database
            if User.update_company_user(id, username, email, role, phone, company, first_name, last_name):
                flash('User updated successfully', 'success')
                return jsonify({'success': True, 'message': 'User updated successfully'}), 200
            else:
                flash('User update failed. Please try again.', 'danger')
                return jsonify({'success': False, 'message': 'User update failed. Please try again.'}), 400
        except Exception as e:
            current_app.logger.error(f"Error updating user: {str(e)}")
            flash('An error occurred while updating the user.', 'danger')
            return jsonify({'success': False, 'message': 'An error occurred while updating the user.'}), 500

    # Render the update form
    return render_template('user_auth/update_company_user.html', form=form, user=user)


@user_data_bp.route('/transfer_account', methods=['GET', 'POST'])
@role_required(['hr'])
def transfer_account_ownership():
    from flask_wtf.csrf import generate_csrf
    csrf_token = generate_csrf()
    transfer_form = AccountTransferForm()
    # Get user id
    user_id = session.get("user_id")
    current_user = User.get_user_by_id(user_id)

    try:
        # get the companies associated with the user
        companies = Company.get_companies_for_user(user_id)
        if not companies:
            session.clear()
        if not companies:
            current_app.logger.error('No companies found!')
            companies = []
    except Exception as e:
        current_app.logger.error('Error fetching companies: %s', e)
        companies = []
    # Populate the company choices
    transfer_form.company.choices = [
        (company.company_id, company.company_name) for company in companies]
    try:
        if request.method == "POST":
            print("FOOORRRM: ", request.form)
            if request.form.get('current_form') == "initial-step-form":
                # Process register
                email = request.form['email'].strip()
                phone_number = request.form['phone'].strip()
                first_name = request.form['first_name'].strip()
                last_name = request.form['last_name'].strip()
                role = current_user.get("role")  # For exchanging roles
                # company = register_form.company.data
                username = email
                password = Auxillary.random_password()

                try:
                    # Check user existance, to not create user
                    existing_user = User.get_user_by_their_email(email)
                    if existing_user:
                        if str(current_user.get("user_id")) == str(existing_user.user_id):
                            return jsonify(success=False, error="You can't transfer ownership to yourself."), 400
                        current_app.logger.error("User with the provided email already exists.")
                        encoded_email = ApiHelper.encode_string_base64(email)
                        response=jsonify(success=True,
                                    data={"first_name": existing_user.first_name, "last_name": existing_user.last_name, "email": existing_user.email},
                                    message="User already exists, Contiue to next steps")
                        response.set_cookie('xxx-to-xxx', encoded_email)
                        return response, 200

                    # Creating new user with migrator role
                    user = User.register_user(username, email, role, password, phone_number,
                                                first_name, last_name, is_active=False)
                    if not user:
                        current_app.logger.error("User not added.")
                        return jsonify(success=False, error="User not added."), 500
                    
                    token_manager = TokenManager()
                    token = token_manager.generate_confirmation_token(
                        email)
                    confirm_url = url_for(
                        'token.confirm_email', token=token, _external=True)
                    body = f"""
                        <h3>Hello {first_name},</h3>
                        <p>Welcome to the NetPipo platform. Your account has been created successfully.</p>
                        <p>Your login credentials are:</p>
                        <p>Username: <strong>{username}</strong></p>
                        <p>Password: <strong>{password}</strong></p>
                        <p>Please confirm your email by clicking on the following link: {confirm_url}</p>
                    """
                    subject = "Account Confirmation"

                    try:
                        _ = Auxillary.send_netpipo_email(subject, email, body)
                        encoded_email = ApiHelper.encode_string_base64(email)

                        response=jsonify(success=True,
                                    data={"first_name": first_name, "last_name": last_name, "email": email},
                                    message="User added successfully")
                        response.set_cookie('xxx-to-xxx', encoded_email)
                        return response, 200
                    except Exception as e:
                        current_app.logger.error(
                            f"Error sending email: {str(e)}")
                        return jsonify(success=False, error="Error sending email"), 500
                        
                except Exception as e:
                    current_app.logger.error(f"Error adding user: {str(e)}")
                    return jsonify(success=False, error="Error adding user"), 500

            else:
                # Process for transfer
                receiver_email = ApiHelper.decode_string_base64(request.cookies.get(('xxx-to-xxx')))
                receiver = User.get_user_by_email(receiver_email)
                transferable_company_id = transfer_form.company.data
                if not transferable_company_id:
                    current_app.logger.error("Company not selected.")
                    return jsonify(success=False, error="Company not selected."), 400

                if not receiver:
                    current_app.logger.error("Receiver user not found.")
                    return jsonify(success=False, error="Receiver user not found."), 404

                try:
                    # If it happens protect user from messing up with transfering account when he already
                    # Owns it
                    for company in receiver.companies:
                        if str(transferable_company_id) == str(company.company_id):
                            return jsonify(success=False, error="User already belongs to the company."), 400
                        
                    assigned = User.assign_user_a_company(
                        receiver.user_id, transferable_company_id)
                    if not assigned:
                        current_app.logger.error(
                            "User not assigned to the company.")
                        flash('User not added', 'danger')
                        return redirect(url_for("user_data.transfer_account_ownership"))
                    
                    # Transfer account
                    _ = User.update_user_role(
                        receiver.user_id, current_user.get("role"))

                    _ = User.remove_company_from_user(
                        current_user.get("user_id"), transferable_company_id)

                    # send an email to the user telling them they have been added to the company
                    # We do not need to send them a token or a random passowrd. They will login with their
                    # existing credentials
                    company_name = Company.get_company_by_id(
                        transferable_company_id)
                    name = company_name['company_name']
                    login_url = 'https://netpipo.com/login_user'
                    body = f"""
                        <h2>Congratulations, {receiver.to_dict()['full_name']}!</h2>
                        <p style="font-size:16px;">
                            You have been granted ownership access to <strong>{name}</strong> on <a href="https://netpipo.com" target="_blank">Netpipo</a>.
                        </p>
                        <p>
                            You can now manage your company's data and enjoy all the features available to account owners.
                        </p>
                        <p>
                            <b>To get started, please log in here:</b> <a href="{login_url}" target="_blank">{login_url}</a>
                        </p>
                        <hr>
                    """
                    subject = f"You were Added to {name}"
                    try:
                        _ = Auxillary.send_netpipo_email(subject, receiver_email, body)
                        # Return user to login after successful transfer
                        session.clear()
                        flash('Company transfered successfully', 'success')

                        return redirect(url_for('admin_data.dashboard'))
                    except Exception as e:
                        current_app.logger.error(
                            f"Error sending email: {str(e)}")
                        
                except Exception as e:
                    # Rollback account when any error failes during transfering account
                    _ = User.remove_company_from_user(receiver.user_id, transferable_company_id)
                    _ = User.assign_user_a_company(current_user.user_id, transferable_company_id)
                    current_app.logger.error(f"Error assigning user to the company: {str(e)}")
                    flash('An error occurred', 'danger')
                    response = redirect(url_for("user_data.transfer_account_ownership"))
                    response.delete_cookie("User")
                    return response
        return render_template("user_auth/transfer_account_ownership.html",
                           csrf_token=csrf_token, transfer_form=transfer_form,
                           company=transfer_form.company.choices[0][0])
    except Exception as e:
        current_app.logger.error(f"Error rendering template: {str(e)}")
        return jsonify(success=False, error="Error rendering template"), 500
    
