"""This module contains the form classes for the user blueprint."""
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SubmitField, IntegerField, DecimalField, EmailField, SelectField
from wtforms.validators import DataRequired,  EqualTo, Length, Email, Optional

from wtforms.validators import ValidationError
from flask import flash


def validate_phone(form, field):
    # Ensure that the field contains only digits and is exactly 10 characters long
    if not field.data.isdigit() or len(field.data) != 10:
        flash('Phone number must be exactly 10 digits long and contain only numbers.', 'danger')
        raise ValidationError(
            'Phone number must be exactly 10 digits long and contain only numbers.')


class SignupForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    first_name = StringField('First Name')
    last_name = StringField('Last Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    password = StringField('Password', validators=[
                           DataRequired(), Length(min=6, max=20)])
    confirm_password = StringField('Confirm Password', validators=[
                                   DataRequired(), EqualTo('password')])
    phone_number = StringField('Phone Number', validators=[
                               DataRequired(), validate_phone])
    submit = SubmitField('Sign Up')


class UpdateUserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    first_name = StringField('First Name')
    last_name = StringField('Last Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    password = StringField('Password', validators=[Optional()])
    role = SelectField('Role', choices=[(
        'admin', 'Admin'), ('hr', 'HR'), ('user', 'User')], validators=[DataRequired()])
    phone_number = StringField('Phone Number', validators=[DataRequired()])
    submit = SubmitField('Update')


class UserProfileForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    phone_number = StringField('Phone Number', validators=[DataRequired()])
    first_name = StringField('First Name')
    last_name = StringField('Last Name', validators=[DataRequired()])
    submit = SubmitField('Update')


class UpdatePasswordForm(FlaskForm):
    old_password = StringField('Old Password', validators=[DataRequired()])
    new_password = StringField('New Password', validators=[
                               DataRequired(), Length(min=6, max=20)])
    confirm_password = StringField('Confirm Password', validators=[
                                   DataRequired(), EqualTo('new_password', message='Passwords must match')])
    submit = SubmitField('Update Password')


class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = StringField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')


class VerifyOtpForm(FlaskForm):
    otp = StringField('OTP', validators=[DataRequired()])
    submit = SubmitField('Verify OTP')


class ResetPasswordForm(FlaskForm):
    email = EmailField('Email', validators=[DataRequired(), Email()])
    submit = SubmitField('Reset Password')


class RegisterUserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    first_name = StringField('First Name')
    last_name = StringField('Last Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    password = StringField('Password', validators=[
                           DataRequired(), Length(min=6, max=20)])
    phone_number = StringField('Phone Number', validators=[
                               DataRequired(), validate_phone])
    role = SelectField('Role', choices=[(
        'admin', 'Admin'), ('hr', 'HR'), ('user', 'User')], validators=[DataRequired()])
    submit = SubmitField('Register')


class RegisterCompanyUserForm(FlaskForm):
    first_name = StringField('First Name')
    last_name = StringField('Last Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    phone = StringField('Phone Number', validators=[
                        DataRequired(), validate_phone])
    role = SelectField('Role', choices=[], validators=[DataRequired()])
    company = SelectField('Company', choices=[], validators=[DataRequired()])
    submit = SubmitField('Register',  name="register_user_form")


class UpdateCompanyUserForm(FlaskForm):
    first_name = StringField('First Name')
    last_name = StringField('Last Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    phone_number = StringField('Phone Number', validators=[
                               DataRequired(), validate_phone])
    role = SelectField('Role', choices=[], validators=[DataRequired()])
    company = SelectField('Company', choices=[], validators=[DataRequired()])
    username = StringField('Username', validators=[DataRequired()])
    submit = SubmitField('Update')


class AccountTransferForm(FlaskForm):
    company = SelectField("Company", choices=[], validators=[DataRequired()])
    submit = SubmitField("Transfer", name="transfer_ownership_form")
