/* Import Remix Icons */
@import url('https://cdnjs.cloudflare.com/ajax/libs/remixicon/2.5.0/remixicon.min.css');

body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7f9;
    margin: 0;
    padding: 0;
    color: #333;
}

h1 {
    background-color: #007bff;
    color: #fff;
    text-align: center;
    padding: 20px;
    margin: 0;
}

h2 {
    color: #333;
    margin-top: 0;
}

.container {
    width: 80%;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

p {
    font-size: 16px;
    line-height: 1.5;
    margin: 10px 0;
}

strong {
    color: #007bff;
}

.icon {
    margin-right: 10px;
    color: #007bff;
}

.footer {
    text-align: center;
    margin-top: 20px;
    color: #777;
}

@media (max-width: 768px) {
    .container {
        width: 90%;
    }
}

@media print {
    body * {
        visibility: hidden;
    }
    .printable-area, .printable-area * {
        visibility: visible;
    }
    .printable-area {
        position: absolute;
        left: 0;
        top: 0;
    }
}