@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700&display=swap');
*{
    font-family: 'Poppins', sans-serif;
}
body {
    
    background-color: #f7f7f7;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

form {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
}

/* Heading style */
form h4 {
    margin-bottom: 20px;
    color: #333;
}

/* Form group */
form div {
    margin-bottom: 15px;
}

/* Labels */
form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

/* Inputs */
form input[type="text"],
form input[type="number"],
form input[type="email"],
form input[type="password"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

form input[type="text"]:focus,
form input[type="number"]:focus,
form input[type="email"]:focus,
form input[type="password"]:focus {
    border-color: #007bff;
}

/* Error messages */
.text-danger {
    color: #e74c3c;
    font-size: 0.875em;
}

/* Submit button */
form input[type="submit"] {
    background: #135a4d;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

form input[type="submit"]:hover {
    background: #1e967c;
}
