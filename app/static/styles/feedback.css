#feedback-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#feedback-heading {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 10px;
    color: #333;
}

#feedback-description {
    text-align: center;
    font-size: 1.2rem;
    color: #777;
}

.feedback-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-feedback {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 1rem;
    font-weight: bold;
    color: #555;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.input-icon {
    color: #888;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.message-control{
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    height: 100px;
}

.error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 5px;
}

.submit-button {
    background-color: #25a38b;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.submit-button:hover {
    background-color: #d5f7f0;
}
