/* Admin Dashboard Styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #cce8e3;
    --secondary-color: #25a38b;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #7d7d7d;
    --shadow-color: rgba(0, 255, 221, 0.1);
    --warning-color: #ff0000;
    --semi-warning-color: #fa7e7e;
    --white-color: #fff;
    --green-color: #3d963d;
    --blue-color: #1c6d99;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--font-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background-color: var(--white-color);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-container img {
    width: 40px;
    height: 40px;
}

.logo-container h1 {
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info p {
    font-weight: 500;
}

.logout-btn {
    background-color: var(--secondary-color);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.logout-btn:hover {
    background-color: #1e8a73;
}

.main-container {
    display: flex;
    flex: 1;
}

.sidebar {
    width: 250px;
    background-color: var(--white-color);
    padding: 20px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 70px);
    position: sticky;
    top: 70px;
    overflow-y: auto;
}

.content {
    flex: 1;
    padding: 20px;
}

.dashboard-container {
    background-color: var(--white-color);
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.welcome-message {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--secondary-color);
    font-weight: 600;
}

.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: var(--white-color);
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.menu-category {
    margin-bottom: 20px;
}

.category-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--mid-font-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.menu-list {
    list-style-type: none;
}

.menu-item {
    margin-bottom: 8px;
}

.menu-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
    color: var(--font-color);
    transition: all 0.3s ease;
}

.menu-link:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.menu-link i {
    font-size: 1.2rem;
    min-width: 24px;
    text-align: center;
}

.messages {
    margin-bottom: 20px;
}

.messages ul {
    list-style: none;
    padding: 0;
}

.messages li {
    padding: 12px 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.danger {
    background-color: #f8d7da;
    color: #721c24;
}

.success {
    background-color: #d4edda;
    color: #155724;
}

.danger::before {
    content: "⚠️";
}

.success::before {
    content: "✅";
}

/* Responsive styles */
@media (max-width: 992px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
    }

    .card-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 10px;
    }

    .content {
        padding: 15px;
    }

    .welcome-message {
        font-size: 1.2rem;
    }
}
