/* Container for the toggle */
.toggle-container {
    display: flex;
    align-items: center;
    gap: 10px; /* Space between elements */
    font-size: 16px;
    color: #333;
}

/* Toggle switch container */
.switch {
    position: relative;
    display: inline-block;
    width: 60px; /* Width of the toggle switch */
    height: 34px; /* Height of the toggle switch */
}

/* Hide the default checkbox */
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* Slider (the moving part of the toggle) */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc; /* Default background color */
    transition: 0.4s; /* Smooth transition */
    border-radius: 34px; /* Rounded corners */
}

/* Slider circle */
.slider:before {
    position: absolute;
    content: "";
    height: 26px; /* Size of the circle */
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white; /* Circle color */
    transition: 0.4s; /* Smooth transition */
    border-radius: 50%; /* Make it round */
}

/* When the checkbox is checked, change the background color and move the slider */
input:checked + .slider {
    background: linear-gradient(to right, #00d8bb, #018573); /* Gradient background color */
}

input:checked + .slider:before {
    transform: translateX(26px); /* Move the circle to the right */
}

/* Optional: Add a label style for the text */
#toggle-text, #toggle-text-2 {
    font-weight: bold;
}

#toggle-text-2 {
    color: #545e5a; /* Highlight color for the "SAVE 17%" text */
}