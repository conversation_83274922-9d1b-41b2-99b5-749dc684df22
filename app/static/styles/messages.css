ul.list-unstyled {
    padding: 0;
    margin: 0;
    list-style: none;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    
}

/* Flash message items */
.alert {
    padding: 15px 20px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 16px;
    max-width: 300px;
    background-color: #155724;
    color: #ffffff;
}

.flash-messages {
    position: fixed;
    top: 10px;
    right: 20px;
    z-index: 1000;
    width: 300px;
    font-family: 'Arial', sans-serif;
}

.flash-messages .messages {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.flash-messages ul {
    padding: 0;
    margin: 0;
}

.flash-messages li {
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.5s ease-in-out;

}

/* Success Messages */
.flash-messages li.success {
    background-color: #4caf50;
}

/* Error Messages */
.flash-messages li.error {
    background-color: #f44336;
}

/* Warning Messages */
.flash-messages li.warning {
    background-color: #ff9800;
}

/* Info Messages */
.flash-messages li.info {
    background-color: #2196f3;
}

/* Close Button */
button.close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    border: none;
    background-color: transparent;
    color: #fff;
    cursor: pointer;
}
/* Animation for sliding in */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}