@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
/*import Anton FONT*/
@import url('https://fonts.googleapis.com/css2?family=Anton&display=swap');
/* Style for the whole body */

.company h2{
    font-family: 'Poppins', serif;
    font-size: 2rem;
    color: #374957;
    text-align: center;
    margin-bottom: 0;
    font-weight: 600;
}
.company p{
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    color: #25a38b;
    text-align: center;
    margin-top: 0;
    background-color: #cce8e3;
    border-radius: 20px;
    padding: 10px;
    width: fit-content;
    margin: 0 auto;
}
.company h3{
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    color: #506461;
    text-align: center;
    margin-top: 0;
}
tfoot td {
    font-weight: bold;
}

.absent{
    background-color: rgb(158, 158, 158);
    color: #f9f9f9;
}
.present{
    background-color: #73bc88;
    color: #f9f9f9;
}
.off{
    background-color: #9ea6b5;
    color: #f9f9f9;
}
.leave{
    background-color: rgb(192, 181, 101);
    color: #555;
}


/* Responsive design for mobile devices */
@media (max-width: 768px) {
    table, thead, tbody, th, td, tr {
        display: block;
    }

    thead tr {
        display: none;
    }

    tr {
        margin-bottom: 15px;
    }

    td {
        text-align: right;
        padding-left: 50%;
        position: relative;
    }

}
/* Print-specific styles */
@media print {
    /* Ensures backgrounds are printed */
    * {
        -webkit-print-color-adjust: exact !important; /* For Webkit-based browsers like Chrome */
        print-color-adjust: exact !important; /* For modern browsers */
    }
    .company, .texts, .status {
        background-color: #ffffff !important; /* Ensure certain elements maintain their background */
        color: #000000 !important; /* Make sure the text is visible */
    }

    .status {
        background-color: #f9f9f9 !important; /* Set background color for attendance status cells */
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    table th, table td {
        padding: 5px 10px;
        border: 1px solid #ddd;

    }

    /* Optional: Hide elements that are not necessary for print */
    .no-print {
        display: none !important;
    }

    /* Make sure header and footer appear correctly */
    h2, p, h3 {
        text-align: center !important;
    }
}
