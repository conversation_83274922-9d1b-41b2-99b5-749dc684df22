.vid-container {
    position: relative; /* Set container position to relative */
    width: 100%; /* Full width */
    max-width: 800px; /* Adjust as needed */
    overflow: hidden; /* Hide overflow */
}

.scanner-line {
    display: none;
    position: absolute; /* Absolute positioning to overlay on video */
    top: 0; /* Start at the top */
    left: 50%; /* Center horizontally */
    transform: translateX(-50%); /* Center align by translating */
    width: 100%; /* Width of the scanner line */
    height: 10px; /* Height of the scanner line */
    background: linear-gradient(to right, rgba(0, 255, 0, 0) 0%, rgb(49, 255, 203) 50%, rgba(0, 255, 0, 0) 100%);
    box-shadow: 0 0 20px #25a38b(0, 255, 0, 0.8), 0 0 40px rgba(64, 223, 255, 0.6), 0 0 60px rgba(0, 255, 0, 0.4);
    animation: scanningMove 2s infinite alternate ease-in-out;
}

/* Animation */
@keyframes scanningMove {
    0% {
        top: 0; /* Start at the top */
    }
    100% {
        top:  /* Start at the top */100%; /* Move down by 100px */
    }
}

video {
    width: 100%; /* Make video full width of container */
    height: auto; /* Maintain aspect ratio */
    display: block; /* Remove bottom space */
}
#canvas {
    display: none;
}
.fa-check{
    background-color: rgb(6, 150, 6);
    font-size: 5rem;
    padding: 2rem;
    border-radius: 100%;
    border: 5px solid white;
    animation: check 1s ease;
}
.fa-xmark{
    background-color: rgb(179, 11, 11);
    font-size: 5rem;
    padding: 2rem;
    border-radius: 100%;
    border: 5px solid white;
    animation: check 1s ease;
}
.fa-info{
    font-size: 2rem;
    padding: 2rem;
    animation: check 1s ease;
    color: #ffffff;
    border: 2px solid white;
    border-radius: 100%;
    background-color: #000000;
}

.decision-buttons{
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap : 20px;
}
.decision-buttons a{
    padding: 10px 20px;
    border-radius: 5px;
    color: #25a38b;
    font-size: 1.2rem;
    text-decoration: none;
    border: 1px solid #25a38b;
    background-color: #ebfffb;

}
.decision-buttons a:hover{
    background-color: #25a38b;
    color: white;
}
.video-frame {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    background: linear-gradient(rgb(0, 170, 130), rgba(217, 254, 255, 0.5));
}
.video-frame h1{
    color: #ffffff;
    font-size: 2rem;
    padding: 20px;
}
#captureButton {
    color: #03c300;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1.2rem;
    border: 1px solid #03c300;
    margin: 20px 0;
    background-color: #eeffee;
}
#captureButton:hover {
    background-color: #03c300;
    color: white;
    cursor: pointer;
}

.captureButton {
    color: rgb(116, 116, 116);
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1.2rem;
    border: 1px solid #ebebeb;
    margin: 20px 0;
}
.captureButton:hover {
    background-color: #25a38b;
    color: white;
    cursor: pointer;
}
.btn-centered {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* Responsive */
@media (max-width: 768px) {
    .vid-container {
        max-width: 500px;
    }
    .video-frame h1{
        font-size: 1.5rem;
        margin: 0;
        padding: 0;
    }
    .decision-buttons a{
        padding: 10px 15px;
        font-size: 1rem;
        
    }
    .decision-buttons {
        padding: 5px;
        margin: 5px;
    }
    #captureButton {
        padding: 10px 15px;
        margin: 5px;
        font-size: 1rem;
    }
    video{
        width: 80%;
        height: auto;
        margin: 0 auto;
        border-radius: 10px;
        

    }
}