@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700&display=swap');
@import url('static/home.css');
* {
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f7f7f7;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;    
}

form {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    width: 100%;
}

/* Heading style */
form h2 {
    margin-bottom: 20px;
    color: #333;
}

/* Form row */
.form-row {
    margin-bottom: 15px;
}

/* Form group */
.form-group {
    margin-bottom: 0;
}

/* Labels */
.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}



.form-control:focus {
    border-color: #5d6166;
}

/* Error messages */
.text-danger {
    color: #e74c3c;
    font-size: 0.875em;
}

/* Submit button */
.btn-primary {
    background: var(--primary-color);
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    
}

.btn-primary:hover {
    background: var(--mid-primary-color);

}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 125%; /* Position above the tooltip element */
    left: 50%;
    margin-left: -100px; /* Center the tooltip */
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
