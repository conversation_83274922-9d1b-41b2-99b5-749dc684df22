/* General Body Styling */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    color: #333;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Blog Container */
.blog-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 15px;
}

/* Blog Card */
.post {
    background-color: #ffffff;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.post:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* Blog Image */
.blog-image img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

/* Blog Content */
.blog-post {
    padding: 15px;
}

.blog-title {
    font-size: 20px;
    font-weight: bold;
    color: #0066cc!important;
    margin: 0 0 10px;
}

body .blog-container .post .blog-content {
    font-size: 10px;
    color: #777;
    text-align: left;
    font-style: italic;
    margin: 10px;
    font-weight: 800;
}



/* Blog Meta Section */
.blog-id,
.blog-date {
    font-size: 12px;
    color: #888;
    margin-bottom: 5px;
    margin: 10px;
}

.meta-label {
    font-weight: bold;
    color: #777;
}

.meta-item {
    color: #333;
}

/* Read More Button */
.read_more a {
    display: inline-block;
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #c6e9e0;
    color: #25a58b;
    font-size: 14px;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    margin:10px;
}

.read_more a:hover {
    background-color: #25a58b;
    color: #fff;
}

/* No Blogs Message */
.no-blogs-message {
    font-size: 18px;
    color: #888;
    text-align: center;
    margin: 20px 0;
}

.blog-container .post .blog-content h2, .blog-container .post .blog-content p , .blog-container .post .blog-content h1{
    font-size: 14px;
    color: #777;
    text-align: left;
    font-style: italic;
    font-weight: 400;
}

@media (max-width: 768px) {
    .blog-container {
        grid-template-columns: repeat(2, 1fr);
}}

@media (max-width: 576px) {
    .blog-container {
        grid-template-columns: repeat(1, 1fr);
    }
}