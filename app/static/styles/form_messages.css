/* Style for the flash messages container */


/* Removes default list styling */
.list-unstyled {
    padding: 0;
    margin: 10px;
    list-style: none;
    width: 100%;
}
.flash--messages{
    position: relative;
    
}
/* Flash message items */
.alert {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 16px;
    font-weight: 500;
    pointer-events: auto; /* Allow interaction with the alert */
    animation: fadeInSlideUp 0.5s ease-in-out;
    text-align: center;
    margin: 10px;
}
button .close{
display: none;
}

/* Success category styling */
.alert-success {
    background-color:#d4edda;
    color: #28a745;
    border-radius: 5px;

}

/* Error category styling */
.alert-danger {
    background-color: #f8d7da;
    color: #eb495a;
}

/* Info category styling */
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Warning category styling */
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeeba;
    color: #856404;
}

/* Close button styling */
.alert .close {
    color: inherit;
    opacity: 0.8;
    font-size: 20px;
    line-height: 20px;
    background: none;
    border: none;
    cursor: pointer;
}

/* Close button hover effect */
.alert .close:hover {
    opacity: 1;
    color: inherit;
}

/* Smooth fade-in and slide-up animation */
@keyframes fadeInSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

        .form-step {
            display: none;
        }
        .form-step.active {
            display: block;
        }
        .progress-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }
        .progress-bar div {
            width: 25%;
            height: 0.3rem;
            background-color: #e0e0e0;
            position: relative;
            border-radius: 10px;

        }
        .progress-bar div.active {
            background-color: #25a38b;
        }
        .progress-bar div.active::after {
            content: '';
            position: absolute;
            top: -1.5rem;
            left: 50%;
            transform: translateX(-50%);
            color: #3b82f6;
            font-size: 1.5rem;
        }
        .error-message {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }
        .error-message.active {
            display: block;
        }
        .error-message {
            color: red;
            display: none;
        }
        .error-message.active {
            display: block;
        }
        .success-message {
            color: green;
            display: none;
        }
        .success-message.active {
            display: block;
        }