/* Full-page loading overlay */
#loading-overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

/* Loading text */
#loading-overlay p {
    font-size: 20px;
    font-weight: bold;
    color: #25a38b;
}

/* Loading dots animation */
.loading-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.dot {
    width: 10px;
    height: 10px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    animation: bounce 1.5s infinite ease-in-out;
}

.dot:nth-child(1) {
    animation-delay: 0s;
}

.dot:nth-child(2) {
    animation-delay: 0.3s;
}

.dot:nth-child(3) {
    animation-delay: 0.6s;
}

/* Animation for dots expanding from small to big */
@keyframes bounce {
    0%, 100% {
        transform: scale(0.5);
    }
    50% {
        transform: scale(1);
    }
}

/* Hide content while loading */
body.loading .real-form {
    visibility: hidden;
}