@import url('/static/styles/dashboards.css');

.form-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    align-items: center;
    padding: 10px;
}

fieldset{
    border-radius: 10px;
    border:1px solid var(--background-color);
    width: 1000px;
    margin: 10px auto;
}

.input-group{
    background-color: var(--background-color);
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 12px;
    margin : 10px;
}
.form-control{
    border-radius: 10px;
    color: var(--font-color);
    padding: 10px;
    background-color: var(--background-color);   
}

.input-group .icon {
    color: var(--font-color);
    font-size: 30px;
}

legend {
    font-size: 20px;
    font-weight: 500;
    color: var(--font-color);
    padding: 0 10px; /* Or desired space */
    position: relative;
    left: 10px;
    top: 20px;
}

.form-group  label {
    font-size: 16px;
    font-weight: 500;
    color: var(--font-color);
    padding: 0 10px; /* Or desired space */
    position: relative;
    left: 10px;
    top: 10px;
}

.btn-custom {
    background-color: var(--text-color);
    color:var(--font-color);
    font-weight: bold;
    border-radius: 10px;
    padding: 10px;
    margin: 10px;
}
.btn-custom:hover {
    background-color: var(--background-color);
    color: var(--font-color);
    cursor: pointer;
}

/* Popup container - positioned to cover the whole screen */
.mypopup {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.4); /* Black background with opacity */
}

/* Popup content box */
.popup-content {
    background-color: var(--text-color);
    margin: 10px auto; /* Center vertically and horizontally */
    padding: 20px;
    border: 1px solid #888;
    width: 50%; /* Adjust as needed */
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border-radius: 8px;
}

/* Close button - positioned in the top-right corner */
.close-popup {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-popup:hover,
.close-popup:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}




button#close-button {
    padding: 10px 20px;
    font-size: 16px;
    background-color: #6c757d;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 4px;
}

button#close-button:hover {
    background-color: #5a6268;
}

.popup-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: #000;
}

.right-buttons-group {
    margin: auto;
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    flex-direction: flex;
    max-height: 50px;
}