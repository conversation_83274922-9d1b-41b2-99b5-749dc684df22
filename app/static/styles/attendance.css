/* Basic reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
a{
    text-decoration: none;
}
.decision-buttons{
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap : 20px;
}
#clockInButton{
    background-color: #00b4db;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}
#clockInButton:hover{
    background-color: #0095b7;
}
#clockOutButton{
    background-color: #ff6b6b;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}
#clockOutButton:hover{
    background-color: #ff5252;
}
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #00b4db, #25a38b);
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
}
.fa-check{
    background-color: rgb(6, 150, 6);
    font-size: 5rem;
    padding: 2rem;
    border-radius: 100%;
    border: 5px solid white;
    animation: check 1s ease;
}
.fa-xmark{
    background-color: rgb(179, 11, 11);
    font-size: 5rem;
    padding: 2rem;
    border-radius: 100%;
    border: 5px solid white;
    animation: check 1s ease;
}
.fa-info{
    font-size: 2rem;
    padding: 2rem;
    animation: check 1s ease;
    color: #ffffff;
    border: 2px solid white;
    border-radius: 100%;
    background-color: #000000;
}
@keyframes check {
    0%{
        transform: scale(0);
    }
    100%{
        transform: scale(1);
    }
}
h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

/* Video feed */
#videoElement {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    width: 80%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

/* Capture button */
#captureButton {
    background-color: #000000;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#captureButton:hover {
    background-color: #737373;
}

/* Canvas for image capture */
#canvas {
    display: none;
}

/* Recognition result */
#result {
    margin-top: 20px;
    font-size: 1.5rem;
    text-align: center;
}
