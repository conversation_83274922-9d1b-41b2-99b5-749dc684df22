@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");
body {
    font-family: 'Jost', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color-light);
    margin: 0;
    padding: 0;
}
@page {
    size: A4;
    margin: 1cm;
}

@media print {
    body {
        width: 21cm;
        height: 29.7cm;
        margin: 0;
        padding: 1cm;
        box-sizing: border-box;
    }
    .print-button{
        display: none;
    }
    .no-print {
        display: none;
    }


    .print-area{
        display: none;
    }
    .header--wrapper{
        display: none;
    }
    .display, .sidebar, .footer{
        display: none;
    }
}
:root{
    --primary-color: #cce8e3;
    --secondary-color: #25a38b;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #737373;
    --shadow-color:rgba(0, 255, 221, 0.1);
    --warning-color: #ff0000;
    --white-color: #fff;
    --green-color: #3d963d;
    
}

* {
    box-sizing: border-box;
    color: #525252;
}
.combined--tables{
    border: 2px solid var(--secondary-color);

}
.payslip{
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 28px;
}
.payslip h1{
    color: #193141!important;
    font-weight: 600;
}
.payslip--header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0 ;
}
.company--logo-payslip img{
    width: 100%;
    height: 100px;
}
.company--details{
    display: flex;
    justify-content: space-between;
    align-items: left;
    border-bottom: 2px solid var(--secondary-color);
    margin: 0;
    
}
.space {
    margin-top: 1em;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    gap: 1em;
}
.company--details table{
    border-collapse: collapse;
    border-radius: 5px;
    font-size: 15px;
    width: max-content;
    border: none;
}
.company--details th {
    margin: 0;
    text-align: left;
    font-weight: 600;
}
.company--details td{
    text-align: left;
    border: none;
    border: none;
}
.payslip--table{
    text-align: center;
    color: var(--white-color);
    background-color: var(--secondary-color);
    
}



.table-title th{
    color: #193141;
    text-align: center;
}


.payslip-content h2{
    text-align: center;
    font-weight: 600;
    border-radius: 5px;
    
}

.payslip--table {
    width: 100%;
    border-collapse: collapse;
    font-size: 15px;
    background-color: var(--white-color);
}
.payslip--table th, .payslip--table td {
    border: 1px solid #c7c7c7;
    padding: 0;
    padding: 0.5px;   
}

.payslip--table th {
    font-weight: 600;
    text-align: left;
    background-color: var(--background-color);
}

/* Payment details table*/
.payment--details {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1em;
    font-size: 13px;
}
.payment--details th, .payment--details td {
    border: 1px solid #c7c7c7;
    padding: 0.4em;
}

.payment--details th {
    background-color: var(--primary-color);
    font-weight: 500!important;
    text-align: left;
}

.payslip--footer{
    display: flex;
    justify-content: space-between;
    height: 40%;
    font-size: 13px;
    border-top: 3px solid var(--secondary-color);
}
.footer--column-two p, .footer--column-three p {
    margin-bottom: 20px!important;
}

.title{
    font-weight: 500
}

p {
    margin: 0.4em 0;
    line-height: 1.6;
}

.payslip-content {
    width: 100%;
    max-width: 19cm;
    margin: 0 auto;
    min-height: 29.7cm;
}

.footer--column-three p, h3, .footer--column-two p, h3, .footer--column-one p, h3{
    height: 8px;
}

.payslip--footer h3{
    background-color: var(--primary-color);
    height: fit-content;
    padding: 0.1em;
    color: var(--secondary-color);

}

#disclaimer{
    font-size: 9px;
    font-weight: 600;
    color: var(--warning-color);
    text-align: center;
    margin-top: 10px;
}

#disclaimer--content{
    font-size: 9px;
    font-weight: 600;
    margin-top: 10px;
    border-top: 5px solid var(--secondary-color);
}

.footer--column-two p, .footer--column-three p {
    margin-bottom: 40px;
}

.footer--column-two .second_row, .footer--column-three .second_row{
    position: relative;
    bottom: -23px;
}
.print-area button{
    color: #ffffff;;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 15px;
    margin-top: 1em;
}

.print-area button:hover{
    outline: solid 1px #737373;
}