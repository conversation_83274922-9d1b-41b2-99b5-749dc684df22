:root{
    --primary-color: #cce8e3;
    --secondary-color: #25a38b;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #737373;
    --shadow-color:rgba(0, 255, 221, 0.1);
    --warning-color: #ff1e00;
    --white-color: #fff;
    --green-color: #3d963d;
    --light-font-color: #f8f9fa;

}
.large--table{
    overflow-x: auto;
    overflow-y: auto;
    border-collapse: collapse;
    font-size: small;
    font-weight: 400;
    color: var(--font-color);
    background-color: var(--white-color);
    z-index: 1;
    border-radius: 5px;
    padding: 1em;
}
tbody tr:nth-child(even),
.table tr:nth-child(even){
    color: #636363;
    background-color: var(--background-color);
    border-bottom: 1px solid #ececec;
}
tbody tr:nth-child(odd),
.table tr:nth-child(odd){
    color: #636363;
    background-color: var(--white-color);
    border-bottom: 1px solid #ececec;
}
.dataTable tbody tr:nth-child(odd), .table tr:nth-child(odd){
    color: #636363;
    background-color: var(--white-color);
    border-bottom: 1px solid #d9d9d9;
}

table, .table{
    border-collapse: collapse;
    font-size: small;
    font-weight: 400;
    color: var(--font-color);
    z-index: 1;
    width: 100% !important;
    border: 1px solid #cecece;
    position: relative;
    overflow: visible;
}
.table{
    grid-column: span 2;
}
td, .table td{
    padding: 0.2rem;
}
th, .table th{
    font-weight: 700;
    color: var(--font-color);
    background-color:var(--background-color);
    padding: 0.5rem;
    text-align: left;
    border-bottom: 3px solid var(--font-color);
}



/*tfoot*/
tfoot, .table tfoot{
    background-color: var(--primary-color);
    color: var(--font-color);
    font-weight: 700;
    text-align: left;
}

tfoot td:first-child, .table tfoot td:first-child{
    text-align: left;
    font-weight: 700;
    position: sticky;
    background-color: var(--background-color);
    z-index: 1;
    left: 0;
}

/* Index column styling */
.table-custom td:first-child {
    text-align: center;
    font-weight: bold;
}
.table-custom tbody tr td:nth-child(1),
.table-custom tbody tr td:nth-child(2),
.table-custom tbody tr td:nth-child(3)
{
    background-color: var(--background-color)
}
.table-custom thead th:nth-child(1),
.table-custom tbody td:nth-child(1)
{
    position:sticky!important;
    left:-13px;
    background-color: #adb1b0;
    z-index: 1;
    width: fit-content!important;
}

.table-custom thead th:nth-child(2),
.table-custom tbody tr td:nth-child(2){
    position:sticky!important;
    left:29px;
    width: 100%;
    z-index: 1;
}
.table-custom thead th:nth-child(3),
.table-custom tbody tr td:nth-child(3){
    position:sticky!important;
    left:5rem;
    z-index: 1;
}
.action-buttons{
    display: flex;
    justify-content: center;
    text-decoration: none;
    color: #f1f1f1;
    background-color: var(--font-color);
    padding: 5px;
    border-radius: 10px;
    text-align: center;
}
.table-custom thead th{
    width: fit-content;
}
@media print{
    .no-print{
        display: none;
    }
    .print{
        display: block;
    }

}
/* Override for subscription management page */
#payments-table_wrapper {
    display: block !important;
}

/* Default DataTables wrapper styling for other tables */
.dataTables_wrapper:not(#payments-table_wrapper) {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    height: fit-content;
    gap: 1rem;
}

/* Make sure search input is visible for payments table */
#payments-table_filter input {
    display: inline-block !important;
}

/* Hide search input for other tables if needed */
.dataTables_filter:not(#payments-table_filter) input {
    display: none;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-direction: row;
}
.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
    position: sticky;
    left: 0;
    z-index: 1; /* Adjust as needed */
}



/* DataTables search input styling */
.dataTables_wrapper .dataTables_filter input {
    margin-left: 0.5em;
    display: inline-block !important;
    width: 150px;
    height: 30px;
    border: 1px solid #ebebeb!important;
    background-color: #ebebeb!important;
    border-radius: 2rem !important;
    padding: 10px;
    font-size: 1em;
    transition: border-color 0.3s, box-shadow 0.3s;
    outline: none;
    box-shadow: var(--shadow-color);
}

/* Special styling for payments table search */
#payments-table_filter input {
    width: 200px !important;
    height: 36px !important;
}


.dataTables_wrapper .dataTables_filter input:focus {
    border-color: #009879!important;
    outline: none;
    background-color: #fff!important;

}

/* search label */
.dataTables_wrapper .dataTables_filter label {
    font-size: 1em;
    margin-right: 10px;
    font-weight: 500;


}

/* DataTables pagination styling */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 5px 10px;
    border: 1px solid var(--secondary-color);
    border-radius: 4px;
    margin: 0 2px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #009879;
    color: #fff;
    border-color: #009879;
}

/* DataTables info styling */
.dataTables_wrapper .dataTables_info {
    font-size: 1em;
    margin-top: 10px;
}