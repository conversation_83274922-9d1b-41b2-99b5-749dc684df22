/* Loading Overlay Styles */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8); /* Semi-transparent background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: #fff;
    font-family: Arial, sans-serif;
    font-size: 18px;
}

.loading-dots {
    display: flex;
    justify-content: space-between;
    width: 60px;
}

.loading-dots .dot {
    width: 12px;
    height: 12px;
    background-color: #fff;
    border-radius: 50%;
    animation: bounce 1s infinite ease-in-out;
}

.loading-dots .dot:nth-child(1) {
    animation-delay: 0s;
}

.loading-dots .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots .dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Form and Page Layout Styles */
.big-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    font-family: Arial, sans-serif;
}

.header-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 26px;
    color: #333;
}

.form-row {
    margin-bottom: 15px;
}

.input-group-text {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 10px;
}

.input-group-text .icon {
    margin-right: 10px;
}

.form-control {
    border: none;
    outline: none;
    flex: 1;
}

.btn-custom {
    display: block;
    width: 100%;
    background-color: #007bff;
    color: #fff;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    text-align: center;
}

.btn-custom:hover {
    background-color: #0056b3;
}

p a {
    color: #007bff;
}

p a:hover {
    text-decoration: underline;
}

/* Flash Messages */
.flash--messages {
    margin-bottom: 20px;
}

.alert {
    padding: 10px;
    border-radius: 4px;
}

.alert .close {
    color: #333;
}

