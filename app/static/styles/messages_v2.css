/* Flash messages container */
ul.list-unstyled {
    padding: 0;
    margin: 0;
    list-style: none;
    position: fixed;
    top: 90px; /* Distance from the top of the viewport */
    right: 20px; /* Distance from the right edge of the viewport, adjust as needed */
    z-index: 1050;
    width: auto; /* Adjust width as needed, e.g., 100% for full width or a specific pixel value */
}

ul .list-unstyled .flash--messages{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: auto;
}

ul.list-unstyled li {
    margin: 10px;
    padding: 0 10px ;
    width: 100%;
    animation: fadeOut 0.5s ease-in-out;
}
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}
.list-unstyled button{
    background-color: transparent;
    border: 5px transparent;
    border-radius: 5px;
    padding: 5px;
    margin: 5px;
    cursor: pointer;
}
.alert{
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin: 5px;
}

.btn-close {
    background-color: transparent;
    border: 5px transparent;
    border-radius: 5px;
    padding: 5px;
    margin: 5px;
    cursor: pointer;
}
/* Success category styling */
.alert-success {
    background-color: #d3fadc;
    color: #00ae3d;
    font-weight: 500;
    margin: 5px;
}

/* Error category styling */
.alert-danger {
    background-color: #f8d7da;
    color: #eb495a;
    font-weight: 500;
    margin: 5px;
}

/* Info category styling */
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Warning category styling */
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeeba;
    color: #856404;
}

/* Close button styling */
.alert .close {
    color: inherit;
    opacity: 0.8;
    font-size: 20px;
    line-height: 20px;
}

/* Close button hover effect */
.alert .close:hover {
    opacity: 1;
    color: inherit;
}

/* Smooth fade-in animation */
.alert-dismissible.fade.show {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
