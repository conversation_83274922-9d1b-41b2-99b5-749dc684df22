@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
/*import Anton FONT*/
@import url('https://fonts.googleapis.com/css2?family=Anton&display=swap');
:root{
    --primary-color: #cce8e3;
    --secondary-color: #25a38b;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #7d7d7d;
    --shadow-color:rgba(0, 255, 221, 0.1);
    --warning-color: #ff0000;
    --semi-warning-color: #fa7e7e;
    --white-color: #fff;
    --green-color: #3d963d;
    --blue-color: #1c6d99;
    --yellow-color: #f7b731;

}
body, html{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
    overflow-x: hidden;
}

*{
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0;
    border-radius: var(--border-radius);
    background: linear-gradient(90deg,var(--primary-color),#ffffff); /* Gradient colors */
}
h1{
    font-size: 2em;
    color: var(--secondary-color);
    background: linear-gradient(90deg, #25a38b, #67b4dd); /* Gradient colors */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.key--features .white h2{
    color: #ffffff !important;
}
.heading {
    position: fixed;
    top: 0;
    z-index: 1000;
    width: 100%;
    background-color: var(--primary-color);
}
.btn-box{
    margin: 5em;
}
.btn-box a{
    text-decoration: none;
    color: var(--font-color);
    padding: 10px;
    border-radius: var(--border-radius);
    transition: 0.3s;
    background-color: var(--secondary-color);
    color: var(--white-color);
    box-sizing: border-box;
}

.middle--contents {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10em;
    width: 100%;
    margin: 10em 0 1em 0;
    position: relative; /* Necessary for the pseudo-element */
    z-index: 1;
}




nav ul{
    display: flex;
    justify-content:center;
    align-items: center;
    list-style: none;
    gap: 1.5rem;
    font-weight: 100;
    margin-right: 2rem;
    font-size: 16px;

}

nav ul li a{
    text-decoration: none;
    color: var(--secondary-color);
    padding: 5px;
    border-radius: var(--border-radius);
    transition: 0.3s;
}
.nav--logo img{
    width: 80px;
    height: 80px;
    border-radius: 5px;
}
.nav--logo{
    margin-left: 2em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}
.nav--logo h1{
    font-size: 4em;
    color: var(--secondary-color);
    background: linear-gradient(90deg, #25a38b, #67b4dd); /* Gradient colors */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.nav--auth{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    
}
.nav--auth li{
    font-weight: 400;
    
}
.nav--auth .login a{
    color: #7d7d7d;
    padding: 5px;
    border-radius: 10px;
    transition: 0.3s;
    border: 1px solid #7d7d7d;
    
}
.nav--auth .login a:hover{
    background-color: var(--blue-color);
    color: var(--white-color);
    transition: 0.3s;
}

.nav--auth .signup a{
    color: #7d7d7d;
    padding: 5px;
    border-radius: 10px;
    width: 100%;
    transition: 0.3s;
    border: 1px solid #7d7d7d;

}
.nav--auth .signup a:hover{
    background-color: var(--green-color);
    color: var(--white-color);
    transition: 0.3s;
}
.advert--content{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    width: 100%;
    padding: 5px;
    width: 100%;
    background-color: var(--white-color);
}
.advert--content .tittle{
    font-size: 12px;
    color: var(--white-color);
    background-color: var(--blue-color);
    padding: 10px;
    border-radius: 20px;
    font-weight: 600;
}
.advert--content .details{
    font-size: 12px;
    color: var(--blue-color);
    padding: 10px;
    font-weight: 600;
}
.page--contents{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

}
.right--info {
    display: flex;
    justify-content: left;
    align-items: left;
    flex-direction: column;
    gap: 1em;
    width: min-content;
    padding: 10px;
    animation: slideIn 3s ease-in;
    box-sizing: border-box;
    border-radius: 10px;
    margin: 10px 0;
    
}

.descriptive p{
    color: var(--white-color);
    font-weight: 300;
    font-size: 24px;
}
.top--info{
    display: flex;
    justify-content: center;
    align-items: left;
    gap: 1em;
    width: 100%;
    box-sizing: border-box;
    height: 100%;
    z-index: 100;

}
.description{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 800px;
    gap: 10px;
    padding: 10px;
    position: relative;
    top: -12em;
    color: #575757;
    font-size: 1.2em;
    margin-right: 42em;
}
.description p{
    font-size: 1.2em;
    color: var(--font-color);
    font-weight: 400;
}
.daata{
    display: flex;
    justify-content: center;
    align-items: left;
    flex-direction: column;
    gap: 0;
    width: 100%;
}
.product--name{
    font-size: 32px
}

.display--notice{
    display: none;
}
.big--components {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 2em;
    width: 100%;
    margin: -10em 0 -1em 0;
    position: relative; /* Ensure the container is positioned relative */
    overflow: hidden; /* Prevent overflow of pseudo-element */
}

.big--components::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/static/images/system_images/backgrounds/pexels-jopwell-2422278-min.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: right;
    z-index: 1; /* Ensure the background image is below the blue overlay */
}

.big--components::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.356); /* Blue color with 50% opacity */
    z-index: 2; /* Ensure the blue overlay is above the background image */
}

.contact--content{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px;
    width: 100%;
    text-align: left;
    margin: 0 auto;
}
.contact--content img{
    width: 1000px;
    height: 1000px;
    border-radius: 5px;
    box-shadow: 10px 10px 10px #a8a4a4;
}
.contact--form{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    position: sticky;
    left: 15em;
    bottom: 18em;
}


.percent{
    grid-row: 1;
    grid-column: 2;
}
.text_v2{
    grid-row: 3;
    grid-column: 2;
}


.right--info h1 {
    font-size: 14em;
    font-family: 'Poppins', serif;
    font-weight: 300;
    color: var(--white-color) !important;
    background: linear-gradient(90deg, #ffffff, #ffffff); /* Gradient colors */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}


.percent h1{
    font-size: 3.5em;
    color: var(--blue-color);
    font-family: 'ANTON', sans-serif;
    font-weight: 600;
}
.text_v2 h1{
    font-size: 3.5em;
    color: var(--secondary-color);
    font-family: 'ANTON', sans-serif;
    font-weight: 600;
}
.features--cards{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    width: 100%;
    padding: 10px;
    width: 100%;
}
.contact--cards{
    display: flex;
    justify-content: space-between;
    align-items: left;
    gap: 10px;
    padding: 10px;
    border-top: 1px solid #7d7d7d;
    margin: 10em 0;
}
.card-body{
    display: flex;
    justify-content: center;
    align-items:left;
    gap: 10px;
    width: 100%;
    padding: 10px;
}
.card--icon{
    align-items: center;
    text-align: center;
    justify-content: center;
    display: flex;
    padding: 10px;
    width: 20%;
}

.card-text h5{
    font-size: 1.5em;
    color: var(--font-color);
    font-weight: 600;
}
.card-text a i{
    text-decoration: none;
    color: var(--blue-color);
    margin: 10px;
    font-size: 1.2em
}
.card-text a i:hover{
    text-decoration: underline;
    animation: fadeIn 0.5 forwards;
    color: #000;
}

.card--icon i{
    font-size: 3em;
    color: var(--white-color);
    background-color: var(--blue-color);
    padding: 10px;
    border-radius: 50%;
}
.built--for{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    width: 100%;
}
.built--for--cards{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3em;
    flex-wrap: wrap;
    width: 100%;
    padding: 10px;
    width: 100%;
}
.simplified--payroll{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
    padding: 10px;

}
.notice{
    padding: 10px;
    background-color: var(--yellow-color);
    width: 100%;
}
.notice a{
    text-decoration: underline;
    color: var(--blue-color);
    font-weight: 600;
    animation: pop 0.5s forwards;
}

@keyframes pop {
    from {
        transform: scale(0.5);
    }
    to {
        transform: scale(1);
    }
}
.simplified--contents{
    display: flex;
    justify-content: center;
    align-items: left;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    width: 35%;
    
}
.simplified--contents li{
    list-style: none;
    color: var(--font-color);
    display: flex;
    align-items: center;
    font-weight: 600;
}
.simplified--image img{
    width: 18em;
    height: 18em;
    border-radius: 5px;
    box-shadow: 10px 10px 10px #a8a4a4;
}
.simplified--contents li span{
    color: var(--secondary-color);
    margin-right: 10px;
    font-size: 1.2em;
    font-weight: bold;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.card_v2{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 5px;
    background-color: var(--bg-color);
    box-shadow: 0 0 10px #000;
    width: 450px;
    height: 450px;
    margin: 10px;
    border-radius: 10px;
    text-align: center;
    overflow-y: hidden;
    overflow-x: hidden;
    box-shadow: 10px 10px 20px #a8a4a4;
}
.card_v2 p{
    font-size: 1em;
    color: #757575;
    padding: 10px;
    font-weight: 400;
    text-align: center;
    height: 70%;

}
.card_v2 img{
    width: 100%;
}


.key--features {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    gap: 10px;
    width: 100%;
    padding: 10px;
    color: white; /* Ensure the text is visible on the gradient background */
    width: 100%;
}
.key--features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/static/images/system_images/backgrounds/back_g.jpg');    
    background-repeat: no-repeat; /* Prevents the image from repeating */
    background-size: cover; /* Ensures the image covers the container */
    background-position: center; /* Centers the image */
    z-index: -1; /* Ensure it stays behind the content */
    pointer-events: none; /* Allows interaction with content */
}


.contacts{
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
    padding: 10px;
}
.contacts table{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: left;
}
.contacts table tr{
    margin:0 20rem;   
    gap: 10rem
}

table td{
    padding: 10px;
    border-radius: 5px;
    color: var(--white-color);
}
table th{
    padding: 10px;
    color: var(--white-color);
}
table td a{
    text-decoration: none;
    color: var(--white-color);
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

table td a:hover{
    text-decoration: underline;
}
.key--features--cards{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    width: 100%;
    padding: 10px;
}
.key--features--header{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    width: 100%;
    padding: 10px;
    width: 100%;
    color: var(--white-color);
}
.key--features--header h3{
    color: var(--white-color);
    font-size: 3em;
}

.card_v3 {
    position: relative; /* Establish local stacking context for the pseudo-element */
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 10px;
    border-radius: 10px; /* Keep consistent radius */
    color: var(--blue-color);
    width: 300px;
    height: 300px;
    margin: 10px;
    text-align: center;
    z-index: 1; /* Ensures content appears above the blurred background */
    overflow: hidden; /* Prevent pseudo-element overflow */
    animation: fadeIn 0.5s forwards;
}

.card_v3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000 ; /* Adjust to the desired background color or use a gradient */
    opacity: 0.2; /* Adjust for visibility */
    filter: blur(15px); /* Controls the blur intensity */
    z-index: -1; /* Keeps it behind the card content */
}
.card_v3:hover{
    transform: translateY(-5px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}
.card_v3 i{
    font-size: 2em;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border: 3px solid var(--white-color);
    border-radius: 50%;
    z-index: 1;
    background-color: var(--white-color);
    color: var(--secondary-color);
    
}
.card_v3 p{
    color: var(--text-color);
    font-weight: 600;
    padding: 10px;
    z-index: 1;
    font-weight: 400;
}
.card_v3 h3{
    color: var(--text-color);
    font-weight: 600;
    padding: 10px;
    z-index: 1;
    font-weight: 500;
    font-size: 20px;
}
.line{
    background-color: var(--text-color);
    width: 100%;
    height: 1px;
    position: relative;
    bottom: 35px;
    border-radius: 10px;
    
}


@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes slideIn {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0%);
    opacity: 1;
  }
}

       
.about_cards{
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    width: 100%;
    padding: 10px;
    text-align: left;
    width: 100%;
}
.card_v5 img{
    width: 220px;
    height: 200px;
    margin : 1em;
    padding: 10px;  

}
.description span {
    opacity: 0;
    display: inline-block;
    animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.card_v5{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border-radius: 5px;
    color: var(--blue-color);
    width: 1200px;
    margin: 2em;
    border-radius: 10px;
}
.about--text{
    border-left: 1px solid var(--blue-color);
    padding: 10px;
    border-radius: 10px;
    width: 100%;
}
.move--up{
    position: relative;
    bottom: 5em;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    gap: 1em;

}
.why--choose--us--content{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
    padding: 10px;
    width: 100%;
    position: relative;
}
.why--choose--us--header{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    padding: 10px;
    width: 100%;
    text-align: center;
}

.card_v4{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 10px;
    border-radius: 5px;
    color: var(--blue-color);
    width: 300px;
    height: 300px;
    margin: 10px;
    border-radius: 10px;
    text-align: center;
    background-color: var(--white-color);
}
.little{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
    top: 0;
    animation: slideUp 2s ease-in;
    padding: 2rem;
    text-align: center;
}
.little h2{
    font-size: 1.5em;
    background: linear-gradient(90deg, #ffffff, #ffffff); /* Gradient colors */
    font-weight: 600;
    -webkit-background-clip: text;
}
.little a{
    text-decoration: none;
    color: #000;
    background-color: #ffdb0f;
    padding: 10px;
    border-radius: 20px;
}
.little a:hover{
    color: #ffffff;
    background-color: #25a38b;
    animation: fadeIn 0.5 forwards;

}
@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}
.top--nav{
    display: none;
}
footer{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    padding: 10px;
    background: linear-gradient(90deg, #25a38b, #67b4dd);
    width: 100%;
    position: relative;
    bottom: 0;
    z-index: 1000;
    color: #f1f1f1;
}

@keyframes fadein {
    from { opacity: 0; }
    to   { opacity: 1; }
}
/* Hide the nav links by default for mobile */
.topnav {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    right: -100%; /* Start off-screen */
    height: 100vh;
    width: 70%;
    background-color: var(--primary-color);
    justify-content: center;
    transition: right 0.3s ease-in-out;
    z-index: 1000;
    padding: 10px;
    overflow-y: scroll;
}

/* Style for the close button */
.close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2em;
    color: var(--secondary-color);
    cursor: pointer;
    background: transparent;
    border: none;
}

.topnav li {
    margin: 1.5em;
    list-style: none;
}

.topnav a {
    color: var(--secondary-color);
    font-size: 1.5em;
    text-decoration: none;
    transition: color 0.3s ease;
}

.top--nav li a:hover {
    color: var(--white-color);
    background-color: var(--secondary-color);
    font-size: 1.5em;
    text-decoration: none;
    transition: color 0.3s ease;
}

.topnav a:hover {
    color: var(--secondary-color);
}

/* Hamburger menu styling */
.hamburger {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 40px;
    height: 30px;
    cursor: pointer;
    position: relative;
    align-items: center;
    right: 2em;
}

.hamburger span {
    display: block;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg,#25a38b, #67b4dd);
    transition: 0.3s;
    border-radius: 10px;
}
.close-btn {
    height: 10px;
}
/* When the menu is open */
.topnav.active {
    right: 0; /* Slides into view */
}

/* Close button animation inside the menu */
.close-btn:hover {
    color: var(--white-color);
}

.container{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    padding: 10px;
    text-align: center;
}
.heads p{
    font-size: 1.2em;
    color: var(--font-color);
    font-weight: 400;
    text-align: center;
    font-style: italic;

}
.heads {
    margin: 0 10rem;
}
.heads i {
    font-size: 3em;
    background: linear-gradient(90deg, #67b4dd,  #25a38b); /* Gradient colors */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-style: italic;
    padding: 10px;
}
.docs--content{
    display: flex;
    justify-content: center;
    align-items: left;
    flex-direction: column;
    gap: 10px;
    width: 1200px;
    padding: 10px;
}
ol, ul{
    margin-left: 2em;
}
ol li, ul li, p{
    font-size: 1em;
    color: var(--font-color);
    font-weight: 500;
}
.docs-container{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
    width: 100%;
}
.contents-container{
    position: relative;
    top: 0;
}

.image-container img{
    width: 100%;
    border-radius: 5px;
}
.image-container-small img{
    width: 50%;
    border-radius: 5px;
}
h3{
    font-size: 1.5em;
    color: var(--font-color);
    font-weight: 600;
}
h4{
    font-size: 1.2em;
    color: var(--font-color);
    font-weight: 600;
}
.docs--content h1{
    font-size: 2em;
    color: var(--font-color);
    font-weight: 600;
}
/*Phone*/
@media(width < 700px) {
    .move--up{
        position: relative;
        bottom: 22em;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 100%;
        gap: 1em;
    }
    .right--info{
        width: 100%;
        text-align: center;
        padding: 0;
    }
    .right--info h1{
        margin-top: 2em;
        font-size: 3em;
        font-weight: 400;
    }
    .right--info p{
        font-size: 1em;
    }
    body, html{
        margin: 0;
        padding: 0;
        font-family: 'Poppins', sans-serif;
    }
    .big--heading{
        display: none;
    }
    .docs--content{
        display: flex;
        justify-content: center;
        align-items: left;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .docs-container{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 2em;
        width: 100%;
    }
    .top--nav{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        flex-wrap: nowrap;
        overflow-y: auto;
    }
    .nav--logo img{
        width: 40px;
        height: 40px;
        border-radius: 5px;
    }
    .nav--logo h1{
        font-size: 1em;
        color: var(--secondary-color);
        background: linear-gradient(90deg, #25a38b, #67b4dd); /* Gradient colors */
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    .top--info{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 1em;
        width: 100%;
        padding: 10px;
        animation: fadein 2s ease-in;
    }
    .page--contents{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        align-items: center;
        width: 100%;
    }
    .big--components{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .heads {    
        .nav--logo h1{
        font-size: 2.5em;
    }
        margin: 0 2rem;
    }
    .text h1{
        font-size: 5em;
        font-family: 'ANTON', sans-serif;
        font-weight: 600;
        gap: 10rem;
    }
    .daata{
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: center;
        gap: 0;
        width: 100%;
    }
    .percent h1, .text_v2 h1{
        display: none;
    }
    .daata .little{
        display: block;
        font-size: 12px;
    }
    .top--image img{
        height: 100px;
        width: 100px;
    }

    .description{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100px;
        gap: 10px;
        padding: 10px;
        position: relative;
        top: 2em;
        color: #575757;
        font-size: 1em;
        margin: 2em;
    }

    .why--choose--us{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 400px;
        padding: 10px;
        position: relative;
        margin-top: 17em;
    }
    .why--choose--us--header{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
        width: 100%;
        text-align: center;
    }
    .why--choose--us--content{
        flex-direction: column;
    }
    .card_v5 img{
        height: 200px;
        width: 200px;
    }
    .contacts{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .contact--cards{
        display: flex;
        justify-content: center;
        align-items: left;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        border-top: 1px solid #7d7d7d;
        margin: 10em 0;
    }
    .card-body{
        display: grid;
        grid-template-columns: 2em 1fr;
        gap: 2em
    
    }
    .card-text h5{
        font-size: 1.2em;
    }
    .card--icon i{
        font-size: 2em;
        color: var(--white-color);
    }
    .contact--form{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .container{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .contacts table{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: left;
    }
    .contacts table tr{
        margin:0 20rem;   
        gap: 10rem
    }
    table td{
        padding: 10px;
        border-radius: 5px;
        color: var(--white-color);
    }
    table th{
        padding: 10px;
    }
    .contacts table{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }
    .card_v5{
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
        border-radius: 5px;
        color: var(--blue-color);
        width: 100%;
        margin: 2em;
        border-radius: 10px;
    }
    .card_v5 img{
        display: none;
    }
    .card_v4{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        flex-wrap: wrap;
        padding: 10px;
        border-radius: 5px;
        color: var(--blue-color);
        width: 100%;
        margin: 10px;
        border-radius: 10px;
        text-align: center;
    }

    .simplified--contents{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        width: 100%;
    }
    .simplified--payroll{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    
    }
    .simplified--image img{
        width: 15em;
        height: 15em;
        border-radius: 5px;
        box-shadow: 10px 10px 10px #a8a4a4;
    }
    .card_v3{
        width: 100%;
    }
}
@media (width < 768px) and (width>700px) {
    .right--info h1{
        font-size: 10em;
    }
    nav{
        display: none;
    }
    .top--nav{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        flex-wrap: nowrap;
    }
    .top--info{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 1em;
        width: 100%;
        padding: 10px;
        animation: fadein 2s ease-in;
    }
    .page--contents{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        align-items: center;
        width: 100%;
    }
    .big--components{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .about--text{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
        width: 100%;
    }
}
@media(min-width:1280px) and (max-width:1536px){
    .percent h1, .text_v2 h1{
        display: none;
    }

    .daata .little{
        display: flex;
        font-size: 13px;
    }
    .big--heading h1{
        font-size: 4em;
    }
    .text h1{
        font-size: 13em;
        font-family: 'ANTON', sans-serif;
        font-weight: 600;
        text-align: center;
    }
    .big--heading ul{
        font-size: 1em;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2em;
    }
    .top--image img{
        height: 500px;
        width: 500px;
    }
    .page--contents{
        width: 75%;
    }
    .description{
        position: relative;
        top: -8em;
        left: 10em;
    }
    .top--info{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.5em;
        width: 100%;
        font-size: 1rem;
    }
    nav ul{
        gap: 1rem;
    }
    .nav--logo img{
        width: 60px;
        height: 60px;
        border-radius: 5px;
    }
    .nav--logo h1{
        font-size: 2em;
    }
    .nav--links{
        display: flex;
        justify-content: center;
        flex-wrap: nowrap;
        align-items: center;
        gap: 0.5rem;
    }
    .nav--links li{
        font-size: 0.8em;
    }
}
@media(max-width:370px){
    .nav--logo h1{
        font-size: 2.5em;
    }
}
/*Tablet*/
@media (min-width: 768px) and (max-width: 1023px) {
    .right--info h1{
        font-size: 10em;
    }
    nav{
        display: none;
    }
    .top--nav{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        flex-wrap: nowrap;
    }
    .top--info{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 1em;
        width: 100%;
        padding: 10px;
        animation: fadein 2s ease-in;
    }
    .page--contents{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        align-items: center;
        width: 100%;
    }
    .big--components{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .heads {    
        .nav--logo h1{
        font-size: 3.5em;
    };
        margin: 0 2rem;
    }
    .text h1{
        font-size: 8em;
        font-family: 'ANTON', sans-serif;
        font-weight: 600;
        gap: 10rem;
    }
    .daata{
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: center;
        gap: 0;
        width: 100%;
    }
    .percent h1, .text_v2 h1{
        display: none;
    }
    .daata .little{
        display: block;
        font-size: 12px;
    }
    .top--image img{
        height: 100px;
        width: 100px;
    }

    .description{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100px;
        gap: 10px;
        padding: 10px;
        position: relative;
        top: 2em;
        color: #575757;
        font-size: 1em;
        margin: 2em;
    }

    .why--choose--us{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 400px;
        padding: 10px;
        position: relative;
        margin-top: 17em;
    }
    .why--choose--us--header{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
        width: 100%;
        text-align: center;
    }
    .why--choose--us--content{
        flex-direction: column;
    }
    .card_v5 img{
        height: 200px;
        width: 200px;
    }
    .contacts{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .contact--cards{
        display: flex;
        justify-content: center;
        align-items: left;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        border-top: 1px solid #7d7d7d;
        margin: 10em 0;
    }
    .card-body{
        display: grid;
        grid-template-columns: 2em 1fr;
        gap: 2em
    
    }
    .card-text h5{
        font-size: 1.2em;
    }
    .card--icon i{
        font-size: 2em;
        color: var(--white-color);
    }
    .contact--form{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .container{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    }
    .contacts table{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: left;
    }
    .contacts table tr{
        margin:0 20rem;   
        gap: 10rem
    }
    table td{
        padding: 10px;
        border-radius: 5px;
        color: var(--white-color);
    }
    table th{
        padding: 10px;
    }
    .contacts table{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }
    .card_v5{
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
        border-radius: 5px;
        color: var(--blue-color);
        width: 100%;
        margin: 2em;
        border-radius: 10px;
    }
    .card_v5 img{
        display: none;
    }
    .card_v4{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        flex-wrap: wrap;
        padding: 10px;
        border-radius: 5px;
        color: var(--blue-color);
        width: 100%;
        margin: 10px;
        border-radius: 10px;
        text-align: center;
    }

    .simplified--contents{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        width: 100%;
    }
    .simplified--payroll{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 10px;
    
    }
    .simplified--image img{
        width: 15em;
        height: 15em;
        border-radius: 5px;
        box-shadow: 10px 10px 10px #a8a4a4;
    }
    .card_v3{
        width: 100%;
    }
    
}

@media (min-width: 1024px) and (max-width: 1050px){
    
    .nav--logo H1{
        font-size: 2em;
    }
    .nav--logo img{
        width: 50px;
        height: 50px;
        border-radius: 5px;
    }
    .nav--links li{
        font-size: 0.6em;
        
    }
    .nav--links{
        gap: 0.1rem;
    }
    .right--info h1{
        font-size: 8em;
    }
}


@media (min-width: 1050px) and (max-width: 1097px) {
    
    .nav--logo h1{
        font-size: 1.5em;
    }
    .nav--logo img{
        width: 40px;
        height: 40px;
        border-radius: 5px;
    }
    .nav--links li{
        font-size: 0.6em;
        
    }
    .percent h1, .text_v2 h1{
        display: none;
    }

    .daata .little{
        display: flex;
        font-size: 13px;
    }
    .big--heading h1{
        font-size: 4em;
    }
    .text h1{
        font-size: 13em;
        font-family: 'ANTON', sans-serif;
        font-weight: 600;
        text-align: center;
    }
    .big--heading ul{
        font-size: 1em;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1em;
    }
    .top--image img{
        height: 500px;
        width: 500px;
    }
    .page--contents{
        width: 75%;
    }
    .description{
        position: relative;
        top: -8em;
        left: 10em;
    }
    .top--info{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.5em;
        width: 100%;
        font-size: 1rem;
    }
    nav ul{
        gap: 1rem;
    }
    .nav--logo img{
        width: 60px;
        height: 60px;
        border-radius: 5px;
    }
    .nav--logo h1{
        font-size: 2em;
    }
    .nav--links{
        display: flex;
        justify-content: left;
        flex-wrap: nowrap;
        align-items: left;
        gap: 10px;
    }
    .nav--links li{
        font-size: 0.5em;
        
    }
}
/*150%*/
@media (min-width: 1097px) and (max-width: 1280px) {
    .nav--logo h1{
        font-size: 0.5em;
    }
    .nav--logo img{
        width: 40px;
        height: 40px;
        border-radius: 5px;
    }
    .nav--links li{
        font-size: 0.6em;
        
    }
    .percent h1, .text_v2 h1{
        display: none;
    }

    .daata .little{
        display: flex;
        font-size: 12px;
    }
    .big--heading h1{
        font-size: 2em;
    }

    .big--heading ul{
        font-size: 1em;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5em;
    }
    .top--image img{
        height: 500px;
        width: 500px;
    }
    .page--contents{
        width: 75%;
    }
    .description{
        position: relative;
        top: -8em;
        left: 10em;
    }
    .top--info{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.5em;
        width: 100%;
        font-size: 1rem;
    }
    nav ul{
        gap: 1rem;
    }
    .nav--logo img{
        width: 50px;
        height: 50px;
    }
    .right--info h1{
        font-size: 9em;
    }
}
/*175%*/
@media (min-width: 1281px) and (max-width: 1546px){
    
    .big--heading ul{
        gap: 12px;
    }
    .nav--logo img{
        width: 50px;
        height: 50px;
        border-radius: 5px;
    }
    .right--info h1{
        font-size: 10em;
    }
}


@media (min-width: 1536px) and (max-width: 1548px){
    nav{}
    .nav--logo img{
        width: 50px;
        height: 50px;
        border-radius: 5px;
    }
    .right--info h1{
        font-size: 8em;
    }
    .big--heading ul {
        gap: 12px;
    }
    .nav--links li{
        font-size: 0.85em;
        
    }
}
@media (min-width: 1537px) and (max-width: 1676px){
    .nav--logo img{
        width: 60px;
        height: 60px;
        border-radius: 5px;
    }
    .right--info h1{
        font-size: 11em;
    }
    .big--heading ul {
        gap: 12px;
    }
    
}