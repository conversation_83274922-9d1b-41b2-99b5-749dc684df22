@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root{
    --primary-color: #cce8e3;
    --secondary-color: #25a38b;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #737373;
    --shadow-color:rgba(0, 255, 221, 0.1);
    --warning-color: #ff1e00;
    --mid-warning-color: #ffd5cf;
    --white-color: #fff;
    --green-color: #3d963d;
    --light-font-color: #f8f9fa;
    --blue-color:#4484c7;
    
}
body, html{
    background-color: var(--background-color);
}
*{
    border: none;
    outline: none;
    box-sizing: border-box; /* This is a CSS property that makes sure that padding and border are included in the element's total width and height. */
    font-family: "Poppins", sans-serif;
}
.header-title{
    font-size: 32px;
    font-weight: 500;
    color: var(--mid-font-color);
    margin-top: 20px;
    margin-bottom: 30px;
}
.big-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 0;
    margin: 1rem;
    width: 100%;
}
/* Reposition containers to the top and center */
#message-container, #passed-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    padding: 0;
    margin: 0;
    max-width: 90%; /* To avoid overflow on small screens */
}

/* Common styles for the messages */
#message-container p, #passed-container p {
    font-size: 16px;
    margin: 10px 0;
    border-radius: 5px;
    width: max-content;
    padding: 10px 20px;
    text-align: center;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(-20px);
    animation: fadeInDown 0.5s ease forwards;
}
.form-error-messages{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    margin: 10px;
}
/* Specific colors for message types */
#message-container p {
    background-color: #f8d7da;
    color: #eb495a;
    font-weight: 500;
    margin: 5px;
}

#passed-container p {
    background-color: #d3fadc;
    color: #00ae3d;
    font-weight: 500;
    margin: 5px;
}

/* Animations */
@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateY(-20px);
    }
}
.form-sector{
    display: flex;flex-direction: column; padding: 0 10px;border: 1px solid #ccc; border-radius: 5px; width: 100%; max-width: 800px; margin: 0 auto;overflow-y: scroll;
}
.form-sector h1{
    font-size: 24px;
    font-weight: 500;
    color: var(--font-color);
    margin: 20px 0;
    border-bottom: 1px solid #ccc;
    text-align: center;
}
.form-sector label{
    font-size: 16px;
    font-weight: 500;
    color: var(--font-color);
    margin: 10px 0;
}
.input-box{
    border: 1px solid #ccc;
    padding: 5px;
    border-radius: 5px;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: space-between;
}
.input-box:focus{
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 0 0.2rem var(--primary-color);
    background-color: var(--white-color);
}
.form-slice{
    margin: 2rem 0;
}
.form-slice button{
    padding: 10px;
    border-radius: 10px;
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.action-button {
    background-color: #e0dfdf;
    color: var(--font-color);
    padding: 10px;
    border-radius: 10px;
    margin: 0 1rem;
    cursor: pointer;
}
.action-link{
    color: var(--blue-color);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    text-align: center;
    gap: 1.5rem;
    width: 100%;
}
.action-link a{
    text-align: center;
}
.action-link:hover{
    color: var(--font-color);
}
.round-container img{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    height: 50px;
    width: 50px;
}
.action-button:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}
.input-group-text i {
    color: var(--font-color)
}


.real--form{
    display: flex;
    flex-direction: column; /* Ensures content is stacked vertically */
    width: 100%;}
.random-form{
    background-color: var(--white-color);
    width: 300px;
    max-width: 500px;
}
.row--container{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    position: relative;
}
.row--container img{
    width: 50px
}
.real-form {
    display: flex;
    flex-direction: column; /* Ensures content is stacked vertically */
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    text-align: center;
    width: fit-content; /* Change to a larger percentage or use a fixed width */
    border-radius: 10px; /* Keeps the border rounded */
    margin: auto; /* Centers the form and adds space above and below */
    padding: 10px; /* Adds padding inside the form for better spacing */
    background-color: #ffffff; /* Optional: Adds a background color */
}
.selective--div{
    border-radius: 10px;
    border:1px solid var(--primary-color);
    margin: 10px auto;
    background-color: #fffcfc;
}
.selective--div legend{
    margin-bottom: 1em;
}
.form--container{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    width: 100%;
}
.form-group select{
    color: var(--font-color);
    padding: 10px;
    width: 100%;
    height: 100%;
}
legend {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--font-color);
    padding: 0 10px; /* Or desired space */
    position: relative;
    left: 10px;
    top: 20px;
}
.otp_container img{
    width: 20em;
}
.form-contents{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
}
.web-contents{
    width: 50%;
}
.real-form p {
    font-size: 16px; /* Adjust font size */
    color: var(--font-color); /* Adjust font color */
    margin: 10px 0; /* Adjust margin */
}
.form-row{
    display: flex;
    justify-content: center;
    width: 100%;
    flex-wrap: wrap;
}
.input-group-text{
    background-color: var(--background-color);
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px;
    margin: 5px 0 5px 0;
    width: fit-content;
    overflow-x: hidden;
}
.input-group-text_v2{
    background-color: transparent;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px;
    margin: 5px 0 5px 0;
    width: 100%;
    border: 1px solid #000;
}
.btn-compa{
    color: var(--font-color);
    font-size: large;
    padding: 10px;
}
.text-area{
    width: 100%;
    height: 265px;
    padding: 10px;
}
.text-area_2{
    width: 470px;
    height: 100px;
    padding: 10px;
}
.col-md-3{
    width: 100%;
    max-width: 25%;
    box-sizing: border-box;
}
.input-group-text .icon{
    color: var(--secondary-color);
    font-size: 30px;
}
.exception{
    width: 4600px;
    height: 250px;
}
.centered{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: transparent
}
.centered h1{
    font-size: 32px;
    font-weight: 500;
    color: var(--secondary-color);
}
.centered p{
    font-size: 18px;
    color: var(--font-color);
    font-weight: 500;margin-bottom: 10px;
}
.centered form{
    box-shadow: 0 0  10px 0 rgba(0, 0, 0, 0.1);
}
.form-control{
    width: 100%;
}
.form-control:focus{
    border-radius: 5px;
    border: 1px solid var(--secondary-color);
    box-shadow: 0 0 0 0.2rem var(--primary-color);
    background-color: var(--white-color);
    width: 100%;

}

.input-group-text input{
    border: none;
    background-color: transparent;
    color: var(--font-color);
    font-size: 16px;
    padding: 5px;
}

.alert{
    list-style: none;
    padding: 0;
    padding: 10px;
    margin: auto 30px auto -10px;
    border-radius: 10px;
    width: inherit;
}
.form-group {
    display: flex;
    flex-direction: column;
}
.form-group label {
    font-size: 16px;
    font-weight: 600;
    color: var(--font-color);
    padding: 0;
    text-align: left;

}
.custom-alert {
    list-style: none;
    padding: 0;
    padding: 10px;
    margin: auto 30px auto -10px;
    border-radius: 10px;
    width: inherit;
    border-radius: 5px; /* Rounded corners */
    font-size: 16px; /* Adjust font size */
    font-weight: 500; /* Slightly bold text */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add a subtle shadow */
    transition: all 0.3s ease-in-out; /* Smooth transition for hover or dismiss actions */
}
.btn-toggle{
    padding: 10px;
    color: #ffffff;
    background-color: #797979;
    border-radius: 10px;
    margin: 10px;
    cursor: pointer;
    font-weight: 500;
}
.btn-toggle:hover{
    background-color: #000;
    color: #ffffff;
}
/* Custom colors based on the category */
.custom-alert.alert-success {
    background-color: #d4edda;
    color: #852727;
    border: 1px solid #c3e6cb;
}

.custom-alert.alert-danger {
    background-color: #f8d7da;
    color: var(--warning-color);
    border: 1px solid #f5c6cb;
}

.custom-alert.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.custom-alert.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Style the close button */
.custom-alert .close {
    color: inherit; /* Inherit color from alert */
    opacity: 0.7; /* Slightly transparent */
    font-size: 20px; /* Larger close icon */
    transition: opacity 0.2s ease-in-out;
}

.custom-alert .close:hover {
    opacity: 1; /* Full opacity on hover */
}
.text-danger {
    color: var(--warning-color);
}
/* Popup container - positioned to cover the whole screen */
.mypopup {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.4); /* Black background with opacity */
}
.btn-custom{
    background-color: var(--secondary-color);
    color: var(--text-color);
    padding: 10px;
    border-radius: 10px;
    margin: 10px;
    cursor: pointer;
    font-weight: 500;
    border: none;
}
.btn-custom:hover{
    background-color: var(--primary-color);
    color: var(--secondary-color);
}
/* Popup content box */
.popup-content {
    background-color: var(--text-color);
    margin: 10px auto; /* Center vertically and horizontally */
    padding: 20px;
    border: 1px solid #888;
    max-width: max-content;
    width: 100%;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction:column;
    z-index: 1000;


}

/* Close button - positioned in the top-right corner */
.close-popup {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-popup:hover,
.close-popup:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}
.action--links{
    display: none;
    justify-content: center;
    color: var(--font-color);
    padding: 10px;
    flex-direction: column;
}
.action--links li{
    list-style: none;
    color: var(--font-color);
}
.action--links li a{
    color: var(--font-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 10px;
    margin: 10px;
    border-radius: 10px;
    transition: all 0.5s ease-in-out;
}
.decisive-btn{
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin: 10px;
    font-size: 12px;
}
.btn-continue{
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    border: none;
    font-weight: 500;
    text-decoration: none;
}
.btn-continue:hover{
    background-color: var(--secondary-color);
    color: var(--primary-color);
}
.btn-cancel{
    background-color: var(--mid-warning-color);
    color: var(--warning-color);
    padding: 5px 10px;
    border-radius: 10px;
    cursor: pointer;
    border: none;
    font-weight: 400;
    text-decoration: none;
}
.btn-cancel p{
    color: var(--warning-color);
}
.btn-cancel:hover{
    background-color: var(--warning-color);
    color: var(--text-color);
}
.action--links li a:hover{
    background-color: var(--secondary-color);
    color: var(--text-color);
    cursor: pointer;
}



.buttons-group{
    margin: auto;
    display: flex;
    justify-content: center;
    gap: 20px;
}
.right-buttons-group {
    display: flex;
    gap: 20px;
    flex-direction: flex;
    max-height: 50px;
}

.listed-links{
    display: flex;
    justify-content: center;
    margin: 20px;
    flex-direction: column;
}

.listed-links li{
    list-style: none;
    padding: 10px;
    margin: 10px;
    border-radius: 20px;
    transition: all 0.5s ease-in-out;
    background-color: var(--bg-color);
}
.listed-links li:hover{
    background-color: var(--font-color);
    color: var(--text-color);
    cursor: pointer;
}

.listed-links a{
    color: var(--font-color);
    font-size: 14px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-weight: 500;
}

.listed-links a:hover{
    color: var(--text-color);
}
.form-group a, .form-group p{
    color: var(--blue-color);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.form-group p:hover {
    text-decoration: underline;
}
.row{
    display: flex;
    justify-content: left;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
    text-align: left;
}
.row i {
    color: var(--font-color);
}
.all-contents {
    display: flex;
    flex-direction: column; /* Ensures content is stacked vertically */
    justify-content: center;
    margin-left: 0;
    width: 100%;/* Change to a larger percentage or use a fixed width */    margin: 20px auto; /* Centers the form and adds space above and below */
    padding: 20px; /* Adds padding inside the form for better spacing */
}
.action-elements {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}
.action-elements .right{
    margin-right: auto;
}

.action-elements .left{
    margin-left: auto;
}

.action-elements .center{
    margin: auto;
    text-align: center;
}


    .real-form {
        display: flex;
    }
    .form-row {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        padding: 10px;
        width: 100%;
    }
    .form-group {
        display: flex;
        flex-direction: column;
        margin: 5px;
    }
    .form-group label {
        font-size: 16px;
        font-weight: 600;
        color: var(--font-color);
        padding: 0;
        text-align: left;
    }

.hidden_input{
    display: none;
}

.next-btn{
    font-weight: 700;
    padding: 5px 20px;
    border-radius: 5px;
    cursor: pointer;
}

.next-btn:hover{
    color:white;
    background-color: var(--green-color);
}

.transfer-info{
    margin: 0;
    position: absolute;
    top: 30%;
    left: 50%;
    -ms-transform: translate(-50%, -30%);
    transform: translate(-50%, -30%);
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    min-width: 800px;
}

.user-info{
    line-height: 0.5;
}
.leave-elements{
    font-size: 16px;
    font-weight: 600;
    color: var(--font-color);
    padding: 0;
    text-align: left;
}
.border-bottom{
    border-top: 2px solid lightgray; 
    margin: 20px 0; 
    border-bottom: 2px solid lightgray;
}
/* Mobile Phones (600px to 767px) */
@media (max-width: 767px) and (min-width: 600px) {
    .real-form {
        width: 100%; /* Full width for mobile screens */
    }
    .form-row {
        display: block; /* Stack rows for mobile */
        gap: 20px;
        padding: 10px;
    }
    .form-group {
        margin: 10px 0; /* Increased spacing for mobile */
        
    }
    .form-group label {
        font-size: 12px; /* Smaller font for mobile */
    }
    .input-group-text{
        width: fit-content;
    }
    .input-group-text input{
        padding: 5px 10px;
        width: fit-content;
    }
    .input-group-text .icon{
        font-size: 20px;
    }
    .input-group-text select{
        padding: 5px;
        width: fit-content;
    }
}

/* Mobile Phones (0px to 600px) */
@media (max-width: 599px) {

    .form-group {
        margin: 10px 0; /* Increased spacing for mobile */
        
    }
    .form-group label {
        font-size: 12px; /* Smaller font for mobile */
    }
    .input-group-text{
        width: fit-content;
    }
    .input-group-text input{
        padding: 5px 10px;
        width: fit-content;
        background-color: transparent;
    }
    .input-group-text .icon{
        font-size: 20px;
    }
    .input-group-text select{
        padding: 5px;
        width: fit-content;
    }
    .real-form {
        width: 100%; /* Full width for mobile screens */
    }
    .form-row {
        display: block; /* Stack rows for mobile */
        gap: 20px;
        padding: 10px;
    }
    .input-group-text{
        width: 300px
    }
    .form-control input{
        width: 100%;
        overflow: hidden;
    }
    .form-control:focus{
        width: 100%;
    }
    legend{
        font-size: 1.2em;
    }
    .form-control{
        width: 100%;
    }
}
