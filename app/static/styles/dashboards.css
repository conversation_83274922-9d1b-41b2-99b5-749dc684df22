@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rowdies:wght@300;400;700&display=swap');
:root{
    --primary-color: #cce8e3;
    --secondary-color: #259E97;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #7d7d7d;
    --max-font-color: #afafaf;
    --shadow-color:rgba(0, 255, 221, 0.1);
    --warning-color: hsl(0, 100%, 50%);
    --semi-warning-color: #ff5e41;
    --white-color: #fff;
    --green-color: #3d963d;
    --blue-color: #1c6d99;
}

*{
    margin:0;
    padding: 0;
    border: none;
    outline: none;
    box-sizing: border-box; /* This is a CSS property that makes sure that padding and border are included in the element's total width and height. */
    font-family: "Poppins", sans-serif;
}
body{
    overflow-x: hidden;
    background-color: var(--background-color);
    min-height: 100vh;
}
.btn-custom {
    background-color: var(--secondary-color);
    color:var(--text-color);
    font-weight: bold;
    border-radius: 10px;
    padding: 10px;
    margin: 10px;
    text-decoration: none;
    margin: 0 auto;
}

.btn-custom:hover {
    background-color: var(--primary-color);
    color: var(--text-color);
    cursor: pointer;
}
.max-container{
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-rows: auto 1fr;
    width: 100%;
    min-height: 100vh;
}
.dashboard-header{
    grid-column: span 12;
    grid-row: span 1;
    z-index: 10;
    position: sticky;
    top: 0;
}

.header--wrapper{
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    gap: 0;
    align-items: center;
    height: 60px;
    margin: 0;
    width: 100%;

}
.logo_icon  {
    display: flex;
    justify-content: center;
    gap: 10px;
}
.sidebar {
    display:block; /* The element is treated as a block element, but it floats to the left or right side of its container. */
    width: 50px;
    padding: 0 1.7rem; /* top and bottom padding is 0, left and right padding is 1.7rem */
    color: var(--secondary-color);
    background-color:var(--primary-color);
    box-shadow: 0 0 10px var(--shadow-color);
    transition: all 0.5s linear;
    z-index: 2;
    position: sticky;
    scrollbar-color: var(--secondary-color) var(--primary-color) ;
    scrollbar-width: thin;
    scroll-padding-block-start: 100px;
    grid-column: span 1;
    left: 0;

}

.display{
    width: 250px;
    background-color: var(--text-color);
    transition: all 0.5s linear;
    padding: 0 0.5rem;
    color: var(--secondary-color);
    overflow: hidden;
    overflow-anchor: none;
    background-color:var(--secondary-color);
    box-shadow: 0 0 10px var(--shadow-color);
    position: sticky;
    z-index: 2;
    scrollbar-color: var(--primary-color) var(--secondary-color) ;
    scrollbar-width: thin;
    scroll-padding-block-start: 100px;
    grid-column: span 1;
    grid-row: span 11;
}
.main--content{
    grid-column: span 11;
    width: 100%;
    position: relative;
}
.footer{
    grid-column: span 12;

}
.header--contents{
    display: flex;
}
.header--contents i{
    color: var(--font-color);
    font-size: 24px;
}
.dynamic-title h1{
    color: var(--font-color);
    font-size: 1rem;
    font-weight: 500;
    margin: auto 0;
}
.logo {
    height: 90px; /* This property sets the height of the element. */
    font-weight: 700;
    font-size: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.netpipo_logo{
    padding: 10px;

}
.netpipo_logo img{
    width: 100px;
}
.display .logo{
    color: var(--white-color);
}
.menu {
    height: 100%;
    position: relative;
    list-style: none;
    padding: 0;
    overflow-y: auto;
}
.dashboard-footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    gap: 10px;
    bottom: 0px;
    padding: 5px;
    left: 0px;
    width: 100%;
    background-color: var(--secondary-color);
}
.dashboard-footer a{
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    color: var(--white-color);
    font-weight: 500;
    font-size: 14px;
    gap: 10px;

}
.menu li{
    display: flex;
}
.menu li span{
    display: none;  
}
.dashboard-footer a span{
    display: none;
}

.sidebar:hover{
    .menu li span{
        display: block;
        color: var(--secondary-color);
    }
    width: 250px;
    .menu li:hover{
        i,span{
            color: var(--white-color);
        };
        background-color: var(--secondary-color);
    }
    .menu li:hover{
        color: var(--white-color);
    }
    .dashboard-footer a span{
        display: flex;
    }
    .feature-items{
        background-color: var(--white-color);
        border-radius: 5px;
        padding: 10px;
        flex-direction: column;
        margin-bottom: 5em;
    }
    .feature-items a{
        color: var(--secondary-color);
        display: flex;
        justify-content: left;
        padding: 10px;
    }
    .feature-items a:hover{
        background-color: var(--secondary-color);
        color: var(--white-color);
        border-radius: 5px;
        .feature-items i{
            color: var(--white-color);
        }
    }
}

.display{
    .menu li span{
        display: block;
        color: var(--white-color);
    }
    .menu li:hover{
        background-color: var(--white-color);
        i,span{
            color: var(--secondary-color);
        }
    }
    .menu li span:hover, .menu li a:hover{
        color: var(--secondary-color);
    }
    .dashboard-footer{
        background-color: var(--white-color);
    }
    .dashboard-footer a{
        color: var(--secondary-color);
    }
    .dashboard-footer a span{
        color: var(--secondary-color);
    }
    .dashboard-footer a span{
        display: flex;
    }
    .feature-items{
        background-color: var(--white-color);
        border-radius: 5px;
        padding: 10px;
        flex-direction: column;
    }
    .feature-items a{
        color: var(--secondary-color);
        display: flex;
        justify-content: left;
        padding: 10px;
    }
    .feature-items a:hover{
        background-color: var(--secondary-color);
        color: var(--white-color);
        border-radius: 5px;
        .feature-items i{
            color: var(--white-color);
        }
    }
}
.menu a i {
    position: relative;
    font-size: 20px;
}
.drop-it{
    margin-left: auto;
}

.menu .active {
    background-color: var(--secondary-color);
}
.menu .active i{
    color: var(--text-color) !important;
}
.menu .active a{
    color: var(--text-color) !important;
}
.menu .active a:hover{
    color: var(--secondary-color) !important;
}
.menu .active:hover{
    background-color: var(--primary-color) !important;
}
.menu .active:hover i{
    color: var(--secondary-color) !important;
}
.menu a{
    color: var(--white-color);
    font-size: 1.1rem;
    text-decoration: none;
    justify-content: space-between;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-weight: 500;
}
.sidebar .menu a i{
    color: var(--secondary-color);
}

.menu a span{
    overflow: hidden; 
}
.clock-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 40px;
    font-style: italic;
    font-weight: bold;
    gap: 10px;
    font-size: 50px;
    position: relative;
    color: #0d7ebb;
    background-color: rgba(61, 200, 255, 0.493); /* Semi-transparent background */
    border-radius: 15px; /* Optional: Rounded corners */
    box-shadow: 0 4px 10px rgba(48, 224, 255, 0.829); /* Optional: Shadow for better visibility */
}
.clock-container i{
    font-size: 30px;
    color: #0d7ebb;
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e0f6ff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;

}
.clock-container h3{
    font-size: 14px;
    color: #0d7ebb;
    background-color: #e0f6ff;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2px 10px;
    position: absolute;
    top: 10px;
    left: 10px;
}

.feature-items {
    display: none;
    width: 100%;
}

.all-contents-payroll{
    display: none;
    opacity: 0;
    transform: translateY(-20px);
    width: 100%;
    background-color: var(--background-color);
    padding: 1rem;
    margin-left: 0;
    border-radius: 5px;
}


.display--notice {
    display: none;
}

 /*header-wrapper */
.header-title{
    display: flex;
    align-items: center;
    gap: 20px;

 }
.company--logo img{
    width: inherit;
    margin: auto;
    height: 50px;
    border: 1px solid var(--border-color);
    padding: 5px;
}
.header-details {
    margin-top: 20px;
}
.header-details h3{
    color: var(--font-color);
    font-size: 1.5rem;
    font-weight: 600;
}
.user--info{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    z-index: 10;

}
.user--info i{
    color: var(--white-color);
    margin-left: 10px;
    font-size: 24px;
}
.user-details{
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 0;
    width: 100%;
    padding: 0 10px 0 0;
    height: 10vh;
    box-sizing: border-box;
    float: left;
}
.user-details p{
    color: var(--secondary-color);
    font-size: 16px;
    font-weight: 500;
}
.user-details .user-name{
    font-weight: 700;
}
.user-details i {
    color: var(--secondary-color);
    font-size: 40px;
}
.search-bar{
    background-color: var(--background-color);
    border-radius: 10px;
    display: none;
    align-items: center;
    gap: 5px;
    padding: 5px 12px;
}
.user--name{
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: fit-content;
    background-color: var(--secondary-color);
    gap: 1rem;
    border-radius: 50px;
}
.user--name i{
    color: var(--white-color)!important;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    left: 0;
}
.user--role {
    margin: 0 10px;
    color: var(--secondary-color);
    font-weight: 700;
}

.user--name p{
    color: var(--white-color);
    font-size: 1rem;
    font-weight: 600;
    padding: 10px;
    
}
.search-bar input{
    border: none;
    background-color: var(--background-color);
    color: var(--font-color);
    padding: 10px;
}
.search-bar i:hover{
    color: var(--secondary-color);
    cursor: pointer;
    transform: scale(1.2,1.2);
    transition: 1s;
}
.many ul li {
    display: grid;
    grid-template-columns:repeat(auto-fit, minmax(10px, 1fr));
    grid-template-rows: auto;
    align-content: space-between;    
}
.card--heading{
    background-color: var(--primary-color);
    color: var(--secondary-color);
    font-weight: 600;
    font-size: 1em!important;
}
.many ul li .right{
    text-align: right;
}
.menu ul li .center{
    text-align: center;
}
.many ul .left{
    text-align: left;
}
/* card wrappers*/
.card--container {
    margin-top: 1rem;
    margin-right: 1rem;
}
.all-card--wrappers{
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: auto;
    gap: 10px;
    margin: 10px;
    
}
.card--wrapper-1{
    grid-column: 1;
}
.card--wrapper-1-limit{
    height: fit-content;
}
.card--wrapper-2{
    grid-column: 2 ;
}
.card--wrapper-3{
    grid-column: 1/span 2;
    grid-row: 2/ span 2;
}
.card--wrapper-4{
    grid-column: 3/ span 2;
    grid-row: 2/ span 2;
}
.card--wrapper-4-limit{
    grid-column: 5/ span 2;
    height: fit-content;
}
.card--wrapper-6{
    grid-column: 5 / span 2;
}
.card--wrapper-6-limit{
    display: none;
}
.card--wrapper-9{
    grid-column: 4 / -1;
    overflow-x: auto;
}
.card--wrapper-3-limit{
    grid-column: 2/ span 3;
    height: fit-content;
}
.global-card-wrapper { 
    background: var(--text-color);
    padding: 2rem;
    border-radius: 10px;
    margin-right: 2rem;
    width: 100%;
    height: fit-content;
    box-shadow: 0 4px 8px rgba(0, 255, 200, 0.1);
}
.top-card-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    padding: 2rem;
    border-radius: 10px;
    margin-right: 2rem;
    width: 100%;
    background-color: var(--text-color);
}
.top-card-wrapper .card--header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 2rem;
    width: 100%;
}
.card--header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 2rem;
}
.card--title{
    color: var(--mid-font-color);
    font-weight: 600;
}
.card--details{
    color: var(--mid-font-color);
    font-weight: 500;
}
.card--details--header{
    color: var(--mid-font-color);
    font-size: 1rem;
    font-weight: 500;
}
.card--digits{
    color: var(--font-color);
    font-size: 2rem;
    font-weight: 600;
}
.main-title{
    font-family: "Roboto", sans-serif;
    color: var(--font-color);
    font-size: 1.5rem;
    font-weight: 700;
}
.styled-character{
    font-family: "Rowdies", sans-serif;
    color: var(--font-color);
    font-size: 3rem;
    font-weight: 700;
}
.card--header i{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #25a38b;
    background-color: var(--primary-color);
    border-radius: 5px;
    padding: 10px;
    font-size: 32px;
}
.click{
    cursor: pointer;
    color: var(--secondary-color);
    text-decoration: none;
    padding: 10px;
    border-radius: 10px;
    background-color: var(--primary-color);
    animation: fadeIn 2s infinite;
}

@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}
.click:hover{
    background-color: var(--secondary-color);
    color: var(--text-color);
    animation: none;
}
/* Dynamic content */
.dynamic-content{
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    border-radius: 10px;
    padding: 1rem;
    margin: 0  auto;
}
.dynamic-content h1{
    color: var(--font-color);
    font-size: 1.5rem;
    font-weight: 500;
    text-align: left;
    padding: 10px;
}
.loading-icon{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    z-index: 1000;
}
.card--body{
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}
.card--body li{
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    background-color: var(--background-color);
    margin: 10px;
    font-size: smaller;
    width: 100%;
    padding: 10px;
}
.card--body li:hover{
    background-color: #e7e7e7;
}
.action--except{
    margin-top: 120px;
}
.action--links {
    display: none;
    justify-content: center;
    flex-direction: column;
    background-color: var(--primary-color);
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    position:absolute;
    top: 70px;
    right: 10px;
}
.action--links ul {
    list-style: none;
    padding: 0;
    background-color: var(--primary-color);
    border-radius: 10px;
    padding: 10px 0; 
}
.action--links ul  li a{
    color: var(--secondary-color) !important;
}
.action--links ul li a:hover{
    color: var(--text-color)!important;
}
.dynamic--buttons {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 20px;
}
/* grid cards containers */
.grid-cards-container{
    display: grid;
    grid-template-columns: repeat(3, minmax(10px, 1fr));
    grid-template-rows: repeat(3, 1fr);
    grid-gap: 20px;

}
.grid-cards-container > div{
    border-radius: 30px;
    padding: 30px;
    width: 100%;
    box-sizing: border-box;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 247, 255, 0.1);
}
.grid-cards-container > div > a > div > i{
    font-size: 50px;
    padding: 20px;
    background-color: #e4f4f7;
    border-radius: 30px;
    float: right;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    box-sizing: border-box;
    margin: auto;
    color: #6d8c9b;
}
.grid-cards-container > div > a {
    color: none;
    text-decoration: none;
}
.grid-cards-container > div > a:hover{
    opacity: 0.5;
}
.grid-digit{
    font-size: 50px;
    font-weight: 500;
    color: #919191;
}
.first-card{
    grid-area: 1 / 1 / 1 / 1;
    background-color: #d7f7d7;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: #9df39d;
}
.second-card{
    grid-area: 1 / 2 / 1 / 2;
    background-color: #d8edf8;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.third-card{
    grid-area: 1 / 3 / 1 / 3;
    border-bottom: 1px solid #c6e2de;
    border-radius: 0 !important;
    background-color: transparent!important;
}
.fourth-card{
    grid-area: 2 / 1 / 2 / 1;
    background-color: #fcdbdb;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.fifth-card{
    grid-area: 2 / 2 / 2 / 2;
    background-color: #f4f5d2;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.sixth-card{
    grid-area: 2 / 3 / 2 / 3;
    background-color: #dbe6fc;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dynamic--buttons a:hover {
    color: var(--primary-color);
    cursor: pointer;
    text-decoration: none;
}

.btn-edit{
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 0;
    color: var(--secondary-color);
    padding: 3px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    gap: 10px;
    font-size: 16px;
    border: 1px solid #e7e7e7;
}
.btn-image{
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 0;
    color: #25a38b;
    border-radius: 5px;
    text-decoration: none;
    border: 1px solid #e7e7e7;
    gap: 10px;
    padding: 0 5px;
    font-size: 12px;
    text-align: center;
}
.btn-image p{
    text-align: center;
}
.btn-image:hover{
    background-color: var(--primary-color);
    color: var(--secondary-color);
    font-weight: 600;
}
span:hover{
    color:var(--white-color);
    animation: shake 0.5s;
}

@keyframes shake{
    0%, 100% {
        transform: translate(1px, 1px) rotate(0deg);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translate(-1px, -2px) rotate(-1deg);
    }
    20%, 40%, 60%, 80% {
        transform: translate(-2px, 0) rotate(1deg);
    }
}


.btn-edit span{
    font-size: 16px;
}
.btn-edit:hover{
    background-color: var(--secondary-color);
    color: var(--text-color);
}
.dynamic--form{
    display: block;
    width: fit-content;
    background-color: var(--white-color);
    padding: 20px;
    border-radius: 10px;
    width: 100%;
    overflow-x: auto;
    height: 100%;
}
.dynamic--form h1{
    color: var(--font-color);
    font-size: 1.5rem;
    font-weight: 500;
    text-align: center;
    padding: 10px;
}
.phase p{
    color: var(--white-color);
    font-size: 0.7rem;
    font-weight: 600;
    background-color: #1c6d99;
    padding: 5px;
    border-radius: 5px;
}
.exception{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    resize: none;
}
#action-links{
    display: none;
}

.the-dots{
    display: flex;
    justify-content: center;
    align-items: center;
}
.btn-delete{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--semi-warning-color);
    color: var(--text-color);
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    gap: 10px
}
.btn-delete:hover{
    background-color: var(--warning-color);
    color: var(--text-color);
}
.btn-view{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--green-color);
    color: var(--white-color);
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    gap: 10px
}
.feedback--container a{
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 400;
}

.feedback--container i{
    font-size: 2rem;
    color: var(--semi-warning-color);
    animation: fadeIn 1s infinite;
}
.feedback--container i:hover{
    color: var(--text-color);
}
.table-buttons{
    display: flex;
    justify-content: center;
    gap: 20px;
}

.user-details{
    cursor: pointer;
}

.icon .action{
    display: none;
}
.right-sidebar{
    display: block;
    height: fit-content;
    width: inherit(user-details);
    background-color: var(--white-color);
    padding: 10px;
    margin: 20px 10px;
}
.footer{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white-color);
    color: var(--font-color);
    padding: 5px;
    border-radius: 5px;
    z-index: 10;
    position: fixed;
    bottom: 0;
    text-align: center;
    width: 100%;
}
.card--digits-big{
    color: var(--font-color);
    font-size: 2rem;
    font-weight: 500;
}
.ee_links{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
    width: 100%;
}
.ee_links a:hover{
    color: var(--text-color);
    background-color: var(--secondary-color);
    border-radius: 5px;
}
.ee_links a{
    color: var(--secondary-color);
    font-size: 0.8em;
    text-decoration: none;
    display: flex;
    gap: 0.5em;
    font-weight: 400;
    padding: 10px;
    background-color: var(--primary-color);
    border-radius: 5px;
    width: fit-content;

}
.attendance-status {
    list-style: none;
    padding: 0;
    margin: 0;
}

.attendance-status ul {
    display: flex;
    justify-content: space-around;
    padding: 0;
    margin: 0;
}

.attendance-status li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}
.attendance-status li a {
    color: inherit;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.attendance-status li:hover {
    opacity: 0.8;
    cursor: pointer;
    animation: popOutAndStay 1s forwards;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

}
@keyframes popOutAndStay {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1.1);
    }
}
.attendance-status i {
    font-size: 1.5rem;
}

.attendance-status .present {
    color: #4caf50; /* Green for Present */
    background-color: #e8f5e9; /* Light Green for Present */
}

.attendance-status .absent {
    color: #f44336; /* Red for Absent */
    background-color: #ffebee; /* Light Red for Absent */
}

.attendance-status .leave {
    color: #ffc107; /* Amber/Yellow for Leave */
    background-color: #fff8e1; /* Light Yellow for Leave */
}

.attendance-status .off {
    color: #6c6c6c; /* Gray for Off */
    background-color: #e5e5e5; /* Light Gray for Off */
}

.attendance-status .text p {
    margin: 0;
    font-size: 0.9rem;
}

.attendance-status .text p:first-child {
    font-weight: bold;
    font-size: 1rem;
}

.inside--subcontainer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    float: left
}

.inside--subcontainer a{
    color: var(--secondary-color);
    font-size: 0.8em;
    background-color: var(--primary-color);
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
}
.inside--subcontainer a:hover{
    color: var(--text-color);
    background-color: var(--secondary-color);
    border-radius: 5px;
}
.card--digits {
    font-size: 1rem;
    font-style: italic;
}
/*110%*/
@media (width > 1745px) {
    .sidebar {
        display: block; /* The element is treated as a block element, but it floats to the left or right side of its container. */
        top: 0;
        left: 0;
        bottom: 0;
        width: 6rem;
    } 
    .menu li{
        padding: 5px;
        margin: 2rem 0;
        border-radius: 5px;
        transition: all 0.5s ease-in-out;
    }
    .display{
        width: none;
        top: 0;
        left: 0;
        bottom: 0;
    }
    .dynamic-content{
        border: 1px solid var(--border-color);
        }
    
    .menu a{
        font-size: 1rem;
    }
    .logo{
        font-size: 25px;
    }
    .header-details h3{
        color: var(--font-color);
        font-size: 1.5rem;
        font-weight: 600;
    }
}
/* 125% */
@media (max-width : 1745px) and (min-width : 1536px ) {
.sidebar {
    display: block; /* The element is treated as a block element, but it floats to the left or right side of its container. */
    top: 0;
    left: 0;
    bottom: 0;
    width: 6rem;
} 
.display{
    width: max-content;
    top: 0;
    left: 0;
    bottom: 0;
}
.logo{
    font-size: 23px;
}
.header-details h3{
    color: var(--font-color);
    font-size: 1.3rem;
    font-weight: 600;
}
.card--header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}
.card--title{
    color: var(--mid-font-color);
    font-weight: 600;
    font-size: 1rem;
}
.dynamic-content{
    width: inherit;
    height: 37rem;
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid var(--border-color);
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;    
    margin: 0 auto;
}
.card--details{
    color: var(--mid-font-color);
    font-weight: 500;
    font-size: 0.8rem;
}
.menu li{
    padding: 5px;
    margin: 2rem 0;
    border-radius: 5px;
    transition: all 0.5s ease-in-out;
}
.menu a{
    font-size: 0.9rem;
}
.card--wrapper-1{
    grid-column: 1;
}
.card--wrapper-2{
    grid-column: 2 ;
}
.card--wrapper-3{
    grid-column: 1/span 2;
    grid-row: 2/ span 2;
}
.card--wrapper-4{
    grid-column: 3/ span 2;
    grid-row: 2/ span 2;
}
.card--wrapper-6{
    grid-column: 5 / span 2;
}
.card--wrapper-9{
    grid-column: 4 / -1;
}
}
/*150%*/
@media (max-width : 1536px) and (min-width : 1280px) { 
    /* Styles for screens between 1280px and 1536px */ 
    .sidebar {
        display: block; /* The element is treated as a block element, but it floats to the left or right side of its container. */
        top: 0;
        left: 0;
        bottom: 0;
        width: 6rem;
    }
    .menu li{
        padding: 5px;
        margin: 2rem 0;
        border-radius: 5px;
        transition: all 0.5s ease-in-out;
    }
    .logo{
        font-size: 20px;
    }
    .display{
        width: max-content;
        top: 0;
        left: 0;
        bottom: 0;
    }
    .header-details h3{
        color: var(--font-color);
        font-size: 1.2rem;
        font-weight: 600;
    }
    .card--header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 5px;
        margin-bottom: 1rem;
    }
    .card--title{
        color: var(--mid-font-color);
        font-weight: 600;
        font-size: 0.8rem;
    }
    .card--header i {
        color: #25a38b;
        background-color: var(--primary-color);
        border-radius: 5px;
        padding: 2px;
        font-size: 20px;
    }
    .card--body li{
        font-size: 12px;
    }
    .card--wrapper-1{
        grid-column: 1;
    }
    .card--wrapper-2{
        grid-column: 2 ;
    }
    .card--wrapper-3{
        grid-column: 1/span 2;
        grid-row: 2/ span 2;
    }
    .card--wrapper-4{
        grid-column: 3/ span 2;
        grid-row: 2/ span 2;
    }
    .card--wrapper-6{
        grid-column: 3 / span 2;
        grid-row: 3/ span 2;
    }
    .card--wrapper-9{
        grid-column: 4 / -1;
       
    }
    .attendance-status ul {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-row: auto;
        gap: 10px;
    }
}

/* 175% */
@media (width > 1097px) and (width < 1280px) {
    .sidebar {
        display: block; /* The element is treated as a block element, but it floats to the left or right side of its container. */
        top: 0;
        left: 0;
        bottom: 0;
        width: 6rem;
    } 
    .display{
        width: 15rem;
        top: 0;
        left: 0;
        bottom: 0;
    }
    .logo{
        font-size: 17px;
    }
    .header-details h3{
        color: var(--font-color);
        font-size: 1.0rem;
        font-weight: 600;
    }
    .card--header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 5px;
        margin-bottom: 1rem;
    }
    .card--title{
        color: var(--mid-font-color);
        font-weight: 600;
        font-size: 0.8rem;
    }
    .card--header i {
        color: #25a38b;
        background-color: var(--primary-color);
        border-radius: 5px;
        padding: 2px;
        font-size: 20px;
    }
    .card--body li{
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        background-color: var(--background-color);
        margin: 10px;
        font-size: 12px;
        width: 100%;
        padding: 10px;
    }
    .main--content{
        width: 80rem;
        /* padding left and right only*/
        
    }
    .card--details{
        color: var(--mid-font-color);
        font-weight: 500;
        font-size: 0.6rem;
    }
    .global-card-wrapper{
        padding: 1.5rem;
    }
    .card--digits{
        font-size: 1rem;
    }

    .search-bar input{
        padding: 5px;
    }
    .search-bar i{
        font-size: 1em;
    }
    .search-bar input{
        padding: 2px;
    }
    .user-details p{
        font-size: 0.7rem;
    }
    .user-details i {
        font-size: 1.5rem;
    }
    .card--details--header{
        font-size: 0.7rem;
    }
    .card--title{
        font-size: 0.6rem;
    }
    .card--body{
        font-size: 0.7rem;
    }
    .card--wrapper-9 .card--body .attendance-status ul{
        display: flex;
        flex-direction: column;
    }
    .menu li{
        padding: 5px;
        margin: 1.5rem 0;
        border-radius: 5px;
        transition: all 0.5s ease-in-out;
    }
    .menu a{
        font-size: 0.6rem;
    }
    
     .card--body li{
        font-size: 12px;
    }
    .card--wrapper-1{
        grid-column: 1;
    }
    .card--wrapper-2{
        grid-column: 2 ;
    }
    .card--wrapper-3{
        grid-column: 1/span 2;
        grid-row: 2/ span 2;
    }
    .card--wrapper-4{
        grid-column: 3/ span 2;
        grid-row: 2/ span 2;
    }
    .card--wrapper-6{
        grid-column: 3 / span 2;
        grid-row: 3/ span 2;
    }
    .card--wrapper-9{
        grid-column: 4 / 5;
       
    }
    .card--wrapper-3-limit{
        grid-row: 2;
        grid-column: 1/span 3;
    }
    .card--wrapper-4-limit{
        grid-column: 2/span 2;
    }
    .user--info{
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        position:relative;
        right: 18rem;
    }
    .attendance-status ul {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-row: auto;
        gap: 10px;
    }
}

/* 200% Tablets */ 
@media(max-width:1097px) and (min-width:768px){
    .sidebar{
        display: none
    }
    #clock{
        margin: 2em;
        font-size: 1.5rem;
    }
    .card--wrapper-1{
        grid-column: 1;
    }
    .card--wrapper-2{
        grid-column: 2 ;
    }
    .card--wrapper-3{
        grid-column: 1/span 2;
        grid-row: 3;
    }
    .card--wrapper-4{
        grid-column: 2/ span 2;
        grid-row: 2;
    }
    .card--wrapper-6{
        grid-column: 3/ span 3;
        grid-row: 3;
    }
    .card--wrapper-9{
        grid-column: 1 / 2;
        grid-row: 2
       
    }
    .card--body{
        display: flex;
        justify-content: center;
        gap: 10px
    }
    .many ul{
        width: 100%;
    }
    .card--header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
        margin-bottom: 1rem;
    }
    .card--title{
        width: 100%;
    }
    .company--logo img{
        width: 50px!important;
        margin: auto;
        height: 50px!important;
    }
    .main-title{
        font-size: 1rem;
    }
    .user-details{
        height: 100%;
    }
    .user-details p{
        font-size: 14px;
    }
    .feedback--container a{
        font-size: 14px;
        font-weight: 500;
        padding: 3px 10px;
    }
    .menu li{
        margin-bottom: 20px;
    }
    .ee_links a{
        font-size: 0.4em;
    }
    .display{
        height: inherit;
    }
    .dynamic-content{
        overflow-x: scroll;
    }
    .attendance-status ul {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-row: auto;
        gap: 10px;
    }
    .card--wrapper-3-limit{
        grid-row: 2;
        grid-column: 1/span 3;
    }
    .card--wrapper-4-limit{
        grid-column: 2/span 3;
    }
}
/* Phone */
@media(max-width:767px) and (min-width:100px){
    .sidebar{
        display: none;
    }
    #clock{
        margin: 2em;
        font-size: 1.5rem;
    }
    .max-container{
        width: 100%;
    }
    .card--wrapper-1 {
        grid-column: 1;
        grid-row: 1;
    }
    .card--wrapper-7{
        grid-row: 2;
    }
    .card--wrapper-8{
        grid-row:3;
    }
    .card--wrapper-3{
        grid-row: 5;
        grid-column: 1;
    }
    .card--wrapper-4{
        grid-row: 4;
        grid-column: 1;
    }
    .card--wrapper-9{
        grid-row: 8;
        grid-column: 1;
        
    }
    .card--wrapper-6{
        display: none;
    }
    .company--logo img{
        width: 40px;
        margin: auto;
        height: 40px;
    }
    .main-title{
        font-size: 0.6rem;
    }
    .user-details i{
        font-size: 20px;
    }
    .user-details p{
        font-size: 16px;
    }
    .feedback--container a{
        border: none
    }
    .header--wrapper{
        gap: 5px
    }
    .feedback--container a{
        font-size: 10px;
        font-weight: 400;
        
    }
    .feedback--container p{
        display: none;
    }
    .feedback--container i{
        font-size: 1rem;
    }
    .user-details{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        margin: 0;
        width: 100%;
        padding: 0 10px 0 0;
        height: 10%;
        box-sizing: border-box;
        float: left;
    }
    .user-details .user--data p{
        font-size: 20px;
        font-weight: 500;
    }
    .attendance-status ul {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-row: auto;
        gap: 10px;
    }
    .footer p{
        font-size: 12px;
    }
    body{
        display: grid;
        grid-template-columns: 1fr auto;
        gap:0px;
        height: fit-content;
    }
    .display{
        height: 100%;
    }
    .menu li{
        margin-bottom: 3rem;
    }
    .card--wrapper-3-limit{
        grid-row: 3;
        grid-column: 1/span 3;
    }
    .card--wrapper-4-limit{
        grid-column: 1/span 2;
        grid-row: 2;
    }
    .superv i{
        font-size: 0.1rem;
    }
    .grid-cards-container{
        display: flex;
        flex-wrap: wrap;
    }
}

.primary-button{
    color: #17B8A6!important;
    background-color: #DBECEF!important;
}
.primary-button:hover{
    color: #fff!important;
    background-color: #17B8A6!important;
}
.secondary-button{
    color: #083153!important;
    background-color: #dce8f8!important;
}
.secondary-button:hover{
    color: #fff!important;
    background-color: #243d52!important;
}

.inverted-primary-button{
    color: #ffffff!important;
    background-color: #17B8A6!important;
}
.inverted-primary-button:hover{
    color: #17B8A6!important;
    background-color: #DBECEF!important;
}
.inverted-secondary-button{
    color: #dce8f8!important;
    background-color: #243d52!important;
}
.inverted-secondary-button:hover{
    color: #083153!important;
    background-color: #dce8f8!important;
}

.table-button{
    padding: 2px 5px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    box-sizing: border-box;
    gap: 5px;
    text-align: center;
    margin: 0 auto;
}
.table-button:hover{
    cursor: pointer;
}

.filter-container{
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    padding: 5px 10px;
    border: 1px solid #e1e5e9;
    border-radius: 10px;
}