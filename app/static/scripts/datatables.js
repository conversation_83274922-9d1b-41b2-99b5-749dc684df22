// department datatable initialization
$(document).ready(function () {
    // check if the datatables exists before initializing it
    if ($.fn.DataTable.isDataTable('#departments-table')) {
        $('#departments-table').DataTable().destroy();
    }
    $('#departments-table').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "pageLength": 10
    });

    // employees datatable initialization
    $(document).ready(function () {
        // check if the DataTables is already initialized
        if ($.fn.DataTable.isDataTable('#employees_list')) {
            $('#employees_list').DataTable().destroy();
        }
        $('#employees_list').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });
    // leave datatable initialization
    $(document).ready(function () {
        if ($.fn.DataTable.isDataTable('#leave-table')) {
            $('#leave-table').DataTable().destroy();
        }
        $('#leave-table').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });

    // get leave applications datatable initialization
    $(document).ready(function () {
        // check if the table exists
        if (!$.fn.DataTable.isDataTable('#leave-application')) {
            $('#leave-application').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10
            });
        }
    });

    // get annual leave balances datatable initialization
    $(document).ready(function () {
        // check if the table exists
        if (!$.fn.DataTable.isDataTable('#employees_leave_balance_list')) {
            $('#employees_leave_balance_list').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10
            });
        }
    });

    // payroll summary datatable initialization
    $(document).ready(function () {
        // check if the DataTables is already initialized
        if ($.fn.DataTable.isDataTable('#payroll_summ')) {
            $('#payroll_summ').DataTable().destroy();
        }
        $('#payroll_summ').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });

    // list_employees datatable initialization
    $(document).ready(function () {
        // check if the DataTables is already initialized
        if ($.fn.DataTable.isDataTable('#list_employees')) {
            $('#list_employees').DataTable().destroy();
        }
        $('#list_employees').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });

    // attendance records datatable initialization
    $(document).ready(function () {
        const table = $('#attendance-table').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true,
            pageLength: 10
        });

        // Extend DataTables search for custom filters
        $.fn.dataTable.ext.search.push(function (settings, data, dataIndex) {
            const startDate = $('#client_start_date').val();
            const endDate = $('#client_end_date').val();
            // const employeeFilter = $('#filter_employee').val().toLowerCase();
            const employeeFilter = $(`#${settings.sInstance}`).val().toLowerCase(); // Dynamic searchbox filter

            const timeIn = data[2]; // assuming time_in is the third column (index 2)
            const recordDate = timeIn.split(' ')[0]; // extract date
            const employeeName = data[1].toLowerCase(); // assuming employee name is second column (index 1)

            // Date filtering
            if (startDate && recordDate < startDate) return false;
            if (endDate && recordDate > endDate) return false;

            // Employee name filtering
            if (employeeFilter && !employeeName.includes(employeeFilter)) return false;

            return true;
        });

        // Redraw table when filters change
        $('#client_start_date, #client_end_date, #filter_employee').on('input change', function () {
            table.draw();
        });
    });



    // payroll history datatable initialization
    $(document).ready(function () {
        // check if the DataTables is already initialized
        if ($.fn.DataTable.isDataTable('#payroll_history')) {
            $('#payroll_history').DataTable().destroy();
        }
        $('#payroll_history').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });

    // subscription payments datatable initialization
    $(document).ready(function () {
        // check if the DataTables is already initialized
        if ($.fn.DataTable.isDataTable('#payments-table')) {
            $('#payments-table').DataTable().destroy();
        }
        $('#payments-table').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "order": [[4, "desc"]], // Sort by date column (index 4) in descending order
            "dom": '<"top"<"dt-controls"<"dt-length"l><"dt-search"f>>>rt<"bottom"ip>', // Custom layout with better positioning
            "scrollX": true, // Enable horizontal scrolling
            "autoWidth": false, // Disable auto width calculation
            "language": {
                "search": "Search:",
                "lengthMenu": "_MENU_",
                "info": "Showing _START_ to _END_ of _TOTAL_ payments",
                "infoEmpty": "No payments found",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
});