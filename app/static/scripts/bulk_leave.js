/*===================HTML ELEMENTS==================*/
let tableTbody = document.querySelector("table tbody")
const search = document.getElementById("search-box")
/*===============VARIABLES=================*/
/**
 * @type {Array} initialData - An array to store the initial list of employees fetched from the API.
 */
let initialData = []
/**
 * @type {Array} searchedData - An array to store the filtered list of employees based on the search input.
 */
let searchedData = []
let leaveData = []
/**
 * @type {string}
 * @description A variable to store the CSRF (Cross-Site Request Forgery) token, 
 * which is used to secure requests by preventing unauthorized actions on behalf of a user.
 */
let CSRFToken;

/**
 * @type {Date} leaveStartDate - The starting date of the leave period.
 * @type {Date} leaveEndDate - The ending date of the leave period.
 */
let leaveStartDate, leaveEndDate;

/**
 * An object to store data for each employee related to their leave details.
 * The data includes the type of leave, start date, and end date of the leave.
 * 
 * @type {Object}
 */
const dataStore = {}



/*=============================Functions and Handlers============================*/
/**
 * Fetches the initial list of employees from the API.
 * @async
 * @function getInitialData
 * @returns {Promise<void>} A promise that resolves when the data is successfully fetched.
 */
async function getInitialData() {
    try {
        const response = await fetch('/employee_bulk_update');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        initialData = data.employees;
        CSRFToken = data.csrf_token;
        leaveData = data.leave_records
        console.log(leaveData, data.employees)
    } catch (error) {
        console.error('There was a problem with the fetch operation:', error);
    }
}

/**
 * Asynchronously fetches employee data based on a search parameter.
 *
 * This function sends a GET request to the `/search_employee` endpoint with the provided
 * search parameter. It retrieves a JSON response containing employee data and assigns
 * the `employees` property of the response to the `searchedData` variable.
 *
 * @async
 * @function getSearchedData
 * @param {string} param - The search parameter used to query employee data.
 */
async function getSearchedData(param) {
    try {
        const response = await fetch(`/search_employee?search-param=${encodeURIComponent(param)}`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        searchedData = data.employees;
    } catch (error) {
        console.error('There was a problem with the fetch operation:', error);
    }
}

/**
 * Asynchronously fetches initial data and renders it after a short delay.
 * 
 * @async
 * @function renderInitialData
 * @returns {Promise<void>} A promise that resolves once the data is rendered.
 */
async function renderInitialData() {
    await getInitialData()
    setTimeout(() => {
        renderData(initialData)
    }, 100)

}

/**
 * Renders the provided data to the UI.
 * @function renderData
 * @param {Array} data - The data to be rendered, typically a list of employees.
 * @returns {void}
 */
function renderData(data) {
    if (data.length === 0) {
        tableTbody.innerHTML = `<td colspan="6" style="text-align:center;">Nothing to show </td>`
        return;
    }
    tableTbody.innerHTML = ''
    data.forEach(employee => {
        tableTbody.innerHTML += `
            <tr data-id=${employee.employee_id}>
                <td><input type="checkbox" name="emp-checkbox"></td>
                <td>${employee.first_name} ${employee.last_name}</td>
                <td>${employee.email}</td>
                <td>
                    <select name="work_status" id="work_status">
                        <option value="">...</option>
                        <option value="leave">Leave</option>
                        <option value="off">Off</option>
                    </select>
                </td>
                <td><input type="date" name="time_off_begin_date" id="time_off_begin_date"></td>
                <td><input type="date" name="time_off_end_date" id="time_off_end_date"></td>
                <td class="num_of_days">-</td>
                <td class="active-offs">
                <p>
                ${employee?.additionals?.map(off => `<span style="border: 1px solid green;"> ${off.time_off_begin_date + "-" + off.time_off_end_date}</span>`).join('<br/>')} 
                </p>
                <span style="text-align: center;margin: auto 0"> ${employee?.additionals?.map(off =>
            findDateDifference(format_date(off.time_off_begin_date), format_date(off.time_off_end_date))
        ).reduce((acc, curr) => acc + curr)} Days</span>
                </td>
                <td class="confirm-col"><p>Confirm</p></td>
            </tr>
        `
    })

    attachEventListeners()
}


/**
 * Event listener for the search input box to enable real-time filtering of data.
 * @event input
 * @param {Event} e - The input event triggered when the user types in the search box.
 */
search.addEventListener("input", async (e) => {
    const param = e.target.value.trim()
    if (param.length === 0) {
        searchedData = []
        if (initialData.length === 0) {
            await getInitialData();
        }
        renderInitialData()
    } else {
        await getSearchedData(param)
        setTimeout(() => {
            renderData(searchedData);
        }, 200)
    }
})

function attachEventListeners() {
    const inputs = document.querySelectorAll("table input, select")
    const confirmBtns = document.querySelectorAll('.confirm-col p')
    const numOfDays = document.querySelector(".num_of_days")

    /*Populating input values*/
    inputs.forEach(input => {
        input.addEventListener("input", (e) => {
            console.log("Changing fields")
            // Find closest table row
            const row = input.closest("tr")
            // set object key being changed row data-id
            const d = dataStore[row.dataset.id]

            // Dynamically setting object properties
            if (d == undefined) {
                dataStore[row.dataset.id] = {}
                dataStore[row.dataset.id][e.target.name] = e.target.value
            } else if (Object.keys(d).length > 0) {
                dataStore[row.dataset.id] = { ...d, [e.target.name]: e.target.value }
            } else {
                // Fallback
                dataStore[row.dataset.id] = {}
                dataStore[row.dataset.id][e.target.name] = e.target.value
            }
            if (e.target.name === 'time_off_begin_date') {
                leaveStartDate = e.target.value
            }
            if (e.target.name === 'time_off_end_date') {
                leaveEndDate = e.target.value
                if (leaveEndDate < leaveStartDate) {
                    alert('End date can not be less than Start date')
                    input.value = ''
                    return
                }
            }
            // Calculate and populate number of days
            if (leaveStartDate && leaveEndDate) {
                tableTbody.querySelectorAll(".num_of_days").forEach(td => {
                    const rowId = td.closest("tr").dataset.id;
                    const rowKeys = dataStore[rowId] && Object.keys(dataStore[rowId])
                    if (rowKeys) {
                        if (rowKeys.includes("time_off_begin_date") && rowKeys.includes('time_off_end_date')) {
                            const total_days = findDateDifference(
                                dataStore[rowId]['time_off_begin_date'],
                                dataStore[rowId]['time_off_end_date']
                            )
                            if (rowId == td.closest("tr").dataset.id) {
                                td.innerHTML = total_days
                            }
                        }
                    }
                })
            }
        })
    })

    /*Applying leave*/
    confirmBtns.forEach(confirmBtn => {
        confirmBtn.addEventListener("click", (e) => {
            console.log("Want to set leave")
            // Check if that row checkbox is checked
            const rowId = e.target.closest("tr").dataset.id
            const isChecked = dataStore[rowId] && dataStore[rowId]["emp-checkbox"] === "on"
            if (!isChecked) {
                alert("Row is not checked")
                return
            }

            // Check all must submit values are there
            const requiredFields = ['work_status', 'time_off_begin_date', 'time_off_end_date'];
            const hasAllFields = requiredFields.every(field => field in dataStore[rowId]);

            if (!hasAllFields) {
                alert("All required fields must be filled out");
                return;
            }
            // Create form data
            const formData = new FormData()
            formData.append('employee_id', rowId);
            formData.append('work_status', dataStore[rowId]['work_status']);
            formData.append('time_off_begin_date', dataStore[rowId]['time_off_begin_date']);
            formData.append('time_off_end_date', dataStore[rowId]['time_off_end_date']);
            formData.append("csrf_token", CSRFToken);
            // Submit the request
            (async function () {
                try {
                    const response = await fetch("/record_leave_or_off", {
                        method: 'POST',
                        body: formData
                    })
                    if (response.ok) {
                        const data = await response.json()
                        alert(data.message)
                        tableTbody.innerHTML = ''
                        await getInitialData()
                        renderData(initialData)
                    } else {
                        const errorMsg = await response.json()
                        alert(errorMsg.error)
                    }
                } catch (error) {
                    console.log("Here in error")
                    console.log(error, error.data), error.response
                    if (error.response) {
                        alert(error.response.error)
                    } else {
                        alert("Try again, something went wrong")
                    }
                }
            }())
            // If successfull, remove employee from the list
        })
    })
}
function format_date(date_string) {
    return String(date_string).replaceAll("/", "-").split("-").reverse().join("-")
}
/**
 * Calculates the difference in days between two dates.
 *
 * @param {string|Date} start - The start date, either as a string or a Date object.
 * @param {string|Date} end - The end date, either as a string or a Date object.
 * @returns {number} The difference in days between the start and end dates.
 */
function findDateDifference(start, end) {
    const startDate = new Date(start)
    const endDate = new Date(end)
    const difference = endDate - startDate
    const differenceInDays = difference / (24 * 3600 * 1000)
    return differenceInDays
}


/**
 * Event listener for the DOMContentLoaded event to render the initial data when the page is fully loaded.
 * @event DOMContentLoaded
 * @param {Event} event - The DOMContentLoaded event triggered when the initial HTML document is completely loaded and parsed.
 */
window.addEventListener("DOMContentLoaded", renderInitialData)

