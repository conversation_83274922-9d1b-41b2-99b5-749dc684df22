// Timeout Logic Variables
let inactivityTimeout;
let countdownInterval;
const INACTIVITY_LIMIT = 60 * 1000 *30; // 30 minutes inactivity limit
const COUNTDOWN_DURATION = 60; // 60 seconds countdown

// Function to reset inactivity timer
function resetInactivityTimer() {
    clearTimeout(inactivityTimeout);
    clearInterval(countdownInterval);
    const timeoutPopup = document.getElementById('timeoutPopup');
    if (timeoutPopup) {
        timeoutPopup.style.display = 'none';
    }
    inactivityTimeout = setTimeout(showTimeoutPopup, INACTIVITY_LIMIT);
    console.log('Inactivity timer reset');
}

// Function to show timeout popup and start countdown
function showTimeoutPopup() {
    const timeoutPopup = document.getElementById('timeoutPopup');
    const countdownElement = document.getElementById('countdown');
    if (!timeoutPopup || !countdownElement) return;

    let timeLeft = COUNTDOWN_DURATION;
    countdownElement.textContent = timeLeft;
    timeoutPopup.style.display = 'flex';

    countdownInterval = setInterval(() => {
        timeLeft--;
        countdownElement.textContent = timeLeft;
        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            window.location.href = '/logout_user'; // Redirect to logout
            console.log('Session timed out - redirecting to /logout');
        }
    }, 1000);

    console.log('Timeout popup shown');
}

// Function to initialize sidebar state
function initializeSidebarState() {
    const sidebarContainer = document.querySelector('.sidebar-container');
    const dashboardContainer = document.querySelector('.dashboard');
    const footerContainer = document.querySelector('.footer');
    const toggleButton = document.getElementById('toggle-sidebar');
    const icon = toggleButton?.querySelector('i');

    if (sidebarContainer && dashboardContainer && footerContainer && icon) {
        const isSidebarHidden = localStorage.getItem('sidebarHidden') === 'true';
        console.log('Initializing Sidebar Hidden:', isSidebarHidden);
        if (isSidebarHidden) {
            sidebarContainer.classList.add('hidden');
            dashboardContainer.classList.add('sidebar-hidden');
            footerContainer.classList.add('sidebar-hidden');
            icon.classList.add('grey');
        }
    }
}

// Function to initialize fullscreen state
function initializeFullscreenState() {
    const fullscreenButton = document.getElementById('toggle-fullscreen');
    const icon = fullscreenButton?.querySelector('i');
    if (!fullscreenButton || !icon) return;

    const isFullscreenPreferred = localStorage.getItem('fullscreenPreferred') === 'true';
    console.log('Initializing Fullscreen Preferred:', isFullscreenPreferred);
    if (document.fullscreenElement) {
        icon.classList.remove('fi-rr-expand');
        icon.classList.add('fi-rr-compress');
        localStorage.setItem('fullscreenPreferred', 'true');
    } else if (isFullscreenPreferred) {
        document.documentElement.requestFullscreen().catch(err => {
            console.error(`Error attempting to enable fullscreen: ${err.message}`);
        });
        icon.classList.remove('fi-rr-expand');
        icon.classList.add('fi-rr-compress');
    } else {
        icon.classList.remove('fi-rr-compress');
        icon.classList.add('fi-rr-expand');
    }
}

// Function to initialize dark mode state
function initializeDarkModeState() {
    const darkModeButton = document.getElementById('toggle-dark-mode');
    const icon = darkModeButton?.querySelector('i');
    if (!darkModeButton || !icon) return;

    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    console.log('Initializing Dark Mode:', isDarkMode);
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        icon.classList.remove('fi-rr-moon');
        icon.classList.add('fi-rr-sun');
    } else {
        document.body.classList.remove('dark-mode');
        icon.classList.remove('fi-rr-sun');
        icon.classList.add('fi-rr-moon');
    }
}

// Sidebar Toggle
document.getElementById('toggle-sidebar')?.addEventListener('click', function () {
    const sidebarContainer = document.querySelector('.sidebar-container');
    const dashboardContainer = document.querySelector('.dashboard');
    const footerContainer = document.querySelector('.footer');
    const icon = this.querySelector('i');

    if (sidebarContainer && dashboardContainer && footerContainer && icon) {
        sidebarContainer.classList.toggle('hidden');
        dashboardContainer.classList.toggle('sidebar-hidden');
        footerContainer.classList.toggle('sidebar-hidden');
        icon.classList.toggle('grey');

        const isSidebarHidden = sidebarContainer.classList.contains('hidden');
        localStorage.setItem('sidebarHidden', isSidebarHidden);
        console.log('Sidebar Toggled - New State:', isSidebarHidden);
    }
});

// Fullscreen Toggle
document.getElementById('toggle-fullscreen')?.addEventListener('click', function () {
    const icon = this.querySelector('i');
    if (!icon) return;

    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.error(`Error attempting to enable fullscreen: ${err.message}`);
        });
        icon.classList.remove('fi-rr-expand');
        icon.classList.add('fi-rr-compress');
        localStorage.setItem('fullscreenPreferred', 'true');
    } else {
        document.exitFullscreen();
        icon.classList.remove('fi-rr-compress');
        icon.classList.add('fi-rr-expand');
        localStorage.setItem('fullscreenPreferred', 'false');
    }
    console.log('Fullscreen Toggled - New State:', localStorage.getItem('fullscreenPreferred'));
});

// Handle fullscreen change event
document.addEventListener('fullscreenchange', function () {
    const fullscreenButton = document.getElementById('toggle-fullscreen');
    const icon = fullscreenButton?.querySelector('i');
    if (!icon) return;

    if (document.fullscreenElement) {
        icon.classList.remove('fi-rr-expand');
        icon.classList.add('fi-rr-compress');
        localStorage.setItem('fullscreenPreferred', 'true');
    } else {
        icon.classList.remove('fi-rr-compress');
        icon.classList.add('fi-rr-expand');
        localStorage.setItem('fullscreenPreferred', 'false');
    }
    console.log('Fullscreen Changed - New State:', localStorage.getItem('fullscreenPreferred'));
});

// Dark Mode Toggle
document.getElementById('toggle-dark-mode')?.addEventListener('click', function () {
    const icon = this.querySelector('i');
    if (!icon) return;

    document.body.classList.toggle('dark-mode');
    const isDarkMode = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDarkMode);
    if (isDarkMode) {
        icon.classList.remove('fi-rr-moon');
        icon.classList.add('fi-rr-sun');
    } else {
        icon.classList.remove('fi-rr-sun');
        icon.classList.add('fi-rr-moon');
    }
    console.log('Dark Mode Toggled - New State:', isDarkMode);
});

// Responsive Sidebar Logic
function handleResponsiveSidebar() {
    const sidebarContainer = document.querySelector('.sidebar-container');
    const dashboardContainer = document.querySelector('.dashboard');
    const footerContainer = document.querySelector('.footer');
    const userDetails = document.querySelector('.user-det');
    const sidebarIcon = document.querySelector('.sidebar-container ~ .header .control-btn i');

    if (window.innerWidth <= 768 && sidebarContainer && dashboardContainer && footerContainer && userDetails && sidebarIcon) {
        userDetails.classList.add('phone');
        sidebarIcon.classList.remove('fi-rr-sidebar');
        sidebarIcon.classList.add('fi-rr-menu-burger');
        sidebarContainer.classList.add('hidden');
        dashboardContainer.classList.add('sidebar-hidden');
        footerContainer.classList.add('sidebar-hidden');
        localStorage.setItem('sidebarHidden', 'true');
        console.log('Responsive: Sidebar Hidden for Small Screen');
    } else if (window.innerWidth > 768 && sidebarContainer && dashboardContainer && footerContainer && userDetails && sidebarIcon) {
        const isSidebarHidden = localStorage.getItem('sidebarHidden') === 'true';
        if (!isSidebarHidden) {
            sidebarContainer.classList.remove('hidden');
            dashboardContainer.classList.remove('sidebar-hidden');
            footerContainer.classList.remove('sidebar-hidden');
            sidebarIcon.classList.remove('fi-rr-menu-burger');
            sidebarIcon.classList.add('fi-rr-sidebar');
            userDetails.classList.remove('phone');
        }
    }
}

// Other Functionality (Search, Dropdowns, Form Submission, Timeout)
document.addEventListener('DOMContentLoaded', function () {
    // Initialize states
    initializeSidebarState();
    initializeFullscreenState();
    initializeDarkModeState();
    handleResponsiveSidebar();

    // Start inactivity timer
    resetInactivityTimer();

    // Handle "Stay Logged In" button
    const stayLoggedInButton = document.getElementById('stayLoggedIn');
    stayLoggedInButton?.addEventListener('click', () => {
        resetInactivityTimer();
        console.log('User clicked Stay Logged In');
    });

    // Feature Toggles
    const featureToggles = document.querySelectorAll('.feature-content > li > a');
    featureToggles.forEach((toggle) => {
        toggle.addEventListener('click', function (e) {
            e.preventDefault();
            const featureContent = e.currentTarget.getAttribute('data-feature');
            const arrowIcon = e.currentTarget.querySelector('.fi-rr-angle-small-down');

            document.querySelectorAll('.feature-items').forEach((item) => {
                const itemArrow = item.previousElementSibling?.querySelector('.fi-rr-angle-small-down');
                if (item.getAttribute('data-feature') === featureContent) {
                    item.classList.toggle('active');
                    if (arrowIcon) {
                        arrowIcon.classList.toggle('fi-rr-angle-small-up');
                    }
                } else {
                    item.classList.remove('active');
                    if (itemArrow) {
                        itemArrow.classList.remove('fi-rr-angle-small-up');
                    }
                }
            });
        });
    });

    // Search Functionality
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');
    const menuItems = document.querySelectorAll('.feature-items a span');

    searchInput?.addEventListener('input', () => {
        const query = searchInput.value.trim().toLowerCase();
        searchResults.innerHTML = '';
        if (query.length === 0) {
            searchResults.style.display = 'none';
            return;
        }

        const matches = Array.from(menuItems).filter((item) =>
            item.textContent.toLowerCase().includes(query)
        );

        matches.forEach((item) => {
            const link = item.closest('a');
            const resultItem = document.createElement('a');
            resultItem.href = link.href;
            resultItem.textContent = item.textContent;
            resultItem.setAttribute('role', 'option');
            resultItem.addEventListener('click', () => {
                searchResults.style.display = 'none';
                searchInput.value = '';
            });
            searchResults.appendChild(resultItem);
        });

        searchResults.style.display = matches.length > 0 ? 'block' : 'none';
    });

    document.addEventListener('click', (e) => {
        if (!searchInput?.contains(e.target) && !searchResults?.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });

    // User Profile Dropdown
    const profileToggle = document.querySelector('.user-profile-toggle');
    const profileMenu = document.querySelector('.user-profile-menu');

    profileToggle?.addEventListener('click', (e) => {
        e.stopPropagation();
        const isExpanded = profileToggle.getAttribute('aria-expanded') === 'true';
        profileMenu.style.display = isExpanded ? 'none' : 'block';
        profileToggle.setAttribute('aria-expanded', !isExpanded);
        profileToggle.classList.toggle('fi-rr-angle-small-up');
    });

    document.addEventListener('click', (e) => {
        if (!profileToggle?.contains(e.target) && !profileMenu?.contains(e.target)) {
            profileMenu.style.display = 'none';
            profileToggle.setAttribute('aria-expanded', 'false');
            profileToggle.classList.remove('fi-rr-angle-small-up');
        }
    });

    // Switch Company Dropdown
    const switchCompanyToggle = document.querySelector('.switch-company-toggle');
    const switchCompanyForm = document.querySelector('.switch-company-form');

    switchCompanyToggle?.addEventListener('click', (e) => {
        e.stopPropagation();
        const isExpanded = switchCompanyToggle.getAttribute('aria-expanded') === 'true';
        switchCompanyForm.style.display = isExpanded ? 'none' : 'block';
        switchCompanyToggle.setAttribute('aria-expanded', !isExpanded);
        switchCompanyToggle.classList.toggle('fi-rr-angle-small-up');
    });

    document.addEventListener('click', (e) => {
        if (!switchCompanyToggle?.contains(e.target) && !switchCompanyForm?.contains(e.target)) {
            switchCompanyForm.style.display = 'none';
            switchCompanyToggle.setAttribute('aria-expanded', 'false');
            switchCompanyToggle.classList.remove('fi-rr-angle-small-up');
        }
    });

    // Form Submission
    const submitButtons = document.querySelectorAll('.submit-btn');
    submitButtons.forEach((button) => {
        button.removeEventListener('click', handleClick);
        button.addEventListener('click', handleClick);

        function handleClick(e) {
            e.preventDefault();
            const form = button.closest('form');
            if (!form) {
                console.error('No parent form found for button:', button);
                return;
            }

            if (form.checkValidity()) {
                const originalContent = button.innerHTML;
                const loader = document.createElement('div');
                loader.className = 'loader';
                loader.innerHTML = `<span></span><span></span><span></span>`;
                button.innerHTML = '';
                button.appendChild(loader);
                button.setAttribute('disabled', 'true');
                setTimeout(() => {
                    form.submit();
                });
            } else {
                form.reportValidity();
            }
        }
    });

    // Log initial states
    console.log('Sidebar Hidden:', localStorage.getItem('sidebarHidden'));
    console.log('Fullscreen Preferred:', localStorage.getItem('fullscreenPreferred'));
    console.log('Dark Mode:', localStorage.getItem('darkMode'));
});

// Handle window resize for responsive sidebar
window.addEventListener('resize', handleResponsiveSidebar);