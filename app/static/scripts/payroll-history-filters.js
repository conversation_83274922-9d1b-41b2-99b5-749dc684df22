/**
 * Advanced filtering for payroll history tables
 */

// Initialize advanced filtering for payroll history
function initPayrollHistoryFilters() {
    const payrollHistoryTable = $('#payroll_history').DataTable();
    
    // Apply filters when the filter button is clicked
    $('#apply-history-filters').on('click', function(e) {
        e.preventDefault();
        
        // Get filter values
        const nameFilter = $('#filter-employee-name').val().toLowerCase();
        const minDate = $('#filter-start-date').val();
        const maxDate = $('#filter-end-date').val();
        const minSalary = parseFloat($('#filter-min-salary').val()) || 0;
        const maxSalary = parseFloat($('#filter-max-salary').val()) || Infinity;
        
        // Clear existing filters
        payrollHistoryTable.search('').columns().search('').draw();
        
        // Apply custom filtering
        $.fn.dataTable.ext.search.push(
            function(settings, data, dataIndex) {
                // Only apply to payroll_history table
                if (settings.nTable.id !== 'payroll_history') return true;
                
                const employeeName = data[0].toLowerCase(); // Employee Name column
                const payDate = data[data.length - 1]; // Pay Date column (last or second to last column)
                const netSalary = parseFloat(data[data.length - 3].replace(/[^0-9.-]+/g, '')) || 0; // Net Salary column
                
                // Check if row matches all filters
                const nameMatch = nameFilter === '' || employeeName.includes(nameFilter);
                
                // Date range check
                let dateMatch = true;
                if (minDate && maxDate) {
                    const rowDate = new Date(payDate);
                    const startDate = new Date(minDate);
                    const endDate = new Date(maxDate);
                    dateMatch = rowDate >= startDate && rowDate <= endDate;
                }
                
                // Salary range check
                const salaryMatch = netSalary >= minSalary && netSalary <= maxSalary;
                
                return nameMatch && dateMatch && salaryMatch;
            }
        );
        
        // Redraw the table with filters applied
        payrollHistoryTable.draw();
        
        // Remove the filter function after drawing
        $.fn.dataTable.ext.search.pop();
    });
    
    // Reset filters when the reset button is clicked
    $('#reset-history-filters').on('click', function() {
        $('#history-filter-form')[0].reset();
        payrollHistoryTable.search('').columns().search('').draw();
    });
}

// Initialize advanced filtering for pending payroll
function initPendingPayrollFilters() {
    const pendingPayrollTable = $('#payroll_history').DataTable();
    
    // Apply filters when the filter button is clicked
    $('#apply-pending-filters').on('click', function(e) {
        e.preventDefault();
        
        // Get filter values
        const nameFilter = $('#filter-employee-name').val().toLowerCase();
        const statusFilter = $('#filter-status').val();
        const minDate = $('#filter-start-date').val();
        const maxDate = $('#filter-end-date').val();
        const minSalary = parseFloat($('#filter-min-salary').val()) || 0;
        const maxSalary = parseFloat($('#filter-max-salary').val()) || Infinity;
        
        // Clear existing filters
        pendingPayrollTable.search('').columns().search('').draw();
        
        // Apply custom filtering
        $.fn.dataTable.ext.search.push(
            function(settings, data, dataIndex) {
                // Only apply to payroll_history table
                if (settings.nTable.id !== 'payroll_history') return true;
                
                const employeeName = data[0].toLowerCase(); // Employee Name column
                const status = data[data.length - 2]; // Status column (second to last)
                const payDate = data[data.length - 3]; // Pay Date column (third to last)
                const netSalary = parseFloat(data[data.length - 5].replace(/[^0-9.-]+/g, '')) || 0; // Net Salary column
                
                // Check if row matches all filters
                const nameMatch = nameFilter === '' || employeeName.includes(nameFilter);
                const statusMatch = statusFilter === '' || status === statusFilter;
                
                // Date range check
                let dateMatch = true;
                if (minDate && maxDate) {
                    const rowDate = new Date(payDate);
                    const startDate = new Date(minDate);
                    const endDate = new Date(maxDate);
                    dateMatch = rowDate >= startDate && rowDate <= endDate;
                }
                
                // Salary range check
                const salaryMatch = netSalary >= minSalary && netSalary <= maxSalary;
                
                return nameMatch && statusMatch && dateMatch && salaryMatch;
            }
        );
        
        // Redraw the table with filters applied
        pendingPayrollTable.draw();
        
        // Remove the filter function after drawing
        $.fn.dataTable.ext.search.pop();
    });
    
    // Reset filters when the reset button is clicked
    $('#reset-pending-filters').on('click', function() {
        $('#pending-filter-form')[0].reset();
        pendingPayrollTable.search('').columns().search('').draw();
    });
}

// Initialize all filters when document is ready
$(document).ready(function() {
    // Check if we're on the payroll history page
    if ($('#payroll_history').length) {
        // Check if we're on the pending payroll page by looking for status column
        const hasPendingStatus = $('#payroll_history thead th:contains("Status")').length > 0;
        
        if (hasPendingStatus) {
            initPendingPayrollFilters();
        } else {
            initPayrollHistoryFilters();
        }
    }
});
