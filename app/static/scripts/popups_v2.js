// Popup Functions
function displayPayrollPopup() {
    document.getElementById('payroll-popup').style.display = 'block';
}

function closePayrollPopup() {
    document.getElementById('payroll-popup').style.display = 'none';
}

function displayPopup() {
    document.getElementById('popup').style.display = 'block';
}

function closePopup() {
    document.getElementById('popup').style.display = 'none';
}

// Toggle "More" Dropdown
document.addEventListener('DOMContentLoaded', function () {
    const dots = document.getElementById('action-dots');
    const actionLinks = document.getElementById('action-links');

    if (dots && actionLinks) {
        dots.addEventListener('click', function () {
            actionLinks.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function (event) {
            if (!dots.contains(event.target) && !actionLinks.contains(event.target)) {
                actionLinks.classList.remove('show');
            }
        });
    }
});

// Handle Template Links (Dynamic Loading, if needed)
document.querySelectorAll('.template-link').forEach(link => {
    link.addEventListener('click', function (event) {
        event.preventDefault();
        const url = this.getAttribute('data-template-url');
        // Example: Redirect or fetch content dynamically
        window.location.href = url; // Simple redirect; modify for AJAX if needed
    });
});