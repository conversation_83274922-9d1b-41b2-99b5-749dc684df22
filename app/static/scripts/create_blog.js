document.addEventListener('DOMContentLoaded', () => {
  const quill = new Quill('#editor', { theme: 'snow' });

  setupFormHandler(quill);
  setupUploadHandler();
  setupTagsSelection();
});

function setupFormHandler(quill) {
  const form = document.getElementById('post-form');

  form.addEventListener('submit', (event) => {
      event.preventDefault();

      const title = form.querySelector('#title').value;
      const content = quill.root.innerHTML;

      // Fetch selected categories and tags
      const categories = getSelectedOptions('categories-select');
      const tags = getSelectedOptions('tags-select');

      // Retrieve uploaded image path (if uploaded)
      const uploadStatus = document.getElementById('upload-status');
      const imagePath = uploadStatus.dataset.imagePath || null;

      console.log("Saved data: ", JSON.stringify({ title, content, categories, tags, imagePath }));

      // Submit the data to the backend
      fetch('/create_blog', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title, content, categories, tags, imagePath }),
      })
      .then(response => response.json())
      .then(data => {
          alert(data.message);
          window.location.href = '/view_blogs';
      })
      .catch(err => console.error(err));
  });
}

function getSelectedOptions(selectId) {
  const selectElement = document.getElementById(selectId);
  if (!selectElement) {
    console.error(`Select element with id ${selectId} not found`);
    return [];
  }
  const selectedOptions = Array.from(selectElement.selectedOptions);
  return selectedOptions.map(option => option.value);
}

function setupUploadHandler() {
  const uploadButton = document.getElementById('upload-button');

  uploadButton.addEventListener('click', () => {
      const fileInput = document.getElementById('file');
      const formData = new FormData();
      formData.append('file', fileInput.files[0]);

      fetch('/upload_image', { method: 'POST', body: formData })
          .then(response => response.json())
          .then(data => {
              const status = document.getElementById('upload-status');
              if (data.image_path) {
                  status.textContent = `Uploaded: ${data.image_path}`;
                  status.dataset.imagePath = data.image_path; // Store the image path
              } else {
                  status.textContent = 'Upload failed.';
                  status.dataset.imagePath = ''; // Clear the image path
              }
          })
          .catch(err => {
              console.error('Upload error:', err);
              const status = document.getElementById('upload-status');
              status.textContent = 'Upload failed.';
              status.dataset.imagePath = ''; // Clear the image path
          });
  });
}

function setupTagsSelection() {
  const tagsSelect = document.getElementById('tags-select');
  if (tagsSelect) {
    console.log('Tags select element found:', tagsSelect);
  } else {
    console.error('Tags select element not found');
  }
}
