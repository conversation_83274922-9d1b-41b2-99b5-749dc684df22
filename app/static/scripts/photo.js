const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const cameraSection = document.getElementById('cameraSection');
      const uploadSection = document.getElementById('uploadSection');
      const capturedImagePreview = document.getElementById('capturedImagePreview');
      const capturedImage = document.getElementById('capturedImage');
      let isCaptureMode = false;
      let capturedBlob = null;  // Store the captured image blob

      // Toggle between Upload and Capture modes
      function toggleUploadCapture() {
          isCaptureMode = !isCaptureMode;
          if (isCaptureMode) {
              startCamera();
              cameraSection.style.display = 'block';
              uploadSection.style.display = 'none';
              capturedImagePreview.style.display = 'none';
              document.querySelector(".btn-toggle").innerText = "Switch to Upload Image";
          } else {
              stopCamera();
              cameraSection.style.display = 'none';
              uploadSection.style.display = 'block';
              capturedImagePreview.style.display = 'none';
              document.querySelector(".btn-toggle").innerText = "Switch to Capture Image";
          }
      }

      // Start the camera
      function startCamera() {
          navigator.mediaDevices.getUserMedia({ video: true })
              .then(stream => {
                  video.srcObject = stream;
              })
              .catch(error => {
                  console.error("Error accessing camera:", error);
                  alert("Camera access is required to use the capture feature.");
              });
      }

      // Stop the camera
      function stopCamera() {
          let stream = video.srcObject;
          if (stream) {
              stream.getTracks().forEach(track => track.stop());
          }
      }

      // Capture the image from the camera
      function captureImage() {
          const context = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          context.drawImage(video, 0, 0, canvas.width, canvas.height);

          canvas.toBlob(blob => {
              capturedBlob = blob;  // Store the blob for upload
              displayCapturedImage(blob);
          }, 'image/png');
      }

      // Display the captured image in the preview section
      function displayCapturedImage(blob) {
          const imageUrl = URL.createObjectURL(blob);
          capturedImage.src = imageUrl;
          capturedImagePreview.style.display = 'block';  // Show the captured image preview
      }
      // Upload the captured image
    
      async function uploadCapturedImage() {
        if (capturedBlob) {
            const formData = new FormData(document.getElementById('uploadForm'));
            formData.set('image', capturedBlob, 'captured.png');  // Replacing the file input with the captured image

            try {
                const response = await fetch("{{ url_for('attendance.create_subject') }}", {
                    method: 'POST',
                    body: formData,
                });
                const result = await response.json();
                alert(result.result || "Image uploaded successfully!");

                // stop the camera  
                stopCamera(); 

            } catch (error) {
                console.error("Upload failed:", error);
            }
        } else {
            alert("No image captured to upload.");
        }
    }
