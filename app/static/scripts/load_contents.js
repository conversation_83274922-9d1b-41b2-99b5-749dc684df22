document.addEventListener('DOMContentLoaded', function () {
    loadDynamicContent();
});

function loadDynamicContent() {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', '/hr_dashboard', true);
    
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            // Inject the response into the container
            document.getElementById('container').innerHTML = xhr.responseText;
        }
    };
    
    xhr.send();
}
