function initializeTableFilter(config) {
    const {
        tableId,
        searchInputId,
        filters = [], // Array of filter configurations
        paginationId,
        toggleFiltersBtnId,
        advancedFiltersId,
        rowsPerPage = 10, // Default rows per page
        rowsPerPageInputId // Optional input for dynamic rows per page
    } = config;

    let currentPage = 1;
    let currentRowsPerPage = rowsPerPage; // Track current rows per page
    let allRows = [];
    let rowspanGroups = []; // Array of row groups affected by rowspan
    let rowspanMap = []; // Map to track cell values for each row and column

    // Initialize table and rows
    const table = document.getElementById(tableId);
    if (!table) {
        console.error(`Table with ID ${tableId} not found`);
        return;
    }
    const tbody = table.querySelector('tbody');
    if (!tbody) {
        console.error(`Table body for ${tableId} not found`);
        return;
    }
    const tfoot = table.querySelector('tfoot');
    allRows = Array.from(tbody.querySelectorAll('tr')); // Store all rows at initialization
    console.log(`Initializing filters for table: ${tableId}`);

    // Get filter elements
    const searchInput = document.getElementById(searchInputId);
    const toggleFiltersBtn = toggleFiltersBtnId ? document.getElementById(toggleFiltersBtnId) : null;
    const advancedFilters = advancedFiltersId ? document.getElementById(advancedFiltersId) : null;
    const rowsPerPageInput = rowsPerPageInputId ? document.getElementById(rowsPerPageInputId) : null;
    const filterElements = filters.reduce((acc, filter) => {
        acc[filter.id] = document.getElementById(filter.id);
        return acc;
    }, {});

    // Build rowspan map and groups
    function buildRowspanMapAndGroups() {
        rowspanMap = Array(allRows.length).fill().map(() => ({}));
        rowspanGroups = [];
        let currentGroup = [];
        let rowIndex = 0;

        while (rowIndex < allRows.length) {
            const row = allRows[rowIndex];
            let maxRowspan = 1;
            let currentColIndex = 0;
            const cells = row.querySelectorAll('td');

            // Process cells in the current row
            cells.forEach(cell => {
                const colspan = parseInt(cell.getAttribute('colspan') || 1);
                const rowspan = parseInt(cell.getAttribute('rowspan') || 1);

                // Skip columns already filled by previous rowspan
                while (rowspanMap[rowIndex][currentColIndex]) {
                    currentColIndex++;
                }

                // Assign cell value to current and spanned columns/rows
                for (let c = 0; c < colspan; c++) {
                    for (let r = 0; r < rowspan; r++) {
                        const targetRow = rowIndex + r;
                        if (targetRow < allRows.length) {
                            rowspanMap[targetRow][currentColIndex + c] = {
                                textContent: cell.textContent.trim(),
                                element: r === 0 ? cell : null // Only store element for the first row of rowspan
                            };
                        }
                    }
                }
                currentColIndex += colspan;
                maxRowspan = Math.max(maxRowspan, rowspan);
            });

            // Add the current row to the group
            currentGroup.push(rowIndex);

            // If the maximum rowspan is 1 or we've collected enough rows for the current rowspan
            if (maxRowspan === 1 || currentGroup.length >= maxRowspan) {
                rowspanGroups.push(currentGroup);
                currentGroup = [];
            }

            rowIndex++;
        }

        // Add any remaining rows to the last group
        if (currentGroup.length > 0) {
            rowspanGroups.push(currentGroup);
        }
    }

    // Helper function to get the cell content at a specific column index
    function getCellAtColumnIndex(rowIndex, targetColIndex) {
        if (rowspanMap[rowIndex] && rowspanMap[rowIndex][targetColIndex]) {
            return rowspanMap[rowIndex][targetColIndex];
        }
        return { textContent: '', element: null }; // Return empty if no cell found
    }

    // Helper function to parse formatted amount (e.g., remove commas and convert to number)
    function parseFormattedAmount(value) {
        if (!value) return 0;
        // Remove any non-numeric characters except the decimal point
        const cleanedValue = value.replace(/[^0-9.-]/g, '');
        const parsed = parseFloat(cleanedValue);
        return isNaN(parsed) ? 0 : parsed;
    }

    // Calculate totals for rows and return as an array
    function calculateTotals(rows) {
        const totals = Array(30).fill(0); // Initialize array for all columns, only sum numeric ones
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            for (let i = 6; i <= 29; i++) { // Columns: Basic Salary (6) to Net To Pay (29)
                const cellData = getCellAtColumnIndex(allRows.indexOf(row), i);
                const value = parseFormattedAmount(cellData.textContent);
                totals[i] += value;
            }
        });
        return totals;
    }

    // Create a totals row for tbody
    function createTotalsRow(totals) {
        const totalsRow = document.createElement('tr');
        totalsRow.className = 'totals-row'; // Class for styling, including bold text
        for (let i = 0; i < 31; i++) { // 31 columns based on table structure
            const cell = document.createElement('td');
            if (i === 0) {
                cell.textContent = ''; // Empty for "No" column
            } else if (i < 6) {
                cell.textContent = i === 1 ? 'Totals:' : ''; // "Totals:" in First Name column, empty for others
            } else if (i <= 29) {
                // Numeric columns: Basic Salary (6) to Net To Pay (29)
                cell.textContent = typeof Auxillary !== 'undefined' && Auxillary.format_amount
                    ? Auxillary.format_amount(totals[i])
                    : totals[i].toLocaleString(); // Fallback to basic formatting
            } else {
                cell.textContent = ''; // Empty for Actions column
            }
            totalsRow.appendChild(cell);
        }
        return totalsRow;
    }

    // Update totals in tfoot for payroll_summary table
    function updateFooterTotals(filteredRows) {
        if (tableId !== 'payroll_summary' || !tfoot) return; // Only apply to payroll_summary table

        const footerRow = tfoot.querySelector('tr');
        if (!footerRow) return;

        // Calculate totals
        const totals = calculateTotals(filteredRows);

        // Update footer cells (skip first 6 columns: No, First Name, Last Name, Category, Employee Type, Department)
        const footerCells = footerRow.querySelectorAll('td');
        for (let i = 6; i <= 29; i++) {
            if (footerCells[i]) {
                footerCells[i].textContent = typeof Auxillary !== 'undefined' && Auxillary.format_amount
                    ? Auxillary.format_amount(totals[i])
                    : totals[i].toLocaleString(); // Fallback to basic formatting
            }
        }
    }

    // Check if all filters are set to "All" or equivalent
    function isAllFiltersDefault() {
        const searchTerm = searchInput ? searchInput.value.trim() : '';
        if (searchTerm !== '') return false;

        return filters.every(filter => {
            const filterValue = filterElements[filter.id]?.value;
            return !filterValue || filterValue === '';
        });
    }

    // Populate select filter options dynamically
    function populateFilterOptions() {
        buildRowspanMapAndGroups(); // Ensure rowspanMap and rowspanGroups are up to date

        filters.forEach(filter => {
            if (filter.type === 'select') {
                const select = filterElements[filter.id];
                if (!select) return;
                const values = new Set();

                // Use allRows (original data rows) to populate filter options
                rowspanGroups.forEach(group => {
                    const rowIndex = group[0]; // Use the first row of the group
                    const cellData = getCellAtColumnIndex(rowIndex, filter.columnIndex);
                    if (cellData.textContent) {
                        values.add(cellData.textContent);
                    }
                });

                // Clear existing options (except "All")
                select.innerHTML = '<option value="">All</option>';
                values.forEach(value => {
                    if (value) { // Skip empty values
                        const option = document.createElement('option');
                        option.value = value;
                        option.textContent = value;
                        select.appendChild(option);
                    }
                });
            }
        });
    }

    // Toggle advanced filters
    if (toggleFiltersBtn && advancedFilters) {
        toggleFiltersBtn.addEventListener('click', () => {
            advancedFilters.classList.toggle('visible');
            toggleFiltersBtn.textContent = advancedFilters.classList.contains('visible')
                ? 'Hide Filters'
                : 'Show Filters';
        });
    }

    // Filter and display rows
    function updateTable() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';

        // Filter rowspan groups
        const filteredGroups = rowspanGroups.filter(group => {
            // Check if any row in the group matches the search term
            const matchesSearch = !searchTerm || group.some(rowIndex => {
                const row = allRows[rowIndex];
                const cells = row.querySelectorAll('td');
                return Array.from(cells).some(cell => 
                    cell.textContent.toLowerCase().includes(searchTerm)
                );
            });

            // Apply additional filters to the first row of the group
            const firstRowIndex = group[0];
            const matchesFilters = filters.every(filter => {
                const cellData = getCellAtColumnIndex(firstRowIndex, filter.columnIndex);
                const cellValue = cellData.textContent;
                const filterValue = filterElements[filter.id]?.value;

                if (filter.type === 'select') {
                    return filterValue === '' || cellValue === filterValue;
                } else if (filter.type === 'date') {
                    const cellDate = cellValue ? new Date(cellValue) : null;
                    const filterDate = filterValue ? new Date(filterValue) : null;

                    if (filter.dateType === 'start' && filterDate && cellDate) {
                        return cellDate >= filterDate;
                    } else if (filter.dateType === 'end' && filterDate && cellDate) {
                        return cellDate <= filterDate;
                    }
                    return true;
                }
                return true;
            });

            return matchesSearch && matchesFilters;
        });

        // Flatten groups to get rows for pagination
        const filteredRows = filteredGroups.flatMap(group => group.map(rowIndex => allRows[rowIndex]));

        // Pagination
        const totalRows = filteredRows.length;
        const totalPages = Math.ceil(totalRows / currentRowsPerPage);
        currentPage = Math.min(currentPage, totalPages || 1);

        // Slice rows for current page, ensuring entire groups stay together
        const startIndex = (currentPage - 1) * currentRowsPerPage;
        let paginatedRows = [];
        let currentCount = 0;
        let groupIndex = 0;

        while (groupIndex < filteredGroups.length && currentCount < startIndex + currentRowsPerPage) {
            const group = filteredGroups[groupIndex];
            if (currentCount >= startIndex) {
                paginatedRows.push(...group.map(rowIndex => allRows[rowIndex]));
            } else if (currentCount + group.length > startIndex) {
                // Include the entire group if any part of it falls within the page
                paginatedRows.push(...group.map(rowIndex => allRows[rowIndex]));
            }
            currentCount += group.length;
            groupIndex++;
        }

        // Update table body and renumber "No" column
        tbody.innerHTML = '';
        paginatedRows.forEach((row, index) => {
            const clonedRow = row.cloneNode(true); // Clone the row to avoid modifying original
            const cells = clonedRow.querySelectorAll('td');
            if (cells[0]) { // Update "No" column (index 0) to reflect filtered position
                cells[0].textContent = index + 1; // Start numbering from 1
            }
            tbody.appendChild(clonedRow);
        });

        // Handle tfoot and totals row for payroll_summary table
        if (tableId === 'payroll_summary') {
            if (!isAllFiltersDefault()) {
                // Hide tfoot and append totals row to tbody if any filter is not set to "All"
                if (tfoot) {
                    tfoot.style.display = 'none';
                }
                if (paginatedRows.length > 0) {
                    const totals = calculateTotals(paginatedRows); // Calculate totals for paginated rows
                    const totalsRow = createTotalsRow(totals);
                    tbody.appendChild(totalsRow);
                }
            } else {
                // Show tfoot and update totals if all filters are set to "All"
                if (tfoot) {
                    tfoot.style.display = ''; // Reset to default display (usually 'table-footer-group')
                    updateFooterTotals(allRows); // Use allRows to show totals for all data
                }
            }
        }

        // Update pagination controls
        updatePagination(totalPages);
    }

    // Update pagination controls
    function updatePagination(totalPages) {
        const pagination = document.getElementById(paginationId);
        if (!pagination) return;
        pagination.innerHTML = '';

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.textContent = 'Previous';
        prevButton.disabled = currentPage === 1;
        prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                updateTable();
            }
        });
        pagination.appendChild(prevButton);

        // Page numbers (show up to 10 pages)
        const maxPagesToShow = 10;
        let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(1, endPage - maxPagesToShow + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.textContent = i;
            pageButton.className = i === currentPage ? 'active' : '';
            pageButton.addEventListener('click', () => {
                currentPage = i;
                updateTable();
            });
            pagination.appendChild(pageButton);
        }

        // Next button
        const nextButton = document.createElement('button');
        nextButton.textContent = 'Next';
        nextButton.disabled = currentPage === totalPages;
        nextButton.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                updateTable();
            }
        });
        pagination.appendChild(nextButton);
    }

    // Event listener for dynamic rows per page
    if (rowsPerPageInput) {
        rowsPerPageInput.addEventListener('change', () => {
            const newValue = parseInt(rowsPerPageInput.value);
            currentRowsPerPage = (isNaN(newValue) || newValue <= 0) ? rowsPerPage : newValue;
            currentPage = 1; // Reset to first page on rows per page change
            updateTable();
        });
    }

    // Event listeners for filters and search
    if (searchInput) {
        searchInput.addEventListener('input', updateTable);
    }
    filters.forEach(filter => {
        const element = filterElements[filter.id];
        if (element) {
            element.addEventListener(filter.type === 'select' ? 'change' : 'change', updateTable);
        }
    });

    // Initial setup
    buildRowspanMapAndGroups(); // Build initial rowspan groups for filtering
    populateFilterOptions(); // Populate filter dropdowns
    updateTable();
}

// Initialize filters for specific tables
document.addEventListener('DOMContentLoaded', function() {
    // Configuration for employees_list table
    initializeTableFilter({
        tableId: 'employees_list',
        searchInputId: 'searchInput',
        filters: [
            { id: 'employeeTypeFilter', type: 'select', columnIndex: 9 },
            { id: 'departmentFilter', type: 'select', columnIndex: 19 },
            { id: 'hireDateStart', type: 'date', columnIndex: 20, dateType: 'start' },
            { id: 'jobTitleFilter', type: 'select', columnIndex: 18 },
            { id: 'hireDateEnd', type: 'date', columnIndex: 20, dateType: 'end' }
        ],
        paginationId: 'pagination',
        toggleFiltersBtnId: 'toggleFiltersBtn',
        advancedFiltersId: 'advancedFilters',
        rowsPerPage: 10, // Static rows per page
        rowsPerPageInputId: 'rowsPerPageInput' // Dynamic rows per page
    });

    // Configuration for attendance list table
    initializeTableFilter({
        tableId: 'attendance_list',
        searchInputId: 'attendanceSearchInput',
        filters: [
            { id: 'attendanceStatusFilter', type: 'select', columnIndex: 6 },
            { id: 'employeeFilter', type: 'select', columnIndex: 1 }
        ],
        paginationId: 'attendancePagination',
        toggleFiltersBtnId: 'attendanceToggleFiltersBtn',
        advancedFiltersId: 'attendanceAdvancedFilters',
        rowsPerPage: 10,
        rowsPerPageInputId: 'rowsPerPageInput' // Dynamic rows per page
    });

    // Configure filters for payroll summary table
    initializeTableFilter({
        tableId: 'payroll_summary',
        searchInputId: 'payrollSearchInput',
        filters: [
            { id: 'employeeTypeFilter', type: 'select', columnIndex: 4 }, // Employee Type (column 4)
            { id: 'departmentFilter', type: 'select', columnIndex: 5 },   // Department (column 5)
            { id: 'jobTitleFilter', type: 'select', columnIndex: 3 }      // Category/Job Title (column 3)
        ],
        paginationId: 'payrollPagination',
        toggleFiltersBtnId: 'payrollToggleFiltersBtn',
        advancedFiltersId: 'payrollAdvancedFilters',
        rowsPerPage: 15, // Static rows per page
        rowsPerPageInputId: 'rowsPerPageInput' // Dynamic rows per page
    });

    // Configure filters for inactive employees table
    initializeTableFilter({
        tableId: 'inactive_employees',
        searchInputId: 'inactiveSearchInput',
        filters: [
            { id: 'inactiveFilter', type: 'select', columnIndex: 10 },
            { id: 'inactiveDepartmentFilter', type: 'select', columnIndex: 10 },
            { id: 'jobTitleFilter', type: 'select', columnIndex: 19 }
        ],
        paginationId: 'inactiveEmployeesPagination',
        toggleFiltersBtnId: 'inactiveToggleFiltersBtn',
        advancedFiltersId: 'inactiveAdvancedFilters',
        rowsPerPage: 10,
        rowsPerPageInputId: 'rowsPerPageInput' // Dynamic rows per page
    });

    // Configure filters for approved advance requests table
    initializeTableFilter({
        tableId: 'approvedAdvanceRequests',
        searchInputId: 'approvedAdvanceSearchInput',
        filters: [
            { id: 'approverFilter', type: 'select', columnIndex: 3 },
            { id: 'approverRole', type: 'select', columnIndex: 4 },
            { id: 'requestStatusFilter', type: 'select', columnIndex: 6 }
        ],
        paginationId: 'approvedAdvancePagination',
        toggleFiltersBtnId: 'approvedAdvanceToggleFiltersBtn',
        advancedFiltersId: 'approvedAdvanceAdvancedFilters',
        rowsPerPage: 10,
        rowsPerPageInputId: 'rowsPerPageInput' // Dynamic rows per page
    });

    // Configure filters for timesheet table
    initializeTableFilter({
        tableId: 'timesheet',
        searchInputId: 'timesheetSearchInput',
        filters: [
            { id: 'timesheetlocationFilter', type: 'select', columnIndex: 2 },
        ],
        paginationId: 'timesheetPagination',
        toggleFiltersBtnId: 'timesheetToggleFiltersBtn',
        advancedFiltersId: 'timesheetAdvancedFilters',
        rowsPerPage: 10,
        rowsPerPageInputId: 'rowsPerPageInput' // Dynamic rows per page
    });
});