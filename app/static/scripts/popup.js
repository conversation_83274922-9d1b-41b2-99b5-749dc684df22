// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
  // Get elements
  const openPopupBtn = document.getElementById('openPopupBtn');
  const closePopupBtn = document.getElementById('closePopupBtn');
  const popup = document.getElementById('popup');

  // Open the popup
  openPopupBtn.addEventListener('click', () => {
      console.log('Open button clicked'); // For debugging
      popup.style.display = 'flex'; // Show the popup
  });

  // Close the popup
  closePopupBtn.addEventListener('click', () => {
      console.log('Close button clicked'); // For debugging
      popup.style.display = 'none'; // Hide the popup
  });

  // Close the popup when clicking outside of the popup content
  window.addEventListener('click', (e) => {
      if (e.target === popup) {
          console.log('Popup background clicked'); // For debugging
          popup.style.display = 'none'; // Hide the popup
      }
  });
});
