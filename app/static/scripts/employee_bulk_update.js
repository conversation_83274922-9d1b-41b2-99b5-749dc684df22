/* HTML Elements */
const openModalBtn = document.querySelector("#open-modal-btn")
const closeModalBtn = document.querySelector("#close-modal-btn")
const modal = document.querySelector("#employee-modal")
const opener = document.getElementById("opener")
const panelBtns = document.querySelectorAll(".panel-btn")
const panelBtnContainer = document.querySelector(".btn-container")
let responseMessage = document.querySelector(".message")
// const csrfToken = document.querySelector("#csrf_token").value;
const updateField = document.querySelector(".update-field")
const search = document.getElementById("search-param")
const tCols = document.querySelector(".table-columns")
const tData = document.querySelector(".table-data")
let tables;
let tableTrInputsAndSelect;
let allSetEditButtons;
let contentContainers;
let UpdateBtns;
let timeoutManager;
let initialData = []
let searchData = []
let departments = []
let currentRow;
let currentTab = 'personal'
let isSearching = false
let CSRFToken = ''
const employmentType = { permanent: "Permanent", casual: "Casual", consultant: "Consultant", second_employee: "Second Employee" }
/* For Managing update of single employee */
const allTabData = { info: [] };
let personalData, employeeD, allowanceData, bankingData, contactData;

/*Table columns*/
const tableColumns = {
    personal: ['First Name', 'Last Name', 'NID', 'Gender', 'DOB', 'RSSB', 'Marital Status', "Anual Leave Balance", "Extra Leave Days"],
    employee: ['First Name', 'Last Name', 'Employee TIN', 'Employee Type', 'Department', 'Attendance Applicable',
        'Active', 'Is BRD Sponsored', 'Job Title', 'Hire Date', 'Salary Type', 'Salary Amount'],
    allowance: ['First Name', 'Last Name', 'Transport Allowance', 'Housing Allowance', 'Communication Allowance', 'Over Time', 'Other Allowance'],
    banking: ['First Name', 'Last Name', 'Bank Name', 'Bank Account', 'Branch Name', 'Account Name'],
    contact: ['First Name', 'Last Name', 'Email', 'Phone']
}

/* Freezing First Name and Last Name columns on modal scroll*/
modal.addEventListener("scroll", () => {
    const cells = document.querySelectorAll("table tr td, table tr th");

    for (let i = 0; i < cells.length; i++) {
        if (cells[i].className === "set-edit-on") {
            cells[i].style.position = "sticky"
            cells[i].style.left = `$0px`
        }
        else if (cells[i].className.startsWith("immovable")) {
            cells[i].style.left = `${cells[i - 1].clientWidth}px`
            cells[i].style.position = "sticky"
        }
        else {
            continue
        }
    }
})
/* Tab navigation*/
panelBtnContainer.addEventListener("click", (e) => {
    const id = e.target.dataset.id;
    currentTab = id

    if (id) {
        // Remove active class to all & Add active class to clicked button
        panelBtns.forEach(btn => btn.classList.remove("active"))
        e.target.classList.add("active")
        // console.log("This is the searched data: ", searchData, isSearching)
        isSearching ? renderData(searchData) : renderData(allTabData['info'])
    }
})

async function getInitialData() {
    try {
        const response = await fetch('/employee_bulk_update');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        initialData = data.employees;
        CSRFToken = data.csrf_token;
        departments = data.departments;
        // console.log(initialData)
    } catch (error) {
        console.error('There was a problem with the fetch operation:', error);
    }
}


/**RENDERING */
async function renderInitialData() {
    await getInitialData()
    clearTimeout(timeoutManager)
    timeoutManager = setTimeout(() => {
        // when it is for the first time, render from db database
        // but after initial render, other will be handled by AllTabData for better frontend manipulation and control over data
        renderData(initialData)
    }, 100)

}

function renderData(employees) {
    tCols.innerHTML = '';
    tData.innerHTML = '';
    if (employees.length < 1) {
        employees = initialData
    }

    // Clear previous data for this tab
    allTabData['info'] = [];

    tCols.innerHTML = `<th>#</th>`;
    tableColumns[currentTab].forEach(col => tCols.innerHTML += `
        <th class="${col == 'First Name' ? 'immovable-1' : col == 'Last Name' ? 'immovable-1' : ''}">${col}</th>
    `);

    employees.forEach(employee => {
        // Store the employee data for this tab
        allTabData['info'].push(employee);

        // Set null values to empty string
        Object.keys(employee).forEach(key => {
            if (employee[key] === null) {
                employee[key] = '';
            }
        })
        personalData = `
            <tr data-id=${employee.employee_id}>
                <td class="set-edit-on">&#9881;</td>
                <td class="immovable-1"><input type="text" name="first_name"
                        value=${employee.first_name}></td>
                <td class="immovable-2"><input type="text" name="last_name"
                        value=${employee.last_name}></td>
                <td><input type="text" name="nid" value=${employee.nid}></td>
                <td>
                    <select name="gender" id="gender">
                        <option value=${employee.gender}>${employee.gender}</option>
                        <option value=${employee.gender == 'male' ? 'female' : 'male'}>
                            ${employee.gender == 'male' ? 'female' : 'male'}
                        </option>
                    </select>
                </td>
                <td>
                ${employee.birth_date ? `<input type="date" name="birth_date" value="${format_date(employee.birth_date)}">` : `<input type="date" name="birth_date" value="">`}
                
                </td>
                <td><input type="text" name="nsf" value=${employee.nsf}></td>
                <td>
                    <select name="marital_status" id="marital_status">
                        <option value=${employee.marital_status}>${employee.marital_status}</option>
                        <option
                            value=${employee.marital_status === 'single' ? 'married' : 'single'}>
                            ${employee.marital_status === 'single' ? 'married' : 'single'}
                        </option>
                    </select>
                </td>
                <td><input type="text" name="annual_leave_balance" value=${parseFloat(employee.annual_leave_balance).toFixed(2)}></td>
                <td><input type="text" name="extra_leave_days" value=${employee.extra_leave_days}></td>
                <td class="update-col"><button class="update-btn" disabled><i class="fi fi-rr-check-circle"></i></button></td>
            </tr>
        `
        employeeD = `
            <tr data-id="${employee.employee_id}">
                <td class="set-edit-on">&#9881;</td>
                <td class="immovable-1"><input type="text" name="first_name"
                        value='${employee.first_name}'></td>
                <td class="immovable-2"><input type="text" name="last_name"
                        value="${employee.last_name}"></td>
                <td><input type="text" name="employee_tin" value="${employee.employee_tin}"></td>
                <td>
                    <select name="employee_type" id="employee_type">
                        <option value="${employee.employee_type}">${employee.employee_type}</option>
                        ${Object.entries(employmentType).map(([key, value]) => key !== employee.employee_type && `<option value="${key}">${value}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <select name="department" id="department">
                        <option value="${employee.department}">${employee.department ? employee.department : '...'}</option>
                        ${departments.map((value) => value !== employee.department && `<option value="${value}">${value}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <select name="attendance_applicable" id="attendance_applicable">
                        <option value="${employee.attendance_applicable}">${employee.attendance_applicable}</option>
                        ${Object.entries({ 'yes': 'Yes', 'no': 'No' }).map(([key, value]) => key !== employee.attendance_applicable && `<option value="${key}">${value}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <select name="is_active" id="is_active">
                        <option value="${employee.is_active}">${employee.is_active}</option>
                        ${Object.entries({ 'yes': 'Yes', 'no': 'No' }).map(([key, value]) => key !== employee.is_active && `<option value="${key}">${value}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <select name="is_brd_sponsored" id="is_brd_sponsored">
                        <option value="${employee.is_brd_sponsored}">${employee.is_brd_sponsored}</option>
                        ${Object.entries({ 'yes': 'Yes', 'no': 'No' }).map(([key, value]) => key !== employee.is_brd_sponsored && `<option value="${key}">${value}</option>`).join('')}
                    </select>
                </td>
                <td><input type="text" name="job_title" value="${employee.job_title}"></td>
                <td>${employee.hire_date ? `<input type="date" name="hire_date" value="${format_date(employee.hire_date)}">` : '<input type="date" name="hire_date" value="" />'}                                          
                </td>
                <td>
                    <select name="salary_type" id="salary_type">
                        <option value="${employee.net_salary ? 'net_salary' : 'gross_salary'}">
                            ${employee.net_salary ? 'Net Salary' : 'Gross Salary'}
                        </option>
                        <option value="${employee.net_salary ? 'gross_salary' : 'net_salary'}">
                            ${employee.net_salary ? 'Gross Salary' : 'Net Salary'}
                        </option>
                    </select>
                </td>
                <td><input type="text" name="salary_amount" value="${formatAmount(employee.net_salary) | formatAmount(employee.gross_salary)}"></td>
                <td class="update-col"><button class="update-btn" disabled>Update</button></td>
            </tr>
        `
        allowanceData = `
            <tr data-id="${employee.employee_id}">
                <td class="set-edit-on">&#9881;</td>
                <td class="immovable-1"><input type="text" name="first_name"
                        value="${employee.first_name}"></td>
                <td class="immovable-2"><input type="text" name="last_name"
                        value="${employee.last_name}"></td>
                <td><input type="text" name="transport_allowance" value="${parseFloat(employee.transport_allowance).toFixed(2)}">
                </td>
                <td><input type="text" name="housing_allowance" value="${parseFloat(employee.housing_allowance).toFixed(2)}"></td>
                <td><input type="text" name="communication_allowance"
                        value="${parseFloat(employee.communication_allowance).toFixed(2)}"></td>
                <td><input type="text" name="over_time" value="${parseFloat(employee.over_time).toFixed(2)}"></td>
                <td><input type="text" name="other_allowance" value="${parseFloat(employee.other_allowance).toFixed(2)}"></td>
                <td class="update-col"><button class="update-btn" disabled>Update</button></td>
            </tr>
        `
        bankingData = `
            <tr data-id="${employee.employee_id}">
                <td class="set-edit-on">&#9881;</td>
                <td class="immovable-1"><input type="text" name="first_name"
                        value="${employee.first_name}"></td>
                <td class="immovable-2"><input type="text" name="last_name"
                        value="${employee.last_name}"></td>
                <td><input type="text" name="bank_name" value="${employee.bank_name}"></td>
                <td><input type="text" name="bank_account" value="${employee.bank_account}"></td>
                <td><input type="text" name="branch_name" value="${employee.branch_name}"></td>
                <td><input type="text" name="account_name" value="${employee.account_name}"></td>
                <td class="update-col"><button class="update-btn" disabled>Update</button></td>
            </tr>
        `
        contactData = `
            <tr data-id="${employee.employee_id}">
                <td class="set-edit-on">&#9881;</td>
                <td class="immovable-1"><input type="text" name="first_name"
                        value="${employee.first_name}"></td>
                    <td class="immovable-2"><input type="text" name="last_name"
                        value="${employee.last_name}"></td>
                    <td><input type="email" name="email" value="${employee.email}"></td>
                    <td><input type="text" name="phone" value="${employee.phone}"></td>
                    <td class="update-col"><button class="update-btn" disabled>Update</button></td>
            </tr>
        `
        const tabData = {
            personal: personalData,
            employee: employeeD,
            allowance: allowanceData,
            banking: bankingData,
            contact: contactData
        };

        construct_table_row(tabData[currentTab], employee);
    })
    attachEventListeners()

}

/**
 * Construct a table row element given the data and employee object
 * @param {string} data - The innerHTML of the table row
 * @param {object} employee - The employee data object
 */
function construct_table_row(data, employee) {
    const d = document.createElement('tr')
    d.dataset.id = employee.employee_id
    d.innerHTML = data
    d.querySelector('input[name="first_name"]').value = employee.first_name
    d.querySelector('input[name="last_name"]').value = employee.last_name
    tData.appendChild(d)
}

/**
 * Returns an object with an 'info' key that contains all of the employee's information.
 * If the employee is not found in the current tab's data, an empty object is returned.
 * @param {number} employeeId - The employee's ID.
 * @returns {object} - An object with an 'info' key that contains the employee's information.
 */
function getEmployeeAllData(employeeId) {
    const employeeData = {};

    // Find employee in this tab's data
    const tabData = allTabData['info'];
    const employeeInTab = tabData.find(emp => emp.employee_id === employeeId);

    if (employeeInTab) {
        employeeData['info'] = employeeInTab;
        delete employeeData['info']['salary_calculation_method']; // Remove this key if it exists
    } else {
        employeeData['info'] = {};
    }

    return employeeData;
}


function getSearchedData(param) {
    let data = initialData
    searchData = data.filter(emp => {
        return (
            emp.first_name && emp.first_name.toLowerCase().includes(param.toLowerCase()) ||
            emp.last_name && emp.last_name && emp.last_name.toLowerCase().includes(param.toLowerCase()) ||
            emp.email && emp.email && emp.email.toLowerCase().includes(param.toLowerCase())
        )
    })
    // console.log("This is the searched data: ", searchData, isSearching)
    renderData(searchData);
}

/* Search functionality */
search.addEventListener("input", async (e) => {
    const param = e.target.value.trim()
    console.log("param: ", param)
    if (param.length === 0) {
        isSearching = false
        currentRow = null
        if (initialData.length === 0) {
            await getInitialData();
        }
        renderInitialData()
    } else {
        isSearching = true
        currentRow = null
        getSearchedData(param)
    }
})



function attachEventListeners() {
    tables = document.querySelectorAll("table")
    tableTrInputsAndSelect = document.querySelectorAll("table tbody tr input, table tbody tr select")
    contentContainers = document.querySelectorAll(".content")
    allSetEditButtons = document.querySelectorAll(".set-edit-on")
    UpdateBtns = document.querySelectorAll(".update-btn")

    /*Disabling everything at initial load*/
    tData.querySelectorAll("input, select").forEach(input => input.disabled = true)

    /*Manipulating Information for Update*/
    tableTrInputsAndSelect.forEach(input => {
        input.addEventListener("change", (e) => {
            if (currentRow.dataset.id) {
                //Modify AllTabData info - for single object matching the current row id
                const currentEdited = getEmployeeAllData(currentRow.dataset.id)
                if (e.target.name === "salary_type") {
                    if (e.target.value === "net_salary") {
                        if (currentEdited.info['gross_salary']) {
                            currentEdited.info['net_salary'] = currentEdited.info['gross_salary'];
                        }
                        currentEdited.info['gross_salary'] = null;
                    } else if (e.target.value === "gross_salary") {
                        if (currentEdited.info['net_salary']) {
                            currentEdited.info['gross_salary'] = currentEdited.info['net_salary'];
                        }
                        currentEdited.info['net_salary'] = null;
                    }
                }
                else if (e.target.name === 'salary_amount') {
                    currentEdited.info['gross_salary']
                        ? currentEdited.info['gross_salary'] = e.target.value
                        : currentEdited.info['net_salary'] = e.target.value
                }
                else if (e.target.name === 'hire_date') {
                    currentEdited.info['hire_date'] = format_date(e.target.value)
                } else {
                    currentEdited.info[e.target.name] = e.target.value
                }
                // console.log(currentEdited)
            }
        })
    })

    /*Handling toggling edit on/off*/
    allSetEditButtons && allSetEditButtons.forEach(btn => {
        btn.addEventListener("click", async (e) => {
            // Step 1: Set all td content to "Off" and reduce opacity
            if (!currentRow) {
                allSetEditButtons.forEach(td => {
                    td.innerHTML = "Off";
                    td.classList.remove("editing");
                    td.classList.add("off-set-edit-on")
                })

                // Step 2: Set the clicked button to "Cancel"
                e.target.innerHTML = "Cancel";
                e.target.classList.add("editing");

                // Step 3: Enable input fields
                currentRow = btn.closest("tr")

                tableTrInputsAndSelect.forEach(input => {
                    if (input.closest("tr").dataset.id !== currentRow.dataset.id) {
                        input.disabled = true
                        input.style.opacity = 0.5

                    } else {
                        input.disabled = false
                        input.style.opacity = 1

                        /*Handle first name/last name change accross all tables*/
                        handleFirstLastNameAcrossTables(input, currentRow)
                    }
                })

                const updateBtns = tData.querySelectorAll('.update-btn');
                updateBtns.forEach(btn => {
                    //handle edit
                    if (btn.closest('tr').dataset.id !== currentRow.dataset.id) {
                        btn.disabled = true
                    } else {
                        btn.disabled = false
                        btn.addEventListener("click", (e) => {
                            updateOneEmployeeInfo(btn.closest('tr'))
                        })
                    }
                })
            } else {
                /*Revert to default when user clicks on cancel*/
                allSetEditButtons.forEach(btn => {
                    btn.innerHTML = "&#9881;"
                    btn.classList.remove("editing")
                    btn.classList.remove("off-set-edit-on")
                })

                currentRow = undefined
                e.target.innerHTML = "&#9881;"
                tData.querySelectorAll("input, select").forEach(input => input.disabled = true)

                /*Disable all update buttons*/
                const updateBtns = tData.querySelectorAll('.update-btn');
                updateBtns.forEach(btn => btn.disabled = true)
                await renderInitialData()
            }
        });
    });

    if (currentRow) {
        /*Keeping current being edite row enabled for editing*/
        tData.querySelectorAll("input, select").forEach(input => {
            if (input.closest("tr").dataset.id === currentRow.dataset.id) {
                input.disabled = false
            }
        })

        /*Allowing canceling */
        tData.querySelectorAll('.set-edit-on').forEach(btn => {
            /*Disabling all set-edit-on buttons - leave current being edited enabled only*/
            if (btn.closest('tr').dataset.id === currentRow.dataset.id) {
                btn.innerHTML = 'Cancel'
                btn.classList.add('editing')
                btn.classList.remove("off-set-edit-on")
            } else {
                btn.innerHTML = "Off";
                btn.classList.remove("editing");
                btn.classList.add("off-set-edit-on")
            }
            /*Disabling all inputs and select except current being edited*/
            tableTrInputsAndSelect.forEach(input => {
                if (input.closest("tr").dataset.id !== currentRow.dataset.id) {
                    input.disabled = true
                    input.style.opacity = 0.5
                }
            })
            /*Disabling all update buttons except current being edited*/
            const updateBtns = tData.querySelectorAll('.update-btn');
            updateBtns.forEach(btn => {
                if (btn.closest('tr').dataset.id !== currentRow.dataset.id) {
                    btn.classList.add("off-set-edit-on")
                } else {
                    btn.disabled = false
                    // Remove any existing event listeners
                    let newBtn = btn.cloneNode(true)
                    btn.parentNode.replaceChild(newBtn, btn)

                    // Add a single event listener
                    newBtn.addEventListener("click", (e) => {
                        updateOneEmployeeInfo(newBtn.closest("tr"))
                    })
                }
            })
        })
    }
}


/**
 * Displays the current update modal for the selected row in the employee table.
 * @params {HTMLElement} currentRow - The table row element representing the current employee data.
*/
function showCurrentUpdate(chosenRow) {
    /*Disable all rows*/
    if (!chosenRow) {
        return
    }
    tableTrInputsAndSelect.forEach(input => {

        if (input.closest("tr").dataset.id !== chosenRow.dataset.id) {
            input.disabled = true
            input.style.opacity = 0.5

        } else {
            input.disabled = false
            input.style.opacity = 1

            /*Handle first name/last name change accross all tables*/
            handleFirstLastNameAcrossTables(input, chosenRow)
        }
    })

    /*Disabling all update buttons except current row*/
    UpdateBtns.forEach(btn => {
        if (btn.closest("tr").dataset.id !== chosenRow.dataset.id) {
            btn.disabled = true
        } else {
            btn.disabled = false
        }
    })

    return;
}


/* Format amount */
function formatAmount(amount) {
    if (amount === null || amount === undefined) {
        return null
    }
    return amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
}
/* Format date */
function format_date(date_string) {
    return String(date_string).replaceAll("/", "-").split("-").reverse().join("-")
}

/* Get cookie value by cookie name from cookies */
function getCookieByName(name) {
    const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
    if (match) return match;
    return null;
}

/* Protect names fields from being changed accross tables */
function handleFirstLastNameAcrossTables(input, currentRow) {
    input.addEventListener("change", (e) => {
        if (input.name === "first_name" || input.name === "last_name") {
            const allFirstNames = document.getElementsByName("first_name")
            const allLastNames = document.getElementsByName("last_name")

            allFirstNames.forEach(currentInput => {
                if (currentInput.closest("tr").dataset.id === currentRow.dataset.id) {
                    if (e.target.name === "first_name") {
                        currentInput.value = e.target.value
                    }
                }
            })
            allLastNames.forEach(currentInput => {
                if (currentInput.closest("tr").dataset.id === currentRow.dataset.id) {
                    if (e.target.name === "last_name") {
                        currentInput.value = e.target.value
                    }
                }
            })
        }
    })
}

// Check if a string is a valid number not alpha number
function safeParseFloat(str) {
    const regex = /^-?\d+(\.\d+)?$/;
    return regex.test(str) ? parseFloat(str) : NaN;
}

/**
* Updates the information of a single employee.
*
* Preconditions:
* - Ensure that the required employee data is available and valid before calling this function.
*
* Postconditions:
* - The employee's information will be updated in the system.
* - The UI may be refreshed to display the updated information.
*/
function updateOneEmployeeInfo(currentTr) {
    // Get all employee attribute data
    if (!currentRow.dataset.id) {
        return
    }
    const forNumbersOnly = [
        "annual_leave_balance", "extra_leave_days", "salary_amount",
        "transport_allowance", "housing_allowance",
        "communication_allowance", "over_time", "other_allowance"
    ]
    let employeeToUpdate = getEmployeeAllData(currentTr.dataset.id).info
    // console.log("OHHHH", employeeToUpdate)

    // Programmatically create formData object
    const formData = new FormData()
    for (const key in employeeToUpdate) {
        if (employeeToUpdate.hasOwnProperty(key)) {
            // Check if key require to have numeric value
            if (forNumbersOnly.includes(key) && isNaN(safeParseFloat(employeeToUpdate[key]))) {
                responseMessage.style.border = '1px solid red'
                responseMessage.style.padding = '10px'
                responseMessage.textContent = "Please enter a valid number for " + key
                clearTimeout(timeoutManager)
                timeoutManager = setTimeout(() => {
                    responseMessage.style.border = ''
                    responseMessage.style.padding = ''
                    responseMessage.textContent = ''
                }, 4000)
                return;
            }
            if (!employeeToUpdate[key]) {
                key === 'extra_leave_days' ? formData.append(key, 0) : formData.append(key, '')
            }
            else if (key === 'birth_date' || key === 'hire_date') {
                formData.append(key, format_date(employeeToUpdate[key]).trim())
            } else {
                formData.append(key, String(employeeToUpdate[key]).trim())
            }
        }
    }

    //Appending CSRFToken
    formData.append("csrf_token", CSRFToken);
    const salaryAmount = employeeToUpdate['net_salary'] || employeeToUpdate['net_salary'] || employeeToUpdate['gross_salary'] || "0"
    const salary_type = employeeToUpdate['net_salary'] ? 'net_salary' : 'gross_salary'

    formData.append('salary_type', salary_type.trim());
    formData.append('salary_amount', salaryAmount.trim());

    // Create PUT request to the backend
    (async function () {
        responseMessage.classList.remove('success', 'error')
        try {
            responseMessage.innerHTML = ''
            const response = await fetch(`/update_employee/${currentRow.dataset.id}`, {
                method: 'POST',
                body: formData,
            })
            console.log(response.status, response.status, response.status in [400, 404])
            if ([200, 201].includes(response.status)) {
                const data = await response.json()
                employeeToUpdate = {}
                responseMessage.classList.add('success')
                responseMessage.innerHTML = `<p>${data.message}</p>`;
                responseMessage.style.display = 'flex'
                await renderInitialData()
            }
            else if ([400, 404, 500].includes(response.status)) {
                const data = await response.json()
                responseMessage.classList.remove('success')
                responseMessage.classList.add('error')

                if (typeof (data.messages) === 'object') {
                    const regex = new RegExp(/\[|\]|\'/g);
                    const errorWithKey = data.messages[0].replaceAll(regex, '').split(":");

                    if (errorWithKey.find(element => element.includes('is required'))) {
                        responseMessage.innerHTML = `<p>${errorWithKey[0]} is required</p>`
                    } else {
                        responseMessage.innerHTML = `<p>${errorWithKey[0]} ${errorWithKey[1].replace("\'", '')}</p>`;
                    }
                } else {
                    responseMessage.innerHTML = `<p>${data.message}</p>`;
                }
                responseMessage.style.display = 'flex'
            } else {
                responseMessage.style.border = '1px solid red'
                responseMessage.style.padding = '10px'
                responseMessage.textContent = 'Failed to update'
            }
        } catch (error) {
            console.log(error)
        } finally {
            clearTimeout(timeoutManager)
            timeoutManager = setTimeout(() => {
                responseMessage.style.display = 'none'
                responseMessage.textContent = ''
            }, 5000);
        }
    }())
    return;
}


window.addEventListener("DOMContentLoaded", renderInitialData)