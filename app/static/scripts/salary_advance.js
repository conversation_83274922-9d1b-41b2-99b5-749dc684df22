$(document).ready(function () {
    console.log("Salary Advance script loaded.");

    // Add installment dynamically
    $("#add-installment").click(function () {
        const count = parseInt($("#installment-amounts-count").val()) + 1;
        $("#installment-amounts-count").val(count);

        const newInstallmentRow = `
            <div class="installment-field input-group-text installment-row">
                <input type="number" name="installment_amounts-${count}" class="form-control installment-amount" placeholder="Installment Amount">
                <input type="date" name="due_dates-${count}" class="form-control installment-due-date" placeholder="Due Date">
            </div>
        `;
        $("#installment-amounts-container").append(newInstallmentRow);
    });
});
