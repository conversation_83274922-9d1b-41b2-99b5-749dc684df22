document.addEventListener('DOMContentLoaded', function() {
    var submitButton = document.querySelectorAll('button[type="submit"]');
    submitButton.forEach(button => {
        
        button.addEventListener('click', (event) => {
            console.log('Button clicked');
            // prevent multiple clicks
            if (button.disabled) return;
            button.disabled = true;
            const originalText = button.innerText;
            // change the text of the button
            button.innerText = 'Loading...';
            // Simulate a delay
            setTimeout(function() {
                button.disabled = false;
                button.innerText = originalText;
            }, 5000);
        });
    });
});
