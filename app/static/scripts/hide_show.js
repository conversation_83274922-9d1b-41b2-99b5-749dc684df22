// This file contains the JavaScript code that hides and shows the content based on the selected value in the dropdown menu.
function showContent() {
    /**
     * Hides all elements with the class 'general-content'.
     * Displays the content based on the selected value.
     */
    var contents = document.querySelectorAll('.general-content');
    contents.forEach(function(content) {
        content.style.display = 'none';
        
    });
    var selectedValue = document.getElementById('employee_type').value;
    if (selectedValue === 'consultant') {
        document.getElementById('consultant').style.display = 'block';
    } else {
        document.getElementById('other-content').style.display = 'block';
    }
    
}
window.onload = showContent; 