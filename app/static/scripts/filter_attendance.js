// Helper function to parse DD/MM/YYYY HH:MM:SS
function parseTimeInDate(timeInText) {
    if (!timeInText || timeInText === '-') return null;
    const match = timeInText.match(/^(\d{2})\/(\d{2})\/(\d{4})\s(\d{2}:\d{2}:\d{2})$/);
    if (!match) return null;
    const [, day, month, year, time] = match;
    return new Date(`${year}-${month}-${day}T${time}+02:00`); // CAT (UTC+2)
}

// Initialize filter functionality
jQuery(document).ready(function($) {
    // DOM elements
    const $periodSelect = $('#attFilterPeriodSelect');
    const $singleDateGroup = $('#attSingleDateGroup');
    const $quarterYearGroup = $('#attQuarterYearGroup');
    const $customDateGroup = $('#attCustomDateGroup');
    const $quarterYearSelect = $('#attFilterQuarterYear');
    const $toggleBtn = $('#attFilterToggleBtn');
    const $advancedFilters = $('#attFilterAdvanced');
    const $dateInput = $('#attFilterDate');
    const $startDateInput = $('#attFilterStartDate');
    const $endDateInput = $('#attFilterEndDate');
    const $searchInput = $('#attFilterSearchInput');
    const $table = $('#attendanceTable');
    const $tableBody = $table.find('tbody');
    const $pagination = $('#attFilterPagination');
    const $rowsPerPageInput = $('#rowsPerPageInput');
    // Collect initial table data from DOM
    let allRecords = [];
    let filteredRecords = [];
    let currentRowsPerPage = parseInt($rowsPerPageInput.val()) || 10;
    let currentPage = 1;
    
    

    // Print button event listener
    $('#printAttendanceBtn').on('click', function() {
        // Ensure all filtered records are rendered (ignore pagination)
        updateTable(filteredRecords, 1, filteredRecords.length);
        // Clear pagination controls during print
        $pagination.hide();
        setTimeout(() => {
            window.print();
            // Restore pagination and original table view
            $pagination.show();
            updateTable(filteredRecords, currentPage, currentRowsPerPage);
        }, 500); // Delay to ensure print dialog appears
    });
    function collectTableData() {
        allRecords = [];
        $tableBody.find('tr').each(function() {
            const $row = $(this);
            const cells = $row.find('td');
            const isFieldAttendance = cells.eq(6).text().trim() === 'Field Attendance';
            const record = {
                index: cells.eq(0).text().trim(),
                employee_name: cells.eq(1).text().trim(),
                time_in: isFieldAttendance ? cells.eq(2).text().trim() : cells.eq(2).text().trim(),
                clockin_location: cells.eq(3).text().trim(),
                time_out: cells.eq(4).text().trim(),
                clockout_location: cells.eq(5).text().trim(),
                status: cells.eq(6).text().trim(),
                total_duration: cells.eq(7).text().trim(),
                actions: cells.eq(8).html().trim()
            };
            allRecords.push(record);
        });
        filteredRecords = [...allRecords]; // Initialize filteredRecords with all records
    }
    collectTableData();

    // Toggle advanced filters visibility
    $toggleBtn.on('click', function() {
        $advancedFilters.toggle();
        $toggleBtn.text($advancedFilters.is(':visible') ? 'Hide Filters' : 'Show Filters');
    });

    // Populate quarter/year options
    function populateQuarterYearOptions(type) {
        $quarterYearSelect.empty();
        const currentYear = new Date().getFullYear();
        if (type === 'quarterly') {
            for (let year = currentYear - 5; year <= currentYear + 1; year++) {
                for (let q = 1; q <= 4; q++) {
                    const option = $('<option></option>').val(`${year}-Q${q}`).text(`Q${q} ${year}`);
                    $quarterYearSelect.append(option);
                }
            }
        } else if (type === 'yearly') {
            for (let year = currentYear - 5; year <= currentYear + 10; year++) {
                const option = $('<option></option>').val(year).text(year);
                $quarterYearSelect.append(option);
            }
        }
    }

    // Handle period selection
    $periodSelect.on('change', function() {
        const period = $(this).val();
        $singleDateGroup.hide();
        $quarterYearGroup.hide();
        $customDateGroup.hide();
        $dateInput.val('');
        $startDateInput.val('');
        $endDateInput.val('');

        if (['daily', 'weekly', 'monthly'].includes(period)) {
            $singleDateGroup.show();
        } else if (period === 'quarterly' || period === 'yearly') {
            $quarterYearGroup.show();
            populateQuarterYearOptions(period);
        } else if (period === 'custom') {
            $customDateGroup.show();
        }
        applyFilter();
    });

    // Filter attendance records
    function filterAttendance(records, period, dateInput, quarterYear, startDate, endDate, searchText) {
        return records.filter(record => {
            const timeField = record.status === 'Field Attendance' ? record.time_in : record.time_in;
            const recordDate = parseTimeInDate(timeField);
            if (!recordDate && period !== '') return false; // Skip invalid dates unless no period is selected

            // Search filter by employee_name or status
            const searchMatch = !searchText ||
                (record.employee_name && record.employee_name.toLowerCase().includes(searchText.toLowerCase())) ||
                (record.status && record.status.toLowerCase().includes(searchText.toLowerCase()));

            if (!searchMatch) return false;

            // If no period is selected, return true if search matches (allow search without filters)
            if (!period || period === '') {
                return true;
            }
            // Period-based filtering
            if (period === 'daily') {
                if (!dateInput) return false;
                const selectedDate = new Date(dateInput);
                return recordDate.toDateString() === selectedDate.toDateString();
            } else if (period === 'weekly') {
                if (!dateInput) return false;
                const selectedDate = new Date(dateInput);
                const weekStart = new Date(selectedDate);
                weekStart.setDate(selectedDate.getDate() - selectedDate.getDay());
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                return recordDate >= weekStart && recordDate <= weekEnd;
            } else if (period === 'monthly') {
                if (!dateInput) return false;
                const selectedDate = new Date(dateInput);
                return recordDate.getMonth() === selectedDate.getMonth() &&
                    recordDate.getFullYear() === selectedDate.getFullYear();
            } else if (period === 'quarterly') {
                if (!quarterYear) return false;
                const [year, quarter] = quarterYear.split('-Q');
                const quarterStartMonth = (parseInt(quarter) - 1) * 3;
                const quarterEndMonth = quarterStartMonth + 2;
                const start = new Date(year, quarterStartMonth, 1);
                const end = new Date(year, quarterEndMonth + 1, 0, 23, 59, 59, 999);
                return recordDate >= start && recordDate <= end;
            } else if (period === 'yearly') {
                if (!quarterYear) return false;
                return recordDate.getFullYear() === parseInt(quarterYear);
            } else if (period === 'custom') {
                if (!startDate || !endDate) return false;
                const start = new Date(startDate);
                const end = new Date(endDate);
                end.setHours(23, 59, 59, 999); // Include entire end day
                return recordDate >= start && recordDate <= end;
            }
            return false; // Default case for unmatched periods
        });
    }
    // Update table with filtered records and pagination
    function updateTable(records, page = 1, perPage = currentRowsPerPage) {
        $tableBody.empty();
        if (records.length === 0) {
            $tableBody.append('<tr style="text-align:center"><td colspan="9">No records found</td></tr>');
            $pagination.empty();
            return;
        }


        
        // Pagination
        const totalPages = Math.ceil(records.length / perPage);
        currentPage = Math.min(page, totalPages); // Ensure currentPage doesn't exceed totalPages
        const startIndex = (currentPage - 1) * perPage;
        const endIndex = startIndex + perPage;
        const paginatedRecords = records.slice(startIndex, endIndex);

        // Render table rows
        paginatedRecords.forEach((record, index) => {
            const row = `
                <tr>
                    <td>${parseInt(record.index)}</td>
                    <td>${record.employee_name}</td>
                    <td>${record.time_in}</td>
                    <td>${record.clockin_location}</td>
                    <td>${record.time_out}</td>
                    <td>${record.clockout_location}</td>
                    <td>${record.status}</td>
                    <td>${record.total_duration}</td>
                    <td>${record.actions}</td>
                </tr>`;
            $tableBody.append(row);
        });

        // Render pagination
        $pagination.empty();
        const $prev = $('<button>Previous</button>').prop('disabled', currentPage === 1).on('click', () => updateTable(records, currentPage - 1, perPage));
        const $next = $('<button>Next</button>').prop('disabled', currentPage === totalPages).on('click', () => updateTable(records, currentPage + 1, perPage));
        const $pageInfo = $(`<span class="primary-text">Page ${currentPage} of ${totalPages}</span>`);
        $pagination.append($prev, $pageInfo, $next);
    }

    // Apply filters and update table
    function applyFilter() {
        const period = $periodSelect.val();
        const dateValue = $dateInput.val();
        const quarterYear = $quarterYearSelect.val();
        const startDate = $startDateInput.val();
        const endDate = $endDateInput.val();
        const searchText = $searchInput.val();

        // Validate custom date range
        if (period === 'custom' && startDate && endDate && new Date(endDate) < new Date(startDate)) {
            alert('End date cannot be before start date.');
            filteredRecords = [];
            updateTable(filteredRecords, 1, currentRowsPerPage);

            return;
        }

        filteredRecords = filterAttendance(
            allRecords,
            period,
            dateValue,
            quarterYear,
            startDate,
            endDate,
            searchText
        );

        currentPage = 1; // Reset to first page on filter change
        updateTable(filteredRecords, currentPage, currentRowsPerPage);
    }

    // Event listener for rows per page
    $rowsPerPageInput.on('change', function() {
        currentRowsPerPage = parseInt($(this).val());
        currentPage = 1; // Reset to first page
        updateTable(filteredRecords, currentPage, currentRowsPerPage); // Use existing filteredRecords
    });

    // Event listeners for all inputs
    $periodSelect.on('change', applyFilter);
    $dateInput.on('change', applyFilter);
    $quarterYearSelect.on('change', applyFilter);
    $startDateInput.on('change', applyFilter);
    $endDateInput.on('change', applyFilter);
    $searchInput.on('input', applyFilter);
    updateTable(filteredRecords, currentPage, currentRowsPerPage);
});