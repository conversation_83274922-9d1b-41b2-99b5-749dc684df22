// Ensure this script is linked after the Chart.js library and the canvas element is available in the DOM
document.addEventListener('DOMContentLoaded', function () {
    console.log("DOM fully loaded and parsed");  // Debugging: Confirm the DOM is ready

    // Get the canvas element by its ID
    const canvas = document.getElementById('payrollChart');
    if (!canvas) {
        console.error("Canvas element with id 'payrollChart' not found");  // Debugging: Check if the canvas exists
        return;
    }
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error("Unable to get canvas context");  // Debugging: Check if context is available
        return;
    }
    console.log("Canvas context acquired");  // Debugging: Confirm the context is acquired

    // Ensure dynamic values are passed from Flask using Jinja template syntax
    const total_gross = {{ total_gross | tojson }};
    const total_pension_er = {{ total_pension_er | tojson }};
    const total_maternity_er = {{ total_maternity_er | tojson }};
    const total_rama_ee = {{ total_rama_ee | tojson }};
    const total_net_salary = {{ total_net_salary | tojson }};
    const total_payee = {{ total_payee | tojson }};
    const total_pension = {{ total_pension | tojson }};
    const total_maternity = {{ total_maternity | tojson }};
    const total_cbhi = {{ total_cbhi | tojson }};
    const total_insurance = {{ total_insurance | tojson }};

    console.log("Variables received:", {
        total_gross,
        total_pension_er,
        total_maternity_er,
        total_rama_ee,
        total_net_salary,
        total_payee,
        total_pension,
        total_maternity,
        total_cbhi,
        total_insurance
    });  // Debugging: Output the values passed from Flask

    // Sample data for the pie chart (you can replace these with dynamic data from your backend)
    const data = {
        labels: ['Gross Salaries', 'Payroll Expense', 'Net Salaries', 'Payroll Tax & Contributions'],
        datasets: [{
            label: 'Payroll Breakdown',
            data: [
                total_gross,  // Gross Salaries
                total_pension_er + total_maternity_er + total_rama_ee,  // Payroll Expense
                total_net_salary,  // Net Salaries
                total_payee + total_pension + total_maternity + total_cbhi + total_insurance  // Payroll Tax & Contributions
            ],
            backgroundColor: [
                'rgba(75, 192, 192, 0.2)',
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)'
            ],
            borderColor: [
                'rgba(75, 192, 192, 1)',
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)'
            ],
            borderWidth: 1
        }]
    };

    console.log("Chart data constructed:", data);  // Debugging: Output the chart data

    // Options for the chart
    const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',  // Position of the legend
            },
            tooltip: {
                enabled: true,  // Enable tooltips
            }
        }
    };

    console.log("Chart options configured:", options);  // Debugging: Output the chart options

    // Create the pie chart
    const payrollChart = new Chart(ctx, {
        type: 'pie',  // Define the chart type
        data: data,   // The data for the chart
        options: options  // Chart options
    });

    console.log("Pie chart successfully created");  // Debugging: Confirm chart creation
});
