document.addEventListener("DOMContentLoaded", function() {
    
    var xValues = ["Gross Salaries", "Insurance", "Pension", "Maternity"];
    
    // PAYE value
    var paye_ = document.querySelector('p.paye').innerText;
    var paye_value = paye_.replace(/,|RWF/g, '');

    // Net salary
    var net_salary = document.querySelector('p.net_value').innerText;
    net_salary = net_salary.replace(/,|RWF/g, '');
    if (net_salary){
        console.log('Net salary: ' + net_salary);
    } else {
        console.log('Net salary not found');
    }

    // Gross salary
    var gross_salary = document.querySelector('p.gross').innerText;
    gross_salary = gross_salary.replace(/,|RWF/g, '');
    if (gross_salary){
        console.log('Gross salary: ' + gross_salary);
    } else {
        console.log('Gross salary not found');
    }
    // Pension ER
    var pension_er = document.querySelector('p.pension_er').innerText;
    pension_er = pension_er.replace(/,|RWF/g, '');
    if (pension_er){
        console.log('Pension ER: ' + pension_er);
    } else {
        console.log('Pension ER not found');
    }
    // Maternity ER
    var maternityTotal = document.querySelector('p.maternity_total').innerText;
    // convert maternityTotal to integer
    maternity_total = maternityTotal.replace(/,|RWF/g, '');
    maternity_er = parseInt(maternity_total) / 2;
    if (maternity_er){
        console.log('Maternity ER: ' + maternity_er);
    } else {
        console.log('Maternity ER not found');
    }
    // Insurance ER
    var insuranceTotal = document.querySelector('p.insurance_total').innerText;
    insurance_total = insuranceTotal.replace(/,|RWF/g, '');
    insurance_er = parseInt(insurance_total / 2);
    if (insurance_er){
        console.log('Insurance ER: ' + insurance_er);
    } else {
        console.log('Insurance ER not found');
    }
    var yValues = [gross_salary, insurance_er, pension_er, maternity_er];

    // Access the root element where the CSS variables are defined
    var root = document.querySelector(':root');
    let rootStyles = getComputedStyle(root);

    // Access the CSS variables
    let primaryColor = rootStyles.getPropertyValue('--primary-color');
    let secondaryColor = rootStyles.getPropertyValue('--secondary-color');
    var midprimaryColor = rootStyles.getPropertyValue('--mid-primary-color');
    var greenColor = rootStyles.getPropertyValue('--green-color');
    var shadowColor = rootStyles.getPropertyValue('--shadow-color');
    var blueColor = rootStyles.getPropertyValue('--blue-color');
    var barColors = [secondaryColor, primaryColor, midprimaryColor, greenColor, blueColor];



    new Chart("myChart", {
        type: "pie",
        data: {
            labels: xValues,
            datasets: [{
                backgroundColor: barColors,
                data: yValues
            }]
        },
        options: {
            title: {
                display: true,
                text: 'Total Staff Cost Per Month',
                fontSize: 20,
                fontFamily: 'Poppins', // font from Google Fonts
                position: 'top',
                textAlign: 'center',
            },
            legend: {
                display: true,
                position: 'right',
                labels: {
                    fontColor: 'black',
                    fontFamily: 'poppins',
                    fontSize: 14,
                    padding: 20,
                },
            },
            layout: {
                padding: {
                    left: 30,
                    right: 0,
                    top: 0,
                    bottom: 0
                },
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        var dataset = data.datasets[tooltipItem.datasetIndex];
                        var meta = dataset._meta[Object.keys(dataset._meta)[0]];
                        var total = meta.total;
                        var currentValue = dataset.data[tooltipItem.index];
                        var percentage = parseFloat((currentValue / total * 100).toFixed(1));

                        // Add comma separator for thousands
                        var formattedValue = currentValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                        return formattedValue + ' RWF (' + percentage + '%)';
                    },
                    title: function(tooltipItem, data) {
                        return data.labels[tooltipItem[0].index];
                    }
                }
            },
            footer: {
                display: true,
                text: 'Total Payroll: ' + net_salary + ' RWF',
                fontFamily: 'poppins',
                fontSize: 14,
                padding: 20,
                textAlign: 'center',
                color: 'black',
                labelColor: 'black',
            }
        }
    });
});
