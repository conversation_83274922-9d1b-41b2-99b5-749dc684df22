function updateClock() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0'); // Ensures 2 digits
    const minutes = String(now.getMinutes()).padStart(2, '0'); // Ensures 2 digits
    const seconds = String(now.getSeconds()).padStart(2, '0'); // Ensures 2 digits
    const clockElement = document.getElementById("clock");
    clockElement.textContent = `${hours}:${minutes}:${seconds}`;
}

// Update the clock every second
setInterval(updateClock, 1000);

// Initialize the clock on page load
updateClock();
