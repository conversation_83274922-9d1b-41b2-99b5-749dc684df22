document.getElementById('toggle-switch').addEventListener('change', function() {
    const isChecked = this.checked;
    const smallPrice = document.getElementById('small-price');
    const mediumPrice = document.getElementById('medium-price');
    const enterprisePrice = document.getElementById('enterprise-price');
    const enterprisePlusPrice = document.getElementById('enterprise-plus-price');
    const toggleText = document.getElementById('toggle-text');
    const toggleText_2 = document.getElementById('toggle-text-2');
    const yearlyText = document.querySelectorAll('.yearly-text');
    if (isChecked) {
        yearlyText.forEach((text) => {
            text.textContent = '/year';
        });
        smallPrice.textContent = ' RWF 468,000 ';
        mediumPrice.textContent = ' RWF 828,000 ';
        enterprisePrice.textContent = ' RWF 1,188,000 ';
        enterprisePlusPrice.textContent = ' RWF 1,668,000 ';
        toggleText_2.style.color = '#25a38b';
        toggleText_2.style.fontWeight = 'bold';
        toggleText_2.style.backgroundColor = '#cce8e3';
        toggleText_2.style.padding = '5px 10px';
        toggleText_2.style.borderRadius = '10px';
        // undo the changes made by the other toggle
        toggleText.style.color = '';
        toggleText.style.fontWeight = '';
        toggleText.style.backgroundColor = '';
        toggleText.style.padding = '';
        toggleText.style.borderRadius = '';
    } else {
        yearlyText.forEach((text) => {
            text.textContent = '/mo';
        });
        smallPrice.textContent = ' RWF 45,630 ';
        mediumPrice.textContent = ' RWF 80,730  ';
        enterprisePrice.textContent = ' RWF 115,830 ';
        enterprisePlusPrice.textContent = ' RWF 162,630  ';
        toggleText.style.color = '#25a38b';
        toggleText.style.fontWeight = 'bold';
        toggleText.style.backgroundColor = '#cce8e3';
        toggleText.style.padding = '5px 10px';
        toggleText.style.borderRadius = '10px';
        // undo the changes made by the other toggle
        toggleText_2.style.color = '';
        toggleText_2.style.fontWeight = '';
        toggleText_2.style.backgroundColor = '';
        toggleText_2.style.padding = '';
        toggleText_2.style.borderRadius = '';

    }
});
