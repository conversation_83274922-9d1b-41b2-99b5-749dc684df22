document.addEventListener("DOMContentLoaded", function () {
    const popupForm = document.getElementById("subscription-popup-form");
    const footerForm = document.getElementById("subscription-footer-form");
    const popup = document.getElementById("subscription-popup");
    const closeButton = document.getElementById("close-popup");

    if(!sessionStorage.getItem("popupShown")) {
        setTimeout(() => {
            document.getElementById("subscription-popup").classList.remove("hidden");
            sessionStorage.setItem("popupShown", true);
        }, 3000);
    }

    // Close popup on button click
    closeButton.addEventListener("click", function () {
        popup.classList.add("hidden");
    });

    popupForm.addEventListener("submit", async function (event) {
        event.preventDefault();

        const emailInput = document.getElementById("popup-email");
        const email = emailInput.value.trim();

        if (!email) {
            alert("Please enter a valid email address.");
            return;
        }
        try {
            const response = await fetch("/subscribe", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (response.ok) {
                alert(data.message);
                emailInput.value = ""; // Clear input after successful subscription
                popup.classList.add("hidden"); // Hide popup on successful subscription
            } else {
                alert(data.error || "Something went wrong. Please try again.");
            }
        } catch (error) {
            alert("Failed to subscribe. Please try again later.");
        }
    });

    footerForm.addEventListener("submit", async function (event) {
        event.preventDefault();

        const emailInput = document.getElementById("footer-email");
        const email = emailInput.value.trim();

        if (!email) {
            alert("Please enter a valid email address.");
            return;
        }
        try {
            const response = await fetch("/subscribe", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (response.ok) {
                alert(data.message);
                emailInput.value = ""; // Clear input after successful subscription
            } else {
                alert(data.error || "Something went wrong. Please try again.");
            }
        } catch (error) {
            alert("Failed to subscribe. Please try again later.");
        }
    });
});
