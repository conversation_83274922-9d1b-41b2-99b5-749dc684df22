document.addEventListener('DOMContentLoaded', function () { // wait for the page to load
    var dynamicData = document.querySelector('.dynamic-content');
    var role = document.querySelector('.role');
    dynamicData.style.display = 'none';
    // element p content as a child of  role class
    var roleContent = role.querySelector('p');
    // get the role content
    var roleText = roleContent.textContent;
    // get the role text
    console.log('Role:', roleText);

    console.log('Dynamic content and flash message containers initialized.');

    function attachListeners() {
        var templateLinks = document.querySelectorAll('.template-link');
        console.log('Attaching click listeners to template links.');

        templateLinks.forEach(function (link) {
            // Remove existing listeners before attaching new ones
            link.removeEventListener('click', handleTemplateLinkClick);
            link.addEventListener('click', handleTemplateLinkClick);
        });
    }

    function handleTemplateLinkClick(event) {
        event.preventDefault();
        console.log('Template link clicked:', this);
        var allCardWrappers = document.querySelector('.all-card--wrappers');
        var dynamicContainer = document.querySelector('.dynamic-container');
        allCardWrappers.style.display = 'none';
        // dynamicContainer.style.display = 'none';
        dynamicData.style.display = 'block';
        var templateUrl = this.getAttribute('data-template-url');
        console.log('Loading dynamic form from URL:', templateUrl);
        loadDynamicForm(templateUrl); // load the dynamic form
    }
    // function to load the dynamic form
    function loadDynamicForm(templateUrl) {
        var formDataToRetain = {};

        // Capture existing form data before reload
        var existingForm = dynamicData.querySelector('form');
        // Capture existing form data before reload
        var existingForm = dynamicData.querySelector('form');
        if (existingForm) {
            var formDataToRetain = {}; // Initialize the object

            // VERY IMPORTANT: Capture inputs into an array immediately
            var inputs = Array.from(existingForm.querySelectorAll('input, select, textarea'));

            inputs.forEach(function (input) {
                formDataToRetain[input.name] = input.value;
            });

            console.log('Captured existing form data:', formDataToRetain);
        }



        // Load the template content
        var xhr = new XMLHttpRequest();
        xhr.open('GET', templateUrl, true);
        console.log('Opening request to load template:', templateUrl);
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) { // if the request is complete
                if (xhr.status === 200) { // if the response is successful
                    console.log('Template loaded successfully.');
                    if (templateUrl === '/delete_company_user' || templateUrl.startsWith('/delete_company_user/')) {
                        loadDynamicForm('/company_users');
                    } else if (templateUrl === '/delete_workflow' || templateUrl.startsWith('/delete_workflow/')) {
                        loadDynamicForm('/get_approval_workflow');
                    } else if (templateUrl === '/delete_shift' || templateUrl.startsWith('/delete_shift/')) {
                        console.log('Delete shift template loaded');
                        loadDynamicForm('/view_shifts');
                    } else if (templateUrl === '/delete_insurance' || templateUrl.includes('/delete_insurance/')) {
                        loadDynamicForm('/insurance');
                    } else if (templateUrl.startsWith('/manual_clockin')) {
                        loadDynamicForm('/clockin_employee');
                    }
                    else if (templateUrl === '/delete_advance' || templateUrl.startsWith('/delete_advance')) {
                        if (roleText.toLowerCase() === 'employee') {
                            loadDynamicForm('/view_advance_requests');
                        } else {
                            alert('You are not authorized to perform this action');
                            location.reload();
                        }
                    }
                    dynamicData.innerHTML = xhr.responseText;
                    console.log(templateUrl)

                    // manually extract and execute javascript from the loaded template and ignore if there is none or if it existed
                    function loadExternalScript(src) {
                        var script = document.createElement('script');
                        script.src = src;
                        script.type = 'text/javascript';
                        document.head.appendChild(script);
                        console.log('External script loaded:', src);
                    }

                    var scriptTags = dynamicData.querySelectorAll('script');
                    scriptTags.forEach(function (script) {
                        if (script.src) {
                            // Handle external script loading
                            loadExternalScript(script.src);
                        } else {
                            // Execute inline script
                            if (!script.hasAttribute('data-executed')) {
                                script.setAttribute('data-executed', 'true');
                                $.globalEval(script.innerHTML || script.textContent);
                                console.log('Script executed:', script);
                            }
                        }
                    });


                    // update the button text after a click
                    // load video tag if there is a video tag in the template, else continue
                    var video = document.querySelector('video');
                    if (video) {
                        navigator.mediaDevices.getUserMedia({ video: true })
                            .then(function (stream) {
                                video.srcObject = stream;
                                video.play();
                            })
                            .catch(function (err) {
                                console.log("Error: " + err);
                            });
                        // load external script
                        var script = document.createElement('script');
                        if (templateUrl === '/clockin') {
                            console.log("Clock in script loaded", templateUrl);
                            script.src = "/static/scripts/clock_in.js";
                        } else {
                            script.src = "/static/scripts/clock_out.js";
                        }
                        document.body.appendChild(script); // Append the script to the body

                    }
                    // remove exsisting event listeners

                    // Re-attach listeners for dynamically loaded template links
                    attachListeners();
                    // Attach form submission logic here
                    var form = dynamicData.querySelector('form');
                    if (form) {
                        console.log('Form found in dynamic content. Restoring captured data.');
                        // Restore the captured data
                        for (var name in formDataToRetain) {
                            if (formDataToRetain.hasOwnProperty(name)) {
                                var input = form.querySelector(`[name="${name}"]`);
                                if (input) {
                                    if (input.type !== 'file') {
                                        input.value = formDataToRetain[name];
                                    } else {
                                        console.log('File input found. Skipping restoration.');
                                    }
                                }
                            }
                        }
                        form.addEventListener('submit', function (e) {
                            e.preventDefault();
                            console.log('Form submission detected.');
                            var formData = new FormData(form);
                            console.log('Form data:', Array.from(formData.entries()));
                            // Disable the submit button to prevent multiple clicks
                            var submitButton = form.querySelector('button[type="submit"]');
                            var submitInput = form.querySelector('input[type="submit"]');


                            if ((submitButton && submitButton.disabled) || (submitInput && submitInput.disabled)) {
                                console.log('Form submission already in progress. Ignoring.');
                                return;
                            }
                            if (submitButton) {
                                submitButton.disabled = true;
                                var originalText = submitButton.innerText;
                                submitButton.innerText = 'Loading...';
                                submitButton.style.cursor = 'not-allowed';
                                //opacity
                                submitButton.style.opacity = '0.7';
                                // reset the button
                                setTimeout(function () {
                                    submitButton.disabled = false;
                                    submitButton.innerText = originalText;
                                    submitButton.style.cursor = 'pointer';
                                    //opacity
                                    submitButton.style.opacity = '1';
                                }, 10000);
                            }
                            if (submitInput) {
                                submitInput.disabled = true;
                                var originalText = submitInput.value;
                                submitInput.value = 'Loading...';
                                submitInput.style.cursor = 'not-allowed';
                                //opacity
                                submitInput.style.opacity = '0.7';
                                // reset the button
                                setTimeout(function () {
                                    submitInput.disabled = false;
                                    submitInput.value = originalText;
                                    submitInput.style.cursor = 'pointer';
                                    //opacity
                                    submitInput.style.opacity = '1';
                                }, 10000);
                            }

                            var submitXhr = new XMLHttpRequest();
                            submitXhr.open('POST', templateUrl, true);
                            submitXhr.onreadystatechange = function () {
                                if (submitXhr.readyState === 4) { // if the request is complete
                                    console.log('Form submission response received:', submitXhr.responseText);
                                    console.log('Status:', submitXhr.status);
                                    console.log('templateUrl:', templateUrl);
                                    if (templateUrl.startsWith('/view_documents')) {
                                        // Remove query params to normalize the path
                                        var baseTemplateUrl = templateUrl.split('?')[0];

                                        var parsedResponse = JSON.parse(submitXhr.responseText); // ✅ Parse JSON first
                                        console.log('Parsed response:', parsedResponse);

                                        var documentQueryParams = new URLSearchParams({
                                            document_type: parsedResponse.document_type || '',
                                            employee_id: parsedResponse.employee_id || '',
                                            uploader: parsedResponse.uploader || '',
                                            date_range: parsedResponse.date_range || ''
                                        }).toString();

                                        var filteredUrl = baseTemplateUrl + '?' + documentQueryParams;
                                        console.log('Filtered URL:', filteredUrl);
                                    }


                                    // switch the company by reloading the database
                                    if (submitXhr.status === 2100) {
                                        window.location.href = '/hr_dashboard';
                                    }
                                    else if (submitXhr.status === 400) {
                                        var response = JSON.parse(submitXhr.responseText);
                                        console.log('Parsed response:', response);
                                        console.log(templateUrl);

                                        // Display error messages from response
                                        var messageContainer = document.getElementById('message-container');
                                        var passedContainer = document.getElementById('passed-container');
                                        // check if the message container is empty
                                        if (messageContainer) {
                                            messageContainer.innerHTML = '';  // Clear out the container
                                        }
                                        else if (passedContainer) {
                                            passedContainer.innerHTML = '';   // Clear out the passed container too
                                        }
                                        else {
                                            console.error('No message container found.');
                                        }
                                        try {
                                            response.messages.forEach(function (message) {
                                                var p = document.createElement('p');
                                                p.textContent = message;
                                                if (message.startsWith('*')) {
                                                    passedContainer.appendChild(p);  // Add to passedContainer
                                                } else {
                                                    messageContainer.appendChild(p);  // Add to messageContainer
                                                }

                                            });
                                        }
                                        catch (error) {
                                            console.error('Error parsing messages:', error);
                                        }


                                        // Handle the form reload for bad request
                                        console.log('Form submission failed. Reloading form for corrections.');

                                    } else if (submitXhr.status === 200) {
                                        var response = JSON.parse(submitXhr.responseText);
                                        console.log('Parsed response:', response);
                                        console.log(templateUrl);
                                        if (response.success) {
                                            // import the clockinLink to the global scope
                                            console.log('Form submission successful. Redirecting to appropriate page.');

                                            // Reload form or redirect based on the action
                                            if (templateUrl === '/add_department' || templateUrl.startsWith('/update_department/')) {
                                                loadDynamicForm('/departments');
                                            } else if (templateUrl === '/register_employees' || templateUrl.startsWith('/register_employees/')) {
                                                loadDynamicForm('/employees_list');
                                            } else if (templateUrl === '/update_employee' || templateUrl.startsWith('/update_employee/')) {
                                                loadDynamicForm('/employees_list');
                                            } else if (templateUrl === '/add_reimbursements') {
                                                loadDynamicForm('/reimbursements');
                                            } else if (templateUrl === '/add_deductions' || templateUrl.includes('/delete_deductions') || templateUrl.startsWith('/update_deductions')) {
                                                loadDynamicForm('/deductions');
                                            } else if (templateUrl === '/payroll_summary') {
                                                loadDynamicForm('/payroll_summary');
                                            } else if (templateUrl === '/update_profile' || templateUrl === '/update_password' || templateUrl === '/edit_company_profile') {
                                                loadDynamicForm('/update_profile');
                                            } else if (templateUrl === '/update_password') {
                                                loadDynamicForm('/update_profile');
                                            } else if (templateUrl === '/upload_employees') {
                                                loadDynamicForm('/employees_list');
                                            } else if (templateUrl.includes('/approve_leave_application')) {
                                                loadDynamicForm('/view_leave_approvals');
                                            } else if (templateUrl.includes('/add_insurance') || templateUrl.includes('/delete_insurance')) {
                                                loadDynamicForm('/insurance');
                                            } else if (templateUrl.includes('/register_company_users') || templateUrl.startsWith('/register_company_users')) {
                                                loadDynamicForm('/view_company_users');
                                            } else if (templateUrl.includes('/switch_company')) {
                                                window.location.href = '/hr_dashboard';
                                            } else if (templateUrl === '/add_advance' || templateUrl.startsWith('/add_advance')) {
                                                if (roleText.toLowerCase() === 'employee') {
                                                    loadDynamicForm('/view_advance_requests');
                                                } else {
                                                    loadDynamicForm('/view_all_advance_requests');
                                                }
                                            } else if (templateUrl === '/apply_for_leave') {
                                                window.location.href = '/employee_dashboard';
                                            }
                                            else if (templateUrl === '/update_advance_request' || templateUrl.includes('/update_advance_request')) {
                                                loadDynamicForm('/view_all_advance_requests');
                                            }
                                            else if (templateUrl.includes('/create_workflow')) {
                                                loadDynamicForm('/get_approval_workflow');
                                            } else if (templateUrl === '/create_subject' || templateUrl.startsWith('/create_subject')) {
                                                if (roleText.toLowerCase() === 'hr') {
                                                    window.location.href = '/hr_dashboard';
                                                } else {
                                                    window.location.href = '/supervisor_dashboard';
                                                }
                                            } else if (templateUrl.includes('/update_workflow')) {
                                                // reload the workflow page
                                                loadDynamicForm('/get_approval_workflow');
                                            } else if (templateUrl === '/add_company_user' || templateUrl.startsWith('/add_company_user')) {
                                                loadDynamicForm('/company_users');
                                            } else if (templateUrl === '/record_leave_or_off') {
                                                loadDynamicForm('/attendance_records');
                                            } else if (templateUrl === '/clockin') {
                                                // set timeout to allow the video to stop playing
                                                setTimeout(function () {
                                                    loadDynamicForm('/clockout');
                                                }, 2000);
                                            } else if (templateUrl === '/timesheet') {
                                                loadDynamicForm('/timesheet');
                                            } else if (templateUrl === '/create_shift' || templateUrl.startsWith('/create_shift')) {
                                                loadDynamicForm('/view_shifts');
                                            } else if (templateUrl.startsWith('/view_documents')) {
                                                loadDynamicForm(`${filteredUrl}`);
                                            } else if (templateUrl === '/upload_documents' || templateUrl.startsWith('/upload_employee_document')) {
                                                loadDynamicForm('/view_documents');
                                            } else if (templateUrl.includes('/clockin_employee')) {
                                                loadDynamicForm('/clockin_employee');
                                            } else if (templateUrl.startsWith('/manual_clockin')) {
                                                loadDynamicForm('/clockin_employee');
                                            }
                                            else if (templateUrl === '/attendance_records') {
                                                loadDynamicForm('/attendance_records');
                                                // Payments
                                            } else if (templateUrl === '/upload_company_logo') {
                                                loadDynamicForm('/settings');
                                            }
                                            else if (templateUrl === 'create_invoice') {
                                                loadDynamicForm('/create_invoice1');
                                            
                                            }
                                            else {
                                                if (roleText.toLowerCase() === 'hr') {
                                                    window.location.href = '/hr_dashboard';
                                                } else if (roleText.toLowerCase() === 'employee') {
                                                    window.location.href = '/employee_dashboard';
                                                } else {
                                                    window.location.href = '/supervisor_dashboard';
                                                }
                                            }
                                            console.log('Form reloaded based on action:', templateUrl);

                                        } else {
                                            console.error('Error with form submission: Response marked as unsuccessful.');
                                            alert('Submission failed. Check your inputs and try again, if the error persists Please contact support.');
                                            window.location.href = '/hr_dashboard';
                                        }

                                    } else if (submitXhr.status === 500) {
                                        var response = JSON.parse(submitXhr.responseText);
                                        console.log('Parsed response:', response);
                                        console.log(templateUrl);

                                        // reload the same page
                                        if (templateUrl.includes('/add_company_user') || templateUrl.includes('/delete_company_user')) {
                                            loadDynamicForm('/company_users');
                                        }
                                    } else if (submitXhr.status === 201) {
                                        var response = JSON.parse(submitXhr.responseText);
                                        console.log('Parsed response:', response);
                                        console.log(templateUrl);
                                        if (response.success) {
                                            if (templateUrl === '/create_invoice') {
                                                loadDynamicForm(`/pay_invoice/${response.invoice_number}`);
                                            } else if (templateUrl === '/create_invoice1') {
                                                loadDynamicForm(`/pay_invoice/${response.invoice_number}`);
                                            } else {
                                                window.location.href = '/hr_dashboard';
                                            }
                                        }
                                    } else {
                                        // Handle unexpected statuses
                                        console.error('Unexpected status code:', submitXhr.status);
                                        alert('An error occured please, try again later. If the error persists, contact the support team.');
                                        if (roleText.toLowerCase() === 'hr') {
                                            window.location.href = '/hr_dashboard';
                                        } else if (roleText.toLowerCase() === 'employee') {
                                            if (templateUrl === '/apply_for_advance') {
                                                loadDynamicForm('/view_advance_requests');
                                            } else {
                                            window.location.href = '/employee_dashboard';
                                            }
                                        } else {
                                            window.location.href = '/supervisor_dashboard';
                                        };
                                    }

                                }
                            };

                            submitXhr.send(formData);
                        });
                    }

                    // Attach event listeners for popups and other interactions
                    setupPopupInteractions();
                    setupActionLinksInteractions();
                } else {
                    // Handle errors loading the template
                    if (xhr.status === 600) {
                        var subscriptionPopup = document.getElementById('subPopup');
                        subscriptionPopup.style.display = 'flex';
                        subscriptionPopup.style.alignItems = 'center';
                        subscriptionPopup.style.justifyContent = 'center';
                        subscriptionPopup.style.position = 'fixed';
                        subscriptionPopup.style.zIndex = '100';
                        subscriptionPopup.style.gap = '20px';
                        subscriptionPopup.style.padding = '20px';

                    } else {
                        console.error('Error loading template:', xhr.status, xhr.statusText);
                        alert('Something went wrong, try again. if the error persists, contact the support team');
                        //after clicking ok, redirect to add employee page within the data-template-url
                        window.location.href = '/hr_dashboard';
                    }
                }
            }
        };
        xhr.send();
    }
    function setupPopupInteractions() {
        var openPopup = document.getElementById('open-popup');
        var closePopup = document.getElementsByClassName('close-popup')[0];
        var closeButton = document.getElementById('close-button');
        var popup = document.getElementById('popup');
        var openPayrollPopup = document.getElementById('open-payroll-popup');
        var closePayrollPopup = document.getElementById('close-payroll-popup');
        var payrollPopup = document.getElementById('payroll-popup');
        if (openPopup) {
            openPopup.addEventListener('click', function () {
                console.log('Opening popup.');
                popup.style.display = 'block';
            });
        }

        else if (closePopup) {
            closePopup.addEventListener('click', function () {
                console.log('Closing popup.');
                popup.style.display = 'none';
            });
        }

        if (closeButton) {
            closeButton.addEventListener('click', function () {
                console.log('Closing popup via close button.');
                popup.style.display = 'none';
            });
        }
        if (openPayrollPopup) {
            openPayrollPopup.addEventListener('click', function () {
                console.log('Opening payroll popup.');
                payrollPopup.style.display = 'block';
            });
        }
        if (closePayrollPopup) {
            closePayrollPopup.addEventListener('click', function () {
                console.log('Closing payroll popup.');
                payrollPopup.style.display = 'none';
            });
        }

        // Close the popup when the user clicks outside of it
        window.addEventListener('click', function (event) {
            if (event.target === popup) {
                console.log('Closing popup by clicking outside.');
                popup.style.display = 'none';
            }
        });
    }

    function setupActionLinksInteractions() {
        var actionDots = document.getElementById('action-dots');
        var actionLinks = document.getElementById('action-links');
        var actionClose = document.getElementById('action-close');

        if (actionDots) {
            actionDots.addEventListener('click', function () {
                console.log('Action dots clicked. Showing action links.');
                actionLinks.style.display = 'block';
                actionDots.style.display = 'none';
            });

            // Hide the action links when the user clicks outside of it
            window.addEventListener('click', function (event) {
                if (event.target !== actionLinks && event.target !== actionDots) {
                    console.log('Click outside action links. Hiding action links.');
                    actionLinks.style.display = 'none';
                    actionDots.style.display = 'block';
                }
            });
        }
    }
    // Attach listeners initially
    attachListeners();
}
);
