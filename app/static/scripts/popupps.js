// Function to display the Payroll popup
function displayPayrollPopup() {
    document.getElementById('payroll-popup').style.display = 'flex';
}

// Function to close the Payroll popup
function closePayrollPopup() {
    document.getElementById('payroll-popup').style.display = 'none';
}

// Function to display the Export popup
function displayPopup() {
    document.getElementById('popup').style.display = 'flex';
}

function actionPopup() {
    document.getElementById('action-popup').style.display = 'flex';
}
// fn to close the action popup
function closeActionPopup() {
    document.getElementById('action-popup').style.display = 'none';
}

// Function to close the Export popup
function closePopup() {
    document.getElementById('popup').style.display = 'none';
}

// Function to display the TimeSheet popup
function timeSheetPopup() {
    document.getElementById('timesheet-popup').style.display = 'flex';
}
// Function to close the TimeSheet popup
function closeTimeSheetPopup() {
    document.getElementById('timesheet-popup').style.display = 'none';
}


// Close popups when clicking outside the popup content
window.onclick = function(event) {
    const payrollPopup = document.getElementById('payroll-popup');
    const exportPopup = document.getElementById('popup');
    const actionPopup = document.getElementById('action-popup');
    const timeSheetPopup = document.getElementById('timesheet-popup');

    if (event.target === payrollPopup) {
        payrollPopup.style.display = 'none';
    }
    if (event.target === exportPopup) {
        exportPopup.style.display = 'none';
    }
    if (event.target === actionPopup) {
        actionPopup.style.display = 'none';
    }
    if (event.target === timeSheetPopup) {
        timeSheetPopup.style.display = 'none';
    }
};

document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closePayrollPopup();
        closePopup();
    }
});