document.querySelectorAll('.status').forEach(function(td) {
    var status = td.textContent.trim(); // Get the text content and trim any extra spaces

    if (status === 'P') {
        td.classList.replace('status', 'present');
        td.classList.add('green-box');
    } else if (status === 'A') {
        td.classList.replace('status', 'absent');
        td.classList.add('red-box');
    } else if (status === 'O') {
        td.classList.replace('status', 'off');
        td.classList.add('yellow-box');
    } else if (status === 'L') {
        td.classList.replace('status', 'leave');
        td.classList.add('blue-box');
    } else {
        td.classList = 'status';
    }
});
