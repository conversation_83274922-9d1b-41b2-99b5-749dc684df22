const video = document.querySelector("#videoElement");

        // Access the webcam
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                video.srcObject = stream;

                // Capture the image and send it to the backend
                document.querySelector("#captureButton").addEventListener("click", function() {
                    const canvas = document.querySelector("#canvas");
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const context = canvas.getContext('2d');
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    var scannerLine = document.querySelector(".scanner-line");
                    scannerLine.style.display = "block";
                    // Convert the canvas content to a Blob and append it to FormData
                    canvas.toBlob(function(blob) {
                        const formData = new FormData();
                        formData.append("image", blob, "employee_image.jpg");  // Use a valid filename with extension
                        
                        // Display "Processing..." message before sending the request
                        const resultDiv = document.querySelector("#result");
                        resultDiv.innerHTML = "Processing...";

                        // Capture the user's location
                        if (navigator.geolocation) {
                            navigator.geolocation.getCurrentPosition(function(position) {
                                const latitude = position.coords.latitude;
                                const longitude = position.coords.longitude;
                                formData.append("latitude", latitude);
                                formData.append("longitude", longitude);

                                // Send the form data with the image and location to the Flask backend
                                sendFormData(formData, stream);
                            }, function(error) {
                                console.error('Error getting location:', error);
                                resultDiv.innerHTML = "Error getting location. Please allow location access.";
                            });
                        } else {
                            resultDiv.innerHTML = "Geolocation is not supported by this browser.";
                        }
                    }, 'image/jpeg');  // Ensure the correct MIME type is used (image/jpeg)
                });
            })
            .catch(function(err) {
                console.log("Error: " + err);
            });

        // Function to stop the webcam
        function stopWebcam(stream) {
            const tracks = stream.getTracks();
            tracks.forEach(track => track.stop());
        }

        function extractMessage(responseText) {
            const match = responseText.match(/"([^']+)"/);
            if (match) {
                return match[1];  // Return the captured text inside the quotes
            }
            return null;  // Return null if no match is found
        }
        // Function to send form data
        function sendFormData(formData, stream) {
            fetch('/field_clockout', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log("Response status:", response.status);
                responseStatus = response.status;
                console.log("Response status text:", response.statusText);
                return response.json();
                
            })
            .then(result => {
                // Display result in the 'result' div
                const resultDiv = document.querySelector("#result");
                const button = document.querySelector("#captureButton");
                const checkButton = document.querySelector("#checkButton");
                const crossButton = document.querySelector("#crossButton");
                const infoButton = document.querySelector("#infoButton");
                let clockinLink = "/clockin";
                let message = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                resultDiv.innerHTML = extractMessage(message);
                console.log(JSON.stringify(result.message));
                console.log(responseStatus);
                // Stop the webcam after capturing the image
                stopWebcam(stream);
                video.style.display = "none";
                button.style.display = "none";
                if (responseStatus === 200) {
                    checkButton.style.display = "block";
                } else if (responseStatus === 401) {
                    infoButton.style.display = "block"; 
                }else {
                    crossButton.style.display = "block";
                }
                setTimeout(function() {
                    location.reload();
                }, 3000);
            })
            .catch(error => {
                console.error('Error:', error);
                const resultDiv = document.querySelector("#result");
                resultDiv.innerHTML = "An error occurred while processing the image.";
            });
        }