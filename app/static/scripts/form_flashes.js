document.addEventListener('DOMContentLoaded', function () {
    // Select all flash messages
    var flashMessages = document.querySelectorAll('.alert-dismissible');

    flashMessages.forEach(function (message) {
        // Set the duration for the message to stay visible
        var duration = 5000; // Duration in milliseconds (5 seconds)

        // Use setTimeout to hide the message after the duration
        setTimeout(function () {
            // Check if the message is still visible before hiding it
            if (message.classList.contains('show')) {
                message.classList.remove('show');
                message.classList.add('fade');
            }
        }, duration);
    });
});
