function displayHide() {
    var item = document.getElementById("item");
    if (item.className === "sidebar") {
        item.className = "display";
    }else{
        item.className = "sidebar";
    }
}

// display the profile, settings and logout
var userDetails = document.getElementById('user-details');
var actionsLinks = document.getElementById('action--links');
userDetails.addEventListener('click', function(event) {
    event.stopPropagation();
    actionsLinks.style.display = 'block';
});

window.addEventListener('click', function(event) {
    if(event.target != actionsLinks && event.target != userDetails){
        actionsLinks.style.display = 'none';
    }
});

// Select all feature-content elements
var featureContents = document.querySelectorAll('.feature-content');

featureContents.forEach(function(featureContent) {
    var subMenu = featureContent.querySelector('li > a'); // Select the main clickable link
    var subLinks = featureContent.querySelector('.feature-items'); // Select the sub-links container

    subMenu.addEventListener('click', function(event) {
        event.stopPropagation();
        // Toggle display of sub-links
        if (subLinks.style.display === 'block') {
            subLinks.style.display = 'none';
        } else {
            subLinks.style.display = 'block';
        }
    });

    window.addEventListener('click', function(event) {
        // Hide sub-links if clicked outside
        if (!featureContent.contains(event.target)) {
            subLinks.style.display = 'none';
        }
    });
});
