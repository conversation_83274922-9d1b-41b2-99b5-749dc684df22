// Function to fetch and display attendance records
async function fetchAttendanceRecords() {
    const tableBody = document.getElementById('attendance-body');
    const errorMessage = document.getElementById('error-message');
    tableBody.innerHTML = ''; // Clear existing rows
    errorMessage.style.display = 'none'; // Hide error message

    // Determine the API endpoint based on URL query parameters
    const urlParams = new URLSearchParams(window.location.search);
    const startDate = urlParams.get('start_date');
    const endDate = urlParams.get('end_date');
    let apiUrl = '/attendance_records'; // Default: unfiltered
    if (startDate && endDate) {
        apiUrl = `/attendance_records/filtered?start_date=${startDate}&end_date=${endDate}`;
    }

    try {
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add authentication headers if needed (e.g., Bearer token)
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        // Check if the response is successful and contains attendance data
        if (data.success && Array.isArray(data.attendance) && data.attendance.length > 0) {
            // Populate table rows
            data.attendance.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.employee_name || 'N/A'}</td>
                    <td>${record.time_in || 'N/A'}</td>
                    <td>${record.time_out || 'N/A'}</td>
                    <td>${record.total_duration || 'N/A'}</td>
                    <td>${record.recorgnition_status || 'N/A'}</td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            // Show message if no records are found
            errorMessage.textContent = data.message || 'No attendance records found.';
            errorMessage.style.display = 'block';
        }
    } catch (error) {
        console.error('Error fetching attendance records:', error);
        errorMessage.textContent = 'Failed to load attendance records. Please try again later.';
        errorMessage.style.display = 'block';
    }
}

// Handle form submission (for filtered view)
document.getElementById('filter-form')?.addEventListener('submit', async (event) => {
    event.preventDefault(); // Prevent default form submission
    const startPeriod = document.getElementById('start_period').value;
    const endPeriod = document.getElementById('end_period').value;

    // Update URL with query parameters and fetch data
    const newUrl = `/attendance_records/filtered?start_date=${startPeriod}&end_date=${endPeriod}`;
    window.history.pushState({}, '', newUrl); // Update browser URL without reloading
    await fetchAttendanceRecords();
});

// Fetch records when the page loads
document.addEventListener('DOMContentLoaded', fetchAttendanceRecords);