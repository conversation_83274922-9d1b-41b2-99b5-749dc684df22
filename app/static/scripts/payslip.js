    function printAndRefresh() {
    // Prepare for printing: Disable non-payslip stylesheets and hide no-print elements
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    stylesheets.forEach(sheet => {
        if (!sheet.href.includes('employee_payslip.css')) {
            sheet.disabled = true;
        }
    });

    const noPrints = document.querySelectorAll('.no-print');
    noPrints.forEach(noPrint => {
        noPrint.style.display = 'none';
    });

    // Trigger print dialog
    window.print();

    // After print dialog closes (Save/Print or Cancel), restore styles and refresh
    window.addEventListener('afterprint', function handleAfterPrint() {

        // hide everything
        const payslipContents = document.querySelectorAll('.payslip-content');
        payslipContents.forEach(content => {
            content.style.display = 'none';
        });

        window.location.reload();
        stylesheets.forEach(sheet => {
            sheet.disabled = false;
        });
        
        window.removeEventListener('afterprint', handleAfterPrint);
    }, { once: true });
}

// Attach the printAndRefresh function to the button click
document.addEventListener('DOMContentLoaded', function() {
    const printButton = document.querySelector('.print-area button');
    if (printButton) {
        printButton.removeEventListener('click', printAndRefresh); // Prevent duplicate listeners
        printButton.addEventListener('click', printAndRefresh);
    } else {
        console.error('Print button not found.');
    }
});