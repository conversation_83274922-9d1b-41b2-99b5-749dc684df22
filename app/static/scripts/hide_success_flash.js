// Function to hide success flash messages after 5 seconds (5000ms)
setTimeout(function() {
    // Select all elements with the 'alert-success' class inside the #flash-messages container
    var successMessages = document.querySelectorAll('#flash-messages .alert-success');

    // Loop through each success message and hide it
    successMessages.forEach(function(message) {
        message.classList.add('fade-out'); // Add a fade-out class for smooth transition
        setTimeout(function() {
            message.style.display = 'none'; // Hide the message after the fade-out effect
        }, 500); // Adjust this delay to match the duration of the fade-out effect
    });
}, 5000); // Adjust this delay as needed (5000ms = 5 seconds)

// Optional: Add smooth fade-out effect with CSS
const style = document.createElement('style');
style.innerHTML = `
    .fade-out {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
    }
`;
document.head.appendChild(style);
