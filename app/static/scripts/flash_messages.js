
document.addEventListener('DOMContentLoaded', function() {
    // Select all flash messages with the alert class
    var flashMessages = document.querySelectorAll('.alert-dismissible');

    flashMessages.forEach(function(message) {
        // Set a timeout to remove the message after 10 seconds (10000 milliseconds)
        setTimeout(function() {
            // Fade out the alert
            message.style.opacity = '0';
            // Optionally, you can use setTimeout to remove the element after fade out
            setTimeout(function() {
                message.style.display = 'none';
            }, 600); // Adjust this duration to match the fade-out transition time
        }, 5000); // duration = 5 seconds
    });

    document.querySelectorAll('.close').forEach(button => {
        button.addEventListener('click', function() {
            // Get the flash message div
            var message_container = button.closest('.alert-dismissible');
            if (message_container) {
                // Remove the flash message div
                message_container.style.display = 'none';
            }
        });
    });
});

