document.addEventListener('DOMContentLoaded', function() {
    // Store all document rows for filtering
    const allDocuments = document.querySelectorAll('.document-item');

    // Update document count and table visibility
    function updateDocumentCount() {
        const visibleDocs = Array.from(allDocuments).filter(doc => doc.style.display !== 'none').length;
        let docCount = document.querySelector('.document-count');
        if (!docCount) {
            docCount = document.createElement('div');
            docCount.className = 'text-muted mb-2 document-count';
            document.getElementById('document-list').closest('.document-table').prepend(docCount);
        }
        docCount.textContent = `Found ${visibleDocs} document(s)`;

        // Show/hide table and no-documents message
        const table = document.getElementById('document-list').closest('table');
        let noDocsMessage = document.querySelector('.no-docs-message');
        if (visibleDocs === 0) {
            if (!noDocsMessage) {
                noDocsMessage = document.createElement('div');
                noDocsMessage.className = 'alert alert-info mt-3 no-docs-message';
                let uploadMessage = 'No documents found. Try adjusting your filters or <a href="' + 
                    document.querySelector('a[href*="/document/upload"]').getAttribute('href') + 
                    '">upload a new document</a>';
                if (document.querySelector('a[href*="/document/upload_employee"]')) {
                    uploadMessage += ' or <a href="' + 
                        document.querySelector('a[href*="/document/upload_employee"]').getAttribute('href') + 
                        '">upload for an employee</a>';
                }
                uploadMessage += '.';
                noDocsMessage.innerHTML = uploadMessage;
                table.parentNode.appendChild(noDocsMessage);
            }
            table.style.display = 'none';
            noDocsMessage.style.display = 'block';
        } else {
            table.style.display = 'table';
            if (noDocsMessage) {
                noDocsMessage.style.display = 'none';
            }
        }
    }

    // Apply filters
    window.applyFilters = function() {
        const documentType = document.getElementById('document-type').value;
        const employeeId = document.getElementById('employee-filter') ? document.getElementById('employee-filter').value : '';
        const uploader = document.getElementById('uploader').value.toLowerCase();
        const dateRange = document.getElementById('date-filter').value;

        allDocuments.forEach(doc => {
            let show = true;

            // Document Type Filter
            if (documentType && doc.dataset.documentType !== documentType) {
                show = false;
            }

            // Employee Filter
            if (employeeId && doc.dataset.employeeId !== employeeId) {
                show = false;
            }

            // Uploader Filter
            if (uploader && !doc.dataset.uploader.toLowerCase().includes(uploader)) {
                show = false;
            }

            // Date Range Filter
            if (dateRange && doc.dataset.uploadedAt) {
                const uploadedDate = new Date(doc.dataset.uploadedAt);
                const now = new Date();

                if (dateRange === 'today' && uploadedDate.toDateString() !== now.toDateString()) {
                    show = false;
                } else if (dateRange === 'week') {
                    const weekAgo = new Date(now - 7 * 24 * 60 * 60 * 1000);
                    if (uploadedDate < weekAgo) {
                        show = false;
                    }
                } else if (dateRange === 'month') {
                    const monthAgo = new Date(now - 30 * 24 * 60 * 60 * 1000);
                    if (uploadedDate < monthAgo) {
                        show = false;
                    }
                }
            }

            // Show or hide document
            doc.style.display = show ? '' : 'none';
        });

        // Update document count and table visibility
        updateDocumentCount();
    };

    // Reset filters
    window.resetFilters = function() {
        // Reset form inputs
        document.getElementById('document-type').value = '';
        if (document.getElementById('employee-filter')) {
            document.getElementById('employee-filter').value = '';
        }
        document.getElementById('uploader').value = '';
        document.getElementById('date-filter').value = '';

        // Show all documents
        allDocuments.forEach(doc => {
            doc.style.display = '';
        });

        // Update document count and table visibility
        updateDocumentCount();
    };

    // Initial document count and table visibility
    updateDocumentCount();

    // Apply filters on input change for real-time filtering (optional)
    document.getElementById('filter-form').addEventListener('change', applyFilters);
});