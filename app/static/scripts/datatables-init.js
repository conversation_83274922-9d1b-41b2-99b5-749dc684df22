document.addEventListener('DOMContentLoaded', function() {
    // departments datatable initialization
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('.dataTable')) {
            $('.dataTable').DataTable().destroy();
        }
        // Initialize DataTables for all tables with the class 'dataTable'
        $('.dataTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });

    // employees datatable initialization
    $(document).ready(function() {
        // check if the DataTables is already initialized
        if ($.fn.DataTable.isDataTable('#employees_list')) {
            $('#employees_list').DataTable().destroy();
        }
        $('#employees_list').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10
        });
    });
});
