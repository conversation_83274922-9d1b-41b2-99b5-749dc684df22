function I(s){if(arguments.length==0)throw new TypeError("`CSS.escape` requires an argument.");var e=String(s),r=e.length,n=-1,t,l="",o=e.charCodeAt(0);if(r==1&&o==45)return"\\"+e;for(;++n<r;){if(t=e.charCodeAt(n),t==0){l+="\uFFFD";continue}if(t>=1&&t<=31||t==127||n==0&&t>=48&&t<=57||n==1&&t>=48&&t<=57&&o==45){l+="\\"+t.toString(16)+" ";continue}if(t>=128||t==45||t==95||t>=48&&t<=57||t>=65&&t<=90||t>=97&&t<=122){l+=e.charAt(n);continue}l+="\\"+e.charAt(n)}return l}function R(s){return s.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,e=>e.length>2?String.fromCodePoint(Number.parseInt(e.slice(1).trim(),16)):e[1])}var L=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-underline-offset","--text-indent","--text-decoration-thickness","--text-decoration-color"]]]);function D(s,e){return(L.get(e)??[]).some(r=>s===r||s.startsWith(`${r}-`))}var b=class{constructor(e=new Map,r=new Set([])){this.values=e;this.keyframes=r}prefix=null;add(e,r,n=0){if(e.endsWith("-*")){if(r!=="initial")throw new Error(`Invalid theme value \`${r}\` for namespace \`${e}\``);e==="--*"?this.values.clear():this.clearNamespace(e.slice(0,-2),0)}if(n&4){let t=this.values.get(e);if(t&&!(t.options&4))return}r==="initial"?this.values.delete(e):this.values.set(e,{value:r,options:n})}keysInNamespaces(e){let r=[];for(let n of e){let t=`${n}-`;for(let l of this.values.keys())l.startsWith(t)&&l.indexOf("--",2)===-1&&(D(l,n)||r.push(l.slice(t.length)))}return r}get(e){for(let r of e){let n=this.values.get(r);if(n)return n.value}return null}hasDefault(e){return(this.getOptions(e)&4)===4}getOptions(e){return e=R(this.#s(e)),this.values.get(e)?.options??0}entries(){return this.prefix?Array.from(this.values,e=>(e[0]=this.#r(e[0]),e)):this.values.entries()}#r(e){return this.prefix?`--${this.prefix}-${e.slice(2)}`:e}#s(e){return this.prefix?`--${e.slice(3+this.prefix.length)}`:e}clearNamespace(e,r){let n=L.get(e)??[];e:for(let t of this.values.keys())if(t.startsWith(e)){if(r!==0&&(this.getOptions(t)&r)!==r)continue;for(let l of n)if(t.startsWith(l))continue e;this.values.delete(t)}}#e(e,r){for(let n of r){let t=e!==null?`${n}-${e}`:n;if(!this.values.has(t))if(e!==null&&e.includes(".")){if(t=`${n}-${e.replaceAll(".","_")}`,!this.values.has(t))continue}else continue;if(!D(t,n))return t}return null}#t(e){return this.values.has(e)?`var(${I(this.#r(e))})`:null}markUsedVariable(e){let r=R(this.#s(e)),n=this.values.get(r);n&&(n.options|=16)}resolve(e,r){let n=this.#e(e,r);if(!n)return null;let t=this.values.get(n);return t.options&1?t.value:this.#t(n)}resolveValue(e,r){let n=this.#e(e,r);return n?this.values.get(n).value:null}resolveWith(e,r,n=[]){let t=this.#e(e,r);if(!t)return null;let l={};for(let u of n){let a=`${t}${u}`,i=this.values.get(a);i&&(i.options&1?l[u]=i.value:l[u]=this.#t(a))}let o=this.values.get(t);return o.options&1?[o.value,l]:[this.#t(t),l]}namespace(e){let r=new Map,n=`${e}-`;for(let[t,l]of this.values)t===e?r.set(null,l.value):t.startsWith(`${n}-`)?r.set(t.slice(e.length),l.value):t.startsWith(n)&&r.set(t.slice(n.length),l.value);return r}addKeyframes(e){this.keyframes.add(e)}getKeyframes(){return Array.from(this.keyframes)}};var N=class extends Map{constructor(r){super();this.factory=r}get(r){let n=super.get(r);return n===void 0&&(n=this.factory(r,this),this.set(r,n)),n}};var G=64;function H(s,e=[]){return{kind:"rule",selector:s,nodes:e}}function T(s,e="",r=[]){return{kind:"at-rule",name:s,params:e,nodes:r}}function M(s,e=[]){return s.charCodeAt(0)===G?x(s,e):H(s,e)}function _(s,e,r=!1){return{kind:"declaration",property:s,value:e,important:r}}function F(s){return{kind:"comment",value:s}}function ue(s,e){return{kind:"context",context:s,nodes:e}}function ce(s){return{kind:"at-root",nodes:s}}function U(s,e,r=[],n={}){for(let t=0;t<s.length;t++){let l=s[t],o=r[r.length-1]??null;if(l.kind==="context"){if(U(l.nodes,e,r,{...n,...l.context})===2)return 2;continue}r.push(l);let u=!1,a=0,i=e(l,{parent:o,context:n,path:r,replaceWith(f){u=!0,Array.isArray(f)?f.length===0?(s.splice(t,1),a=0):f.length===1?(s[t]=f[0],a=1):(s.splice(t,1,...f),a=f.length):(s[t]=f,a=1)}})??0;if(r.pop(),u){i===0?t--:t+=a-1;continue}if(i===2)return 2;if(i!==1&&"nodes"in l){r.push(l);let f=U(l.nodes,e,r,n);if(r.pop(),f===2)return 2}}}function W(s,e,r=[],n={}){for(let t=0;t<s.length;t++){let l=s[t],o=r[r.length-1]??null;if(l.kind==="rule"||l.kind==="at-rule")r.push(l),W(l.nodes,e,r,n),r.pop();else if(l.kind==="context"){W(l.nodes,e,r,{...n,...l.context});continue}r.push(l),e(l,{parent:o,context:n,path:r,replaceWith(u){Array.isArray(u)?u.length===0?s.splice(t,1):u.length===1?s[t]=u[0]:s.splice(t,1,...u):s[t]=u,t+=u.length-1}}),r.pop()}}function he(s,e){let r=[],n=new Set,t=new N(()=>new Set),l=new Set,o=new Set;function u(i,f,h={},d=0){if(i.kind==="declaration"){if(i.property==="--tw-sort"||i.value===void 0||i.value===null)return;if(h.theme&&i.property[0]==="-"&&i.property[1]==="-"&&t.get(f).add(i),i.value.includes("var(")&&e.trackUsedVariables(i.value),i.property==="animation"){let c=i.value.split(/\s+/);for(let p of c)o.add(p)}f.push(i)}else if(i.kind==="rule")if(i.selector==="&")for(let c of i.nodes){let p=[];u(c,p,h,d+1),p.length>0&&f.push(...p)}else{let c={...i,nodes:[]};for(let p of i.nodes)u(p,c.nodes,h,d+1);c.nodes.length>0&&f.push(c)}else if(i.kind==="at-rule"&&i.name==="@property"&&d===0){if(n.has(i.params))return;n.add(i.params);let c={...i,nodes:[]};for(let p of i.nodes)u(p,c.nodes,h,d+1);f.push(c)}else if(i.kind==="at-rule"){let c={...i,nodes:[]};for(let p of i.nodes)u(p,c.nodes,h,d+1);i.name==="@keyframes"&&h.theme&&l.add(c),(c.nodes.length>0||c.name==="@layer"||c.name==="@charset"||c.name==="@custom-media"||c.name==="@namespace"||c.name==="@import")&&f.push(c)}else if(i.kind==="at-root")for(let c of i.nodes){let p=[];u(c,p,h,0);for(let g of p)r.push(g)}else if(i.kind==="context"){if(i.context.reference)return;for(let c of i.nodes)u(c,f,{...h,...i.context},d)}else i.kind==="comment"&&f.push(i)}let a=[];for(let i of s)u(i,a,{},0);if(!1){e:for(let[i,f]of t)for(let h of f){if(e.theme.getOptions(h.property)&24){if(h.property.startsWith("--animate-")){let p=h.value.split(/\s+/);for(let g of p)o.add(g)}continue}let c=i.indexOf(h);if(i.splice(c,1),i.length===0){for(let[p,g]of a.entries())if(g.kind==="rule"&&g.nodes===i){a.splice(p,1);break}continue e}}for(let i of l)if(!o.has(i.params)){let f=r.indexOf(i);r.splice(f,1)}}return a.concat(r)}function de(s){function e(n,t=0){let l="",o="  ".repeat(t);if(n.kind==="declaration")l+=`${o}${n.property}: ${n.value}${n.important?" !important":""};
`;else if(n.kind==="rule"){l+=`${o}${n.selector} {
`;for(let u of n.nodes)l+=e(u,t+1);l+=`${o}}
`}else if(n.kind==="at-rule"){if(n.nodes.length===0)return`${o}${n.name} ${n.params};
`;l+=`${o}${n.name}${n.params?` ${n.params} `:" "}{
`;for(let u of n.nodes)l+=e(u,t+1);l+=`${o}}
`}else if(n.kind==="comment")l+=`${o}/*${n.value}*/
`;else if(n.kind==="context"||n.kind==="at-root")return"";return l}let r="";for(let n of s){let t=e(n);t!==""&&(r+=t)}return r}var v=92,C=47,k=42,z=34,Q=39,Y=58,E=59,m=10,y=32,S=9,P=123,$=125,K=40,B=41,q=91,X=93,V=45,w=64,J=33;function me(s){s=s.replaceAll(`\r
`,`
`);let e=[],r=[],n=[],t=null,l=null,o="",u="",a;for(let i=0;i<s.length;i++){let f=s.charCodeAt(i);if(f===v)o+=s.slice(i,i+2),i+=1;else if(f===C&&s.charCodeAt(i+1)===k){let h=i;for(let c=i+2;c<s.length;c++)if(a=s.charCodeAt(c),a===v)c+=1;else if(a===k&&s.charCodeAt(c+1)===C){i=c+1;break}let d=s.slice(h,i+1);d.charCodeAt(2)===J&&r.push(F(d.slice(2,-2)))}else if(f===Q||f===z){let h=i;for(let d=i+1;d<s.length;d++)if(a=s.charCodeAt(d),a===v)d+=1;else if(a===f){i=d;break}else{if(a===E&&s.charCodeAt(d+1)===m)throw new Error(`Unterminated string: ${s.slice(h,d+1)+String.fromCharCode(f)}`);if(a===m)throw new Error(`Unterminated string: ${s.slice(h,d)+String.fromCharCode(f)}`)}o+=s.slice(h,i+1)}else{if((f===y||f===m||f===S)&&(a=s.charCodeAt(i+1))&&(a===y||a===m||a===S))continue;if(f===m){if(o.length===0)continue;a=o.charCodeAt(o.length-1),a!==y&&a!==m&&a!==S&&(o+=" ")}else if(f===V&&s.charCodeAt(i+1)===V&&o.length===0){let h="",d=i,c=-1;for(let g=i+2;g<s.length;g++)if(a=s.charCodeAt(g),a===v)g+=1;else if(a===C&&s.charCodeAt(g+1)===k){for(let A=g+2;A<s.length;A++)if(a=s.charCodeAt(A),a===v)A+=1;else if(a===k&&s.charCodeAt(A+1)===C){g=A+1;break}}else if(c===-1&&a===Y)c=o.length+g-d;else if(a===E&&h.length===0){o+=s.slice(d,g),i=g;break}else if(a===K)h+=")";else if(a===q)h+="]";else if(a===P)h+="}";else if((a===$||s.length-1===g)&&h.length===0){i=g-1,o+=s.slice(d,g);break}else(a===B||a===X||a===$)&&h.length>0&&s[g]===h[h.length-1]&&(h=h.slice(0,-1));let p=O(o,c);if(!p)throw new Error("Invalid custom property, expected a value");t?t.nodes.push(p):e.push(p),o=""}else if(f===E&&o.charCodeAt(0)===w)l=x(o),t?t.nodes.push(l):e.push(l),o="",l=null;else if(f===E&&u[u.length-1]!==")"){let h=O(o);if(!h)throw o.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${o.trim()}\``);t?t.nodes.push(h):e.push(h),o=""}else if(f===P&&u[u.length-1]!==")")u+="}",l=M(o.trim()),t&&t.nodes.push(l),n.push(t),t=l,o="",l=null;else if(f===$&&u[u.length-1]!==")"){if(u==="")throw new Error("Missing opening {");if(u=u.slice(0,-1),o.length>0)if(o.charCodeAt(0)===w)l=x(o),t?t.nodes.push(l):e.push(l),o="",l=null;else{let d=o.indexOf(":");if(t){let c=O(o,d);if(!c)throw new Error(`Invalid declaration: \`${o.trim()}\``);t.nodes.push(c)}}let h=n.pop()??null;h===null&&t&&e.push(t),t=h,o="",l=null}else if(f===K)u+=")",o+="(";else if(f===B){if(u[u.length-1]!==")")throw new Error("Missing opening (");u=u.slice(0,-1),o+=")"}else{if(o.length===0&&(f===y||f===m||f===S))continue;o+=String.fromCharCode(f)}}}if(o.charCodeAt(0)===w&&e.push(x(o)),u.length>0&&t){if(t.kind==="rule")throw new Error(`Missing closing } at ${t.selector}`);if(t.kind==="at-rule")throw new Error(`Missing closing } at ${t.name} ${t.params}`)}return r.length>0?r.concat(e):e}function x(s,e=[]){for(let r=5;r<s.length;r++){let n=s.charCodeAt(r);if(n===y||n===K){let t=s.slice(0,r).trim(),l=s.slice(r).trim();return T(t,l,e)}}return T(s.trim(),"",e)}function O(s,e=s.indexOf(":")){if(e===-1)return null;let r=s.indexOf("!important",e+1);return _(s.slice(0,e).trim(),s.slice(e+1,r===-1?s.length:r).trim(),r!==-1)}export{me as a,I as b,R as c,b as d,N as e,H as f,T as g,M as h,_ as i,F as j,ue as k,ce as l,U as m,W as n,he as o,de as p};
