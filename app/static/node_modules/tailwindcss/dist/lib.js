"use strict";var zr=Object.defineProperty;var Lr=(t,r)=>{for(var n in r)zr(t,n,{get:r[n],enumerable:!0})};var Ye={};Lr(Ye,{Features:()=>he,__unstable__loadDesignSystem:()=>wi,compile:()=>bi,compileAst:()=>Fr,default:()=>Se});var wt="4.0.6";var we=92,Re=47,Oe=42,Wr=34,Br=39,qr=58,Ke=59,ie=10,ke=32,Pe=9,kt=123,Ze=125,et=40,xt=41,Hr=91,Gr=93,At=45,Qe=64,Jr=33;function oe(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=[],i=null,u=null,a="",p="",f;for(let s=0;s<t.length;s++){let d=t.charCodeAt(s);if(d===we)a+=t.slice(s,s+2),s+=1;else if(d===Re&&t.charCodeAt(s+1)===Oe){let m=s;for(let v=s+2;v<t.length;v++)if(f=t.charCodeAt(v),f===we)v+=1;else if(f===Oe&&t.charCodeAt(v+1)===Re){s=v+1;break}let g=t.slice(m,s+1);g.charCodeAt(2)===Jr&&n.push(De(g.slice(2,-2)))}else if(d===Br||d===Wr){let m=s;for(let g=s+1;g<t.length;g++)if(f=t.charCodeAt(g),f===we)g+=1;else if(f===d){s=g;break}else{if(f===Ke&&t.charCodeAt(g+1)===ie)throw new Error(`Unterminated string: ${t.slice(m,g+1)+String.fromCharCode(d)}`);if(f===ie)throw new Error(`Unterminated string: ${t.slice(m,g)+String.fromCharCode(d)}`)}a+=t.slice(m,s+1)}else{if((d===ke||d===ie||d===Pe)&&(f=t.charCodeAt(s+1))&&(f===ke||f===ie||f===Pe))continue;if(d===ie){if(a.length===0)continue;f=a.charCodeAt(a.length-1),f!==ke&&f!==ie&&f!==Pe&&(a+=" ")}else if(d===At&&t.charCodeAt(s+1)===At&&a.length===0){let m="",g=s,v=-1;for(let y=s+2;y<t.length;y++)if(f=t.charCodeAt(y),f===we)y+=1;else if(f===Re&&t.charCodeAt(y+1)===Oe){for(let x=y+2;x<t.length;x++)if(f=t.charCodeAt(x),f===we)x+=1;else if(f===Oe&&t.charCodeAt(x+1)===Re){y=x+1;break}}else if(v===-1&&f===qr)v=a.length+y-g;else if(f===Ke&&m.length===0){a+=t.slice(g,y),s=y;break}else if(f===et)m+=")";else if(f===Hr)m+="]";else if(f===kt)m+="}";else if((f===Ze||t.length-1===y)&&m.length===0){s=y-1,a+=t.slice(g,y);break}else(f===xt||f===Gr||f===Ze)&&m.length>0&&t[y]===m[m.length-1]&&(m=m.slice(0,-1));let b=Xe(a,v);if(!b)throw new Error("Invalid custom property, expected a value");i?i.nodes.push(b):r.push(b),a=""}else if(d===Ke&&a.charCodeAt(0)===Qe)u=xe(a),i?i.nodes.push(u):r.push(u),a="",u=null;else if(d===Ke&&p[p.length-1]!==")"){let m=Xe(a);if(!m)throw a.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${a.trim()}\``);i?i.nodes.push(m):r.push(m),a=""}else if(d===kt&&p[p.length-1]!==")")p+="}",u=M(a.trim()),i&&i.nodes.push(u),e.push(i),i=u,a="",u=null;else if(d===Ze&&p[p.length-1]!==")"){if(p==="")throw new Error("Missing opening {");if(p=p.slice(0,-1),a.length>0)if(a.charCodeAt(0)===Qe)u=xe(a),i?i.nodes.push(u):r.push(u),a="",u=null;else{let g=a.indexOf(":");if(i){let v=Xe(a,g);if(!v)throw new Error(`Invalid declaration: \`${a.trim()}\``);i.nodes.push(v)}}let m=e.pop()??null;m===null&&i&&r.push(i),i=m,a="",u=null}else if(d===et)p+=")",a+="(";else if(d===xt){if(p[p.length-1]!==")")throw new Error("Missing opening (");p=p.slice(0,-1),a+=")"}else{if(a.length===0&&(d===ke||d===ie||d===Pe))continue;a+=String.fromCharCode(d)}}}if(a.charCodeAt(0)===Qe&&r.push(xe(a)),p.length>0&&i){if(i.kind==="rule")throw new Error(`Missing closing } at ${i.selector}`);if(i.kind==="at-rule")throw new Error(`Missing closing } at ${i.name} ${i.params}`)}return n.length>0?n.concat(r):r}function xe(t,r=[]){for(let n=5;n<t.length;n++){let e=t.charCodeAt(n);if(e===ke||e===et){let i=t.slice(0,n).trim(),u=t.slice(n).trim();return D(i,u,r)}}return D(t.trim(),"",r)}function Xe(t,r=t.indexOf(":")){if(r===-1)return null;let n=t.indexOf("!important",r+1);return l(t.slice(0,r).trim(),t.slice(r+1,n===-1?t.length:n).trim(),n!==-1)}function de(t){if(arguments.length==0)throw new TypeError("`CSS.escape` requires an argument.");var r=String(t),n=r.length,e=-1,i,u="",a=r.charCodeAt(0);if(n==1&&a==45)return"\\"+r;for(;++e<n;){if(i=r.charCodeAt(e),i==0){u+="\uFFFD";continue}if(i>=1&&i<=31||i==127||e==0&&i>=48&&i<=57||e==1&&i>=48&&i<=57&&a==45){u+="\\"+i.toString(16)+" ";continue}if(i>=128||i==45||i==95||i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122){u+=r.charAt(e);continue}u+="\\"+r.charAt(e)}return u}function le(t){return t.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}var $t=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-underline-offset","--text-indent","--text-decoration-thickness","--text-decoration-color"]]]);function Ct(t,r){return($t.get(r)??[]).some(n=>t===n||t.startsWith(`${n}-`))}var _e=class{constructor(r=new Map,n=new Set([])){this.values=r;this.keyframes=n}prefix=null;add(r,n,e=0){if(r.endsWith("-*")){if(n!=="initial")throw new Error(`Invalid theme value \`${n}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(e&4){let i=this.values.get(r);if(i&&!(i.options&4))return}n==="initial"?this.values.delete(r):this.values.set(r,{value:n,options:e})}keysInNamespaces(r){let n=[];for(let e of r){let i=`${e}-`;for(let u of this.values.keys())u.startsWith(i)&&u.indexOf("--",2)===-1&&(Ct(u,e)||n.push(u.slice(i.length)))}return n}get(r){for(let n of r){let e=this.values.get(n);if(e)return e.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return r=le(this.#n(r)),this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.#r(r[0]),r)):this.values.entries()}#r(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}#n(r){return this.prefix?`--${r.slice(3+this.prefix.length)}`:r}clearNamespace(r,n){let e=$t.get(r)??[];e:for(let i of this.values.keys())if(i.startsWith(r)){if(n!==0&&(this.getOptions(i)&n)!==n)continue;for(let u of e)if(i.startsWith(u))continue e;this.values.delete(i)}}#e(r,n){for(let e of n){let i=r!==null?`${e}-${r}`:e;if(!this.values.has(i))if(r!==null&&r.includes(".")){if(i=`${e}-${r.replaceAll(".","_")}`,!this.values.has(i))continue}else continue;if(!Ct(i,e))return i}return null}#t(r){return this.values.has(r)?`var(${de(this.#r(r))})`:null}markUsedVariable(r){let n=le(this.#n(r)),e=this.values.get(n);e&&(e.options|=16)}resolve(r,n){let e=this.#e(r,n);if(!e)return null;let i=this.values.get(e);return i.options&1?i.value:this.#t(e)}resolveValue(r,n){let e=this.#e(r,n);return e?this.values.get(e).value:null}resolveWith(r,n,e=[]){let i=this.#e(r,n);if(!i)return null;let u={};for(let p of e){let f=`${i}${p}`,s=this.values.get(f);s&&(s.options&1?u[p]=s.value:u[p]=this.#t(f))}let a=this.values.get(i);return a.options&1?[a.value,u]:[this.#t(i),u]}namespace(r){let n=new Map,e=`${r}-`;for(let[i,u]of this.values)i===r?n.set(null,u.value):i.startsWith(`${e}-`)?n.set(i.slice(r.length),u.value):i.startsWith(e)&&n.set(i.slice(e.length),u.value);return n}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};var F=class extends Map{constructor(n){super();this.factory=n}get(n){let e=super.get(n);return e===void 0&&(e=this.factory(n,this),this.set(n,e)),e}};var Zr=64;function j(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function D(t,r="",n=[]){return{kind:"at-rule",name:t,params:r,nodes:n}}function M(t,r=[]){return t.charCodeAt(0)===Zr?xe(t,r):j(t,r)}function l(t,r,n=!1){return{kind:"declaration",property:t,value:r,important:n}}function De(t){return{kind:"comment",value:t}}function Q(t,r){return{kind:"context",context:t,nodes:r}}function U(t){return{kind:"at-root",nodes:t}}function _(t,r,n=[],e={}){for(let i=0;i<t.length;i++){let u=t[i],a=n[n.length-1]??null;if(u.kind==="context"){if(_(u.nodes,r,n,{...e,...u.context})===2)return 2;continue}n.push(u);let p=!1,f=0,s=r(u,{parent:a,context:e,path:n,replaceWith(d){p=!0,Array.isArray(d)?d.length===0?(t.splice(i,1),f=0):d.length===1?(t[i]=d[0],f=1):(t.splice(i,1,...d),f=d.length):(t[i]=d,f=1)}})??0;if(n.pop(),p){s===0?i--:i+=f-1;continue}if(s===2)return 2;if(s!==1&&"nodes"in u){n.push(u);let d=_(u.nodes,r,n,e);if(n.pop(),d===2)return 2}}}function Ue(t,r,n=[],e={}){for(let i=0;i<t.length;i++){let u=t[i],a=n[n.length-1]??null;if(u.kind==="rule"||u.kind==="at-rule")n.push(u),Ue(u.nodes,r,n,e),n.pop();else if(u.kind==="context"){Ue(u.nodes,r,n,{...e,...u.context});continue}n.push(u),r(u,{parent:a,context:e,path:n,replaceWith(p){Array.isArray(p)?p.length===0?t.splice(i,1):p.length===1?t[i]=p[0]:t.splice(i,1,...p):t[i]=p,i+=p.length-1}}),n.pop()}}function ae(t,r){let n=[],e=new Set,i=new F(()=>new Set),u=new Set,a=new Set;function p(s,d,m={},g=0){if(s.kind==="declaration"){if(s.property==="--tw-sort"||s.value===void 0||s.value===null)return;if(m.theme&&s.property[0]==="-"&&s.property[1]==="-"&&i.get(d).add(s),s.value.includes("var(")&&r.trackUsedVariables(s.value),s.property==="animation"){let v=s.value.split(/\s+/);for(let b of v)a.add(b)}d.push(s)}else if(s.kind==="rule")if(s.selector==="&")for(let v of s.nodes){let b=[];p(v,b,m,g+1),b.length>0&&d.push(...b)}else{let v={...s,nodes:[]};for(let b of s.nodes)p(b,v.nodes,m,g+1);v.nodes.length>0&&d.push(v)}else if(s.kind==="at-rule"&&s.name==="@property"&&g===0){if(e.has(s.params))return;e.add(s.params);let v={...s,nodes:[]};for(let b of s.nodes)p(b,v.nodes,m,g+1);d.push(v)}else if(s.kind==="at-rule"){let v={...s,nodes:[]};for(let b of s.nodes)p(b,v.nodes,m,g+1);s.name==="@keyframes"&&m.theme&&u.add(v),(v.nodes.length>0||v.name==="@layer"||v.name==="@charset"||v.name==="@custom-media"||v.name==="@namespace"||v.name==="@import")&&d.push(v)}else if(s.kind==="at-root")for(let v of s.nodes){let b=[];p(v,b,m,0);for(let y of b)n.push(y)}else if(s.kind==="context"){if(s.context.reference)return;for(let v of s.nodes)p(v,d,{...m,...s.context},g)}else s.kind==="comment"&&d.push(s)}let f=[];for(let s of t)p(s,f,{},0);if(!1){e:for(let[s,d]of i)for(let m of d){if(r.theme.getOptions(m.property)&24){if(m.property.startsWith("--animate-")){let b=m.value.split(/\s+/);for(let y of b)a.add(y)}continue}let v=s.indexOf(m);if(s.splice(v,1),s.length===0){for(let[b,y]of f.entries())if(y.kind==="rule"&&y.nodes===s){f.splice(b,1);break}continue e}}for(let s of u)if(!a.has(s.params)){let d=n.indexOf(s);n.splice(d,1)}}return f.concat(n)}function G(t){function r(e,i=0){let u="",a="  ".repeat(i);if(e.kind==="declaration")u+=`${a}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){u+=`${a}${e.selector} {
`;for(let p of e.nodes)u+=r(p,i+1);u+=`${a}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${a}${e.name} ${e.params};
`;u+=`${a}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let p of e.nodes)u+=r(p,i+1);u+=`${a}}
`}else if(e.kind==="comment")u+=`${a}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return u}let n="";for(let e of t){let i=r(e);i!==""&&(n+=i)}return n}function nt(t){return{kind:"word",value:t}}function Qr(t,r){return{kind:"function",value:t,nodes:r}}function Xr(t){return{kind:"separator",value:t}}function X(t,r,n=null){for(let e=0;e<t.length;e++){let i=t[e],u=!1,a=0,p=r(i,{parent:n,replaceWith(f){u=!0,Array.isArray(f)?f.length===0?(t.splice(e,1),a=0):f.length===1?(t[e]=f[0],a=1):(t.splice(e,1,...f),a=f.length):t[e]=f}})??0;if(u){p===0?e--:e+=a-1;continue}if(p===2)return 2;if(p!==1&&i.kind==="function"&&X(i.nodes,r,i)===2)return 2}}function q(t){let r="";for(let n of t)switch(n.kind){case"word":case"separator":{r+=n.value;break}case"function":r+=n.value+"("+q(n.nodes)+")"}return r}var Nt=92,en=41,Vt=58,Tt=44,tn=34,St=61,Et=62,Rt=60,Ot=10,rn=40,nn=39,Kt=47,Pt=32,Dt=9;function L(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,i="",u;for(let a=0;a<t.length;a++){let p=t.charCodeAt(a);switch(p){case Nt:{i+=t[a]+t[a+1],a++;break}case Vt:case Tt:case St:case Et:case Rt:case Ot:case Kt:case Pt:case Dt:{if(i.length>0){let m=nt(i);e?e.nodes.push(m):r.push(m),i=""}let f=a,s=a+1;for(;s<t.length&&(u=t.charCodeAt(s),!(u!==Vt&&u!==Tt&&u!==St&&u!==Et&&u!==Rt&&u!==Ot&&u!==Kt&&u!==Pt&&u!==Dt));s++);a=s-1;let d=Xr(t.slice(f,s));e?e.nodes.push(d):r.push(d);break}case nn:case tn:{let f=a;for(let s=a+1;s<t.length;s++)if(u=t.charCodeAt(s),u===Nt)s+=1;else if(u===p){a=s;break}i+=t.slice(f,a+1);break}case rn:{let f=Qr(i,[]);i="",e?e.nodes.push(f):r.push(f),n.push(f),e=f;break}case en:{let f=n.pop();if(i.length>0){let s=nt(i);f.nodes.push(s),i=""}n.length>0?e=n[n.length-1]:e=null;break}default:i+=String.fromCharCode(p)}}return i.length>0&&r.push(nt(i)),r}var it=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"],je=["anchor-size"],_t=new RegExp(`(${je.join("|")})\\(`,"g");function Ce(t){return t.indexOf("(")!==-1&&it.some(r=>t.includes(`${r}(`))}function Ut(t){if(!it.some(i=>t.includes(i)))return t;let r=!1;je.some(i=>t.includes(i))&&(_t.lastIndex=0,t=t.replace(_t,(i,u)=>(r=!0,`$${je.indexOf(u)}$(`)));let n="",e=[];for(let i=0;i<t.length;i++){let u=t[i];if(u==="("){n+=u;let a=i;for(let f=i-1;f>=0;f--){let s=t.charCodeAt(f);if(s>=48&&s<=57)a=f;else if(s>=97&&s<=122)a=f;else break}let p=t.slice(a,i);if(it.includes(p)){e.unshift(!0);continue}else if(e[0]&&p===""){e.unshift(!0);continue}e.unshift(!1);continue}else if(u===")")n+=u,e.shift();else if(u===","&&e[0]){n+=", ";continue}else{if(u===" "&&e[0]&&n[n.length-1]===" ")continue;if((u==="+"||u==="*"||u==="/"||u==="-")&&e[0]){let a=n.trimEnd(),p=a[a.length-1];if(p==="+"||p==="*"||p==="/"||p==="-"){n+=u;continue}else if(p==="("||p===","){n+=u;continue}else t[i-1]===" "?n+=`${u} `:n+=` ${u} `}else if(e[0]&&t.startsWith("to-zero",i)){let a=i;i+=7,n+=t.slice(a,i+1)}else n+=u}}return r?n.replace(/\$(\d+)\$/g,(i,u)=>je[u]??i):n}function te(t){if(t.indexOf("(")===-1)return pe(t);let r=L(t);return ot(r),t=q(r),t=Ut(t),t}function pe(t,r=!1){let n="";for(let e=0;e<t.length;e++){let i=t[e];i==="\\"&&t[e+1]==="_"?(n+="_",e+=1):i==="_"&&!r?n+=" ":n+=i}return n}function ot(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=pe(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=pe(r.value);for(let n=0;n<r.nodes.length;n++){if(n==0&&r.nodes[n].kind==="word"){r.nodes[n].value=pe(r.nodes[n].value,!0);continue}ot([r.nodes[n]])}break}r.value=pe(r.value),ot(r.nodes);break}case"separator":case"word":{r.value=pe(r.value);break}default:on(r)}}function on(t){throw new Error(`Unexpected value: ${t}`)}var Ie=new Uint8Array(256);function P(t,r){let n=0,e=[],i=0,u=t.length,a=r.charCodeAt(0);for(let p=0;p<u;p++){let f=t.charCodeAt(p);if(n===0&&f===a){e.push(t.slice(i,p)),i=p+1;continue}switch(f){case 92:p+=1;break;case 39:case 34:for(;++p<u;){let s=t.charCodeAt(p);if(s===92){p+=1;continue}if(s===f)break}break;case 40:Ie[n]=41,n++;break;case 91:Ie[n]=93,n++;break;case 123:Ie[n]=125,n++;break;case 93:case 125:case 41:n>0&&f===Ie[n-1]&&n--;break}}return e.push(t.slice(i)),e}var ln=58,jt=45,It=97,Ft=122;function*zt(t,r){let n=P(t,":");if(r.theme.prefix){if(n.length===1||n[0]!==r.theme.prefix)return null;n.shift()}let e=n.pop(),i=[];for(let m=n.length-1;m>=0;--m){let g=r.parseVariant(n[m]);if(g===null)return;i.push(g)}let u=!1;e[e.length-1]==="!"?(u=!0,e=e.slice(0,-1)):e[0]==="!"&&(u=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:i,important:u,raw:t});let[a,p=null,f]=P(e,"/");if(f)return;let s=p===null?null:lt(p);if(p!==null&&s===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let m=a.charCodeAt(1);if(m!==jt&&!(m>=It&&m<=Ft))return;a=a.slice(1,-1);let g=a.indexOf(":");if(g===-1||g===0||g===a.length-1)return;let v=a.slice(0,g),b=te(a.slice(g+1));yield{kind:"arbitrary",property:v,value:b,modifier:s,variants:i,important:u,raw:t};return}let d;if(a[a.length-1]==="]"){let m=a.indexOf("-[");if(m===-1)return;let g=a.slice(0,m);if(!r.utilities.has(g,"functional"))return;let v=a.slice(m+1);d=[[g,v]]}else if(a[a.length-1]===")"){let m=a.indexOf("-(");if(m===-1)return;let g=a.slice(0,m);if(!r.utilities.has(g,"functional"))return;let v=a.slice(m+2,-1),b=P(v,":"),y=null;if(b.length===2&&(y=b[0],v=b[1]),v[0]!=="-"&&v[1]!=="-")return;d=[[g,y===null?`[var(${v})]`:`[${y}:var(${v})]`]]}else d=Mt(a,m=>r.utilities.has(m,"functional"));for(let[m,g]of d){let v={kind:"functional",root:m,modifier:s,value:null,variants:i,important:u,raw:t};if(g===null){yield v;continue}{let b=g.indexOf("[");if(b!==-1){if(g[g.length-1]!=="]")return;let x=te(g.slice(b+1,-1)),T="";for(let S=0;S<x.length;S++){let O=x.charCodeAt(S);if(O===ln){T=x.slice(0,S),x=x.slice(S+1);break}if(!(O===jt||O>=It&&O<=Ft))break}if(x.length===0||x.trim().length===0)continue;v.value={kind:"arbitrary",dataType:T||null,value:x}}else{let x=p===null||v.modifier?.kind==="arbitrary"?null:`${g}/${p}`;v.value={kind:"named",value:g,fraction:x}}}yield v}}function lt(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=te(t.slice(1,-1));return r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=te(t.slice(1,-1));return r.length===0||r.trim().length===0||r[0]!=="-"&&r[1]!=="-"?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function Lt(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let n=te(t.slice(1,-1));if(n.length===0||n.trim().length===0)return null;let e=n[0]===">"||n[0]==="+"||n[0]==="~";return!e&&n[0]!=="@"&&!n.includes("&")&&(n=`&:is(${n})`),{kind:"arbitrary",selector:n,relative:e}}{let[n,e=null,i]=P(t,"/");if(i)return null;let u=Mt(n,a=>r.variants.has(a));for(let[a,p]of u)switch(r.variants.kind(a)){case"static":return p!==null||e!==null?null:{kind:"static",root:a};case"functional":{let f=e===null?null:lt(e);if(e!==null&&f===null)return null;if(p===null)return{kind:"functional",root:a,modifier:f,value:null};if(p[p.length-1]==="]"){if(p[0]!=="[")continue;let s=te(p.slice(1,-1));return s.length===0||s.trim().length===0?null:{kind:"functional",root:a,modifier:f,value:{kind:"arbitrary",value:s}}}if(p[p.length-1]===")"){if(p[0]!=="(")continue;let s=te(p.slice(1,-1));return s.length===0||s.trim().length===0||s[0]!=="-"&&s[1]!=="-"?null:{kind:"functional",root:a,modifier:f,value:{kind:"arbitrary",value:`var(${s})`}}}return{kind:"functional",root:a,modifier:f,value:{kind:"named",value:p}}}case"compound":{if(p===null)return null;let f=r.parseVariant(p);if(f===null||!r.variants.compoundsWith(a,f))return null;let s=e===null?null:lt(e);return e!==null&&s===null?null:{kind:"compound",root:a,modifier:s,variant:f}}}}return null}function*Mt(t,r){r(t)&&(yield[t,null]);let n=t.lastIndexOf("-");if(n===-1){t[0]==="@"&&r("@")&&(yield["@",t.slice(1)]);return}do{let e=t.slice(0,n);if(r(e)){let i=[e,t.slice(n+1)];if(i[1]==="")break;yield i}n=t.lastIndexOf("-",n-1)}while(n>0)}function se(t,r,n){if(t===r)return 0;let e=t.indexOf("("),i=r.indexOf("("),u=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),a=i===-1?r.replace(/[\d.]+/g,""):r.slice(0,i),p=(u===a?0:u<a?-1:1)||(n==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(p)?t<r?-1:1:p}var an=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),sn=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function Wt(t){return t.charCodeAt(0)===35||sn.test(t)||an.has(t.toLowerCase())}var un={color:Wt,length:st,percentage:at,ratio:An,number:wn,integer:N,url:Bt,position:Nn,"bg-size":Vn,"line-width":fn,image:mn,"family-name":hn,"generic-name":gn,"absolute-size":vn,"relative-size":yn,angle:En,vector:On};function z(t,r){if(t.startsWith("var("))return null;for(let n of r)if(un[n]?.(t))return n;return null}var cn=/^url\(.*\)$/;function Bt(t){return cn.test(t)}function fn(t){return t==="thin"||t==="medium"||t==="thick"}var dn=/^(?:element|image|cross-fade|image-set)\(/,pn=/^(repeating-)?(conic|linear|radial)-gradient\(/;function mn(t){let r=0;for(let n of P(t,","))if(!n.startsWith("var(")){if(Bt(n)){r+=1;continue}if(pn.test(n)){r+=1;continue}if(dn.test(n)){r+=1;continue}return!1}return r>0}function gn(t){return t==="serif"||t==="sans-serif"||t==="monospace"||t==="cursive"||t==="fantasy"||t==="system-ui"||t==="ui-serif"||t==="ui-sans-serif"||t==="ui-monospace"||t==="ui-rounded"||t==="math"||t==="emoji"||t==="fangsong"}function hn(t){let r=0;for(let n of P(t,",")){let e=n.charCodeAt(0);if(e>=48&&e<=57)return!1;n.startsWith("var(")||(r+=1)}return r>0}function vn(t){return t==="xx-small"||t==="x-small"||t==="small"||t==="medium"||t==="large"||t==="x-large"||t==="xx-large"||t==="xxx-large"}function yn(t){return t==="larger"||t==="smaller"}var ee=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,bn=new RegExp(`^${ee.source}$`);function wn(t){return bn.test(t)||Ce(t)}var kn=new RegExp(`^${ee.source}%$`);function at(t){return kn.test(t)||Ce(t)}var xn=new RegExp(`^${ee.source}s*/s*${ee.source}$`);function An(t){return xn.test(t)||Ce(t)}var Cn=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],$n=new RegExp(`^${ee.source}(${Cn.join("|")})$`);function st(t){return $n.test(t)||Ce(t)}function Nn(t){let r=0;for(let n of P(t," ")){if(n==="center"||n==="top"||n==="right"||n==="bottom"||n==="left"){r+=1;continue}if(!n.startsWith("var(")){if(st(n)||at(n)){r+=1;continue}return!1}}return r>0}function Vn(t){let r=0;for(let n of P(t,",")){if(n==="cover"||n==="contain"){r+=1;continue}let e=P(n," ");if(e.length!==1&&e.length!==2)return!1;if(e.every(i=>i==="auto"||st(i)||at(i))){r+=1;continue}}return r>0}var Tn=["deg","rad","grad","turn"],Sn=new RegExp(`^${ee.source}(${Tn.join("|")})$`);function En(t){return Sn.test(t)}var Rn=new RegExp(`^${ee.source} +${ee.source} +${ee.source}$`);function On(t){return Rn.test(t)}function N(t){let r=Number(t);return Number.isInteger(r)&&r>=0&&String(r)===String(t)}function ut(t){let r=Number(t);return Number.isInteger(r)&&r>0&&String(r)===String(t)}function me(t){return qt(t,.25)}function Fe(t){return qt(t,.25)}function qt(t,r){let n=Number(t);return n>=0&&n%r===0&&String(n)===String(t)}var Kn=new Set(["inset","inherit","initial","revert","unset"]),Ht=/^-?(\d+|\.\d+)(.*?)$/g;function ue(t,r){return P(t,",").map(e=>{e=e.trim();let i=P(e," ").filter(s=>s.trim()!==""),u=null,a=null,p=null;for(let s of i)Kn.has(s)||(Ht.test(s)?(a===null?a=s:p===null&&(p=s),Ht.lastIndex=0):u===null&&(u=s));if(a===null||p===null)return e;let f=r(u??"currentcolor");return u!==null?e.replace(u,f):`${e} ${f}`}).join(", ")}var Dn=/^-?[a-z][a-zA-Z0-9/%._-]*$/,_n=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,ct=class{utilities=new F(()=>[]);completions=new Map;static(r,n){this.utilities.get(r).push({kind:"static",compileFn:n})}functional(r,n,e){this.utilities.get(r).push({kind:"functional",compileFn:n,options:e})}has(r,n){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===n)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,n){this.completions.set(r,n)}keys(r){let n=[];for(let[e,i]of this.utilities.entries())for(let u of i)if(u.kind===r){n.push(e);break}return n}};function $(t,r,n){return D("@property",t,[l("syntax",n?`"${n}"`:'"*"'),l("inherits","false"),...r?[l("initial-value",r)]:[]])}function J(t,r){if(r===null)return t;let n=Number(r);return Number.isNaN(n)||(r=`${n*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function W(t,r,n){if(!r)return t;if(r.kind==="arbitrary")return J(t,r.value);let e=n.resolve(r.value,["--opacity"]);return e?J(t,e):Fe(r.value)?J(t,`${r.value}%`):null}function H(t,r,n){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentColor";break}default:{e=r.resolve(t.value.value,n);break}}return e?W(e,t.modifier,r):null}function Jt(t){let r=new ct;function n(o,c){function*h(k){for(let w of t.keysInNamespaces(k))yield w.replaceAll("_",".")}r.suggest(o,()=>{let k=[];for(let w of c()){if(typeof w=="string"){k.push({values:[w],modifiers:[]});continue}let V=[...w.values??[],...h(w.valueThemeKeys??[])],R=[...w.modifiers??[],...h(w.modifierThemeKeys??[])];w.hasDefaultValue&&V.unshift(null),k.push({supportsNegative:w.supportsNegative,values:V,modifiers:R})}return k})}function e(o,c){r.static(o,()=>c.map(h=>typeof h=="function"?h():l(h[0],h[1])))}function i(o,c){function h({negative:k}){return w=>{let V=null;if(w.value)if(w.value.kind==="arbitrary"){if(w.modifier)return;V=w.value.value}else{if(V=t.resolve(w.value.fraction??w.value.value,c.themeKeys??[]),V===null&&c.supportsFractions&&w.value.fraction){let[R,C]=P(w.value.fraction,"/");if(!N(R)||!N(C))return;V=`calc(${w.value.fraction} * 100%)`}if(V===null&&k&&c.handleNegativeBareValue){if(V=c.handleNegativeBareValue(w.value),!V?.includes("/")&&w.modifier)return;if(V!==null)return c.handle(V)}if(V===null&&c.handleBareValue&&(V=c.handleBareValue(w.value),!V?.includes("/")&&w.modifier))return}else{if(w.modifier)return;V=c.defaultValue!==void 0?c.defaultValue:t.resolve(null,c.themeKeys??[])}if(V!==null)return c.handle(k?`calc(${V} * -1)`:V)}}c.supportsNegative&&r.functional(`-${o}`,h({negative:!0})),r.functional(o,h({negative:!1})),n(o,()=>[{supportsNegative:c.supportsNegative,valueThemeKeys:c.themeKeys??[],hasDefaultValue:c.defaultValue!==void 0&&c.defaultValue!==null}])}function u(o,c){r.functional(o,h=>{if(!h.value)return;let k=null;if(h.value.kind==="arbitrary"?(k=h.value.value,k=W(k,h.modifier,t)):k=H(h,t,c.themeKeys),k!==null)return c.handle(k)}),n(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:c.themeKeys,modifiers:Array.from({length:21},(h,k)=>`${k*5}`)}])}function a(o,c,h,{supportsNegative:k=!1,supportsFractions:w=!1}={}){k&&r.static(`-${o}-px`,()=>h("-1px")),r.static(`${o}-px`,()=>h("1px")),i(o,{themeKeys:c,supportsFractions:w,supportsNegative:k,defaultValue:null,handleBareValue:({value:V})=>{let R=t.resolve(null,["--spacing"]);return!R||!me(V)?null:`calc(${R} * ${V})`},handleNegativeBareValue:({value:V})=>{let R=t.resolve(null,["--spacing"]);return!R||!me(V)?null:`calc(${R} * -${V})`},handle:h}),n(o,()=>[{values:t.get(["--spacing"])?["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"]:[],supportsNegative:k,valueThemeKeys:c}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,c]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[c,"auto"]]),e(`${o}-full`,[[c,"100%"]]),e(`-${o}-full`,[[c,"-100%"]]),a(o,["--inset","--spacing"],h=>[l(c,h)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),i("z",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--z-index"],handle:o=>[l("z-index",o)]}),n("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),e("order-none",[["order","0"]]),i("order",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--order"],handle:o=>[l("order",o)]}),n("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),i("col",{themeKeys:["--grid-column"],handle:o=>[l("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),i("col-span",{handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[l("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),i("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[l("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),i("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[l("grid-column-end",o)]}),n("col-span",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:[]}]),n("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-column-start"]}]),n("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),i("row",{themeKeys:["--grid-row"],handle:o=>[l("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),i("row-span",{themeKeys:[],handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[l("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),i("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[l("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),i("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>N(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[l("grid-row-end",o)]}),n("row-span",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:[]}]),n("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-row-start"]}]),n("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,c]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[c,"auto"]]),a(o,["--margin","--spacing"],h=>[l(c,h)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),i("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",o)]}),n("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),i("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[c,h]=P(o,"/");return!N(c)||!N(h)?null:o},handle:o=>[l("aspect-ratio",o)]});for(let[o,c]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",c],["height",c]]),e(`w-${o}`,[["width",c]]),e(`min-w-${o}`,[["min-width",c]]),e(`max-w-${o}`,[["max-width",c]]),e(`h-${o}`,[["height",c]]),e(`min-h-${o}`,[["min-height",c]]),e(`max-h-${o}`,[["max-height",c]]);e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],o=>[l("--tw-sort","size"),l("width",o),l("height",o)],{supportsFractions:!0});for(let[o,c,h]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(o,c,k=>[l(h,k)],{supportsFractions:!0});r.static("container",()=>{let o=[...t.namespace("--breakpoint").values()];o.sort((h,k)=>se(h,k,"asc"));let c=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let h of o)c.push(D("@media",`(width >= ${h})`,[l("max-width",h)]));return c}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[l("flex",o.value.value)];if(o.value.fraction){let[c,h]=P(o.value.fraction,"/");return!N(c)||!N(h)?void 0:[l("flex",`calc(${o.value.fraction} * 100%)`)]}if(N(o.value.value))return o.modifier?void 0:[l("flex",o.value.value)]}}),i("shrink",{defaultValue:"1",handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[l("flex-shrink",o)]}),i("grow",{defaultValue:"1",handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[l("flex-grow",o)]}),n("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),n("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],o=>[l("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let p=()=>U([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-x",o),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-x",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),i("origin",{themeKeys:["--transform-origin"],handle:o=>[l("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),i("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[l("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),i("perspective",{themeKeys:["--perspective"],handle:o=>[l("perspective",o)]});let f=()=>U([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[f,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[f,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],o=>[f(),l("--tw-translate-x",o),l("--tw-translate-y",o),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[f,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[f,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${o}`,["--translate","--spacing"],c=>[f(),l(`--tw-translate-${o}`,c),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],o=>[f(),l("--tw-translate-z",o),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("-translate-z-px",[f,["--tw-translate-z","-1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-z-px",[f,["--tw-translate-z","1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-3d",[f,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let s=()=>U([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function d({negative:o}){return c=>{if(!c.value||c.modifier)return;let h;return c.value.kind==="arbitrary"?(h=c.value.value,[l("scale",h)]):(h=t.resolve(c.value.value,["--scale"]),!h&&N(c.value.value)&&(h=`${c.value.value}%`),h?(h=o?`calc(${h} * -1)`:h,[s(),l("--tw-scale-x",h),l("--tw-scale-y",h),l("--tw-scale-z",h),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",d({negative:!0})),r.functional("scale",d({negative:!1})),n("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])i(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:c})=>N(c)?`${c}%`:null,handle:c=>[s(),l(`--tw-scale-${o}`,c),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),n(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[s,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:o}){return c=>{if(!c.value||c.modifier)return;let h;if(c.value.kind==="arbitrary"){h=c.value.value;let k=c.value.dataType??z(h,["angle","vector"]);if(k==="vector")return[l("rotate",`${h} var(--tw-rotate)`)];if(k!=="angle")return[l("rotate",h)]}else if(h=t.resolve(c.value.value,["--rotate"]),!h&&N(c.value.value)&&(h=`${c.value.value}deg`),!h)return;return[l("rotate",o?`calc(${h} * -1)`:h)]}}r.functional("-rotate",m({negative:!0})),r.functional("rotate",m({negative:!1})),n("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),c=()=>U([$("--tw-rotate-x","rotateX(0)"),$("--tw-rotate-y","rotateY(0)"),$("--tw-rotate-z","rotateZ(0)"),$("--tw-skew-x","skewX(0)"),$("--tw-skew-y","skewY(0)")]);for(let h of["x","y","z"])i(`rotate-${h}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:k})=>N(k)?`${k}deg`:null,handle:k=>[c(),l(`--tw-rotate-${h}`,`rotate${h.toUpperCase()}(${k})`),l("transform",o)]}),n(`rotate-${h}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);i("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>N(h)?`${h}deg`:null,handle:h=>[c(),l("--tw-skew-x",`skewX(${h})`),l("--tw-skew-y",`skewY(${h})`),l("transform",o)]}),i("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>N(h)?`${h}deg`:null,handle:h=>[c(),l("--tw-skew-x",`skewX(${h})`),l("transform",o)]}),i("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>N(h)?`${h}deg`:null,handle:h=>[c(),l("--tw-skew-y",`skewY(${h})`),l("transform",o)]}),n("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),n("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",h=>{if(h.modifier)return;let k=null;if(h.value?h.value.kind==="arbitrary"&&(k=h.value.value):k=o,k!==null)return[c(),l("transform",k)]}),n("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);i("cursor",{themeKeys:["--cursor"],handle:o=>[l("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let g=()=>U([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[g,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[g,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[g,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let v=()=>U([$("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[v,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[v,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[v,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,c]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(o,["--scroll-margin","--spacing"],h=>[l(c,h)],{supportsNegative:!0});for(let[o,c]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(o,["--scroll-padding","--spacing"],h=>[l(c,h)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),i("list",{themeKeys:["--list-style-type"],handle:o=>[l("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),i("list-image",{themeKeys:["--list-style-image"],handle:o=>[l("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),i("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>N(o)?o:null,handle:o=>[l("columns",o)]}),n("columns",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),i("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[l("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),i("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[l("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),i("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>ut(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),i("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>ut(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-rows",o)]}),n("grid-cols",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-template-columns"]}]),n("grid-rows",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],o=>[l("gap",o)]),a("gap-x",["--gap","--spacing"],o=>[l("column-gap",o)]),a("gap-y",["--gap","--spacing"],o=>[l("row-gap",o)]),a("space-x",["--space","--spacing"],o=>[U([$("--tw-space-x-reverse","0")]),j(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],o=>[U([$("--tw-space-y-reverse","0")]),j(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>U([$("--tw-space-x-reverse","0")]),()=>j(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>U([$("--tw-space-y-reverse","0")]),()=>j(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),u("accent",{themeKeys:["--accent-color","--color"],handle:o=>[l("accent-color",o)]}),u("caret",{themeKeys:["--caret-color","--color"],handle:o=>[l("caret-color",o)]}),u("divide",{themeKeys:["--divide-color","--color"],handle:o=>[j(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]);for(let[o,c]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,c.map(h=>[h,"0"])),e(`${o}-full`,c.map(h=>[h,"calc(infinity * 1px)"])),i(o,{themeKeys:["--radius"],handle:h=>c.map(k=>l(k,h))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let c=function(h,k){r.functional(h,w=>{if(!w.value){if(w.modifier)return;let V=t.get(["--default-border-width"])??"1px",R=k.width(V);return R?[o(),...R]:void 0}if(w.value.kind==="arbitrary"){let V=w.value.value;switch(w.value.dataType??z(V,["color","line-width","length"])){case"line-width":case"length":{if(w.modifier)return;let C=k.width(V);return C?[o(),...C]:void 0}default:return V=W(V,w.modifier,t),V===null?void 0:k.color(V)}}{let V=H(w,t,["--border-color","--color"]);if(V)return k.color(V)}{if(w.modifier)return;let V=t.resolve(w.value.value,["--border-width"]);if(V){let R=k.width(V);return R?[o(),...R]:void 0}if(N(w.value.value)){let R=k.width(`${w.value.value}px`);return R?[o(),...R]:void 0}}}),n(h,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(w,V)=>`${V*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var x=c;let o=()=>U([$("--tw-border-style","solid")]);c("border",{width:h=>[l("border-style","var(--tw-border-style)"),l("border-width",h)],color:h=>[l("border-color",h)]}),c("border-x",{width:h=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",h)],color:h=>[l("border-inline-color",h)]}),c("border-y",{width:h=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",h)],color:h=>[l("border-block-color",h)]}),c("border-s",{width:h=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",h)],color:h=>[l("border-inline-start-color",h)]}),c("border-e",{width:h=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",h)],color:h=>[l("border-inline-end-color",h)]}),c("border-t",{width:h=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",h)],color:h=>[l("border-top-color",h)]}),c("border-r",{width:h=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",h)],color:h=>[l("border-right-color",h)]}),c("border-b",{width:h=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",h)],color:h=>[l("border-bottom-color",h)]}),c("border-l",{width:h=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",h)],color:h=>[l("border-left-color",h)]}),i("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>N(h)?`${h}px`:null,handle:h=>[U([$("--tw-divide-x-reverse","0")]),j(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),o(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${h} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${h} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),i("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>N(h)?`${h}px`:null,handle:h=>[U([$("--tw-divide-y-reverse","0")]),j(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),o(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${h} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${h} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),n("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),n("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>U([$("--tw-divide-x-reverse","0")]),()=>j(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>U([$("--tw-divide-y-reverse","0")]),()=>j(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let h of["solid","dashed","dotted","double","none"])e(`divide-${h}`,[()=>j(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",h),l("border-style",h)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-center",[["background-position","center"]]),e("bg-top",[["background-position","top"]]),e("bg-right-top",[["background-position","right top"]]),e("bg-right",[["background-position","right"]]),e("bg-right-bottom",[["background-position","right bottom"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-left-bottom",[["background-position","left bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-left-top",[["background-position","left top"]]),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let h=function(V){let R="in oklab";if(V?.kind==="named")switch(V.value){case"longer":case"shorter":case"increasing":case"decreasing":R=`in oklch ${V.value} hue`;break;default:R=`in ${V.value}`}else V?.kind==="arbitrary"&&(R=V.value);return R},k=function({negative:V}){return R=>{if(!R.value)return;if(R.value.kind==="arbitrary"){if(R.modifier)return;let I=R.value.value;switch(R.value.dataType??z(I,["angle"])){case"angle":return I=V?`calc(${I} * -1)`:`${I}`,[l("--tw-gradient-position",I),l("background-image",`linear-gradient(var(--tw-gradient-stops,${I}))`)];default:return V?void 0:[l("--tw-gradient-position",I),l("background-image",`linear-gradient(var(--tw-gradient-stops,${I}))`)]}}let C=R.value.value;if(!V&&c.has(C))C=c.get(C);else if(N(C))C=V?`calc(${C}deg * -1)`:`${C}deg`;else return;let A=h(R.modifier);return[l("--tw-gradient-position",`${C} ${A}`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},w=function({negative:V}){return R=>{if(R.value?.kind==="arbitrary"){if(R.modifier)return;let I=R.value.value;return[l("--tw-gradient-position",I),l("background-image",`conic-gradient(var(--tw-gradient-stops,${I}))`)]}let C=h(R.modifier);if(!R.value)return[l("--tw-gradient-position",C),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let A=R.value.value;if(N(A))return A=V?`calc(${A} * -1)`:`${A}deg`,[l("--tw-gradient-position",`from ${A} ${C}`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var T=h,S=k,O=w;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],c=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",k({negative:!0})),r.functional("bg-linear",k({negative:!1})),n("bg-linear",()=>[{values:[...c.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("-bg-conic",w({negative:!0})),r.functional("bg-conic",w({negative:!1})),n("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("bg-radial",V=>{if(!V.value){let R=h(V.modifier);return[l("--tw-gradient-position",R),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(V.value.kind==="arbitrary"){if(V.modifier)return;let R=V.value.value;return[l("--tw-gradient-position",R),l("background-image",`radial-gradient(var(--tw-gradient-stops,${R}))`)]}}),n("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}r.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??z(c,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("background-position",c)];case"bg-size":case"length":case"size":return o.modifier?void 0:[l("background-size",c)];case"image":case"url":return o.modifier?void 0:[l("background-image",c)];default:return c=W(c,o.modifier,t),c===null?void 0:[l("background-color",c)]}}{let c=H(o,t,["--background-color","--color"]);if(c)return[l("background-color",c)]}{if(o.modifier)return;let c=t.resolve(o.value.value,["--background-image"]);if(c)return[l("background-image",c)]}}}),n("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let b=()=>U([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function y(o,c){r.functional(o,h=>{if(h.value){if(h.value.kind==="arbitrary"){let k=h.value.value;switch(h.value.dataType??z(k,["color","length","percentage"])){case"length":case"percentage":return h.modifier?void 0:c.position(k);default:return k=W(k,h.modifier,t),k===null?void 0:c.color(k)}}{let k=H(h,t,["--background-color","--color"]);if(k)return c.color(k)}{if(h.modifier)return;let k=t.resolve(h.value.value,["--gradient-color-stop-positions"]);if(k)return c.position(k);if(h.value.value[h.value.value.length-1]==="%"&&N(h.value.value.slice(0,-1)))return c.position(h.value.value)}}}),n(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,k)=>`${k*5}`)},{values:Array.from({length:21},(h,k)=>`${k*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}y("from",{color:o=>[b(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[b(),l("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),y("via",{color:o=>[b(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",o),l("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[b(),l("--tw-gradient-via-position",o)]}),y("to",{color:o=>[b(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[b(),l("--tw-gradient-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let h=W(o.value.value,o.modifier,t);return h===null?void 0:[l("fill",h)]}let c=H(o,t,["--fill","--color"]);if(c)return[l("fill",c)]}),n("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??z(c,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[l("stroke-width",c)];default:return c=W(o.value.value,o.modifier,t),c===null?void 0:[l("stroke",c)]}}{let c=H(o,t,["--stroke","--color"]);if(c)return[l("stroke",c)]}{let c=t.resolve(o.value.value,["--stroke-width"]);if(c)return[l("stroke-width",c)];if(N(o.value.value))return[l("stroke-width",o.value.value)]}}}),n("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-bottom",[["object-position","bottom"]]),e("object-center",[["object-position","center"]]),e("object-left",[["object-position","left"]]),e("object-left-bottom",[["object-position","left bottom"]]),e("object-left-top",[["object-position","left top"]]),e("object-right",[["object-position","right"]]),e("object-right-bottom",[["object-position","right bottom"]]),e("object-right-top",[["object-position","right top"]]),e("object-top",[["object-position","top"]]),i("object",{themeKeys:["--object-position"],handle:o=>[l("object-position",o)]});for(let[o,c]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(o,["--padding","--spacing"],h=>[l(c,h)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],o=>[l("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),i("align",{themeKeys:[],handle:o=>[l("vertical-align",o)]}),r.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??z(c,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",c)];default:return[U([$("--tw-font-weight")]),l("--tw-font-weight",c),l("font-weight",c)]}}{let c=t.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(c){let[h,k={}]=c;return[l("font-family",h),l("font-feature-settings",k["--font-feature-settings"]),l("font-variation-settings",k["--font-variation-settings"])]}}{let c=t.resolve(o.value.value,["--font-weight"]);if(c)return[U([$("--tw-font-weight")]),l("--tw-font-weight",c),l("font-weight",c)]}}}),n("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),i("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let c=Number(o.slice(0,-1));return!N(c)||Number.isNaN(c)||c<50||c>200?null:o},handle:o=>[l("font-stretch",o)]}),n("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),u("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[j("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??z(c,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[l("text-decoration-thickness",c)];default:return c=W(c,o.modifier,t),c===null?void 0:[l("text-decoration-color",c)]}}{let c=t.resolve(o.value.value,["--text-decoration-thickness"]);if(c)return o.modifier?void 0:[l("text-decoration-thickness",c)];if(N(o.value.value))return o.modifier?void 0:[l("text-decoration-thickness",`${o.value.value}px`)]}{let c=H(o,t,["--text-decoration-color","--color"]);if(c)return[l("text-decoration-color",c)]}}}),n("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),i("animate",{themeKeys:["--animate"],handle:o=>[l("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),c=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),h=()=>U([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia")]),k=()=>U([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);r.functional("filter",w=>{if(!w.modifier){if(w.value===null)return[h(),l("filter",o)];if(w.value.kind==="arbitrary")return[l("filter",w.value.value)];switch(w.value.value){case"none":return[l("filter","none")]}}}),r.functional("backdrop-filter",w=>{if(!w.modifier){if(w.value===null)return[k(),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)];if(w.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",w.value.value),l("backdrop-filter",w.value.value)];switch(w.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),i("blur",{themeKeys:["--blur"],handle:w=>[h(),l("--tw-blur",`blur(${w})`),l("filter",o)]}),e("blur-none",[h,["--tw-blur"," "],["filter",o]]),i("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:w=>[k(),l("--tw-backdrop-blur",`blur(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),e("backdrop-blur-none",[k,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",c],["backdrop-filter",c]]),i("brightness",{themeKeys:["--brightness"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[h(),l("--tw-brightness",`brightness(${w})`),l("filter",o)]}),i("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[k(),l("--tw-backdrop-brightness",`brightness(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),n("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),i("contrast",{themeKeys:["--contrast"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[h(),l("--tw-contrast",`contrast(${w})`),l("filter",o)]}),i("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[k(),l("--tw-backdrop-contrast",`contrast(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),n("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),i("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[h(),l("--tw-grayscale",`grayscale(${w})`),l("filter",o)]}),i("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[k(),l("--tw-backdrop-grayscale",`grayscale(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),n("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),i("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:w})=>N(w)?`${w}deg`:null,handle:w=>[h(),l("--tw-hue-rotate",`hue-rotate(${w})`),l("filter",o)]}),i("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:w})=>N(w)?`${w}deg`:null,handle:w=>[k(),l("--tw-backdrop-hue-rotate",`hue-rotate(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),n("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),i("invert",{themeKeys:["--invert"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[h(),l("--tw-invert",`invert(${w})`),l("filter",o)]}),i("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[k(),l("--tw-backdrop-invert",`invert(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),n("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),i("saturate",{themeKeys:["--saturate"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[h(),l("--tw-saturate",`saturate(${w})`),l("filter",o)]}),i("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,handle:w=>[k(),l("--tw-backdrop-saturate",`saturate(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),n("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),i("sepia",{themeKeys:["--sepia"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[h(),l("--tw-sepia",`sepia(${w})`),l("filter",o)]}),i("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:w})=>N(w)?`${w}%`:null,defaultValue:"100%",handle:w=>[k(),l("--tw-backdrop-sepia",`sepia(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),n("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[h,["--tw-drop-shadow"," "],["filter",o]]),i("drop-shadow",{themeKeys:["--drop-shadow"],handle:w=>[h(),l("--tw-drop-shadow",P(w,",").map(V=>`drop-shadow(${V})`).join(" ")),l("filter",o)]}),i("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:w})=>Fe(w)?`${w}%`:null,handle:w=>[k(),l("--tw-backdrop-opacity",`opacity(${w})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),n("backdrop-opacity",()=>[{values:Array.from({length:21},(w,V)=>`${V*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,c=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",c]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",c]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",c]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",c]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",c]]),i("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:h=>[l("transition-property",h),l("transition-timing-function",o),l("transition-duration",c)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),i("delay",{handleBareValue:({value:h})=>N(h)?`${h}ms`:null,themeKeys:["--transition-delay"],handle:h=>[l("transition-delay",h)]});{let h=()=>U([$("--tw-duration")]);e("duration-initial",[h,["--tw-duration","initial"]]),r.functional("duration",k=>{if(k.modifier||!k.value)return;let w=null;if(k.value.kind==="arbitrary"?w=k.value.value:(w=t.resolve(k.value.fraction??k.value.value,["--transition-duration"]),w===null&&N(k.value.value)&&(w=`${k.value.value}ms`)),w!==null)return[h(),l("--tw-duration",w),l("transition-duration",w)]})}n("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),n("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>U([$("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),i("ease",{themeKeys:["--ease"],handle:c=>[o(),l("--tw-ease",c),l("transition-timing-function",c)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),i("will-change",{themeKeys:[],handle:o=>[l("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),i("content",{themeKeys:[],handle:o=>[U([$("--tw-content",'""')]),l("--tw-content",o),l("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",c=()=>U([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[c,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[c,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[c,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[c,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[c,["--tw-contain-style","style"],["contain",o]]),i("contain",{themeKeys:[],handle:h=>[l("contain",h)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>U([$("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),a("leading",["--leading","--spacing"],o=>[U([$("--tw-leading")]),l("--tw-leading",o),l("line-height",o)]),i("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[U([$("--tw-tracking")]),l("--tw-tracking",o),l("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",c=()=>U([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[c,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[c,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[c,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[c,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[c,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[c,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[c,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[c,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>U([$("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[l("outline-style","none"),D("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",c=>{if(c.value===null)return c.modifier?void 0:[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width","1px")];if(c.value.kind==="arbitrary"){let h=c.value.value;switch(c.value.dataType??z(h,["color","length","number","percentage"])){case"length":case"number":case"percentage":return c.modifier?void 0:[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)];default:return h=W(h,c.modifier,t),h===null?void 0:[l("outline-color",h)]}}{let h=H(c,t,["--outline-color","--color"]);if(h)return[l("outline-color",h)]}{if(c.modifier)return;let h=t.resolve(c.value.value,["--outline-width"]);if(h)return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)];if(N(c.value.value))return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${c.value.value}px`)]}}),n("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(c,h)=>`${h*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),i("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:c})=>N(c)?`${c}px`:null,handle:c=>[l("outline-offset",c)]}),n("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}i("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>Fe(o)?`${o}%`:null,handle:o=>[l("opacity",o)]}),n("opacity",()=>[{values:Array.from({length:21},(o,c)=>`${c*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),i("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>N(o)?`${o}px`:null,handle:o=>[l("text-underline-offset",o)]}),n("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??z(c,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let k=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!k&&me(o.modifier.value)){let w=t.resolve(null,["--spacing"]);if(!w)return null;k=`calc(${w} * ${o.modifier.value})`}return!k&&o.modifier.value==="none"&&(k="1"),k?[l("font-size",c),l("line-height",k)]:null}return[l("font-size",c)]}default:return c=W(c,o.modifier,t),c===null?void 0:[l("color",c)]}}{let c=H(o,t,["--text-color","--color"]);if(c)return[l("color",c)]}{let c=t.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(c){let[h,k={}]=Array.isArray(c)?c:[c];if(o.modifier){let w=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!w&&me(o.modifier.value)){let R=t.resolve(null,["--spacing"]);if(!R)return null;w=`calc(${R} * ${o.modifier.value})`}if(!w&&o.modifier.value==="none"&&(w="1"),!w)return null;let V=[l("font-size",h)];return w&&V.push(l("line-height",w)),V}return typeof k=="string"?[l("font-size",h),l("line-height",k)]:[l("font-size",h),l("line-height",k["--line-height"]?`var(--tw-leading, ${k["--line-height"]})`:void 0),l("letter-spacing",k["--letter-spacing"]?`var(--tw-tracking, ${k["--letter-spacing"]})`:void 0),l("font-weight",k["--font-weight"]?`var(--tw-font-weight, ${k["--font-weight"]})`:void 0)]}}}}),n("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);{let w=function(C){return`var(--tw-ring-inset,) 0 0 0 calc(${C} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${k})`},V=function(C){return`inset 0 0 0 ${C} var(--tw-inset-ring-color, currentColor)`};var E=w,K=V;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),c="0 0 #0000",h=()=>U([$("--tw-shadow",c),$("--tw-shadow-color"),$("--tw-inset-shadow",c),$("--tw-inset-shadow-color"),$("--tw-ring-color"),$("--tw-ring-shadow",c),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",c),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",c)]);e("shadow-initial",[h,["--tw-shadow-color","initial"]]),r.functional("shadow",C=>{if(!C.value){let A=t.get(["--shadow"]);return A===null?void 0:[h(),l("--tw-shadow",ue(A,I=>`var(--tw-shadow-color, ${I})`)),l("box-shadow",o)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color"])){case"color":return A=W(A,C.modifier,t),A===null?void 0:[h(),l("--tw-shadow-color",A)];default:return[h(),l("--tw-shadow",ue(A,Ee=>`var(--tw-shadow-color, ${Ee})`)),l("box-shadow",o)]}}switch(C.value.value){case"none":return C.modifier?void 0:[h(),l("--tw-shadow",c),l("box-shadow",o)]}{let A=t.get([`--shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[h(),l("--tw-shadow",ue(A,I=>`var(--tw-shadow-color, ${I})`)),l("box-shadow",o)]}{let A=H(C,t,["--box-shadow-color","--color"]);if(A)return[h(),l("--tw-shadow-color",A)]}}),n("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["none"],valueThemeKeys:["--shadow"],hasDefaultValue:!0}]),e("inset-shadow-initial",[h,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",C=>{if(!C.value){let A=t.get(["--inset-shadow"]);return A===null?void 0:[h(),l("--tw-inset-shadow",ue(A,I=>`var(--tw-inset-shadow-color, ${I})`)),l("box-shadow",o)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color"])){case"color":return A=W(A,C.modifier,t),A===null?void 0:[h(),l("--tw-inset-shadow-color",A)];default:return[h(),l("--tw-inset-shadow",`inset ${ue(A,Ee=>`var(--tw-inset-shadow-color, ${Ee})`)}`),l("box-shadow",o)]}}switch(C.value.value){case"none":return C.modifier?void 0:[h(),l("--tw-inset-shadow",c),l("box-shadow",o)]}{let A=t.get([`--inset-shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[h(),l("--tw-inset-shadow",ue(A,I=>`var(--tw-inset-shadow-color, ${I})`)),l("box-shadow",o)]}{let A=H(C,t,["--box-shadow-color","--color"]);if(A)return[h(),l("--tw-inset-shadow-color",A)]}}),n("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:[],valueThemeKeys:["--inset-shadow"],hasDefaultValue:!0}]),e("ring-inset",[h,["--tw-ring-inset","inset"]]);let k=t.get(["--default-ring-color"])??"currentColor";r.functional("ring",C=>{if(!C.value){if(C.modifier)return;let A=t.get(["--default-ring-width"])??"1px";return[h(),l("--tw-ring-shadow",w(A)),l("box-shadow",o)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color","length"])){case"length":return C.modifier?void 0:[h(),l("--tw-ring-shadow",w(A)),l("box-shadow",o)];default:return A=W(A,C.modifier,t),A===null?void 0:[l("--tw-ring-color",A)]}}{let A=H(C,t,["--ring-color","--color"]);if(A)return[l("--tw-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&N(C.value.value)&&(A=`${C.value.value}px`),A)return[h(),l("--tw-ring-shadow",w(A)),l("box-shadow",o)]}}),n("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",C=>{if(!C.value)return C.modifier?void 0:[h(),l("--tw-inset-ring-shadow",V("1px")),l("box-shadow",o)];if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color","length"])){case"length":return C.modifier?void 0:[h(),l("--tw-inset-ring-shadow",V(A)),l("box-shadow",o)];default:return A=W(A,C.modifier,t),A===null?void 0:[l("--tw-inset-ring-color",A)]}}{let A=H(C,t,["--ring-color","--color"]);if(A)return[l("--tw-inset-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&N(C.value.value)&&(A=`${C.value.value}px`),A)return[h(),l("--tw-inset-ring-shadow",V(A)),l("box-shadow",o)]}}),n("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let R="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",C=>{if(C.value){if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??z(A,["color","length"])){case"length":return C.modifier?void 0:[l("--tw-ring-offset-width",A),l("--tw-ring-offset-shadow",R)];default:return A=W(A,C.modifier,t),A===null?void 0:[l("--tw-ring-offset-color",A)]}}{let A=t.resolve(C.value.value,["--ring-offset-width"]);if(A)return C.modifier?void 0:[l("--tw-ring-offset-width",A),l("--tw-ring-offset-shadow",R)];if(N(C.value.value))return C.modifier?void 0:[l("--tw-ring-offset-width",`${C.value.value}px`),l("--tw-ring-offset-shadow",R)]}{let A=H(C,t,["--ring-offset-color","--color"]);if(A)return[l("--tw-ring-offset-color",A)]}}})}return n("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",o=>{let c=null;if(o.value===null?c="inline-size":o.value.kind==="arbitrary"?c=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(c="normal"),c!==null)return o.modifier?[l("container-type",c),l("container-name",o.modifier.value)]:[l("container-type",c)]}),n("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}function Yt(t){let r=t.params;return _n.test(r)?n=>{let e=new Set,i=new Set;_(t.nodes,u=>{if(u.kind!=="declaration"||!u.value||!u.value.includes("--value(")&&!u.value.includes("--modifier("))return;let a=L(u.value);X(a,p=>{if(p.kind!=="function"||p.value!=="--value"&&p.value!=="--modifier")return;let f=P(q(p.nodes),",");for(let[s,d]of f.entries())d=d.replace(/\\\*/g,"*"),d=d.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),d=d.replace(/\s+/g,""),d=d.replace(/(-\*){2,}/g,"-*"),d[0]==="-"&&d[1]==="-"&&!d.includes("-*")&&(d+="-*"),f[s]=d;p.nodes=L(f.join(","));for(let s of p.nodes)if(s.kind==="word"&&s.value[0]==="-"&&s.value[1]==="-"){let d=s.value.replace(/-\*.*$/g,"");p.value==="--value"?e.add(d):p.value==="--modifier"&&i.add(d)}}),u.value=q(a)}),n.utilities.functional(r.slice(0,-2),u=>{let a=structuredClone(t),p=u.value,f=u.modifier;if(p===null)return;let s=!1,d=!1,m=!1,g=!1,v=new Map,b=!1;if(_([a],(y,{parent:x,replaceWith:T})=>{if(x?.kind!=="rule"&&x?.kind!=="at-rule"||y.kind!=="declaration"||!y.value)return;let S=L(y.value);(X(S,(E,{replaceWith:K})=>{if(E.kind==="function"){if(E.value==="--value"){s=!0;let o=Gt(p,E,n);return o?(d=!0,o.ratio?b=!0:v.set(y,x),K(o.nodes),1):(s||=!1,T([]),2)}else if(E.value==="--modifier"){if(f===null)return T([]),1;m=!0;let o=Gt(f,E,n);return o?(g=!0,K(o.nodes),1):(m||=!1,T([]),2)}}})??0)===0&&(y.value=q(S))}),s&&!d||m&&!g||b&&g||f&&!b&&!g)return null;if(b)for(let[y,x]of v){let T=x.nodes.indexOf(y);T!==-1&&x.nodes.splice(T,1)}return a.nodes}),n.utilities.suggest(r.slice(0,-2),()=>[{values:n.theme.keysInNamespaces(e).map(u=>u.replaceAll("_",".")),modifiers:n.theme.keysInNamespaces(i).map(u=>u.replaceAll("_","."))}])}:Dn.test(r)?n=>{n.utilities.static(r,()=>structuredClone(t.nodes))}:null}function Gt(t,r,n){for(let e of r.nodes)if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let i=e.value;if(i.endsWith("-*")){i=i.slice(0,-2);let u=n.theme.resolve(t.value,[i]);if(u)return{nodes:L(u)}}else{let u=i.split("-*");if(u.length<=1)continue;let a=[u.shift()],p=n.theme.resolveWith(t.value,a,u);if(p){let[,f={}]=p;{let s=f[u.pop()];if(s)return{nodes:L(s)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(e.value!=="number"&&e.value!=="integer"&&e.value!=="ratio"&&e.value!=="percentage")continue;let i=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!i)continue;let u=z(i,[e.value]);if(u===null)continue;if(u==="ratio"){let[a,p]=P(i,"/");if(!N(a)||!N(p))continue}else{if(u==="number"&&!me(i))continue;if(u==="percentage"&&!N(i.slice(0,-1)))continue}return{nodes:L(i),ratio:u==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let i=e.value.slice(1,-1);if(i==="*")return{nodes:L(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==i)continue;if("dataType"in t&&t.dataType)return{nodes:L(t.value)};if(z(t.value,[i])!==null)return{nodes:L(t.value)}}}var ft={"--alpha":Un,"--spacing":jn,"--theme":In,theme:Xt};function Un(t,r,...n){let[e,i]=P(r,"/").map(u=>u.trim());if(!e||!i)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${e||"var(--my-color)"} / ${i||"50%"})\``);if(n.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${e||"var(--my-color)"} / ${i||"50%"})\``);return J(e,i)}function jn(t,r,...n){if(!r)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(n.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${n.length+1}.`);let e=t.theme.resolve(null,["--spacing"]);if(!e)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${e} * ${r})`}function In(t,r,...n){if(!r.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");return Xt(t,r,...n)}function Xt(t,r,...n){r=Fn(r);let e=t.resolveThemeValue(r);if(!e&&n.length>0)return n.join(", ");if(!e)throw new Error(`Could not resolve value for theme function: \`theme(${r})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return e}var Zt=new RegExp(Object.keys(ft).map(t=>`${t}\\(`).join("|"));function ge(t,r){let n=0;return _(t,e=>{if(e.kind==="declaration"&&e.value&&Zt.test(e.value)){n|=8,e.value=Qt(e.value,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&Zt.test(e.params)&&(n|=8,e.params=Qt(e.params,r))}),n}function Qt(t,r){let n=L(t);return X(n,(e,{replaceWith:i})=>{if(e.kind==="function"&&e.value in ft){let u=P(q(e.nodes).trim(),",").map(p=>p.trim()),a=ft[e.value](r,...u);return i(L(a))}}),q(n)}function Fn(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",n=t[0];for(let e=1;e<t.length-1;e++){let i=t[e],u=t[e+1];i==="\\"&&(u===n||u==="\\")?(r+=u,e++):r+=i}return r}function ze(t,r){let n=t.length,e=r.length,i=n<e?n:e;for(let u=0;u<i;u++){let a=t.charCodeAt(u),p=r.charCodeAt(u);if(a!==p){if(a>=48&&a<=57&&p>=48&&p<=57){let f=u,s=u+1,d=u,m=u+1;for(a=t.charCodeAt(s);a>=48&&a<=57;)a=t.charCodeAt(++s);for(p=r.charCodeAt(m);p>=48&&p<=57;)p=r.charCodeAt(++m);let g=t.slice(f,s),v=r.slice(d,m);return Number(g)-Number(v)||(g<v?-1:1)}return a-p}}return t.length-r.length}function er(t){let r=[];for(let n of t.utilities.keys("static"))r.push([n,{modifiers:[]}]);for(let n of t.utilities.keys("functional")){let e=t.utilities.getCompletions(n);for(let i of e)for(let u of i.values){let a=u===null?n:`${n}-${u}`;r.push([a,{modifiers:i.modifiers}]),i.supportsNegative&&r.push([`-${a}`,{modifiers:i.modifiers}])}}return r.sort((n,e)=>ze(n[0],e[0])),r}function tr(t){let r=[];for(let[e,i]of t.variants.entries()){let p=function({value:f,modifier:s}={}){let d=e;f&&(d+=u?`-${f}`:f),s&&(d+=`/${s}`);let m=t.parseVariant(d);if(!m)return[];let g=j(".__placeholder__",[]);if(ve(g,m,t.variants)===null)return[];let v=[];return Ue(g.nodes,(b,{path:y})=>{if(b.kind!=="rule"&&b.kind!=="at-rule"||b.nodes.length>0)return;y.sort((S,O)=>{let E=S.kind==="at-rule",K=O.kind==="at-rule";return E&&!K?-1:!E&&K?1:0});let x=y.flatMap(S=>S.kind==="rule"?S.selector==="&"?[]:[S.selector]:S.kind==="at-rule"?[`${S.name} ${S.params}`]:[]),T="";for(let S=x.length-1;S>=0;S--)T=T===""?x[S]:`${x[S]} { ${T} }`;v.push(T)}),v};var n=p;if(i.kind==="arbitrary")continue;let u=e!=="@",a=t.variants.getCompletions(e);switch(i.kind){case"static":{r.push({name:e,values:a,isArbitrary:!1,hasDash:u,selectors:p});break}case"functional":{r.push({name:e,values:a,isArbitrary:!0,hasDash:u,selectors:p});break}case"compound":{r.push({name:e,values:a,isArbitrary:!0,hasDash:u,selectors:p});break}}}return r}function rr(t,r){let{astNodes:n,nodeSorting:e}=re(Array.from(r),t),i=new Map(r.map(a=>[a,null])),u=0n;for(let a of n){let p=e.get(a)?.candidate;p&&i.set(p,i.get(p)??u++)}return r.map(a=>[a,i.get(a)??null])}var Le=/^@?[a-zA-Z0-9_-]*$/;var dt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,n,{compounds:e,order:i}={}){this.set(r,{kind:"static",applyFn:n,compoundsWith:0,compounds:e??2,order:i})}fromAst(r,n){let e=[];_(n,i=>{i.kind==="rule"?e.push(i.selector):i.kind==="at-rule"&&i.name!=="@slot"&&e.push(`${i.name} ${i.params}`)}),this.static(r,i=>{let u=structuredClone(n);pt(u,i.nodes),i.nodes=u},{compounds:ce(e)})}functional(r,n,{compounds:e,order:i}={}){this.set(r,{kind:"functional",applyFn:n,compoundsWith:0,compounds:e??2,order:i})}compound(r,n,e,{compounds:i,order:u}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:n,compounds:i??2,order:u})}group(r,n){this.groupOrder=this.nextOrder(),n&&this.compareFns.set(this.groupOrder,n),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,n){let e=this.variants.get(r),i=typeof n=="string"?this.variants.get(n):n.kind==="arbitrary"?{compounds:ce([n.selector])}:this.variants.get(n.root);return!(!e||!i||e.kind!=="compound"||i.compounds===0||e.compoundsWith===0||!(e.compoundsWith&i.compounds))}suggest(r,n){this.completions.set(r,n)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,n){if(r===n)return 0;if(r===null)return-1;if(n===null)return 1;if(r.kind==="arbitrary"&&n.kind==="arbitrary")return r.selector<n.selector?-1:1;if(r.kind==="arbitrary")return 1;if(n.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,i=this.variants.get(n.root).order,u=e-i;if(u!==0)return u;if(r.kind==="compound"&&n.kind==="compound"){let s=this.compare(r.variant,n.variant);return s!==0?s:r.modifier&&n.modifier?r.modifier.value<n.modifier.value?-1:1:r.modifier?1:n.modifier?-1:0}let a=this.compareFns.get(e);if(a!==void 0)return a(r,n);if(r.root!==n.root)return r.root<n.root?-1:1;let p=r.value,f=n.value;return p===null?-1:f===null||p.kind==="arbitrary"&&f.kind!=="arbitrary"?1:p.kind!=="arbitrary"&&f.kind==="arbitrary"||p.value<f.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:n,applyFn:e,compounds:i,compoundsWith:u,order:a}){let p=this.variants.get(r);p?Object.assign(p,{kind:n,applyFn:e,compounds:i}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(r,{kind:n,applyFn:e,order:a,compoundsWith:u,compounds:i}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function ce(t){let r=0;for(let n of t){if(n[0]==="@"){if(!n.startsWith("@media")&&!n.startsWith("@supports")&&!n.startsWith("@container"))return 0;r|=1;continue}if(n.includes("::"))return 0;r|=2}return r}function ir(t){let r=new dt;function n(s,d,{compounds:m}={}){m=m??ce(d),r.static(s,g=>{g.nodes=d.map(v=>M(v,g.nodes))},{compounds:m})}n("*",[":is(& > *)"],{compounds:0}),n("**",[":is(& *)"],{compounds:0});function e(s,d){return d.map(m=>{m=m.trim();let g=P(m," ");return g[0]==="not"?g.slice(1).join(" "):s==="@container"?g[0][0]==="("?`not ${m}`:g[1]==="not"?`${g[0]} ${g.slice(2).join(" ")}`:`${g[0]} not ${g.slice(1).join(" ")}`:`not ${m}`})}let i=["@media","@supports","@container"];function u(s){for(let d of i){if(d!==s.name)continue;let m=P(s.params,",");return m.length>1?null:(m=e(s.name,m),D(s.name,m.join(", ")))}return null}function a(s){return s.includes("::")?null:`&:not(${P(s,",").map(m=>(m.startsWith("&:is(")&&m.endsWith(")")&&(m=m.slice(5,-1)),m=m.replaceAll("&","*"),m)).join(", ")})`}r.compound("not",3,(s,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative||d.modifier)return null;let m=!1;if(_([s],(g,{path:v})=>{if(g.kind!=="rule"&&g.kind!=="at-rule")return 0;if(g.nodes.length>0)return 0;let b=[],y=[];for(let T of v)T.kind==="at-rule"?b.push(T):T.kind==="rule"&&y.push(T);if(b.length>1)return 2;if(y.length>1)return 2;let x=[];for(let T of y){let S=a(T.selector);if(!S)return m=!1,2;x.push(j(S,[]))}for(let T of b){let S=u(T);if(!S)return m=!1,2;x.push(S)}return Object.assign(s,j("&",x)),m=!0,1}),s.kind==="rule"&&s.selector==="&"&&s.nodes.length===1&&Object.assign(s,s.nodes[0]),!m)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(s=>r.compoundsWith("not",s))),r.compound("group",2,(s,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative)return null;let m=d.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${d.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,g=!1;if(_([s],(v,{path:b})=>{if(v.kind!=="rule")return 0;for(let x of b.slice(0,-1))if(x.kind==="rule")return g=!1,2;let y=v.selector.replaceAll("&",m);P(y,",").length>1&&(y=`:is(${y})`),v.selector=`&:is(${y} *)`,g=!0}),!g)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(s=>r.compoundsWith("group",s))),r.compound("peer",2,(s,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative)return null;let m=d.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${d.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,g=!1;if(_([s],(v,{path:b})=>{if(v.kind!=="rule")return 0;for(let x of b.slice(0,-1))if(x.kind==="rule")return g=!1,2;let y=v.selector.replaceAll("&",m);P(y,",").length>1&&(y=`:is(${y})`),v.selector=`&:is(${y} ~ *)`,g=!0}),!g)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(s=>r.compoundsWith("peer",s))),n("first-letter",["&::first-letter"]),n("first-line",["&::first-line"]),n("marker",["& *::marker","&::marker"]),n("selection",["& *::selection","&::selection"]),n("file",["&::file-selector-button"]),n("placeholder",["&::placeholder"]),n("backdrop",["&::backdrop"]);{let s=function(){return U([D("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var p=s;r.static("before",d=>{d.nodes=[j("&::before",[s(),l("content","var(--tw-content)"),...d.nodes])]},{compounds:0}),r.static("after",d=>{d.nodes=[j("&::after",[s(),l("content","var(--tw-content)"),...d.nodes])]},{compounds:0})}n("first",["&:first-child"]),n("last",["&:last-child"]),n("only",["&:only-child"]),n("odd",["&:nth-child(odd)"]),n("even",["&:nth-child(even)"]),n("first-of-type",["&:first-of-type"]),n("last-of-type",["&:last-of-type"]),n("only-of-type",["&:only-of-type"]),n("visited",["&:visited"]),n("target",["&:target"]),n("open",["&:is([open], :popover-open, :open)"]),n("default",["&:default"]),n("checked",["&:checked"]),n("indeterminate",["&:indeterminate"]),n("placeholder-shown",["&:placeholder-shown"]),n("autofill",["&:autofill"]),n("optional",["&:optional"]),n("required",["&:required"]),n("valid",["&:valid"]),n("invalid",["&:invalid"]),n("in-range",["&:in-range"]),n("out-of-range",["&:out-of-range"]),n("read-only",["&:read-only"]),n("empty",["&:empty"]),n("focus-within",["&:focus-within"]),r.static("hover",s=>{s.nodes=[j("&:hover",[D("@media","(hover: hover)",s.nodes)])]}),n("focus",["&:focus"]),n("focus-visible",["&:focus-visible"]),n("active",["&:active"]),n("enabled",["&:enabled"]),n("disabled",["&:disabled"]),n("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(s,d)=>{if(d.modifier)return null;let m=!1;if(_([s],(g,{path:v})=>{if(g.kind!=="rule")return 0;for(let b of v.slice(0,-1))if(b.kind==="rule")return m=!1,2;g.selector=`:where(${g.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(s=>r.compoundsWith("in",s))),r.compound("has",2,(s,d)=>{if(d.modifier)return null;let m=!1;if(_([s],(g,{path:v})=>{if(g.kind!=="rule")return 0;for(let b of v.slice(0,-1))if(b.kind==="rule")return m=!1,2;g.selector=`&:has(${g.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(s=>r.compoundsWith("has",s))),r.functional("aria",(s,d)=>{if(!d.value||d.modifier)return null;d.value.kind==="arbitrary"?s.nodes=[j(`&[aria-${nr(d.value.value)}]`,s.nodes)]:s.nodes=[j(`&[aria-${d.value.value}="true"]`,s.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(s,d)=>{if(!d.value||d.modifier)return null;s.nodes=[j(`&[data-${nr(d.value.value)}]`,s.nodes)]}),r.functional("nth",(s,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!N(d.value.value))return null;s.nodes=[j(`&:nth-child(${d.value.value})`,s.nodes)]}),r.functional("nth-last",(s,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!N(d.value.value))return null;s.nodes=[j(`&:nth-last-child(${d.value.value})`,s.nodes)]}),r.functional("nth-of-type",(s,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!N(d.value.value))return null;s.nodes=[j(`&:nth-of-type(${d.value.value})`,s.nodes)]}),r.functional("nth-last-of-type",(s,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!N(d.value.value))return null;s.nodes=[j(`&:nth-last-of-type(${d.value.value})`,s.nodes)]}),r.functional("supports",(s,d)=>{if(!d.value||d.modifier)return null;let m=d.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let g=m.replace(/\b(and|or|not)\b/g," $1 ");s.nodes=[D("@supports",g,s.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),s.nodes=[D("@supports",m,s.nodes)]},{compounds:1}),n("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),n("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),n("contrast-more",["@media (prefers-contrast: more)"]),n("contrast-less",["@media (prefers-contrast: less)"]);{let s=function(d,m,g,v){if(d===m)return 0;let b=v.get(d);if(b===null)return g==="asc"?-1:1;let y=v.get(m);return y===null?g==="asc"?1:-1:se(b,y,g)};var f=s;{let d=t.namespace("--breakpoint"),m=new F(g=>{switch(g.kind){case"static":return t.resolveValue(g.root,["--breakpoint"])??null;case"functional":{if(!g.value||g.modifier)return null;let v=null;return g.value.kind==="arbitrary"?v=g.value.value:g.value.kind==="named"&&(v=t.resolveValue(g.value.value,["--breakpoint"])),!v||v.includes("var(")?null:v}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(g,v)=>{if(v.modifier)return null;let b=m.get(v);if(b===null)return null;g.nodes=[D("@media",`(width < ${b})`,g.nodes)]},{compounds:1})},(g,v)=>s(g,v,"desc",m)),r.suggest("max",()=>Array.from(d.keys()).filter(g=>g!==null)),r.group(()=>{for(let[g,v]of t.namespace("--breakpoint"))g!==null&&r.static(g,b=>{b.nodes=[D("@media",`(width >= ${v})`,b.nodes)]},{compounds:1});r.functional("min",(g,v)=>{if(v.modifier)return null;let b=m.get(v);if(b===null)return null;g.nodes=[D("@media",`(width >= ${b})`,g.nodes)]},{compounds:1})},(g,v)=>s(g,v,"asc",m)),r.suggest("min",()=>Array.from(d.keys()).filter(g=>g!==null))}{let d=t.namespace("--container"),m=new F(g=>{switch(g.kind){case"functional":{if(g.value===null)return null;let v=null;return g.value.kind==="arbitrary"?v=g.value.value:g.value.kind==="named"&&(v=t.resolveValue(g.value.value,["--container"])),!v||v.includes("var(")?null:v}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(g,v)=>{let b=m.get(v);if(b===null)return null;g.nodes=[D("@container",v.modifier?`${v.modifier.value} (width < ${b})`:`(width < ${b})`,g.nodes)]},{compounds:1})},(g,v)=>s(g,v,"desc",m)),r.suggest("@max",()=>Array.from(d.keys()).filter(g=>g!==null)),r.group(()=>{r.functional("@",(g,v)=>{let b=m.get(v);if(b===null)return null;g.nodes=[D("@container",v.modifier?`${v.modifier.value} (width >= ${b})`:`(width >= ${b})`,g.nodes)]},{compounds:1}),r.functional("@min",(g,v)=>{let b=m.get(v);if(b===null)return null;g.nodes=[D("@container",v.modifier?`${v.modifier.value} (width >= ${b})`:`(width >= ${b})`,g.nodes)]},{compounds:1})},(g,v)=>s(g,v,"asc",m)),r.suggest("@min",()=>Array.from(d.keys()).filter(g=>g!==null)),r.suggest("@",()=>Array.from(d.keys()).filter(g=>g!==null))}}return n("portrait",["@media (orientation: portrait)"]),n("landscape",["@media (orientation: landscape)"]),n("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),n("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),n("dark",["@media (prefers-color-scheme: dark)"]),n("starting",["@starting-style"]),n("print",["@media print"]),n("forced-colors",["@media (forced-colors: active)"]),r}function nr(t){if(t.includes("=")){let[r,...n]=P(t,"="),e=n.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let i=e[e.length-1];if(e[e.length-2]===" "&&(i==="i"||i==="I"||i==="s"||i==="S"))return`${r}="${e.slice(0,-2)}" ${i}`}return`${r}="${e}"`}return t}function pt(t,r){_(t,(n,{replaceWith:e})=>{if(n.kind==="at-rule"&&n.name==="@slot")e(r);else if(n.kind==="at-rule"&&(n.name==="@keyframes"||n.name==="@property"))return Object.assign(n,U([D(n.name,n.params,n.nodes)])),1})}function or(t){let r=Jt(t),n=ir(t),e=new F(f=>Lt(f,p)),i=new F(f=>Array.from(zt(f,p))),u=new F(f=>{let s=lr(f,p);try{ge(s.map(({node:d})=>d),p)}catch{return[]}return s}),a=new F(f=>(X(L(f),s=>{if(!(s.kind!=="function"||s.value!=="var"))return X(s.nodes,d=>{d.kind!=="word"||d.value[0]!=="-"||d.value[1]!=="-"||t.markUsedVariable(d.value)}),1}),!0)),p={theme:t,utilities:r,variants:n,invalidCandidates:new Set,important:!1,candidatesToCss(f){let s=[];for(let d of f){let m=!1,{astNodes:g}=re([d],this,{onInvalidCandidate(){m=!0}});g=ae(g,p),g.length===0||m?s.push(null):s.push(G(g))}return s},getClassOrder(f){return rr(this,f)},getClassList(){return er(this)},getVariants(){return tr(this)},parseCandidate(f){return i.get(f)},parseVariant(f){return e.get(f)},compileAstNodes(f){return u.get(f)},getVariantOrder(){let f=Array.from(e.values());f.sort((g,v)=>this.variants.compare(g,v));let s=new Map,d,m=0;for(let g of f)g!==null&&(d!==void 0&&this.variants.compare(d,g)!==0&&m++,s.set(g,m),d=g);return s},resolveThemeValue(f){let s=f.lastIndexOf("/"),d=null;s!==-1&&(d=f.slice(s+1).trim(),f=f.slice(0,s).trim());let m=t.get([f])??void 0;return d&&m?J(m,d):m},trackUsedVariables(f){a.get(f)}};return p}var mt=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function re(t,r,{onInvalidCandidate:n}={}){let e=new Map,i=[],u=new Map;for(let p of t){if(r.invalidCandidates.has(p)){n?.(p);continue}let f=r.parseCandidate(p);if(f.length===0){n?.(p);continue}u.set(p,f)}let a=r.getVariantOrder();for(let[p,f]of u){let s=!1;for(let d of f){let m=r.compileAstNodes(d);if(m.length!==0){s=!0;for(let{node:g,propertySort:v}of m){let b=0n;for(let y of d.variants)b|=1n<<BigInt(a.get(y));e.set(g,{properties:v,variants:b,candidate:p}),i.push(g)}}}s||n?.(p)}return i.sort((p,f)=>{let s=e.get(p),d=e.get(f);if(s.variants-d.variants!==0n)return Number(s.variants-d.variants);let m=0;for(;s.properties.length<m&&d.properties.length<m&&s.properties[m]===d.properties[m];)m+=1;return(s.properties[m]??1/0)-(d.properties[m]??1/0)||d.properties.length-s.properties.length||ze(s.candidate,d.candidate)}),{astNodes:i,nodeSorting:e}}function lr(t,r){let n=zn(t,r);if(n.length===0)return[];let e=[],i=`.${de(t.raw)}`;for(let u of n){let a=Ln(u);(t.important||r.important)&&sr(u);let p={kind:"rule",selector:i,nodes:u};for(let f of t.variants)if(ve(p,f,r.variants)===null)return[];e.push({node:p,propertySort:a})}return e}function ve(t,r,n,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[M(r.selector,t.nodes)];return}let{applyFn:i}=n.get(r.root);if(r.kind==="compound"){let a=D("@slot");if(ve(a,r.variant,n,e+1)===null||r.root==="not"&&a.nodes.length>1)return null;for(let f of a.nodes)if(f.kind!=="rule"&&f.kind!=="at-rule"||i(f,r)===null)return null;_(a.nodes,f=>{if((f.kind==="rule"||f.kind==="at-rule")&&f.nodes.length<=0)return f.nodes=t.nodes,1}),t.nodes=a.nodes;return}if(i(t,r)===null)return null}function ar(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function zn(t,r){if(t.kind==="arbitrary"){let a=t.value;return t.modifier&&(a=W(a,t.modifier,r.theme)),a===null?[]:[[l(t.property,a)]]}let n=r.utilities.get(t.root)??[],e=[],i=n.filter(a=>!ar(a));for(let a of i){if(a.kind!==t.kind)continue;let p=a.compileFn(t);if(p!==void 0){if(p===null)return e;e.push(p)}}if(e.length>0)return e;let u=n.filter(a=>ar(a));for(let a of u){if(a.kind!==t.kind)continue;let p=a.compileFn(t);if(p!==void 0){if(p===null)return e;e.push(p)}}return e}function sr(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&sr(r.nodes))}function Ln(t){let r=new Set,n=t.slice();for(;n.length>0;){let e=n.shift();if(e.kind==="declaration"){if(e.property==="--tw-sort"){let u=mt.indexOf(e.value??"");if(u!==-1){r.add(u);break}}let i=mt.indexOf(e.property);i!==-1&&r.add(i)}else if(e.kind==="rule"||e.kind==="at-rule")for(let i of e.nodes)n.push(i)}return Array.from(r).sort((e,i)=>e-i)}function Ne(t,r){let n=0,e=M("&",t),i=new Set,u=new F(()=>new Set),a=new F(()=>new Set);_([e],(m,{parent:g})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return _(m.nodes,v=>{if(v.kind==="at-rule"&&v.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let v=m.params.replace(/-\*$/,"");a.get(v).add(m),_(m.nodes,b=>{if(!(b.kind!=="at-rule"||b.name!=="@apply")){i.add(m);for(let y of ur(b,r))u.get(m).add(y)}});return}if(m.name==="@apply"){if(g===null)return;n|=1,i.add(g);for(let v of ur(m,r))u.get(g).add(v)}}});let p=new Set,f=[],s=new Set;function d(m,g=[]){if(!p.has(m)){if(s.has(m)){let v=g[(g.indexOf(m)+1)%g.length];throw m.kind==="at-rule"&&m.name==="@utility"&&v.kind==="at-rule"&&v.name==="@utility"&&_(m.nodes,b=>{if(b.kind!=="at-rule"||b.name!=="@apply")return;let y=b.params.split(/\s+/g);for(let x of y)for(let T of r.parseCandidate(x))switch(T.kind){case"arbitrary":break;case"static":case"functional":if(v.params.replace(/-\*$/,"")===T.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${G([m])}
Relies on:

${G([v])}`)}s.add(m);for(let v of u.get(m))for(let b of a.get(v))g.push(m),d(b,g),g.pop();p.add(m),s.delete(m),f.push(m)}}for(let m of i)d(m);return _(f,(m,{replaceWith:g})=>{if(m.kind!=="at-rule"||m.name!=="@apply")return;let v=m.params.split(/\s+/g);{let b=re(v,r,{onInvalidCandidate:x=>{throw new Error(`Cannot apply unknown utility class: ${x}`)}}).astNodes,y=[];for(let x of b)if(x.kind==="rule")for(let T of x.nodes)y.push(T);else y.push(x);g(y)}}),n}function*ur(t,r){for(let n of t.params.split(/\s+/g))for(let e of r.parseCandidate(n))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function gt(t,r,n,e=0){let i=0,u=[];return _(t,(a,{replaceWith:p})=>{if(a.kind==="at-rule"&&(a.name==="@import"||a.name==="@reference")){let f=Mn(L(a.params));if(f===null)return;a.name==="@reference"&&(f.media="reference"),i|=2;let{uri:s,layer:d,media:m,supports:g}=f;if(s.startsWith("data:")||s.startsWith("http://")||s.startsWith("https://"))return;let v=Q({},[]);return u.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${s}\` in \`${r}\`)`);let b=await n(s,r),y=oe(b.content);await gt(y,b.base,n,e+1),v.nodes=Wn([Q({base:b.base},y)],d,m,g)})()),p(v),1}}),u.length>0&&await Promise.all(u),i}function Mn(t){let r,n=null,e=null,i=null;for(let u=0;u<t.length;u++){let a=t[u];if(a.kind!=="separator"){if(a.kind==="word"&&!r){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;r=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!r)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(n)return null;if(i)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?n=q(a.nodes):n="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(i)return null;i=q(a.nodes);continue}e=q(t.slice(u));break}}return r?{uri:r,layer:n,media:e,supports:i}:null}function Wn(t,r,n,e){let i=t;return r!==null&&(i=[D("@layer",r,i)]),n!==null&&(i=[D("@media",n,i)]),e!==null&&(i=[D("@supports",e[0]==="("?e:`(${e})`,i)]),i}function ye(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function cr(t,{theme:r},n){for(let e of n){let i=Me([e]);i&&t.theme.clearNamespace(`--${i}`,4)}for(let[e,i]of Bn(r)){if(typeof i!="string"&&typeof i!="number")continue;if(typeof i=="string"&&(i=i.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof i=="number"||typeof i=="string")){let a=typeof i=="string"?parseFloat(i):i;a>=0&&a<=1&&(i=a*100+"%")}let u=Me(e);u&&t.theme.add(`--${u}`,""+i,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let i=ye(r.fontFamily.sans);i&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",i,e),t.theme.add("--default-font-feature-settings",ye(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",ye(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let i=ye(r.fontFamily.mono);i&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",i,e),t.theme.add("--default-mono-font-feature-settings",ye(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",ye(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function Bn(t){let r=[];return fr(t,[],(n,e)=>{if(Hn(n))return r.push([e,n]),1;if(Gn(n)){r.push([e,n[0]]);for(let i of Reflect.ownKeys(n[1]))r.push([[...e,`-${i}`],n[1][i]]);return 1}if(Array.isArray(n)&&n.every(i=>typeof i=="string"))return r.push([e,n.join(", ")]),1}),r}var qn=/^[a-zA-Z0-9-_%/\.]+$/;function Me(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="containers"&&(t[0]="container"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!qn.test(r))return null;return t.map((r,n,e)=>r==="1"&&n!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(n,e,i)=>`${e}-${i.toLowerCase()}`)).filter((r,n)=>r!=="DEFAULT"||n!==t.length-1).join("-")}function Hn(t){return typeof t=="number"||typeof t=="string"}function Gn(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function fr(t,r=[],n){for(let e of Reflect.ownKeys(t)){let i=t[e];if(i==null)continue;let u=[...r,e],a=n(i,u)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(i)&&typeof i!="object")&&fr(i,u,n)===2)return 2}}}function We(t){let r=[];for(let n of P(t,".")){if(!n.includes("[")){r.push(n);continue}let e=0;for(;;){let i=n.indexOf("[",e),u=n.indexOf("]",i);if(i===-1||u===-1)break;i>e&&r.push(n.slice(e,i)),r.push(n.slice(i+1,u)),e=u+1}e<=n.length-1&&r.push(n.slice(e))}return r}function be(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function Ve(t,r,n,e=[]){for(let i of r)if(i!=null)for(let u of Reflect.ownKeys(i)){e.push(u);let a=n(t[u],i[u],e);a!==void 0?t[u]=a:!be(t[u])||!be(i[u])?t[u]=i[u]:t[u]=Ve({},[t[u],i[u]],n,e),e.pop()}return t}function Be(t,r,n){return function(i,u){let a=i.lastIndexOf("/"),p=null;a!==-1&&(p=i.slice(a+1).trim(),i=i.slice(0,a).trim());let f=(()=>{let s=We(i),[d,m]=Jn(t.theme,s),g=n(dr(r()??{},s)??null);if(typeof g=="string"&&(g=g.replace("<alpha-value>","1")),typeof d!="object")return typeof m!="object"&&m&4?g??d:d;if(g!==null&&typeof g=="object"&&!Array.isArray(g)){let v=Ve({},[g],(b,y)=>y);if(d===null&&Object.hasOwn(g,"__CSS_VALUES__")){let b={};for(let y in g.__CSS_VALUES__)b[y]=g[y],delete v[y];d=b}for(let b in d)b!=="__CSS_VALUES__"&&(g?.__CSS_VALUES__?.[b]&4&&dr(v,b.split("-"))!==void 0||(v[le(b)]=d[b]));return v}if(Array.isArray(d)&&Array.isArray(m)&&Array.isArray(g)){let v=d[0],b=d[1];m[0]&4&&(v=g[0]??v);for(let y of Object.keys(b))m[1][y]&4&&(b[y]=g[1][y]??b[y]);return[v,b]}return d??g})();return p&&typeof f=="string"&&(f=J(f,p)),f??u}}function Jn(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let n=Me(r),e=new Map,i=new F(()=>new Map),u=t.namespace(`--${n}`);if(u.size===0)return[null,0];let a=new Map;for(let[d,m]of u){if(!d||!d.includes("--")){e.set(d,m),a.set(d,t.getOptions(d?`--${n}-${d}`:`--${n}`));continue}let g=d.indexOf("--"),v=d.slice(0,g),b=d.slice(g+2);b=b.replace(/-([a-z])/g,(y,x)=>x.toUpperCase()),i.get(v===""?null:v).set(b,[m,t.getOptions(`--${n}${d}`)])}let p=t.getOptions(`--${n}`);for(let[d,m]of i){let g=e.get(d);if(typeof g!="string")continue;let v={},b={};for(let[y,[x,T]]of m)v[y]=x,b[y]=T;e.set(d,[g,v]),a.set(d,[p,b])}let f={},s={};for(let[d,m]of e)pr(f,[d??"DEFAULT"],m);for(let[d,m]of a)pr(s,[d??"DEFAULT"],m);return r[r.length-1]==="DEFAULT"?[f?.DEFAULT??null,s.DEFAULT??0]:"DEFAULT"in f&&Object.keys(f).length===1?[f.DEFAULT,s.DEFAULT??0]:(f.__CSS_VALUES__=s,[f,s])}function dr(t,r){for(let n=0;n<r.length;++n){let e=r[n];if(t?.[e]===void 0){if(r[n+1]===void 0)return;r[n+1]=`${e}-${r[n+1]}`;continue}t=t[e]}return t}function pr(t,r,n){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=n}function Yn(t){return{kind:"combinator",value:t}}function Zn(t,r){return{kind:"function",value:t,nodes:r}}function Te(t){return{kind:"selector",value:t}}function Qn(t){return{kind:"separator",value:t}}function Xn(t){return{kind:"value",value:t}}function qe(t,r,n=null){for(let e=0;e<t.length;e++){let i=t[e],u=!1,a=0,p=r(i,{parent:n,replaceWith(f){u=!0,Array.isArray(f)?f.length===0?(t.splice(e,1),a=0):f.length===1?(t[e]=f[0],a=1):(t.splice(e,1,...f),a=f.length):(t[e]=f,a=1)}})??0;if(u){p===0?e--:e+=a-1;continue}if(p===2)return 2;if(p!==1&&i.kind==="function"&&qe(i.nodes,r,i)===2)return 2}}function He(t){let r="";for(let n of t)switch(n.kind){case"combinator":case"selector":case"separator":case"value":{r+=n.value;break}case"function":r+=n.value+"("+He(n.nodes)+")"}return r}var mr=92,ei=93,gr=41,ti=58,hr=44,ri=34,ni=46,vr=62,yr=10,ii=35,br=91,wr=40,kr=43,oi=39,xr=32,Ar=9,Cr=126;function ht(t){t=t.replaceAll(`\r
`,`
`);let r=[],n=[],e=null,i="",u;for(let a=0;a<t.length;a++){let p=t.charCodeAt(a);switch(p){case hr:case vr:case yr:case xr:case kr:case Ar:case Cr:{if(i.length>0){let g=Te(i);e?e.nodes.push(g):r.push(g),i=""}let f=a,s=a+1;for(;s<t.length&&(u=t.charCodeAt(s),!(u!==hr&&u!==vr&&u!==yr&&u!==xr&&u!==kr&&u!==Ar&&u!==Cr));s++);a=s-1;let d=t.slice(f,s),m=d.trim()===","?Qn(d):Yn(d);e?e.nodes.push(m):r.push(m);break}case wr:{let f=Zn(i,[]);if(i="",f.value!==":not"&&f.value!==":where"&&f.value!==":has"&&f.value!==":is"){let s=a+1,d=0;for(let g=a+1;g<t.length;g++){if(u=t.charCodeAt(g),u===wr){d++;continue}if(u===gr){if(d===0){a=g;break}d--}}let m=a;f.nodes.push(Xn(t.slice(s,m))),i="",a=m,r.push(f);break}e?e.nodes.push(f):r.push(f),n.push(f),e=f;break}case gr:{let f=n.pop();if(i.length>0){let s=Te(i);f.nodes.push(s),i=""}n.length>0?e=n[n.length-1]:e=null;break}case ni:case ti:case ii:{if(i.length>0){let f=Te(i);e?e.nodes.push(f):r.push(f)}i=String.fromCharCode(p);break}case br:{if(i.length>0){let d=Te(i);e?e.nodes.push(d):r.push(d)}i="";let f=a,s=0;for(let d=a+1;d<t.length;d++){if(u=t.charCodeAt(d),u===br){s++;continue}if(u===ei){if(s===0){a=d;break}s--}}i+=t.slice(f,a+1);break}case oi:case ri:{let f=a;for(let s=a+1;s<t.length;s++)if(u=t.charCodeAt(s),u===mr)s+=1;else if(u===p){a=s;break}i+=t.slice(f,a+1);break}case mr:{let f=t.charCodeAt(a+1);i+=String.fromCharCode(p)+String.fromCharCode(f),a+=1;break}default:i+=String.fromCharCode(p)}}return i.length>0&&r.push(Te(i)),r}var $r=/^[a-z@][a-zA-Z0-9/%._-]*$/;function vt({designSystem:t,ast:r,resolvedConfig:n,featuresRef:e,referenceMode:i}){let u={addBase(a){if(i)return;let p=Z(a);e.current|=ge(p,t),r.push(D("@layer","base",p))},addVariant(a,p){if(!Le.test(a))throw new Error(`\`addVariant('${a}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof p=="string"||Array.isArray(p)?t.variants.static(a,f=>{f.nodes=Nr(p,f.nodes)},{compounds:ce(typeof p=="string"?[p]:p)}):typeof p=="object"&&t.variants.fromAst(a,Z(p))},matchVariant(a,p,f){function s(m,g,v){let b=p(m,{modifier:g?.value??null});return Nr(b,v)}let d=Object.keys(f?.values??{});t.variants.group(()=>{t.variants.functional(a,(m,g)=>{if(!g.value){if(f?.values&&"DEFAULT"in f.values){m.nodes=s(f.values.DEFAULT,g.modifier,m.nodes);return}return null}if(g.value.kind==="arbitrary")m.nodes=s(g.value.value,g.modifier,m.nodes);else if(g.value.kind==="named"&&f?.values){let v=f.values[g.value.value];if(typeof v!="string")return;m.nodes=s(v,g.modifier,m.nodes)}})},(m,g)=>{if(m.kind!=="functional"||g.kind!=="functional")return 0;let v=m.value?m.value.value:"DEFAULT",b=g.value?g.value.value:"DEFAULT",y=f?.values?.[v]??v,x=f?.values?.[b]??b;if(f&&typeof f.sort=="function")return f.sort({value:y,modifier:m.modifier?.value??null},{value:x,modifier:g.modifier?.value??null});let T=d.indexOf(v),S=d.indexOf(b);return T=T===-1?d.length:T,S=S===-1?d.length:S,T!==S?T-S:y<x?-1:1})},addUtilities(a){a=Array.isArray(a)?a:[a];let p=a.flatMap(s=>Object.entries(s));p=p.flatMap(([s,d])=>P(s,",").map(m=>[m.trim(),d]));let f=new F(()=>[]);for(let[s,d]of p){if(s.startsWith("@keyframes ")){i||r.push(M(s,Z(d)));continue}let m=ht(s),g=!1;if(qe(m,v=>{if(v.kind==="selector"&&v.value[0]==="."&&$r.test(v.value.slice(1))){let b=v.value;v.value="&";let y=He(m),x=b.slice(1),T=y==="&"?Z(d):[M(y,Z(d))];f.get(x).push(...T),g=!0,v.value=b;return}if(v.kind==="function"&&v.value===":not")return 1}),!g)throw new Error(`\`addUtilities({ '${s}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[s,d]of f)t.theme.prefix&&_(d,m=>{if(m.kind==="rule"){let g=ht(m.selector);qe(g,v=>{v.kind==="selector"&&v.value[0]==="."&&(v.value=`.${t.theme.prefix}\\:${v.value.slice(1)}`)}),m.selector=He(g)}}),t.utilities.static(s,()=>{let m=structuredClone(d);return e.current|=Ne(m,t),m})},matchUtilities(a,p){let f=p?.type?Array.isArray(p?.type)?p.type:[p.type]:["any"];for(let[d,m]of Object.entries(a)){let g=function({negative:v}){return b=>{if(b.value?.kind==="arbitrary"&&f.length>0&&!f.includes("any")&&(b.value.dataType&&!f.includes(b.value.dataType)||!b.value.dataType&&!z(b.value.value,f)))return;let y=f.includes("color"),x=null,T=!1;{let E=p?.values??{};y&&(E=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentColor"},E)),b.value?b.value.kind==="arbitrary"?x=b.value.value:b.value.fraction&&E[b.value.fraction]?(x=E[b.value.fraction],T=!0):E[b.value.value]?x=E[b.value.value]:E.__BARE_VALUE__&&(x=E.__BARE_VALUE__(b.value)??null,T=(b.value.fraction!==null&&x?.includes("/"))??!1):x=E.DEFAULT??null}if(x===null)return;let S;{let E=p?.modifiers??null;b.modifier?E==="any"||b.modifier.kind==="arbitrary"?S=b.modifier.value:E?.[b.modifier.value]?S=E[b.modifier.value]:y&&!Number.isNaN(Number(b.modifier.value))?S=`${b.modifier.value}%`:S=null:S=null}if(b.modifier&&S===null&&!T)return b.value?.kind==="arbitrary"?null:void 0;y&&S!==null&&(x=J(x,S)),v&&(x=`calc(${x} * -1)`);let O=Z(m(x,{modifier:S}));return e.current|=Ne(O,t),O}};var s=g;if(!$r.test(d))throw new Error(`\`matchUtilities({ '${d}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);p?.supportsNegativeValues&&t.utilities.functional(`-${d}`,g({negative:!0}),{types:f}),t.utilities.functional(d,g({negative:!1}),{types:f}),t.utilities.suggest(d,()=>{let v=p?.values??{},b=new Set(Object.keys(v));b.delete("__BARE_VALUE__"),b.has("DEFAULT")&&(b.delete("DEFAULT"),b.add(null));let y=p?.modifiers??{},x=y==="any"?[]:Object.keys(y);return[{supportsNegative:p?.supportsNegativeValues??!1,values:Array.from(b),modifiers:x}]})}},addComponents(a,p){this.addUtilities(a,p)},matchComponents(a,p){this.matchUtilities(a,p)},theme:Be(t,()=>n.theme??{},a=>a),prefix(a){return a},config(a,p){let f=n;if(!a)return f;let s=We(a);for(let d=0;d<s.length;++d){let m=s[d];if(f[m]===void 0)return p;f=f[m]}return f??p}};return u.addComponents=u.addComponents.bind(u),u.matchComponents=u.matchComponents.bind(u),u}function Z(t){let r=[];t=Array.isArray(t)?t:[t];let n=t.flatMap(e=>Object.entries(e));for(let[e,i]of n)if(typeof i!="object"){if(!e.startsWith("--")){if(i==="@slot"){r.push(M(e,[D("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(l(e,String(i)))}else if(Array.isArray(i))for(let u of i)typeof u=="string"?r.push(l(e,u)):r.push(M(e,Z(u)));else i!==null&&r.push(M(e,Z(i)));return r}function Nr(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let i=e.replace("}","{@slot}}"),u=oe(i);return pt(u,r),u}else return M(e,r)})}function Vr(t,r,n){for(let e of ai(r))t.theme.addKeyframes(e)}function ai(t){let r=[];if("keyframes"in t.theme)for(let[n,e]of Object.entries(t.theme.keyframes))r.push(D("@keyframes",n,Z(e)));return r}var Ge={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(0.984 0.003 247.858)",100:"oklch(0.968 0.007 247.896)",200:"oklch(0.929 0.013 255.508)",300:"oklch(0.869 0.022 252.894)",400:"oklch(0.704 0.04 256.788)",500:"oklch(0.554 0.046 257.417)",600:"oklch(0.446 0.043 257.281)",700:"oklch(0.372 0.044 257.287)",800:"oklch(0.279 0.041 260.031)",900:"oklch(0.208 0.042 265.755)",950:"oklch(0.129 0.042 264.695)"},gray:{50:"oklch(0.985 0.002 247.839)",100:"oklch(0.967 0.003 264.542)",200:"oklch(0.928 0.006 264.531)",300:"oklch(0.872 0.01 258.338)",400:"oklch(0.707 0.022 261.325)",500:"oklch(0.551 0.027 264.364)",600:"oklch(0.446 0.03 256.802)",700:"oklch(0.373 0.034 259.733)",800:"oklch(0.278 0.033 256.848)",900:"oklch(0.21 0.034 264.665)",950:"oklch(0.13 0.028 261.692)"},zinc:{50:"oklch(0.985 0 0)",100:"oklch(0.967 0.001 286.375)",200:"oklch(0.92 0.004 286.32)",300:"oklch(0.871 0.006 286.286)",400:"oklch(0.705 0.015 286.067)",500:"oklch(0.552 0.016 285.938)",600:"oklch(0.442 0.017 285.786)",700:"oklch(0.37 0.013 285.805)",800:"oklch(0.274 0.006 286.033)",900:"oklch(0.21 0.006 285.885)",950:"oklch(0.141 0.005 285.823)"},neutral:{50:"oklch(0.985 0 0)",100:"oklch(0.97 0 0)",200:"oklch(0.922 0 0)",300:"oklch(0.87 0 0)",400:"oklch(0.708 0 0)",500:"oklch(0.556 0 0)",600:"oklch(0.439 0 0)",700:"oklch(0.371 0 0)",800:"oklch(0.269 0 0)",900:"oklch(0.205 0 0)",950:"oklch(0.145 0 0)"},stone:{50:"oklch(0.985 0.001 106.423)",100:"oklch(0.97 0.001 106.424)",200:"oklch(0.923 0.003 48.717)",300:"oklch(0.869 0.005 56.366)",400:"oklch(0.709 0.01 56.259)",500:"oklch(0.553 0.013 58.071)",600:"oklch(0.444 0.011 73.639)",700:"oklch(0.374 0.01 67.558)",800:"oklch(0.268 0.007 34.298)",900:"oklch(0.216 0.006 56.043)",950:"oklch(0.147 0.004 49.25)"},red:{50:"oklch(0.971 0.013 17.38)",100:"oklch(0.936 0.032 17.717)",200:"oklch(0.885 0.062 18.334)",300:"oklch(0.808 0.114 19.571)",400:"oklch(0.704 0.191 22.216)",500:"oklch(0.637 0.237 25.331)",600:"oklch(0.577 0.245 27.325)",700:"oklch(0.505 0.213 27.518)",800:"oklch(0.444 0.177 26.899)",900:"oklch(0.396 0.141 25.723)",950:"oklch(0.258 0.092 26.042)"},orange:{50:"oklch(0.98 0.016 73.684)",100:"oklch(0.954 0.038 75.164)",200:"oklch(0.901 0.076 70.697)",300:"oklch(0.837 0.128 66.29)",400:"oklch(0.75 0.183 55.934)",500:"oklch(0.705 0.213 47.604)",600:"oklch(0.646 0.222 41.116)",700:"oklch(0.553 0.195 38.402)",800:"oklch(0.47 0.157 37.304)",900:"oklch(0.408 0.123 38.172)",950:"oklch(0.266 0.079 36.259)"},amber:{50:"oklch(0.987 0.022 95.277)",100:"oklch(0.962 0.059 95.617)",200:"oklch(0.924 0.12 95.746)",300:"oklch(0.879 0.169 91.605)",400:"oklch(0.828 0.189 84.429)",500:"oklch(0.769 0.188 70.08)",600:"oklch(0.666 0.179 58.318)",700:"oklch(0.555 0.163 48.998)",800:"oklch(0.473 0.137 46.201)",900:"oklch(0.414 0.112 45.904)",950:"oklch(0.279 0.077 45.635)"},yellow:{50:"oklch(0.987 0.026 102.212)",100:"oklch(0.973 0.071 103.193)",200:"oklch(0.945 0.129 101.54)",300:"oklch(0.905 0.182 98.111)",400:"oklch(0.852 0.199 91.936)",500:"oklch(0.795 0.184 86.047)",600:"oklch(0.681 0.162 75.834)",700:"oklch(0.554 0.135 66.442)",800:"oklch(0.476 0.114 61.907)",900:"oklch(0.421 0.095 57.708)",950:"oklch(0.286 0.066 53.813)"},lime:{50:"oklch(0.986 0.031 120.757)",100:"oklch(0.967 0.067 122.328)",200:"oklch(0.938 0.127 124.321)",300:"oklch(0.897 0.196 126.665)",400:"oklch(0.841 0.238 128.85)",500:"oklch(0.768 0.233 130.85)",600:"oklch(0.648 0.2 131.684)",700:"oklch(0.532 0.157 131.589)",800:"oklch(0.453 0.124 130.933)",900:"oklch(0.405 0.101 131.063)",950:"oklch(0.274 0.072 132.109)"},green:{50:"oklch(0.982 0.018 155.826)",100:"oklch(0.962 0.044 156.743)",200:"oklch(0.925 0.084 155.995)",300:"oklch(0.871 0.15 154.449)",400:"oklch(0.792 0.209 151.711)",500:"oklch(0.723 0.219 149.579)",600:"oklch(0.627 0.194 149.214)",700:"oklch(0.527 0.154 150.069)",800:"oklch(0.448 0.119 151.328)",900:"oklch(0.393 0.095 152.535)",950:"oklch(0.266 0.065 152.934)"},emerald:{50:"oklch(0.979 0.021 166.113)",100:"oklch(0.95 0.052 163.051)",200:"oklch(0.905 0.093 164.15)",300:"oklch(0.845 0.143 164.978)",400:"oklch(0.765 0.177 163.223)",500:"oklch(0.696 0.17 162.48)",600:"oklch(0.596 0.145 163.225)",700:"oklch(0.508 0.118 165.612)",800:"oklch(0.432 0.095 166.913)",900:"oklch(0.378 0.077 168.94)",950:"oklch(0.262 0.051 172.552)"},teal:{50:"oklch(0.984 0.014 180.72)",100:"oklch(0.953 0.051 180.801)",200:"oklch(0.91 0.096 180.426)",300:"oklch(0.855 0.138 181.071)",400:"oklch(0.777 0.152 181.912)",500:"oklch(0.704 0.14 182.503)",600:"oklch(0.6 0.118 184.704)",700:"oklch(0.511 0.096 186.391)",800:"oklch(0.437 0.078 188.216)",900:"oklch(0.386 0.063 188.416)",950:"oklch(0.277 0.046 192.524)"},cyan:{50:"oklch(0.984 0.019 200.873)",100:"oklch(0.956 0.045 203.388)",200:"oklch(0.917 0.08 205.041)",300:"oklch(0.865 0.127 207.078)",400:"oklch(0.789 0.154 211.53)",500:"oklch(0.715 0.143 215.221)",600:"oklch(0.609 0.126 221.723)",700:"oklch(0.52 0.105 223.128)",800:"oklch(0.45 0.085 224.283)",900:"oklch(0.398 0.07 227.392)",950:"oklch(0.302 0.056 229.695)"},sky:{50:"oklch(0.977 0.013 236.62)",100:"oklch(0.951 0.026 236.824)",200:"oklch(0.901 0.058 230.902)",300:"oklch(0.828 0.111 230.318)",400:"oklch(0.746 0.16 232.661)",500:"oklch(0.685 0.169 237.323)",600:"oklch(0.588 0.158 241.966)",700:"oklch(0.5 0.134 242.749)",800:"oklch(0.443 0.11 240.79)",900:"oklch(0.391 0.09 240.876)",950:"oklch(0.293 0.066 243.157)"},blue:{50:"oklch(0.97 0.014 254.604)",100:"oklch(0.932 0.032 255.585)",200:"oklch(0.882 0.059 254.128)",300:"oklch(0.809 0.105 251.813)",400:"oklch(0.707 0.165 254.624)",500:"oklch(0.623 0.214 259.815)",600:"oklch(0.546 0.245 262.881)",700:"oklch(0.488 0.243 264.376)",800:"oklch(0.424 0.199 265.638)",900:"oklch(0.379 0.146 265.522)",950:"oklch(0.282 0.091 267.935)"},indigo:{50:"oklch(0.962 0.018 272.314)",100:"oklch(0.93 0.034 272.788)",200:"oklch(0.87 0.065 274.039)",300:"oklch(0.785 0.115 274.713)",400:"oklch(0.673 0.182 276.935)",500:"oklch(0.585 0.233 277.117)",600:"oklch(0.511 0.262 276.966)",700:"oklch(0.457 0.24 277.023)",800:"oklch(0.398 0.195 277.366)",900:"oklch(0.359 0.144 278.697)",950:"oklch(0.257 0.09 281.288)"},violet:{50:"oklch(0.969 0.016 293.756)",100:"oklch(0.943 0.029 294.588)",200:"oklch(0.894 0.057 293.283)",300:"oklch(0.811 0.111 293.571)",400:"oklch(0.702 0.183 293.541)",500:"oklch(0.606 0.25 292.717)",600:"oklch(0.541 0.281 293.009)",700:"oklch(0.491 0.27 292.581)",800:"oklch(0.432 0.232 292.759)",900:"oklch(0.38 0.189 293.745)",950:"oklch(0.283 0.141 291.089)"},purple:{50:"oklch(0.977 0.014 308.299)",100:"oklch(0.946 0.033 307.174)",200:"oklch(0.902 0.063 306.703)",300:"oklch(0.827 0.119 306.383)",400:"oklch(0.714 0.203 305.504)",500:"oklch(0.627 0.265 303.9)",600:"oklch(0.558 0.288 302.321)",700:"oklch(0.496 0.265 301.924)",800:"oklch(0.438 0.218 303.724)",900:"oklch(0.381 0.176 304.987)",950:"oklch(0.291 0.149 302.717)"},fuchsia:{50:"oklch(0.977 0.017 320.058)",100:"oklch(0.952 0.037 318.852)",200:"oklch(0.903 0.076 319.62)",300:"oklch(0.833 0.145 321.434)",400:"oklch(0.74 0.238 322.16)",500:"oklch(0.667 0.295 322.15)",600:"oklch(0.591 0.293 322.896)",700:"oklch(0.518 0.253 323.949)",800:"oklch(0.452 0.211 324.591)",900:"oklch(0.401 0.17 325.612)",950:"oklch(0.293 0.136 325.661)"},pink:{50:"oklch(0.971 0.014 343.198)",100:"oklch(0.948 0.028 342.258)",200:"oklch(0.899 0.061 343.231)",300:"oklch(0.823 0.12 346.018)",400:"oklch(0.718 0.202 349.761)",500:"oklch(0.656 0.241 354.308)",600:"oklch(0.592 0.249 0.584)",700:"oklch(0.525 0.223 3.958)",800:"oklch(0.459 0.187 3.815)",900:"oklch(0.408 0.153 2.432)",950:"oklch(0.284 0.109 3.907)"},rose:{50:"oklch(0.969 0.015 12.422)",100:"oklch(0.941 0.03 12.58)",200:"oklch(0.892 0.058 10.001)",300:"oklch(0.81 0.117 11.638)",400:"oklch(0.712 0.194 13.428)",500:"oklch(0.645 0.246 16.439)",600:"oklch(0.586 0.253 17.585)",700:"oklch(0.514 0.222 16.935)",800:"oklch(0.455 0.188 13.697)",900:"oklch(0.41 0.159 10.272)",950:"oklch(0.271 0.105 12.094)"}};function fe(t){return{__BARE_VALUE__:t}}var Y=fe(t=>{if(N(t.value))return t.value}),B=fe(t=>{if(N(t.value))return`${t.value}%`}),ne=fe(t=>{if(N(t.value))return`${t.value}px`}),Tr=fe(t=>{if(N(t.value))return`${t.value}ms`}),Je=fe(t=>{if(N(t.value))return`${t.value}deg`}),si=fe(t=>{if(t.fraction===null)return;let[r,n]=P(t.fraction,"/");if(!(!N(r)||!N(n)))return t.fraction}),Sr=fe(t=>{if(N(Number(t.value)))return`repeat(${t.value}, minmax(0, 1fr))`}),Er={accentColor:({theme:t})=>t("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...si},backdropBlur:({theme:t})=>t("blur"),backdropBrightness:({theme:t})=>({...t("brightness"),...B}),backdropContrast:({theme:t})=>({...t("contrast"),...B}),backdropGrayscale:({theme:t})=>({...t("grayscale"),...B}),backdropHueRotate:({theme:t})=>({...t("hueRotate"),...Je}),backdropInvert:({theme:t})=>({...t("invert"),...B}),backdropOpacity:({theme:t})=>({...t("opacity"),...B}),backdropSaturate:({theme:t})=>({...t("saturate"),...B}),backdropSepia:({theme:t})=>({...t("sepia"),...B}),backgroundColor:({theme:t})=>t("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:t})=>t("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:t})=>({DEFAULT:"currentColor",...t("colors")}),borderOpacity:({theme:t})=>t("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:t})=>t("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...ne},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:t})=>t("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...B},caretColor:({theme:t})=>t("colors"),colors:()=>({...Ge}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...Y},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...B},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:t})=>t("borderColor"),divideOpacity:({theme:t})=>t("borderOpacity"),divideWidth:({theme:t})=>({...t("borderWidth"),...ne}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:t})=>t("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...Y},flexShrink:{0:"0",DEFAULT:"1",...Y},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:t})=>t("spacing"),gradientColorStops:({theme:t})=>t("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...B},grayscale:{0:"0",DEFAULT:"100%",...B},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...Y},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Sr},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Sr},height:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...Je},inset:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),invert:{0:"0",DEFAULT:"100%",...B},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:t})=>({auto:"auto",...t("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...Y},maxHeight:({theme:t})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),maxWidth:({theme:t})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}),minHeight:({theme:t})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),minWidth:({theme:t})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...B},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...Y},outlineColor:({theme:t})=>t("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ne},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ne},padding:({theme:t})=>t("spacing"),placeholderColor:({theme:t})=>t("colors"),placeholderOpacity:({theme:t})=>t("opacity"),ringColor:({theme:t})=>({DEFAULT:"currentColor",...t("colors")}),ringOffsetColor:({theme:t})=>t("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ne},ringOpacity:({theme:t})=>({DEFAULT:"0.5",...t("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ne},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...Je},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...B},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...B},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:t})=>t("spacing"),scrollPadding:({theme:t})=>t("spacing"),sepia:{0:"0",DEFAULT:"100%",...B},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...Je},space:({theme:t})=>t("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:t})=>({none:"none",...t("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...Y},supports:{},data:{},textColor:({theme:t})=>t("colors"),textDecorationColor:({theme:t})=>t("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ne},textIndent:({theme:t})=>t("spacing"),textOpacity:({theme:t})=>t("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ne},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Tr},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Tr},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:t})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),size:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),width:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...Y}};function Rr(t){return{theme:{...Er,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var ui={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function bt(t,r){let n={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(ui)};for(let i of r)yt(n,i);for(let i of n.configs)"darkMode"in i&&i.darkMode!==void 0&&(n.result.darkMode=i.darkMode??null),"prefix"in i&&i.prefix!==void 0&&(n.result.prefix=i.prefix??""),"blocklist"in i&&i.blocklist!==void 0&&(n.result.blocklist=i.blocklist??[]),"important"in i&&i.important!==void 0&&(n.result.important=i.important??!1);let e=fi(n);return{resolvedConfig:{...n.result,content:n.content,theme:n.theme,plugins:n.plugins},replacedThemeKeys:e}}function ci(t,r){if(Array.isArray(t)&&be(t[0]))return t.concat(r);if(Array.isArray(r)&&be(r[0])&&be(t))return[t,...r];if(Array.isArray(r))return r}function yt(t,{config:r,base:n,path:e,reference:i}){let u=[];for(let f of r.plugins??[])"__isOptionsFunction"in f?u.push({...f(),reference:i}):"handler"in f?u.push({...f,reference:i}):u.push({handler:f,reference:i});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let f of r.presets??[])yt(t,{path:e,base:n,config:f,reference:i});for(let f of u)t.plugins.push(f),f.config&&yt(t,{path:e,base:n,config:f.config,reference:!!f.reference});let a=r.content??[],p=Array.isArray(a)?a:a.files;for(let f of p)t.content.files.push(typeof f=="object"?f:{base:n,pattern:f});t.configs.push(r)}function fi(t){let r=new Set,n=Be(t.design,()=>t.theme,i),e=Object.assign(n,{theme:n,colors:Ge});function i(u){return typeof u=="function"?u(e)??null:u??null}for(let u of t.configs){let a=u.theme??{},p=a.extend??{};for(let f in a)f!=="extend"&&r.add(f);Object.assign(t.theme,a);for(let f in p)t.extend[f]??=[],t.extend[f].push(p[f])}delete t.theme.extend;for(let u in t.extend){let a=[t.theme[u],...t.extend[u]];t.theme[u]=()=>{let p=a.map(i);return Ve({},p,ci)}}for(let u in t.theme)t.theme[u]=i(t.theme[u]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let u of Object.keys(t.theme.screens)){let a=t.theme.screens[u];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(t.theme.screens[u]=a.min))}return r}function Or(t,r){let n=t.theme.container||{};if(typeof n!="object"||n===null)return;let e=di(n,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function di({center:t,padding:r,screens:n},e){let i=[],u=null;if(t&&i.push(l("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&i.push(l("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof n=="object"&&n!==null){u=new Map;let a=Array.from(e.theme.namespace("--breakpoint").entries());if(a.sort((p,f)=>se(p[1],f[1],"asc")),a.length>0){let[p]=a[0];i.push(D("@media",`(width >= --theme(--breakpoint-${p}))`,[l("max-width","none")]))}for(let[p,f]of Object.entries(n)){if(typeof f=="object")if("min"in f)f=f.min;else continue;u.set(p,D("@media",`(width >= ${f})`,[l("max-width",f)]))}}if(typeof r=="object"&&r!==null){let a=Object.entries(r).filter(([p])=>p!=="DEFAULT").map(([p,f])=>[p,e.theme.resolveValue(p,["--breakpoint"]),f]).filter(Boolean);a.sort((p,f)=>se(p[1],f[1],"asc"));for(let[p,,f]of a)if(u&&u.has(p))u.get(p).nodes.push(l("padding-inline",f));else{if(u)continue;i.push(D("@media",`(width >= theme(--breakpoint-${p}))`,[l("padding-inline",f)]))}}if(u)for(let[,a]of u)i.push(a);return i}function Kr({addVariant:t,config:r}){let n=r("darkMode",null),[e,i=".dark"]=Array.isArray(n)?n:[n];if(e==="variant"){let u;if(Array.isArray(i)||typeof i=="function"?u=i:typeof i=="string"&&(u=[i]),Array.isArray(u))for(let a of u)a===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));i=u}e===null||(e==="selector"?t("dark",`&:where(${i}, ${i} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",i):e==="class"&&t("dark",`&:is(${i} *)`))}function Pr(t){for(let[r,n]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[l("--tw-gradient-position",`to ${n} in oklab`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let n=t.theme.resolve(r.value.value,["--breakpoint"]);if(n)return[l("max-width",n)]}),t.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[l("flex-shrink","1")];if(r.value.kind==="arbitrary")return[l("flex-shrink",r.value.value)];if(N(r.value.value))return[l("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[l("flex-grow","1")];if(r.value.kind==="arbitrary")return[l("flex-grow",r.value.value)];if(N(r.value.value))return[l("flex-grow",r.value.value)]}})}function Dr(t,r){let n=t.theme.screens||{},e=r.variants.get("min")?.order??0,i=[];for(let[a,p]of Object.entries(n)){let g=function(v){r.variants.static(a,b=>{b.nodes=[D("@media",m,b.nodes)]},{order:v})};var u=g;let f=r.variants.get(a),s=r.theme.resolveValue(a,["--breakpoint"]);if(f&&s&&!r.theme.hasDefault(`--breakpoint-${a}`))continue;let d=!0;typeof p=="string"&&(d=!1);let m=pi(p);d?i.push(g):g(e)}if(i.length!==0){for(let[,a]of r.variants.variants)a.order>e&&(a.order+=i.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([a,p])=>(a>e&&(a+=i.length),[a,p])));for(let[a,p]of i.entries())p(e+a+1)}}function pi(t){return(Array.isArray(t)?t:[t]).map(n=>typeof n=="string"?{min:n}:n&&typeof n=="object"?n:null).map(n=>{if(n===null)return null;if("raw"in n)return n.raw;let e="";return n.max!==void 0&&(e+=`${n.max} >= `),e+="width",n.min!==void 0&&(e+=` >= ${n.min}`),`(${e})`}).filter(Boolean).join(", ")}function _r(t,r){let n=t.theme.aria||{},e=t.theme.supports||{},i=t.theme.data||{};if(Object.keys(n).length>0){let u=r.variants.get("aria"),a=u?.applyFn,p=u?.compounds;r.variants.functional("aria",(f,s)=>{let d=s.value;return d&&d.kind==="named"&&d.value in n?a?.(f,{...s,value:{kind:"arbitrary",value:n[d.value]}}):a?.(f,s)},{compounds:p})}if(Object.keys(e).length>0){let u=r.variants.get("supports"),a=u?.applyFn,p=u?.compounds;r.variants.functional("supports",(f,s)=>{let d=s.value;return d&&d.kind==="named"&&d.value in e?a?.(f,{...s,value:{kind:"arbitrary",value:e[d.value]}}):a?.(f,s)},{compounds:p})}if(Object.keys(i).length>0){let u=r.variants.get("data"),a=u?.applyFn,p=u?.compounds;r.variants.functional("data",(f,s)=>{let d=s.value;return d&&d.kind==="named"&&d.value in i?a?.(f,{...s,value:{kind:"arbitrary",value:i[d.value]}}):a?.(f,s)},{compounds:p})}}var mi=/^[a-z]+$/;async function jr({designSystem:t,base:r,ast:n,loadModule:e,globs:i}){let u=0,a=[],p=[];_(n,(m,{parent:g,replaceWith:v,context:b})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(g!==null)throw new Error("`@plugin` cannot be nested.");let y=m.params.slice(1,-1);if(y.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let T of m.nodes??[]){if(T.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${G([T])}

\`@plugin\` options must be a flat list of declarations.`);if(T.value===void 0)continue;let S=T.value,O=P(S,",").map(E=>{if(E=E.trim(),E==="null")return null;if(E==="true")return!0;if(E==="false")return!1;if(Number.isNaN(Number(E))){if(E[0]==='"'&&E[E.length-1]==='"'||E[0]==="'"&&E[E.length-1]==="'")return E.slice(1,-1);if(E[0]==="{"&&E[E.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${G([T]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(E);return E});x[T.property]=O.length===1?O[0]:O}a.push([{id:y,base:b.base,reference:!!b.reference},Object.keys(x).length>0?x:null]),v([]),u|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(g!==null)throw new Error("`@config` cannot be nested.");p.push({id:m.params.slice(1,-1),base:b.base,reference:!!b.reference}),v([]),u|=4;return}}}),Pr(t);let f=t.resolveThemeValue;if(t.resolveThemeValue=function(g){return g.startsWith("--")?f(g):(u|=Ur({designSystem:t,base:r,ast:n,globs:i,configs:[],pluginDetails:[]}),t.resolveThemeValue(g))},!a.length&&!p.length)return 0;let[s,d]=await Promise.all([Promise.all(p.map(async({id:m,base:g,reference:v})=>{let b=await e(m,g,"config");return{path:m,base:b.base,config:b.module,reference:v}})),Promise.all(a.map(async([{id:m,base:g,reference:v},b])=>{let y=await e(m,g,"plugin");return{path:m,base:y.base,plugin:y.module,options:b,reference:v}}))]);return u|=Ur({designSystem:t,base:r,ast:n,globs:i,configs:s,pluginDetails:d}),u}function Ur({designSystem:t,base:r,ast:n,globs:e,configs:i,pluginDetails:u}){let a=0,f=[...u.map(y=>{if(!y.options)return{config:{plugins:[y.plugin]},base:y.base,reference:y.reference};if("__isOptionsFunction"in y.plugin)return{config:{plugins:[y.plugin(y.options)]},base:y.base,reference:y.reference};throw new Error(`The plugin "${y.path}" does not accept options`)}),...i],{resolvedConfig:s}=bt(t,[{config:Rr(t.theme),base:r,reference:!0},...f,{config:{plugins:[Kr]},base:r,reference:!0}]),{resolvedConfig:d,replacedThemeKeys:m}=bt(t,f);t.resolveThemeValue=function(x,T){let S=v.theme(x,T);if(Array.isArray(S)&&S.length===2)return S[0];if(Array.isArray(S))return S.join(", ");if(typeof S=="string")return S};let g={designSystem:t,ast:n,resolvedConfig:s,featuresRef:{set current(y){a|=y}}},v=vt({...g,referenceMode:!1}),b;for(let{handler:y,reference:x}of s.plugins)x?(b||=vt({...g,referenceMode:!0}),y(b)):y(v);if(cr(t,d,m),Vr(t,d,m),_r(d,t),Dr(d,t),Or(d,t),!t.theme.prefix&&s.prefix){if(s.prefix.endsWith("-")&&(s.prefix=s.prefix.slice(0,-1),console.warn(`The prefix "${s.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!mi.test(s.prefix))throw new Error(`The prefix "${s.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=s.prefix}if(!t.important&&s.important===!0&&(t.important=!0),typeof s.important=="string"){let y=s.important;_(n,(x,{replaceWith:T,parent:S})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return S?.kind==="rule"&&S.selector===y?2:(T(j(y,[x])),2)})}for(let y of s.blocklist)t.invalidCandidates.add(y);for(let y of s.content.files){if("raw"in y)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(y,null,2)}

This feature is not currently supported.`);e.push(y)}return a}var gi=/^[a-z]+$/;function hi(){throw new Error("No `loadModule` function provided to `compile`")}function vi(){throw new Error("No `loadStylesheet` function provided to `compile`")}function yi(t){let r=0,n=null;for(let e of P(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e==="static"?r|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(n=e.slice(7,-1));return[r,n]}var he=(p=>(p[p.None=0]="None",p[p.AtApply=1]="AtApply",p[p.AtImport=2]="AtImport",p[p.JsPluginCompat=4]="JsPluginCompat",p[p.ThemeFunction=8]="ThemeFunction",p[p.Utilities=16]="Utilities",p[p.Variants=32]="Variants",p))(he||{});async function Ir(t,{base:r="",loadModule:n=hi,loadStylesheet:e=vi}={}){let i=0;t=[Q({base:r},t)],i|=await gt(t,r,e);let u=null,a=new _e,p=[],f=[],s=null,d=null,m=[],g=[],v=null;_(t,(y,{parent:x,replaceWith:T,context:S})=>{if(y.kind==="at-rule"){if(y.name==="@tailwind"&&(y.params==="utilities"||y.params.startsWith("utilities"))){if(d!==null){T([]);return}let O=P(y.params," ");for(let E of O)if(E.startsWith("source(")){let K=E.slice(7,-1);if(K==="none"){v=K;continue}if(K[0]==='"'&&K[K.length-1]!=='"'||K[0]==="'"&&K[K.length-1]!=="'"||K[0]!=="'"&&K[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");v={base:S.sourceBase??S.base,pattern:K.slice(1,-1)}}d=y,i|=16}if(y.name==="@utility"){if(x!==null)throw new Error("`@utility` cannot be nested.");if(y.nodes.length===0)throw new Error(`\`@utility ${y.params}\` is empty. Utilities should include at least one property.`);let O=Yt(y);if(O===null)throw new Error(`\`@utility ${y.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);f.push(O)}if(y.name==="@source"){if(y.nodes.length>0)throw new Error("`@source` cannot have a body.");if(x!==null)throw new Error("`@source` cannot be nested.");let O=y.params;if(O[0]==='"'&&O[O.length-1]!=='"'||O[0]==="'"&&O[O.length-1]!=="'"||O[0]!=="'"&&O[0]!=='"')throw new Error("`@source` paths must be quoted.");g.push({base:S.base,pattern:O.slice(1,-1)}),T([]);return}if(y.name==="@variant"&&(x===null?y.nodes.length===0?y.name="@custom-variant":(_(y.nodes,O=>{if(O.kind==="at-rule"&&O.name==="@slot")return y.name="@custom-variant",2}),y.name==="@variant"&&m.push(y)):m.push(y)),y.name==="@custom-variant"){if(x!==null)throw new Error("`@custom-variant` cannot be nested.");T([]);let[O,E]=P(y.params," ");if(!Le.test(O))throw new Error(`\`@custom-variant ${O}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(y.nodes.length>0&&E)throw new Error(`\`@custom-variant ${O}\` cannot have both a selector and a body.`);if(y.nodes.length===0){if(!E)throw new Error(`\`@custom-variant ${O}\` has no selector or body.`);let K=P(E.slice(1,-1),",");if(K.length===0||K.some(h=>h.trim()===""))throw new Error(`\`@custom-variant ${O} (${K.join(",")})\` selector is invalid.`);let o=[],c=[];for(let h of K)h=h.trim(),h[0]==="@"?o.push(h):c.push(h);p.push(h=>{h.variants.static(O,k=>{let w=[];c.length>0&&w.push(j(c.join(", "),k.nodes));for(let V of o)w.push(M(V,k.nodes));k.nodes=w},{compounds:ce([...c,...o])})});return}else{p.push(K=>{K.variants.fromAst(O,y.nodes)});return}}if(y.name==="@media"){let O=P(y.params," "),E=[];for(let K of O)if(K.startsWith("source(")){let o=K.slice(7,-1);_(y.nodes,(c,{replaceWith:h})=>{if(c.kind==="at-rule"&&c.name==="@tailwind"&&c.params==="utilities")return c.params+=` source(${o})`,h([Q({sourceBase:S.base},[c])]),2})}else if(K.startsWith("theme(")){let o=K.slice(6,-1);_(y.nodes,c=>{if(c.kind!=="at-rule")throw new Error('Files imported with `@import "\u2026" theme(\u2026)` must only contain `@theme` blocks.');if(c.name==="@theme")return c.params+=" "+o,1})}else if(K.startsWith("prefix(")){let o=K.slice(7,-1);_(y.nodes,c=>{if(c.kind==="at-rule"&&c.name==="@theme")return c.params+=` prefix(${o})`,1})}else K==="important"?u=!0:K==="reference"?y.nodes=[Q({reference:!0},y.nodes)]:E.push(K);E.length>0?y.params=E.join(" "):O.length>0&&T(y.nodes)}if(y.name==="@theme"){let[O,E]=yi(y.params);if(S.reference&&(O|=2),E){if(!gi.test(E))throw new Error(`The prefix "${E}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);a.prefix=E}return _(y.nodes,K=>{if(K.kind==="at-rule"&&K.name==="@keyframes")return O&2?1:(a.addKeyframes(K),1);if(K.kind==="comment")return;if(K.kind==="declaration"&&K.property.startsWith("--")){a.add(le(K.property),K.value??"",O);return}let o=G([D(y.name,y.params,[K])]).split(`
`).map((c,h,k)=>`${h===0||h>=k.length-2?" ":">"} ${c}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${o}`)}),!s&&!(O&2)?(s=j(":root, :host",[]),T([s])):T([]),1}}});let b=or(a);u&&(b.important=u),i|=await jr({designSystem:b,base:r,ast:t,loadModule:n,globs:g});for(let y of p)y(b);for(let y of f)y(b);if(s){let y=[];for(let[T,S]of b.theme.entries())S.options&2||y.push(l(de(T),S.value));let x=b.theme.getKeyframes();for(let T of x)y.push(U([T]));s.nodes=[Q({theme:!0},y)]}if(d){let y=d;y.kind="context",y.context={}}if(m.length>0){for(let y of m){let x=j("&",y.nodes),T=y.params,S=b.parseVariant(T);if(S===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${T}`);if(ve(x,S,b.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${T}`);Object.assign(y,x)}i|=32}return i|=ge(t,b),i|=Ne(t,b),_(t,(y,{replaceWith:x})=>{if(y.kind==="at-rule")return y.name==="@utility"&&x([]),1}),{designSystem:b,ast:t,globs:g,root:v,utilitiesNode:d,features:i}}async function Fr(t,r={}){let{designSystem:n,ast:e,globs:i,root:u,utilitiesNode:a,features:p}=await Ir(t,r);e.unshift(De(`! tailwindcss v${wt} | MIT License | https://tailwindcss.com `));function f(g){n.invalidCandidates.add(g)}let s=new Set,d=null,m=0;return{globs:i,root:u,features:p,build(g){if(p===0)return t;if(!a)return d??=ae(e,n),d;let v=!1,b=s.size;for(let x of g)n.invalidCandidates.has(x)||(x[0]==="-"&&x[1]==="-"?n.theme.markUsedVariable(x):s.add(x),v||=s.size!==b);if(!v)return d??=ae(e,n),d;let y=re(s,n,{onInvalidCandidate:f}).astNodes;return m===y.length?(d??=ae(e,n),d):(m=y.length,a.nodes=y,d=ae(e,n),d)}}}async function bi(t,r={}){let n=oe(t),e=await Fr(n,r),i=n,u=t;return{...e,build(a){let p=e.build(a);return p===i||(u=G(p),i=p),u}}}async function wi(t,r={}){return(await Ir(oe(t),r)).designSystem}function Se(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let t in Ye)t!=="default"&&(Se[t]=Ye[t]);module.exports=Se;
