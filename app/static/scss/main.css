.light-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background-color: none;
  color: #fff;
  border: 1px solid #fff;
  width: -moz-fit-content;
  width: fit-content;
  cursor: pointer;
}
.light-button:active {
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);
}
.light-button:disabled {
  background-color: #fff;
  color: #17B8A6;
  cursor: not-allowed;
}
.light-button:hover {
  background-color: #fff;
  color: #17B8A6;
  animation: slide 0.5s forwards;
}
@keyframes slide {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
  }
}

.card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
  margin-top: 4rem;
}

.small-card {
  background-color: none;
  border-radius: 10px;
  box-shadow: 0;
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
  margin-top: 4rem;
  height: -moz-max-content;
  height: max-content;
  width: -moz-fit-content;
  width: fit-content;
  gap: 1rem;
}

.green-card {
  background-color: #e4fffa;
  border-radius: 10px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
}

.horizontal-line {
  width: 80%;
  height: 1px;
  background-color: #374957;
  opacity: 0.2;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem auto;
  text-align: center;
}

@keyframes backandforth {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(50%);
  }
  100% {
    transform: translateX(100%);
  }
}
.animated-line {
  width: 100px; /* Adjust as needed */
  height: 3px;
  border-radius: 10px;
  opacity: 0.8;
  background-color: #17B8A6;
  position: relative;
  animation: backandforth 2s infinite alternate ease-in-out;
}

.right-line {
  width: 2px;
}

body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  background-image: url("/static/images/system_images/Background2.png");
  backdrop-filter: blur(50px);
  -webkit-backdrop-filter: blur(50px);
  position: relative;
}

header {
  background-color: #fff;
  color: #6C757D;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1;
}
header h1 {
  color: #17B8A6;
}
header img {
  width: 120px;
  padding: 10px 0;
}

footer {
  background: linear-gradient(180deg, #17B8A6, #0A524A);
  color: #fff;
  padding: 1rem;
  z-index: 1000;
}

section {
  padding: 2rem;
}

.brand {
  color: #17B8A6;
}

.hero {
  background: linear-gradient(180deg, #17B8A6, #0A524A);
  color: #fff;
  padding: 2rem;
}

h1 {
  color: #fff;
  font-weight: 600;
  font-size: 2rem;
  margin-bottom: 1rem;
}

h2 {
  color: #fff;
  font-weight: 600;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

h3 {
  color: #fff;
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.dark {
  color: #374957;
}

.mid-dark {
  color: #6C757D;
}

.primary {
  color: #17B8A6;
}

.light {
  color: #F5F7FA;
}

.centered-contents {
  display: flex;
  justify-content: center;
  align-items: center;
}

.centered-text {
  text-align: center;
}

.two-columns {
  display: grid;
  grid-template-columns: repeat(2, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}
.two-columns .hero-content p {
  color: #fff;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  width: inherit;
  font-weight: 500;
}

.primary-background {
  background-color: #17B8A6;
  border-radius: 5px;
  padding: 5px 10px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
}

.primary-background:hover {
  background-color: #0aa37d;
  color: #d4ebe5;
}

.three-columns {
  display: grid;
  grid-template-columns: repeat(3, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}

.four-columns {
  display: grid;
  grid-template-columns: repeat(4, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}

.image-medium {
  width: 80%;
  height: 80%;
  border-radius: 10px;
}

.image-smaller {
  width: 20%;
  height: 20%;
  border-radius: 0px;
}

.image-small {
  width: 40%;
  height: 40%;
  border-radius: 0px;
  margin: auto;
}

.light-bg {
  background-color: #F5F7FA;
}

.rocket-icon {
  animation: rocketFloat 1s infinite;
}

@keyframes rocketFloat {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}
.dark-green-container {
  max-width: 100%;
  padding: 2rem;
  color: #0A524A;
  margin: 0;
  background-color: #0A524A;
  background-image: url("/static/images/system_images/back-gg.png");
  background-size: cover;
}

.netpipo {
  background-image: url("/static/images/system_images/netpipo.jpg");
  background-size: cover;
  background-position: center;
  background-color: #0A524A;
  height: 100vh;
}

img.extra-small {
  width: 50px;
}

img.small {
  width: 100px;
}

img.medium {
  width: 200px;
}

img.large {
  width: 400px;
}

.glass-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


.feature {
  background-color: #1a685f;
}

.shape-decor {
  background-color: #00f7ff;
  border-radius: 70%;
  width: 100px;
  height: 100px;
  position: absolute;
  top: 20px;
  left: 0px;
  filter: blur(70px);
  z-index: -1;
}/*# sourceMappingURL=main.css.map */