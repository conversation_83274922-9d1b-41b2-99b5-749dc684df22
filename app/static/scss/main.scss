@use 'variables';
@use 'components';
@use 'mixins' as *;
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Jost', sans-serif;
    overflow-x: hidden;
    background-image: url('/static/images/system_images/netfish.png');
    backdrop-filter: blur(50px);
    -webkit-backdrop-filter: blur(50px); 
    position: relative;
    overflow-x: hidden;
}

header{
    background-color: variables.$white-color;
    color: variables.$mid-dark-color;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    h1{
        color: variables.$primary-color;
    }
    img{
        width: 120px;
        padding: 10px 0;
    }
    position:sticky;
    top: 0;
    z-index: 1;
}
footer{
    background: variables.$background-gradient;
    color: variables.$white-color;
    padding: 1rem;
    z-index: 1000;
}
section{
    padding: 2rem;
}
.brand{
    color: variables.$primary-color;
}
.hero{
    background: variables.$background-gradient;
    color: variables.$white-color;
    padding: 2rem;
}

h1{
    color: variables.$white-color;
    font-weight: 600;
    font-size: 2rem;
    margin-bottom: 1rem;
    animation-delay: 0.5s;
}
.reg-button{
    animation-delay: 1.5s;
}
article{
    animation-delay: 1s;
}
h2{
    color: variables.$white-color;
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}
h3{
    color: variables.$white-color;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}
.desktop-nav{ 
    a{
    position: relative;
    width: max-content;
    color: variables.$dark-color;
    text-decoration: none;
    }
    a:hover{
        background: linear-gradient(90deg, variables.$primary-color, variables.$dark-color);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        z-index: 1;
    }
}

.desktop-nav{
    a::before{
        content: "";
        background-color: variables.$dark-color;
        width: 0;
        height: 3px;
        position: absolute;
        bottom: 0;
        margin: -5px 0;
        border-radius: 10px;
        transition: 0.5s;
    }
    a:hover::before{
        width: 100%;
        background: linear-gradient(90deg, #17B8A6, variables.$dark-color);
    }
}

.dark{
    color: variables.$dark-color;
}
.mid-dark{
    color: variables.$mid-dark-color;
}
.primary{
    color: variables.$primary-color;
}
.light{
    color: variables.$light-color;
}
.centered-contents{
    display: flex;
    justify-content: center;
    align-items: center;
}
.centered-text{
    text-align: center;
}
.two-columns{
    @include grid-container(2, 2rem, center);

    .hero-content {
        p{
            color: variables.$white-color;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            width: inherit;
            font-weight: 500;
        }        
    }
}
.primary-background{
    background-color: variables.$primary-color;
    border-radius: 5px;
    padding: 5px 10px;
    color: #ffff;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
}


.primary-background:hover{
    background-color: #0aa37d;
    color: #d4ebe5
}
.three-columns{
    @include grid-container(3, 2rem, center);
}
.four-columns{
    @include grid-container(4, 2rem, center);
}
.image-medium{
    @include image(80%, 80%, 10px);
}
.image-smaller{
    @include image(20%, 20%, 0px);
}
.image-small{
    @include image(40%, 40%, 0px);
    margin: auto;
}

.light-bg{
    background-color: variables.$light-color;
}

.rocket-icon{
    animation: rocketFloat 1s infinite;
}
@keyframes rocketFloat {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
    100%{
        transform: translateY(0);

    }
}
.dark-green-container{
    @include container(100%, 2rem, variables.$dark-green-color, 0, variables.$dark-green-color);
    background-image: url('/static/images/system_images/back-gg.png');
    background-size: cover;
}
.netpipo {
    background-image: url('/static/images/system_images/netpipo.jpg');
    background-size: cover;
    background-position: center;  // Ensures the image is centered
    background-color: variables.$dark-green-color; // Apply solid background color
    height: 100vh;
}

img.extra-small {
    width: 50px;
}

img.small {
    width: 100px;
}

img.medium {
    width: 200px;
}

img.large {
    width: 400px;
}

.glass-container{
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.features{
    background: variables.$background-gradient;
}

.feature {
    background-color: variables.$mid-primary-color;
}

.shape-decor{
    @include decor-shape(#00f7ff, 70%, 100px, 100px, absolute, 20px, 0px);
    filter: blur(70px);
    z-index: -1;
}

.icon i{
    @include icon-container(variables.$primary-color, 50%, 30px, 30px, 10px, flex, center, center, 10px, 0 0 10px 0, variables.$white-color,
    absolute, -25px, 60px);
    box-sizing: border-box;
    
}
.icon i:hover{
    background-color: #0aa37d;
    color: #d4ebe5;
    cursor: pointer;
}

.primary-bg{
    background-color: variables.$primary-color;
}

.slideLeft{
    animation: 1s slideLeft ease-in;
}
.slideDown{
    animation: 1s slideDown ease-in;

}

@keyframes slideLeft {
    0% {
        transform: translateX(-40px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideDown {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(0);
    }
}
.moveUp{
    @include move(slideUp, 0.5s, linear, forwards);
    opacity: 0;
    @include slide(up, 20px);
}

.autoShow{
    animation: autoShowAnimation both;
    animation-timeline: view(50% 30%);
}
@media (max-width: 768px) {
    .autoShow {
        animation: autoShowAnimation both;
        animation-timeline: view(80% 20%);
    }
    .about{
        margin-top: 5rem;
    }
    .left-slide{
        animation: slideLeftAnimation both;
        animation-timeline: view(20% 90%);
    }
    .right-slide{
        animation: slideRightAnimation both;
        animation-timeline: view(20% 90%);
    }
    .norm-slideup{
        animation: normalSlidingUp both;
        animation-timeline: view(80% 20%);
    }
    .absolute-down{
        @include absolute-position(440px, 0, 0, 0);
        @include move(slideUp, 0.5s, linear, forwards);
        opacity: 0;
    }
}

@keyframes autoShowAnimation {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.autoSlide{
    animation: autoSlideAnimation 1s both;
    animation-duration: 2s;
    animation-timeline: view(60% 50%);
}
@keyframes autoSlideAnimation {
    0% {
        transform: translateY(440px);
        opacity: 0;
    }
    100% {
        transform: translateY(200px);
        opacity: 1;
    }
}

.payroll-processing {
    position: relative;
    background-position: center;  // Ensures the image is centered
    min-height: 500px;
}

.payroll-processing::before {
    content: "";
    position: absolute;
    background-image: url('/static/images/system_images/success.webp');
    background-size: cover;
    background-position: center;  // Ensures the image is centered
    filter: brightness(0.7);
    width: 100%;
    height: 100%;
    z-index: -1;
}

.absolute-down{
    @include absolute-position(440px, 0, 0, 0);
    @include move(slideUp, 0.5s, linear, forwards);
    opacity: 0;
}

.single-card{
    @include card(variables.$white-color, 0px, 0 0 10px 0 rgba(0,0,0,0.1), 1rem, 2rem, 100%);
    position: relative;
}

@keyframes slideUpper {
    0% {
        transform: translateY(200px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.norm-slideup{
    animation: normalSlidingUp both;
    animation-timeline: view(70% 30%);
}

@keyframes normalSlidingUp{
    0%{
        transform: translateY(100px);
        opacity: 0;
    }
    100%{
        transform: translateY(0);
        opacity: 1;
    }
}

.left-slide{
    animation: slideLeftAnimation both;
    animation-timeline: view(70% 30%);
}

@keyframes slideLeftAnimation {
    0%{
        transform: translateX(-200px);
        opacity: 0;
    }
    100%{
        transform: translateX(0);
        opacity: 1;
    }
}

.right-slide{
    animation: slideRightAnimation both;
    animation-timeline: view(70% 30%);
}

@keyframes slideRightAnimation {
    0%{
        transform: translateX(200px);
        opacity: 0;
    }
    100%{
        transform: translateX(0);
        opacity: 1;
    }
}

.hero-images{
    padding: 2rem;
    width: fit-content;
    display: flex;
    justify-content: center;
    align-items: center;
}
.hero_image{
    box-sizing: border-box;
    overflow: hidden;

}
.hero_image img{
    border: 2px solid rgb(255, 255, 255);
    width: 100px;
    min-height: 80px;
}
.hero-text {
    opacity: 0; /* Initially hidden */
    background: rgba(255, 255, 255, 0.795);
    padding: 5px 10px;
    position: absolute;
    bottom: 2px;
    
    width: 100%;
    display: flex;
    flex-direction: column;
    

    h1{
        color: variables.$dark-color;
        font-size: 8px;
        font-weight: 600;
    }
    p{  
        margin-top: -1rem;
        color: variables.$dark-color;
        font-size: 5px;
        font-weight: 500;
    }
}
.scroll-to-top-btn {
    position: sticky;
    bottom: 0;
    left: 100rem;
    background-color: #0ccec4;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s;
    z-index: 1000;
}

.scroll-to-top-btn:hover {
    opacity: 1;
}

