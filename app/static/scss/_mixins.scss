@mixin card($background-color, $border-radius, $box-shadow, $padding, $margin-bottom, $height) {
    background-color: $background-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    padding: $padding;
    margin-bottom: $margin-bottom;
    height: $height;
}

@mixin image($width, $height, $border-radius) {
    width: $width;
    height: $height;
    border-radius: $border-radius;
}

@mixin grid-container($columns, $gap, $align-items) {
    display: grid;
    grid-template-columns: repeat($columns, minmax(200px, 1fr));
    gap: $gap;
    align-items: $align-items;
}

@mixin container($max-width, $padding, $color, $margin, $background-color) {
    max-width: $max-width;
    padding: $padding;
    color: $color;
    margin: $margin;
    background-color: $background-color;
}

@mixin decor-shape($background-color, $border-radius, $width, $height, $position, $top, $left) {
    background-color: $background-color;
    border-radius: $border-radius;
    width: $width;
    height: $height;
    position: $position;
    top: $top;
    left: $left;
}

@mixin icon-container($background-color, $border-radius, $width, $height, $padding, $display, $align-items, $justify-content, $margin, $box-shadow, $color, $position, $top, $left) {
    background-color: $background-color;
    border-radius: $border-radius;
    width: $width;
    height: $height;
    padding: $padding;
    display: $display;
    align-items: $align-items;
    justify-content: $justify-content;
    margin: $margin;
    box-shadow: $box-shadow;
    color: $color;
    position: $position;
    top: $top;
    left: $left;
}

@mixin absolute-position($top, $right, $bottom, $left) {
    position: absolute;
    top: $top;
    right: $right;
    bottom: $bottom;
    left: $left;
}

@mixin flex-container($display, $flex-direction, $justify-content, $align-items) {
    display: $display;
    flex-direction: $flex-direction;
    justify-content: $justify-content;
    align-items: $align-items;
}

@mixin move ($animation-name, $animation-duration, $animation-timing-function:linear, $animation-fill-mode: forwards) {
    animation-name: $animation-name;
    animation-duration: $animation-duration;
    animation-timing-function: $animation-timing-function;
    animation-fill-mode: $animation-fill-mode;
}

@mixin slide($direction, $distance){
    @if $direction == "up"{
        @keyframes slideUp {
            0% {
                transform: translateY($distance);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
    }
}

