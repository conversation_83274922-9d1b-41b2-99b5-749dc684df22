@use 'variables';
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
a {
    text-decoration: none;
    font-size: 16px;
    border-radius: 5px;
}
li {
    list-style: none;
}
p{
    font-size: 16px;
    color: variables.$dark-color;
}
h1, h2, h3, h4, h5, h6{
    color: variables.$dark-color;
}
h1{
    font-size: 1.5rem;
    margin-bottom: 1rem;
}
h2{
    font-size: 1rem;
}
body {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Jost', sans-serif;
    position: relative;
    overflow: hidden;
    background-color: #eef2f1;
}

body.dark-mode {
    background-color: variables.$mid-dark-color;
    color: #e0e0e0;
    transition: background-color 0.3s ease, color 0.3s ease;

    @keyframes onOff {
    0% {
        background-color: #eef2f1;
        color: #000000;
    }
    100% {
        background-color: variables.$mid-dark-color;
        color: #e0e0e0;
    }

}


    .page-loader{
        background-color: rgba(0, 0, 0, 0.8);
        color: #e0e0e0;
        span, p{
            color: #e0e0e0;
        }
    }
    .sidebar-container{
        background-color: #2c2c2c;
        border-left: 1px solid #444;
        box-shadow: 3px 0px 8px rgba(0, 0, 0, 0.2);

        .feature-items{
            background-color: #1a1a1a;
            border: none;
            outline: none;
            li a{
                color: #e0e0e0;
                &:hover{
                    outline: 1px solid #444; /* Add outline on hover */
                    background-color: #333;
                }
            }
        }

    }
    .section {

        .red{
                    strong {
            color: #555555;
        }
            color: #c53030;
            background-color: #f6abab;
            padding: 2px 5px;
            border-radius: 5px;
        }
    }
    .header, .footer {
    background-color: #2c2c2c;
    border-bottom: 1px solid #444;
    .control-btn {
        color: #e0e0e0;
    }

    }
    .role {
        p{
            color: #e0e0e0;
        }
    }

    .dynamic-container {
    background-color: #1a1a1a;
    }
    .form-control, .text-box input {
        background-color: #535353;
        color: #e0e0e0;
        &:focus {
            outline: 1px solid variables.$primary-color;
            box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
        }
    }
    .form--container, .dsh-card, .second-row, .advanced-filters, .switch-company-form, .user-profile-menu, .search-results {
        p{ 
            strong{
                color: #e0e0e0;
            }
            color: #e0e0e0;
        }
        label{
            color: #e0e0e0;
        }
        background-color: #5a5757;
        color: #e0e0e0;
        border: 1px solid #444;
        box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.2);

        .flex-container{
            a{
                color: #e0e0e0;
                &:hover{
                    outline: 1px solid #444; /* Add outline on hover */
                    background-color: #333;
                }
            }
        }



        li {
            a{
                color: #e0e0e0;
                &:hover{
                    outline: 1px solid #444; /* Add outline on hover */
                    background-color: #333;
                }
            }
        }
    }   
    .dyn_header{
        background-color: #1a1a1a;
        color: #e0e0e0;
        border-bottom: 1px solid #444;
    }
    h1, h2, h3, h4, h5, h6, p {
        color: #e0e0e0;
    }

    table {
        border: none;
        th, td {
            color: #e0e0e0;
            border: 1px solid #444;
        }
        th {
            background-color: #2c2c2c;
            color: #292929;
        }
        tr:nth-child(even) {
            background-color: #333;
        }
        tr:nth-child(odd) {
            background-color: #444;
        }
    }
    .tfooter {
        background-color: #2c2c2c!important;
        color: #e0e0e0;
        border-top: 1px solid #444;
    }
    .grey-container {
        background-color: #2c2c2c;
        color: #e0e0e0;
        border: 1px solid #444;
        box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.2);
        ol{
            li{
                color: #e0e0e0;
            }
        }
    }
    .popup-content {
        background-color: #2c2c2c;
        color: #e0e0e0;
        border: 1px solid #444;
        box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.2);
    }

}


.bold{
    font-weight: 600;
}
.bolder{
    font-weight: 700;
}
.dashboard{
    display: grid;
    grid-template-columns:  250px auto;
    grid-template-rows:  auto 1fr 1fr auto;
    height: 100vh;
    overflow: hidden;
    width: 100%;
}

.dashboard.sidebar-hidden{
    display: flex;
    flex-direction: column;
    position: relative;
}



.sidebar-container{
    background-color: white;
    padding: 1rem 10px;
    grid-column: 1;
    grid-row: 1/4;
    position: sticky;
    top: 0;
    //box-shadow: 3px 0px 8px rgba(67, 95, 97, 0.1);
    z-index: 100;
    animation: slideRight 0.3s ease-in-out;
}
@keyframes slideRight {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}
@keyframes slideLeft {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

.sidebar-container.hidden {
    display: none;
    animation: slideLeft 0.3s ease-in-out;
}

/* Header controls styling */
.header-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.control-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    font-size: 1.2rem;
    color: #333;
    transition: color 0.3s ease;
}
.grey {
    color: rgb(186, 184, 184);
}

.control-btn:hover {
    color: variables.$primary-color;
    font-weight: 600;
}

/* Fullscreen adjustments */
.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
}

/* Adjust main content when sidebar is hidden */
.dynamic-container {
    transition: margin-left 0.3s ease;
}

.sidebar-container.hidden ~ .dynamic-container {
    margin-left: 0;
}

/* Ensure header and footer adjust */
.header, .footer {
    transition: margin-left 0.3s ease;
}

.sidebar-container.hidden ~ .header,
.sidebar-container.hidden ~ .footer {
    margin-left: 0;
}

.sidebar {
    padding: 2rem 10px;
    overflow-y: auto;
    height: 100%;
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: variables.$light-dark-color;
        border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
        background: variables.$light-dark;
        border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: variables.$mid-primary-color;
    }
    .active{
        width: fit-content;
        outline: 1px solid variables.$light-dark-color;

    }
    transition: opacity 0.3s ease;

}
 .top-right{
    position:absolute;
    top: 10px;
    right: 5px;
 }

button{
    outline: none;
    border: none;
    &hover{
        cursor: pointer;
    }
}

.side-buttons {
    margin: 1rem 0;
    p{
        margin-bottom: 10px;
        font-weight: 600;
    }
    ul{
        font-weight: 500;
        li{
            margin: 0.5rem ;
            a{
                color: variables.$mid-dark-color;
                display: flex;
                align-items: center;
                margin: 0 auto;
                padding: 5px;
                gap: 1rem;
                text-align: left;

                &:hover{
                    outline: 1px solid variables.$mid-dark-color; /* Add outline on hover */

                }
            }

        }
    }

}
.big{
    font-size: 5rem!important;
    font-weight: 600;
}

.mid-dark{
    color: variables.$mid-dark-color;
}

.light{
    font-weight: 300;
}
.bold{
    font-weight: 600;
}
.blue{
    color: variables.$blue-color;
}


.leave-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .leave-details h3 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .leave-details p {
            margin: 8px 0;
        }
        .leave-details strong {
            display: inline-block;
            width: 150px;
        }

.d-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 150px;
    padding: 5px 10px;
    box-sizing: border-box;
    margin: 0 auto;
}
.feature-items{
    display: none;
    transition: max-height 0.3s ease-in-out;
    overflow: hidden;
    margin: 0 1rem ;
    background-color: #f6ffff;
    width: 100%;
    box-sizing: border-box;
    li{
        a{
            color: variables.$mid-dark-color;
            display: flex;
            align-items: center;
            padding: 8px;
            gap: 1rem;
            text-align: left;
            font-size: 14px;
            text-decoration: none;

        }

    }

}

.table-responsive{
    position: relative;
}



.ls_container{
    display: grid;
    grid-template-columns: auto;
    grid-template-rows: auto;
    gap: 0.5rem;

    .dyn_header{
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #eef2f1;
        width: 100%;
        padding: 1rem 0 0 0;
    }
    .dyn_container{
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        height: 100%;
    }
}
@keyframes slideDown {
    0% {
        max-height: 0;
    }
    100% {
        max-height: 100px; /* Adjust based on your content */
    }
}
.flex{
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}
.document-actions{
    a{
        color: variables.$blue-color;
        display: flex;
        align-items: center;
        padding: 5px 10px;
        gap: 1rem;
        text-align: left;
        font-size: 14px;
        text-decoration: none;
        &:hover{
            outline: 1px solid variables.$blue-color; /* Add outline on hover */
        }
    }
}
.feature-items.active{
    display: block;
}
.header{
    border-bottom: 1px solid variables.$light-dark;
    height: fit-content;
    align-items: center;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    background-color: variables.$white-color;
    width: 100%;
    position: sticky;
    grid-column: 2;
    top: 0;
    z-index: 100;
}

.text-box{
        width: fit-content;
        display: flex;
        background-color: variables.$light-dark-color;
        position: relative;
        border-radius: 5px;

        input{
            border: none;
            color: variables.$mid-dark-color;
            width: inherit;
            border-radius: 5px;
            background-color: variables.$light-dark-color;
            padding: 10px 12px;
            outline: 1px solid variables.$border-color;
            transition: all 0.3s ease-in-out;

        }
        input:focus{
            outline: 1px solid variables.$primary-color;

            }
        i{
            position: absolute;
            top: 20%;
            right: 10px;
            color: variables.$primary-color;
        }
    }


.company-info{
    gap: 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 100;
    .fi-rr-building{
        border-radius: 50%;
        color: variables.$primary-color;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
    }
    h4{
        color: variables.$mid-dark-color;
    }
}


.user--info, .company-info{
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    .user--role p{
        color: variables.$dark-color;
        font-size: 16px;
        font-weight: 600;
    }
}

.phone{
display: none;
}


.employee-info{
    display: flex;
    gap: 2rem;
    align-items: center;
    justify-content: center;
    p{
        color: variables.$mid-dark-color;
    }
    
    .notification{
        position: relative;
        .red-dot{
            position: absolute;
            background-color: red;
            width: 7px;
            height: 7px;
            border-radius: 50%;
            top: 0;
            right: 0;
        }
    }
}
.icon{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border-radius: 50%;
    padding: 10px;
    font-weight: 700;
}
.button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    border-radius: 5px;
    width: fit-content;
    transition: all 0.3s ease-in-out;
    color: inherit;
}
.submit-btn, .btn-continue, .next-btn{
    background-color: variables.$primary-color;
    color: variables.$white-color;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 600;

    border: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    &:hover {
        background-color: variables.$mid-primary-color;
        color: variables.$white-color;
        animation: none;
    }
    .loader span{
        background-color: white!important;
    }
}

.submit-btn:disabled {
  background-color: #afafaf;
  cursor: not-allowed;
}

/* Loader styles */
.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
}

.loader span {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: spin 1.2s infinite ease-in-out;
}

.loader span:nth-child(2) {
  animation-delay: 0.2s;
}

.loader span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes spin {
  0%, 80%, 100% {
    transform: scale(0.3);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Hide loader by default */
.loader.hidden {
  display: none;
}

.white-container{
    background-color: variables.$white-color;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.1);
}
.submit-light-btn{
    background-color: variables.$light-primary-color;
    color: variables.$primary-color;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 600;

    border: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    &:hover {
        background-color: variables.$mid-primary-color;
        transform: scale(1.05);
        color: variables.$white-color;
        animation: none;
    }
}

.table-buttons{
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;

}
.primary-fx{
    color: variables.$blue-color;
    border: none;
    outline: none;
    font-size: 14px;
    border-radius: 10px;
    transition: all 0.3s ease-in-out;
    margin-bottom: 4px;
    i{
            font-size: 14px;

    }
    &:hover{
        text-decoration: underline;
        cursor: pointer;
    }
}

.primary{
    background-color: variables.$light-primary-color;
    color: variables.$primary-color;
    border: none;
    outline: none;
    &:hover{
        background-color: variables.$primary-color;
        color: variables.$white-color;
        cursor: pointer;
    }
}

.primary-text{
    color: variables.$primary-color;
    font-weight: 600;
    font-size: 12px;
}
.white{
    color: #ffffff;
}
.red-bg{
    background-color: #c53030;
    width: fit-content;
    padding: 5px 10px;
    border-radius: 5px;
}
.red{
    color: #c53030;
    font-weight: 400;
}



// Helper mixin for colored buttons
@mixin colored-button($name, $bg, $hover-bg, $color: #fff, $hover-color: #fff) {
    .#{$name}-button {
        i {
            font-size: 14px;
        }
        background-color: $bg;
        color: $color;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        text-align: center;
        gap: 5px;
        border: none;
        width: fit-content;
        cursor: pointer;
        transition: all 0.3s ease-in-out;
        &:hover {
            background-color: $hover-bg;
            color: $hover-color;
            animation: none;
        }
    }
}

// Use the mixin for common colors
@include colored-button('green', #009c12, #007a0d);
@include colored-button('red', #c53030, #a12323);
@include colored-button('blue', variables.$blue-color, #03568a);
@include colored-button('yellow', #ffc107, #bb8d03);
@include colored-button('orange', #ff620e, #be4c00);
@include colored-button('purple', #6f42c1, #5a2c9b);
@include colored-button('grey', #6c757d, #5e656c);
@include colored-button('primary', variables.$primary-color, variables.$mid-primary-color); // Primary box with light primary background

.form-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
    button i{
        font-weight: 600!important;
    }

}

.green{
    color: #009c12;
    font-weight: 400;
}
// Helper function to generate status box classes for major colors
@mixin status-box($name, $color, $bg-color: #f0f0f0) {
    .#{$name}-box {
        .status-text {
            background-color: $bg-color;
            color: $color;
            font-weight: 500;
        }
    }
}

// Use the mixin for each major color
@include status-box('green', #ffffff, #009c12, ); // Green box with light green background
@include status-box('red', #ffffff, #f6abab ); // Red box with light red background
@include status-box('blue', #f6f6f6, #007bff); // Blue box with light blue background
@include status-box('yellow', #6e540c, #ffc107); // Yellow box with light yellow background

// .green-box {
//     .status-text {
//         background-color: #009c12;
//     }
// }

.status-text{
    display: inline-block;
    padding: 0 5px;
    border-radius: 5px;
    font-size: 12px;
    color: white
}

.orange-box{
    .status-text {
    background-color: #ff620e;
    }
}

.bold{
    font-weight: 600;
}
.xsml{
    width: 50px;
    height: 50px;
}

.xxsml{
    width: 20px;
    height: 20px;
}

.orange{
    color: #ed9600;
}
.flex-container{
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: left;
    margin-bottom: 2rem;

    a{
        color: variables.$mid-dark-color;
        display: flex;
        align-items: center;
        padding: 5px 10px;
        gap: 1rem;
        text-align: left;
        text-decoration: none;
        outline: 1px solid variables.$mid-dark-color; /* Add outline on hover */
        transition: all 0.3s ease-in-out;
        &:hover{
            background-color: variables.$mid-dark-color;
            color: variables.$white-color;
        }
    }
}

.inside--subcontainer{
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.dynamic-container{
    grid-row: 2/4;
    grid-column: 2 ;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: auto;
    height: 100%;
    padding: 0 1rem 1rem 1rem;
    .row-data{
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        width: 100%;
    }
    .second-row{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background-color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.1);
    }

}

// for the sake of the dynamic content
.dynamic-content{
    grid-row: 2/4;
    grid-column: 2 ;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: auto;
    padding: 1rem;
    
    .row-data{
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        width: 100%;
    }

    .second-row{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background-color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.1);
    }
}

table th .fit-content{
    display: none;
    width: fit-content;
    height: fit-content;
    padding: 1rem;
    background-color: variables.$white-color;
    border-radius: 10px;
    box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.1);
}

.text-danger{
    color: variables.$red-color;
}
.dsh-card{
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-rows: auto, auto;
    background-color: variables.$white-color;
    border-radius: 10px;
    padding:15px;
    gap: 10px;
    box-shadow: 3px 4px 8px rgba(0, 0, 0, 0.1);
    flex: 1;
}

.card-title{
    grid-column: 1/span 2;
    grid-row: 1;
    display: flex;
    justify-content: space-between;
}
.card-data{
    grid-column: 1;
    grid-row: 2;
    margin-right: 1rem;
    p, i{
        font-size: 2rem;
        padding: 10px 20px;
        border-radius: 10px;
        font-weight: 600;
    }
}
.card-desc{
    grid-column: 2;
    grid-row: 2;
}
.flexed{
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 2rem;
    .dsh-card{
        width: fit-content;
    }
}
.comb-cards{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    margin-bottom: 1rem;

}
.mult-cards{
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 1rem;
}
.short-card{
    border-radius: 10px;
    padding: 5px 10px;
    gap: 1rem;
    display: flex;
    align-items: center;
    .card-title{
        display: flex;
        flex-direction: row;
        gap: 5px
    }
    .card-data{
        i{
            font-size: 1rem;
            border-radius: 50%;
            font-weight: 600;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }


}


.footer{
    grid-row: 4;
    grid-column: 1/span 2;
    background-color: variables.$white-color;
    display: flex;
    justify-content: center;
    align-items: center;
    color: variables.$mid-dark-color;
    gap: 1rem;
    z-index: 10000;
    }

.footer.sidebar-hidden{
    position: absolute;
    bottom: 0;
    width: 100%;
}
    .card--body {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 10px;
        overflow-x: auto;
        ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #e0e0e0;
            gap: 10rem;
            &:last-child {
                border-bottom: none;
                background-color: variables.$light-dark-color;
            }
            color: variables.$mid-dark-color;
        }
    }
    
    .card--heading {
        font-size: 1rem;
        font-weight: 600;
        background-color: variables.$light-dark-color;
        padding: 14px 16px;
        border-radius: 6px;
        color: variables.$mid-dark-color;

    }
    
    .card--details {
        font-size: .9rem;
        font-weight: 500;
        color: variables.$mid-dark-color;
    }
    
    .right {
        text-align: left;
        font-weight: 600;
    }
    
    .net_value {
        font-size: 1rem;
        font-weight: bold;
        color: variables.$primary-color;
    }
    
    .table-container {
        width: 100%;
        overflow-x: auto;
        position: relative; /* Ensures sticky positioning works within the container */
    }
    
    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 20px 0;
        text-align: left;
        border-radius: 10px;
        border: 1px solid variables.$light-primary-color;
    }
    .table-custom{
        overflow-y: auto;
    }
    td {
        padding:0 8px;
        border: 1px solid variables.$light-primary-color;
        color: variables.$mid-dark-color;
        min-width: 100px; /* Optional: Ensures columns have a minimum width */
        font-size: 14px;
    }

   .btn-container{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
   }
   .btn-container{
        .panel-btn{
        background-color: variables.$light-primary-color;
        color: variables.$primary-color;
        padding: 5px 10px;
        border-left: 1px solid variables.$primary-color;
        transition: all 0.3s ease-in-out;
    }
    .panel-btn:hover{
        background-color: variables.$primary-color;
        color: variables.$white-color;
        cursor: pointer;
    }
    .active{
        background-color: variables.$primary-color;
        color: variables.$white-color;
        cursor: pointer;
    }
}


    th, .tfooter, tfoot{
        padding: 5px 8px;
        background-color: variables.$light-dark-color!important;
        font-weight: 600;
        color: variables.$mid-dark-color;
    }

    .no-vertical-border, 
    .no-vertical-border th, 
    .no-vertical-border td {
        border-left: none;
        border-right: none;
    }

    .no-border,
    .no-border th,
    .no-border td {
        border: none;
    }

    .no-background{
        background-color: transparent;
    }
    
    tr:nth-child(even) {
        background-color: variables.$light-dark-color;
    }
    tr:nth-child(odd) {
        background-color: #ffffff;
    }
    tr:hover {
        background-color: #fffdfd;
    }
    
    /* Fix the first three columns */
    th:nth-child(-n+3),
    td:nth-child(-n+3) {
        position: sticky;
        left: 0;
        background-color: inherit; /* Maintains background consistency */
        z-index: 1; /* Ensures fixed columns stay above scrolling content */
    }
    
    /* Adjust left offset for each of the first three columns */
    th:nth-child(1),
    td:nth-child(1) {
        left: 0;
        z-index: 2; /* Higher z-index to ensure it stays on top */
    }
    
    th:nth-child(2),
    td:nth-child(2) {
        left: 100px; /* Adjust based on the width of the first column */
        z-index: 2;
    }
    
    th:nth-child(3),
    td:nth-child(3) {
        left: 200px; /* Adjust based on the combined width of the first two columns */
        z-index: 2;
    }
    
    /* Optional: Add a subtle shadow to indicate fixed columns */

.primary-btn {
    background-color: variables.$light-primary-color;
    color: variables.$primary-color;
    padding: 5px 8px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    animation: onOff 0.6s ease-in-out infinite alternate;
    &:hover {
        background-color: variables.$mid-primary-color;
        transform: scale(1.05);
        color: variables.$white-color;
        animation: none;
    }
    a {
        color: variables.$white-color;
        text-decoration: none;
    }
}
@keyframes onOff {
    0% {
        background-color: variables.$light-primary-color;
        color: variables.$primary-color;
    }
    100% {
        background-color: variables.$primary-color;
        color: variables.$white-color;
    }

}


  
  /* Dynamic Buttons */
  .dynamic--buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: space-between;
    align-items: center;
  }
  
  .btn-edit {
    display: flex;
    align-items: center; /* Vertically center icon and text */
    justify-content: center; /* Horizontally center icon and text */
    gap: 8px; /* Space between icon and text */
    padding: 8px 12px; /* Comfortable padding for button */
    text-decoration: none; /* Remove default underline */
    color: variables.$mid-dark-color; /* White text color */
    background-color: variables.$white-color;/* Blue background (customize as needed) */
    border-radius: 5px; /* Rounded corners */
    transition: outline 0.3s ease; 
    width: fit-content;
    box-sizing: border-box;
    text-align: center;
    
  }
  
  .btn-edit:hover {
    outline: 1px solid variables.$mid-dark-color; /* Add outline on hover */
}
  
  .btn-edit i {
    font-size: 18px; /* Slightly larger icon */
    line-height: 1; /* Ensure icon aligns properly */
  }
  

  
  .dynamic-title h1 {
    font-size: 24px;
    color: #333;
    display: inline;
    margin-bottom: 10px;

  }

  
  /* Form Sections */
  .selective--div {
    margin-bottom: 30px;
  }
  
  .selective--div legend {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 5px;
  }

  /* Form Row and Group */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
  }
  

.grey-container{
    background-color: #f1f5f8;
    padding: 20px;
    margin-bottom: 20px;
    width: 100%;
}


.upload-info ol {
    display: flex;
    justify-content: left;
    flex-direction: column;
    gap: 0.5rem;
}

.upload-info ol li {
    position: relative;
    padding-left: 40px;
    color: #333;
    font-size: 14px;
}

.upload-info ol li:before {
    content: '✔';
    color: #3498db;
    position: absolute;
    left: 15px;
    font-size: 1.2rem;
}

.form-group {
flex: 1;
min-width: 200px;
}

.form-group label {
display: block;
font-size: 14px;
font-weight: 500;
margin-bottom: 5px;
color: #333;
}

.form-group label .text-danger {
color: #dc3545;
}

.form-group .row {
display: flex;
align-items: center;
gap: 5px;
}
.message{
    animation: slideDown 0.5s ease-in-out;

    text-align: right;
    position: absolute;
    right: 0;
    top:0;

    p{
    border-radius: 5px;
    padding: 5px 10px;
    font-size: 14px;
    }
}

.success
    p{
            color: #00aa28;
            margin-top: 5px;
            background-color: #d4edda;
            outline: 1px solid #00aa28;
    
    }
.error
    p{
    color: #dc3545;
    margin-top: 5px;
    background-color: #ffebeb;
    outline: 1px solid #dc3545;
    }

@keyframes slideDown {
    0% {
        max-height: 0;
    }
    100% {
        max-height: 100px; /* Adjust based on your content */
    }
}


.update-btn {
  background-color: variables.$primary-color; /* sky-500 */
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
    width: fit-content;
}

.update-btn:hover {
  background-color: variables.$primary-color; /* sky-500 */
}

.update-btn:disabled {
  background-color: #94a3b8; /* slate-400 */
  cursor: not-allowed;
  opacity: 0.6;
}


#employee-modal{
    position: relative;
}
  
  .form--container {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    width: 100%;
    min-width: fit-content;
    color: variables.$mid-dark-color!important;
  }
  /* Submit Button */
  .btn-custom {
    display: inline-block;
    padding: 12px 30px;
    background-color: #28a745;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  .btn-custom:hover {
    background-color: #218838;
  }
  
  /* Message Containers */
  #message-container,
  #passed-container {
    margin-bottom: 20px;
    padding: 10px;
    border-radius: 5px;
    display: none;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .form-row {
      flex-direction: column;
    }
  
    .form-group {
      min-width: 100%;
    }
  

  }

.icon{
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}
.table-buttons{
    i{
        font-size: 12px;
    }
}

  .btn-continue {
    background-color: #3182ce;
    color: #fff;
    border: none;
}

.btn-continue:hover {
    background-color: #2b6cb0;
    transform: translateY(-1px);
}

.btn-cancel {
    background-color: #e53e3e;
    color: #fff;
    border: none;
}

.btn-cancel:hover {
    background-color: #c53030;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #38a169;
    color: #fff;
    border: none;
}

.btn-success:hover {
    background-color: #2f855a;
    transform: translateY(-1px);
}

.right-buttons-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.right-btns {
    position: relative;
}

.the-dots {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
}

.action--links {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
}


.action--links.show {
    display: block;
}

.action--links ul {
    list-style: none;
}

.action--links li {
    padding: 8px 16px;
}

.action--links a {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.9rem;
}

.action--links a:hover {
    background-color: #f7fafc;
}


.btn-image {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* Popups */
.mypopup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.uderline{
    text-decoration: underline;
    color: variables.$blue-color;
}

.popup-content {
    background-color: #fff;
    max-width: 800px;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.icons{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto;
    img{
        width: 50px;
        height: 50px;
    }
}

.icon{
        img{
        width: 50px;
        height: 50px;
    }
}

ol{
    padding-left: 10px;
    margin-bottom: 1rem;
    li{
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 2px;
    }
}
ol li::before{
    content: "•";
    color: variables.$blue-color;
    font-size: 1.5rem;
    margin-right: 10px;

}

.popup-content h2 {
    margin-bottom: 1.5rem;
}

.popup-content p {
    margin-bottom: 1rem;
    color: #4a5568;
}

.close{
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5rem;
    cursor: pointer;
}
strong{
    color: variables.$dark-color;
}
.listed-links {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;

    li {
        a{
            display: flex;
            gap: 5px;
            text-align: left;

        }
    }
}
.two-columns {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;


    
    li{
        a {
            display: flex;
            gap: 5px;
            padding: 10px 15px;
            margin: 0 auto;
            transition: all 0.3s ease-in-out;
            &:hover {
                background-color: variables.$light-primary-color;
                color: variables.$primary-color;
                border-radius: 5px;
                font-size: 14px;
                font-weight: 600;
            }
        }
    }
}

/* Full-screen loader */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 1rem;
    z-index: 9999; /* Ensure loader is on top */
}

/* Reused three-dot loader */
.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.loader span {
    width: 12px;
    height: 12px;
    background: variables.$primary-color; /* Match your submit-btn color */
    border-radius: 50%;
    animation: spin 1.2s infinite ease-in-out;
}

.loader span:nth-child(2) {
    animation-delay: 0.2s;
}

.loader span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes spin {
    0%, 80%, 100% {
        transform: scale(0.3);
        opacity: 0.3;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Forms */
.form-row {
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: #2d3748;
}

.input-group-text {
    display: flex;
    align-items: center;

}
.box{
    display: flex;
    align-items: center;
    justify-content: left;
    padding: 10px;
    gap: 10px;
    border-radius: 10px;
}
.bg-blue{
    background-color: variables.$light-blue-color;
}
.bg-green{
    background-color: variables.$light-green-color;
}
.bg-red{
    background-color: variables.$red-color;
}
.bg-yellow{
    background-color: variables.$yellow-color;
}
.blue-outline{
    border: 1px solid variables.$blue-color;
}
.form-control, .form-select {
    width: 100%;
    padding: 10px;
    border: 1px solid variables.$border-color;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: #f7f7f7;
    transition: border-color 0.4s ease-in-out;
    color: variables.$mid-dark-color;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: variables.$primary-color;
}

/* Flash Messages */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-success {
    background-color: #f0fff4;
    color: #2f855a;
    border: 1px solid #c6f6d5;
}

.alert-danger {
    background-color: #fff5f5;
    color: #c53030;
    border: 1px solid #fed7d7;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    color: #4a5568;
}

/* Icons */
.material-symbols-outlined, .fi {
    font-size: 1.2rem;
    vertical-align: middle;
}

.search-bar{
    position:relative
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    max-height: 200px;
    width: inherit;
    color: variables.$light-dark-color;
    font-size: 14px;
    overflow-y: auto;
    border-radius: 10px;
    z-index: 100000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 5px;
    margin-top: 5px
}
.search-results a {
    display: block;
    padding: 10px;
    color: #333;
    text-decoration: none;
    transition: background 0.2s;
}
.search-results a:hover {
    background: #f5f5f5;
}
.search-results:empty {
    display: none;
}

.user--profile {
    position: relative; /* Anchor for absolute positioning of dropdown */
}

.user-profile-menu, .switch-company-form{
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    list-style: none;
    padding: 0;
    margin: 0;
    padding: 10px;
}

.user-profile-menu li {
    border-bottom: 1px solid #eee;
}

.user-profile-menu li:last-child {
    border-bottom: none;
}

.user-profile-menu a {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: background 0.2s;
}

.user-profile-menu a:hover {
    background: #f5f5f5;
}

.user-profile-menu a span.material-symbols-outlined {
    margin-right: 10px;
    font-size: 20px;
}

.user-profile-toggle {
    cursor: pointer;
}

.user-profile-toggle.fi-rr-angle-small-up {
    transform: rotate(180deg); /* Rotate arrow when open */
}

.company-profile-container {
            margin: 0 auto;
            border-radius: 12px;
            overflow: hidden;
        }
        .profile-header {
            color: #fff;
            padding: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 15px;
            border-bottom: 1px solid #ddd;
        }
        .profile-header img {
            max-width: 100px;
        }
        .profile-header h1 {
            margin: 0;
            font-size: 24px;
        }
        .profile-content {
            padding: 30px;
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 20px;
        }
        .section {
            padding: 20px;
            border-radius: 8px;
            border-bottom: 1px variables.$border-color solid;
        }
        .section h2 {
            margin: 0 0 10px;
            font-size: 18px;
            color: variables.$mid-dark-color;
        }
        .section p, .section a {
            margin: 5px 0;
            font-size: 16px;
        }
        .section a {
            color: variables.$primary-color;
            background-color: variables.$light-primary-color;
            text-decoration: none;
            font-weight: 500;

        }

        .action-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .action-links a {
            padding: 8px 15px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background 0.3s;
            color: variables.$mid-dark-color;
            background-color: variables.$light-dark-color;
            width: fit-content;
            font-weight: 500;
            &:hover {
                background-color: variables.$primary-color;
                color: #fff;
            }
        }
        
        .attendance-section {
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .guidelines ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .guidelines li {
            margin-bottom: 10px;
            font-size: 14px;
        }

.totals-row td {
    font-weight: bold;
    color: variables.$mid-dark-color;

}
.filter-container, .att-filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 15px;
    position: relative;
}


.advanced-filters {
    display: none; /* Hidden by default */
}

.att-filter-container{
    position: relative;
    width: fit-content;
}

.att-filter-container .advanced-filters {
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    width: inherit;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.advanced-filters.visible{
    display: flex; /* Shown when toggled */
    flex-wrap: wrap;
    flex-direction: column;
    gap: 10px;
    position: absolute;
    top: 100%;
    left: 0px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    width: fit-content;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

}
.white-box{
    background-color: #fff;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: fit-content;
    margin: 0 auto;
}

.filter-container label, .att-filter-container label {
    margin-right: 5px;
    font-weight: 500;
}

.filter-container input, .att-filter-container input {
    padding: 6px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}
.filter-container select, .att-filter-container select{
    padding: 6px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.filter-container input[type="text"], .att-filter-container input[type="text"] {
    width: 200px;
}

.filter-container input[type="date"], .att-filter-container input[type="date"] {
    width: 150px;
}


.page_search{
    label{
        color: variables.$mid-dark-color;
        font-weight: 600;
    }
}

.page_rows {
    label{
        color: variables.$mid-dark-color;
        font-weight: 600;
    }
    select{
        background-color: white;
        color:variables.$primary-color;
        border-radius: 4px;
        border: 1px solid variables.$primary-color;
        font-weight: 600;
    }

}



.pagination button {
    padding: 6px 12px;
    margin: 0 5px;
    border: 1px solid variables.$primary-color;
    border-radius: 4px;
    color: variables.$primary-color;
    background-color: #fff;
    cursor: pointer;
}

.pagination button:hover {
    background-color: variables.$primary-color;
    color: white;
}


.pagination button.active {
    background-color: variables.$primary-color;
    color: white;
    border-color: variables.$primary-color;
}

.pagination button:disabled {
    color: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
}

.space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 10px
}

.pagination {
    text-align: center;
    margin:20px 0;
    position: sticky;
    bottom: 0;
    left: 0;
    width: fit-content;
}
.pagination button {
    margin: 0 5px;
    padding: 5px 10px;
    cursor: pointer;
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 4px;
}
.pagination button.active {
    background-color: variables.$primary-color;
    color: white;
    border-color: variables.$primary-color;
}
.pagination button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}


.primary-hover{
    background-color: variables.$primary-color;
    color: #ffffff;
    margin-left: 5px;
    .card-title p{
        color: white;
    }
    &:hover {
    background-color: variables.$primary-color;
    color: white!important;
    scale: 1.05;
    transition: all 0.3s ease-in-out;

    .card-title p, .card-data, .card-desc {
        color: white!important;
    }
    }
}

.grey-hover{
    color: #e6e6e6;
    background-color: #6c757d!important;
    .card-title p{
        color: #ffffff!important;
    }
    &:hover {
        background-color: #d3d3d3;
        color: white!important;
        scale: 1.05;
        transition: all 0.3s ease-in-out;
        cursor: pointer;

    .card-title p {
        color: white!important;
    }
    }
}


.red-hover{
    //background :#fae8ea;
    outline: 1px solid #dc3545;
    color: #dc3545!important;
    .card-title p{
        color: #dc3545!important;
    }
    &:hover {
        background-color: #dc3545;
        color: white!important;
        scale: 1.05;
        transition: all 0.3s ease-in-out;
        cursor: pointer;

    .card-title p {
        color: white!important;
    }
    }
}

.blue-hover{
    // background-color: #d1ecf1;
    outline: 1px solid #0d6efd;
    color: #0d6efd!important;
    .card-title p{
        color: #0d6efd!important;
    }
    &:hover {
        background-color: #0d6efd;
        color: white!important;
        scale: 1.05;
        transition: all 0.3s ease-in-out;
        cursor: pointer;

    .card-title p {
        color: white!important;
    }
    }
}

.purple-hover{
    background-color: #e2d9f3;
    color: #6f42c1!important;
    .card-title p{
        color: #6f42c1!important;
    }
    &:hover {
        background-color: #6f42c1;
        color: white!important;
        scale: 1.05;
        transition: all 0.3s ease-in-out;
        cursor: pointer;

    .card-title p {
        color: white!important;
    }
    }
}


.light-green{
    outline: 1px solid #218838;
    color: #218838!important;
    .card-title p{
        color: #218838!important;
    }
    &:hover {
        background-color: #218838;
        color: white!important;
        scale: 1.05;
        transition: all 0.3s ease-in-out;
        cursor: pointer;

    .card-title p {
        color: white!important;
    }
    }
}

/* Existing styles... */

/* Popup styles (may already exist for subPopup) */
.popup-timeout {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.popup-content-timeout {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

body.dark-mode .popup-content-timeout {
    background: #2c2c2c;
    color: #e0e0e0;
}

.decisive-btn {
    margin-top: 20px;
}

.btn-continue, .btn-cancel {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn-continue {
    background: variables.$primary-color;
    color: #fff;
    margin-right: 10px;
}

.btn-continue:hover {
    background: #177275;
}

.btn-cancel {
    background: #6c757d;
    color: #fff;
}

.btn-cancel:hover {
    background: #5a6268;
}

body.dark-mode .btn-continue {
    background: variables.$primary-color;
}

body.dark-mode .btn-continue:hover {
    background: variables.$primary-color;
}

body.dark-mode .btn-cancel {
    background: #495057;
}

body.dark-mode .btn-cancel:hover {
    background: #343a40;
}

/* Timeout popup specific styles */
#timeoutPopup .popup-content-timeout h1 {
    font-size: 24px;
    margin-bottom: 10px;
}

#timeoutPopup .popup-content-timeout p {
    font-size: 16px;
    margin-bottom: 20px;
}

#timeoutPopup #countdown {
    font-weight: bold;
    color: #dc3545; /* Red for urgency */
}


/* Responsive Design */
@media (max-width: 768px) {
    .dynamic--buttons {
        flex-direction: column;
        gap: 10px;
    }

    .right-buttons-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-edit, .btn-continue, .btn-cancel, .btn-success {
        width: 100%;
        justify-content: center;
    }

    table th, table td {
        padding: 8px;
        font-size: 0.85rem;
    }

    .popup-content {
        max-width: 90%;
        margin: 20% auto;
    }

    .company-info {
        p{
            font-size: 14px;
        }
    }
    .search-bar{
        display: none;
    }
    .header-controls {
        display: none;
    }
    .user--info{
        .fi-rr-user{
            display: none;
        }
    }
    .right-buttons-group {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        margin-bottom: 1rem;
        a, button{
            width: fit-content;
            font-size: 10px;
        }
    }
    .switch-company-toggle{
        margin-right: 3rem;
    }

    h1{
        font-size: 1rem;
    }
    .short-card{
        gap: 2px;
        padding: 5px;
    }
    .mult-cards{
        padding: 5px;
    }
    .dsh-card{
        padding: 10px;
        margin-bottom: 10px;
    }
    .flexed{
        flex-direction: column;
        align-items: center;
    }
     /* Fix the first three columns */
    th:nth-child(-n+3),
    td:nth-child(-n+3) {
        width: fit-content;
        position: static;
    }
    
    /* Adjust left offset for each of the first three columns */
    th:nth-child(1),
    td:nth-child(1) {
        position: static;
    }
    
    th:nth-child(2),
    td:nth-child(2) {
        position: static;
    }
    .profile-content{
        grid-template-columns: 1fr;
    }
    .profile-header {
        flex-direction: column;
    }
    .dynamic--buttons{
        h1 {
            margin: 0;
        }
    }

    .att-filter-container, .filter-container{
        gap: 5px;
    }
}

#map { height: 400px; width: 100%; }
        #search-box { width: 100%; padding: 8px; margin-bottom: 10px; }