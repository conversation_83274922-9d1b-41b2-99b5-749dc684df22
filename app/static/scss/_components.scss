@use 'variables';
@use 'mixins' as *;
.light-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    border: 1px solid variables.$white-color;
    width: fit-content;
    cursor: pointer;
    position: relative;
    overflow: hidden; // Prevents unwanted pseudo-element overlay
    text-decoration: none;
}

.light-button::before {
    content: '';
    width: 0%;
    height: 100%;
    position: absolute;
    background-color: variables.$white-color;
    border-radius: 20px;
    top: 0;
    left: 0;
    z-index: 0; // Keeps it behind
    transition: 0.5s;
}
.light-button:hover::before {
    width: 100%;
}

.light-button:hover {
    span {
    position: relative;
    z-index: 1; // Ensures it's above the pseudo-element
    background: linear-gradient(90deg, variables.$primary-color, variables.$dark-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
}

.card {
    @include card(variables.$white-color, 10px, 0 0 10px 0 rgba(0,0,0,0.1), 1rem, 2rem, 100%);
    margin-top: 4rem;
}
.small-card {
    @include card(none, 10px, 0, 1rem, 2rem, 100%);
    margin-top: 4rem;
    height: max-content;
    width: fit-content;
    gap: 1rem;

}

.green-card{
    @include card(variables.$light-green-color, 10px, 0 0 10px 0 rgba(0,0,0,0.1), 1rem, 2rem, 100%);
}

.horizontal-line{
    width: 80%;
    height: 1px;
    background-color: variables.$dark-color;
    opacity: 0.2;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem auto;
    text-align: center;
}

@keyframes backandforth {
    0% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(50%);
    }
    100% {
        transform: translateX(100%);
    }
}

.animated-line {
    width: 100px; /* Adjust as needed */
    height: 3px;
    border-radius: 10px;
    opacity: 0.8;
    background-color: variables.$primary-color;
    position: relative;
    animation: backandforth 2s infinite alternate ease-in-out;

}
.right-line {
    width: 2px;
    
}
