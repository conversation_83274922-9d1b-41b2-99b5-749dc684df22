@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");
.light-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #fff;
  width: fit-content;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.light-button::before {
  content: "";
  width: 0%;
  height: 100%;
  position: absolute;
  background-color: #fff;
  border-radius: 20px;
  top: 0;
  left: 0;
  z-index: 0;
  transition: 0.5s;
}

.light-button:hover::before {
  width: 100%;
}

.light-button:hover span {
  position: relative;
  z-index: 1;
  background: linear-gradient(90deg, #17B8A6, #374957);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
  margin-top: 4rem;
}

.small-card {
  background-color: none;
  border-radius: 10px;
  box-shadow: 0;
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
  margin-top: 4rem;
  height: max-content;
  width: fit-content;
  gap: 1rem;
}

.green-card {
  background-color: #e4fffa;
  border-radius: 10px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
}

.horizontal-line {
  width: 80%;
  height: 1px;
  background-color: #374957;
  opacity: 0.2;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem auto;
  text-align: center;
}

@keyframes backandforth {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(50%);
  }
  100% {
    transform: translateX(100%);
  }
}
.animated-line {
  width: 100px; /* Adjust as needed */
  height: 3px;
  border-radius: 10px;
  opacity: 0.8;
  background-color: #17B8A6;
  position: relative;
  animation: backandforth 2s infinite alternate ease-in-out;
}

.right-line {
  width: 2px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Jost", sans-serif;
  overflow-x: hidden;
  background-image: url("/static/images/system_images/netfish.png");
  backdrop-filter: blur(50px);
  -webkit-backdrop-filter: blur(50px);
  position: relative;
  overflow-x: hidden;
}

header {
  background-color: #fff;
  color: #6C757D;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1;
}
header h1 {
  color: #17B8A6;
}
header img {
  width: 120px;
  padding: 10px 0;
}

footer {
  background: linear-gradient(180deg, #17B8A6, #0A524A);
  color: #fff;
  padding: 1rem;
  z-index: 1000;
}

section {
  padding: 2rem;
}

.brand {
  color: #17B8A6;
}

.hero {
  background: linear-gradient(180deg, #17B8A6, #0A524A);
  color: #fff;
  padding: 2rem;
}

h1 {
  color: #fff;
  font-weight: 600;
  font-size: 2rem;
  margin-bottom: 1rem;
  animation-delay: 0.5s;
}

.reg-button {
  animation-delay: 1.5s;
}

article {
  animation-delay: 1s;
}

h2 {
  color: #fff;
  font-weight: 600;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.desktop-nav a {
  position: relative;
  width: max-content;
  color: #374957;
  text-decoration: none;
}
.desktop-nav a:hover {
  background: linear-gradient(90deg, #17B8A6, #374957);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  z-index: 1;
}

.desktop-nav a::before {
  content: "";
  background-color: #374957;
  width: 0;
  height: 3px;
  position: absolute;
  bottom: 0;
  margin: -5px 0;
  border-radius: 10px;
  transition: 0.5s;
}
.desktop-nav a:hover::before {
  width: 100%;
  background: linear-gradient(90deg, #17B8A6, #374957);
}

.dark {
  color: #374957;
}

.mid-dark {
  color: #6C757D;
}

.primary {
  color: #17B8A6;
}

.light {
  color: #F5F7FA;
}

.centered-contents {
  display: flex;
  justify-content: center;
  align-items: center;
}

.centered-text {
  text-align: center;
}

.two-columns {
  display: grid;
  grid-template-columns: repeat(2, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}
.two-columns .hero-content p {
  color: #fff;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  width: inherit;
  font-weight: 500;
}

.primary-background {
  background-color: #17B8A6;
  border-radius: 5px;
  padding: 5px 10px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
}

.primary-background:hover {
  background-color: #0aa37d;
  color: #d4ebe5;
}

.three-columns {
  display: grid;
  grid-template-columns: repeat(3, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}

.four-columns {
  display: grid;
  grid-template-columns: repeat(4, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}

.image-medium {
  width: 80%;
  height: 80%;
  border-radius: 10px;
}

.image-smaller {
  width: 20%;
  height: 20%;
  border-radius: 0px;
}

.image-small {
  width: 40%;
  height: 40%;
  border-radius: 0px;
  margin: auto;
}

.light-bg {
  background-color: #F5F7FA;
}

.rocket-icon {
  animation: rocketFloat 1s infinite;
}

@keyframes rocketFloat {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}
.dark-green-container {
  max-width: 100%;
  padding: 2rem;
  color: #0A524A;
  margin: 0;
  background-color: #0A524A;
  background-image: url("/static/images/system_images/back-gg.png");
  background-size: cover;
}

.netpipo {
  background-image: url("/static/images/system_images/netpipo.jpg");
  background-size: cover;
  background-position: center;
  background-color: #0A524A;
  height: 100vh;
}

img.extra-small {
  width: 50px;
}

img.small {
  width: 100px;
}

img.medium {
  width: 200px;
}

img.large {
  width: 400px;
}

.glass-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


.feature {
  background-color: #ffffff;
  color: #6e6e6e!important;
}
@keyframes scaleUp {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.feature:hover {
  background-color: #17B8A6;
  color: #fff!important;
  animation: scaleUp 0.5s ease-in-out;
}

.shape-decor {
  background-color: #00f7ff;
  border-radius: 70%;
  width: 100px;
  height: 100px;
  position: absolute;
  top: 20px;
  left: 0px;
  filter: blur(70px);
  z-index: -1;
}

.features h3{
  color: #374957;
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.icon i {
  background-color: #17B8A6;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px;
  box-shadow: 0 0 10px 0;
  color: #fff;
  position: absolute;
  top: -25px;
  left: 60px;
  box-sizing: border-box;
}

.icon i:hover {
  background-color: #0aa37d;
  color: #d4ebe5;
  cursor: pointer;
}

.primary-bg {
  background-color: #17B8A6;
}

.slideLeft {
  animation: 1s slideLeft ease-in;
}

.slideDown {
  animation: 1s slideDown ease-in;
}

@keyframes slideLeft {
  0% {
    transform: translateX(-40px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideDown {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
  }
}
.moveUp {
  animation-name: slideUp;
  animation-duration: 0.5s;
  animation-timing-function: linear;
  animation-fill-mode: forwards;
  opacity: 0;
}
@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.autoShow {
  animation: autoShowAnimation both;
  animation-timeline: view(50% 30%);
}

@media (max-width: 768px) {
  .autoShow {
    animation: autoShowAnimation both;
    animation-timeline: view(80% 20%);
  }
  .about {
    margin-top: 5rem;
  }
  .left-slide {
    animation: slideLeftAnimation both;
    animation-timeline: view(20% 90%);
  }
  .right-slide {
    animation: slideRightAnimation both;
    animation-timeline: view(20% 90%);
  }
  .norm-slideup {
    animation: normalSlidingUp both;
    animation-timeline: view(80% 20%);
  }
  .absolute-down {
    position: absolute;
    top: 440px;
    right: 0;
    bottom: 0;
    left: 0;
    animation-name: slideUp;
    animation-duration: 0.5s;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
    opacity: 0;
  }
}
@keyframes autoShowAnimation {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.autoSlide {
  animation: autoSlideAnimation 1s both;
  animation-duration: 2s;
  animation-timeline: view(60% 50%);
}

@keyframes autoSlideAnimation {
  0% {
    transform: translateY(440px);
    opacity: 0;
  }
  100% {
    transform: translateY(200px);
    opacity: 1;
  }
}
.payroll-processing {
  position: relative;
  background-position: center;
  min-height: 500px;
}

.payroll-processing::before {
  content: "";
  position: absolute;
  background-image: url("/static/images/system_images/success.webp");
  background-size: cover;
  background-position: center;
  filter: brightness(0.7);
  width: 100%;
  height: 100%;
  z-index: -1;
}

.absolute-down {
  position: absolute;
  top: 440px;
  right: 0;
  bottom: 0;
  left: 0;
  animation-name: slideUp;
  animation-duration: 0.5s;
  animation-timing-function: linear;
  animation-fill-mode: forwards;
  opacity: 0;
}

.single-card {
  background-color: #fff;
  border-radius: 0px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  height: 100%;
  position: relative;
}

@keyframes slideUpper {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.norm-slideup {
  animation: normalSlidingUp both;
  animation-timeline: view(70% 30%);
}

@keyframes normalSlidingUp {
  0% {
    transform: translateY(100px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.left-slide {
  animation: slideLeftAnimation both;
  animation-timeline: view(70% 30%);
}

@keyframes slideLeftAnimation {
  0% {
    transform: translateX(-200px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
.right-slide {
  animation: slideRightAnimation both;
  animation-timeline: view(70% 30%);
}

@keyframes slideRightAnimation {
  0% {
    transform: translateX(200px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
.hero-images {
  padding: 2rem;
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero_image {
  box-sizing: border-box;
  overflow: hidden;
}

.hero_image img {
  border: 2px solid rgb(255, 255, 255);
  width: 100px;
  min-height: 80px;
}

.hero-text {
  opacity: 0; /* Initially hidden */
  background: rgba(255, 255, 255, 0.795);
  padding: 5px 10px;
  position: absolute;
  bottom: 2px;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hero-text h1 {
  color: #374957;
  font-size: 8px;
  font-weight: 600;
}
.hero-text p {
  margin-top: -1rem;
  color: #374957;
  font-size: 5px;
  font-weight: 500;
}

.scroll-to-top-btn {
  position: sticky;
  bottom: 0;
  left: 100rem;
  background-color: #0ccec4;
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 1000;
}

.scroll-to-top-btn:hover {
  opacity: 1;
}

/*# sourceMappingURL=styles.css.map */
