/* Popup overlay */
.mypopup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* Popup content */
.popup-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

/* Responsive design */
@media (max-width: 600px) {
    .popup-content {
        width: 95%;
        padding: 15px;
    }

    .popup-content h2 {
        font-size: 1.2rem;
    }

    .form-control {
        font-size: 0.9rem;
    }

    .btn-continue,
    .btn-cancel {
        font-size: 0.9rem;
        padding: 8px 15px;
    }
}