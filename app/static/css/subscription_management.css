/* Subscription Management Styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

:root {
    --primary-color: #cce8e3;
    --secondary-color: #259E97;
    --text-color: #fff;
    --text-color-light: #000;
    --bg-color: #f4f4f4;
    --border-color: #f1f1f1;
    --border-radius: 5px;
    --background-color: #eff3f3;
    --main-color: rgb(157, 157, 157);
    --font-color: #575757;
    --mid-font-color: #7d7d7d;
    --max-font-color: #afafaf;
    --shadow-color: rgba(0, 255, 221, 0.1);
    --warning-color: hsl(0, 100%, 50%);
    --semi-warning-color: #ff5e41;
    --white-color: #fff;
    --green-color: #3d963d;
    --blue-color: #1c6d99;
}

/* Body styling */
body {
    background-color: var(--background-color);
    font-family: "Poppins", sans-serif;
    color: var(--font-color);
    line-height: 1.6;
}

/* Main container */
.container {
    max-width: 1100px; /* Increased from 900px to provide more space */
    margin: 0 auto;
    padding: 20px;
    width: 95%; /* Use percentage width for better responsiveness */
}

/* Page title */
.page-title {
    color: var(--font-color);
    margin-bottom: 25px;
    font-weight: 600;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
}

.page-title i {
    margin-right: 10px;
    color: var(--secondary-color);
}

/* Card styling */
.card {
    background-color: var(--white-color);
    border-radius: 10px;
    box-shadow: 0 4px 8px var(--shadow-color);
    margin-bottom: 30px;
    border: none;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px var(--shadow-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 2rem;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    color: var(--mid-font-color);
    font-weight: 600;
    font-size: 1.25rem;
    margin: 0;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #25a38b;
    background-color: var(--primary-color);
    border-radius: 5px;
    padding: 10px;
    margin-right: 10px;
    font-size: 20px;
}

.card-body {
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

/* Form styling */
.form-group {
    margin-bottom: 20px;
}

.form-control-label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: var(--font-color);
    font-size: 0.95rem;
}

.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 12px 15px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    width: 100%;
    background-color: var(--bg-color);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem var(--shadow-color);
    background-color: var(--white-color);
}

.form-control::placeholder {
    color: var(--max-font-color);
    font-style: italic;
    font-size: 0.9rem;
}

/* Helper text styling */

.form-text.text-muted {
    font-size: 0.8rem;
    margin-top: 5px;
    color: #6c757d;
}

.text-danger {
    color: var(--warning-color);
    font-size: 0.85rem;
    margin-top: 5px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

.text-right {
    text-align: right;
}

/* Button group styling */
.button-group {
    margin-top: 20px;
}

.button-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
}

.button-group-left {
    display: flex;
    gap: 15px;
    align-items: center;
}

.button-group-left .btn {
    margin-right: 0;
}

@media (max-width: 576px) {
    .button-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .button-group-left {
        flex-direction: column;
        width: 100%;
        gap: 10px;
    }

    .button-container .btn,
    .button-group-left .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Button styling */
.btn {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

.btn-secondary {
    background-color: var(--main-color);
    border-color: var(--main-color);
    color: var(--white-color);
}

.btn-secondary:hover {
    background-color: var(--mid-font-color);
    border-color: var(--mid-font-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

/* Custom button classes from dashboard */
.primary-button {
    color: #17B8A6 !important;
    background-color: #DBECEF !important;
}

.primary-button:hover {
    color: #fff !important;
    background-color: #17B8A6 !important;
}

.secondary-button {
    color: #083153 !important;
    background-color: #dce8f8 !important;
}

.secondary-button:hover {
    color: #fff !important;
    background-color: #243d52 !important;
}

/* Table styling */
.table-responsive {
    margin-top: 15px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    font-weight: 600;
    text-align: left;
    padding: 14px 15px;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    transition: background-color 0.2s ease;
    color: var(--font-color);
    white-space: nowrap; /* Prevent text wrapping in cells */
    min-width: 100px; /* Minimum width for each cell */
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--bg-color);
}

.table tbody tr:hover {
    background-color: rgba(37, 158, 151, 0.05);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Alert styling */
.alert {
    padding: 12px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-success {
    color: var(--green-color);
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: var(--warning-color);
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: var(--blue-color);
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* DataTables Custom Styling */
.datatable-container {
    padding: 10px 0;
    width: 100%;
}

.table-custom {
    width: 100%;
    overflow-x: auto;
    display: block;
    max-width: 100%;
}

/* Ensure table takes full width and allows scrolling */
.dataTables_wrapper {
    width: 100%;
    overflow-x: auto;
    display: block !important;
    margin-bottom: 20px;
}

/* Make the table wider to fit all content */
table.dataTable {
    width: 100% !important;
    min-width: 800px; /* Minimum width to ensure all columns are visible */
}

/* Controls container */
.dataTables_wrapper .dt-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;
    position: relative;
}

.dataTables_wrapper .dt-length {
    position: absolute;
    left: 0;
}

.dataTables_wrapper .dt-search {
    /* Centered position */
    margin: 0 auto;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: var(--font-color);
    margin-bottom: 15px;
    float: none !important;
    text-align: left;
}

/* Adjust filter position */
.dataTables_wrapper .dataTables_filter {
    text-align: center;
    margin-bottom: 15px;
    width: auto;
}

/* Style the search label */
.dataTables_wrapper .dataTables_filter label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.dataTables_wrapper .dataTables_length {
    margin-right: 20px;
    width: auto;
}

/* Container for length and filter */
.dataTables_wrapper .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 4px;
    background-color: var(--bg-color);
    color: var(--font-color);
    min-width: 80px;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 6px 10px;
    background-color: var(--bg-color);
    color: var(--font-color);
    margin-left: 5px;
    width: 180px;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 0.2rem var(--shadow-color);
}

.dataTables_wrapper .dataTables_paginate {
    margin-top: 15px;
    float: right;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    color: var(--font-color) !important;
    border-radius: var(--border-radius);
    margin: 0 2px;
    padding: 5px 10px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--secondary-color) !important;
    color: var(--white-color) !important;
    border-color: var(--secondary-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--primary-color) !important;
    color: var(--secondary-color) !important;
    border-color: var(--primary-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
    color: var(--max-font-color) !important;
    background-color: transparent !important;
    border: 1px solid var(--border-color);
    cursor: default;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .card-body {
        padding: 15px;
    }

    .table th, .table td {
        padding: 8px 10px;
    }

    .btn {
        padding: 8px 16px;
    }

    .row {
        margin-right: 0;
        margin-left: 0;
    }

    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
        padding-right: 0;
        padding-left: 0;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: left;
        float: none;
        display: block;
        margin-bottom: 10px;
    }
}

/* Alert styling */
.alert {
    padding: 12px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Payments table container */
.payments-table-container {
    width: 100%;
    overflow-x: auto;
}

/* Specific styling for payments table */
#payments-table_wrapper {
    margin-top: 15px;
    width: 100%;
}

#payments-table_filter {
    margin-bottom: 15px;
    text-align: center;
    position: relative;
    z-index: 2;
}

#payments-table_filter input {
    width: 250px !important;
    height: 36px !important;
    border-radius: 18px !important;
    padding: 0 15px !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #ddd !important;
    transition: all 0.3s ease !important;
}

#payments-table_filter input:focus {
    box-shadow: 0 0 0 2px rgba(37, 158, 151, 0.25) !important;
    border-color: var(--secondary-color) !important;
    background-color: #fff !important;
}

#payments-table_filter label {
    font-weight: 500;
    color: var(--mid-font-color);
}

#payments-table_length {
    margin-bottom: 15px;
    text-align: left;
    position: relative;
    z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .card-body {
        padding: 15px;
    }

    .table th, .table td {
        padding: 8px 10px;
    }

    .btn {
        padding: 8px 16px;
    }

    .dataTables_wrapper .dt-controls {
        flex-direction: column;
        gap: 10px;
    }

    .dataTables_wrapper .dt-length {
        position: static;
        width: 100%;
    }

    .dataTables_wrapper .dt-search {
        width: 100%;
    }

    #payments-table_length {
        text-align: center;
        margin-bottom: 10px;
        width: 100%;
    }

    #payments-table_filter {
        text-align: center;
        margin-bottom: 10px;
        width: 100%;
    }
}
