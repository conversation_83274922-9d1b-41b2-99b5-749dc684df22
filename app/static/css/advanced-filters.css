/* Advanced Filtering Styles */

.filter-section {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filter-form {
    margin-top: 15px;
    padding: 15px;
    background-color: #fff;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 15px;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
}
