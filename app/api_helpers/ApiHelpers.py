# import all unrecoginzed refrences except db_session and employe_id
import base64
import re
from flask import current_app
from datetime import datetime, timedelta
from app.models.company import Attendance, LeaveApplication
from flask_jwt_extended import create_access_token, create_refresh_token, get_jti
from app.api import jwt_config

class ApiHelper:
    @staticmethod
    def get_employee_attendence(db_session, employee_id):
        # Get attendance for an employee
        attendance = Attendance.get_attendance_for_employee(db_session, employee_id)
        # get the number of attendance in the current month
        attendance_count = 0
        for att in attendance:
            date_str = att.get('time_in')
            if date_str is None:
                continue
            try:
                date = datetime.strptime(date_str, "%d/%m/%Y %H:%M:%S").date()
            except Exception as e:
                current_app.logger.error(f"Error parsing date: {str(e)}")
                continue
            if date.month == datetime.now().month:
                attendance_count += 1
        
        return attendance_count

    @staticmethod
    def get_employee_leave_data(db_session, employee_id):
        # Get leave details for the employee
        details = LeaveApplication.get_leave_application_for_employee(db_session, employee_id)
        # count the number of leave applications whose status is pending
        leave_pending = 0
        for detail in details:
            if detail['status'] == 'pending':
                leave_pending += 1

        return leave_pending
    
    @staticmethod
    def count_employee_documents(db_session, employee_id):
        from app.models.company_documents import Document
        
        # Count documents for this specific employee
        employee_documents_count = db_session.query(Document).filter(
            Document.document_type == 'employee',
            Document.employee_id == employee_id
        ).count()

        return employee_documents_count
    
    @staticmethod
    def get_employee_external_data(db_session, employee_id):
        try:
            attence_count = ApiHelper.get_employee_attendence(db_session, employee_id)
        except Exception as e:
            current_app.logger.error(f"Error getting attendance data: {str(e)}")
            raise
        try:
            leave_pending = ApiHelper.get_employee_leave_data(db_session, employee_id)
        except Exception as e:
            current_app.logger.error(f"Error getting leave details: {str(e)}")
            raise
        # Get employee document counts
        try:
            employee_documents_count = ApiHelper.count_employee_documents(db_session, employee_id)
        except Exception as e:
            current_app.logger.error(f"Error getting employee document counts: {str(e)}")
            raise

        return attence_count, leave_pending, employee_documents_count

    @staticmethod
    def filter_by_date(uploaded_at, date_range):
        """Helper function to filter documents by date range."""
        if not uploaded_at:
            return False
        now = datetime.now()
        if date_range == 'today':
            return uploaded_at.date() == now.date()
        if date_range == 'week':
            return uploaded_at >= now - timedelta(days=7)
        if date_range == 'month':
            return uploaded_at >= now - timedelta(days=30)
        return False

    @staticmethod
    def allowed_file(filename):
        """
        Checks if a file is allowed based on the ALLOWED_EXTENSIONS list.
        
        If the file has an ALLOWED_EXTENSIONS, it returns True, otherwise it returns False.
        """
        ALLOWED_EXTENSIONS = {'pdf', 'docx'}
        return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

    @staticmethod
    def encode_string_base64(decoded_string: str):
        """Encodes a string using Base64."""
        encoded_bytes = base64.urlsafe_b64encode(decoded_string.encode('utf-8'))
        return encoded_bytes.decode('utf-8')

    @staticmethod
    def decode_string_base64(encoded_string: str):
        """Decodes a Base64 string back to original."""
        decoded_bytes = base64.urlsafe_b64encode(encoded_string.encode('utf-8'))
        return decoded_bytes.decode('utf-8')
    
    @staticmethod
    def refresh_tokens(identity:dict, additional_claims: dict, jwt_payload: dict):
        """
        Refresh access and refresh tokens, given the current identity and additional claims.
        
        Revokes the old access and refresh tokens by setting an empty value in the redis store with the old jti.
        Stores the new access token jti in the redis store for the given identity.
        
        Args:
            identity (dict): A dictionary containing the user's email and other information.
            additional_claims (dict): Additional claims to be included in the access token.
            jwt_payload (dict): The current JWT payload.
        Returns:
            access_token (str): The new access token.
            refresh_token (str): The new refresh token.
        """
        refresh_token = create_refresh_token(identity=identity,additional_claims=additional_claims)
        additional_claims.update(refresh_jti=get_jti(refresh_token))
        access_token = create_access_token(identity=identity, additional_claims=additional_claims)
        
        # Revoke old access and refresh token
        user_email = identity.get('email')
        old_access_jti = jwt_config.redis_client.get(f"{user_email}_access")
        old_refresh_jti = jwt_config.redis_client.get(f"{user_email}_refresh")
        if old_access_jti:
            # Refresh token might be valid while access token is already expired and removed from redis
            jwt_config.redis_client.set(old_access_jti, "", ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        if old_refresh_jti:
            jwt_config.redis_client.set(old_refresh_jti, "", ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])
        
        # Store new user access and refresh jti in redis store for easy revocation
        jwt_config.redis_client.set(f"{user_email}_access", get_jti(access_token), ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        jwt_config.redis_client.set(f"{user_email}_refresh", get_jti(refresh_token), ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])
        
        return access_token, refresh_token

class UserInputValidator:
    STRING_CHECK = lambda x: isinstance(x, str) and len(x.strip()) > 2
    LIST_CHECK = lambda x: isinstance(x, list) and len(x) > 0
    DIGIT_CHECK = lambda x: str(x).isdigit() and int(x) >= 0
    FLOAT_CHECK = lambda x: (isinstance(x, float) or isinstance(x, int)) or (isinstance(x, str) and re.match(r"^\d+(\.\d+)?$", str(x).strip()))
    TIME_CHECK = staticmethod(lambda x: isinstance(x, str) and bool(re.match(r"^\d{2}:\d{2}(:\d{2})?$", x.strip())))

    COMMON_STRING_CHECK_PROPERTIES = {
        "required": True,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(STRING_CHECK)
    }
    COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES = {
        "required": False,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(STRING_CHECK)
    }
    COMMON_DIGITS_CHECK_PROPERTIES = {
        "required": True,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(DIGIT_CHECK)
    }
    COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES = {
        "required": False,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(FLOAT_CHECK)
    }
    COMMON_FLOAT_CHECK_PROPERTIES = {
        "required": True,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(FLOAT_CHECK)
    }
    COMMON_NOT_REQUIRED_DIGITS_CHECK_PROPERTIES = {
        "required": False,
        "error_message": staticmethod(lambda field: f"{field} is required".float),
        "validate": staticmethod(DIGIT_CHECK)
    }
    COMMON_LIST_CHECK_PROPERTIES = {
        "required": True,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(LIST_CHECK)
    }
    UUID_CHECK_PROPERTIES = {
        "required": True,
        "error_message": staticmethod(lambda field: f"{field} is required"),
        "validate": staticmethod(lambda x: len(x) >= 36) # Because all UUID has length of 36
    }

    COMMON_DATE_CHECK_PROPERTIES = {
        "required": True,
        "error_message": lambda field: f"{field} is required", # To make key appear in 'is required' error
        "validate": staticmethod(lambda x: isinstance(x, str) and '-' in x and len(x.strip()) > 0)
    }

    COMMON_NOT_REQUIRED_DATE_CHECK_PROPERTIES = {
        "required": False,
        "error_message": lambda field: f"{field} is required", # To make key appear in 'is required' error
        "validate": staticmethod(lambda x: isinstance(x, str) and '-' in x and len(x.strip()) > 0)
    }

    TIME_CHECK_PROPERTIES = {
        "required": True,
        "error_message": lambda field: f"{field} is required",
        "validate": TIME_CHECK
    }


    USER_REGISTER_INPUT_CONFIG = {
        'username': {
            'required': True,
            'error_message': "Username must be between 3 and 20 characters",
            "validate": staticmethod(lambda x: x.isalnum() and not x.isdigit() and 3 <= len(x) <= 20)
        },
        'email': {
            'required': True,
            'error_message': "Invalid email address",
            'validate': staticmethod(lambda x: '@' in x and '.' in x.split('@')[-1])
        },
        'password': {
            'required': True,
            'error_message': "Password must contain at least one letter, one number, and be between 6 and 20 characters",
            "validate": staticmethod(lambda x: (any(char.isdigit() for char in x) and
                                                any(char.isalpha() for char in x) and 6 <= len(x) <= 20))
        },
        'phone_number': {
            'required': True,
            'error_message': "Invalid phone number",
            'validate': staticmethod(lambda x: x.isdigit() and len(x) == 10)
        },
        'first_name': {
            'required': True,
            'error_message': "First name must be between 2 and 20 characters",
            'validate': staticmethod(lambda x: 2 <= len(x) <= 20)
        },
        'last_name': {
            'required': True,
            'error_message': "Last name must be between 2 and 20 characters",
            'validate': staticmethod(lambda x: 2 <= len(x) <= 20)
        }
    }

    USER_LOGIN_CONFIG= {
        'username': {
            'required': True,
            'error_message': "Username must be between 3 and 50 characters",
            "validate": staticmethod(lambda x: not x.isalnum() or x.isalnum() and 3 <= len(x) <= 50)
        },
        'password': {
            'required': True,
            'error_message': "Password must contain at least one letter, one number, and be between 6 and 20 characters",
            "validate": staticmethod(lambda x: (any(char.isdigit() for char in x) and
                                                any(char.isalpha() for char in x) and 6 <= len(x) <= 20))
        }
    }

    USER_LOGIN_OTP_CONFIG = {
        'otp': {
            'required': True,
            'error_message': "Invalid OTP",
            'validate': staticmethod(lambda x: str(x).isdigit() and len(str(x)) == 6)
        }
    }

    USER_RESET_PASSWORD_CONFIG = {
        'email': {
            'required': True,
            'error_message': "Invalid email address",
            'validate': staticmethod(lambda x: '@' in x and '.' in x.split('@')[-1])
        }
    }

    COMPANY_REGISTER_INPUT_CONFIG = {
        'company_name': {
            'required': True,
            'error_message': "Company name must be between 2 and 100 characters",
            'validate': staticmethod(lambda x: isinstance(x, str) and 2 <= len(x.strip()) <= 100)
        },
        'company_tin': {
            'required': True,
            'error_message': "Company TIN must be 9 digits",
            'validate': staticmethod(lambda x: x.isdigit() and len(x) == 9)
        },
        'rssb_number': {
            'required': True,
            'error_message': "RSSB number must be 5-15 digits",
            'validate': staticmethod(lambda x: x.isdigit() and 5 <= len(x) <= 15)
        },
        'phone_number': {
            'required': True,
            'error_message': "Phone number must be 10 digits",
            'validate': staticmethod(lambda x: x.isdigit() and len(x) == 10)
        },
        'email': {
            'required': True,
            'error_message': "Invalid email address",
            'validate': staticmethod(lambda x: isinstance(x, str) and '@' in x and '.' in x.split('@')[-1])
        },
        'number_employee': COMMON_DIGITS_CHECK_PROPERTIES,
        'company_type': COMMON_STRING_CHECK_PROPERTIES,
        'country': COMMON_STRING_CHECK_PROPERTIES,
        'province': COMMON_STRING_CHECK_PROPERTIES,
        'district': COMMON_STRING_CHECK_PROPERTIES,
        'sector': COMMON_STRING_CHECK_PROPERTIES,
        'cell': COMMON_STRING_CHECK_PROPERTIES,
        'village': COMMON_STRING_CHECK_PROPERTIES,
        'plan_id': COMMON_DIGITS_CHECK_PROPERTIES,
        'initial_qualification_period': COMMON_NOT_REQUIRED_DIGITS_CHECK_PROPERTIES,
        'increment_policy': COMMON_NOT_REQUIRED_DIGITS_CHECK_PROPERTIES
    }

    LEAVE_APPROVAL_INPUT_CONFIG = {
        "approval":{
            "required": True,
            "error_message": "Approval is required",
            "validate": staticmethod(lambda x: x in ['approved', 'rejected'] and len(x.strip()) > 0)
        },
        "remarks": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "approver_id": UUID_CHECK_PROPERTIES,
        "approver_role":{
            "required": False,
            "error_message": "Approver Role is required",
            "validate": staticmethod(lambda x: isinstance(x, str) and len(x.strip()) > 0 and x in ["manager", "hr", 'company_hr', 'accountant'])
        }
    }

    ADD_ADVANCE_INPUT_CONFIG = {
        "amount": COMMON_DIGITS_CHECK_PROPERTIES,
        "reason": COMMON_STRING_CHECK_PROPERTIES,
        "due_dates": COMMON_LIST_CHECK_PROPERTIES,
        "installment_amounts": COMMON_LIST_CHECK_PROPERTIES,
        "employee_id": UUID_CHECK_PROPERTIES
    }

    ADD_APPROVAL_TYPE_INPUT_CONFIG = {
        'name': COMMON_STRING_CHECK_PROPERTIES,
        'description': COMMON_STRING_CHECK_PROPERTIES
    }

    WORKFLOW_INPUT_CONFIG = {
        "approval_type":UUID_CHECK_PROPERTIES,
        "role":UUID_CHECK_PROPERTIES,
        "sequence_order":COMMON_DIGITS_CHECK_PROPERTIES
    }

    BLOG_POST_INPUT_CONFIG = {
        "title": COMMON_STRING_CHECK_PROPERTIES,
        "content": COMMON_STRING_CHECK_PROPERTIES,
        "categories": COMMON_LIST_CHECK_PROPERTIES,
        "tags": COMMON_LIST_CHECK_PROPERTIES,
        "imagePath": COMMON_STRING_CHECK_PROPERTIES
    }
    
    BLOG_POST_TAG_INPUT_CONFIG = {
        "name":COMMON_STRING_CHECK_PROPERTIES,
        "description":COMMON_STRING_CHECK_PROPERTIES
    }

    COMPANY_LOCATION_INPUT_CONFIG ={
        "site_name": COMMON_STRING_CHECK_PROPERTIES,
        "location": COMMON_STRING_CHECK_PROPERTIES,
        "latitude": COMMON_STRING_CHECK_PROPERTIES,
        "longitude": COMMON_STRING_CHECK_PROPERTIES
    }

    EMPLOYEE_REGISTRATION_INPUT_CONFIG = {
        "first_name": COMMON_STRING_CHECK_PROPERTIES,
        "last_name": COMMON_STRING_CHECK_PROPERTIES,
        "nid": {
            "required": True,
            "error_message": "NID is required (16 digits)",
            "validate": staticmethod(lambda x: str(x).isdigit() and len(x) == 16)
        },
        "rssb_number": COMMON_STRING_CHECK_PROPERTIES,
        "bank_name": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "bank_account": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "branch_name": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "account_name": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "birth_date": {
            "required": False,
            "error_message": "Birth date is required",
            "validate": staticmethod(lambda x: isinstance(x, str) and '-' in x and len(x.strip()) > 0)
        },
        "marital_status": {
            "required": True,
            "error_message": "Marital status is required",
            "validate": staticmethod(lambda x: x in ["married", "single"])
        },
        "gender": {
            "required": True,
            "error_message": "Gender is required",
            "validate": staticmethod(lambda x: x in ["male", "female"])
        },
        'company_tin': {
            'required': True,
            'error_message': "Company TIN must be 9 digits",
            'validate': staticmethod(lambda x: x.isdigit() and len(x) == 9)
        },
        "employee_type": {
            "required": True,
            "error_message": "Employee type is required",
            "validate": staticmethod(lambda x: x in ["permanent", "consultant", "casual", "second_employee"])
        },
        "department": COMMON_NOT_REQUIRED_DIGITS_CHECK_PROPERTIES,
        "salary_type": {
            "required": True,
            "error_message": "Salary Type is required (net_salary or gross_salary)",
            "validate": staticmethod(lambda x: x in ["net_salary", "gross_salary"])
        },
        "salary_amount": COMMON_FLOAT_CHECK_PROPERTIES,
        "transport_allowance": COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES,
        "housing_allowance": COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES,
        "communication_allowance": COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES,
        "over_time": COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES,
        "other_allowance": COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES,
        'email': {
            'required': True,
            'error_message': "Invalid email address",
            'validate': staticmethod(lambda x: isinstance(x, str) and '@' in x and '.' in x.split('@')[-1])
        },
        'phone_number': {
            'required': True,
            'error_message': "Phone number must be 10 digits",
            'validate': staticmethod(lambda x: x.isdigit() and len(x) == 10)
        },
        "job_title": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "hire_date": {
            "required": False,
            "error_message": "Hire date is required",
            "validate": staticmethod(lambda x: isinstance(x, str) and '-' in x and len(x.strip()) > 0)
        },
        "annual_leave_balance": COMMON_NOT_REQUIRED_FLOAT_CHECK_PROPERTIES,
        "extra_leave_days": COMMON_NOT_REQUIRED_DIGITS_CHECK_PROPERTIES
    }

    DEDUCTION_INPUT_CONFIG = {
        "employee_id": UUID_CHECK_PROPERTIES,
        "description": COMMON_STRING_CHECK_PROPERTIES,
        "amount": COMMON_FLOAT_CHECK_PROPERTIES,
        "deduction_date": {
            "required": True,
            "error_message": lambda field: f"{field} is required", # To make key appear in 'is required' error
            "validate": staticmethod(lambda x: isinstance(x, str) and '-' in x and len(x.strip()) > 0)
        }
    }
    
    UPDATE_DEDUCTION_INPUT_CONFIG = {
        "description": COMMON_STRING_CHECK_PROPERTIES,
        "amount": COMMON_FLOAT_CHECK_PROPERTIES,
        "deduction_date": {
            "required": True,
            "error_message": lambda field: f"{field} is required", # To make key appear in 'is required' error
            "validate": staticmethod(lambda x: isinstance(x, str) and '-' in x and len(x.strip()) > 0)
        }
    }

    ADD_INSURANCE_INPUT_CONFIG ={
        "insurance_name": COMMON_STRING_CHECK_PROPERTIES,
        "employee_rate": COMMON_FLOAT_CHECK_PROPERTIES,
        "employer_rate": COMMON_FLOAT_CHECK_PROPERTIES
    }

    ADD_PLAN_INPUT_CONFIG = {
        "plan_name": COMMON_STRING_CHECK_PROPERTIES,
        "description": COMMON_STRING_CHECK_PROPERTIES,
        "price": COMMON_FLOAT_CHECK_PROPERTIES,
        "num_of_employees": COMMON_DIGITS_CHECK_PROPERTIES,
        "price_per_employee": COMMON_FLOAT_CHECK_PROPERTIES
    }

    ADD_REIMBURSEMENT_INPUT_CONFIG = {
        "employee_id": UUID_CHECK_PROPERTIES,
        "description": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES,
        "reimbursement_amount": COMMON_FLOAT_CHECK_PROPERTIES,
        "reimbursement_date": COMMON_DATE_CHECK_PROPERTIES
    }

    ADD_ROUTE_PLAN_REQUIREMENT_INPUT_CONFIG = {
        "route_path": {
            "required": True,
            "error_message": lambda field: f"{field} is required", # To make key appear in 'is required' error
            "validate": staticmethod(lambda x: isinstance(x, str) and str(x).startswith('/'))
        },
        "required_plan_id": UUID_CHECK_PROPERTIES,
        "description": COMMON_NOT_REQUIRED_STRING_CHECK_PROPERTIES
    }

    ADD_RSSB_CONTRIBUTIONS_INPUT_CONFIG = {
        "contribution_name": COMMON_STRING_CHECK_PROPERTIES,
        "employee_rate": COMMON_FLOAT_CHECK_PROPERTIES,
        "employer_rate": COMMON_FLOAT_CHECK_PROPERTIES,
        "start_date": COMMON_DATE_CHECK_PROPERTIES,
        "end_date": COMMON_NOT_REQUIRED_DATE_CHECK_PROPERTIES
    }

    CREATE_SHIFT_INPUT_CONFIG = {
        "name": COMMON_STRING_CHECK_PROPERTIES,
        "start_time": TIME_CHECK_PROPERTIES,
        "end_time": TIME_CHECK_PROPERTIES,
        "auto_clock_out_hours": COMMON_FLOAT_CHECK_PROPERTIES
    }

    ADD_BRACKET_INPUT_CONFIG = {
        "lower_bound": COMMON_FLOAT_CHECK_PROPERTIES,
        "upper_bound": COMMON_FLOAT_CHECK_PROPERTIES,
        "rate": COMMON_DIGITS_CHECK_PROPERTIES
    }

    CREATE_QUICKBOOKS_ACCOUNT_INPUT_CONFIG={
        "name": COMMON_STRING_CHECK_PROPERTIES,
        "description": COMMON_STRING_CHECK_PROPERTIES,
        "account_type": COMMON_STRING_CHECK_PROPERTIES,
        "account_subtype": COMMON_STRING_CHECK_PROPERTIES
    }

    QUICKBOOKS_WEBHOOK_INPUT_CONFIG = {
        'code': COMMON_STRING_CHECK_PROPERTIES,
        'state': COMMON_STRING_CHECK_PROPERTIES,
        'error': COMMON_STRING_CHECK_PROPERTIES,
        'realmId': COMMON_STRING_CHECK_PROPERTIES
    }

    config_input_type_mapper = {
        'user_registration': USER_REGISTER_INPUT_CONFIG,
        'user_login': USER_LOGIN_CONFIG,
        'user_login_otp': USER_LOGIN_OTP_CONFIG,
        'user_reset_password': USER_RESET_PASSWORD_CONFIG,
        'company_registration': COMPANY_REGISTER_INPUT_CONFIG,
        'leave_approval': LEAVE_APPROVAL_INPUT_CONFIG,
        'add_advance': ADD_ADVANCE_INPUT_CONFIG,
        'add_approval_type': ADD_APPROVAL_TYPE_INPUT_CONFIG,
        'create_update_workflow': WORKFLOW_INPUT_CONFIG,
        'blog_post': BLOG_POST_INPUT_CONFIG,
        'blog_tag': BLOG_POST_TAG_INPUT_CONFIG,
        'company_location': COMPANY_LOCATION_INPUT_CONFIG,
        'register_employee': EMPLOYEE_REGISTRATION_INPUT_CONFIG,
        'deduction': DEDUCTION_INPUT_CONFIG,
        'update_deduction': UPDATE_DEDUCTION_INPUT_CONFIG,
        'add_insurance': ADD_INSURANCE_INPUT_CONFIG,
        'add_plan': ADD_PLAN_INPUT_CONFIG,
        'add_reimbursement': ADD_REIMBURSEMENT_INPUT_CONFIG,
        'add_route_plan_requirement': ADD_ROUTE_PLAN_REQUIREMENT_INPUT_CONFIG,
        'add_rssb_contributions': ADD_RSSB_CONTRIBUTIONS_INPUT_CONFIG,
        'create_shift': CREATE_SHIFT_INPUT_CONFIG,
        'add_tax_bracket': ADD_BRACKET_INPUT_CONFIG,
        'create_qb_account': CREATE_QUICKBOOKS_ACCOUNT_INPUT_CONFIG,
        'quickbooks_webhook': QUICKBOOKS_WEBHOOK_INPUT_CONFIG
    }
    
    @classmethod
    def validate(cls, data, input_type):
        """
        Validates the given data against predefined configurations.

        :param data: A dictionary of key-value pairs where keys are field names and values are input values
        :return: A dictionary of errors where keys are field names and values are error messages
        """

        if not cls.is_input_type_in_config(input_type):
            current_app.logger.error(f"Input type '{input_type}' is not recognized.")
            return False, {"error": f"Input type '{input_type}' is not recognized."}
        
        errors = {}
        for field, config in cls.config_input_type_mapper.get(input_type, {}).items():
            value = data.get(field, "").strip() if isinstance(data.get(field), str) else data.get(field)

            # Check for unexpected keys
            config_keys = set(cls.config_input_type_mapper[input_type].keys())
            invalid_keys = [k for k in data.keys() if k not in config_keys]
            if invalid_keys:
                errors['invalid_keys'] = f"Invalid field(s) for '{input_type}': {', '.join(invalid_keys)}"
                break
            
            # Update error, skip if value is not present and field is config['required']
            if config.get('required') and not value:
                errors[field] = f"{field.replace('_', ' ').capitalize()} is required"
                continue
            
            # Skip if value is not present and config['required'] is False
            if not value and not config.get('required'):
                continue

            # If input value is present, validate it against 'validate' function from configs
            # config['validate'](value) is a callable function that takes input value and returns True or False
            if 'validate' in config and not config['validate'](value):
                if callable(config['error_message']):
                    errors[field] = config['error_message'](field.replace('_', ' ').capitalize()) # That `lambda x` function
                else:
                    errors[field] = config['error_message']
                
                # if data.get(field)
                if errors.get('info'):
                    errors['info'][f"data-{field}"] = data[field]
                else:
                    errors['info'] = {f"data-{field}": data[field]}
        return (False, errors) if errors.keys() else (True, None)


    @staticmethod
    def is_input_type_in_config(input_type):
        """
        Checks if the input type is in the configuration mapper.

        :return: True if input type is in the configuration mapper, False otherwise
        """
        return input_type in UserInputValidator.config_input_type_mapper.keys()