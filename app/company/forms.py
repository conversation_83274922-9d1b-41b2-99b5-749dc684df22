from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, IntegerField, DecimalField, EmailField, SelectField, FileField
from wtforms.validators import DataRequired, Optional, NumberRange, ValidationError
from flask import flash
import uuid

def validate_company_tin(form, field):
    # Ensure that the field contains only digits and is exactly 9 characters long
    if not field.data.isdigit() or len(field.data) != 9:
        flash('TIN must be exactly 9 digits long and contain only numbers.', 'danger')
        raise ValidationError('TIN must be exactly 9 digits long and contain only numbers.')

def validate_phone(form, field):
    # Ensure that the field contains only digits and is exactly 10 characters long
    if not field.data.isdigit() or len(field.data) != 10:
        flash('Phone number must be exactly 10 digits long and contain only numbers.', 'danger')
        raise ValidationError('Phone number must be exactly 10 digits long and contain only numbers.')

class CompanyRegistrationForm(FlaskForm):
    company_name = StringField('Company Name', validators=[DataRequired()])
    company_tin = StringField('Company TIN', validators=[DataRequired(), validate_company_tin])
    rssb_number = StringField('RSSB Number', validators=[DataRequired()])
    phone_number = StringField('Phone Number', validators=[DataRequired(), validate_phone])
    email = EmailField('Email', validators=[DataRequired()])
    number_of_employees = IntegerField('Number of Employees', validators=[DataRequired()])
    company_type = SelectField('Company Type', choices=[('public', 'Public'), ('private', 'Private'), ('non-profit', 'Non-Profit')])
    country = StringField('Country', validators=[DataRequired()], default='Rwanda')
    province = StringField('province', validators=[DataRequired()])
    district = StringField('district', validators=[DataRequired()])
    sector = StringField('sector', validators=[DataRequired()])
    cell = StringField('cell', validators=[DataRequired()])
    village = StringField('village', validators=[DataRequired()])
    plan_id = SelectField('Select Plan', validators=[DataRequired()])
    submit = SubmitField('Register')

class CompanyProfileForm(FlaskForm):
    company_name = StringField('Company Name', validators=[DataRequired()])
    company_tin = StringField('Company TIN', validators=[DataRequired(), validate_company_tin])
    rssb_number = StringField('RSSB Number', validators=[DataRequired()])
    phone_number = StringField('Phone Number', validators=[DataRequired(), validate_phone])
    email = EmailField('Email', validators=[DataRequired()])
    number_employee = IntegerField('Number of Employees', validators=[DataRequired()])
    company_type = SelectField('Company Type', choices=[('public', 'Public'), ('private', 'Private')])
    country = StringField('Country', validators=[DataRequired()], default='Rwanda')
    province = StringField('province', validators=[DataRequired()])
    district = StringField('district', validators=[DataRequired()])
    sector = StringField('sector', validators=[DataRequired()])
    cell = StringField('cell', validators=[DataRequired()])
    village = StringField('village', validators=[DataRequired()])
    initial_qualification_period =IntegerField("Leave Qualification Period", validators=[Optional()])
    increment_policy = IntegerField("Increment Policy", validators=[Optional()])
    rama_applicable = SelectField('RAMA Applicable', choices=[('no', 'No'), ('yes', 'Yes')], validators=[Optional()])
    submit = SubmitField('Update')

class UploadLogoForm(FlaskForm):
    logo = FileField('Logo', validators=[DataRequired()])
    submit = SubmitField('Upload')

class UpdateCompanyProfileForm(FlaskForm):
    company_name = StringField('Company Name', validators=[DataRequired()])
    company_tin = StringField('Company TIN', validators=[DataRequired(), validate_company_tin])
    rssb_number = StringField('RSSB Number', validators=[DataRequired()])
    phone_number = StringField('Phone Number', validators=[DataRequired(), validate_phone])
    email = EmailField('Email', validators=[DataRequired()])
    number_employee = IntegerField('Number of Employees', validators=[DataRequired()])
    face_api_key = StringField('Face API Key')
    company_type = SelectField('Company Type', choices=[('public', 'Public'), ('private', 'Private')])
    plan_id = SelectField('Assign Plan', validators=[DataRequired()])
    submit = SubmitField('Update')

class CompanyDeleteForm(FlaskForm):
    confirm = StringField('Type "DELETE" to confirm', validators=[DataRequired()])
    company_id = StringField('Company ID', default=str(uuid.uuid4()), validators=[DataRequired()])

    submit = SubmitField('Delete Company')

    def validate_confirm(self, field):
        if field.data != 'DELETE':
            flash('You must type "DELETE" to confirm.', 'danger')
            raise ValidationError('You must type "DELETE" to confirm.')





