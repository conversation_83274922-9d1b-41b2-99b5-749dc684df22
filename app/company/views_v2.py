# app.company.views.py
from flask import Blueprint,jsonify, render_template, redirect, url_for, flash, request, session, current_app
from app import db
from .forms import CompanyRegistrationForm, CompanyProfileForm, UploadLogoForm, UpdateCompanyProfileForm
from app.models.central import Company, User, Plans
import logging
from app.automations.create_dbdynamically import create_database
from app.decorators.hr_decorator import hr_required
from app.decorators.admin_decorator import admin_required
from app.helpers.company_helpers import CompanyHelpers
import uuid
import os
import json
from app.decorators.role_decorator import role_required
from app.helpers.auxillary import Auxillary
from datetime import datetime, timedelta, timezone


company_data_bp_v2 = Blueprint('company_data_v2', __name__)

@company_data_bp_v2.route('/v2/check_company_exists', methods=['POST'])
def check_company_exists():
    """Check if a company exists"""
    data = request.json
    company_name = data.get('company_name')
    company_tin = data.get('company_tin')
    email = data.get('email')
    rssb_number = data.get('rssb_number')
    phone_number = data.get('phone_number')

    issues = CompanyHelpers.check_company_exists(company_name, company_tin, email, rssb_number, phone_number)
    if issues:
        return redirect(url_for('company_data_v2.register'))
    else:
        return redirect(url_for('company_data_v2.register'))


@company_data_bp_v2.route('/v2/register_company', methods=['GET', 'POST'])
#@hr_required
def register():
    current_app.logger.info('Registering company route accessed')
    form = CompanyRegistrationForm()
    try:
        # Load JSON data
        with open('app/company/rwanda.json') as f:
            rwanda_data = json.load(f)
        current_app.logger.info('Json loaded successfully')
    except Exception as e:
        current_app.logger.error(f"Error loading json: {str(e)}")
        flash("Json load failed.")
        return redirect(url_for('user_data.login_user'))

    try:
        # Retrieve all plans
        plans = Plans.get_plans()
        form.plan_id.choices = [(plan['plan_id'], plan['plan_name']) for plan in plans]
    except Exception as e:
        current_app.logger.error(f"Error retrieving plans: {str(e)}")
        plans = []

    if form.validate_on_submit():
        # Randomly generate the company alias
        random_part = str(uuid.uuid4())[:4]
        company_alias = f"{form.company_tin.data.strip()}_{random_part}"
        current_app.logger.info(f"Company alias: {company_alias}")
        new_company = Company(
            company_name=form.company_name.data.strip(),
            company_tin=form.company_tin.data.strip(),
            company_alias=company_alias,
            database_name=f"{company_alias}_db",
            rssb_number=form.rssb_number.data.strip(),
            phone_number=form.phone_number.data.strip(),
            email=form.email.data.strip(),
            number_employee=form.number_of_employees.data,
            company_type=form.company_type.data,
            country=form.country.data,
            province=form.province.data,
            district=form.district.data,
            sector=form.sector.data,
            cell=form.cell.data,
            village=form.village.data,
            plan_id = form.plan_id.data,
            trial_until=datetime.now(timezone.utc) + timedelta(days=30)
        )
        try:
            try:
                # Check if the tin number, email, company alias, rssb number, phonee number already exists
                if CompanyHelpers.check_company_exists(new_company.company_tin, new_company.email, new_company.company_alias, new_company.rssb_number, new_company.phone_number):
                    flash('Company already exists', 'danger')
                    return redirect(url_for('company_data_v2.register'))
            except Exception as e:
                current_app.logger.error(f"An error occurred while checking company existence: {str(e)}")
                flash('Error checking company existence', 'danger')
                return redirect(url_for('company_data_v2.register'))

            try:
                # Register the company
                CompanyHelpers.register_company(new_company)
                current_app.logger.info('Company registered successfully')
                flash('Company registered successfully', 'success')
            except Exception as e:
                current_app.logger.error(f"An error occurred: {str(e)}")
                flash('Error registering company', 'danger')
                return redirect(url_for('company_data_v2.register'))
            username = session.get('username')
            user = User.query.filter_by(username=username).first()
            created_by = user.first_name + ' ' + user.last_name
            created_company = Company.query.filter_by(company_alias=company_alias).first() # Get the company that was just created
            """Check if the user is already registered to a company"""
            if user:
                # Check if the user is already associated with the company via the relationship
                if created_company in user.companies:
                    flash('User is already associated with this company', 'info')
                    return redirect(url_for('company_data_v2.register'))
                else:
                    # Associate the user with the company
                    user.companies.append(created_company)
                try:
                    # Create a new database for the company
                    create_database(created_company.database_name)
                except Exception as e:
                    current_app.logger.error(f"An error occurred: {str(e)}")
                    flash('Error creating database', 'danger')
                    return redirect(url_for('company_data_v2.register'))
                try:
                    db.session.commit()
                    current_app.logger.info('User registered to company successfully')
                    # send email to the system admins so that they can know the company details
                    message = f"""
                    A new company has been registered by {created_by} with the following details:
                    Company Name: {created_company.company_name},
                    Company TIN: {created_company.company_tin},
                    Phone Number: {created_company.phone_number},
                    Email: {created_company.email},
                    Number of Employees: {created_company.number_employee},
                    plan: {created_company.plan.plan_name},
                    Province: {created_company.province}, District: {created_company.district}, sector: {created_company.sector}, cell: {created_company.cell}, Village: {created_company.village}
                    Please plan accordingly.
                    """
                    subject = 'New Company Registration'
                    receipients = ['<EMAIL>','<EMAIL>','<EMAIL>']
                    try:
                        sent = Auxillary.send_netpipo_email(subject, receipients, message)
                        message = f"Email sent successfully to {receipients}!"
                        current_app.logger.info(message)
                        message2 = f"sent response: {sent}"
                        current_app.logger.info(message2)
                    except Exception as e:
                        current_app.logger.error(f"Error sending email: {e}")
                    return redirect(url_for('admin_data.dashboard'))
                except Exception as e:
                    flash('An error occurred', 'danger')
                    current_app.logger.error(f"an error occured: {str(e)}")
                    return redirect(url_for('user_data.login'))
            else:
                current_app.logger.error('User not found')
                flash('User not found', 'danger')
                print("User not found")
                return redirect(url_for('user_data.login'))
        except Exception as e:
            flash('An error occurred', 'danger')
            current_app.logger.error(f"An error occurred: {str(e)}")
            return redirect(url_for('user_data.login'))

    else:
        # get the form data
        data = request.form
        current_app.logger.info(f"Form data: {data}")
        current_app.logger.error(form.errors)
    try:
        current_app.logger.info('Rendering template to register company')
        return render_template('company/register_v2.html', form=form, rwanda_data=rwanda_data)
    except Exception as e:
        flash('Error rendering template', 'danger')
        current_app.logger.error(f"Error rendering template: {str(e)}")
        return redirect(url_for('user_data.login'))


@company_data_bp_v2.route('/v2/upload_company_logo', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def upload_company_logo():
    """Upload company logo to DigitalOcean Spaces"""

    form = UploadLogoForm()
    company_id = session.get('company_id')
    current_app.logger.info(f"Accessing upload_company_logo route for company ID: {company_id}")

    if request.method == 'POST':
        logo = request.files['logo']
        if logo.filename == '':
            message = 'No selected file'
            current_app.logger.error(message)
            flash(message, 'danger')
            return redirect(url_for('company_data_v2.upload_company_logo'))
        elif CompanyHelpers.allowed_file(logo.filename):
            try:
                # Upload the logo to DigitalOcean Spaces
                current_app.logger.info(f"Uploading logo for company ID: {company_id}")
                result = Company.upload_company_logo(company_id, logo)

                if result['success']:
                    message = result['message']
                    current_app.logger.info(f"{message} - URL: {result.get('url')}")
                    flash(message, 'success')
                    return redirect(url_for('company_data_v2.upload_company_logo'))
                else:
                    message = result['message']
                    current_app.logger.error(message)
                    flash(message, 'danger')
                    return redirect(url_for('company_data_v2.upload_company_logo'))
            except Exception as e:
                current_app.logger.error(f"Error in upload_company_logo route: {str(e)}")
                message = 'Error uploading logo'
                flash(message, 'danger')
                return redirect(url_for('company_data_v2.upload_company_logo'))
        else:
            message = "Invalid file format. Allowed formats are: jpg, jpeg, png, gif, tif"
            current_app.logger.warning(message)
            flash(message, 'danger')
            return redirect(url_for('company_data_v2.upload_company_logo'))
    try:
        current_app.logger.info("Rendering upload_logo.html template")
        return render_template('company/upload_logo_v2.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error rendering template: {str(e)}")
        message = 'Error loading logo upload page'
        flash(message, 'danger')
        return redirect(url_for('admin_data.dashboard'))

@company_data_bp_v2.route('/v2/edit_company_profile', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr'])
def edit_company_profile():
    # get user role
    company_id = session.get('company_id')
    company = Company.query.get(company_id)
    form = CompanyProfileForm(obj=company)
    form.initial_qualification_period.data = company.leave_data.get("initial_qualification_period").get("value") \
        if isinstance(company.leave_data, dict) else 0
    form.increment_policy.data = company.leave_data.get("increment_policy").get("after_years") \
        if isinstance(company.leave_data, dict) else 0   
     
    # ✅ Set RAMA field explicitly
    form.rama_applicable.data = 'yes' if company.rama_applicable else 'no'
    
    print("Testing Data: ", company.leave_data)
    
    try:
        # Load JSON data
        with open('app/company/rwanda.json') as f:
            rwanda_data = json.load(f)
    except Exception as e:
        current_app.logger.error(f"Error loading json: {str(e)}")
        flash("Json load failed.")
        return redirect(url_for('user_data.login_user'))
    if request.method == 'POST':
        if not form.validate_on_submit():
            # show the validation errors raised
            for field, errors in form.errors.items():
                for error in errors:
                    flash(f'{field}: {error}', 'danger')
            message = 'Validation error'
            current_app.logger.error(message)
            flash("Validation error", 'danger')
            return redirect(url_for('company_data_v2.edit_company_profile'))


        try:
            data = request.form
            current_app.logger.info(f"Form data: {data}")
            company_name = data.get('company_name').strip()
            company_tin = data.get('company_tin').strip()
            rssb_number = data.get('rssb_number').strip()
            phone_number = data.get('phone_number').strip()
            email = data.get('email').strip()
            number_employee = data.get('number_employee').strip()
            company_type = data.get('company_type').strip()
            country = data.get('country')
            province = data.get('province')
            district = data.get('district')
            sector = data.get('sector')
            cell = data.get('cell')
            village = data.get('village')
            rama_applicable = data.get('rama_applicable')
            if rama_applicable == None:
                rama = False
            elif rama_applicable == 'yes':
                rama = True
            else:
                rama = False
            initial_qualification_period = int(data.get('initial_qualification_period') or 0)
            increment_policy = int(data.get('increment_policy') or 0),
        

        except Exception as e:
            message = 'Error getting form data'
            flash(message, 'danger')
            current_app.logger.error(f"Error getting form data: {str(e)}")
            return redirect(url_for('company_data_v2.edit_company_profile'))
        try:
            result = Company.update_company(
                company_id, company_name, company_tin,
                rssb_number, phone_number, email,
                number_employee, company_type, country,
                province, district, sector, cell, village,
                initial_qualification_period,
                increment_policy, rama
            )
            if result:
                current_app.logger.info(f"Company updated successfully: {result}")
                message = 'Company updated successfully'
                flash(message, 'success')
                current_app.logger.info(message)
                return redirect(url_for('company_data_v2.edit_company_profile'))
            else:
                message = 'Error updating company'
                flash(message, 'danger')
                current_app.logger.error(message)
                return redirect(url_for('company_data_v2.edit_company_profile'))
        except Exception as e:
            message = 'Error updating company'
            flash(message, 'danger')
            current_app.logger.error(f"Error updating company: {str(e)}")
            return redirect(url_for('company_data_v2.edit_company_profile'))
    try:
        current_app.logger.info('Rendering template')
        return render_template('company/edit_company_profile_v2.html', form=form, rwanda_data=rwanda_data)
    except Exception as e:
        message = 'Error rendering template'
        flash(message, 'danger')
        current_app.logger.error(f"Error rendering template: {str(e)}")
        return redirect(url_for('admin_data.dashboard'))


@company_data_bp_v2.route('/v2/switch_company', methods=['GET', 'POST'])
@role_required(['hr', 'admin', 'company_hr', 'manager', 'accountant'])
def switch_company():
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))

    # Fetch the companies for the user
    companies = Company.get_companies_for_user(user_id)
    current_app.logger.info(f"Companies: {companies}")

    if request.method == 'POST':
        # Get the selected company ID from the form
        selected_company_id = request.form.get('company_id')
        current_app.logger.info(f"Selected company ID: {selected_company_id}")
        if selected_company_id:
            # Switch to the selected company database
            selected_company = Company.query.get(selected_company_id)
            session['company_id'] = selected_company.company_id
            session['company_name'] = selected_company.company_name
            session['company_alias'] = selected_company.company_alias
            session['database_name'] = selected_company.database_name
            # Add the company logo to the session
            session['company_logo'] = selected_company.logo
            # Add the company plan to the session
            session['company_plan'] = selected_company.plan.plan_name
            session['company_plan_id'] = selected_company.plan.plan_id
            session['plan_price'] = selected_company.plan.price
            session['company'] = selected_company
            session['has_access'] = selected_company.to_dict().get('has_access', False)

            current_app.logger.info(f"Company switched successfully to: {selected_company.company_name}")
            current_app.logger.info(f"Company plan: {selected_company.plan.plan_name}")
            current_app.logger.info(f"Company plan id: {selected_company.plan.plan_id}")
            current_app.logger.info(f"Company plan price: {selected_company.plan.price}")
            current_app.logger.info(f"Company has access: {selected_company.to_dict().get('has_access', False)}")
            try:
                # Redirect to the HR dashboard
                flash('Company switched successfully', 'success')
                return redirect(url_for('admin_data.dashboard'))
            except Exception as e:
                flash('An error occurred', 'danger')
                current_app.logger.error(f"An error occurred: {str(e)}")
                return redirect(url_for('user_data.login'))

    return render_template('company/switch_dashboard_v2.html', companies=companies)

@company_data_bp_v2.route('/v2/delete_company', methods=['POST', 'GET'])
@admin_required
def delete_company():
    """Delete a company"""
    current_app.logger.info('Accessing delete_company route in v2')
    if request.method == 'GET':
        #get a list of all companies
        companies = Company.get_companies()
        return render_template('company/delete_company_v2.html', companies=companies)
    company_id = request.form.get('company_id')
    current_app.logger.info(f"Deleting company with ID: {company_id}")
    if not company_id:
        flash('Company ID is required', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    try:
        # Delete the company
        result = Company.delete_company_and_database(company_id)
        current_app.logger.info(f"Result of delete operation: {result}")
        flash(result['message'], 'success' if result['message'] else 'danger')
        return redirect(url_for('admin_data.admin_dashboard'))
    except Exception as e:
        flash(f'Error deleting company: {str(e)}', 'danger')
        current_app.logger.error(f"Error deleting company: {str(e)}")
        return redirect(url_for('admin_data.admin_dashboard'))

@company_data_bp_v2.route('/v2/company_location', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def company_location():
    """Get company location"""
    if request.method == 'POST':
        # get the location data from the form
        longitude = request.form.get('longitude')
        latitude = request.form.get('latitude')
        current_app.logger.info(f"Longitude: {longitude}, Latitude: {latitude}")
        try:
            # Update the company location
            company_id = session.get('company_id')
            result = Company.add_company_location(company_id, latitude, longitude)
            current_app.logger.info(f"Result after adding location: {result}")
            if result:
                message = 'Location added successfully'
                flash(message, 'success')
                current_app.logger.info(message)
                return redirect(url_for('company_data_v2.company_location'))
            else:
                message = 'Error adding location'
                flash(message, 'danger')
                current_app.logger.error(message)
                return jsonify({'success': False, 'message': message}), 400
        except Exception as e:
            message = 'Error adding location'
            flash(message, 'danger')
            current_app.logger.error(f"Error adding location: {str(e)}")
            return jsonify({'success': False, 'message': message}), 400
    return render_template('company/location.html' )