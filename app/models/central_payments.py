import uuid
from app import db
from sqlalchemy.dialects.postgresql import UUID
from app.models.central import User

class Payments(db.Model):
    """A model to represent payments made by users."""
    __tablename__ = 'payments'
    payment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    company_id = db.Column(UUID(as_uuid=True), db.ForeignKey('companies.company_id', ondelete='SET NULL'), nullable=True)
    amount = db.Column(db.Numeric(precision=12, scale=2), nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)
    transaction_id = db.Column(db.String(50), nullable=False)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.user_id', ondelete='SET NULL'), nullable=True)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp())

    user = db.relationship('User', backref=db.backref('payments'))
    company = db.relationship('Company', backref=db.backref('payments'))
    

    def __repr__(self):
        return f"Payments('{self.amount}', '{self.payment_method}')"

    def to_dict(self):
        # Get the name of the person who made the payment
        paid_by = self.user.to_dict()['full_name'].upper() if self.user else None

        # Format the dates in dd/mm/yyyy: HH:MM:SS
        created_at = self.created_at.strftime('%d/%m/%Y: %H:%M:%S') if self.created_at else None
        updated_at = self.updated_at.strftime('%d/%m/%Y: %H:%M:%S') if self.updated_at else None

        # Get company details
        company_data = self.company.to_dict() if self.company else None

        return {
            'payment_id': self.payment_id,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'transaction_id': self.transaction_id,
            'user_id': self.user_id,
            'paid_by': paid_by,
            'company_id': self.company_id,
            'company_name': company_data['company_name'] if company_data else None,
            'company_email': company_data['email'] if company_data else None,
            'paid_at': created_at,
            'updated_at': updated_at
        }


    @classmethod
    def create_payment(cls, **kwargs):
        # Create the payment
        payment = cls(**kwargs)
        db.session.add(payment)
        db.session.commit()
        return payment
    
    @classmethod
    def get_payments(cls):
        payments = cls.query.all()
        converted_payments = [payment.to_dict() for payment in payments]
        return converted_payments
