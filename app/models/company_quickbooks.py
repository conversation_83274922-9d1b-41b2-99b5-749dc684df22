from app.models.company_base import DynamicBase
import uuid
from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Integer, Time
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
import pytz # For time zone handling
from flask import current_app
from flask import flash
from sqlalchemy import and_
from sqlalchemy import Enum
from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import Boolean
from sqlalchemy import Index
from sqlalchemy import func
from app.models.central import User

class QuickbooksAuditLogs(DynamicBase):
    """Quickbooks Audit Logs model class."""
    __tablename__ = "quickbooks_audit_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    action_type = Column(String(128), nullable=False)
    timestamp = Column(DateTime, default=datetime.now)
    operation_status = Column(Enum("Success", "Failure", name="operation_status"), nullable=False)
    request_payload = Column(JSON, nullable=True)
    response_payload = Column(JSON, nullable=True)
    error_message = Column(String(512), nullable=True)
    user_id = Column(UUID(as_uuid=True), nullable=True)



    def __repr__(self):
        """String representation of the object."""
        return f"""
        "id": "{self.id}",
        "action_type": "{self.action_type}",
        "timestamp": "{self.timestamp}",
        "operation_status": "{self.operation_status}",
        "request_payload": "{self.request_payload}",
        "response_payload": "{self.response_payload}",
        "error_message": "{self.error_message}",
        "user_id": "{self.user_id}"
        """
    def to_dict(self):
        """To dictionary method. Converts the object to a dictionary."""
        user_info = None
        if self.user_id:
            user = User.get_user_by_id(self.user_id)
            if user:
                user_info = f"{user.first_name} {user.last_name}"
        return {
            "id": self.id,
            "action_type": self.action_type,
            "timestamp": self.timestamp,
            "operation_status": self.operation_status,
            "request_payload": self.request_payload,
            "response_payload": self.response_payload,
            "error_message": self.error_message,
            "user_id": self.user_id,
            "user": user_info,
        }
    
    @classmethod
    def add_quickbooks_audit_log(cls, session, **kwargs):
        """Add a Quickbooks Audit Log entry to the database."""
        try:
            log_entry = cls(**kwargs)
            session.add(log_entry)
            session.commit()
            return log_entry
        except Exception as e:
            session.rollback()
            flash(f"Error adding Quickbooks Audit Log: {str(e)}", "error")
            return None
    @classmethod
    def get_quickbooks_audit_logs(cls, session, **kwargs):
        """Get Quickbooks Audit Logs from the database."""
        try:
            logs = session.query(cls).filter_by(**kwargs).all()
            return logs
        except Exception as e:
            flash(f"Error retrieving Quickbooks Audit Logs: {str(e)}", "error")
            return None
    @classmethod
    def get_quickbooks_audit_logs_by_user(cls, session, user_id):
        """Get Quickbooks Audit Logs for a specific user."""
        try:
            logs = session.query(cls).filter(cls.user_id == user_id).all()
            return logs
        except Exception as e:
            flash(f"Error retrieving Quickbooks Audit Logs for user {user_id}: {str(e)}", "error")
            return None
    @classmethod
    def get_quickbooks_audit_logs_by_date(cls, session, start_date, end_date):
        """Get Quickbooks Audit Logs within a date range."""
        try:
            logs = session.query(cls).filter(
                cls.timestamp >= start_date,
                cls.timestamp <= end_date
            ).all()
            return logs
        except Exception as e:
            flash(f"Error retrieving Quickbooks Audit Logs between {start_date} and {end_date}: {str(e)}", "error")
            return None
    @classmethod
    def get_quickbooks_audit_logs_by_action_type(cls, session, action_type):
        """Get Quickbooks Audit Logs by action type."""
        try:
            logs = session.query(cls).filter(cls.action_type == action_type).all()
            return logs
        except Exception as e:
            flash(f"Error retrieving Quickbooks Audit Logs for action type {action_type}: {str(e)}", "error")
            return None
    @classmethod
    def get_quickbooks_audit_logs_by_operation_status(cls, session, operation_status):
        """Get Quickbooks Audit Logs by operation status."""
        try:
            logs = session.query(cls).filter(cls.operation_status == operation_status).all()
            return logs
        except Exception as e:
            flash(f"Error retrieving Quickbooks Audit Logs for operation status {operation_status}: {str(e)}", "error")
            return None  
