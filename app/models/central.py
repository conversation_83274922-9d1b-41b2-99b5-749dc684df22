import os
import uuid
import random
import hashlib
import secrets
import boto3
import urllib.parse
from datetime import date, datetime, timezone, timedelta
from decimal import Decimal
from app import db
from flask import abort, flash, session, current_app
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy_json import NestedMutableJson
from sqlalchemy import or_, text
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from werkzeug.security import generate_password_hash, check_password_hash
from app.helpers.auxillary import Auxillary

user_company_association = db.Table(
    'user_company_association',
    db.Column('user_id', UUID(as_uuid=True), db.<PERSON>ey(
        'users.user_id'), primary_key=True),
    db.Column('company_id', UUID(as_uuid=True), db.Foreign<PERSON>ey(
        'companies.company_id'), primary_key=True),
    db.Column('added_at', db.DateTime, server_default=db.func.now())
)

# Association table for agency users and agencies
user_agency_association = db.Table(
    'user_agency_association',
    db.Column('user_id', UUID(as_uuid=True), db.ForeignKey(
        'users.user_id'), primary_key=True),
    db.Column('agency_id', UUID(as_uuid=True), db.ForeignKey(
        'agencies.agency_id'), primary_key=True),
    db.Column('added_at', db.DateTime, server_default=db.func.now())
)

# Association table for agencies and companies they manage
agency_company_association = db.Table(
    'agency_company_association',
    db.Column('agency_id', UUID(as_uuid=True), db.ForeignKey(
        'agencies.agency_id'), primary_key=True),
    db.Column('company_id', UUID(as_uuid=True), db.ForeignKey(
        'companies.company_id'), primary_key=True),
    db.Column('added_at', db.DateTime, server_default=db.func.now())
)


LEAVE_POLICY = {
    "initial_qualification_period": {
        "description": "The initial period an employee must work to qualify for annual leave",
        "value": 1
    },
    "increment_policy": {
        "description": "The number of days to add to annual leave after a certain number of years",
        "after_years": 3
    }
}

class Company(db.Model):
    __tablename__ = 'companies'

    created_at = db.Column(db.DateTime, server_default=db.func.now())
    company_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    plan_id = db.Column(UUID(as_uuid=True), db.ForeignKey(
        'plans.plan_id'), nullable=True)
    company_name = db.Column(db.String(255), nullable=False)
    company_alias = db.Column(db.String(255), unique=True, nullable=False)
    database_name = db.Column(db.String(255), unique=True, nullable=False)
    company_tin = db.Column(db.String(255), nullable=False, unique=True)
    rssb_number = db.Column(db.String(255), nullable=False, unique=True)
    phone_number = db.Column(db.String(255), nullable=False, unique=True)
    email = db.Column(db.String(255), nullable=False, unique=True)
    number_employee = db.Column(db.Integer, nullable=False)
    company_type = db.Column(
        db.Enum(
            'public', 'private', 'non-profit',
            'solepropriator', 'government', 'cooperative',
            name='company_type'
        ),
        nullable=False
    )
    logo = db.Column(db.String(255), nullable=True)
    country = db.Column(db.String(255), nullable=False, default="Rwanda")
    province = db.Column(db.String(255), nullable=True)
    district = db.Column(db.String(255), nullable=True)
    sector = db.Column(db.String(255), nullable=True)
    cell = db.Column(db.String(255), nullable=True)
    village = db.Column(db.String(255), nullable=True)
    trial_until = db.Column(db.DateTime, nullable=True)
    subscription_end_period = db.Column(db.DateTime, nullable=True)
    compreface_api_key = db.Column(db.String(128), unique=True, nullable=True)
    latitude = db.Column(db.Numeric(9, 6), nullable=True)
    longitude = db.Column(db.Numeric(9, 6), nullable=True)
    quickbooks_access_token = db.Column(db.Text, nullable=True)
    quickbooks_refresh_token = db.Column(db.String(255), nullable=True)
    quickbooks_authorization_code = db.Column(db.String(255), nullable=True)
    quickbooks_realm_id = db.Column(db.String(255), nullable=True, unique=True)
    total_annual_leave_days=db.Column(db.Integer, nullable=True)
    leave_data = db.Column(NestedMutableJson, nullable=True, default=LEAVE_POLICY)
    rama_applicable = db.Column(db.Boolean, default=False, nullable=True)

    users = db.relationship(
        'User',
        secondary=user_company_association,
        back_populates='companies',
        lazy=True
    )
    plan = db.relationship('Plans', back_populates='companies')

    # Relationship with agencies
    agencies = db.relationship(
        'Agency',
        secondary=agency_company_association,
        back_populates='companies',
        lazy=True
    )

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        company_id: {self.company_id},
        company_name: {self.company_name},
        company_alias: {self.company_alias},
        database_name: {self.database_name},
        company_tin: {self.company_tin},
        rssb_number: {self.rssb_number},
        phone_number: {self.phone_number},
        email: {self.email},
        country: {self.country},
        province: {self.province},
        district: {self.district},
        sector: {self.sector},
        cell: {self.cell},
        village: {self.village},
        number_employee: {self.number_employee},
        company_type: {self.company_type},
        logo: {self.logo},
        created_at: {self.created_at},
        plan: {self.plan},
        plan_id: {self.plan_id},
        trial_until: {self.trial_until},
        subscription_end_period: {self.subscription_end_period},
        is_on_trial: {self.is_on_trial()},
        is_subscribed: {self.is_subscribed()},
        has_access: {self.has_access()},
        compreface_api_key: {self.compreface_api_key},
        latitude: {self.latitude},
        longitude: {self.longitude}
        """

    def to_dict(self):
        """Convert company object to dictionary."""
        return {
            "company_id": self.company_id,
            "company_name": self.company_name,
            "company_alias": self.company_alias,
            "database_name": self.database_name,
            "company_tin": self.company_tin,
            "rssb_number": self.rssb_number,
            "phone_number": self.phone_number,
            "email": self.email,
            "country": self.country,
            "province": self.province,
            "district": self.district,
            "sector": self.sector,
            "cell": self.cell,
            "village": self.village,
            "number_employee": self.number_employee,
            "company_type": self.company_type,
            'created_at': self.created_at.strftime("%d/%m/%Y %H:%M:%S"),
            "logo": self.logo if self.logo else None,
            "address": f"{self.province}, {self.district}, {self.sector}, {self.cell}",
            "plan": self.plan if self.plan else None,
            "plan_id": self.plan_id if self.plan_id else None,
            "trial_until": self.trial_until.strftime("%d/%m/%Y %H:%M:%S") if self.trial_until else None,
            "subscription_end_period": self.subscription_end_period.strftime("%d/%m/%Y %H:%M:%S") if self.subscription_end_period else None,
            "is_on_trial": self.is_on_trial(),
            "is_subscribed": self.is_subscribed(),
            "has_access": self.has_access(),
            "compreface_api_key": self.compreface_api_key if self.compreface_api_key else None,
            "latitude": self.latitude,
            "longitude": self.longitude,
            "quickbooks_access_token": self.quickbooks_access_token if self.quickbooks_access_token else None,
            "quickbooks_refresh_token": self.quickbooks_refresh_token if self.quickbooks_refresh_token else None,
            "quickbooks_authorization_code": self.quickbooks_authorization_code if self.quickbooks_authorization_code else None,
            "quickbooks_realm_id": self.quickbooks_realm_id if self.quickbooks_realm_id else None,
            "total_annual_leave_days": self.total_annual_leave_days,
            "leave_data": self.leave_data,
            "rama_applicable": self.rama_applicable,
            # Include all associated agencies' information
            "agencies": [{"agency_id": agency.agency_id, "agency_name": agency.agency_name} for agency in self.agencies] if hasattr(self, 'agencies') and self.agencies else []

        }

    @classmethod
    def get_companies(cls):
        """Get all companies from the database."""
        try:
            companies = db.session.query(Company).all()
            return [company.to_dict() for company in companies]
        except Exception as e:
            print("Error getting companies: ", e)
            return []
        
    @classmethod
    def delete_company_and_database(cls, company_id):
        session = db.session
        try:
            company = session.query(cls).filter_by(company_id=company_id).first()
            if not company:
                return {"error": "Company not found"}

            db_name = company.database_name

            users = session.query(User).join(user_company_association).filter(
                user_company_association.c.company_id == company_id
            ).all()

            session.execute(user_company_association.delete().where(
                user_company_association.c.company_id == company_id
            ))

            for user in users:
                if user.company_id == company_id:
                    user.company_id = None

            for user in users:
                remaining = session.query(user_company_association).filter(
                    user_company_association.c.user_id == user.user_id
                ).count()
                if remaining == 0:
                    session.delete(user)

            session.delete(company)
            session.commit()

            try:
                dropping = cls._drop_database(db_name)
                current_app.logger.info(f"Database {db_name} dropped successfully with status: {dropping}")
            except Exception as e:
                current_app.logger.error(f"Error dropping database {db_name}: {str(e)}")
                session.rollback()
                return {"error": f"Failed to drop database: {str(e)}"}

            return {"message": "Company and database deleted successfully."}

        except SQLAlchemyError as e:
            session.rollback()
            return {"error": str(e)}


    @staticmethod
    def _drop_database(db_name):
        """
        Drops a PostgreSQL database after terminating active connections.
        """
        engine = db.engine
        with engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
            conn.execute(text("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = :dbname
            """), {"dbname": db_name})

            conn.execute(text(f'DROP DATABASE IF EXISTS "{db_name}"'))

    def is_on_trial(self):
        """Check if the company is currently on trial."""
        if not self.trial_until:
            return False
        return self.trial_until.replace(tzinfo=timezone.utc) >= datetime.now(timezone.utc)

    def is_subscribed(self):
        """Check if the company has an active subscription."""
        if not self.subscription_end_period:
            return False
        return self.subscription_end_period.replace(tzinfo=timezone.utc) >= datetime.now(timezone.utc)

    def has_access(self):
        """Check if the company has access to the system."""
        return self.is_on_trial() or self.is_subscribed()

    def calculate_monthly_leave_increment(self):
        """
        Calculate the monthly leave increment based on total annual leave days.

        Returns:
            float: The monthly leave increment value.
        """
        if self.total_annual_leave_days is None or self.total_annual_leave_days <= 0:
            return 0
        return self.total_annual_leave_days / 12

    @staticmethod
    def update_subscription_end_period(company_id, num_days):
        """Update the subscription end period for a company."""
        try:
            # Get company by ID
            company = db.session.query(Company).filter_by(
                company_id=company_id).first()
            if not company:
                current_app.logger.error("Company not found")
                return {"error": "Company not found"}, False

            current_end_date = company.subscription_end_period
            current_app.logger.info(
                f"Current end date (raw from DB): {current_end_date} and the type is {type(current_end_date)}")

            # Ensure current_end_date is timezone-aware
            if current_end_date is None:
                current_end_date = datetime.now(timezone.utc)
                current_app.logger.info(
                    f"Subscription was None, setting to current UTC time: {current_end_date}")
            elif current_end_date.tzinfo is None:
                current_end_date = current_end_date.replace(
                    tzinfo=timezone.utc)
                current_app.logger.info(
                    f"Converted current_end_date to UTC-aware: {current_end_date}")

            # Make datetime.now() timezone-aware
            now_utc = datetime.now(timezone.utc)
            current_app.logger.info(f"Current UTC time: {now_utc}")

            # Extend or set new subscription period
            if current_end_date >= now_utc:
                new_end_date = current_end_date + timedelta(days=num_days)
                current_app.logger.info(
                    f"Extended subscription. New end date: {new_end_date}")
            else:
                new_end_date = now_utc + timedelta(days=num_days)
                current_app.logger.info(
                    f"New subscription. New end date: {new_end_date}")

            # Save new subscription period
            try:
                company.subscription_end_period = new_end_date
                db.session.commit()
                current_app.logger.info("Subscription updated successfully")

                # Extract session data
                data = session.get('data', {})
                amount = data.get("amount", "Unknown")
                invoice_number = data.get("invoiceNumber", "N/A")
                payment_items = data.get("paymentItems", [])
                payment_method = "IremboPay API"

                # Extract number of months paid for
                quantity = payment_items[0].get(
                    "quantity", 1) if payment_items else 1

                company_name = session.get('company_name', "Unknown")
                payer = f"{session.get('first_name', '')} {session.get('last_name', '')}".strip(
                )

                # Format the new end date
                try:
                    formatted_end_date = new_end_date.strftime(
                        "%d/%m/%Y %H:%M:%S")
                    current_app.logger.info(
                        f'Formatted new end date: {formatted_end_date}')
                except Exception as e:
                    current_app.logger.error(
                        f"Error formatting new end date: {str(e)}")
                    formatted_end_date = "Not available"

                # Send confirmation email
                email = session.get('email', "")
                subject = "Payment Confirmation"
                body = f"""
                        <h3>Dear {payer},</h3>
                        <p>Your payment has been successfully processed. Below are the payment details:</p>
                        <table>
                            <tr><th>Amount</th><td>{amount}</td></tr>
                            <tr><th>Payment Method</th><td>{payment_method}</td></tr>
                            <tr><th>Transaction ID</th><td>{invoice_number}</td></tr>
                            <tr><th>Company Name</th><td>{company_name}</td></tr>
                            <tr><th>Number of Months</th><td>{quantity}</td></tr>
                            <tr><th>Subscription End Date</th><td>{formatted_end_date}</td></tr>
                        </table>
                """

                try:
                    sent = Auxillary.send_netpipo_email(subject, email, body)
                    current_app.logger.info(f"Email sent: {sent}")
                except Exception as e:
                    current_app.logger.error(f"Error sending email: {e}")

                return {"message": "Subscription updated successfully", "new_end_date": formatted_end_date}, True
            except Exception as e:
                current_app.logger.error(
                    f"Error updating subscription: {str(e)}")
                return {"error": "Error updating subscription"}, False
        except Exception as e:
            current_app.logger.error(f"Unexpected error: {str(e)}")
            return {"error": "Error updating subscription"}, False

    @classmethod
    def get_company_by_id(cls, company_id):
        """Get a company by ID."""
        company = db.session.get(Company, company_id)
        if not company:
            abort(404, description="Company not found")
        return company.to_dict()

    @classmethod
    def update_company_plan(cls, company_id, plan_id):
        """Update the plan for a company."""
        company = db.session.get(Company, company_id)
        company.plan_id = plan_id
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating company plan: {str(e)}")
            return False

    @classmethod
    def get_company_by_tin(cls, tin):
        """Get a company by TIN."""
        company = db.session.query(Company).filter_by(company_tin=tin).first()
        if not company:
            return None
        return company.to_dict()

    @classmethod
    def update_company(
        cls, company_id, company_name, company_tin,
        rssb_number, phone_number, email, number_employee,
        company_type, country, province, district, sector,
        cell, village, initial_qualification_period,
        increment_policy, rama_applicable=False
    ):
        """Update a company."""
        company = db.session.get(Company, company_id)
        if not company:
            abort(404, description="Company not found")

        company.company_name = company_name
        company.company_tin = company_tin
        company.rssb_number = rssb_number
        company.phone_number = phone_number
        company.email = email
        company.number_employee = number_employee
        company.company_type = company_type
        company.country = country
        company.province = province
        company.district = district
        company.sector = sector
        company.cell = cell
        company.village = village
        company.rama_applicable = rama_applicable
        try:
            payload = {
                "initial_qualification_period": { "value": initial_qualification_period },
                "increment_policy": {
                    "description":"The first consideration for incrementing extra_leave_days",
                    "after_years": increment_policy
                }
            }
            is_updated, _ = cls.add_leave_policy(company, payload=payload)

            if is_updated:
                db.session.commit()
                return True
            return False
        except Exception as e:
            db.session.rollback()
            print(f"Error updating company: {str(e)}")
            return False

    @classmethod
    def add_company_location(
            cls, company_id, latitude, longitude):
        """Add a company location."""
        company = db.session.get(Company, company_id)
        if not company:
            abort(404, description="Company not found")
        company.latitude = latitude
        company.longitude = longitude
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error adding company location: {str(e)}")
            return False

    @classmethod
    def edit_company_profile(cls, **kwargs):
        """Edit a company profile."""
        current_app.logger.info(f"Edit company profile: {kwargs}")
        company_id = kwargs.get("company_id")
        company = db.session.get(Company, company_id)
        if not company:
            abort(404, description="Company not found")

        # Update the company attributes
        for key, value in kwargs.items():
            if hasattr(company, key):
                setattr(company, key, value)
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating company: {str(e)}")
            return False

    @classmethod
    def get_companies_for_user(cls, user_id):
        """
        Retrieve companies associated with a specific user.

        :param user_id: UUID of the user.
        :return: List of companies associated with the user.
        """
        return (
            cls.query
            .join(user_company_association)
            .filter(user_company_association.c.user_id == user_id)
            .all()
        )

    @classmethod
    def get_users_for_company(cls, company_id):
        """
        Retrieve users associated with a specific company.

        :param company_id: UUID of the company.
        :return: List of users associated with the company.
        """
        users = (
            User.query
            .join(user_company_association)
            .filter(user_company_association.c.company_id == company_id)
            .all()
        )
        return users

    @classmethod
    def add_company_quickbooks_authentication(
            cls, company_id, quickbooks_access_token, quickbooks_refresh_token, quickbooks_authorization_code):
        """Add QuickBooks authentication details for a company."""
        company = db.session.get(Company, company_id)
        if not company:
            abort(404, description="Company not found")
        company.quickbooks_access_token = quickbooks_access_token
        company.quickbooks_refresh_token = quickbooks_refresh_token
        company.quickbooks_authorization_code = quickbooks_authorization_code
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error adding QuickBooks authentication: {str(e)}")
            return False

    @classmethod
    def update_company_data(cls, company_id, **kwargs):
        """Update company data."""
        from flask import current_app
        current_app.logger.info(f"Updating company data for company ID: {company_id}")
        current_app.logger.info(f"Update parameters: {', '.join(kwargs.keys())}")

        company = db.session.get(Company, company_id)
        if not company:
            current_app.logger.error(f"Company with ID {company_id} not found")
            abort(404, description="Company not found")

        # Log current values before update
        if 'quickbooks_refresh_token' in kwargs:
            current_app.logger.info(f"Current refresh token in DB: {company.quickbooks_refresh_token}")
            current_app.logger.info(f"New refresh token to be set: {kwargs['quickbooks_refresh_token']}")

        # Update the company attributes
        for key, value in kwargs.items():
            if hasattr(company, key):
                current_app.logger.info(f"Setting {key} attribute")
                setattr(company, key, value)
            else:
                current_app.logger.warning(f"Company does not have attribute: {key}")

        try:
            current_app.logger.info("Committing changes to database")
            db.session.commit()

            # Verify the update
            updated_company = db.session.get(Company, company_id)
            if 'quickbooks_refresh_token' in kwargs:
                current_app.logger.info(f"After commit - refresh token in DB: {updated_company.quickbooks_refresh_token}")

            current_app.logger.info("Company data updated successfully")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating company: {str(e)}")
            current_app.logger.error(f"Error details: {str(e)}")
            return False

    @classmethod
    def define_initial_qualification_period(cls, company_id, initial_qualification_period, description):

        """
        Updates the initial qualification period for a class instance.

        Args:
            initial_qualification_period (int): The duration of the initial qualification period in year(s).

        Returns:
            Company Instance: The updated Company instance if successful or  None: If the Company instance is not
            found or the update fails.
        """
        if not initial_qualification_period:
            return None, "Inital qualification period is not present"

        company = db.session.query(Company).get(company_id)
        if not company:
            return None, "Company not found"

        # When it is the first time
        if not company.leave_data:
            company.leave_data = {}

        if not company.leave_data.get("initial_qualification_period"):
            initial_period = company.leave_data["initial_qualification_period"]={}
            initial_period["value"] = initial_qualification_period
            initial_period["description"]=description

        # Updating/Creating
        company.leave_data["initial_qualification_period"]["value"] = initial_qualification_period

        try:
            db.session.add(company)
            db.session.commit()
            return company, "Initial qualification period added/updated"
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating initial qualification period: {str(e)}")
            return None, "Failed to update initial qualification"


    @classmethod
    def add_leave_policy(cls, company, payload: dict):
        """
        Adds or updates a leave policy in the system.

        Args:
            payload: Arbitrary keyword arguments containing the details of the leave policy.
                The keys should match the structure of the LEAVE_POLICY dictionary except initial_qualification_period.
                For example:
                - 'increment_policy' (dict): Contains 'description' (str), 'days_to_add' (int), and 'after_years' (int).
        """

        if not company:
            return False, "Company not found"

        # When it is the first time
        if not company.leave_data:
            company.leave_data = {}

        if not company.leave_data.get("initial_qualification_period"):
            company.leave_data["initial_qualification_period"] = {}

        if not company.leave_data.get("increment_policy"):
            company.leave_data["increment_policy"]= {}

        current_app.logger.info(f"Leave Data: {company.leave_data}")

        try:
            for key, value in payload.items():
                if key in company.leave_data.keys():
                    company.leave_data[key].update(value)
                else:
                    raise ValueError(f"Invalid leave policy key: {key}")
            return True, "Company is set for update"
        except Exception as e:
            raise # Raise the orginal exception


    @classmethod
    def upload_company_logo(cls, company_id, logo_file):
        """
        Upload company logo to DigitalOcean Spaces and update the company record.

        Args:
            company_id (UUID): The ID of the company
            logo_file (FileStorage): The logo file to upload

        Returns:
            dict: A dictionary containing success status, message, and URL if successful
        """
        try:
            current_app.logger.info(f"Uploading logo for company ID: {company_id}")

            # Get the company
            company = db.session.get(Company, company_id)
            if not company:
                message = f"Company with ID {company_id} not found"
                current_app.logger.error(message)
                return {'success': False, 'message': message}

            # Configure S3 client for DigitalOcean Spaces
            s3 = boto3.client(
                's3',
                region_name=os.getenv("DO_SPACE_REGION"),
                endpoint_url=os.getenv("DO_SPACE_ENDPOINT"),
                aws_access_key_id=os.getenv("DO_SPACE_KEY"),
                aws_secret_access_key=os.getenv("DO_SPACE_SECRET")
            )

            # Generate a unique filename to avoid overwriting existing files
            filename = f"{company.company_name}_logo"

            # Define the S3 path
            s3_path = f"company_logos/{company.company_name}_{company.company_id}_{filename}"

            # Upload the file to DigitalOcean Spaces
            s3.upload_fileobj(
                Fileobj=logo_file,
                Bucket=os.getenv("DO_SPACE_BUCKET"),
                Key=s3_path,
                ExtraArgs={'ACL': 'public-read'}
            )

            # Generate the URL for the uploaded file
            file_url = f"{os.getenv('DO_SPACE_ENDPOINT')}/{os.getenv('DO_SPACE_BUCKET')}/{s3_path}"

            # Update the company's logo field with the URL
            company.logo = file_url
            db.session.commit()

            message = "Company logo uploaded successfully"
            current_app.logger.info(f"{message} - URL: {file_url}")
            return {'success': True, 'message': message, 'url': file_url, 'status': 200}

        except Exception as e:
            db.session.rollback()
            message = f"Error uploading company logo: {str(e)}"
            current_app.logger.error(message)
            return {'success': False, 'message': message}


class User(db.Model):
    """Model representing a user."""
    __tablename__ = 'users'

    user_id = db.Column(UUID(as_uuid=True), primary_key=True,
                        default=uuid.uuid4, nullable=False)
    company_id = db.Column(UUID(as_uuid=True), db.ForeignKey(
        'companies.company_id'), nullable=True)
    username = db.Column(db.String(255), unique=True, nullable=False)
    first_name = db.Column(db.String(255), nullable=True)
    last_name = db.Column(db.String(255), nullable=False)
    password = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False)
    phone_number = db.Column(db.String(20), unique=True, nullable=False)
    salt = db.Column(db.String(255), nullable=False)
    is_2fa_enabled = db.Column(db.Boolean, default=False)
    otp_key = db.Column(db.String(255), nullable=True)
    is_active = db.Column(db.Boolean, default=False)
    otp = db.Column(db.String(255), nullable=True)
    otp_expiry = db.Column(db.DateTime, nullable=True)
    is_otp_used = db.Column(db.Boolean, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # Many-to-many relationship with companies
    companies = db.relationship(
        'Company',
        secondary=user_company_association,
        back_populates='users'
    )

    # Many-to-many relationship with agencies
    agencies = db.relationship(
        'Agency',
        secondary=user_agency_association,
        back_populates='users'
    )

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        user_id: {self.user_id},
        company_id: {self.company_id},
        username: {self.username},
        role: {self.role},
        first_name: {self.first_name},
        last_name: {self.last_name},
        email: {self.email},
        phone_number: {self.phone_number},
        created_at: {self.created_at}
        """

    def to_dict(self):
        """Convert user object to dictionary."""
        # Get the name of the company given that we have the company_id as a foregign key in this table
        return {
            "user_id": self.user_id,
            "company_id": self.company_id,
            "username": self.username,
            "role": self.role,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}",
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S"),
            "email": self.email,
            "phone_number": self.phone_number,
            # Include all associated companies' names
            "company_names": [company.company_name for company in self.companies],
            # Include all associated agencies' names
            "agency_names": [agency.agency_name for agency in self.agencies] if hasattr(self, 'agencies') else []
        }

    @staticmethod
    def generate_otp() -> int:
        """Generate a random 6 digit otp"""
        otp = random.randint(100000, 999999)
        return otp

    def create_otp(self) -> int:
        """Create an otp for password reset"""
        otp = self.generate_otp()
        self.otp = generate_password_hash(str(otp))
        self.otp_expiry = datetime.now() + timedelta(minutes=10)
        self.is_otp_used = False
        db.session.commit()
        return otp

    def check_otp(self, otp: int) -> bool:
        """Check if the otp is correct"""
        if self.is_otp_used:
            current_app.logger.error("OTP IS USED")
            self.otp = None
            self.otp_expiry = None
            self.is_otp_used = False
            return False
        matches = check_password_hash(self.otp, str(otp).strip()) # from werkzeug.security
        if matches and self.otp_expiry > datetime.now():
            self.is_otp_used = True
            db.session.commit()
            return True
        else:
            return False

    @staticmethod
    def hash_password(password):
        """Hash the password."""
        # Generate a random salt
        salt = secrets.token_hex(16)
        print("salt", salt)

        # Concatenate salt and password
        salted_password = salt + password
        print("salted_password", salted_password)

        # Hash the password before registering in the database.
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        print("hashed_password", hashed_password)
        return [hashed_password, salt]

    @classmethod
    def register_user(
            cls, username, email, role, password, phone_number,
            first_name, last_name, is_active=False):
        """Register a user."""
        try:
            current_app.logger.info(f"Starting user registration for {username}, {email}, role: {role}")

            # Check if user with same email or username already exists
            existing_email = cls.query.filter_by(email=email).first()
            if existing_email:
                current_app.logger.error(f"User with email {email} already exists")
                return False

            existing_username = cls.query.filter_by(username=username).first()
            if existing_username:
                current_app.logger.error(f"User with username {username} already exists")
                return False

            existing_phone = cls.query.filter_by(phone_number=phone_number).first()
            if existing_phone:
                current_app.logger.error(f"User with phone number {phone_number} already exists")
                return False

            hashed_password, salt = cls.hash_password(password)
            current_app.logger.info(f"Password hashed successfully")

            new_user = User(
                username=username,
                email=email,
                role=role,
                password=hashed_password,
                phone_number=phone_number,
                first_name=first_name,
                last_name=last_name,
                salt=salt,
                is_active=is_active
            )
            current_app.logger.info(f"User object created")

            try:
                db.session.add(new_user)
                current_app.logger.info(f"User added to session")
                db.session.commit()
                current_app.logger.info(f"User committed to database successfully")
                return True
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error committing user to database: {str(e)}")
                return False
        except Exception as e:
            current_app.logger.error(f"Unexpected error in register_user: {str(e)}")
            return False

    @classmethod
    def assign_user_a_company(cls, user_id, company_id):
        """Assign a user to a company."""
        user = db.session.get(User, user_id)
        company = db.session.get(Company, company_id)
        if not user or not company:
            return False
        user.companies.append(company)
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error assigning user to company: {str(e)}")
            return False

    @classmethod
    def remove_company_from_user(cls, user_id, company_id):
        """Remove a user from a company."""
        user = db.session.get(User, user_id)
        company = db.session.get(Company, company_id)
        if not user or not company:
            return False
        user.companies.remove(company)
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error removing user from company: {str(e)}")
            return False

    @classmethod
    def login_user(cls, username, password):
        """Login a user."""
        user = db.session.query(User).filter_by(username=username).first()
        if not user:
            return False
        salted_password = user.salt + password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        if hashed_password == user.password:
            return True
        return False

    @classmethod
    def check_old_password(cls, user_id, old_password):
        """Check the old password of a user."""
        user = db.session.query(User).filter_by(user_id=user_id).first()
        if not user:
            return False
        salted_password = user.salt + old_password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        if hashed_password == user.password:
            return True
        return False

    @classmethod
    def update_user(cls, user_id, username, email, role, password, phone_number):
        """Update a user."""
        user = db.session.get(User, user_id)
        user.username = username
        user.email = email
        user.role = role
        if password:
            user.password = password
        user.phone_number = phone_number

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating user: {str(e)}")
            return False

    @classmethod
    def update_profile(cls, user_id, username, email, phone, first_name, last_name):
        """Update a user profile."""
        user = db.session.get(User, user_id)
        user.username = username
        user.email = email
        user.phone_number = phone
        user.first_name = first_name
        user.last_name = last_name

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating user profile: {str(e)}")
            return False

    @classmethod
    def update_user_role(cls, user_id, role_name):
        "Update user role"
        user = db.session.get(cls, user_id)
        user.role = role_name

        try:
            db.session.commit()
            return user
        except Exception as e:
            db.session.rollback()
            print("Error updating user role: ", str(e))
            return None

    @classmethod
    def get_users(cls):
        """Get all users from the database."""
        try:
            users = db.session.query(User).all()
            return [user.to_dict() for user in users]
        except Exception as e:
            print("Error getting users: ", e)
            return []

    @classmethod
    def get_user_by_id(cls, user_id, abort_on_not_found=True):
        """Get user by ID."""
        user = db.session.get(User, user_id)
        if not user and abort_on_not_found:
            abort(404, description="User not found")

        # convert user to a dictionary
        converted_user = user.to_dict()
        return converted_user

    @staticmethod
    def get_user_by_email(email):
        """Get user by email."""
        user = db.session.query(User).filter_by(email=email).first()
        if not user:
            return None
        return user

    @classmethod
    def get_user_by_their_email(cls, email):
        user = db.session.query(cls).filter_by(email=email).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_username(username):
        """Get user by username."""
        user = db.session.query(User).filter_by(username=username).first()
        if not user:
            abort(404, description="User not found")
        return user

    @classmethod
    def delete_user(cls, user_id):
        """Delete a user."""
        user = db.session.get(User, user_id)
        if not user:
            abort(404, description="User not found")
        db.session.delete(user)
        db.session.commit()
        return True

    @classmethod
    def update_otp_key(cls, otp_key, email):
        """Update the OTP key for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        user.otp_key = otp_key
        user.is_2fa_enabled = True
        db.session.commit()
        return True

    @classmethod
    def update_2fa_status(cls, email, status):
        """Update the 2FA status for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        user.is_2fa_enabled = status
        db.session.commit()
        return True

    @classmethod
    def reset_password(cls, email, password):
        """Reset the password for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        hashed_password, salt = cls.hash_password(password)
        user.password = hashed_password
        user.salt = salt
        user.is_otp_used = False
        user.otp = None
        user.otp_expiry = None
        db.session.commit()
        return True

    @classmethod
    def update_is_active(cls, email):
        """Update the is_active status for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        user.is_active = True
        db.session.commit()
        print("User is active")
        return True

    @classmethod
    def check_user_existence(cls, identifier, value):
        """Check if a user exists.
        Description: This method checks if a user exists in the database.
        Args:
            identifier (str): The identifier to use for checking the user.
            value (str): The value of the identifier.
            Returns:
                bool: A boolean indicating whether the user exists.
        """
        user = db.session.query(User).filter_by(**{identifier: value}).first()
        if user:
            message = f"Registration failed: User with {identifier} {value} already exists."
            flash(message, "danger")
            current_app.logger.error(message)
            return True
        return False

    @classmethod
    def register_company_user(
            cls, username, email, role, password, phone_number,
            first_name, last_name, company_id):
        """Register a user and associate them with a specific company."""
        try:
            # Hash the password
            hashed_password, salt = cls.hash_password(password)

            # Validate the company ID
            company = Company.query.get(company_id)
            if not company:
                return {"success": False, "message": f"Company with ID {company_id} does not exist."}

            # Check if a user with the same email or username already exists
            existing_user = User.query.filter(
                (User.email == email) | (User.username == username)
            ).first()
            if existing_user:
                return {"success": False, "message": "A user with this email or username already exists."}

            # Create a new user
            new_user = User(
                username=username,
                email=email,
                role=role,
                password=hashed_password,
                phone_number=phone_number,
                first_name=first_name,
                last_name=last_name,
                salt=salt,
                is_active=False
            )
            db.session.add(new_user)
            db.session.flush()  # Ensure new_user gets a user_id

            if company in new_user.companies:
                return {"success": False, "message": "User already assigned to the company."}

            # Associate the user with the company
            if company not in new_user.companies:
                new_user.companies.append(company)

            # Commit the transaction
            db.session.commit()
            return {"success": True, "message": "User successfully registered and assigned to the company."}
        except ValueError as ve:
            current_app.logger.error(f"Error registering user: {str(ve)}")
            db.session.rollback()
            return {"success": False, "message": str(ve)}
        except SQLAlchemyError as e:
            current_app.logger.error(f"Error registering user: {str(e)}")
            db.session.rollback()
            return {"success": False, "message": "A database error occurred."}
        except Exception as e:
            current_app.logger.error(f"Error registering user: {str(e)}")
            db.session.rollback()
            return {"success": False, "message": f"An unexpected error occurred: {str(e)}"}

    @classmethod
    def update_company_user(
            cls, id, username, email, role, phone, company, first_name, last_name):
        """Update a company user."""
        # Get the actual User object, not a dictionary
        user = db.session.get(User, id)
        if not user:
            current_app.logger.error(f"User with ID {id} not found")
            return False

        user.username = username
        user.email = email
        user.role = role
        user.phone_number = phone
        user.first_name = first_name
        user.last_name = last_name
        user.companies = [Company.query.get(company)]
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating company user: {str(e)}")
            return False

    @classmethod
    def get_company_users(cls, company_id):
        """Get all users associated with a specific company."""

        # The users should be found the user_company_association table
        users = db.session.query(User).join(user_company_association).filter(
            user_company_association.c.company_id == company_id).all()
        return [user.to_dict() for user in users]

    @classmethod
    def find_user_by_email_or_username(cls, email):
        return cls.query.filter(or_(cls.username==email, cls.email==email)).first()

class TaxBracket(db.Model):
    """Model representing a tax bracket.
    Description: This model represents a tax bracket in the system.
    Attributes:
        bracket_id (UUID): A unique identifier for the tax bracket.
        lower_bound (float): The lower bound of the tax bracket.
        upper_bound (float): The upper bound of the tax bracket.
        rate (float): The tax rate of the tax bracket.
        created_at (datetime): The date and time the tax bracket was created."""
    __tablename__ = 'tax_brackets'

    bracket_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    lower_bound = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    upper_bound = db.Column(db.Numeric(precision=12, scale=2), nullable=False)
    rate = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        TaxBracket: {self.bracket_id},
        lower_bound: {self.lower_bound},
        upper_bound: {self.upper_bound},
        rate: {self.rate}
        """

    def to_dict(self):
        """Convert tax bracket object to dictionary.
        Description: This method converts a tax bracket object to a dictionary.
        Returns:
            dict: A dictionary representing the tax bracket object.
        """
        return {
            "bracket_id": self.bracket_id,
            "lower_bound": self.lower_bound,
            "upper_bound": self.upper_bound,
            "rate": self.rate
        }

    def insert_taxbracket(self):
        """Insert a tax bracket into the database.
        Description: This method inserts a tax bracket into the database.
        Returns:
            bool: A boolean indicating whether the tax bracket was inserted successfully.
        """
        try:
            # Validate before inserting
            self.validate()

            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting tax bracket: ", e)
            return False

    def validate(self):
        """Validate the tax bracket attributes."""
        # check if the rate is a number
        if not isinstance(self.rate, (int, float, Decimal)):
            raise ValueError("Rate must be a number.")

        # check if the lower bound and upper bound are numbers
        if not isinstance(self.lower_bound, (int, float, Decimal)) or not isinstance(self.upper_bound, (int, float, Decimal)):
            raise ValueError("Lower bound and upper bound must be numbers.")

        if not (0 <= self.rate <= 1):
            raise ValueError("Rate must be between 0 and 100.")
        if self.lower_bound >= self.upper_bound:
            raise ValueError("Lower bound must be less than upper bound.")

        # Check if the tax bracket overlaps with any existing tax bracket
        tax_brackets = TaxBracket.query.all()
        for tax_bracket in tax_brackets:
            if self.bracket_id == tax_bracket.bracket_id:
                continue  # Skip self when updating an existing tax bracket

            if not (self.upper_bound <= tax_bracket.lower_bound or self.lower_bound >= tax_bracket.upper_bound):
                raise ValueError(
                    "Tax bracket overlaps with existing tax bracket.")

        # Check if lower bound and upper bound are within the range of 0 and 1000000000
        if not (0 <= self.lower_bound <= 1000000000) or not (0 <= self.upper_bound <= 1000000000):
            raise ValueError(
                "Lower bound and upper bound must be between 0 and 1000000000.")

        return True

    @classmethod
    def get_taxbrackets(cls):
        """Get all tax brackets from the database.
        Description: This method gets all tax brackets from the database.
        Returns:
            list: A list of dictionaries representing the tax brackets.
        """
        try:
            tax_brackets = db.session.query(TaxBracket).all()
            return [tax_bracket.to_dict() for tax_bracket in tax_brackets]
        except Exception as e:
            print("Error getting tax brackets: ", e)
            return []

    @classmethod
    def get_single_taxbracket(cls, id):
        """Get a single tax bracket by ID.
        Description: This method gets a single tax bracket by ID.
        Args:
            id (UUID): The unique identifier of the tax bracket.
        Returns:
            dict: A dictionary representing the tax bracket.
        """
        tax_bracket = db.session.get(TaxBracket, id)
        if not tax_bracket:
            return None
        return tax_bracket.to_dict()

    @classmethod
    def update_permanent_employee_taxbracket(cls, id, lower_bound, upper_bound, rate):
        """Update a tax bracket for permanent employees.
        Description: This method updates a tax bracket for permanent employees.
        Args:
            id (UUID): The unique identifier of the tax bracket.
            lower_bound (float): The lower bound of the tax bracket.
            upper_bound (float): The upper bound of the tax bracket.
            rate (float): The tax rate of the tax bracket.
        Returns:
            bool: A boolean indicating whether the tax bracket was updated successfully.
        """
        tax_bracket = db.session.get(TaxBracket, id)
        tax_bracket.lower_bound = lower_bound
        tax_bracket.upper_bound = upper_bound
        tax_bracket.rate = rate

        try:
            # validate the tax bracket before updating
            tax_bracket.validate()
        except ValueError as e:
            print(f"Error updating tax bracket: {str(e)}")
            raise
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating tax bracket: {str(e)}")
            return False

    @classmethod
    def delete_permanent_employee_taxbracket(cls, id):
        """Delete a tax bracket for permanent employees.
        Description: This method deletes a tax bracket for permanent employees.
        Args:
            id (UUID): The unique identifier of the tax bracket.
        Returns:
            bool: A boolean indicating whether the tax bracket was deleted successfully.
        """
        tax_bracket = db.session.get(TaxBracket, id)
        if not tax_bracket:
            return False
        
        try:
            db.session.delete(tax_bracket)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting tax bracket: {str(e)}")
            return False

class AgencyCommission(db.Model):
    """Model representing commissions earned by agencies."""
    __tablename__ = 'agency_commissions'

    commission_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    agency_id = db.Column(UUID(as_uuid=True), db.ForeignKey('agencies.agency_id'), nullable=False)
    company_id = db.Column(UUID(as_uuid=True), db.ForeignKey('companies.company_id'), nullable=False)
    amount = db.Column(db.Numeric(precision=12, scale=2), nullable=False)
    commission_date = db.Column(db.DateTime, server_default=db.func.now())
    payment_reference = db.Column(db.String(255), nullable=True)
    is_paid = db.Column(db.Boolean, default=False)
    paid_date = db.Column(db.DateTime, nullable=True)
    description = db.Column(db.String(255), nullable=True)

    # Relationships
    agency = db.relationship('Agency', backref=db.backref('commissions', lazy=True))
    company = db.relationship('Company', backref=db.backref('agency_commissions', lazy=True))

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        commission_id: {self.commission_id},
        agency_id: {self.agency_id},
        company_id: {self.company_id},
        amount: {self.amount},
        commission_date: {self.commission_date},
        is_paid: {self.is_paid},
        paid_date: {self.paid_date},
        description: {self.description}
        """

    def to_dict(self):
        """Convert commission object to dictionary."""
        return {
            "commission_id": self.commission_id,
            "agency_id": self.agency_id,
            "agency_name": self.agency.agency_name if self.agency else None,
            "company_id": self.company_id,
            "company_name": self.company.company_name if self.company else None,
            "amount": float(self.amount) if self.amount else 0,
            "commission_date": self.commission_date.strftime("%d/%m/%Y %H:%M:%S"),
            "payment_reference": self.payment_reference,
            "is_paid": self.is_paid,
            "paid_date": self.paid_date.strftime("%d/%m/%Y %H:%M:%S") if self.paid_date else None,
            "description": self.description
        }

    @classmethod
    def get_commissions_by_agency(cls, agency_id):
        """Get all commissions for an agency."""
        try:
            commissions = db.session.query(AgencyCommission).filter_by(agency_id=agency_id).all()
            return [commission.to_dict() for commission in commissions]
        except Exception as e:
            current_app.logger.error(f"Error getting commissions for agency: {str(e)}")
            return []

    @classmethod
    def get_commissions_by_company(cls, company_id):
        """Get all commissions for a company."""
        try:
            commissions = db.session.query(AgencyCommission).filter_by(company_id=company_id).all()
            return [commission.to_dict() for commission in commissions]
        except Exception as e:
            current_app.logger.error(f"Error getting commissions for company: {str(e)}")
            return []

    @classmethod
    def create_commission(cls, agency_id, company_id, amount, description=None, payment_reference=None):
        """Create a new commission record."""
        try:
            commission = AgencyCommission(
                agency_id=agency_id,
                company_id=company_id,
                amount=amount,
                description=description,
                payment_reference=payment_reference
            )
            db.session.add(commission)
            db.session.commit()
            return commission
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating commission: {str(e)}")
            return None

    @classmethod
    def mark_as_paid(cls, commission_id):
        """Mark a commission as paid."""
        try:
            commission = db.session.get(AgencyCommission, commission_id)
            if not commission:
                abort(404, description="Commission not found")

            commission.is_paid = True
            commission.paid_date = datetime.now(timezone.utc)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error marking commission as paid: {str(e)}")
            return False


class Agency(db.Model):
    """Model representing an agency that can manage companies."""
    __tablename__ = 'agencies'

    agency_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    agency_name = db.Column(db.String(255), nullable=False)
    agency_email = db.Column(db.String(255), unique=True, nullable=False)
    agency_phone = db.Column(db.String(255), nullable=False)
    agency_address = db.Column(db.String(255), nullable=True)
    commission_rate = db.Column(db.Numeric(precision=5, scale=2), nullable=False, default=10.00)  # Default 10%
    logo = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    users = db.relationship(
        'User',
        secondary=user_agency_association,
        back_populates='agencies',
        lazy=True
    )

    companies = db.relationship(
        'Company',
        secondary=agency_company_association,
        back_populates='agencies',
        lazy=True
    )

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        agency_id: {self.agency_id},
        agency_name: {self.agency_name},
        agency_email: {self.agency_email},
        agency_phone: {self.agency_phone},
        agency_address: {self.agency_address},
        commission_rate: {self.commission_rate},
        logo: {self.logo},
        created_at: {self.created_at},
        is_active: {self.is_active}
        """

    def to_dict(self):
        """Convert agency object to dictionary."""
        return {
            "agency_id": self.agency_id,
            "agency_name": self.agency_name,
            "agency_email": self.agency_email,
            "agency_phone": self.agency_phone,
            "agency_address": self.agency_address,
            "commission_rate": float(self.commission_rate) if self.commission_rate else None,
            "logo": self.logo,
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S"),
            "is_active": self.is_active,
            "company_count": len(self.companies) if self.companies else 0
        }

    @classmethod
    def get_agencies(cls):
        """Get all agencies from the database."""
        try:
            agencies = db.session.query(Agency).all()
            return [agency.to_dict() for agency in agencies]
        except Exception as e:
            current_app.logger.error(f"Error getting agencies: {str(e)}")
            return []

    @classmethod
    def get_agency_by_id(cls, agency_id):
        """Get an agency by ID."""
        agency = db.session.get(Agency, agency_id)
        if not agency:
            abort(404, description="Agency not found")
        return agency.to_dict()

    @classmethod
    def register_agency(cls, agency_name, agency_email, agency_phone, agency_address=None, commission_rate=10.00):
        """Register a new agency."""
        try:
            new_agency = Agency(
                agency_name=agency_name,
                agency_email=agency_email,
                agency_phone=agency_phone,
                agency_address=agency_address,
                commission_rate=commission_rate
            )
            db.session.add(new_agency)
            db.session.commit()
            return new_agency
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error registering agency: {str(e)}")
            return None

    @classmethod
    def update_agency(cls, agency_id, **kwargs):
        """Update an agency."""
        agency = db.session.get(Agency, agency_id)
        if not agency:
            abort(404, description="Agency not found")

        for key, value in kwargs.items():
            if hasattr(agency, key):
                setattr(agency, key, value)

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating agency: {str(e)}")
            return False

    @classmethod
    def get_agencies_for_user(cls, user_id):
        """Get all agencies associated with a user."""
        return (
            cls.query
            .join(user_agency_association)
            .filter(user_agency_association.c.user_id == user_id)
            .all()
        )

    @classmethod
    def get_users_for_agency(cls, agency_id):
        """Get all users associated with an agency."""
        users = (
            User.query
            .join(user_agency_association)
            .filter(user_agency_association.c.agency_id == agency_id)
            .all()
        )
        return users

    @classmethod
    def assign_user_to_agency(cls, user_id, agency_id):
        """Assign a user to an agency."""
        current_app.logger.info(f"Starting assign_user_to_agency with user_id: {user_id}, agency_id: {agency_id}")

        try:
            user = db.session.get(User, user_id)
            if not user:
                current_app.logger.error(f"User with ID {user_id} not found")
                return False

            current_app.logger.info(f"Found user: {user.username}")

            agency = db.session.get(Agency, agency_id)
            if not agency:
                current_app.logger.error(f"Agency with ID {agency_id} not found")
                return False

            current_app.logger.info(f"Found agency: {agency.agency_name}")

            # Check if association already exists
            if agency in user.agencies:
                current_app.logger.info(f"User already assigned to agency")
                return True

            # Add agency to user's agencies
            user.agencies.append(agency)
            try:
                db.session.commit()
                current_app.logger.info(f"Successfully assigned user to agency")
                return True
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error committing assignment: {str(e)}")
                return False
        except Exception as e:
            current_app.logger.error(f"Unexpected error in assign_user_to_agency: {str(e)}")
            return False

    @classmethod
    def remove_user_from_agency(cls, user_id, agency_id):
        """Remove a user from an agency."""
        user = db.session.get(User, user_id)
        agency = db.session.get(Agency, agency_id)
        if not user or not agency:
            return False

        if agency in user.agencies:
            user.agencies.remove(agency)
            try:
                db.session.commit()
                return True
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error removing user from agency: {str(e)}")
                return False
        return True  # User not assigned to agency

    @classmethod
    def assign_company_to_agency(cls, company_id, agency_id):
        """Assign a company to an agency."""
        company = db.session.get(Company, company_id)
        agency = db.session.get(Agency, agency_id)
        if not company or not agency:
            return False

        if agency not in company.agencies:
            company.agencies.append(agency)
            try:
                db.session.commit()
                return True
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error assigning company to agency: {str(e)}")
                return False
        return True  # Company already assigned to agency

    @classmethod
    def remove_company_from_agency(cls, company_id, agency_id):
        """Remove a company from an agency."""
        company = db.session.get(Company, company_id)
        agency = db.session.get(Agency, agency_id)
        if not company or not agency:
            return False

        if agency in company.agencies:
            company.agencies.remove(agency)
            try:
                db.session.commit()
                return True
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error removing company from agency: {str(e)}")
                return False
        return True  # Company not assigned to agency

    @classmethod
    def upload_agency_logo(cls, agency_id, logo_file):
        """Upload agency logo to DigitalOcean Spaces."""
        try:
            current_app.logger.info(f"Uploading logo for agency ID: {agency_id}")

            # Get the agency
            agency = db.session.get(Agency, agency_id)
            if not agency:
                message = f"Agency with ID {agency_id} not found"
                current_app.logger.error(message)
                return {'success': False, 'message': message}

            # Configure S3 client for DigitalOcean Spaces
            s3 = boto3.client(
                's3',
                region_name=os.getenv("DO_SPACE_REGION"),
                endpoint_url=os.getenv("DO_SPACE_ENDPOINT"),
                aws_access_key_id=os.getenv("DO_SPACE_KEY"),
                aws_secret_access_key=os.getenv("DO_SPACE_SECRET")
            )

            # Generate a unique filename
            filename = f"{agency.agency_name}_logo"

            # Define the S3 path
            s3_path = f"agency_logos/{agency.agency_name}_{agency.agency_id}_{filename}"

            # Upload the file to DigitalOcean Spaces
            s3.upload_fileobj(
                Fileobj=logo_file,
                Bucket=os.getenv("DO_SPACE_BUCKET"),
                Key=s3_path,
                ExtraArgs={'ACL': 'public-read'}
            )

            # Generate the URL for the uploaded file
            file_url = f"{os.getenv('DO_SPACE_ENDPOINT')}/{os.getenv('DO_SPACE_BUCKET')}/{s3_path}"

            # Update the agency's logo field with the URL
            agency.logo = file_url
            db.session.commit()

            message = "Agency logo uploaded successfully"
            current_app.logger.info(f"{message} - URL: {file_url}")
            return {'success': True, 'message': message, 'url': file_url, 'status': 200}

        except Exception as e:
            db.session.rollback()
            message = f"Error uploading agency logo: {str(e)}"
            current_app.logger.error(message)
            return {'success': False, 'message': message}


class NsfContributions(db.Model):
    """Model representing National Social Security Fund contributions."""
    __tablename__ = 'nsf_contributions'
    __table_args__ = (
        db.UniqueConstraint('contribution_name', 'start_date',
                            name='uq_contribution_name_start_date'),
    )

    nsf_id = db.Column(UUID(as_uuid=True), primary_key=True,
                       default=uuid.uuid4, nullable=False)
    contribution_name = db.Column(db.String(255), nullable=False)
    employee_rate = db.Column(db.Numeric(
        precision=12, scale=6), nullable=False)
    employer_rate = db.Column(db.Numeric(
        precision=12, scale=6), nullable=False)
    start_date = db.Column(db.Date, nullable=True)
    end_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        contribution_name: {self.contribution_name},
        employee_rate: {self.employee_rate},
        employer_rate: {self.employer_rate},
        start_date: {self.start_date},
        end_date: {self.end_date}
        """

    def to_dict(self):
        """Convert NSF contribution object to dictionary."""
        # format the dates to dd/mm/yyyy
        start_date = self.start_date.strftime(
            "%d/%m/%Y") if self.start_date else None
        end_date = self.end_date.strftime(
            "%d/%m/%Y") if self.end_date else None

        # Round the rates to 2 decimal places
        employee_rate = round(self.employee_rate * 100, 2)
        employer_rate = round(self.employer_rate * 100, 2)

        return {
            "nsf_id": self.nsf_id,
            "contribution_name": self.contribution_name,
            "employee_rate": employee_rate,
            "employer_rate": employer_rate,
            "start_date": start_date,
            "end_date": end_date
        }

    def insert_nsf_contribution(self):
        """Insert an NSF contribution into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting NSF contribution: ", e)
            return False

    @classmethod
    def get_nsf_contributions(cls):
        """Get all NSF contributions from the database."""
        try:
            nsf_contributions = db.session.query(NsfContributions).all()
            return nsf_contributions
        except Exception as e:
            print("Error getting NSF contributions: ", e)
            return []

    @classmethod
    def get_valid_contributions(cls, today=datetime.now().date()):
        """Get all valid NSF contributions."""
        return db.session.query(NsfContributions).filter(
            NsfContributions.start_date <= today,
            (NsfContributions.end_date.is_(None) |
             (NsfContributions.end_date >= today))
        ).all()

    @classmethod
    def update_nsf_contribution(cls, id, contribution_name, employee_rate, employer_rate, start_date=None, end_date=None):
        """Update an NSF contribution.
        Description: This method updates an NSF contribution.
        Args:
            id (UUID): The unique identifier of the NSF contribution.
            contribution_name (str): The name of the contribution.
            employee_rate (float): The employee rate of the contribution.
            employer_rate (float): The employer rate of the contribution.
        Returns:
            bool: A boolean indicating whether the NSF contribution was updated successfully.
        """
        nsf_contribution = NsfContributions.query.get_or_404(id)
        nsf_contribution.contribution_name = contribution_name
        nsf_contribution.employee_rate = employee_rate
        nsf_contribution.employer_rate = employer_rate

        # Updated during API development to allow dates update
        if start_date:
            nsf_contribution.start_date = start_date
        if end_date:
            nsf_contribution.end_date=end_date
            
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating NSF contribution: {str(e)}")
            return False

    @classmethod
    def delete_nsf_contribution(cls, id):
        """Delete an NSF contribution.
        Description: This method deletes an NSF contribution.
        Args:
            id (UUID): The unique identifier of the NSF contribution.
        Returns:
            bool: A boolean indicating whether the NSF contribution was deleted successfully.
        """
        nsf_contribution = NsfContributions.query.get_or_404(id)

        try:
            db.session.delete(nsf_contribution)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting NSF contribution: {str(e)}")
            return False


class UserRole(db.Model):
    """Model representing a user role.
    Description: This model represents a user role in the system.
    Attributes:
        role_id (UUID): A unique identifier for the user role.
        role_name (str): The name of the user role.
    """
    __tablename__ = 'user_roles'

    role_id = db.Column(UUID(as_uuid=True), primary_key=True,
                        default=uuid.uuid4, nullable=False)
    role_name = db.Column(db.String(255), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        role_id: {self.role_id},
        role_name: {self.role_name}
        """

    def to_dict(self):
        """Convert user role object to dictionary."""
        return {
            "role_id": self.role_id,
            "role_name": self.role_name
        }

    def insert_user_role(self):
        """Insert a user role into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting user role: ", e)
            return False

    @classmethod
    def get_user_roles(cls):
        """Get all user roles from the database."""
        try:
            user_roles = db.session.query(UserRole).all()
            return [user_role.to_dict() for user_role in user_roles]
        except Exception as e:
            print("Error getting user roles: ", e)
            return []

    @classmethod
    def get_user_role_by_id(cls, role_id):
        """Get a user role by ID."""
        try:
            user_role = db.session.query(UserRole).filter_by(role_id=role_id).first()
            if not user_role:
                return None
            return user_role.to_dict()
        except Exception as e:
            print("Error getting user role: ", e)
            return None
        
    @classmethod
    def update_user_role(cls, id, role_name):
        """Update a user role."""
        user_role = UserRole.query.get_or_404(id)
        user_role.role_name = role_name
        print("Role name before commit: ", user_role.role_name)
        try:
            print("Updating user role in the database")
            db.session.commit()
            print("User role updated successfully.")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating user role: {str(e)}")
            return False

    @classmethod
    def delete_user_role(cls, id):
        """Delete a user role."""
        user_role = UserRole.query.get_or_404(id)
        try:
            db.session.delete(user_role)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting user role: {str(e)}")
            return False


class CasualsTaxBracket(db.Model):
    __tablename__ = 'casuals_tax_brackets'
    bracket_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    lower_bound = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    upper_bound = db.Column(db.Numeric(precision=12, scale=2), nullable=False)
    rate = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        CasualsTaxBracket: {self.bracket_id},
        lower_bound: {self.lower_bound},
        upper_bound: {self.upper_bound},
        rate: {self.rate}
        """

    def to_dict(self):
        """Convert casuals tax bracket object to dictionary."""
        return {
            "bracket_id": self.bracket_id,
            "lower_bound": self.lower_bound,
            "upper_bound": self.upper_bound,
            "rate": self.rate
        }

    def insert_casuals_taxbracket(self):
        """Insert a casual tax bracket into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting casual tax bracket: ", e)
            return False

    @classmethod
    def get_casuals_taxbrackets(self):
        """Get all casuals tax brackets from the database."""
        try:
            casuals_tax_brackets = db.session.query(CasualsTaxBracket).all()
            return [casuals_tax_bracket.to_dict() for casuals_tax_bracket in casuals_tax_brackets]
        except Exception as e:
            print("Error getting casuals tax brackets: ", e)
            return []

    @classmethod
    def get_single_casual_taxbrackets(cls, id):
        """Get a single casuals tax bracket by ID."""
        casuals_tax_bracket = db.session.get(CasualsTaxBracket, id)
        if not casuals_tax_bracket:
            return None
        return casuals_tax_bracket.to_dict()
    
    @classmethod
    def update_casuals_taxbracket(self, id, lower_bound, upper_bound, rate):
        """Update a tax bracket for casuals."""
        tax_bracket = CasualsTaxBracket.query.get_or_404(id)
        tax_bracket.lower_bound = lower_bound
        tax_bracket.upper_bound = upper_bound
        tax_bracket.rate = rate

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating tax bracket: {str(e)}")
            return False

    @classmethod
    def delete_casuals_taxbracket(cls, id):
        """Delete a casuals tax bracket."""
        casuals_tax_bracket = CasualsTaxBracket.query.get_or_404(id)

        try:
            db.session.delete(casuals_tax_bracket)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting casuals tax bracket: {str(e)}")
            return False
        
class SecondEmployeeTaxBracket(db.Model):
    """Model representing a tax bracket for the second employee.
    Description: This model represents a tax bracket for the second employee in the system.
    Attributes:
        bracket_id (UUID): A unique identifier for the tax bracket.
        lower_bound (float): The lower bound of the tax bracket.
        upper_bound (float): The upper bound of the tax bracket.
        rate (float): The tax rate of the tax bracket.
        created_at (datetime): The date and time the tax bracket was created."""

    __tablename__ = 'second_employee_tax_brackets'
    bracket_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    lower_bound = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    upper_bound = db.Column(db.Numeric(precision=12, scale=2), nullable=False)
    rate = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        bracket_id: {self.bracket_id},
        lower_bound: {self.lower_bound},
        upper_bound: {self.upper_bound},
        rate: {self.rate}
        """

    def to_dict(self):
        """Convert second employee tax bracket object to dictionary."""
        return {
            "bracket_id": self.bracket_id,
            "lower_bound": self.lower_bound,
            "upper_bound": self.upper_bound,
            "rate": self.rate
        }

    def insert_second_employee_taxbracket(self):
        """Insert a second employee tax bracket into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting second employee tax bracket: ", e)
            return False

    @classmethod
    def get_second_employee_taxbrackets(cls):
        """Get all second employee tax brackets from the database."""
        try:
            second_employee_tax_brackets = db.session.query(cls).all()
            return [second_employee_tax_bracket.to_dict() for second_employee_tax_bracket in second_employee_tax_brackets]
        except Exception as e:
            print("Error getting second employee tax brackets: ", e)
            return []
    @classmethod
    def get_single_second_employee_taxbracket(cls, id):
        """Get a single second employee tax bracket by ID."""
        second_employee_tax_bracket = db.session.get(cls, id)
        if not second_employee_tax_bracket:
            return None
        return second_employee_tax_bracket.to_dict()
    
    @classmethod
    def update_second_employee_taxbracket(cls, id, lower_bound, upper_bound, rate):
        """Update a tax bracket for the second employee."""
        tax_bracket = cls.query.get_or_404(id)
        tax_bracket.lower_bound = lower_bound
        tax_bracket.upper_bound = upper_bound
        tax_bracket.rate = rate

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating tax bracket: {str(e)}")
            return False

    @classmethod
    def delete_second_employee_taxbracket(cls, id):
        """Delete a second employee tax bracket."""
        second_employee_tax_bracket = cls.query.get_or_404(id)

        try:
            db.session.delete(second_employee_tax_bracket)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting second employee tax bracket: {str(e)}")
            return False

class EmployeeType(db.Model):
    """
    Model representing an employee type.
    Description: This model represents an employee type in the system.
    Attributes:
        employee_type_id (UUID): A unique identifier for the employee type.
        employee_type_name (str): The name of the employee type.
    """

    __tablename__ = 'employee_types'
    employee_type_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_type_name = db.Column(db.String(255), unique=True, nullable=False)

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        employee_type_id: {self.employee_type_id},
        employee_type_name: {self.employee_type_name}
        """

    def to_dict(self):
        """Convert employee type object to dictionary."""
        return {
            "employee_type_id": self.employee_type_id,
            "employee_type_name": self.employee_type_name
        }

    def insert_employee_type(self):
        """Insert an employee type into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting employee type: ", e)
            return False

    @staticmethod
    def get_employee_types():
        """Get all employee types from the database."""
        try:
            employee_types = db.session.query(EmployeeType).all()
            return [employee_type.to_dict() for employee_type in employee_types]
        except Exception as e:
            print("Error getting employee types: ", e)
            return []

    @classmethod
    def update_employee_type(cls, id, employee_type_name):
        """Update an employee type."""
        employee_type = EmployeeType.query.get_or_404(id)
        employee_type.employee_type_name = employee_type_name

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating employee type: {str(e)}")
            return False

    @classmethod
    def delete_employee_type(cls, id):
        """Delete an employee type."""
        employee_type = EmployeeType.query.get_or_404(id)

        try:
            db.session.delete(employee_type)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting employee type: {str(e)}")
            return False


class ConsultantTaxBracket(db.Model):
    """Model representing a tax bracket for consultants.
    Description: This model represents a tax bracket for consultants in the system.
    Attributes:
        bracket_id (UUID): A unique identifier for the tax bracket.
        lower_bound (float): The lower bound of the tax bracket.
        upper_bound (float): The upper bound of the tax bracket.
        rate (float): The tax rate of the tax bracket.
        created_at (datetime): The date and time the tax bracket was created."""
    __tablename__ = 'consultant_tax_brackets'
    bracket_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    lower_bound = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    upper_bound = db.Column(db.Numeric(precision=12, scale=2), nullable=False)
    rate = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        bracket_id: {self.bracket_id},
        lower_bound: {self.lower_bound},
        upper_bound: {self.upper_bound},
        rate: {self.rate}
        """

    def to_dict(self):
        """Convert consultant tax bracket object to dictionary."""
        return {
            "bracket_id": self.bracket_id,
            "lower_bound": self.lower_bound,
            "upper_bound": self.upper_bound,
            "rate": self.rate
        }

    def add_consultant_taxbracket(self):
        """Add a consultant tax bracket into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting consultant tax bracket: ", e)
            return False

    @classmethod
    def get_consultant_taxbrackets(cls):
        """Get all consultant tax brackets from the database."""
        try:
            consultant_tax_brackets = db.session.query(cls).all()
            return [consultant_tax_bracket.to_dict() for consultant_tax_bracket in consultant_tax_brackets]
        except Exception as e:
            print("Error getting consultant tax brackets: ", e)
            return []
    @classmethod
    def get_single_consultant_taxbrackets(cls, id):
        """Get a single consultant tax bracket by ID."""
        consultant_tax_bracket = db.session.get(cls, id)
        if not consultant_tax_bracket:
            return None
        return consultant_tax_bracket.to_dict()
    
    @classmethod
    def update_consultant_taxbracket(cls, id, lower_bound, upper_bound, rate):
        """Update a tax bracket for consultants."""
        tax_bracket = cls.query.get_or_404(id)
        tax_bracket.lower_bound = lower_bound
        tax_bracket.upper_bound = upper_bound
        tax_bracket.rate = rate

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating tax bracket: {str(e)}")
            return False

    @classmethod
    def delete_consultant_taxbracket(cls, id):
        """Delete a consultant tax bracket."""
        consultant_tax_bracket = cls.query.get_or_404(id)

        try:
            db.session.delete(consultant_tax_bracket)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting consultant tax bracket: {str(e)}")
            return False

class Contact(db.Model):
    """Model representing a contact.
    Description: This model represents a contact in the system.
    Attributes:
        contact_id (UUID): A unique identifier for the contact.
        name (str): The name of the contact.
        email (str): The email of the contact.
        phone_number (str): The phone number of the contact.
        message (str): The message of the contact.
        created_at (datetime): The date and time the contact was created."""
    __tablename__ = 'contacts'
    contact_id = db.Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=False)
    phone_number = db.Column(db.String(255), nullable=True)
    company_name = db.Column(db.String(255), nullable=True)
    message = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        contact_id: {self.contact_id},
        name: {self.name},
        email: {self.email},
        phone_number: {self.phone_number},
        company_name: {self.company_name},
        message: {self.message}
        """

    def to_dict(self):
        """Convert contact object to dictionary."""
        return {
            "contact_id": self.contact_id,
            "name": self.name,
            "email": self.email,
            "phone_number": self.phone_number,
            "company_name": self.company_name,
            "message": self.message,
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S")
        }

    def insert_contact(self):
        """Insert a contact into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting contact: ", e)
            return False

    @classmethod
    def get_contacts(cls):
        """Get all contacts from the database."""
        try:
            contacts = db.session.query(Contact).all()
            return [contact.to_dict() for contact in contacts]
        except Exception as e:
            print("Error getting contacts: ", e)
            return []

    @classmethod
    def delete_contact(cls, id):
        """Delete a contact."""
        contact = Contact.query.get_or_404(id)

        try:
            db.session.delete(contact)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting contact: {str(e)}")
            return False


class BrdDeductions(db.Model):
    """
    Model representing a BRD deduction.
    Description: This model represents a BRD deduction in the system.

    Attributes:
        id (UUID): A unique identifier for the BRD deduction.
        deduction_rate (float): The rate at which the BRD loan is deducted from the employee's salary.
    """
    __tablename__ = 'brd_deductions'

    id = db.Column(UUID(as_uuid=True), primary_key=True,
                   default=uuid.uuid4, nullable=False)
    deduction_rate = db.Column(db.Numeric(
        precision=12, scale=6), nullable=False)

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        id: {self.id},
        deduction_rate: {self.deduction_rate}
        """

    def to_dict(self):
        """Convert BRD deduction object to dictionary."""
        return {
            "id": self.id,
            "deduction_rate": self.deduction_rate
        }

    @classmethod
    def add_brd_deduction(cls, deduction_rate):
        """Add a BRD deduction."""
        brd_deduction = BrdDeductions(deduction_rate=deduction_rate)
        try:
            db.session.add(brd_deduction)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            current_app.logger.error(f"Error adding BRD deduction: {str(e)}")
            return False

    @classmethod
    def get_brd_deductions(cls):
        """Get the BRD deductions."""
        brd_deduction = db.session.query(BrdDeductions).first()
        if not brd_deduction:
            return None
        return brd_deduction.to_dict()

    @classmethod
    def update_brd_deduction(cls, id, deduction_rate):
        """Update a BRD deduction."""
        brd_deduction = db.session.get(BrdDeductions, id)
        brd_deduction.deduction_rate = deduction_rate

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating BRD deduction: {str(e)}")
            return False

    @classmethod
    def delete_brd_deduction(cls, id):
        """Delete a BRD deduction."""
        brd_deduction = db.session.get(BrdDeductions, id)
        db.session.delete(brd_deduction)
        db.session.commit()
        return True


# Junction table for the many-to-many relationship between Plans and Features
plan_features = db.Table(
    'plan_features',
    db.Column('plan_id', UUID(as_uuid=True), db.ForeignKey(
        'plans.plan_id'), primary_key=True),
    db.Column('feature_id', UUID(as_uuid=True),
              db.ForeignKey('features.id'), primary_key=True)
)


class Features(db.Model):
    """
    Model representing a feature.
    Description: This model represents a feature in the system.

    Attributes:
        id (UUID): A unique identifier for the feature.
        feature_name (str): The name of the feature.
        description (str): The description of the feature.
    """
    __tablename__ = 'features'

    id = db.Column(UUID(as_uuid=True), primary_key=True,
                   default=uuid.uuid4, nullable=False)
    feature_name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=False)

    plans = db.relationship(
        'Plans', secondary='plan_features', back_populates='features')

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        id: {self.id},
        feature_name: {self.feature_name},
        description: {self.description}
        """

    def to_dict(self):
        """Convert feature object to dictionary."""
        return {
            "id": self.id,
            "feature_name": self.feature_name,
            "description": self.description
        }

    @classmethod
    def add_feature(cls, feature_name, description):
        """Add a feature."""
        feature = Features(feature_name=feature_name, description=description)
        try:
            db.session.add(feature)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            current_app.logger.error(f"Error adding feature: {str(e)}")
            return False

    @classmethod
    def get_features(cls):
        """Get the features."""
        try:
            features = db.session.query(Features).all()
            return [feature.to_dict() for feature in features]
        except Exception as e:
            current_app.logger.error(f"Error getting features: {str(e)}")
            return []

    @classmethod
    def update_feature(cls, id, feature_name, description):
        """Update a feature."""
        feature = db.session.get(Features, id)
        feature.feature_name = feature_name
        feature.description = description

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating feature: {str(e)}")
            return False

    @classmethod
    def delete_feature(cls, id):
        """Delete a feature."""
        try:
            feature = db.session.get(Features, id)
            db.session.delete(feature)
            db.session.commit()
            return True
        except Exception:
            raise

    @classmethod
    def get_feature_by_id(cls, id):
        """Get a feature by ID."""
        try:
            feature = db.session.query(Features).filter_by(id=id).first()
            if not feature:
                return None
            return feature.to_dict()
        except Exception as e:
            current_app.logger.error(f"Error getting feature: {str(e)}")
            return None


class Plans(db.Model):
    __tablename__ = 'plans'
    plan_id = db.Column(UUID(as_uuid=True), primary_key=True,
                        default=uuid.uuid4, nullable=False)
    plan_name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=False)
    price = db.Column(db.Numeric(precision=12, scale=6), nullable=False)
    num_of_employees = db.Column(db.Integer, nullable=False)
    price_per_employee = db.Column(db.Numeric(precision=12, scale=2), nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, onupdate=db.func.now())

    # Set up the reverse relationship
    companies = db.relationship(
        'Company', back_populates='plan', cascade="all, delete-orphan")
    features = db.relationship(
        'Features', secondary='plan_features', back_populates='plans')
    route_plan_requirements = db.relationship(
        'RoutePlanRequirement', back_populates='plan', cascade="all, delete-orphan")

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        plan_id: {self.plan_id},
        plan_name: {self.plan_name},
        description: {self.description},
        price: {self.price},
        created_at: {self.created_at},
        updated_at: {self.updated_at}
        """

    def to_dict(self):
        """Convert plan object to dictionary."""
        return {
            "plan_id": self.plan_id,
            "plan_name": self.plan_name,
            "description": self.description,
            "price": float(self.price),
            "num_of_employees": self.num_of_employees,
            "price_per_employee": float(self.price_per_employee) if self.price_per_employee is not None else None,
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%d/%m/%Y %H:%M:%S") if self.updated_at else None,
            "features": [{"id": feature.id, "feature_name": feature.feature_name} for feature in self.features]
        }

    @classmethod
    def add_plan(cls, plan_name, description, price, num_of_employees, price_per_employee):
        """Add a plan."""
        plan = Plans(plan_name=plan_name, description=description,price=price,
                      num_of_employees=num_of_employees, price_per_employee=price_per_employee)

        # Check if the plan already exists
        if cls.check_plan_existence(plan_name):
            message = 'exists'
            return message
        try:
            db.session.add(plan)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error adding plan: ", e)
            return False

    @classmethod
    def check_plan_existence(cls, plan_name):
        """Check if a plan exists."""
        plan = db.session.query(Plans).filter_by(plan_name=plan_name).first()
        if plan:
            return plan.to_dict()
        return None

    @classmethod
    def get_plans(cls):
        """Get all plans from the database."""
        try:
            plans = db.session.query(Plans).all()
            return [plan.to_dict() for plan in plans]
        except Exception as e:
            print("Error getting plans: ", e)
            return []

    @classmethod
    def get_plan_by_id(cls, id):
        """Get a plan by ID."""
        try:
            plan = db.session.query(Plans).filter_by(plan_id=id).first()
            if not plan:
                return None
            return plan.to_dict()
        except Exception as e:
            print("Error getting plan: ", e)
            return None

    @classmethod
    def update_plan(cls, id, plan_name, description, price, num_of_employees, price_per_employee):
        """Update a plan."""
        plan = db.session.get(Plans, id)
        plan.plan_name = plan_name
        plan.description = description
        plan.price = price
        plan.num_of_employees = num_of_employees
        plan.price_per_employee=price_per_employee

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating plan: {str(e)}")
            return False

    @classmethod
    def delete_plan(cls, id):
        """Delete a plan."""
        plan = db.session.get(Plans, id)
        try:
            db.session.delete(plan)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deleting plan: {str(e)}")
            return False

    @staticmethod
    def add_feature(plan_id, feature_id):
        plan = db.session.query(Plans).filter_by(plan_id=plan_id).first()
        feature = db.session.query(Features).filter_by(id=feature_id).first()
        if plan and feature:
            if feature not in plan.features:
                plan.features.append(feature)
                try:
                    db.session.commit()
                    message = 'success'
                    return message

                except Exception as e:
                    db.session.rollback()
                    current_app.logger.error(
                        f"Error adding feature to plan: {str(e)}")
                    return False
            elif feature in plan.features:
                message = 'exists'
                return message
        return False

    @staticmethod
    def remove_feature(plan_id, feature_id):
        plan = db.session.query(Plans).filter_by(plan_id=plan_id).first()
        feature = db.session.query(Features).filter_by(id=feature_id).first()
        if plan and feature:
            if feature in plan.features:
                plan.features.remove(feature)
                try:
                    db.session.commit()
                    return True
                except Exception as e:
                    db.session.rollback()
                    current_app.logger.error(
                        f"Error removing feature from plan: {str(e)}")
                    return False
        return False


class RoutePlanRequirement(db.Model):
    """Route Plan Requirement Model.
    Description: This model represents a route plan requirement in the system.
    Attributes:
        id (UUID): A unique identifier for the route plan requirement.
        route_path (str): The route path of the requirement.
        required_plan_id (UUID): The unique identifier of the required plan.
        created_at (datetime): The date and time the route plan requirement was created.
        updated_at (datetime): The date and time the route plan requirement was last updated.
        """
    __tablename__ = 'route_plan_requirements'

    id = db.Column(UUID(as_uuid=True), primary_key=True,
                   default=uuid.uuid4, nullable=False)
    route_path = db.Column(db.String(255), nullable=False)
    required_plan_id = db.Column(
        UUID(as_uuid=True), db.ForeignKey('plans.plan_id'), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, onupdate=db.func.now())

    # Relationships
    plan = db.relationship('Plans', back_populates='route_plan_requirements')

    def __repr__(self):
        return f"<RoutePlanRequirement(route_path='{self.route_path}', required_plan_id='{self.required_plan_id}')>"

    def to_dict(self):
        """Convert route-plan requirement object to dictionary."""
        return {
            "id": self.id,
            "route_path": self.route_path,
            "required_plan_id": self.required_plan_id,
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%d/%m/%Y %H:%M:%S") if self.updated_at else None,
            "Plan_name": self.plan.plan_name,
            "description": self.description
        }

    @classmethod
    def add_route_plan_requirement(cls, route_path, required_plan_id, description=None):
        """Add a route-plan requirement."""
        requirement = cls(
            route_path=route_path, required_plan_id=required_plan_id, description=description)
        try:
            db.session.add(requirement)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error adding route-plan requirement: {e}")
            return False

    @classmethod
    def get_route_plan_requirements(cls):
        """Get all route-plan requirements from the database."""
        try:
            requirements = db.session.query(RoutePlanRequirement).all()
            return [requirement.to_dict() for requirement in requirements]
        except Exception as e:
            print(f"Error getting route-plan requirements: {e}")
            return []

    @classmethod
    def get_route_plan_requirement_by_id(cls, id):
        try:
            requirement = db.session.get(RoutePlanRequirement, id)
            return requirement
        except Exception as e:
            raise

    @classmethod
    def update_route_plan_requirement(cls, id, route_path, required_plan_id, description=None):
        requirement = db.session.get(RoutePlanRequirement, id)
        try:
            requirement.route_path = route_path
            requirement.required_plan_id = required_plan_id
            requirement.description = description
            db.session.commit()
            return requirement
        except Exception as e:
            db.session.rollback()
            print(f"Error updating route-plan requirement: {e}")
            return None
        
    @classmethod
    def get_route_path_given_plan_id(cls, plan_id):
        """Get a list of route paths given a plan ID."""
        try:
            routes = db.session.query(RoutePlanRequirement.route_path).filter_by(
                required_plan_id=plan_id).all()

            # Extract route paths from the result tuples
            route_paths = [route[0] for route in routes]
            return route_paths
        except Exception as e:
            current_app.logger.error(f"Error getting route paths: {str(e)}")
            return []

    @classmethod
    def delete_route_plan_requirement(cls, id):
        """Delete a route-plan requirement."""
        requirement = db.session.get(RoutePlanRequirement, id)
        try:
            db.session.delete(requirement)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting route-plan requirement: {e}")
            return False


class ApprovalType(db.Model):
    """Model representing an approval type."
    Description: This model represents an approval type in the system.
    Attributes:
        id (UUID): A unique identifier for the approval type.
        approval_type (str): The name of the approval type.
        created_at (datetime): The date and time the approval type was created.
    """
    __tablename__ = 'approval_types'
    id = db.Column(UUID(as_uuid=True), primary_key=True,
                   default=uuid.uuid4, nullable=False)
    approval_type = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __repr__(self):
        return f"""
        id: {self.id},
        approval_type: {self.approval_type},
        description: {self.description},
        created_at: {self.created_at}
        """

    def to_dict(self):
        """Convert approval type object to dictionary."""
        return {
            "id": self.id,
            "approval_type": self.approval_type,
            "description": self.description,
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S") if self.created_at else None
        }

    @classmethod
    def add_approval_type(cls, approval_type, description=None):
        """Add an approval type."""
        approval = cls(approval_type=approval_type,
                       description=description)
        try:
            db.session.add(approval)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error adding approval type: {e}")
            return False

    @classmethod
    def get_approval_types(cls):
        """Get all approval types from the database."""
        try:
            approvals = db.session.query(ApprovalType).all()
            return [approval.to_dict() for approval in approvals]
        except Exception as e:
            print(f"Error getting approval types: {e}")
            return []

    @classmethod
    def get_approval_type_by_id(cls, id):
        """Get an approval type by ID."""
        try:
            approval = db.session.query(ApprovalType).filter_by(id=id).first()
            if not approval:
                return {}
            return approval.to_dict()
        except Exception as e:
            print(f"Error getting approval type: {e}")
            raise

    @classmethod
    def update_approval_type(cls, id, approval_type, description=None):
        """Update an approval type."""
        approval = db.session.get(ApprovalType, id)
        approval.approval_type = approval_type
        approval.description = description
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating approval type: {e}")
            return False

    @classmethod
    def delete_approval_type(cls, id):
        """Delete an approval type."""
        approval = db.session.get(ApprovalType, id)
        try:
            db.session.delete(approval)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting approval type: {e}")
            return False


class Subscription(db.Model):
    __tablename__ = 'subscriptions'
    id = db.Column(UUID(as_uuid=True), primary_key=True,
                   default=uuid.uuid4, nullable=False)
    email = db.Column(db.String(255), nullable=False)
    status = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
