import uuid
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Integer, Text, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.central import NsfContributions
from datetime import datetime, timedelta
import pytz # For time zone handling
from flask import current_app
from flask import flash, session
import calendar
from sqlalchemy import and_, or_, Numeric
from dotenv import load_dotenv
import os
import requests
from app.models.central import User as CentralUser
import hashlib
from sqlalchemy import Enum
from sqlalchemy import Boolean
from app.models.company_base import DynamicBase
from app.models.company_salary_advance import SalaryAdvanceRequest
from app.models.company_payroll_approval import PayrollApproval
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from sqlalchemy.sql import func
import boto3
import os
from app.models.company import Employee
import urllib.parse

# Load environment variables
load_dotenv()


class Document(DynamicBase):
    """
    Represents documents related to companies and employees.
    This model is used to store various types of documents such as
    company documents and employee documents.
    """
    __tablename__ = 'documents'
    __table_args__ = {'extend_existing': True}
    document_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_type = Column(String(64), nullable=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=True)
    file_name = Column(String(255), nullable=False)
    file_label = Column(String(64), nullable=True) 
    file_url = Column(String(512), nullable=False)
    uploaded_at = Column(DateTime, server_default=func.now())
    uploaded_by = Column(String(128), nullable=True)

    # Optional: Relationship to Employee if it's an employee document
    employee = relationship("Employee", back_populates="documents", foreign_keys=[employee_id])

    def repr__(self):
        """
        Returns a string representation of the Document object.
        """
        return f"""
        <Document(document_id={self.document_id}, 
        document_type={self.document_type}, 
        file_name={self.file_name})>,
        uploaded_at={self.uploaded_at},
        uploaded_by={self.uploaded_by}
        """
    def __str__(self):
        """
        Returns a string representation of the Document object.
        """
        return f"Document(document_id={self.document_id}, document_type={self.document_type}, file_name={self.file_name})"
    
    def to_dict(self):
        """
        Converts the Document object to a dictionary representation.
        """
        return {
            'document_id': str(self.document_id),
            'document_type': self.document_type,
            'employee_id': str(self.employee_id) if self.employee_id else None,
            'file_name': self.file_name,
            'file_label': self.file_label,
            'file_url': self.file_url,
            'uploaded_at': self.uploaded_at.isoformat() if self.uploaded_at else None,
            'uploaded_by': self.uploaded_by
        }
    
    @classmethod
    def get_s3_client(cls):
        """
        Returns an S3 client configured for DigitalOcean Spaces.
        """
        return boto3.client(
            's3',
            region_name=os.getenv("DO_SPACE_REGION"),
            endpoint_url=os.getenv("DO_SPACE_ENDPOINT"),
            aws_access_key_id=os.getenv("DO_SPACE_KEY"),
            aws_secret_access_key=os.getenv("DO_SPACE_SECRET")
        )
    
    @classmethod
    def upload_document(cls, session, company_id, document_type, file, uploaded_by, employee_id=None, file_label=None):
        """
        Uploads a document to DigitalOcean Spaces and saves the DB record.
        """
        company_name = CompanyHelpers.get_company_name(company_id)
        if employee_id is not None:
            employee = Employee.get_employee_by_id(session, employee_id)
            employee_name = employee.get('full_name', 'Unknown Employee')
        s3 = cls.get_s3_client()
        filename = f"{file.filename}"
        
        if document_type == "company":
            s3_path = f"{company_name}_{company_id}/{filename}"
        elif document_type == "employee" and employee_id:
            s3_path = f"{company_name}_{company_id}/employees/{employee_name}_{employee_id}/{filename}"
        else:
            raise ValueError("Invalid document type or missing employee ID.")

        s3.upload_fileobj(
            Fileobj=file,
            Bucket=os.getenv("DO_SPACE_BUCKET"),
            Key=s3_path,
            ExtraArgs={'ACL': 'public-read'}
        )

        file_url = f"{os.getenv('DO_SPACE_ENDPOINT')}/{os.getenv('DO_SPACE_BUCKET')}/{s3_path}"

        doc = cls(
            document_type=document_type,
            employee_id=employee_id,
            file_name=filename,
            file_label=file_label,
            file_url=file_url,
            uploaded_by=uploaded_by
        )
        session.add(doc)
        session.commit()
        return doc
    
    @classmethod
    def get_documents(cls, session, document_type=None, employee_id=None):
        """
        Retrieves documents from the company database.

        - If `employee_id` is provided, it filters by that employee.
        - If `document_type` is provided (e.g. 'company' or 'employee'), it filters by that type.
        - If neither is provided, it returns all documents.
        """
        query = session.query(cls)

        if document_type:
            query = query.filter(cls.document_type == document_type)

        if employee_id:
            query = query.filter(cls.employee_id == employee_id)

        return query.order_by(cls.uploaded_at.desc()).all()
    
    @classmethod
    def get_document_by_id(cls, session, document_id):
        """
        Retrieves a document by its ID.
        """
        return session.query(cls).filter(cls.document_id == document_id).first()
    
    @classmethod
    def download_document(cls, session, document_id):
        """
        Downloads a document from DigitalOcean Spaces using its ID.
        Returns the local path of the downloaded file.
        """
        s3 = cls.get_s3_client()
        document = cls.get_document_by_id(session, document_id)
        if not document:
            raise ValueError("Document not found.")
        file_url = document.file_url
        filename = file_url.split("/")[-1]
        local_path = os.path.join("/tmp", filename)
        print(f"Local path for download: {local_path}")

        endpoint = os.getenv("DO_SPACE_ENDPOINT")
        bucket = os.getenv("DO_SPACE_BUCKET")

        prefix = f"{endpoint}/{bucket}/"
        if not file_url.startswith(prefix):
            raise ValueError("Invalid file URL for configured DigitalOcean Space.")

        # Decode the key for S3
        s3_key = urllib.parse.unquote(file_url.replace(prefix, ""))
        print(f"Decoded S3 key: {s3_key}")

        s3.download_file(
            Bucket=bucket,
            Key=s3_key,
            Filename=local_path
        )

        return local_path


