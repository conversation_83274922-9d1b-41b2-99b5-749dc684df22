from app.models.company_base import DynamicBase
import uuid
from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.central import NsfContributions
from datetime import datetime, timedelta
import pytz # For time zone handling
from flask import current_app
from flask import flash
from sqlalchemy import and_
from sqlalchemy import Enum
import os
import tempfile
import calendar
from io import BytesIO

class PayrollApproval(DynamicBase):
    __tablename__ = "payroll_approvals"

    approval_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    payroll_id = Column(UUID(as_uuid=True), ForeignKey('payrolls.payroll_id'), nullable=False)
    approver_id = Column(UUID(as_uuid=True), nullable=False)  # Likely references an Employee ID or a User ID
    approver_role = Column(String(128), nullable=False)  # The role of the approver (e.g., HR Manager, Finance Head)
    status = Column(String(128), nullable=True, default='Pending')
    remarks = Column(Text, nullable=True)  # Optional comments from the approver
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    payroll = relationship(
        "Payroll",
        back_populates="payroll_approvals",
        foreign_keys=[payroll_id]
    )

    def __repr__(self):
        """String representation of the object."""
        return f"""
        "approval_id": "{self.approval_id}",
        "payroll_id": "{self.payroll_id}",
        "approver_id": "{self.approver_id}",
        "approver_role": "{self.approver_role}",
        "status": "{self.status}",
        "remarks": "{self.remarks}",
        "created_at": "{self.created_at}",
        "updated_at": "{self.updated_at}"
        """

    def to_dict(self):
        """To dictionary method. Converts the object to a dictionary."""
        # Convert the datetime objects to strings with the format DD/MM/YYYY
        created_at = self.created_at.strftime("%d/%m/%Y") if self.created_at else None
        return {
            "approval_id": self.approval_id,
            "payroll_id": self.payroll_id,
            "approver_id": self.approver_id,
            "approver_role": self.approver_role,
            "status": self.status,
            "remarks": self.remarks,
            "created_at": created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def create_approval(cls, db_session, payroll_id, approver_id, approver_role, status, remarks):
        """Create a new approval."""
        from app.models.company_approval_work_flow import ApprovalWorkflow
        new_approval = cls(
            payroll_id=payroll_id,
            approver_id=approver_id,
            approver_role=approver_role,
            status=status,
            remarks=remarks
        )
        approval_type = 'payroll'
        # Import the Payroll model here to avoid circular imports
        from app.models.company import Payroll

        # Fetch payroll request
        try:
            payroll = Payroll.get_payroll_by_id(db_session, payroll_id)
            print(f"payroll: {payroll} and the type is: {type(payroll)}")

            if not payroll:
                message = "Payroll request not found."
                return message
        except Exception as e:
            current_app.logger.error(f"An error occurred while fetching the payroll request: {str(e)}")
            message = "An error occurred while fetching the payroll request."
            return message

        print("Getting the payroll request")
        try:
            payroll_status = payroll.status
            print(f"payroll_status: {payroll_status}")
        except Exception as e:
            current_app.logger.error(f"An error occurred while fetching the payroll status: {str(e)}")
            message = "An error occurred while fetching the payroll status."
            return message

        # if the requests has been either approved or rejected, no further action is allowed
        if payroll_status in ['Approved', 'Rejected']:
            current_app.logger.error(f"The payroll request has already been {payroll.status}")
            message = f"The payroll request has already been {payroll.status}"
            return message

        current_app.logger.info("before getting the workflow")
        print("before getting the workflow")

        # Get the sequence for the approver's role
        workflow = ApprovalWorkflow.get_workflow_by_role_and_approval_type(
            db_session, approver_role, approval_type
        )
        current_app.logger.info(f"Retrieved workflow: {workflow}")
        if not workflow:
            return f"No approval sequence defined for the role: {approver_role}"

        sequence_order = workflow['sequence_order']
        current_app.logger.info(f"Retrieved sequence order: {sequence_order}")

        # Check if this role has already approved the leave
        existing_approval = db_session.query(cls).filter(
            and_(cls.payroll_id == payroll_id, cls.approver_role == approver_role)
        ).first()
        current_app.logger.info(f"Existing approval: {existing_approval}")
        if existing_approval:
            # Get the name of the person who already approved it
            try:
                from app.models.company import LeaveApproval
                existing_approver_name = LeaveApproval.get_approver_name(
                    existing_approval.approver_id,
                    existing_approval.approver_role,
                    db_session
                )
                if not existing_approver_name or existing_approver_name == "Unknown":
                    existing_approver_name = "Someone"
                return f"{existing_approver_name} ({approver_role}) has already approved this payroll request."
            except Exception as e:
                current_app.logger.warning(f"Could not fetch existing approver name: {str(e)}")
                return f"{approver_role} has already approved this payroll request."

        # Get the highest sequence order already approved
        try:
            approved_sequences = [
                ApprovalWorkflow.get_workflow_by_role_and_approval_type(
                    db_session, approval.approver_role, approval_type
                )['sequence_order']
                for approval in payroll.payroll_approvals
            ]
            current_app.logger.info(f"Approved sequences: {approved_sequences}")
        except Exception as e:
            current_app.logger.error(f"An error occurred while fetching approved sequences: {str(e)}")
            message = "An error occurred while fetching approved sequences."
            return message
        highest_approved_sequence = max(approved_sequences, default=0)
        current_app.logger.info(f"Highest approved sequence: {highest_approved_sequence}")

        # Check if the current approver is authorized
        if sequence_order != highest_approved_sequence + 1:
            return "You are not authorized to approve this Payroll at this stage."
        try:
            db_session.add(new_approval)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"An error occurred while creating an approval: {str(e)}")

        # Check if this is the final approval
        last_sequence_order = ApprovalWorkflow.get_the_last_sequence_order(db_session, approval_type)
        check = (sequence_order == last_sequence_order)
        current_app.logger.info(f"Check: {check}")
        if sequence_order == last_sequence_order:
            # If this is the final approval, update the status of the payroll request
            payroll.status = status
            db_session.commit()

            # If approved, upload payroll to Digital Ocean
            if status == 'Approved':
                try:
                    cls.upload_approved_payroll_to_digital_ocean(db_session, payroll, company_id)
                except Exception as e:
                    current_app.logger.error(f"Failed to upload payroll to Digital Ocean: {str(e)}")
                    # Don't fail the approval process if upload fails

            # If the status is 'Approved', generate and upload payslip, then send email
            if status == 'Approved':
                try:
                    # Import necessary modules
                    from app.helpers.pdf_generator import PDFGenerator
                    from app.models.company_documents import Document
                    from app.helpers.auxillary import Auxillary
                    from app.models.company import Employee
                    from app.models.central import Company
                    import tempfile
                    import os
                    from datetime import datetime
                    import calendar
                    from io import BytesIO
                    from flask import session

                    # Get employee data
                    employee = Employee.get_employee_by_id(db_session, payroll.employee_id)
                    if not employee:
                        current_app.logger.error(f"Employee not found for payroll ID: {payroll.payroll_id}")
                        message = f"The payroll request has been {status} successfully, but employee data could not be found."
                        return message
                    company_id = session.get('company_id')
                    # Get company data
                    company = Company.get_company_by_id(company_id)
                    if not company:
                        current_app.logger.error(f"Company not found for payroll ID: {payroll.payroll_id}")
                        message = f"The payroll request has been {status} successfully, but company data could not be found."
                        return message

                    # Prepare data for PDF generation
                    pay_date = payroll.pay_date
                    month = pay_date.month
                    year = pay_date.year
                    first_day = datetime(year, month, 1)
                    last_day = datetime(year, month, calendar.monthrange(year, month)[1])
                    formatted_first_day = first_day.strftime('%d/%m/%Y')
                    formatted_last_day = last_day.strftime('%d/%m/%Y')
                    days_in_month = calendar.monthrange(year, month)[1]

                    # Create employee data dictionary for PDF generation
                    employee_data = {
                        'employee': employee,
                        'basic_needed': payroll.basic_salary,
                        'gross_needed': payroll.gross_salary,
                        'paye': payroll.payee,
                        'pension_ee_value': payroll.employee_pension,
                        'pension_er_value': payroll.employer_pension,
                        'maternity_ee_value': payroll.employee_maternity,
                        'maternity_er_value': payroll.employer_maternity,
                        'rama_ee': payroll.medical_fee,
                        'cbhi_value': payroll.cbhi,
                        'total_deductions_value': payroll.total_deductions,
                        'net_salary_value': payroll.net_salary,
                        'total_reimbursements': payroll.reimbursement or 0,
                        'total_deductions': payroll.other_deductions or 0,
                        'brd_deduction': payroll.brd_deductions or 0,
                        'salary_advance': payroll.advance or 0
                    }

                    # Generate PDF
                    prepared_by = "Payroll System"
                    pdf_buffer = PDFGenerator.generate_payslip_pdf(
                        employee_data,
                        company,
                        pay_date,
                        formatted_first_day,
                        formatted_last_day,
                        days_in_month,
                        prepared_by
                    )

                    # Create a custom filename with employee name and pay date
                    employee_first_name = employee.get('first_name', '').replace(' ', '_')
                    employee_last_name = employee.get('last_name', '').replace(' ', '_')
                    month_year = pay_date.strftime('%B_%Y')

                    # Create a sanitized filename
                    custom_filename = f"Payslip_{employee_first_name}_{employee_last_name}_{month_year}.pdf"
                    custom_filename = ''.join(c for c in custom_filename if c.isalnum() or c in ['_', '-', '.'])

                    # Save PDF to a temporary file
                    temp_dir = tempfile.gettempdir()
                    temp_file_path = os.path.join(temp_dir, custom_filename)

                    with open(temp_file_path, 'wb') as temp_file:
                        temp_file.write(pdf_buffer.read())

                    # Upload the PDF to DigitalOcean Spaces
                    try:
                        # Create a file-like object from the temporary file that has a filename attribute
                        from io import BytesIO

                        # Read the file content
                        with open(temp_file_path, 'rb') as f:
                            file_content = f.read()

                        # Create a custom file-like object with a filename attribute
                        class FileWithName(BytesIO):
                            def __init__(self, content, name):
                                super().__init__(content)
                                self.filename = name

                        file_obj = FileWithName(file_content, custom_filename)

                        # Upload to DigitalOcean Spaces
                        document = Document.upload_document(
                            db_session,
                            company_id,
                            'employee',
                            file_obj,
                            'Payroll System',
                            employee_id=payroll.employee_id,
                            file_label=f"Payslip for {month_year}"
                        )

                        current_app.logger.info(f"Payslip uploaded to DigitalOcean Spaces: {document.file_url}")

                        # Send email if employee has an email address
                        body = f"""
                        Dear {employee.get('first_name', '')},

                        Please find attached your payslip for {pay_date.strftime('%B %Y')}.

                        If you have any questions regarding your payslip, please contact the HR department.

                        Best regards,
                        HR Team
                        """
                        subject = "Payslip"
                        if employee.get('email') is not None:
                            try:
                                sending = Auxillary.send_payslip_from_spaces(
                                    payroll.employee_id,
                                    document.file_url, 
                                    employee.get('email'),
                                    subject, body
                                )
                            except Exception as e:
                                current_app.logger.warning(f"Employee {employee.get('first_name')} {employee.get('last_name')} did not receive payslip email: {e}")
                        else:
                            current_app.logger.warning(f"Employee {employee.get('first_name')} {employee.get('last_name')} does not have an email address.")
                            flash(f"Employee {employee.get('first_name')} {employee.get('last_name')} does not have an email address.", "warning")
                    except Exception as e:
                        current_app.logger.error(f"Error uploading payslip to DigitalOcean Spaces: {e}")

                except Exception as e:
                    current_app.logger.error(f"Error processing payslip after approval: {e}")
                    import traceback
                    current_app.logger.error(traceback.format_exc())

            message = f"The payroll request has been {status} successfully."
            return message

        else:
            current_app.logger.info(f"Approval created successfully.")
            return "Approval created successfully."
        
    
    @classmethod
    def get_approval_by_id(cls, db_session, approval_id):
        """Fetch an approval by ID."""
        approval = db_session.query(cls).filter(cls.approval_id == approval_id).first()

        # convert to dictionary
        if approval:
            return approval.to_dict()
        return []

    @classmethod
    def bulk_approve_payrolls(cls, db_session, payroll_ids, approver_id, approver_role, status, remarks):
        """Bulk approve or reject multiple payrolls."""
        from app.models.company_approval_work_flow import ApprovalWorkflow
        from app.models.company import Payroll

        success_count = 0
        error_count = 0
        results = []

        for payroll_id in payroll_ids:
            try:
                # Use the existing create_approval method for each payroll
                response = cls.create_approval(
                    db_session, payroll_id, approver_id, approver_role, status, remarks
                )

                if response == "Approval created successfully." or "successfully" in response.lower():
                    success_count += 1
                    results.append({
                        'payroll_id': payroll_id,
                        'status': 'success',
                        'message': response
                    })
                else:
                    error_count += 1
                    results.append({
                        'payroll_id': payroll_id,
                        'status': 'error',
                        'message': response
                    })

            except Exception as e:
                error_count += 1
                current_app.logger.error(f"Error in bulk approval for payroll {payroll_id}: {str(e)}")
                results.append({
                    'payroll_id': payroll_id,
                    'status': 'error',
                    'message': str(e)
                })

        return {
            'success_count': success_count,
            'error_count': error_count,
            'total_processed': len(payroll_ids),
            'results': results
        }

    @classmethod
    def upload_approved_payroll_to_digital_ocean(cls, db_session, payroll, company_id):
        """Upload bulk approved payroll data to Digital Ocean - one file per month with all employees."""
        try:
            from app.models.company_documents import Document
            from app.models.central import Company as CentralCompany
            from app.models.company import Payroll
            import pandas as pd
            from io import BytesIO
            import calendar
            from sqlalchemy import extract

            current_app.logger.info(f"Starting bulk Digital Ocean upload for payroll month {payroll.pay_date}")

            # Get company information using existing helper (matches existing structure)
            from app.helpers.company_helpers import CompanyHelpers
            company_name = CompanyHelpers.get_company_name(company_id)

            # Get pay date info
            pay_date = payroll.pay_date
            year = pay_date.year
            month = pay_date.month
            pay_date_str = pay_date.strftime('%Y-%m')

            # Check if bulk file already exists for this month
            existing_doc = (db_session.query(Document)
                           .filter(Document.file_name == f"Approved_Payroll_{pay_date_str}.xlsx")
                           .filter(Document.document_type == 'company')
                           .first())

            # Get ALL approved payrolls for this month/year
            all_approved_payrolls = (db_session.query(Payroll)
                                   .filter(
                                       Payroll.status == 'Approved',
                                       extract('year', Payroll.pay_date) == year,
                                       extract('month', Payroll.pay_date) == month
                                   )
                                   .order_by(Payroll.employee_name)
                                   .all())

            if not all_approved_payrolls:
                current_app.logger.info(f"No approved payrolls found for {pay_date_str}")
                return None

            # Prepare bulk payroll data
            payroll_data = []
            approval_data = []

            for payroll_item in all_approved_payrolls:
                # Get all approvals for this payroll
                approvals = (db_session.query(cls)
                           .filter(cls.payroll_id == payroll_item.payroll_id)
                           .filter(cls.status == 'Approved')
                           .order_by(cls.created_at.asc())
                           .all())

                # Get "prepared by" information
                prepared_by_name = "Unknown"
                if payroll_item.created_by:
                    try:
                        from app.models.company import LeaveApproval
                        prepared_by_name = LeaveApproval.get_approver_name(
                            payroll_item.created_by,
                            'hr',
                            db_session
                        )
                        if not prepared_by_name or prepared_by_name == "Unknown":
                            prepared_by_name = "Unknown"
                    except Exception as e:
                        current_app.logger.warning(f"Could not fetch prepared by name: {str(e)}")
                        prepared_by_name = "Unknown"

                # Collect all approver information for this payroll
                approver_names = []
                approver_roles = []
                approval_dates = []

                for approval in approvals:
                    try:
                        from app.models.company import LeaveApproval
                        approver_name = LeaveApproval.get_approver_name(
                            approval.approver_id,
                            approval.approver_role,
                            db_session
                        )
                        if not approver_name or approver_name == "Unknown":
                            approver_name = "Unknown Approver"
                    except Exception as e:
                        current_app.logger.warning(f"Could not fetch approver name: {str(e)}")
                        approver_name = "Unknown Approver"

                    approver_names.append(approver_name)
                    approver_roles.append(approval.approver_role)
                    approval_dates.append(approval.created_at.strftime('%d/%m/%Y %H:%M') if approval.created_at else 'N/A')

                # Add payroll data row
                payroll_data.append({
                    'Employee Name': payroll_item.employee_name,
                    'Job Title': payroll_item.job_title or 'N/A',
                    'Basic Salary': int(payroll_item.basic_salary or 0),
                    'Transport Allowance': int(payroll_item.transport_allowance or 0),
                    'Housing Allowance': int(payroll_item.housing_allowance or 0),
                    'Gross Salary': int(payroll_item.gross_salary or 0),
                    'Employer Pension': int(payroll_item.employer_pension or 0),
                    'Employee Pension': int(payroll_item.employee_pension or 0),
                    'Employer Maternity': int(payroll_item.employer_maternity or 0),
                    'Employee Maternity': int(payroll_item.employee_maternity or 0),
                    'PAYE': int(payroll_item.payee or 0),
                    'CBHI': int(payroll_item.cbhi or 0),
                    'Total Deductions': int(payroll_item.total_deductions or 0),
                    'Net Salary': int(payroll_item.net_salary or 0),
                    'Other Deductions': int(payroll_item.other_deductions or 0),
                    'Advances': int(payroll_item.advance or 0),
                    'Reimbursements': int(payroll_item.reimbursement or 0),
                    'BRD Deductions': int(payroll_item.brd_deductions or 0),
                    'Net To Pay': int((payroll_item.net_salary or 0) - (payroll_item.other_deductions or 0) + (payroll_item.reimbursement or 0) - (payroll_item.brd_deductions or 0) - (payroll_item.advance or 0)),
                    'Prepared By': prepared_by_name,
                    'Prepared Date': payroll_item.created_at.strftime('%d/%m/%Y %H:%M') if payroll_item.created_at else 'N/A',
                    'All Approvers': " | ".join(approver_names) if approver_names else "Unknown",
                    'Approver Roles': " | ".join(approver_roles) if approver_roles else "Unknown",
                    'Approval Dates': " | ".join(approval_dates) if approval_dates else "N/A",
                    'Number of Approvals': len(approvals),
                    'Pay Date': payroll_item.pay_date.strftime('%d/%m/%Y') if payroll_item.pay_date else 'N/A',
                    'Status': payroll_item.status
                })

                # Add detailed approval history for each payroll
                for i, approval in enumerate(approvals, 1):
                    try:
                        from app.models.company import LeaveApproval
                        approver_name = LeaveApproval.get_approver_name(
                            approval.approver_id,
                            approval.approver_role,
                            db_session
                        )
                        if not approver_name or approver_name == "Unknown":
                            approver_name = "Unknown Approver"
                    except Exception as e:
                        current_app.logger.warning(f"Could not fetch approver name: {str(e)}")
                        approver_name = "Unknown Approver"

                    approval_data.append({
                        'Employee Name': payroll_item.employee_name,
                        'Approval Level': i,
                        'Approver Name': approver_name,
                        'Approver Role': approval.approver_role,
                        'Approval Date': approval.created_at.strftime('%d/%m/%Y %H:%M') if approval.created_at else 'N/A',
                        'Remarks': approval.remarks or 'No remarks'
                    })

            # Create Excel file with multiple sheets
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # All payroll data sheet
                payroll_df = pd.DataFrame(payroll_data)
                payroll_df.to_excel(writer, index=False, sheet_name=f'Payroll {calendar.month_name[month]} {year}')

                # Detailed approval history sheet
                if approval_data:
                    approval_df = pd.DataFrame(approval_data)
                    approval_df.to_excel(writer, index=False, sheet_name='Approval History')

                # Auto-adjust column widths
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width

            output.seek(0)

            # Create filename: Approved_Payroll_YYYY-MM.xlsx
            filename = f"Approved_Payroll_{pay_date_str}.xlsx"

            # Create a file-like object with filename attribute
            class FileWithName(BytesIO):
                def __init__(self, content, name):
                    super().__init__(content)
                    self.filename = name

            file_obj = FileWithName(output.getvalue(), filename)

            # Upload to Digital Ocean (matching existing folder structure)
            s3 = Document.get_s3_client()
            s3_path = f"{company_name}_{company_id}/payroll/{filename}"

            s3.upload_fileobj(
                Fileobj=file_obj,
                Bucket=os.getenv("DO_SPACE_BUCKET"),
                Key=s3_path,
                ExtraArgs={'ACL': 'public-read'}
            )

            file_url = f"{os.getenv('DO_SPACE_ENDPOINT')}/{os.getenv('DO_SPACE_BUCKET')}/{s3_path}"

            # Update or create document record
            if existing_doc:
                # Update existing document
                existing_doc.file_url = file_url
                existing_doc.uploaded_by = 'Payroll System (Auto-upload - Updated)'
                db_session.commit()
                current_app.logger.info(f"Updated existing bulk payroll file: {file_url}")
            else:
                # Create new document record
                doc = Document(
                    document_type='company',
                    employee_id=None,
                    file_name=filename,
                    file_label=f"Approved Payroll - {calendar.month_name[month]} {year} (All Employees)",
                    file_url=file_url,
                    uploaded_by='Payroll System (Auto-upload)'
                )
                db_session.add(doc)
                db_session.commit()
                current_app.logger.info(f"Created new bulk payroll file: {file_url}")

            return file_url

        except Exception as e:
            current_app.logger.error(f"Error uploading bulk payroll to Digital Ocean: {str(e)}")
            raise e

    @classmethod
    def get_all_approvals(cls, db_session):
        """Fetch all approvals."""
        approvals = db_session.query(cls).all()

        # convert to dictionary
        if approvals:
            return [approval.to_dict() for approval in approvals]
        return []
