import uuid
from sqlalchemy import <PERSON>umn, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON>loat, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.central import NsfContributions
from datetime import datetime, timedelta
import pytz # For time zone handling
from flask import current_app
from flask import flash
from sqlalchemy import and_
from app.models.central import User as CentralUser
from app.models.company_base import DynamicBase

class ApprovalWorkflow(DynamicBase):
    __tablename__ = "approval_workflows"
    workflow_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    approval_type = Column(String, nullable=False)  
    role = Column(String, nullable=False)  
    sequence_order = Column(Integer, nullable=False)  
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        """Return a representation of the object."""
        return f"""
        workflow_id: {self.workflow_id},
        approval_type: {self.approval_type},
        role: {self.role},
        sequence_order: {self.sequence_order},
        created_at: {self.created_at},
        updated_at: {self.updated_at}
        """
    
    def to_dict(self):
        """Return a dictionary representation of the object."""
        update_at = self.updated_at.strftime("%d-%m-%Y") if self.updated_at else None
        created_at = self.created_at.strftime("%d-%m-%Y") if self.created_at else None
        return {
            "workflow_id": self.workflow_id,
            "approval_type": self.approval_type,
            "role": self.role,
            "sequence_order": self.sequence_order,
            "created_at": created_at,
            "updated_at": update_at
        }
    
    @classmethod
    def create_workflow(cls, db_session, approval_type, role, sequence_order):
        """
        Create a new workflow, ensuring uniqueness of:
        - `approval_type` and `role` pairing
        - Consistent `sequence_order` for the same `approval_type` and `role`
        """
        # Check if the workflow violates uniqueness rules
        if not cls.is_workflow_unique(db_session, approval_type, role, sequence_order):
            return {
                "error": f"Cannot create workflow. Either the sequence order '{sequence_order}' "
                        f"is already assigned for approval type '{approval_type}', or the role '{role}' "
                        f"is already associated with a conflicting sequence order."
            }

        try:
            # Create and save the new workflow
            new_workflow = cls(
                approval_type=approval_type,
                role=role,
                sequence_order=sequence_order
            )
            db_session.add(new_workflow)
            db_session.commit()
            return new_workflow.to_dict()
        except Exception as e:
            # Handle exceptions and rollback
            current_app.logger.error(f"Error creating workflow: {e}")
            db_session.rollback()
            return {"error": "An error occurred while creating the workflow."}

        
    @classmethod
    def is_workflow_unique(cls, db_session, approval_type, role, sequence_order):
        """
        Check if the workflow is unique.
        Enforces:
        - Each `approval_type` can have a specific `role` assigned only once.
        - The same `approval_type` and `role` cannot have different `sequence_order` values.
        - A `sequence_order` cannot be assigned more than once within the same `approval_type`.
        """
        # Check if any workflow exists with the same approval_type and role
        existing_workflow = db_session.query(cls).filter(
            and_(
                cls.approval_type == approval_type,
                cls.role == role
            )
        ).first()

        # If a workflow exists with the same approval_type and role
        if existing_workflow:
            # Ensure the sequence_order matches
            if existing_workflow.sequence_order != sequence_order:
                return False  # Not unique if sequence_order differs

        # Check if the sequence_order already exists for this approval_type
        sequence_conflict = db_session.query(cls).filter(
            and_(
                cls.approval_type == approval_type,
                cls.sequence_order == sequence_order
            )
        ).first()

        if sequence_conflict:
            return False  # Not unique if sequence_order is already taken

        # If no conflicts are found, the workflow is unique
        return True


    
    @classmethod
    def get_workflow_by_role(cls, db_session, role):
        """Get workflow by role."""
        try:
            work_flow = db_session.query(cls).filter_by(role=role).first()
            converted = work_flow.to_dict()
            return converted
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflow by role: {e}")
            return []
        
    @classmethod
    def get_workflow_sequence(cls, db_session, approval_type):
        """Retrieve the workflows for the specified approval type in sequence order."""
        try:
            workflows = (
                db_session.query(cls)
                .filter_by(approval_type=approval_type)
                .order_by(cls.sequence_order)
                .all()
            )
            return [workflow.to_dict() for workflow in workflows]
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflow sequence for approval type '{approval_type}': {e}")
            return []
    @classmethod
    def get_workflow_by_role_and_approval_type(cls, db_session, role, approval_type):
        """Get workflow by role and approval type."""
        try:
            work_flow = db_session.query(cls).filter_by(role=role, approval_type=approval_type).first()
            converted = work_flow.to_dict()
            return converted
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflow by role and approval type: {e}")
            return []
        
    @classmethod
    def get_workflow_by_id(cls, db_session, workflow_id):
        """Get workflow by id."""
        try:
            work_flow = db_session.query(cls).filter_by(workflow_id=workflow_id).first()
            if work_flow:
                converted = work_flow.to_dict()
                return converted
            return []
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflow by id: {e}")
            return []
        
    @classmethod
    def get_all_workflows(cls, db_session):
        """Get all workflows."""
        try:
            workflows = db_session.query(cls).all()
            converted = [workflow.to_dict() for workflow in workflows]
            return converted
        except Exception as e:
            current_app.logger.error(f"Error retrieving all workflows: {e}")
            return []
        
    @classmethod
    def get_the_last_sequence_order(cls, db_session, approval_type):
        """Get the last sequence order."""
        try:
            work_flow = db_session.query(cls).filter_by(approval_type=approval_type).order_by(cls.sequence_order.desc()).first()
            return work_flow.sequence_order
        except Exception as e:
            current_app.logger.error(f"Error retrieving the last sequence order: {e}")
            return []
        
    @classmethod
    def update_workflow(cls, db_session, workflow_id, approval_type, role, sequence_order):
        """Update a workflow."""
        try:
            work_flow = db_session.query(cls).filter_by(workflow_id=workflow_id).first()
            work_flow.approval_type = approval_type
            work_flow.role = role
            work_flow.sequence_order = sequence_order
            db_session.commit()
            return work_flow.to_dict()
        except Exception as e:
            current_app.logger.error(f"Error updating workflow: {e}")
            db_session.rollback()
            return []
        
    @classmethod
    def delete_workflow(cls, db_session, workflow_id):
        """Delete a workflow."""
        try:
            work_flow = db_session.query(cls).filter_by(workflow_id=workflow_id).first()
            db_session.delete(work_flow)
            db_session.commit()
            return True
        except Exception as e:
            current_app.logger.error(f"Error deleting workflow: {e}")
            db_session.rollback()
            return False


class ApprovalLog(DynamicBase):
    __tablename__ = "approval_logs"
    log_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    application_id = Column(UUID(as_uuid=True), ForeignKey('leave_applications.leave_id'), nullable=False)
    approver_role = Column(String, nullable=False) 
    approver_id = Column(UUID(as_uuid=True), nullable=True)
    status = Column(String, default='pending')  # 'pending', 'approved', 'rejected'
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    LeaveApplication = relationship("LeaveApplication", back_populates="ApprovalLogs")

    def __repr__(self):
        """Return a representation of the object."""
        return f"""
        log_id: {self.log_id},
        application_id: {self.application_id},
        approver_role: {self.approver_role},
        approver_id: {self.approver_id},
        status: {self.status},
        created_at: {self.created_at},
        updated_at: {self.updated_at}
        """
    
    def to_dict(self):
        """Return a dictionary representation of the object."""
        update_at = self.updated_at.strftime("%d-%m-%Y") if self.updated_at else None
        created_at = self.created_at.strftime("%d-%m-%Y") if self.created_at else None
        return {
            "log_id": self.log_id,
            "application_id": self.application_id,
            "approver_role": self.approver_role,
            "approver_id": self.approver_id,
            "status": self.status,
            "created_at": created_at,
            "updated_at": update_at
        }
    
    @classmethod
    def create_log(cls, db_session, application_id, approver_role, approver_id=None, status='pending'):
        """
        Create a new log entry.
        """
        try:
            # Create and save the new log entry
            new_log = cls(
                application_id=application_id,
                approver_role=approver_role,
                approver_id=approver_id,
                status=status
            )
            db_session.add(new_log)
            db_session.commit()
            return new_log.to_dict()
        except Exception as e:
            # Handle exceptions and rollback
            current_app.logger.error(f"Error creating log: {e}")
            db_session.rollback()
            return {"error": "An error occurred while creating the log."}
        
    @classmethod
    def get_log_by_id(cls, db_session, log_id):
        """Get log by id."""
        try:
            log = db_session.query(cls).filter_by(log_id=log_id).first()
            converted = log.to_dict()
            return converted
        except Exception as e:
            current_app.logger.error(f"Error retrieving log by id: {e}")
            return []
        
    @classmethod
    def get_all_logs(cls, db_session):
        """Get all logs."""
        try:
            logs = db_session.query(cls).all()
            converted = [log.to_dict() for log in logs]
            return converted
        except Exception as e:
            current_app.logger.error(f"Error retrieving all logs: {e}")
            return []
        
    @classmethod
    def update_log(cls, db_session, log_id, status):
        """Update a log."""
        try:
            log = db_session.query(cls).filter_by(log_id=log_id).first()
            log.status = status
            db_session.commit()
            return log.to_dict()
        except Exception as e:
            current_app.logger.error(f"Error updating log: {e}")
            db_session.rollback()
            return []

