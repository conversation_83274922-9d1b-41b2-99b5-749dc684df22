import uuid
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, DateTime, ForeignKey, Integer, Text, Numeric, case, update, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.central import NsfContributions, Company
from app.utils.db_connection import DatabaseConnection
from datetime import datetime, timedelta, date
import pytz # For time zone handling
from flask import current_app
from flask import flash
import calendar
from sqlalchemy import and_, or_, Numeric
from dotenv import load_dotenv
import os
import requests
from app.models.central import User as CentralUser
import hashlib
from sqlalchemy import Enum
from sqlalchemy import Boolean
from app.models.company_base import DynamicBase
from app.models.company_salary_advance import SalaryAdvanceRequest
from app.models.company_payroll_approval import PayrollApproval
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from decimal import Decimal
import math
from collections import OrderedDict
from datetime import date
from app.helpers.auxillary import Auxillary
from app.routes.payroll.goal_Seek_mine import SalaryCalculatorGross
from app.models.central import NsfContributions
from flask import current_app
load_dotenv()

class Employee(DynamicBase):

    __tablename__ = 'employees'
    employee_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), nullable=False)
    first_name = Column(String(128), nullable=False)
    last_name = Column(String(128), nullable=False)
    nid = Column(String(16), unique=True, nullable=False)
    nsf = Column(String(16), unique=True, nullable=False)
    bank_name = Column(String(128), nullable=True)
    bank_account = Column(String(128), nullable=True)
    branch_name = Column(String(128), nullable=True)
    account_name = Column(String(128), nullable=True)
    birth_date = Column(DateTime, nullable=True)
    marital_status = Column(String(16), nullable=True)
    gender = Column(String(16), nullable=False)
    employee_tin = Column(String(16),  nullable=False, default="*********")
    employee_type = Column(String(16), nullable=False)
    department = Column(String(128), nullable=True)
    is_active = Column(String(16), nullable=True, default="yes")
    is_brd_sponsored = Column(String(16), nullable=True, default="no")
    attendance_applicable = Column(String(16), nullable=True, default="yes")
    net_salary = Column(Numeric(precision=18, scale=6), nullable=True)
    gross_salary = Column(Numeric(precision=18, scale=6), nullable=True)
    total_staff_cost = Column(Numeric(precision=18, scale=6), nullable=True)
    basic_salary = Column(Numeric(precision=18, scale=6), nullable=True)
    transport_allowance = Column(Numeric(precision=12, scale=6), nullable=True)
    housing_allowance = Column(Numeric(precision=12, scale=6), nullable=True)
    communication_allowance = Column(Numeric(precision=12, scale=6), nullable=True)
    over_time = Column(Numeric(precision=12, scale=6), nullable=True)
    other_allowance = Column(Numeric(precision=12, scale=6), nullable=True)
    salary_advance_balance = Column(Numeric(precision=12, scale=6), nullable=True, default=0)

    # Salary calculation method and percentage fields
    salary_calculation_method = Column(String(20), nullable=True, default='fixed_amounts')
    basic_salary_percentage = Column(Numeric(precision=5, scale=2), nullable=True)
    transport_allowance_percentage = Column(Numeric(precision=5, scale=2), nullable=True)
    housing_allowance_percentage = Column(Numeric(precision=5, scale=2), nullable=True)
    communication_allowance_percentage = Column(Numeric(precision=5, scale=2), nullable=True)

    email = Column(String(128), unique=True, nullable=True, default=None)
    phone = Column(String(20), nullable=True, unique=True, default=None)
    job_title = Column(String(128), nullable=True)
    hire_date = Column(DateTime, nullable=True)
    contract_end_date = Column(DateTime, nullable=True)
    annual_leave_balance=Column(Numeric(precision=12, scale=9), nullable=True) #Like 20/12=1.666666667
    extra_leave_days = Column(Integer, nullable=True)
    site_id = Column(UUID(as_uuid=True), ForeignKey('sites.site_id'), nullable=True)
    payrolls = relationship("Payroll", back_populates="employee")
    attendance = relationship("Attendance", back_populates="Employee")
    users = relationship("User", back_populates="employee")
    Sites = relationship("Site", back_populates="employees")
    leave_applications = relationship(
        "LeaveApplication",
        back_populates="Employee",
        cascade="all, delete-orphan")
    salary_advance_requests = relationship(
        "SalaryAdvanceRequest",
        back_populates="Employee",
        cascade="all, delete-orphan"
    )
    documents = relationship(
    "Document",
    back_populates="employee",
    cascade="all, delete-orphan"
)


    def __repr__(self):
        """Return a string representation of the object."""
        return f"{self.first_name} {self.last_name}"

    def to_dict(self):
        """Convert employee object to dictionary."""
        # format the dates to dd/mm/yyyy
        birth_date = self.birth_date.strftime('%d/%m/%Y') if self.birth_date else None
        hire_date = self.hire_date.strftime('%d/%m/%Y') if self.hire_date else None
        contract_end_date = self.contract_end_date.strftime('%d/%m/%Y') if self.contract_end_date else None

         # Coalesce allowances to zero
        if not self.transport_allowance:
            self.transport_allowance = Decimal(0.0)
        if not self.housing_allowance:
            self.housing_allowance = Decimal(0.0)
        if not self.communication_allowance:
            self.communication_allowance = Decimal(0.0)
        if not self.over_time:
            self.over_time = Decimal(0.0)
        if not self.other_allowance:
            self.other_allowance = Decimal(0.0)
        if not self.annual_leave_balance:
            self.annual_leave_balance = Decimal(0.0)
        if not self.extra_leave_days:
            self.extra_leave_days = 0
        if not self.salary_advance_balance:
            self.salary_advance_balance = Decimal(0.0)
        return {
            "employee_id": self.employee_id,
            "company_id": self.company_id,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}",
            "nid": self.nid,
            "nsf": self.nsf,
            "bank_name": self.bank_name,
            "bank_account": self.bank_account,
            "branch_name": self.branch_name,
            "account_name": self.account_name,
            "birth_date": birth_date,
            "marital_status": self.marital_status,
            "gender": self.gender,
            "employee_tin": self.employee_tin,
            "employee_type": self.employee_type,
            "department": self.department,
            "net_salary": self.net_salary,
            "gross_salary": self.gross_salary,
            "total_staff_cost": self.total_staff_cost,
            "basic_salary": self.basic_salary,
            "transport_allowance": self.transport_allowance,
            "housing_allowance": self.housing_allowance,
            "communication_allowance": self.communication_allowance,
            "over_time": self.over_time,
            "other_allowance": self.other_allowance,
            "salary_calculation_method": self.salary_calculation_method,
            "basic_salary_percentage": self.basic_salary_percentage,
            "transport_allowance_percentage": self.transport_allowance_percentage,
            "housing_allowance_percentage": self.housing_allowance_percentage,
            "communication_allowance_percentage": self.communication_allowance_percentage,
            "is_active": self.is_active,
            "is_brd_sponsored": self.is_brd_sponsored,
            "attendance_applicable": self.attendance_applicable,
            "email": self.email,
            "phone": self.phone,
            "job_title": self.job_title,
            "hire_date": hire_date,
            "contract_end_date": contract_end_date,
            "site_id": self.site_id,
            "total_allowances": self.transport_allowance + self.housing_allowance + self.communication_allowance + self.over_time + self.other_allowance,
            "allowances": self.housing_allowance + self.communication_allowance + self.over_time + self.other_allowance,
            "salary_advance_balance": self.salary_advance_balance,
            "annual_leave_balance":self.annual_leave_balance,
            "extra_leave_days": self.extra_leave_days,
            "salary_advance_requests": [advance.to_dict() for advance in self.salary_advance_requests]
        }

    @classmethod
    def update_employees_for_all_companies(cls, companies_db, all_employees):
        total_updated = 0
        current_app.logger.info(f"All Employees: {all_employees}")

        # Initialize the Database Connection
        db_connection = DatabaseConnection()

        for database_name in companies_db:
            current_app.logger.info(f"Updating employees for database: {database_name}")

            # new session for each database
            with db_connection.get_session(database_name) as db_session:
                try:
                    active_company = CompanyHelpers.get_company_by_database_name(database_name)
                    company_leave_days = int(active_company.total_annual_leave_days) if active_company.total_annual_leave_days else 18
                    current_app.logger.info(f"Company leave days: {company_leave_days}")

                    # These are list of changes that will be applied to each employee in the database based on their ID
                    mappings = [
                        {
                            "employee_id": str(employee.get("employee_id")),
                            "increment_value": (company_leave_days + int(employee.get("extra_leave_days") if employee.get("extra_leave_days") else 0)) / 12
                        } for employee in all_employees if str(employee.get("company_id")) == str(active_company.company_id)
                    ]
                    current_app.logger.info(f"All mappings: {mappings}")
                    updated_rows = cls.bulk_annual_leave_update(db_session, 'annual_leave_balance', mappings)
                    total_updated += updated_rows

                    # Commit changes for the current database
                    db_session.commit()
                    current_app.logger.info(f"Successfully updated employees for database: {database_name}")
                except Exception as e:
                    db_session.rollback()
                    current_app.logger.error(f"Failed to update employees for database {database_name}: {e}")
                    return 0
        return total_updated

    @staticmethod
    def create_extra_leave_days_mappings(mappings: list, employee: dict, company_leave_data: dict, today: date):
        """
            Create mappings for extra leave days increment for an employee based on their hire date and company leave policy.

            Args:
                mappings (list): A list to store employee leave increment mappings.
                employee (dict): A dictionary containing employee details, including 'employee_id' and 'hire_date'.
                company_leave_data (dict): A dictionary containing the company's leave increment policies.
                today (date): The current date to compare against the employee's hire anniversary.

            Returns:
                list: The updated mappings list with any new leave increment entries for the employee.

            This function calculates the number of years an employee has been in service based on their hire date.
            If today is the anniversary of their hire date, it checks the company's leave policy and adds an
            increment entry to the mappings if applicable. The default policy increments leave days by 1 every
            3 years if no specific company policy exists.
        """
        print("Got this company data: ", company_leave_data, employee, type(employee))
        hire_date = datetime.strptime(employee.get("hire_date"), "%d/%m/%Y")
        new_year_date = hire_date.replace(year=today.year)

        # Check if today matches their "new year"
        if new_year_date.date() == today:
            years_of_service = (new_year_date - hire_date).days // 365
            after_years = 3
            dividend = years_of_service / after_years
            remainder = years_of_service % after_years

            if not company_leave_data.get("increment_policy").get("after_years"):
                # This company uses default leave policy - Increment by 1 every after 3 years
                # is_eligible helps to avoid incrementing twice
                is_eligible = (dividend == 1  and employee.get("extra_leave_days", 0) >= 1) or \
                    (dividend == 2 and employee.get("extra_leave_days") or 0 >= 2) or \
                    (dividend == 3 and employee.get("extra_leave_days") or 0  == 3)

                if not is_eligible:
                    if remainder == 0 and employee.get("extra_leave_days", 0) <= 3: # Not exceeding 21 days
                        mappings.append({
                            "employee_id": str(employee.get("employee_id")),
                            "increment_value": 1
                        })
            else:
                for policy_name, policy_data in company_leave_data.items():
                    # For custom leave policy increment with 1 every x years
                    if not policy_name.startswith("increment_policy"):
                        continue
                    after_years = policy_data.get("after_years")

                    # Check if the employee has already received this incrementation
                    current_extra_leave_days = employee.get("extra_leave_days") or 0
                    dividend = years_of_service / after_years

                    current_app.logger.info(f"This is extra leave days: {current_extra_leave_days}")
                    # Ensure the employee has not already received this increment
                    if years_of_service % after_years == 0 and dividend > current_extra_leave_days and current_extra_leave_days <= 3:
                        mappings.append({
                            "employee_id": str(employee.get("employee_id")),
                            "increment_value": dividend - current_extra_leave_days
                        })
        return mappings

    @classmethod
    def update_extra_leave_days_for_all_companies(cls, companies_db, all_employees):
        total_updated = 0
        mappings = []
        today = datetime.now().date()

        # Initialize the Database Connection
        db_connection = DatabaseConnection()

        for database_name in companies_db:
            mappings = []
            current_app.logger.info(f"Updating employees for database: {database_name}")

            # new session for each database
            with db_connection.get_session(database_name) as db_session:
                try:
                    active_company = CompanyHelpers.get_company_by_database_name(database_name)
                    company_leave_data = active_company.leave_data
                    # These are list of changes that will be applied to each employee in the database based on the ID
                    for employee in all_employees:
                        if str(employee.get("company_id")) != str(active_company.company_id):
                            continue
                        # Skip employee if they don't have a hire date / HR will update it manually
                        if not employee.get("hire_date"):
                            continue

                        mappings = Employee.create_extra_leave_days_mappings(mappings, employee, company_leave_data, today)

                    current_app.logger.info(f"All mappings: {mappings}")
                    updated_rows = cls.bulk_annual_leave_update(db_session, 'extra_leave_days', mappings)
                    total_updated += updated_rows

                    # Commit changes for the current database
                    db_session.commit()
                    current_app.logger.info(f"Successfully updated employees for database: {database_name}")

                except Exception as e:
                    db_session.rollback()
                    current_app.logger.error(f"Failed to update employees for database {database_name}: {e}")
                    return 0
            print(f"After one db: {database_name}, total_updated is: {total_updated}")
        return total_updated


    @property
    def can_apply_for_leave(self):
        """Check if the employee is allowed to apply for leave based on annual leave balance."""
        return self.annual_leave_balance is not None and self.annual_leave_balance > 0

    @classmethod
    def can_request_leave_or_off(cls, db_session, employee_id, requested_days):
        """
        Check if the employee can request the specified number of leave or off days.
        """
        if not isinstance(requested_days, int) or requested_days <= 0:
            return False, "Requested days must be a positive integer."

        employee = db_session.query(cls).filter_by(employee_id=employee_id).first()
        if not employee:
            return False, "Employee with this id not found"

        hire_date = employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else None

        # We can only automate employee with hire date
        if hire_date:
            employee_company = [cmp for cmp in CompanyHelpers.get_companies() \
                                if str(cmp.company_id) == str(employee.company_id)][0]
            today = str(date.today())
            difference_in_days = Auxillary.calculate_days_difference(hire_date, today)
            is_qualified =(difference_in_days / 365) >= int(employee_company.to_dict().get("leave_data")\
                                                            .get("initial_qualification_period").get("value"))
            if not is_qualified: return False, "Employee is not eligible to apply for leave."

        if not employee.can_apply_for_leave:
            return False, "Employee is not eligible to apply for leave."
        if employee.annual_leave_balance < requested_days:
            return False, f"Insufficient leave balance. Available: {employee.annual_leave_balance}, Requested: {requested_days}."

        return True, "Employee is eligible for leave application"

    @classmethod
    def update_employee_leave_balance(cls, db_session, employee_id, requested_days):
        """
        Updates the annual leave balance of an employee by subtracting the requested days.
        Args:
            db_session (Session): The database session to use for querying and updating the employee record.
                                  This ensures the same session is used during the operation, allowing for a rollback
                                  in case of any errors.
            employee_id (int): The ID of the employee whose leave balance is to be updated.
        Returns:
            tuple: A tuple containing a boolean indicating success or failure, and a message string.
        Special Remark:
            The `db_session` argument is used to ensure the same session is maintained during the leave approval process.
            This allows for a rollback in case of any errors, ensuring data consistency.
        """

        # Subtract requested days from the leave balance and update the database
        employee = db_session.query(cls).filter_by(employee_id=employee_id).first()
        if not employee:
            return False, "Employee with this id not found"

        try:
            employee.annual_leave_balance -= requested_days
            db_session.commit()
            current_app.logger.info("")
            return True, "Leave request approved and balance updated."
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Error in can_request_leave_or_off: {e}")
            return False, f"Failed to update leave balance: {e}"

    @classmethod
    def get_employees(cls, db_session):
        """Get all active employees from the database where is_active= yes."""
        try:
            employees = db_session.query(cls).filter_by(is_active="yes").all()
            # current_app.logger.info(f"Successfully retrieved employees: {employees}")
            return [employee.to_dict() for employee in employees]
        except Exception as e:
            current_app.logger.error(f"Failed to retrieve employees: {e}")
            return []

    @staticmethod
    def search_employee(db_session, param):
        if not param:
            return []

        query = db_session.query(Employee).filter(
            or_(
                Employee.first_name.ilike(f"%{param}%"),
                Employee.last_name.ilike(f"%{param}%"),
                Employee.email.ilike(f"%{param}%")
            )
        ).all()
        print("\nGot these: ", query, "\n\n")
        return [employee.to_dict() for employee in query]

    @classmethod
    def get_inactive_employees(cls, db_session):
        """Get all inactive employees from the database where is_active= no."""
        employees = db_session.query(cls).filter_by(is_active="no").all()
        return [employee.to_dict() for employee in employees]


    @classmethod
    def flag_as_inactive(cls, db_session, employee_id):
        """Flag a user as inactive."""
        employee = db_session.query(cls).filter_by(employee_id=employee_id).first()
        if not employee:
            return None
        
        employee.is_active = "no"
        db_session.commit()
        return employee            
    @classmethod
    
    def make_employee_active(cls, db_session, employee_id):
        """Flag a user as inactive."""
        employee = db_session.query(cls).filter_by(employee_id=employee_id).first()
        if not employee:
            return None
        
        employee.is_active = "yes"
        db_session.commit()
        return employee            
        
    @classmethod
    def get_employee_by_id(cls, db_session, employee_id):
        """Get an employee by ID."""
        employee = db_session.query(cls).get(employee_id)
        if employee:
            return employee.to_dict()
        else:
            return {}

    @classmethod
    def get_employee_by_their_id(cls, db_session, employee_id):
        """Get an employee by ID."""
        employee = db_session.query(cls).filter_by(employee_id=employee_id).first()
        # convert to a dictionary
        return employee.to_dict()

    @classmethod
    def get_employee_by_email(cls, db_session, email):
        """Get an employee by email."""
        employee = db_session.query(cls).filter_by(email=email).first()
        # convert to a dictionary
        return employee

    @classmethod
    def count_employees(cls, db_session):
        """Count the number of employees."""
        return db_session.query(cls).count()

    @classmethod
    def deactivate_or_activate_employee(cls, db_session, employee_id, is_active):
        """Deactivate or activate an employee."""
        employee = db_session.query(cls).filter_by(employee_id=employee_id).first()
        if not employee:
            return None
        employee.is_active = is_active
        db_session.commit()
        return employee


    @classmethod
    def add_employee(
        cls, db_session, company_id,
        first_name, last_name, nid, nsf,
        bank_name, bank_account, branch_name,
        account_name, birth_date, marital_status, gender,
        employee_tin, employee_type, department, net_salary,
        transport_allowance, housing_allowance,
        communication_allowance, over_time,
        other_allowance, email, phone, job_title,
        hire_date, annual_leave_balance, extra_leave_days,
        gross_salary=None, total_staff_cost=None, contract_end_date=None
    ):
        """Add an employee to the database."""
        if not birth_date:
            birth_date = None
        if not hire_date:
            hire_date = None
        if not contract_end_date:
            contract_end_date = None
        print(f"Received this data: {annual_leave_balance}, {extra_leave_days}")
        employee = cls(
            company_id=company_id,
            first_name=first_name,
            last_name=last_name,
            nid=nid,
            nsf=nsf,
            bank_name=bank_name,
            bank_account=bank_account,
            branch_name=branch_name,
            account_name=account_name,
            birth_date=birth_date,
            marital_status=marital_status,
            gender=gender,
            employee_tin=employee_tin,
            employee_type=employee_type,
            department=department,
            net_salary=net_salary,
            transport_allowance=transport_allowance,
            housing_allowance=housing_allowance,
            communication_allowance=communication_allowance,
            over_time=over_time,
            other_allowance=other_allowance,
            email=email,
            phone=phone,
            job_title=job_title,
            hire_date=hire_date,
            gross_salary=gross_salary,
            total_staff_cost=total_staff_cost,
            annual_leave_balance=annual_leave_balance,
            extra_leave_days=extra_leave_days,
            contract_end_date=contract_end_date
        )
        try:
            current_app.logger.info(f"Attempting to add employee: {first_name} {last_name}\n,\
                                     NID: {nid}, Email: {email}, Annual Leave Balance: {annual_leave_balance}\n,\
                                       Extra Leave Days: {extra_leave_days}")
            db_session.add(employee)
            db_session.commit()
            current_app.logger.info(f"Successfully added employee: {first_name} {last_name}, NID: {nid}, Email: {email}")
            return employee
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Failed to add employee {first_name} {last_name}: {e}")
            raise  # Re-raise the exception to ensure it's handled or logged upstream

    @classmethod
    def add_employee_percentage_from_dict(cls, db_session, employee_data):
        """
        Add an employee with percentage-based salary calculations using a data dictionary.

        Args:
            db_session: Database session
            employee_data (dict): Complete employee data dictionary

        Returns:
            Employee: Created employee object or None if failed
        """
        try:
            # Extract data from dictionary with defaults
            first_name = employee_data.get('first_name')
            last_name = employee_data.get('last_name')
            nid = employee_data.get('nid')
            email = employee_data.get('email')

            # Handle date fields
            birth_date = employee_data.get('birth_date')
            hire_date = employee_data.get('hire_date')
            contract_end_date = employee_data.get('contract_end_date')
            if not birth_date:
                birth_date = None
            if not hire_date:
                hire_date = None
            if not contract_end_date:
                contract_end_date = None

            # Log the employee registration attempt
            current_app.logger.info(f"Attempting to add employee with percentage-based calculation: {first_name} {last_name}")
            current_app.logger.info(f"  - NID: {nid}")
            current_app.logger.info(f"  - Email: {email}")
            current_app.logger.info(f"  - Input Salary Type: {employee_data.get('input_salary_type')}")
            current_app.logger.info(f"  - Input Salary Amount: {employee_data.get('input_salary_amount')}")
            current_app.logger.info(f"  - Calculated Gross Salary: {employee_data.get('gross_salary')}")
            current_app.logger.info(f"  - Calculated Basic Salary: {employee_data.get('basic_salary')}")
            current_app.logger.info(f"  - Annual Leave Balance: {employee_data.get('annual_leave_balance')}")
            current_app.logger.info(f"  - Extra Leave Days: {employee_data.get('extra_leave_days')}")

            # Create employee object with all data
            employee = cls(
                company_id=employee_data.get('company_id'),
                first_name=first_name,
                last_name=last_name,
                nid=nid,
                nsf=employee_data.get('nsf'),
                bank_name=employee_data.get('bank_name'),
                bank_account=employee_data.get('bank_account'),
                branch_name=employee_data.get('branch_name'),
                account_name=employee_data.get('account_name'),
                birth_date=birth_date,
                marital_status=employee_data.get('marital_status'),
                gender=employee_data.get('gender'),
                employee_tin=employee_data.get('employee_tin'),
                employee_type=employee_data.get('employee_type'),
                department=employee_data.get('department'),
                net_salary=employee_data.get('net_salary'),
                basic_salary=employee_data.get('basic_salary'),
                transport_allowance=employee_data.get('transport_allowance'),
                housing_allowance=employee_data.get('housing_allowance'),
                communication_allowance=employee_data.get('communication_allowance'),
                over_time=employee_data.get('over_time'),
                other_allowance=employee_data.get('other_allowance'),
                salary_calculation_method=employee_data.get('salary_calculation_method', 'percentage_based'),
                basic_salary_percentage=employee_data.get('basic_salary_percentage'),
                transport_allowance_percentage=employee_data.get('transport_allowance_percentage'),
                housing_allowance_percentage=employee_data.get('housing_allowance_percentage'),
                communication_allowance_percentage=employee_data.get('communication_allowance_percentage'),
                email=email,
                phone=employee_data.get('phone'),
                job_title=employee_data.get('job_title'),
                hire_date=hire_date,
                gross_salary=employee_data.get('gross_salary'),
                total_staff_cost=employee_data.get('total_staff_cost'),
                annual_leave_balance=employee_data.get('annual_leave_balance'),
                extra_leave_days=employee_data.get('extra_leave_days'),
                contract_end_date=employee_data.get('contract_end_date')
            )

            # Save to database
            db_session.add(employee)
            db_session.commit()

            current_app.logger.info(f"Successfully added employee: {first_name} {last_name}")
            current_app.logger.info(f"  - Employee ID: {employee.employee_id}")
            current_app.logger.info(f"  - Stored gross_salary: {employee.gross_salary}")
            current_app.logger.info(f"  - Stored net_salary: {employee.net_salary}")
            current_app.logger.info(f"  - Stored basic_salary: {employee.basic_salary}")
            current_app.logger.info(f"  - Stored transport_allowance: {employee.transport_allowance}")
            current_app.logger.info(f"  - Stored housing_allowance: {employee.housing_allowance}")
            current_app.logger.info(f"  - Stored communication_allowance: {employee.communication_allowance}")

            return employee

        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Failed to add employee {employee_data.get('first_name')} {employee_data.get('last_name')}: {e}")
            raise  # Re-raise the exception to ensure it's handled or logged upstream

    @classmethod
    def add_employee_percentage(
        cls, db_session, company_id,
        first_name, last_name, nid, nsf,
        bank_name, bank_account, branch_name,
        account_name, birth_date, marital_status, gender,
        employee_tin, employee_type, department, net_salary,
        transport_allowance, housing_allowance,
        communication_allowance, over_time,
        other_allowance, email, phone, job_title,
        hire_date, annual_leave_balance, extra_leave_days,
        gross_salary=None, total_staff_cost=None, basic_salary=None,
        salary_calculation_method='percentage_based',
        basic_salary_percentage=None, transport_allowance_percentage=None,
        housing_allowance_percentage=None, communication_allowance_percentage=None,
        contract_end_date=None
    ):
        """Add an employee with percentage-based salary calculations to the database (legacy method)."""
        # Convert to dictionary format and use the new method
        employee_data = {
            'company_id': company_id,
            'first_name': first_name,
            'last_name': last_name,
            'nid': nid,
            'nsf': nsf,
            'bank_name': bank_name,
            'bank_account': bank_account,
            'branch_name': branch_name,
            'account_name': account_name,
            'birth_date': birth_date,
            'marital_status': marital_status,
            'gender': gender,
            'employee_tin': employee_tin,
            'employee_type': employee_type,
            'department': department,
            'net_salary': net_salary,
            'basic_salary': basic_salary,
            'transport_allowance': transport_allowance,
            'housing_allowance': housing_allowance,
            'communication_allowance': communication_allowance,
            'over_time': over_time,
            'other_allowance': other_allowance,
            'salary_calculation_method': salary_calculation_method,
            'basic_salary_percentage': basic_salary_percentage,
            'transport_allowance_percentage': transport_allowance_percentage,
            'housing_allowance_percentage': housing_allowance_percentage,
            'communication_allowance_percentage': communication_allowance_percentage,
            'email': email,
            'phone': phone,
            'job_title': job_title,
            'hire_date': hire_date,
            'gross_salary': gross_salary,
            'total_staff_cost': total_staff_cost,
            'annual_leave_balance': annual_leave_balance,
            'extra_leave_days': extra_leave_days,
            'contract_end_date': contract_end_date
        }

        return cls.add_employee_percentage_from_dict(db_session, employee_data)

    @classmethod
    def update_employee(
        cls, db_session, employee_id,
        first_name, last_name, nid, nsf,
        bank_name, bank_account, branch_name,
        account_name, birth_date, marital_status,
        gender, employee_tin, employee_type,
        department, net_salary, gross_salary, target_total_staff_cost,
        transport_allowance, housing_allowance, communication_allowance,
        over_time, other_allowance, email, phone, job_title,
        hire_date, site_id, is_active, is_brd_sponsored, attendance_applicable,
        annual_leave_balance, extra_leave_days, contract_end_date
    ):
        """Update an employee in the database."""
        employee = cls.get_employee_by_their_id(db_session, employee_id)
        if not employee:
            return "Employee not found"
        updated_employee = cls(
            employee_id=employee_id,
            company_id=employee['company_id'],
            first_name=first_name,
            last_name=last_name,
            nid=nid,
            nsf=nsf,
            bank_name=bank_name,
            bank_account=bank_account,
            branch_name=branch_name,
            account_name=account_name,
            birth_date=birth_date,
            marital_status=marital_status,
            gender=gender,
            employee_tin=employee_tin,
            employee_type=employee_type,
            department=department,
            net_salary=net_salary,
            gross_salary=gross_salary,
            total_staff_cost=target_total_staff_cost,
            transport_allowance=transport_allowance,
            housing_allowance=housing_allowance,
            communication_allowance=communication_allowance,
            over_time=over_time,
            other_allowance=other_allowance,
            email=email,
            phone=phone,
            job_title=job_title,
            hire_date=hire_date,
            site_id=site_id,
            is_active=is_active,
            is_brd_sponsored=is_brd_sponsored,
            attendance_applicable=attendance_applicable,
            annual_leave_balance=annual_leave_balance,
            extra_leave_days=extra_leave_days,
            contract_end_date=contract_end_date
        )
        try:
            db_session.merge(updated_employee)
            db_session.commit()
            return updated_employee
        except Exception as e:
            db_session.rollback()
            return str(e)

    @classmethod
    def bulk_annual_leave_update(cls, db_session, field, mappings):
        """
        Perform a bulk update of annual leave balances for multiple employees.

        Args:
            db_session (Session): The database session to use for the update.
            mappings (list): A list of dictionaries containing the employee_id and the new annual_leave_balance.
                             Example:
                             [
                                 {"employee_id": "uuid1", "annual_leave_balance": 15.5},
                                 {"employee_id": "uuid2", "annual_leave_balance": 10.0},
                             ]

        Returns:
            int: The number of updated employee records.
        """
        if not isinstance(mappings, list) or len(mappings) == 0 or not isinstance(field, str):
            return 0

        try:
            if field == 'annual_leave_balance':
                case_stmt = case(
                    {mapping["employee_id"]: func.coalesce(cls.annual_leave_balance, 0) + mapping["increment_value"] for mapping in mappings},
                    value=cls.employee_id
                )
                update_stmt = update(cls).values(annual_leave_balance=case_stmt)
                result = db_session.execute(update_stmt)
                db_session.commit()
                return result.rowcount  # Number of rows updated
            if field == 'extra_leave_days':
                case_stmt = case(
                    {mapping["employee_id"]: func.coalesce(cls.extra_leave_days, 0) + mapping["increment_value"] for mapping in mappings},
                    value=cls.employee_id
                )
                update_stmt = update(cls).values(extra_leave_days=case_stmt)
                result = db_session.execute(update_stmt)
                db_session.commit()
                return result.rowcount  # Number of rows updated

            # If field name passed is not valid raise an error to discard all transactions if there is.
            raise ValueError("Passed field is not correct")
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Failed to perform bulk annual leave update: {e}")
            return 0

    @classmethod
    def delete_employee(cls, db_session, employee_id):
        try:
            employee = db_session.query(cls).get(employee_id)
            if not employee:
                return False
            db_session.delete(employee)
            db_session.commit()
            return True
        except Exception as e:
            raise
    @classmethod
    def check_unique(cls, db_session, nid, nsf, email, phone):
        """Validate an employee."""
        employee = db_session.query(cls).filter_by(nid=nid).first()
        if employee:
            return f"NID {nid} already exists"
        employee = db_session.query(cls).filter_by(nsf=nsf).first()
        if employee:
            return f"RSSB Number: {nsf} already exists"
        employee = db_session.query(cls).filter_by(email=email).first()
        if employee and email:
            return f"Email: {email} already exists"
        
        employee = db_session.query(cls).filter_by(phone=phone).first()
        if employee and phone:
            return f"Phone: {phone} already exists"
        return None

    @classmethod
    def check_mandatory(cls, db_session, employee):
        """Check for empty that are mandatory."""
        if not employee.first_name:
            return "First name is required"
        elif not employee.last_name:
            return "Last name is required"
        elif not employee.nid:
            return "NID is required"
        elif not employee.nsf:
            return "RSSB is required"
        elif not employee.employee_type:
            return "Employee type is required"
        elif not (employee.net_salary or employee.gross_salary or employee.total_staff_cost):
            return "Salary amount is required"
        elif not employee.gender:
            return "Gender is required"
        return None

    @classmethod
    def check_if_department_available(cls, db_session, department):
        """Check if a department is available."""
        department = db_session.query(Departments).filter_by(department_name=department).first()
        if department:
            return department
        return None

    @classmethod
    def calculate_gross_based_on_total_staff_cost(cls, db_session, employee, calculation_date=None):
        """Calculate the gross salary based on the total staff cost using goal seek approach.

        This method uses binary search to find the gross salary that produces the target total staff cost
        when processed through the actual payroll calculation logic. This ensures consistency between
        forward and reverse calculations.

        Total Staff Cost = Gross Salary + Pension Employer + Maternity Employer + RAMA Employer
        """
        from datetime import date
        from app.helpers.auxillary import Auxillary
        from app.routes.payroll.goal_Seek_mine import SalaryCalculatorGross
        from app.models.central import NsfContributions, Company

        # Use provided date or default to today
        if calculation_date is None:
            calculation_date = date.today()

        # Convert to date if it's a datetime
        if hasattr(calculation_date, 'date'):
            calculation_date = calculation_date.date()

        # Get employee data
        transport_allowance = Auxillary.to_decimal(employee['transport_allowance'])
        allowances = Auxillary.to_decimal(employee['allowances'])  # Excludes transport_allowance
        total_staff_cost = Auxillary.to_decimal(employee['total_staff_cost'])
        employee_type = employee.get('employee_type', 'permanent')

        # Handle consultant employees (no employer contributions)
        if employee_type == 'consultant':
            return Auxillary.round_to_decimal(total_staff_cost)

        # Get rates from database (same logic as payroll calculations)
        pension_ee_rate = Decimal('0.06')
        pension_er_rate = Decimal('0.06')
        maternity_ee_rate = Decimal('0.003')
        maternity_er_rate = Decimal('0.003')
        rama_ee_rate = Decimal('0.00')

        # Retrieve RSSB Contributions
        try:
            contributions = NsfContributions.get_nsf_contributions()
            for contribution in contributions:
                contribution_name = contribution.contribution_name
                if contribution_name == 'pension':
                    pension_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
                    pension_er_rate = Auxillary.to_decimal(contribution.employer_rate)
                elif contribution_name == 'maternity':
                    maternity_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
                    maternity_er_rate = Auxillary.to_decimal(contribution.employer_rate)

                elif contribution_name == 'rama':
                    # RAMA rates are handled separately only if the company has rama applicable
                    company_id = session.get('company_id', None)
                    Company = Company.get_company_by_id(db_session, company_id)
                    if Company and Company.rama_applicable:
                        rama_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
                        rama_er_rate = Auxillary.to_decimal(contribution.employer_rate)
                    else:
                        rama_ee_rate = Decimal('0.00')
                        rama_er_rate = Decimal('0.00')
        except Exception as e:
            current_app.logger.error(f"Failed to get RSSB rates: {e}")                

        def calculate_total_staff_cost_from_gross(gross_salary_test):
            """Calculate total staff cost from gross salary using actual payroll calculation logic"""
            try:
                # Use the same SalaryCalculatorGross as payroll calculations
                calculator = SalaryCalculatorGross(
                    allowances, transport_allowance, 0,  # total_deductions=0 for this calculation
                    0,  # contributions_rate (not needed for employer calculations)
                    pension_ee_rate, pension_er_rate,
                    maternity_ee_rate, maternity_er_rate,
                    rama_ee_rate, employee_type, calculation_date
                )

                # Calculate employer contributions using the actual calculation methods
                pension_er = calculator.pension_er(gross_salary_test, employee_type)
                maternity_er = calculator.maternity_er(gross_salary_test, employee_type)
                rama_er = calculator.rama_er(gross_salary_test, employee_type)

                # Calculate total staff cost
                calculated_total_staff_cost = gross_salary_test + pension_er + maternity_er + rama_er
                return calculated_total_staff_cost

            except Exception as e:
                current_app.logger.error(f"Error in calculate_total_staff_cost_from_gross: {e}")
                return Decimal('0')

        # Binary search goal seek to find the gross salary
        tolerance = Decimal('1.0')  # Accept within 1 unit
        max_iterations = 50

        # Set search bounds
        low = total_staff_cost * Decimal('0.5')  # Lower bound
        high = total_staff_cost * Decimal('1.2')  # Upper bound

        # current_app.logger.info(f"Starting binary search: target={total_staff_cost}, low={low}, high={high}")

        iteration = 0
        while (high - low) > tolerance and iteration < max_iterations:
            iteration += 1
            mid = (low + high) / 2
            calculated_total = calculate_total_staff_cost_from_gross(mid)

            # current_app.logger.info(f"Iteration {iteration}: gross={mid}, calculated_total={calculated_total}, target={total_staff_cost}")

            if calculated_total < total_staff_cost:
                low = mid
            else:
                high = mid

        # Final result
        final_gross = (low + high) / 2
        final_total = calculate_total_staff_cost_from_gross(final_gross)

        # current_app.logger.info(f"Goal seek completed: final_gross={final_gross}, final_total={final_total}, target={total_staff_cost}, difference={abs(final_total - total_staff_cost)}")

        # Round and return
        result = Auxillary.round_to_decimal(final_gross)
        # current_app.logger.info(f"Final gross_salary: {result}")
        return result
    
    @classmethod
    def calculate_gross_based_on_total_staff_cost1(cls, db_session, employee, calculation_date=None):
        """
        Calculate the gross salary based on the total staff cost using binary search goal-seek.
        This method uses the same calculation logic as the actual payroll processing for consistency.

        Total Staff Cost = Gross + Employer Pension + Employer Maternity + Employer RAMA (if applicable)
        """
        # Default date to today if not provided
        if calculation_date is None:
            calculation_date = date.today()
        if hasattr(calculation_date, 'date'):
            calculation_date = calculation_date.date()

        # Extract employee data
        transport_allowance = Auxillary.to_decimal(employee.get('transport_allowance', 0))
        allowances = Auxillary.to_decimal(employee.get('allowances', 0))
        total_staff_cost = Auxillary.to_decimal(employee.get('total_staff_cost', 0))
        employee_type = employee.get('employee_type', 'permanent')

        # Handle consultant employees (no employer contributions)
        if employee_type == 'consultant':
            result = Auxillary.round_to_decimal(total_staff_cost)
            current_app.logger.info(f"[TSC Goal Seek] Consultant employee - returning total staff cost as gross: {result}")
            return result

        # Get RSSB contributions for the calculation date
        try:
            rssb_contributions = NsfContributions.get_valid_contributions(calculation_date)
            if not rssb_contributions:
                rssb_contributions = NsfContributions.get_nsf_contributions()
        except Exception as e:
            current_app.logger.error(f"Error getting RSSB contributions: {e}")
            rssb_contributions = NsfContributions.get_nsf_contributions()

        # Extract contribution rates
        pension_ee_rate = pension_er_rate = Decimal('0.06')
        maternity_ee_rate = maternity_er_rate = Decimal('0.003')
        rama_ee_rate = rama_er_rate = Decimal('0.075')
        cbhi_ee_rate = Decimal('0.075')

        for contribution in rssb_contributions:
            name = contribution.contribution_name.lower()
            if name == "pension":
                pension_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
                pension_er_rate = Auxillary.to_decimal(contribution.employer_rate)
            elif name == "maternity":
                maternity_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
                maternity_er_rate = Auxillary.to_decimal(contribution.employer_rate)
            elif name == "cbhi":
                cbhi_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
            elif name == "rama":
                rama_ee_rate = Auxillary.to_decimal(contribution.employee_rate)
                rama_er_rate = Auxillary.to_decimal(contribution.employer_rate)

        # Check if RAMA is applicable for this company
        company_id = employee.get('company_id')
        company = Company.get_company_by_id(company_id)
        rama_applicable = company.get('rama_applicable', False) if company else False

        if not rama_applicable:
            rama_ee_rate = rama_er_rate = Decimal('0.00')

        current_app.logger.info(f"[TSC Goal Seek] Contribution rates - Pension EE: {pension_ee_rate}, ER: {pension_er_rate}")
        current_app.logger.info(f"[TSC Goal Seek] Maternity EE: {maternity_ee_rate}, ER: {maternity_er_rate}")
        current_app.logger.info(f"[TSC Goal Seek] RAMA EE: {rama_ee_rate}, ER: {rama_er_rate}, Applicable: {rama_applicable}")

        def calculate_total_staff_cost_from_gross(gross_salary_test):
            """Calculate total staff cost from gross salary using the same logic as payroll processing"""
            try:
                # Create calculator with proper parameters including rama_er_rate
                calculator = SalaryCalculatorGross(
                    allowances, transport_allowance, 0,  # total_deductions=0 for this calculation
                    cbhi_ee_rate, pension_ee_rate, pension_er_rate,
                    maternity_ee_rate, maternity_er_rate, rama_ee_rate,
                    employee_type, calculation_date, rama_er_rate
                )

                # Calculate basic salary from gross
                basic_salary = calculator.calculate_basic_salary(gross_salary_test)

                # Calculate employer contributions using the calculator's methods
                pension_er = calculator.pension_er(gross_salary_test, employee_type)
                maternity_er = calculator.maternity_er(gross_salary_test, employee_type)
                rama_er = calculator.rama_er(gross_salary_test, employee_type)

                # Calculate total staff cost
                calculated_total_staff_cost = gross_salary_test + pension_er + maternity_er + rama_er

                current_app.logger.info(f"[TSC Calc] Gross: {gross_salary_test}, Pension ER: {pension_er}, Maternity ER: {maternity_er}, RAMA ER: {rama_er}")
                current_app.logger.info(f"[TSC Calc] Total calculated: {calculated_total_staff_cost}")

                return calculated_total_staff_cost

            except Exception as e:
                current_app.logger.error(f"Error in calculate_total_staff_cost_from_gross: {e}")
                return Decimal('0')

        # Binary search for the correct gross salary
        low = Decimal('10000')  # Minimum reasonable gross salary
        high = total_staff_cost  # Maximum possible gross salary
        tolerance = Decimal('1.00')  # Tolerance for convergence
        max_iterations = 50

        current_app.logger.info(f"[TSC Goal Seek] Starting binary search: low={low}, high={high}, target={total_staff_cost}")

        for iteration in range(max_iterations):
            mid = (low + high) / 2
            calculated_total = calculate_total_staff_cost_from_gross(mid)
            difference = calculated_total - total_staff_cost

            current_app.logger.info(f"[TSC Goal Seek] Iteration {iteration + 1}: gross={mid}, calculated_total={calculated_total}, difference={difference}")

            if abs(difference) <= tolerance:
                current_app.logger.info(f"[TSC Goal Seek] Converged after {iteration + 1} iterations")
                break

            if difference > 0:
                high = mid
            else:
                low = mid

        # Final result
        final_gross = (low + high) / 2
        final_total = calculate_total_staff_cost_from_gross(final_gross)

        current_app.logger.info(f"[TSC Goal Seek] Final result: gross={final_gross}, total={final_total}, target={total_staff_cost}, difference={abs(final_total - total_staff_cost)}")

        return Auxillary.round_to_decimal(final_gross)


class Payroll(DynamicBase):
    """Model representing a payroll.
    Description: This model represents a payroll record for an employee.
    Attributes:
        payroll_id (UUID): A unique identifier for the payroll record.
        employee_id (UUID): A unique identifier for the employee.
        employee_name (str): The name of the employee.
        job_title (str): The job title of the employee.
        basic_salary (float): The basic salary of the employee.
        transport_allowance (float): The transport allowance of the employee.
        housing_allowance (float): The housing allowance of the employee.
        bonus (float): The bonus of the employee.
        medical_fee (float): The amount the company paid on behalf of the employee for medical services.
        gross_salary (float): The total salary before deductions and taxes, and contributions.
        employer_pension (float): The amount the employer contributes to the pension fund.
        employee_pension (float): The amount the employee contributes to the pension fund.
        total_pension (float): The total pension contribution.
        employer_maternity (float): The amount the employer contributes to the maternity fund.
        employee_maternity (float): The amount the employee contributes to the maternity fund.
        total_maternity (float): The total maternity contribution.
        payee (float): The amount of Pay As You Earn tax deducted from the employee's salary.
        cbhi (float): The amount the employee contributes to the Community-Based Health Insurance.
        total_deductions (float): The total deductions from the employee's salary.
        net_pay (float): The net salary after deductions.
        other_deductions (float): Other deductions from the employee's salary.
        pay_date (DateTime): The date the payroll was processed.
        created_at (DateTime): The date the payroll record was created.
        status (str): The status of the payroll (e.g., Pending, Approved, Rejected).
    Relationships:
        employee (Employee): The employee associated with the payroll.
        payroll_approvals (list[PayrollApproval]): The list of approvals for the payroll.
    Methods:
        to_dict(): Convert payroll object to dictionary.
        total_maternity(): Calculate the total maternity contribution.
        total_pension(): Calculate the total pension contribution.
    """
    __tablename__ = 'payrolls'
    payroll_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=False)
    employee_name = Column(String(128), nullable=False)
    job_title = Column(String(128), nullable=True)
    basic_salary = Column(Numeric(precision=18, scale=6), nullable=False)
    transport_allowance = Column(Numeric(precision=12, scale=6), nullable=True)
    housing_allowance = Column(Numeric(precision=12, scale=6), nullable=True)
    bonus = Column(Numeric(precision=12, scale=6), nullable=True)
    medical_fee = Column(Numeric(precision=12, scale=6), nullable=True)
    gross_salary = Column(Numeric(precision=18, scale=6), nullable=False)
    employer_pension = Column(Numeric(precision=12, scale=6), nullable=False)
    employee_pension = Column(Numeric(precision=12, scale=6), nullable=False)
    total_pension = Column(Numeric(precision=12, scale=6), nullable=False)
    employer_maternity = Column(Numeric(precision=12, scale=6), nullable=False)
    employee_maternity = Column(Numeric(precision=12, scale=6), nullable=False)
    total_maternity = Column(Numeric(precision=12, scale=6), nullable=False)
    payee = Column(Numeric(precision=18, scale=6), nullable=False)
    cbhi = Column(Numeric(precision=12, scale=6), nullable=False)
    total_deductions = Column(Numeric(precision=18, scale=6), nullable=True)
    net_pay = Column(Numeric(precision=18, scale=6), nullable=False)
    net_salary = Column(Numeric(precision=18, scale=6), nullable=True)
    reimbursement = Column(Numeric(precision=12, scale=6), nullable=True)
    advance = Column(Numeric(precision=12, scale=6), nullable=True)
    brd_deductions = Column(Numeric(precision=12, scale=6), nullable=True)
    other_deductions = Column(Numeric(precision=12, scale=6), nullable=True)
    pay_date = Column(DateTime, nullable=False)
    status = Column(String(128), nullable=True, default='Pending')
    created_by = Column(UUID(as_uuid=True), nullable=True)  # Track who created the payroll
    created_at = Column(DateTime, nullable=False)
    employee = relationship("Employee", back_populates="payrolls")
    payroll_approvals = relationship(
    "PayrollApproval",
    back_populates="payroll",
    cascade="all, delete-orphan"
    )

    def __repr__(self):
        """Return a string representation of the object."""
        return f"<Payroll {self.payroll_id}>"

    def to_dict(self):
        """Convert payroll object to dictionary."""
        return {
            "payroll_id": self.payroll_id,
            "employee_id": self.employee_id,
            "employee_name": self.employee_name,
            "job_title": self.job_title,
            "basic_salary": self.basic_salary,
            "transport_allowance": self.transport_allowance,
            "housing_allowance": self.housing_allowance,
            "bonus": self.bonus,
            "medical_fee": self.medical_fee,
            "gross_salary": self.gross_salary,
            "employer_pension": self.employer_pension,
            "employee_pension": self.employee_pension,
            "total_pension": self.total_pension(),
            "employer_maternity": self.employer_maternity,
            "employee_maternity": self.employee_maternity,
            "total_maternity": self.total_maternity(),
            "payee": self.payee,
            "cbhi": self.cbhi,
            "total_deductions": self.total_deductions,
            "net_pay": self.net_pay,
            "net_salary": self.net_salary,
            "reimbursement": self.reimbursement,
            "advance": self.advance,
            "brd_deductions": self.brd_deductions,
            "other_deductions": self.other_deductions,
            "pay_date": self.pay_date,
            "created_by": self.created_by,
            "created_at": self.created_at,
            "status": self.status
        }

    def total_maternity(self):
        """Calculate the total maternity contribution."""
        return self.employer_maternity + self.employee_maternity

    def total_pension(self):
        """Calculate the total pension contribution."""
        return self.employer_pension + self.employee_pension

    @classmethod
    def add_payroll(cls,
                    db_session, employee_id, employee_name,
                    job_title, basic_salary, transport_allowance,
                    housing_allowance, bonus, medical_fee, gross_salary,
                    employer_pension, employee_pension, employer_maternity,
                    employee_maternity, payee, cbhi, total_deductions,
                    net_pay, other_deductions, pay_date, net_salary, reimbursement, advance, brd_deductions, created_by=None):
        """Add a payroll record to the database.
        args:
            db_session (Session): The database session.
            employee_id (UUID): The unique identifier for the employee.
            employee_name (str): The name of the employee.
            job_title (str): The job title of the employee.
            basic_salary (float): The basic salary of the employee.
            transport_allowance (float): The transport allowance of the employee.
            housing_allowance (float): The housing allowance of the employee.
            bonus (float): The bonus of the employee.
            medical_fee (float): The amount the company paid on behalf of the employee for medical services.
            gross_salary (float): The total salary before deductions and taxes, and contributions.
            employer_pension (float): The amount the employer contributes to the pension fund.
            employee_pension (float): The amount the employee contributes to the pension fund.
            employer_maternity (float): The amount the employer contributes to the maternity fund.
            employee_maternity (float): The amount the employee contributes to the maternity fund.
            payee (float): The amount of Pay As You Earn tax deducted from the employee's salary.
            cbhi (float): The amount the employee contributes to the Community-Based Health Insurance.
            total_deductions (float): The total deductions from the employee's salary.
            net_pay (float): The net salary after deductions.
            other_deductions (float): Other deductions from the employee's salary.
            pay_date (DateTime): The date the payroll was processed.
            created_at (DateTime): The date the payroll record was created.
        """
        payroll = cls(
            employee_id=employee_id,
            employee_name=employee_name,
            job_title=job_title,
            basic_salary=basic_salary,
            transport_allowance=transport_allowance,
            housing_allowance=housing_allowance,
            bonus=bonus,
            medical_fee=medical_fee,
            gross_salary=gross_salary,
            employer_pension=employer_pension,
            employee_pension=employee_pension,
            employer_maternity=employer_maternity,
            employee_maternity=employee_maternity,
            payee=payee,
            cbhi=cbhi,
            total_deductions=total_deductions,
            net_pay=net_pay,
            net_salary=net_salary,
            reimbursement=reimbursement,
            advance=advance,
            brd_deductions=brd_deductions,
            other_deductions=other_deductions,
            pay_date=pay_date,
            created_by=created_by,
            created_at=datetime.now()
              )
        try:
            db_session.add(payroll)
            db_session.commit()
            return payroll
        except Exception as e:
            db_session.rollback()
            return str(e)
    @classmethod
    def get_payrolls(cls, db_session):
        """Get all payrolls from the database."""
        payrolls = db_session.query(cls).all()
        return [payroll.to_dict() for payroll in payrolls]

    @classmethod
    def get_payroll_for_a_specific_month(cls, db_session, month, year):
        """Get all payrolls for a specific month from the database."""
        payrolls = db_session.query(cls).filter(
            cls.pay_date >= datetime(year, month, 1),
            cls.pay_date <= datetime(year, month, calendar.monthrange(year, month)[1])
        ).all()
        return [payroll.to_dict() for payroll in payrolls]

    @classmethod
    def get_employee_payroll_for_a_specific_month(cls, db_session, employee_id, month, year):
        """Get all payrolls for a specific month for an employee from the database."""
        payrolls = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.pay_date >= datetime(year, month, 1),
            cls.pay_date <= datetime(year, month, calendar.monthrange(year, month)[1])
        ).all()
        return [payroll.to_dict() for payroll in payrolls]

    @classmethod
    def get_payroll_by_id(cls, db_session, payroll_id):
        """Get a payroll by ID."""
        payroll = db_session.query(cls).get(payroll_id)
        if payroll:
            return payroll
        else:
            return []

class User(DynamicBase):
    """Model representing a user.
    Description: This model represents a user in the system.
    Attributes:
        user_id (UUID): A unique identifier for the user.
        username (str): The username of the user.
        email (str): The email address of the user.
        password (str): The password of the user.
        role_id (UUID): The unique identifier for the user role.
        phone_number (str): The phone number of the user.
        created_at (DateTime): The date the user was created.
        """
    __tablename__ = 'users'

    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(128), nullable=False)
    email = Column(String(128), nullable=True, unique=True)
    password = Column(String(128), nullable=False)
    role = Column(String(128), nullable=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=True)
    phone_number = Column(String(128), nullable=False, unique=True)
    salt = Column(String(128), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    # Define the relationship between the user and the employe
    employee = relationship("Employee", back_populates="users")

    def __repr__(self) -> str:
        """Return a string representation of the object."""
        return f"<User {self.username}>"

    def to_dict(self):
        """Convert user object to dictionary."""
        full_name = None
        if self.employee:  # Check if the user is linked to an employee
            full_name = f"{self.employee.first_name} {self.employee.last_name}"
            first_name = self.employee.first_name
            last_name = self.employee.last_name
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role,
            "phone_number": self.phone_number,
            "employee_id": self.employee_id,
            "full_name": full_name,
            "first_name": first_name,
            "last_name": last_name
        }

    @classmethod
    def add_user(
        cls, db_session, username, email, password,
        role, phone_number, employee_id):
        """Add a user to the database."""
        # Hash the password
        hashed_password, salt = CentralUser.hash_password(password)
        user = cls(
            username=username,
            email=email,
            password=hashed_password,
            role=role,
            phone_number=phone_number,
            employee_id=employee_id,
            salt=salt
        )
        db_session.add(user)
        db_session.commit()
        return user

    @classmethod
    def login_user(cls, db_session, username, password):
        """Login a user."""
        user = db_session.query(cls).filter_by(username=username).first()
        if not user:
            return False
        salted_password = user.salt + password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        if hashed_password == user.password:
            return True
        return False

    @classmethod
    def get_users(cls, db_session):
        """Get all users from the database."""
        users = db_session.query(cls).all()
        return [user.to_dict() for user in users]

    @classmethod
    def delete_user(cls, db_session, user_id):
        """Delete a user from the database."""
        user = db_session.query(cls).get(user_id)
        db_session.delete(user)
        db_session.commit()
        return user

    @classmethod
    def get_user_by_id(cls, db_session, user_id):
        """Get a user by ID."""
        if user_id:
            user = db_session.query(cls).get(user_id)
            if user:
                return user.to_dict()
        return []

    @classmethod
    def get_user_for_loader(cls, db_session, user_id):
        """Get a user by ID."""
        if user_id:
            user = db_session.query(cls).get(user_id)
            if user:
                return user
        return None

    @classmethod
    def reset_password(cls, db_session, email, password):
        """Reset a user's password."""
        user = db_session.query(cls).filter_by(email=email).first()
        if not user:
            return "User not found"
        hashed_password, salt = CentralUser.hash_password(password)
        user.password = hashed_password
        user.salt = salt
        db_session.commit()
        return True

    @classmethod
    def get_user_by_username(cls, db_session, username):
        """Get a user by username."""
        user = db_session.query(cls).filter_by(username=username).first()
        # check if user is not None
        if not user:
            return {}
        # convert to a dictionary
        return user.to_dict()

    @classmethod
    def get_user_by_email(cls, db_session, email):
        """Get a user by email."""
        return db_session.query(cls).filter_by(email=email).first()

class Insurance(DynamicBase):
    """Model representing an insurance."""

    __tablename__ = 'insurances'
    insurance_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    insurance_name = Column(String(128), nullable=False)
    employee_rate = Column(Numeric(precision=12, scale=6), nullable=False)
    employer_rate = Column(Numeric(precision=12, scale=6), nullable=False)
    created_at = Column(DateTime, nullable=False)


    def __repr__(self):
        """Return a string representation of the object."""
        return {
            "insurance_id": self.insurance_id,
            "insurance_name": self.insurance_name,
            "employee_rate": self.employee_rate,
            "employer_rate": self.employer_rate,
            "created_at": self.created_at
        }

    def to_dict(self):
        """Convert insurance object to dictionary."""
        return {
            "insurance_id": self.insurance_id,
            "insurance_name": self.insurance_name,
            "employee_rate": self.employee_rate,
            "employer_rate": self.employer_rate,
            "created_at": self.created_at
        }

    @classmethod
    def insert_insurance(cls, db_session, insurance_name, employee_rate, employer_rate, created_at=datetime.now()):
        """Insert an insurance into the database."""
        insurance = cls(
            insurance_name=insurance_name,
            employee_rate=employee_rate,
            employer_rate=employer_rate,
            created_at=created_at
        )
        db_session.add(insurance)
        db_session.commit()
        return insurance

    @classmethod
    def get_insurances(cls, db_session):
        """Get all insurances from the database."""
        insurances = db_session.query(cls).all()
        return [insurance.to_dict() for insurance in insurances]

    @classmethod
    def get_insurance_by_id(cls, db_session, insurance_id):
        """Get an insurance by ID."""
        return db_session.query(cls).get(insurance_id)
    
    @classmethod
    def update_insurance(cls, db_session, insurance_id, **kwargs):
        insurance = cls.get_insurance_by_id(db_session, insurance_id)
        if not insurance:
            return None
        
        for k, v in kwargs.items():
            if hasattr(insurance, k):
                setattr(insurance, k, v)
        
        # Commit changes made on insurance object
        db_session.commit()
        return insurance

class Deductions(DynamicBase):
    """Model representing deductions.
    Description: This model represents deductions from an employee's net salary.
    Attributes:
        deduction_id (UUID): A unique identifier for the deduction.
        employee_id (UUID): A unique identifier for the employee.
        description (str): The description of the deduction.
        deduction_amount (float): The amount of the deduction.
        deduction_date (DateTime): The date the deduction was made.
    """
    __tablename__ = 'deductions'
    deduction_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=False)
    description = Column(String(128), nullable=False)
    deduction_amount = Column(Numeric(precision=12, scale=6), nullable=False)
    deduction_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.now)

    def __repr__(self):
        """Return a string representation of the object."""
        # format the dates to dd/mm/yyyy
        deduction_date = self.deduction_date.strftime('%d/%m/%Y')
        return f"<Deductions(deduction_id={self.deduction_id}, employee_id={self.employee_id}, description='{self.description}', deduction_amount={self.deduction_amount}, deduction_date={deduction_date})>"


    def to_dict(self):
        """Convert deduction object to dictionary."""
        # format the dates to dd/mm/yyyy
        deduction_date = self.deduction_date.strftime('%d/%m/%Y')


        return {
            "deduction_id": self.deduction_id,
            "employee_id": self.employee_id,
            "description": self.description,
            "deduction_amount": self.deduction_amount,
            "deduction_date": deduction_date
        }

    @classmethod
    def get_deductions(cls, db_session):
        """Get all deductions from the database.
        args:
            db_session (Session): The database session.
            returns:
                list: A list of dictionaries representing the deductions.
        """
        deductions = db_session.query(cls).all()
        return [deduction.to_dict() for deduction in deductions]

    @classmethod
    def get_deductions_for_employee(cls, db_session, employee_id):
        """Get all deductions for a specific employee from the database."""
        try:
            deductions = db_session.query(cls).filter_by(employee_id=employee_id).all()
            current_app.logger.info(f"Successfully retrieved deductions: {deductions}")
            return [deduction.to_dict() for deduction in deductions]
        except Exception as e:
            current_app.logger.error(f"Failed to retrieve deductions: {e}")
            return None
    @classmethod
    def add_deduction(cls, db_session, employee_id, description, deduction_amount, deduction_date):
        """Add a deduction to the database."""
        deduction = cls(
            employee_id=employee_id,
            description=description,
            deduction_amount=deduction_amount,
            deduction_date=deduction_date
        )
        try:
            db_session.add(deduction)
            db_session.commit()
            return deduction
        except Exception as e:
            db_session.rollback()
            return str(e)
    @classmethod
    def get_deductions_for_current_month_for_employee(cls, db_session, employee_id):
        """Get all deductions for the current month for a specific
        employee from the database.
        args:
            db_session (Session): The database session.
            employee_id (UUID): The unique identifier for the employee.
            returns:
                list: A list of dictionaries representing the deductions.
        """
        # Define the desired time zone (West Africa Time)
        wat = pytz.timezone('Africa/Kigali')
        # Get the current date and time in the desired time zone
        now = datetime.now(wat)
        first_day_of_month = wat.localize(datetime(now.year, now.month, 1))
        # Get the last day of the month
        last_day_of_month = (first_day_of_month + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)

        deductions = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.deduction_date >= first_day_of_month,
            cls.deduction_date <= last_day_of_month
        ).all()

        return [deduction.to_dict() for deduction in deductions]

    @classmethod
    def get_deduction_for_given_month_and_year_for_employee(cls, db_session, employee_id, month, year):
        """Get all deductions for a specific employee for a given month and year from the database.
        args:
            db_session (Session): The database session.
            employee_id (UUID): The unique identifier for the employee.
            month (int): The month for which the deductions are required.
            year (int): The year for which the deductions are required.
        returns:
            list: A list of dictionaries representing the deductions.
        """
        # Get the last day of the given month
        last_day_of_month = calendar.monthrange(year, month)[1]  # Returns a tuple (weekday, number_of_days_in_month)

        deductions = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.deduction_date >= datetime(year, month, 1),
            cls.deduction_date <= datetime(year, month, last_day_of_month)
        ).all()

        return [deduction.to_dict() for deduction in deductions]

    @classmethod
    def update_deduction(cls, db_session, deduction_id, description, amount, deduction_date):
        try:
            deduction = db_session.query(cls).get(deduction_id)
            if deduction is None:
                return f"Deduction with id {deduction_id} not found"

            deduction.description = description
            deduction.deduction_amount = amount  # Fixed the attribute name here
            deduction.deduction_date = deduction_date

            db_session.commit()
            return deduction
        except Exception as e:
            db_session.rollback()
            return f"An error occurred: {str(e)}"

    @classmethod
    def get_deduction_by_id(cls, db_session, deduction_id):
        """Get a single deduction by ID."""
        return db_session.query(cls).get(deduction_id)

    @classmethod
    def delete_deduction(cls, db_session, deduction_obj):
        """Delete Deduction instance from the database."""
        try:
            db_session.delete(deduction_obj)
            return True
        except Exception as e:
            db_session.rollback()
            raise

class Reimbursements(DynamicBase):
    """Model representing reimbursements."""
    __tablename__ = 'reimbursements'
    reimbursement_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=False)
    description = Column(String(128), nullable=False)
    reimbursement_amount = Column(Numeric(precision=12, scale=6), nullable=False)
    reimbursement_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.now)


    def __repr__(self):
        """Return a string representation of the object."""
        return f"<Deductions(reimbursement_id={self.reimbursement_id}, employee_id={self.employee_id}, description='{self.description}', reimbursement_amount={self.reimbursement_amount}, reimbursement_date={self.reimbursement_date})>"

    def to_dict(self):
        """Convert deduction object to dictionary."""
        # format the dates to dd/mm/yyyy
        reimbursement_date = self.reimbursement_date.strftime('%d/%m/%Y')

        return {
            "reimbursement_id": self.reimbursement_id,
            "employee_id": self.employee_id,
            "description": self.description,
            "reimbursement_amount": self.reimbursement_amount,
            "reimbursement_date": reimbursement_date
        }
    @classmethod
    def add_reimbursement(cls, db_session,
                          employee_id, description,
                          reimbursement_amount, reimbursement_date):
        """Add reimbursements."""
        reimbursement = cls(
            employee_id=employee_id,
            description=description,
            reimbursement_amount=reimbursement_amount,
            reimbursement_date=reimbursement_date
        )
        try:
            db_session.add(reimbursement)
            db_session.commit()
            return reimbursement
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Failed to add reimbursement: {e}")
            flash("Failed to add reimbursement", "danger")
            message = f"Failed to add reimbursement: {e}"
            return message

    @classmethod
    def get_reimbursements(cls, db_session):
        """Get all reimbursements from the database.
        args:
            db_session (Session): The database session.
            returns:
                list: A list of dictionaries representing the reimbursements.
        """
        reimbursements = db_session.query(cls).all()
        return [reimbursement.to_dict() for reimbursement in reimbursements]

    @classmethod
    def get_reimbursement_for_employee(cls, db_session, employee_id):
        """Get all reimbursements for a specific employee from the database."""
        reimbursements = db_session.query(cls).filter_by(employee_id=employee_id).all()
        return [reimbursement.to_dict() for reimbursement in reimbursements]

    @classmethod
    def get_reimbursement_for_current_month_for_employee(cls, db_session, employee_id):
        """Get all reimbursements for the current month for a specific
        employee from the database.
        args:
            db_session (Session): The database session.
            employee_id (UUID): The unique identifier for the employee.
            returns:
                list: A list of dictionaries representing the reimbursements.
        """
        # Define the desired time zone (West Africa Time)
        wat = pytz.timezone('Africa/Kigali')
        # Get the current date and time in the desired time zone
        now = datetime.now(wat)
        first_day_of_month = wat.localize(datetime(now.year, now.month, 1))
        # Get the last day of the month
        last_day_of_month = (first_day_of_month + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)

        reimbursements = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.reimbursement_date >= first_day_of_month,
            cls.reimbursement_date <= last_day_of_month
        ).all()

        return [reimbursement.to_dict() for reimbursement in reimbursements]

    @classmethod
    def update_reimbursement(cls, db_session, reimbursement_id, description, amount, reimbursement_date):
        try:
            reimbursement = db_session.query(cls).get(reimbursement_id)
            if reimbursement is None:
                return f"Reimbursement with id {reimbursement_id} not found"

            reimbursement.description = description
            reimbursement.reimbursement_amount = amount  # Fixed the attribute name here
            reimbursement.reimbursement_date = reimbursement_date

            db_session.commit()
            return reimbursement
        except Exception as e:
            db_session.rollback()
            return f"An error occurred: {str(e)}"
    
    @classmethod
    def update_reimbursement_for_api(cls, db_session, employee_id, reimbursement_id, description, amount, reimbursement_date):
        try:
            reimbursement = db_session.query(cls).get(reimbursement_id)
            if reimbursement is None:
                return f"Reimbursement with id {reimbursement_id} not found"
            reimbursement.employee_id = employee_id
            reimbursement.description = description
            reimbursement.reimbursement_amount = amount  # Fixed the attribute name here
            reimbursement.reimbursement_date = reimbursement_date

            db_session.commit()
            return reimbursement
        except Exception as e:
            db_session.rollback()
            return f"An error occurred: {str(e)}"

    @classmethod
    def get_reimbursement_by_id(cls, db_session, reimbursement_id):
        """Get a single reimbursement by ID."""
        return db_session.query(cls).get(reimbursement_id)

    @classmethod
    def delete_reimbursement(cls, db_session, reimbursement_id):
        """Delete a reimbursement."""
        try:
            reimbursement = db_session.query(cls).get(reimbursement_id)
            if reimbursement is None:
                return f"Reimbursement with id {reimbursement_id} not found"

            db_session.delete(reimbursement)
            db_session.commit()
            current_app.logger.info(f"Reimbursement with id {reimbursement_id} deleted successfully")
            return f"Reimbursement with id {reimbursement_id} deleted successfully"
        except Exception as e:
            db_session.rollback()
            return f"An error occurred: {str(e)}"

    @classmethod
    def get_reimbursement_for_current_month_for_employee(cls, db_session, employee_id):
        """Get all reimbursements for the current month for a specific
        employee from the database.
        args:
            db_session (Session): The database session.
            employee_id (UUID): The unique identifier for the employee.
            returns:
                list: A list of dictionaries representing the reimbursements.
        """
        # Define the desired time zone (West Africa Time)
        wat = pytz.timezone('Africa/Kigali')
        # Get the current date and time in the desired time zone
        now = datetime.now(wat)
        first_day_of_month = wat.localize(datetime(now.year, now.month, 1))
        # Get the last day of the month
        last_day_of_month = (first_day_of_month + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)

        reimbursements = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.reimbursement_date >= first_day_of_month,
            cls.reimbursement_date <= last_day_of_month
        ).all()

        return [reimbursement.to_dict() for reimbursement in reimbursements]

    @classmethod
    def get_reimbursement_for_given_month_and_year_for_employee(cls, db_session, employee_id, month, year):
        """Get all reimbursements for a specific employee for a given month and year from the database.
        args:
            db_session (Session): The database session.
            employee_id (UUID): The unique identifier for the employee.
            month (int): The month for which the reimbursements are required.
            year (int): The year for which the reimbursements are required.
        returns:
            list: A list of dictionaries representing the reimbursements.
        """
        # Get the last day of the given month
        last_day_of_month = calendar.monthrange(year, month)[1]

        reimbursements = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.reimbursement_date >= datetime(year, month, 1),
            cls.reimbursement_date <= datetime(year, month, last_day_of_month)
        ).all()

        return [reimbursement.to_dict() for reimbursement in reimbursements]

class Departments(DynamicBase):
    """Model representing departments."""

    __tablename__ = 'departments'
    department_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    department_name = Column(String(128), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    attendance = relationship("Attendance", back_populates="Departments")

    def __repr__(self):
        """Return a string representation of the object."""
        return f"Department: {self.department_name.upper()}, Department ID: {self.department_id}, Created At: {self.created_at}"


    def to_dict(self):
        """Convert department object to dictionary."""
        # format the dates to dd/mm/yyyy
        created_at = self.created_at.strftime('%d/%m/%Y')
        return {
            "department_id": self.department_id,
            "department_name": self.department_name.upper(),
            "created_at": created_at
        }

    @classmethod
    def insert_department(cls, db_session, department_name):
        """Insert a department into the database."""

        # Check if the department already exists
        try:
            department = cls.get_department_by_name(db_session, department_name)
            if department:
                return "Department already exists"
            department = cls(
                department_name=department_name,
                created_at=datetime.now()
            )
            db_session.add(department)
            db_session.commit()
            return department
        except Exception:
            raise

    @classmethod
    def get_departments(cls, db_session):
        """Get all departments from the database."""
        departments = db_session.query(cls).all()
        return [department.to_dict() for department in departments]

    @classmethod
    def get_department_by_id(cls, db_session, department_id):
        """Get a department by ID."""
        department = db_session.query(cls).get(department_id)

        return department

    @classmethod
    def update_department(cls, db_session, department_id, department_name):
        try:
            department = db_session.query(cls).get(department_id)
            if department is None:
                return f"Department with id {department_id} not found"

            department.department_name = department_name

            db_session.commit()
            return department
        except Exception as e:
            db_session.rollback()
            raise

    @classmethod
    def delete_department(cls, db_session, department_id):
        """Delete a department."""
        try:
            department = db_session.query(cls).get(department_id)
            if department is None:
                return f"Department with id {department_id} not found"

            db_session.delete(department)
            db_session.commit()
            return f"Department with id {department_id} deleted successfully"
        except Exception as e:
            db_session.rollback()
            raise

    @classmethod
    def get_department_by_name(cls, db_session, department_name):
        """Get a department by name."""
        department = db_session.query(cls).filter_by(department_name=department_name).first()
        return department

class FeedBack(DynamicBase):
    """Model representing feedback."""
    __tablename__ = 'feedback'
    feedback_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    company_id = Column(UUID(as_uuid=True), nullable=False)
    subject = Column(String(128), nullable=False)
    message = Column(Text, nullable=False)
    status = Column(String(128), default='open')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        Feedback: {self.subject}, Feedback ID: {self.feedback_id},
          Created At: {self.created_at}
        """

    def to_dict(self):
        """Convert feedback object to dictionary."""
        # format the dates to dd/mm/yyyy
        created_at = self.created_at.strftime('%d/%m/%Y')
        updated_at = self.updated_at.strftime('%d/%m/%Y')
        return {
            "feedback_id": self.feedback_id,
            "user_id": self.user_id,
            "company_id": self.company_id,
            "subject": self.subject,
            "message": self.message,
            "status": self.status,
            "created_at": created_at,
            "updated_at": updated_at
        }

    @classmethod
    def insert_feedback(cls, db_session, user_id, company_id, subject, message):
        """Insert feedback into the database."""
        try:
            feedback = cls(
                user_id=user_id,
                company_id=company_id,
                subject=subject,
                message=message,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db_session.add(feedback)
            db_session.commit()
            return feedback
        except Exception:
            raise

    @classmethod
    def get_feedbacks(cls, db_session):
        """Get all feedbacks from the database."""
        feedbacks = db_session.query(cls).all()
        return [feedback.to_dict() for feedback in feedbacks]

class Attendance(DynamicBase):
    """Model representing Attendance of Employees."""
    __tablename__ = "attendance"
    attendance_id =  Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    time_in = Column(DateTime, nullable=True)
    time_out = Column(DateTime, nullable=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    recorgnition_status = Column(String(128), default='Recognized')
    department_id = Column(UUID(as_uuid=True), ForeignKey('departments.department_id'), nullable=True)
    clockin_location = Column(String(128), nullable=True)
    clockin_location_name = Column(String(128), nullable=True)
    clockout_location = Column(String(128), nullable=True)
    clockout_location_name = Column(String(128), nullable=True)
    device_used = Column(String(256), nullable=False)
    site_id = Column(UUID(as_uuid=True), ForeignKey('sites.site_id'), nullable=True)
    field_in = Column(DateTime, nullable=True)
    field_out = Column(DateTime, nullable=True)
    field_in_location = Column(String(128), nullable=True)
    field_in_location_name = Column(String(128), nullable=True)
    field_out_location = Column(String(128), nullable=True)
    field_out_location_name = Column(String(128), nullable=True)
    break_in = Column(DateTime, nullable=True)
    break_out = Column(DateTime, nullable=True)
    break_duration = Column(String(128), nullable=True)
    total_duration = Column(String(128), nullable=True)
    work_status = Column(Enum('leave', 'off', 'annual_leave', name='work_status_enum'), nullable=True)
    time_off_begin_date = Column(DateTime, nullable=True)
    time_off_end_date = Column(DateTime, nullable=True)
    remarks = Column(String(128), nullable=True)
    is_void = Column(Boolean, default=False, nullable=True)
    void_reason = Column(Text, nullable=True)
    Employee = relationship("Employee", back_populates="attendance")
    Departments = relationship("Departments", back_populates="attendance")
    Sites = relationship("Site", back_populates="attendance")
    LeaveApplications = relationship("LeaveApplication", back_populates="Attendance")



    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        attendance_id: {self.attendance_id},
        employee_id: {self.employee_id},
        time_in: {self.time_in},
        time_out: {self.time_out},
        department_id: {self.department_id},
        clockin_Location: {self.clockin_location},
        device_used: {self.device_used},
        field_in: {self.field_in},
        field_out: {self.field_out},
        field_in_location: {self.field_in_location},
        field_out_location: {self.field_out_location},
        field_in_location_name: {self.field_in_location_name},
        field_out_location_name: {self.field_out_location_name},
        break_in: {self.break_in},
        break_out: {self.break_out},
        break_duration: {self.break_duration},
        total_duration: {self.total_duration},
        work_status: {self.work_status},
        time_off_begin_date: {self.time_off_begin_date},
        time_off_end_date: {self.time_off_end_date},

        """

    def to_dict(self):
        """Convert attendance object to dictionary."""
        # format the dates to dd/mm/yyyy and the time to hh:mm:ss
        # Check if the time is not None

        time_in = self.time_in.strftime('%d/%m/%Y %H:%M:%S') if self.time_in else None
        time_out = self.time_out.strftime('%d/%m/%Y %H:%M:%S') if self.time_out else None
        created_at = self.created_at.strftime('%d/%m/%Y %H:%M:%S')
        break_in = self.break_in.strftime('%d/%m/%Y %H:%M:%S') if self.break_in else None
        break_out = self.break_out.strftime('%d/%m/%Y %H:%M:%S') if self.break_out else None
        time_off_begin_date = self.time_off_begin_date.strftime('%d/%m/%Y') if self.time_off_begin_date else None
        time_off_end_date = self.time_off_end_date.strftime('%d/%m/%Y') if self.time_off_end_date else None
        field_in = self.field_in.strftime('%d/%m/%Y %H:%M:%S') if self.field_in else None
        field_out = self.field_out.strftime('%d/%m/%Y %H:%M:%S') if self.field_out else None
        # Split the string by the comma if it exists
        if self.clockin_location and "," in self.clockin_location:
            location_parts = self.clockin_location.split(", ")
            clockin_longitude, clockin_latitude = location_parts[:2]
        else:
            clockin_longitude = None
            clockin_latitude = None

        if self.time_out and self.time_in:
            total_duration = self.time_out - self.time_in
        elif self.field_out and self.field_in:
            total_duration = self.field_out - self.field_in
        else:
            # If the employee hasn't clocked out yet,F set the total duration to 00:00:00
            total_duration = timedelta(hours=0, minutes=0, seconds=0)

        # format the total duration to hh:mm:ss so that we only have 2 places
        total_duration = str(total_duration).split(".")[0]

        return {
            "attendance_id": self.attendance_id,
            "time_in": time_in,
            "time_out": time_out,
            "employee_id": self.employee_id,
            "created_at": created_at,
            "recorgnition_status": self.recorgnition_status,
            "department_id": self.department_id,
            "clockin_location": self.clockin_location,
            "clockout_location": self.clockout_location,
            "clockin_location_name": self.clockin_location_name,
            "clockout_location_name": self.clockout_location_name,
            "work_status": self.work_status,
            "device_used": self.device_used,
            "field_in": field_in,
            "field_out": field_out,
            "field_in_location": self.field_in_location,
            "field_out_location": self.field_out_location,
            "field_in_location_name": self.field_in_location_name,
            "field_out_location_name": self.field_out_location_name,
            "break_in": break_in,
            "break_out": break_out,
            "break_duration": self.break_duration,
            "time_off_begin_date": time_off_begin_date,
            "time_off_end_date": time_off_end_date,
            "total_duration": total_duration,
            "remarks": self.remarks,
            "is_void": self.is_void,
            "void_reason": self.void_reason,
            "employee_name": self.Employee.first_name + " " + self.Employee.last_name
        }

    @classmethod
    def clockin(cls, db_session, employee_id, location, device_used, clockin_location_name='Unknown'):
        """Insert attendance into the database, ensuring no duplicate clock-ins."""

        # Check if the employee has an open attendance (i.e., they haven't clocked out yet)
        existing_attendance = db_session.query(cls).filter(
            and_(cls.employee_id == employee_id, cls.time_out == None, cls.work_status == None, cls.is_void == False)
        ).first()
        current_app.logger.info(f"Existing attendance: {existing_attendance}")

        # Get the employee's name
        employee = db_session.query(Employee).get(employee_id)
        employee_name = f"{employee.first_name} {employee.last_name}"

        # Check if the employee has an open attendance or is currently on leave/off
        current_date = datetime.now()
        overlapping_leave = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.work_status.in_(['leave', 'off']),  # Consider only leave/off statuses
                cls.time_off_begin_date <= current_date,
                cls.time_off_end_date >= current_date
            )
        ).first()
        # If overlapping leave period exists, block the clock-in
        if overlapping_leave:
            return f"{employee_name}, clockin not allowed  while on leave or off."


        if existing_attendance:
            # Check if the user has clocked in already
            if existing_attendance:
                return f"{employee_name}, already clocked in and have not clocked out yet."


        # If no conflicts, proceed with clock-in
        attendance = cls(
            employee_id=employee_id,
            clockin_location=location,
            device_used=device_used,
            clockin_location_name=clockin_location_name,
            created_at=datetime.now(),
            time_in=datetime.now()  # Ensure you record the clock-in time
        )
        db_session.add(attendance)
        db_session.commit()
        time_in = datetime.now().strftime('%d/%m/%Y %H:%M:%S')
        current_app.logger.info(f"{employee_name}, has successfully clocked in at {time_in}.")
        return f"{employee_name}, has successfully clocked in at {time_in}."

    @classmethod
    def field_clockin(cls, db_session, employee_id, location, device_used, field_in_location_name='Unknown'):
        """Insert attendance into the database, ensuring no duplicate clock-ins."""

        # Check if the employee has an open attendance (i.e., they haven't clocked out yet)
        existing_attendance = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.is_void == False,
                or_(
                    and_(cls.time_in != None, cls.time_out == None),
                    and_(cls.field_in != None, cls.field_out == None)
                    )
                    )).first()

        current_app.logger.info(f"Existing attendance: {existing_attendance}")

        # Get the employee's name
        employee = db_session.query(Employee).get(employee_id)
        employee_name = f"{employee.first_name} {employee.last_name}"

        # Check if the employee has an open attendance or is currently on leave/off
        current_date = datetime.now()
        overlapping_leave = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.work_status.in_(['leave', 'off']),  # Consider only leave/off statuses
                cls.time_off_begin_date <= current_date,
                cls.time_off_end_date >= current_date
            )
        ).first()
        # If overlapping leave period exists, block the clock-in
        if overlapping_leave:
            return f"{employee_name}, clockin not allowed  while on leave or off."

        if existing_attendance:
            # Check if the user has clocked in already
            if existing_attendance:
                return f"{employee_name}, you already have an active attendance record. Please clock out before starting a new one."


        # If no conflicts, proceed with clock-in
        attendance = cls(
            employee_id=employee_id,
            field_in_location=location,
            device_used=device_used,
            field_in_location_name=field_in_location_name,
            created_at=datetime.now(),
            field_in=datetime.now()  # Ensure you record the clock-in time
        )
        db_session.add(attendance)
        db_session.commit()
        time_in = datetime.now().strftime('%d/%m/%Y %H:%M:%S')
        current_app.logger.info(f"{employee_name}, has successfully clocked in at {time_in}.")
        return f"{employee_name}, your field clockin has been successfully recorded at {time_in}."

    @classmethod
    def clockout(cls, db_session, employee_id, location, clockout_location_name='Unknown'):
        """Update attendance in the database for a clock-out, ensuring work_status is None."""

        # Retrieve employee's information
        employee = db_session.query(Employee).get(employee_id)
        employee_name = f"{employee.first_name} {employee.last_name}"

        # Check if the employee has an open attendance (i.e., they haven't clocked out yet and work_status is None)
        existing_attendance = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.time_out == None,  # Not clocked out yet
                cls.work_status == None,  # Ensure it's a regular work status
                cls.is_void == False  # Ensure the attendance record is not voided
            )
        ).first()

        if not existing_attendance:
            return f"{employee_name}, you have not clocked in yet or you are on leave/off."

        # Update the attendance record with clock-out information
        existing_attendance.time_out = datetime.now()
        existing_attendance.clockout_location = location
        existing_attendance.clockout_location_name = clockout_location_name
        db_session.commit()

        return f"{employee_name}, you have successfully clocked out."

    @classmethod
    def field_clockout(cls, db_session, employee_id, location, device_used, field_out_location_name='Unknown'):
        """Update attendance in the database for a clock-out, ensuring work_status is None."""

        # Retrieve employee's information
        employee = db_session.query(Employee).get(employee_id)
        employee_name = f"{employee.first_name} {employee.last_name}"

        # Check if the employee has an open field attendance
        existing_attendance = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.field_in != None,  # Ensure there's a field clock-in
                cls.field_out == None,  # Ensure there's no field clock-out yet
                cls.work_status == None,  # Ensure work_status is None
                cls.is_void == False  # Ensure it's not voided
            )
        ).first()

        if not existing_attendance:
            return f"{employee_name}, you have not clocked in yet or you are on leave/off."

        # Update the attendance record with clock-out information
        existing_attendance.field_out = datetime.now()
        existing_attendance.field_out_location = location
        existing_attendance.field_out_location_name = field_out_location_name
        existing_attendance.device_used = device_used
        db_session.commit()

        return f"{employee_name}, you have successfully clocked out."

    @classmethod
    def check_time_difference(cls, db_session, employee_id, clockin_time):
        """Check if the last clock-in or clock-out was less than 10 minutes ago."""
        # Fetch the last clock-in and last clock-out for the employee
        last_clockin_record = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.is_void == False,
            cls.time_out == None
        ).order_by(cls.time_in.desc()).first()
        current_app.logger.info(f"Last clockin record: {last_clockin_record}")
        # check the time_in
        if last_clockin_record:
            time_in = last_clockin_record.time_in
            current_app.logger.info(f"Time in: {time_in} and the type is {type(time_in)}")


        last_clockout_record = db_session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.is_void == False,
            cls.time_out != None
        ).order_by(cls.time_out.desc()).first()
        current_app.logger.info(f"Last clockout record: {last_clockout_record}")
        time_out = last_clockout_record.time_out
        current_app.logger.info(f"Time out: {time_out} and the type is {type(time_out)}")

        #Make sure the clockin_time not none
        if clockin_time is None:
            return None

        current_app.logger.info(f"Clockin time: {clockin_time} and the type is {type(clockin_time)}")

        # Ensure clockin_time is a datetime object
        if isinstance(clockin_time, str):
            try:
                clockin_time = datetime.strptime(clockin_time, "%Y-%m-%d %H:%M:%S")
                current_app.logger.info(f"Clockin time: {clockin_time} and the type is {type(clockin_time)}")
            except ValueError:
                return None

        current_app.logger.info(f"Clockin time after converting: {clockin_time} and the type is {type(clockin_time)}")

        # Check if either the last clock-in or clock-out is within 10 minutes of the record time
        if last_clockin_record:
            last_clockin_time = last_clockin_record.time_in
            time_difference = (clockin_time - last_clockin_time).total_seconds() / 60
            if time_difference < 60:
                return f"Skipping clock-in for {employee_id}, too soon after last clock-in ({time_difference:.2f} min)."

        if last_clockout_record:
            last_clockout_time = last_clockout_record.time_out
            time_difference = (clockin_time - last_clockout_time).total_seconds() / 60
            if time_difference < 60:
                return f"Skipping clock-in for {employee_id}, too soon after last clock-out ({time_difference:.2f} min)."

        return None  # No issues, proceed with clock-in


    @classmethod
    def clockin2(cls, db_session, employee_id, location, device_used, record_time=None):
        """Insert attendance into the database, ensuring no duplicate clock-ins."""

        current_app.logger.info(f"Starting clockin2 for employee_id: {employee_id}, record_time: {record_time}")

        # Check if the employee has an open attendance (i.e., they haven't clocked out yet)
        existing_attendance = db_session.query(cls).filter(
            and_(cls.employee_id == employee_id, cls.time_out == None, cls.work_status == None, cls.is_void == False)
        ).first()
        current_app.logger.info(f"Existing attendance check: {existing_attendance}")

        try:
            time_check_message = cls.check_time_difference(db_session, employee_id, record_time)
            current_app.logger.info(f"Time check message: {time_check_message}")
            if time_check_message:
                current_app.logger.info(f"Rejecting due to time check: {time_check_message}")
                return time_check_message
        except Exception as e:
            current_app.logger.error(f"Error checking time difference: {e}")

        # Get the employee's name
        employee = db_session.query(Employee).get(employee_id)
        employee_name = f"{employee.first_name} {employee.last_name}"
        current_app.logger.info(f"Processing for employee: {employee_name}")

        existing_record = db_session.query(cls).filter(and_(
            cls.employee_id == employee_id,
            or_(
                cls.time_in == datetime.strptime(record_time, "%Y-%m-%d %H:%M:%S"),
                cls.time_out == datetime.strptime(record_time, "%Y-%m-%d %H:%M:%S")
            ),
        cls.is_void == False)).first()
        current_app.logger.info(f"Existing record check: {existing_record}")
        if existing_record:
            current_app.logger.info(f"Rejecting due to existing record: {existing_record}")
            return f"{employee_name}, attendance already recorded."

        # Check if the employee is currently on leave or off
        current_date = datetime.now()
        overlapping_leave = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.work_status.in_(['leave', 'off']),  # Consider only leave/off statuses
                cls.time_off_begin_date <= current_date,
                cls.time_off_end_date >= current_date
            )
        ).first()
        current_app.logger.info(f"Overlapping leave check: {overlapping_leave}")

        if overlapping_leave:
            current_app.logger.info(f"Rejecting due to overlapping leave: {overlapping_leave}")
            return f"{employee_name}, clock-in not allowed while on leave or off."

        if existing_attendance:
            comparison = existing_attendance.time_in == datetime.strptime(record_time, "%Y-%m-%d %H:%M:%S")
            time_in = existing_attendance.time_in
            records_time = datetime.strptime(record_time, "%Y-%m-%d %H:%M:%S")
            current_app.logger.info(f"Record time: {records_time} and has a type of {type(records_time)}")
            current_app.logger.info(f"Time in: {time_in} and has a type of {type(time_in)}")
            current_app.logger.info(f"Comparison: {comparison}")

            # Check if clockin_time is the same as the existing attendance time
            if comparison:
                current_app.logger.info(f"Rejecting due to same timestamp: {comparison}")
                return f"{employee_name}, attendance already recorded."

            current_app.logger.info(f"Rejecting due to existing attendance: {existing_attendance}")
            return f"{employee_name}, already clocked in and have not clocked out yet."

        # Use the provided record_time or default to now
        clockin_time = datetime.strptime(record_time, "%Y-%m-%d %H:%M:%S") if record_time else datetime.now()
        current_app.logger.info(f"Creating new attendance record with time: {clockin_time}")

        # Proceed with clock-in
        attendance = cls(
            employee_id=employee_id,
            clockin_location=location,
            device_used=device_used,
            created_at=clockin_time,
            time_in=clockin_time  # Ensure recorded time is used
        )

        try:
            db_session.add(attendance)
            db_session.commit()
            current_app.logger.info(f"Successfully added attendance record for {employee_name}")
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Error adding attendance record: {e}")
            return f"Error recording attendance: {str(e)}"

        formatted_time = clockin_time.strftime('%d/%m/%Y %H:%M:%S')
        current_app.logger.info(f"{employee_name}, has successfully clocked in at {formatted_time}.")

        return f"{employee_name}, has successfully clocked in at {formatted_time}."

    @classmethod
    def clockout2(cls, db_session, employee_id, location, record_time=None):
        """Update attendance in the database for a clock-out, ensuring work_status is None."""

        # Retrieve employee's information
        employee = db_session.query(Employee).get(employee_id)
        employee_name = f"{employee.first_name} {employee.last_name}"

        # Check if the employee has an open attendance (i.e., they haven't clocked out yet and work_status is None)
        existing_attendance = db_session.query(cls).filter(
            and_(
                cls.employee_id == employee_id,
                cls.time_out == None,  # Not clocked out yet
                cls.work_status == None,  # Ensure it's a regular work status
                cls.is_void == False  # Ensure the attendance record is not voided
            )
        ).first()

        """try:
            time_check_message = cls.check_time_difference(db_session, employee_id, record_time)
            current_app.logger.info(f"Time check message: {time_check_message}")
            if time_check_message:
                return time_check_message
        except Exception as e:
            current_app.logger.error(f"Error checking time difference: {e}")
        """
        if not existing_attendance:
            return f"{employee_name}, you have not clocked in yet or you are on leave/off."
        clockout_time = datetime.strptime(record_time, "%Y-%m-%d %H:%M:%S") if record_time else datetime.now()
        # Update the attendance record with clock-out information
        existing_attendance.time_out = clockout_time
        existing_attendance.clockout_location = location
        db_session.commit()

        return f"{employee_name}, you have successfully clocked out."

    @classmethod
    def get_attendance(cls, db_session):
        """Get all attendance from the database."""
        attendances = db_session.query(cls).all()

        # convert to dictionary
        my_attendance = [attendance.to_dict() for attendance in attendances]
        # We sort the attendance that have not been voided where is_void is False
        sorted_attendance = [attendance for attendance in my_attendance if attendance['is_void'] == False or attendance['is_void'] == None]
        return sorted_attendance

    @classmethod
    def get_previous_day_attendance(cls, db_session):
        """Get all attendance from the database."""
        # Get the current date
        current_date = datetime.now()
        # Get the previous day
        previous_day = current_date - timedelta(days=1)
        # Get the attendance for the previous day
        attendances = db_session.query(cls).filter(cls.created_at >= previous_day, cls.created_at <= current_date).all()
        return [attendance.to_dict() for attendance in attendances]

    @classmethod
    def get_attendance_by_id(cls, db_session, attendance_id):
        """Get a single attendance by ID."""
        return db_session.query(cls).get(attendance_id)

    @classmethod
    def get_attendance_for_employee(cls, db_session, employee_id):
        """Get all attendance for a specific employee from the database."""
        try:
            try:
                # Query the database for attendance records for the given employee
                attendances = db_session.query(cls).filter_by(employee_id=employee_id).all()
                # current_app.logger.info(f"Attendance records for employee {employee_id}")
            except Exception as e:
                current_app.logger.error(f"Failed to get attendance for employee {employee_id}: {e}")
                return []

            # If no attendance records are found, return an empty list
            if not attendances:
                return []

            try:
                # Convert attendance objects to dictionaries
                my_attendance = [attendance.to_dict() for attendance in attendances]
                # current_app.logger.info(f"Converted attendance records to dict")
            except Exception as e:
                current_app.logger.error(f"Failed to convert attendance records to dict: {e}")
                return []

            try:
                # Filter out attendance records where 'is_void' is True
                sorted_attendance = [attendance for attendance in my_attendance if not attendance.get('is_void')]

                return sorted_attendance
            except Exception as e:
                current_app.logger.error(f"Failed to filter attendance records: {e}")
                return []
        except Exception as e:
            # Log the error and return an empty list
            current_app.logger.error(f"Failed to get attendance for employee {employee_id}: {e}")
            return []


    @classmethod
    def get_all_attendance_in_range(cls, db_session, begin_date, end_date):
        """Get all attendance in a given date range."""
        try:
            attendance = db_session.query(cls).filter(
                and_(cls.created_at >= begin_date, cls.created_at <= end_date)
            ).all()
            return [attendance.to_dict() for attendance in attendance]
        except Exception as e:
            current_app.logger.error(f"Failed to get attendance in range: {e}")
            return None

    @classmethod
    def add_work_status(cls, db_session, employee_id, work_status, time_off_begin_date=datetime.now(), time_off_end_date=datetime.now()):
        """Add work status to the attendance."""
        message="Leave or off days recorded successfully for the employee."
        # check if the time_off_begin_date is greater than time_off_end_date
        if time_off_begin_date > time_off_end_date:
            current_app.logger.error("The begin date cannot be greater than the end date")
            return False, "The begin date cannot be greater than the end date"

        # Check if the employee is not on leave in the given period
        leave_range = db_session.query(cls).filter(
            and_(cls.employee_id == employee_id, cls.time_off_begin_date <= time_off_end_date,
                  cls.time_off_end_date >= time_off_begin_date)
        ).first()
        if leave_range:
            return False, "Employee is already on leave in the given period"

        if work_status == "annual_leave":
            # Block to check if employee is eligible to apply for leave
            days_difference = Auxillary.calculate_days_difference(time_off_begin_date, time_off_end_date)
            is_eligible, message = Employee.can_request_leave_or_off(db_session, employee_id, days_difference)

            if not is_eligible:
                return False, message

        try:
            Attendance = cls(
                employee_id=employee_id,
                work_status=work_status,
                time_off_begin_date=time_off_begin_date,
                time_off_end_date=time_off_end_date,
                device_used="N/A"
            )

            if work_status == "annual_leave":
                # Update employee leave balance
                _, message = Employee.update_employee_leave_balance(db_session, employee_id, days_difference)
            db_session.add(Attendance)
            db_session.commit()
            return Attendance, message
        except Exception as e:
            current_app.logger.exception(f"Failed to add work status: {e}")
            db_session.rollback()
            return False, "Failed to record leave or off days for the employee."

    @classmethod
    def get_leave_records(cls, db_session):
        """Get all attendance records where the employee did not clock in."""
        try:
            # Query all attendance records where time_in is None
            missing_clockin_records = db_session.query(cls).filter(cls.time_in == None).all()

            leave_records = []
            for record in missing_clockin_records:
                try:
                    # Convert each record to a dictionary and add it to the list
                    leave_records.append(record.to_dict())
                except Exception as inner_e:
                    current_app.logger.error(f"Error converting record to dict: {inner_e}")
                    continue  # Skip problematic records and continue processing the others

            return leave_records
        except Exception as e:
            current_app.logger.error(f"Error getting leave records: {e}")
            return []

    @classmethod
    def update_leave_record(
        cls, db_session, attendance_id,
        time_off_begin_date, time_off_end_date,
        remarks, work_status):
        """Update a leave record."""
        try:
            # Get the record by ID
            record = db_session.query(cls).get(attendance_id)
            if record is None:
                return f"Record with id {attendance_id} not found"

            # Update the record
            record.work_status = work_status
            record.time_off_begin_date = time_off_begin_date
            record.time_off_end_date = time_off_end_date
            record.remarks = remarks

            db_session.commit()
            return record
        except Exception as e:
            db_session.rollback()
            return f"An error occurred: {str(e)}"

    @classmethod
    def get_employee_attendance(cls, db_session, employee, current_month, current_year, total_days_in_month):
        """Helper function to get attendance details for a single employee."""
        try:
            attendance = Attendance.get_attendance_for_employee(db_session, employee['employee_id'])
            # current_app.logger.info(f"attendance from get_employee_attendance: {attendance}")

            days_present = set()
            days_on_leave = set()
            days_off = set()
            total_hours_worked = timedelta()

            for record in attendance:
                time_in = record['time_in']
                time_out = record['time_out']
                # current_app.logger.info(f"time_in: {time_in} and the type is {type(time_in)}")
                # current_app.logger.info(f"time_out: {time_out} and the type is {type(time_out)}")
                if time_in is None:
                    work_status = record.get('work_status')
                    if work_status and work_status.lower() == 'leave':
                        leave_days = cls._get_time_off_days(
                            record, current_month, current_year, total_days_in_month)
                        days_on_leave.update(leave_days)

                    elif work_status and work_status.lower() == 'off':
                        off_days = cls._get_time_off_days(
                            record, current_month, current_year, total_days_in_month)
                        days_off.update(off_days)

                else:
                    cls._process_time_in_record(
                        record, current_year, current_month, days_present, total_hours_worked)

            try:
                # current_app.logger.info("getting attendance status")
                attendance_status = cls._generate_attendance_status(
                    total_days_in_month,
                    days_present,
                    days_on_leave,
                    days_off)
            except Exception as e:
                current_app.logger.error(f"Error generating attendance status: {e}")
                attendance_status = []

            days_worked = len(days_present)
            days_leave = len(days_on_leave)
            days_off_count = len(days_off)
            days_absent = total_days_in_month - (days_worked + days_leave + days_off_count)

            total_hours_worked = cls._convert_hours_to_decimal(total_hours_worked)
            paid_days = days_worked + days_leave + days_off_count
            applicable_net_salary = cls._calculate_net_salary(employee, paid_days, total_days_in_month)

            return {
                'employee': employee,
                'attendance_status': attendance_status,
                'days_worked': days_worked,
                'days_absent': days_absent,
                'attendance': attendance,
                "days_leave": days_leave,
                'days_off_count': days_off_count,
                'total_hours_worked': total_hours_worked,
                'paid_days': paid_days,
                'applicable_net_salary': applicable_net_salary
            }

        except Exception as e:
            current_app.logger.error(f"Error getting attendance: {e}")
            return None

    @classmethod
    def _get_time_off_days(cls, record, current_month, current_year, total_days_in_month):
        """Helper function to calculate time off days (leave/off)."""
        time_off_begin_date = datetime.strptime(record['time_off_begin_date'], '%d/%m/%Y')
        time_off_end_date = datetime.strptime(record['time_off_end_date'], '%d/%m/%Y')

        if time_off_begin_date.month != current_month:
            time_off_begin_date = datetime(current_year, current_month, 1)
        if time_off_end_date.month != current_month:
            time_off_end_date = datetime(current_year, current_month, total_days_in_month)

        return {day.day for day in [time_off_begin_date + timedelta(days=i)
                                    for i in range((time_off_end_date - time_off_begin_date).days + 1)]}

    @classmethod
    def _process_time_in_record(cls, record, current_year, current_month, days_present, total_hours_worked):
        """Helper function to process time in records."""
        record['time_in'] = datetime.strptime(record['time_in'], '%d/%m/%Y %H:%M:%S')
        if record['time_in'].year == current_year and record['time_in'].month == current_month:
            days_present.add(record['time_in'].day)
            total_duration = record.get('total_duration')
            # current_app.logger.info(f"total_duration: {total_duration} and the type is {type(total_duration)}")
            if isinstance(total_duration, str):
                total_duration = timedelta(hours=int(total_duration.split(':')[0]), minutes=int(total_duration.split(':')[1]))
            # current_app.logger.info(f"total_duration after converting: {total_duration} and the type is {type(total_duration)}")
            if total_duration:
                total_hours_worked += total_duration

    @classmethod
    def _generate_attendance_status(cls, total_days_in_month, days_present, days_on_leave, days_off):
        """Helper function to generate attendance status list."""
        return [
            'P' if day in days_present else
            'L' if day in days_on_leave else
            'O' if day in days_off else
            'A' for day in range(1, total_days_in_month + 1)
        ]

    @classmethod
    def _convert_hours_to_decimal(cls, total_hours_worked):
        """Helper function to convert total hours worked to decimal hours."""
        # current_app.logger.info(f"Raw total_hours_worked: {total_hours_worked}, Type: {type(total_hours_worked)}")

        if isinstance(total_hours_worked, timedelta):
            total_hours = total_hours_worked.total_seconds() / 3600
            return round(total_hours, 2)

        # If the type is incorrect, log an error
        current_app.logger.error(f"Unexpected type for total_hours_worked: {type(total_hours_worked)}")
        return 0  # Return 0 if an invalid type is found


    @classmethod
    def _calculate_net_salary(cls, employee, paid_days, total_days_in_month):
        """Helper function to calculate the applicable net salary."""
        net_salary = employee['net_salary']
        if net_salary:
            return round((net_salary / total_days_in_month) * paid_days)
        return 0

    @classmethod
    def reverse_geocode(cls, lat, lon):
        """Reverse geocode the given latitude and longitude."""
        api_key = os.getenv("GEOCODING_API_KEY")
        url = f"https://geocode.maps.co/reverse?lat={lat}&lon={lon}&api_key={api_key}"

        try:
            response = requests.get(url)
            response.raise_for_status()  # Raise an error for bad responses (4xx, 5xx)
            data = response.json()
            return data  # Returns the full response JSON
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}

    @classmethod
    def change_attendace_status(cls, db_session, attendance_id, status, void_reason=None):
        """Change the status of an attendance record."""
        try:
            record = db_session.query(cls).get(attendance_id)
            if record is None:
                return f"Record with id {attendance_id} not found"

            if status.lower() == 'void':
                record.is_void = True
                record.void_reason = void_reason
            else:
                record.is_void = False
                record.void_reason = void_reason

            db_session.commit()
            return record
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Failed to change status: {e}")
            return False

    @classmethod
    def get_voided_attendance(cls, db_session):
        """Get all voided attendance records."""
        try:
            attendance = db_session.query(cls).filter(cls.is_void == True).all()
            return [record.to_dict() for record in attendance]
        except Exception as e:
            current_app.logger.error(f"Failed to get voided attendance: {e}")
            return []

    @classmethod
    def get_images_by_subject(cls, api_key, base_url):
        """Retrieve images by subject from the CompreFace API."""
        headers = {'x-api-key': api_key}
        faces_url = f"{base_url}/faces"
        images = []

        try:
            # Request to get faces
            response = requests.get(faces_url, headers=headers)
            if response.status_code != 200:
                current_app.logger.error(f"Failed to retrieve faces: {response.status_code}")
                return images

            # Extract face data from the response
            faces = response.json().get('faces', [])
            for face in faces:
                subject_name = face.get('subject')  # Subject name
                image_id = face.get('image_id')     # Image ID
                if subject_name and image_id:
                    image_url = f"{faces_url}/{image_id}/img"  # URL for image download
                    images.append({'subject_name': subject_name, 'image_id': image_id, 'image_url': image_url})

        except Exception as e:
            current_app.logger.error(f"Error retrieving images: {e}")

        return images

    @classmethod
    def calculate_attendance(cls, attendance, current_year, current_month, total_days_in_month):
        """Calculate attendance details for an employee."""
        days_present = set()
        days_on_leave = set()
        days_off = set()
        total_hours_worked = timedelta()

        if not isinstance(attendance, list):
            current_app.logger.error(f"Invalid attendance data format: {attendance}")
            return days_present, days_on_leave, days_off, total_hours_worked

        for record in attendance:
            try:
                if 'time_in' not in record or 'work_status' not in record:
                    current_app.logger.warning(f"Malformed record encountered: {record}")
                    continue

                if record['time_in'] is None:
                    time_off_days = cls._get_time_off_days(record, current_year, current_month, total_days_in_month)
                    work_status = record.get('work_status')
                    if work_status and work_status.lower() == 'leave':
                        days_on_leave.update(time_off_days)
                    elif work_status and work_status.lower() == 'off':
                        days_off.update(time_off_days)
                else:
                    record['time_in'] = datetime.strptime(record['time_in'], '%d/%m/%Y %H:%M:%S')
                    if record['time_in'].year == current_year and record['time_in'].month == current_month:
                        days_present.add(record['time_in'].day)
                        if record.get('total_duration'):
                            total_hours_worked += record['total_duration']
            except IndexError as ie:
                current_app.logger.error(f"Index error in record processing: {ie}")
            except Exception as e:
                current_app.logger.error(f"Error processing record: {e}")

        return days_present, days_on_leave, days_off, total_hours_worked

    @classmethod
    def get_attendance_by_date(cls, db_session, date):
        """Get categorized attendance records for the given date."""
        try:
            # Clocked-in employees
            from sqlalchemy import func
            # get those who have clocked in and is_void is False

            clocked_in = db_session.query(cls).filter(
                and_(
                    cls.is_void.is_(False),
                    func.DATE(cls.time_in) == date
                )
            ).all()

            # Employees on leave
            on_leave = db_session.query(cls).filter(
                and_(
                    cls.work_status == 'leave',
                    cls.time_off_begin_date <= date,
                    cls.time_off_end_date >= date,
                )
            ).all()

            # Employees on off
            on_off = db_session.query(cls).filter(
                and_(
                    cls.work_status == 'off',
                    cls.time_off_begin_date <= date,
                    cls.time_off_end_date >= date,

                )
            ).all()

            # field clockin
            field_clockin = db_session.query(cls).filter(
                and_(
                    cls.field_in != None,
                    func.DATE(cls.field_in) == date
                )
            ).all()

            return {
                "clocked_in": [record.to_dict() for record in clocked_in],
                "on_leave": [record.to_dict() for record in on_leave],
                "on_off": [record.to_dict() for record in on_off],
                "field_clockin": [record.to_dict() for record in field_clockin]
            }
        except Exception as e:
            current_app.logger.error(f"Failed to get attendance by date: {e}")
            return {"clocked_in": [], "on_leave": [], "on_off": []}

    @classmethod
    def auto_clockout(cls, db_session):
        """A method for auto clockout."""
        from app.models.company_shifts import Shift
        from datetime import datetime, timedelta

        # Fetch current datetime
        current_datetime = datetime.now()
        # print(f"Current time: {current_datetime}")

        # Initialize the employees who have been auto clocked out
        auto_clocked_out_employees = []

        # Fetch all shifts for the company
        shifts = Shift.get_shifts(db_session)
        if not shifts:
            # current_app.logger.info("No shifts found for the company.")
            return "No shifts found for the company."

        # print(f"Shifts: {shifts}")

        # Filter attendance records where clockout is needed
        try:
            attendances_to_clockout = db_session.query(cls).filter(
                cls.time_in.isnot(None),
                cls.time_out.is_(None),
                cls.work_status.is_(None),
                cls.is_void.is_(False)
            ).all()
            # current_app.logger.info(f"Attendances to clockout: {attendances_to_clockout}")
        except Exception as e:
            current_app.logger.error(f"Error occurred: {str(e)}")
            attendances_to_clockout = []

        if not attendances_to_clockout:
            message = "No employees to auto clockout."
            # current_app.logger.info(message)
            return message

        for attendance in attendances_to_clockout:
            matched_shift = None

            for shift in shifts:

                total_allowed_duration = shift['shift_duration'] + shift['auto_clock_out_hours']
                # current_app.logger.info(f'')

                # Calculate the time worked by the employee
                time_in_datetime = attendance.time_in
                # current_app.logger.info(f"Time in: {time_in_datetime} and the type is {type(time_in_datetime)}")
                time_worked = current_datetime - time_in_datetime
                # current_app.logger.info(f"Time worked: {time_worked} and the type is {type(time_worked)}")

                # Match the employee's `time_in` with the shift
                if shift['start_time'] <= time_in_datetime.time() <= shift['end_time']:
                    matched_shift = shift
                    # convert the total allowed duration to timedelta
                    total_allowed_duration_timedelta = timedelta(hours=total_allowed_duration)
                    # current_app.logger.info(f"Total allowed duration: {total_allowed_duration_timedelta}")
                    # Auto clockout if the time worked exceeds the allowed duration
                    if time_worked > total_allowed_duration_timedelta:
                        attendance.time_out = current_datetime
                        attendance.recognition_status = "Auto Clockout"
                        attendance.clockout_location = "Auto Clockout"
                        attendance.device_used = "Auto Clockout"
                        attendance.clockout_location_name = "Auto Clockout"
                        employee_name = f"{attendance.Employee.first_name} {attendance.Employee.last_name}"
                        # add the employee to the auto clocked out list
                        auto_clocked_out_employees.append(employee_name)
                        # print(f"Employee {employee_name} has been auto clocked out.")
                        break

            # Log unmatched attendance
            if not matched_shift:
                time_in_datetime = attendance.time_in
                time_worked = current_datetime - time_in_datetime
                # current_app.logger.info(f"Time worked: {time_worked} and the type is {type(time_worked)}")
                # check if the time worked is at least 2 hours
                if time_worked >= timedelta(hours=2):
                    attendance.time_out = current_datetime
                    attendance.recognition_status = "Auto Clockout"
                    attendance.clockout_location = "Auto Clockout"
                    attendance.device_used = "Auto Clockout"
                    attendance.clockout_location_name = "Auto Clockout"
                    employee_name = f"{attendance.Employee.first_name} {attendance.Employee.last_name}"
                    auto_clocked_out_employees.append(employee_name)
                    # current_app.logger.info(f"Employee {employee_name} no matching shift and has been auto clocked out.")

        # Commit changes after processing all records
        db_session.commit()
        # print("Auto clockout completed.")
        return "Auto clockout completed.", auto_clocked_out_employees

    @classmethod
    def is_within_perimeter(cls, clockin_lat, clockin_lon, business_lat, business_lon, radius_meters=100):
        """
        Checks if the clock-in location is within a specified radius of the business location using the Haversine formula.

        Args:
            clockin_lat (float): Latitude of the clock-in location.
            clockin_lon (float): Longitude of the clock-in location.
            business_lat (float): Latitude of the business location.
            business_lon (float): Longitude of the business location.
            radius_meters (float): The radius within which the clock-in location must fall (default is 50 meters).

        Returns:
            tuple: (distance in meters, bool) - True if within the radius, False otherwise.
        """
        try:
            # Ensure all values are present
            if None in [clockin_lat, clockin_lon, business_lat, business_lon]:
                current_app.logger.warning("Invalid coordinates: one or more values are None")
                return None, False

            # Convert latitudes and longitudes to radians
            clockin_lat = math.radians(Decimal(clockin_lat))
            clockin_lon = math.radians(Decimal(clockin_lon))
            business_lat = math.radians(Decimal(business_lat))
            business_lon = math.radians(Decimal(business_lon))

            # Haversine formula
            delta_phi = business_lat - clockin_lat
            delta_lambda = business_lon - clockin_lon

            a = (math.sin(delta_phi / 2) ** 2 +
                 math.cos(clockin_lat) * math.cos(business_lat) *
                 math.sin(delta_lambda / 2) ** 2)
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

            # Earth radius in meters
            R = 6371000  # meters
            distance = R * c  # distance in meters

            # current_app.logger.info(f"Calculated distance: {distance:.2f} meters")

            # Check if the distance is within the allowed perimeter
            return distance, distance <= radius_meters

        except Exception as e:
            current_app.logger.error(f"Error in is_within_perimeter: {str(e)}")
            return None, False
    
    @classmethod
    def get_unique_locations(cls, attendance):
        """Extract unique locations from attendance and field_attendance records."""
        locations = OrderedDict()
        
        # Process attendance records
        for record in attendance:
            if 'clockin_location_name' in record and record['clockin_location_name']:
                locations[record['clockin_location_name']] = {
                    'location_name': record['clockin_location_name'],
                    'location_id': record.get('clockin_location')
                }
            if 'clockout_location_name' in record and record['clockout_location_name']:
                locations[record['clockout_location_name']] = {
                    'location_name': record['clockout_location_name'],
                    'location_id': record.get('clockout_location')
                }    
        
        return list(locations.values())

class Site(DynamicBase):
    __tablename__ = 'sites'
    site_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    site_name = Column(String(128), nullable=False)
    location = Column(String(256), nullable=True)
    longitude = Column(Numeric(9, 6), nullable=True)
    latitude = Column(Numeric(9, 6), nullable=True)
    employees = relationship("Employee", back_populates="Sites")
    attendance = relationship("Attendance", back_populates="Sites")

    def __repr__(self):
        """Return a string representation of the object."""
        return f"Site: {self.site_name}, Site ID: {self.site_id}, Location: {self.location}"

    def to_dict(self):
        """Convert site object to dictionary."""
        return {
            "site_id": self.site_id,
            "site_name": self.site_name,
            "location": self.location,
            "longitude": self.longitude,
            "latitude": self.latitude
        }

    @classmethod
    def add_site(cls, db_session, site_name, location, longitude, latitude):
        """add a site into the database."""
        if latitude and longitude:
            site_location = Attendance.reverse_geocode(latitude, longitude)
            location = site_location.get('display_name', 'Unknown')
        else:
            location = location
        site = cls(
            site_name=site_name,
            location=location,
            longitude=longitude,
            latitude=latitude
        )
        db_session.add(site)
        db_session.commit()
        return site

    @classmethod
    def get_sites(cls, db_session):
        """Get all sites from the database."""
        sites = db_session.query(cls).all()
        return [site.to_dict() for site in sites]

    @classmethod
    def get_site_by_id(cls, db_session, site_id):
        """Get a site by ID."""
        try:
            # Validate site_id
            if not site_id:
                current_app.logger.warning("site_id is None or invalid.")
                return []

            # Query the site
            site = db_session.query(cls).get(site_id)

            if not site:
                return []

            # Convert to a dictionary
            return site.to_dict()
        except Exception as e:
            current_app.logger.error(f"Failed to get site by id: {e}")
            return []

    @classmethod
    def update_site(cls, db_session, site_id, site_name, location):
        try:
            site = db_session.query(cls).get(site_id)
            if site is None:
                return f"Site with id {site_id} not found"

            site.site_name = site_name
            site.location = location

            db_session.commit()
            return site
        except Exception as e:
            db_session.rollback()
            return f"An error occurred: {str(e)}"

class LeaveApplication(DynamicBase):
    __tablename__ = "leave_applications"

    leave_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=False)
    attendance_id = Column(UUID(as_uuid=True), ForeignKey('attendance.attendance_id'), nullable=True)
    leave_type = Column(Enum('leave', 'off','annual_leave', name='leave_types_enum'), nullable=False)
    time_off_begin_date = Column(DateTime, nullable=False)
    time_off_end_date = Column(DateTime, nullable=False)
    reason = Column(Text, nullable=True)
    status = Column(Enum('pending', 'approved', 'rejected', name='leaves_status_enum'), default='pending')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    Employee = relationship("Employee", back_populates="leave_applications", foreign_keys=[employee_id])
    Attendance = relationship("Attendance", back_populates="LeaveApplications")
    Approvals = relationship("LeaveApproval", back_populates="LeaveApplication", cascade="all, delete-orphan")
    ApprovalLogs = relationship("ApprovalLog", back_populates="LeaveApplication", cascade="all, delete-orphan")

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        leave_id: {self.leave_id}, employee_id: {self.employee_id}, leave_type: {self.leave_type},
        time_off_begin_date: {self.time_off_begin_date}, time_off_end_date: {self.time_off_end_date},
        reason: {self.reason}, status: {self.status}
        """

    def to_dict(self):
        """Convert leave application object to dictionary."""
        # format the dates to dd/mm/yyyy
        time_off_begin_date = self.time_off_begin_date.strftime('%d/%m/%Y') if self.time_off_begin_date else None
        time_off_end_date = self.time_off_end_date.strftime('%d/%m/%Y') if self.time_off_end_date else None
        created_at = self.created_at.strftime('%d/%m/%Y') if self.created_at else None
        updated_at = self.updated_at.strftime('%d/%m/%Y') if self.updated_at else None
        return {
            "leave_id": self.leave_id,
            "employee_id": self.employee_id,
            "leave_type": self.leave_type,
            "time_off_begin_date": time_off_begin_date,
            "time_off_end_date": time_off_end_date,
            "reason": self.reason,
            "status": self.status,
            "created_at": created_at,
            "updated_at": updated_at,
            "employee_name": self.Employee.first_name + " " + self.Employee.last_name,
            "attendance_id": self.attendance_id if self.attendance_id else None
        }

    @classmethod
    def insert_leave_application(cls, db_session, employee_id,
                                 leave_type, time_off_begin_date,
                                 time_off_end_date, reason):
        """Insert leave application into the database."""
        leave_application = cls(
            employee_id=employee_id,
            leave_type=leave_type,
            time_off_begin_date=time_off_begin_date,
            time_off_end_date=time_off_end_date,
            reason=reason,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(leave_application)
        db_session.commit()
        return leave_application

    @classmethod
    def get_leave_applications(cls, db_session):
        """Get all leave applications from the database."""
        leave_applications = db_session.query(cls).all()
        return [leave_application.to_dict() for leave_application in leave_applications]

    @classmethod
    def get_leave_application_by_id(cls, db_session, leave_id):
        """Get a leave application by ID."""
        leave_application = db_session.query(cls).get(leave_id)
        try:
            # convert to a dictionary
            converted = leave_application.to_dict()
            current_app.logger.info("Leave application retrieved successfully")
            return converted
        except Exception as e:
            current_app.logger.error(f"Failed to get leave application by id: {e}")
            return []

    @classmethod
    def get_leave_application_for_employee(cls, db_session, employee_id):
        """Get all leave applications for a specific employee from the database."""
        leave_applications = db_session.query(cls).filter_by(employee_id=employee_id).all()
        return [leave_application.to_dict() for leave_application in leave_applications]

    @classmethod
    def view_leave_applications(cls, db_session):
        """Get all leave applications for all employees from the database."""
        try:
            # Query the database for all leave applications that are pending
            leave_applications = db_session.query(cls).filter(cls.status == 'pending').all()
            current_app.logger.info("Leave applications retrieved successfully")
        except Exception as e:
            current_app.logger.error(f"Failed to get leave applications: {e}")
            return []
        try:
            converted = [leave_application.to_dict() for leave_application in leave_applications]
            current_app.logger.info("Leave applications converted successfully")
            return converted
        except Exception as e:
            current_app.logger.error(f"Failed to convert leave applications: {e}")
            return []

    @classmethod
    def apply_leave_to_attendance(cls, db_session, leave_id):
        """Apply leave to the attendance table once approved."""
        try:
            # Fetch the leave application
            leave_application = db_session.query(cls).filter_by(leave_id=leave_id, status='approved').first()
            if not leave_application:
                raise ValueError("Leave application not found or not yet approved.")

            # Check if leave is already applied to attendance
            if leave_application.attendance_id:
                current_app.logger.info(f"Leave already applied for leave_id: {leave_id}")
                return {"message": "Leave already applied to attendance."}

            # Update the attendance table
            attendance = db_session.query(Attendance).filter_by(
                employee_id=leave_application.employee_id,
                time_off_begin_date=leave_application.time_off_begin_date,
                time_off_end_date=leave_application.time_off_end_date,
                work_status=leave_application.leave_type
            ).first()

            current_app.logger.info(f"Attendance {attendance}")
            if not attendance:
                # Create a new attendance record if not exists
                attendance = Attendance(
                    employee_id=leave_application.employee_id,
                    time_off_begin_date=leave_application.time_off_begin_date,
                    time_off_end_date=leave_application.time_off_end_date,
                    work_status=leave_application.leave_type,
                    remarks="Leave applied automatically after approval.",
                    device_used="System auto-apply"
                )
                db_session.add(attendance)
                db_session.flush()  # Flush to generate the attendance_id without committing

            # Link the leave application to the attendance record
            leave_application.attendance_id = attendance.attendance_id
            db_session.commit()

            current_app.logger.info(f"Leave applied to attendance successfully for leave_id: {leave_id}")
            return {"message": "Leave applied to attendance successfully."}

        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Error applying leave to attendance for leave_id {leave_id}: {e}")
            return {"error": str(e)}

class LeaveApproval(DynamicBase):
    __tablename__ = "leave_approvals"

    approval_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    leave_id = Column(UUID(as_uuid=True), ForeignKey('leave_applications.leave_id'), nullable=False)
    approver_id = Column(UUID(as_uuid=True), nullable=False)  # Likely references an Employee ID or a User ID
    approver_role = Column(String(128), nullable=False)
    status = Column(Enum('pending', 'approved', 'rejected', name='approval_status_enum'), default='pending')
    remarks = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    LeaveApplication = relationship("LeaveApplication", back_populates="Approvals")

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        approval_id: {self.approval_id}, leave_id: {self.leave_id}, approver_id: {self.approver_id},
        approver_role: {self.approver_role}, status: {self.status}, remarks: {self.remarks}
        """

    def to_dict(self):
        """Convert leave approval object to dictionary."""
        # format the dates to dd/mm/yyyy
        created_at = self.created_at.strftime('%d/%m/%Y') if self.created_at else None
        updated_at = self.updated_at.strftime('%d/%m/%Y') if self.updated_at else None
        return {
            "approval_id": self.approval_id,
            "leave_id": self.leave_id,
            "approver_id": self.approver_id,
            "approver_role": self.approver_role,
            "status": self.status,
            "remarks": self.remarks,
            "created_at": created_at,
            "updated_at": updated_at,
            "employee_name": self.LeaveApplication.Employee.first_name + " " + self.LeaveApplication.Employee.last_name,
            "leave_type": self.LeaveApplication.leave_type,
            "time_off_begin_date": self.LeaveApplication.time_off_begin_date.strftime('%d/%m/%Y') if self.LeaveApplication.time_off_begin_date else None,
            "time_off_end_date": self.LeaveApplication.time_off_end_date.strftime('%d/%m/%Y') if self.LeaveApplication.time_off_end_date else None,
            "reason": self.LeaveApplication.reason
            #"approver_name": LeaveApproval.get_approver_name(self.approver_id, self.approver_role, db_session)
        }

    @classmethod
    def get_approver_name(cls, approver_id, approver_role, db_session=None):
        """Get the name of the approver."""
        if approver_role == 'supervisor':
            current_app.logger.info(f"Getting supervisor name for approver_id: {approver_id}")
            try:
                supervisor = User.get_user_by_id(db_session, approver_id)
                current_app.logger.info(f"Supervisor: {supervisor}")

                # Check if supervisor is a non-empty dictionary
                if supervisor and isinstance(supervisor, dict) and len(supervisor) > 0:
                    if 'full_name' in supervisor:
                        supervisor_name = supervisor['full_name']
                    elif 'employee_id' in supervisor:
                        # If user is linked to an employee, get the employee's name
                        employee = Employee.get_employee_by_id(db_session, supervisor['employee_id'])
                        if employee and isinstance(employee, dict) and len(employee) > 0:
                            supervisor_name = f"{employee.get('first_name', '')} {employee.get('last_name', '')}"
                        else:
                            supervisor_name = supervisor.get('username', 'Unknown')
                    else:
                        supervisor_name = supervisor.get('username', 'Unknown')
                else:
                    current_app.logger.warning(f"Supervisor not found or empty: {supervisor}")
                    return "Unknown"
            except Exception as e:
                current_app.logger.error(f"Failed to get approver name: {e}")
                return "Unknown"
        else:
            try:
               supervisor = CentralUser.get_user_by_id(approver_id)
               if supervisor and isinstance(supervisor, dict) and len(supervisor) > 0:
                   supervisor_name = f"{supervisor.get('first_name', '')} {supervisor.get('last_name', '')}"
               else:
                   current_app.logger.warning(f"Central user not found or empty: {supervisor}")
                   return "Unknown"
            except Exception as e:
                current_app.logger.error(f"Failed to get approver name: {e}")
                return "Unknown"

        current_app.logger.info(f"Approver name retrieved successfully: {supervisor_name}")
        return supervisor_name

    @classmethod
    def apply_update_to_employee(cls, db_session, leave_application):
        # Deduct employee leave balance
        days_difference = Auxillary.calculate_days_difference(
            leave_application.time_off_begin_date.strftime('%Y-%m-%d'),
            leave_application.time_off_end_date.strftime('%Y-%m-%d')
        )
        is_eligible, message = Employee.can_request_leave_or_off(db_session, str(leave_application.employee_id), days_difference)
        if not is_eligible:
            raise ValueError(message)
        is_updated, update_message = Employee.update_employee_leave_balance(db_session,
                                                                            str(leave_application.employee_id),
                                                                            days_difference)

        # Raise exception for this method to rollback the new approval recorded in the db
        if not is_updated:
            raise ValueError(update_message)

    @classmethod
    def approve_leave_application(cls, db_session, leave_id, approver_id, approver_role, status='approved', remarks=None, leave_type=""):
        """Approve a leave application."""
        from app.models.company_approval_work_flow import ApprovalWorkflow
        print("Received: ", remarks)
        print("Yeah: ", leave_type)
        approval_type = 'leave'
        # leave_type = "None"

        try:
            # Fetch the LeaveApplication
            leave_application = db_session.query(LeaveApplication).filter_by(leave_id=leave_id).first()
            if not leave_application:
                return f"Leave application with ID {leave_id} not found."

            # If leave is already rejected or approved, no further action is allowed
            if leave_application.status in ['rejected', 'approved']:
                return f"Leave application is already {leave_application.status}. No further action allowed."

            # Get the sequence for the approver's role
            workflow = ApprovalWorkflow.get_workflow_by_role_and_approval_type(
                db_session, approver_role, approval_type
            )
            if not workflow:
                return f"No approval sequence defined for the role: {approver_role}"

            sequence_order = workflow['sequence_order']
            current_app.logger.info(f"Retrieved sequence order: {sequence_order}")

            # Check if this role has already approved the leaveapproval_type
            existing_approval = db_session.query(LeaveApproval).filter_by(
                leave_id=leave_id, approver_role=approver_role
            ).first()
            if existing_approval:
                return f"The leave application has already been approved by {approver_role}."

            # Get the highest sequence order already approved
            approved_sequences = [
                ApprovalWorkflow.get_workflow_by_role_and_approval_type(
                    db_session, approval.approver_role, approval_type
                )['sequence_order']
                for approval in leave_application.Approvals
            ]
            highest_approved_sequence = max(approved_sequences, default=0)

            # Check if the current approver is authorized
            if sequence_order != highest_approved_sequence + 1:
                return "You are not authorized to approve this leave at this stage."

            # Approve the leave
            new_approval = LeaveApproval(
                leave_id=leave_id,
                approver_id=approver_id,
                approver_role=approver_role,
                status=status,
                remarks=remarks,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            db_session.add(new_approval)

            # Check if this is the final approval
            last_sequence_order = ApprovalWorkflow.get_the_last_sequence_order(db_session, approval_type)
            if sequence_order == last_sequence_order:
                leave_application.status = status

                if status == 'rejected':
                    current_app.logger.info(f"Leave application {leave_id} fully rejected and status updated.")
                    return f"Leave application {status} successfully by {approver_role}."
                current_app.logger.info(f"Leave application {leave_id} fully approved and status updated.")

                if leave_type == "annual_leave":
                    # Apply changes for annual leave - Other types of leave to be checked after
                    LeaveApproval.apply_update_to_employee(db_session, leave_application)

                # Apply leave to the attendance table
                try:
                    response = LeaveApplication.apply_leave_to_attendance(db_session, leave_id)
                    if "error" in response:
                        current_app.logger.error(f"Failed to apply leave for leave_id {leave_id}: {response['error']}")
                        return "An error occurred while applying leave to attendance."

                    db_session.commit() # Commit after all operations are successful
                    current_app.logger.info(f"Leave successfully applied to attendance for leave_id {leave_id}.")
                    return f"Leave application {status} successfully by {approver_role}."
                except Exception as e:
                    db_session.rollback()
                    current_app.logger.error(f"Error applying leave to attendance: {e}")
            else:
                current_app.logger.info(f"Leave application {leave_id} {status} by {approver_role}.")
                db_session.commit()
                return f"Leave application {status} successfully by {approver_role}."

        except Exception as e:
            db_session.rollback() # Commit after all operations are successful
            current_app.logger.error(f"Error approving leave application: {e}")
            return "An error occurred while approving the leave application."


    @classmethod
    def get_leave_approvals(cls, db_session):
        """Get all leave approvals from the database."""
        leave_approvals = db_session.query(cls).all()
        try:
            converted = [leave_approval.to_dict() for leave_approval in leave_approvals]
            current_app.logger.info("Leave approvals retrieved successfully")
            return converted
        except Exception as e:
            current_app.logger.error(f"Failed to get leave approvals: {e}")
            return []

    @classmethod
    def get_leave_approvals_by_id(cls, db_session, leave_id):
        """Get a leave approvals by leave_id."""
        leave_approvals = db_session.query(cls).filter_by(leave_id=leave_id).all()
        try:
            if not leave_approvals:
                return []
            converted = [leave_approval.to_dict() for leave_approval in leave_approvals]
            return converted
        except Exception as e:
            current_app.logger.error(f"Failed to get leave approvals by id: {e}")
            return []

    @classmethod
    def get_leave_approvals_for_employee(cls, db_session, employee_id):
        """Get all leave approvals for an employee."""
        leave_approvals = db_session.query(cls).all()
        try:
            if not leave_approvals:
                return []
            dict_approvals = [leave_approval.to_dict() for leave_approval in leave_approvals \
                              if str(leave_approval.LeaveApplication.employee_id) == employee_id]

            return dict_approvals
        except Exception as e:
            current_app.logger.error(f"Failed to get leave approvals for employee: {e}")
            raise