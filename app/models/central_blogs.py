import uuid
from app import db
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
from app.models.central import User
import boto3
import os
from app.helpers.company_helpers import CompanyHelpers
from flask import current_app
from sqlalchemy import func

from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

blog_post_categories = db.Table(
    'blog_post_categories',
    db.Column('blog_id', UUID(as_uuid=True), db.<PERSON>('blog_posts.blog_id', ondelete='CASCADE'), primary_key=True),
    db.Column('category_id', UUID(as_uuid=True), db.<PERSON>('blogcategories.category_id', ondelete='CASCADE'), primary_key=True)
)

blog_post_tags = db.Table(
    'blog_post_tags',
    db.Column('blog_id', UUID(as_uuid=True), db.<PERSON><PERSON>('blog_posts.blog_id', ondelete='CASCADE'), primary_key=True),
    db.<PERSON>umn('tag_id', UUID(as_uuid=True), db.<PERSON>('blogtags.tag_id', ondelete='CASCADE'), primary_key=True)
)


class BlogPosts(db.Model):
    __tablename__ = 'blog_posts'

    blog_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    date_posted = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp(), index=True)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.user_id', ondelete='SET NULL'), nullable=True)
    image_path = db.Column(db.String(255), nullable=True, default='default.jpg')
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp())

    # Many-to-Many Relationships
    tags = db.relationship('BlogTags', secondary=blog_post_tags, backref=db.backref('posts', lazy='dynamic'))
    categories = db.relationship('BlogCategories', secondary=blog_post_categories, backref=db.backref('posts', lazy='dynamic'))
    user = db.relationship('User', backref=db.backref('blog_posts'))
   

    def __repr__(self):
        return f"BlogPosts('{self.title}', '{self.date_posted}')"
    
    def to_dict(self):
        return {
            'blog_id': self.blog_id,
            'title': self.title,
            'content': self.content,
            'date_posted': self.date_posted,
            'user_id': self.user_id,
            'image_path': self.image_path,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'categories': [category.to_dict() for category in self.categories],
            'tags': [tag.to_dict() for tag in self.tags]
        }
    

    @classmethod
    def get_s3_client(cls):
        """
        Returns an S3 client configured for DigitalOcean Spaces.
        """
        return boto3.client(
            's3',
            region_name=os.getenv("DO_SPACE_REGION"),
            endpoint_url=os.getenv("DO_SPACE_ENDPOINT"),
            aws_access_key_id=os.getenv("DO_SPACE_KEY"),
            aws_secret_access_key=os.getenv("DO_SPACE_SECRET")
        )
    
    @classmethod
    def upload_document(cls, file):
        """
        Uploads a document to DigitalOcean Spaces and saves the DB record.
        """
        s3 = cls.get_s3_client()
        filename = f"{file.filename}"
        s3_path = f"Blogs/{filename}"      
        try:
            s3.upload_fileobj(
            Fileobj=file,
            Bucket=os.getenv("DO_SPACE_BUCKET"),
            Key=s3_path,
            ExtraArgs={'ACL': 'public-read'}
            )
        except Exception as e:
            current_app.logger.error(f"Error uploading file to S3: {e}")
            raise e

        file_url = f"{os.getenv('DO_SPACE_ENDPOINT')}/{os.getenv('DO_SPACE_BUCKET')}/{s3_path}"

        return file_url
    
    
    @classmethod
    def create_blog(cls, title, content, image_path=None, category_ids=None, tag_ids=None, user_id=None):
        # Create the blog post
        blog = cls(
            title=title, 
            content=content, 
            user_id=user_id, 
            image_path=image_path
        )
        
        try:
            # Add the blog to the session (but don't commit yet)
            db.session.add(blog)

            # Associate categories if provided
            if category_ids:
                for category_id in category_ids:
                    category = BlogCategories.query.get(category_id)  # Fetch category by ID
                    if category:
                        blog.categories.append(category)  # Associate the category one by one

            # Associate tags if provided
            if tag_ids:
                for tag_id in tag_ids:
                    tag = BlogTags.query.get(tag_id)  # Fetch tag by ID
                    if tag:
                        blog.tags.append(tag)  # Associate the tag one by one
            
            # Commit the session after all associations
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            raise e
        
        return blog

    
    @classmethod
    def get_blogs(cls):
        """Get blogs."""
        # Get the blogs from the db
        blogs = cls.query.all()
        return blogs

    @classmethod
    def get_blog_by_id(cls, blog_id):
        """Get a blog by id."""
        blog = cls.query.filter_by(blog_id=blog_id).first()
        return blog

class BlogCategories(db.Model):
    __tablename__ = 'blogcategories'

    category_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    category_name = db.Column(db.String(100), nullable=False, unique=True)
    category_description = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp())

    def __repr__(self):
        return f"BlogCategories('{self.category_name}', '{self.category_description}')"
    
    def to_dict(self):
        return {
            'category_id': self.category_id,
            'category_name': self.category_name,
            'category_description': self.category_description,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def add_category(cls, category_name, category_description):
        category = cls(
            category_name=category_name, 
            category_description=category_description)
        try:
            db.session.add(category)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
        return category
    
    @classmethod
    def get_blog_categories(cls):
        """Get blog categories."""
        # Get the categories from the db
        blog_categories = cls.query.all()
        return blog_categories
    
    @classmethod
    def get_category_by_name(cls, category_name):
        """Get a category by name."""
        category = cls.query.filter_by(category_name=category_name).first()
        return category
    
    @classmethod
    def update_category(cls, category_id, category_name, category_description):
        """Update a blog category."""
        category = cls.query.filter_by(category_id=category_id).first()
        if not category:
            raise ValueError('Category not found')
        category.category_name = category_name
        category.category_description = category_description
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
        return category
    
    @classmethod
    def get_category_by_id(cls, category_id):
        """Get a category by id."""
        category = cls.query.filter_by(category_id=category_id).first()
        return category
    
class BlogTags(db.Model):
    __tablename__ = 'blogtags'
    
    tag_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    tag_name = db.Column(db.String(100), nullable=False, unique=True)
    tag_description = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    
    def __repr__(self):
        return f"BlogTags('{self.tag_name}', '{self.tag_description}')"
    
    def to_dict(self):
        return {
            'tag_id': self.tag_id,
            'tag_name': self.tag_name,
            'tag_description': self.tag_description,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def add_tag(cls, tag_name, tag_description):
        tag = cls(
            tag_name=tag_name, 
            tag_description=tag_description)
        try:
            db.session.add(tag)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
        return tag
    
    @classmethod
    def get_blog_tags(cls):
        """Get blog tags."""
        # Get the tags from the db
        blog_tags = cls.query.all()
        return blog_tags
    
    @classmethod
    def get_tag_by_name(cls, name):
        tag = cls.query.filter_by(tag_name=name).first()
        return tag
    
    @classmethod
    def update_tag(cls, tag_id, tag_name, tag_description):
        """Update a blog tag."""
        tag = cls.query.filter_by(tag_id=tag_id).first()
        if not tag:
            raise ValueError('Tag not found')
        tag.tag_name = tag_name
        tag.tag_description = tag_description
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
        return tag
    
    @classmethod
    def get_tag_by_id(cls, tag_id):
        """Get a tag by id."""
        tag = cls.query.filter_by(tag_id=tag_id).first()
        return tag


