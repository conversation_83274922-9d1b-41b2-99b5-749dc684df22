import uuid
from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, Float, Integer, Text, Enum, Numeric, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.central import NsfContributions
from datetime import datetime, timedelta
import pytz # For time zone handling
from flask import current_app, session
from flask_jwt_extended import current_user
from sqlalchemy import and_
from app.models.central import User as CentralUser
from app.models.company_base import DynamicBase
from decimal import Decimal

class SalaryAdvanceRequest(DynamicBase):
    __tablename__ = "salary_advance_requests"

    request_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey('employees.employee_id'), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)  
    installments = Column(Integer, nullable=True)  
    due_date = Column(DateTime, nullable=True)  
    balance = Column(Numeric(10, 2), nullable=True)  
    reason = Column(Text, nullable=True)
    status = Column(Enum('pending', 'approved', 'rejected', name='request_status_enum'), default='pending')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    salary_approvals = relationship(
    "SalaryAdvanceApproval",
    back_populates="salary_advance_request",
    cascade="all, delete-orphan"
    )

    Employee = relationship("Employee", back_populates="salary_advance_requests", foreign_keys=[employee_id])
    repayment_logs = relationship(
        "RepaymentLog",
        back_populates="SalaryAdvanceRequest",
        cascade="all, delete-orphan"
    )

    installment_plans = relationship(
    "InstallmentPlan",
    back_populates="salary_advance_request",
    cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"""
        request_id: {self.request_id},
        employee_id: {self.employee_id},
        amount: {self.amount},
        installments: {self.installments},
        due_date: {self.due_date},
        reason: {self.reason},
        status: {self.status},
        employee_name: 
        created_at: {self.created_at},
        updated_at: {self.updated_at}
        """
    
    def to_dict(self):
        # Convert the dates to the d/m/Y format
        due_date = self.due_date.strftime('%d/%m/%Y') if self.due_date else None
        created_at = self.created_at.strftime('%d/%m/%Y') if self.created_at else None
        updated_at = self.updated_at.strftime('%d/%m/%Y') if self.updated_at else None

        # Get employee email
        employee_email = self.Employee.email if self.Employee else None
        
        # Get the full name of the employee
        full_name = f"{self.Employee.first_name} {self.Employee.last_name}" if self.Employee else None
        return {
            'request_id': self.request_id,
            'employee_id': self.employee_id,
            'amount': self.amount,
            'installments': self.installments,
            'due_date': due_date,
            'reason': self.reason,
            'status': self.status,
            'full_name': full_name,
            'created_at': created_at,
            'updated_at': updated_at,
            'employee_email': employee_email,
            'balance': self.balance,
            #'installment_amounts': [plan.planned_amount for plan in self.installment_plans],
            #'installment_dates': [plan.due_date.strftime('%d/%m/%Y') for plan in self.installment_plans],
            'installment_plans': [plan.to_dict() for plan in self.installment_plans]


        }
    
    @classmethod
    def create_salary_advance_request(cls, db_session, employee_id, amount, installments, reason):
        num_installments = len(installments)
        # Create a new salary advance request        
        new_request = cls(
            employee_id=employee_id, 
            amount=amount, 
            installments=num_installments,
            balance=amount, 
            reason=reason)
        try:
            db_session.add(new_request)
            db_session.flush()
            request_id = new_request.request_id
            # Create installment plans
            try:
                if num_installments > 0:
                    for i in range(num_installments):
                        planned_amount = installments[i]['amount']
                        due_date = installments[i]['due_date']
                        print(f"amount: {planned_amount}, due date: {due_date}")
                        result = InstallmentPlan.create_installment_plan(
                            db_session, request_id, planned_amount, due_date
                        )
                        current_app.logger.info(f"Installment plan created: {result}")
            except Exception as e:
                current_app.logger.error(f"An error occurred while creating installment plans: {str(e)}")
                return False
            return True
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"An error occurred while creating a salary advance request: {str(e)}")
            return False
        
    @classmethod
    def manually_create_salary_advance_request(cls, db_session, employee_id, amount, installments, reason, is_using_jwt=False):
        num_installments = len(installments)
        current_app.logger.info(f"Creating salary advance request for Employee ID: {employee_id}, Amount: {amount}, Installments: {num_installments}")

        # Create a new salary advance request        
        new_request = cls(
            employee_id=employee_id, 
            amount=amount, 
            installments=num_installments,
            balance=amount, 
            reason=reason,
            status="pending"  # Ensure initial status
        )

        try:
            db_session.add(new_request)
            db_session.flush()  # Flush to generate request_id
            request_id = new_request.request_id
            current_app.logger.info(f"Salary advance request created with ID: {request_id}")

            # Create installment plans
            try:
                if num_installments > 0:
                    for i, installment in enumerate(installments, start=1):
                        planned_amount = installment['amount']
                        due_date = installment['due_date']
                        current_app.logger.info(f"Creating installment {i}/{num_installments} - Amount: {planned_amount}, Due Date: {due_date}")
                        
                        result = InstallmentPlan.create_installment_plan(db_session, request_id, planned_amount, due_date)
                        current_app.logger.info(f"Installment {i} created successfully: {result}")

            except Exception as e:
                current_app.logger.error(f"Error creating installment plans: {str(e)}")
                db_session.rollback()
                return False

            # Approve the request
            try:
                # Modified to support both session and JWT Authentication
                if not is_using_jwt:
                    approver_id = session.get('user_id')
                    approver_role = session.get('role')
                else:
                    approver_id = current_user.get('user_id')
                    approver_role = current_user.get('role')
                
                if not approver_id or not approver_role:
                    current_app.logger.error("Approver details missing from session.")
                    return False

                current_app.logger.info(f"Approving request ID: {request_id} by User ID: {approver_id} with Role: {approver_role}")

                approval = SalaryAdvanceApproval(
                    request_id=request_id,
                    approver_id=approver_id,
                    approver_role=approver_role,
                    status="approved",
                    remarks="Manually approved"
                )
                db_session.add(approval)
                db_session.flush()  # Ensure approval record is saved
                current_app.logger.info(f"Approval record created: {approval}")

                # Update Salary Advance Status
                new_request.status = "approved"  # 🔥 FIX: Explicitly update status
                db_session.flush()  # Ensure the change is persisted before commit
                current_app.logger.info(f"Updated salary advance request {request_id} status to 'approved'.")

                # Update Employee's Salary Advance Balance
                try:
                    balance = new_request.Employee.salary_advance_balance or Decimal(0)
                    amount_requested = Decimal(amount)
                    new_request.Employee.salary_advance_balance = balance + amount_requested
                    db_session.flush()
                    current_app.logger.info(f"Updated employee salary advance balance to: {new_request.Employee.salary_advance_balance}")

                except Exception as e:
                    current_app.logger.error(f"Error updating salary advance balance: {str(e)}")
                    db_session.rollback()
                    return False

                # Commit all changes
                db_session.commit()
                current_app.logger.info(f"Salary advance request ID {request_id} approved and committed successfully.")

                return True

            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"Error approving salary advance request: {str(e)}")
                return False

        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Error creating salary advance request: {str(e)}")
            return False


    @classmethod
    def get_approved_salary_advance_requests_for_employee(cls, db_session, employee_id):
        """Get all approved salary advance requests for a specific employee."""
        requests = db_session.query(cls).filter(and_(cls.employee_id == employee_id, cls.status == 'approved')).all()
        try:
            converted = [request.to_dict() for request in requests]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting salary advance requests to dictionary: {str(e)}")
            if requests:
                return requests
            return []
        
    @classmethod
    def get_salary_advance_request_for_employee(cls, db_session, employee_id):
        # Get all salary advance requests for a specific employee where the status is 'pending'
        requests = db_session.query(cls).filter(and_(cls.employee_id == employee_id, cls.status == 'pending')).all()
        # Convert the requests to a dictionary
        try:
            converted = [request.to_dict() for request in requests]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting salary advance requests to dictionary: {str(e)}")
            if requests:
                return requests
            return []
        
    @classmethod
    def get_salary_advance_requests(cls, db_session):
        # Get all salary advance requests that are pending
        requests = db_session.query(cls).filter(cls.status == 'pending').all()
        # Convert the requests to a dictionary
        try:
            converted = [request.to_dict() for request in requests]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting salary advance requests to dictionary: {str(e)}")
            if requests:
                return requests
            return []
        
    @classmethod
    def get_salary_advance_request_by_id(cls, db_session, request_id):
        # Get a specific salary advance request
        request = db_session.query(cls).filter(cls.request_id == request_id).first()
        if request:
            return request.to_dict()
        return []
    
    @classmethod
    def update_salary_advance_request(cls, db_session, request_id, amount, installments):
        # Fetch and update the salary advance request
        request = db_session.query(cls).filter(and_(cls.request_id == request_id, cls.status == 'pending')).first()
        if request:
            request.amount = amount
            request.updated_at = datetime.now()
            try:
                db_session.commit()

                # Delete all existing installments for this request
                db_session.query(InstallmentPlan).filter(InstallmentPlan.request_id == request_id).delete()
                db_session.commit()

                number_of_installments = len(installments)
                # Add new installments
                for installment in installments:
                    new_plan = InstallmentPlan(
                        request_id=request_id,
                        planned_amount=installment['amount'],
                        is_paid=False,
                        due_date=installment['due_date']
                    )
                    db_session.add(new_plan)
                db_session.commit()

                current_app.logger.info(f"Salary advance request and installments updated successfully.")
                return True
            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"An error occurred while updating a salary advance request: {str(e)}")
                return False
        return False

    
    @classmethod
    def delete_salary_advance_request(cls, db_session, request_id):
        # Delete a salary advance request that is pending
        request = db_session.query(cls).filter(and_(cls.request_id == request_id, cls.status == 'pending')).first()
        if request:
            db_session.delete(request)
            try:
                db_session.commit()
                return True
            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"An error occurred while deleting a salary advance request: {str(e)}")
                return False
        return False
    
class RepaymentLog(DynamicBase):
    __tablename__ = "repayment_logs"

    repayment_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    request_id = Column(UUID(as_uuid=True), ForeignKey('salary_advance_requests.request_id'), nullable=False)
    amount_paid = Column(Numeric(10, 2), nullable=False)  # Repayment amount
    payment_date = Column(DateTime, default=datetime.now)
    balance_after_payment = Column(Numeric(10, 2), nullable=False)  # Balance after this payment
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    SalaryAdvanceRequest = relationship("SalaryAdvanceRequest", back_populates="repayment_logs", foreign_keys=[request_id])

    def __to_dict__(self):
        """Convert the object to a dictionary."""
        payment_date = self.payment_date.strftime('%d/%m/%Y') if self.payment_date else None
        created_at = self.created_at.strftime('%d/%m/%Y') if self.created_at else None
        updated_at = self.updated_at.strftime('%d/%m/%Y') if self.updated_at else None
        return {
            'repayment_id': self.repayment_id,
            'request_id': self.request_id,
            'amount_paid': self.amount_paid,
            'payment_date': payment_date,
            'balance_after_payment': self.balance_after_payment,
            'created_at': created_at,
            'updated_at': updated_at
        }
    
class InstallmentPlan(DynamicBase):
    __tablename__ = "installment_plans"

    installment_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    request_id = Column(UUID(as_uuid=True), ForeignKey('salary_advance_requests.request_id'), nullable=False) 
    planned_amount = Column(Numeric(10, 2), nullable=False)
    due_date = Column(DateTime, nullable=True)  
    is_paid = Column(Boolean, default=False)  
    paid_at = Column(DateTime, nullable=True)  

    salary_advance_request = relationship("SalaryAdvanceRequest", back_populates="installment_plans")

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        installment_id: {self.installment_id},
        request_id: {self.request_id},
        planned_amount: {self.planned_amount},
        is_paid: {self.is_paid},
        paid_at: {self.paid_at},
        due_date: {self.due_date}
        """
    
    def to_dict(self):
        """Convert the object to a dictionary."""
        paid_at = self.paid_at.strftime('%d/%m/%Y') if self.paid_at else None
        #due_date = self.due_date.strftime('%d/%m/%Y') if self.due_date else None
        # get the request status
        status = self.salary_advance_request.status
        return {
            'installment_id': self.installment_id,
            'request_id': self.request_id,
            'planned_amount': self.planned_amount,
            'is_paid': self.is_paid,
            'paid_at': paid_at,
            'due_date': self.due_date,
            'status': status
        }
    
    @classmethod
    def create_installment_plan(
        cls, db_session, request_id,
        planned_amount, due_date):
        """Create a new installment plan."""
        new_plan = cls(
            request_id=request_id,
            due_date=due_date,
            planned_amount=planned_amount
        )
        try:
            db_session.add(new_plan)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"An error occurred while creating an installment plan: {str(e)}")
            return False
        
    @classmethod
    def get_installment_plans_for_request(cls, db_session, request_id):
        """Get all installment plans for a specific request that have been approved."""
        plans = db_session.query(cls).filter(cls.request_id == request_id).all()
        try:
            converted = [plan.to_dict() for plan in plans]
            # Make sure we only return installment plans for requests that have been approved
            sorted = [plan for plan in converted if plan['status'] == 'approved']
            return sorted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting installment plans to dictionary: {str(e)}")
            return []
        
    @classmethod
    def get_installment_plans(cls, db_session):
        """Get all installment plans."""
        plans = db_session.query(cls).all()
        try:
            converted = [plan.to_dict() for plan in plans]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting installment plans to dictionary: {str(e)}")
            if plans:
                return plans
            return []
        
    @classmethod
    def get_installment_plan_for_employee(cls, db_session, employee_id):
        """Get all installment plans for a specific employee."""
        # Get all salary advance requests for the employee
        requests = db_session.query(SalaryAdvanceRequest).filter(SalaryAdvanceRequest.employee_id == employee_id).all()
        # Get all installment plans for the requests
        plans = []
        for request in requests:
            plans.extend(cls.get_installment_plans_for_request(db_session, request.request_id))
        return plans
    
    @classmethod
    def update_installment_plan(cls, db_session, installment_id, planned_amount, is_paid=False):
        """Update an installment plan."""
        plan = db_session.query(cls).filter(cls.installment_id == installment_id).first()
        if plan:
            plan.planned_amount = planned_amount
            plan.is_paid = is_paid
            plan.updated_at = datetime.now()
            try:
                db_session.commit()
                return True
            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"An error occurred while updating an installment plan: {str(e)}")
                return False
            
        return False
    
    @classmethod
    def update_installment_plan_payment(cls, db_session, installment_id, paid_at=datetime.now(), is_paid=True):
        """Update an installment plan."""
        plan = db_session.query(cls).filter(cls.installment_id == installment_id).first()
        if plan:
            plan.is_paid = is_paid
            plan.paid_at = paid_at
            plan.updated_at = datetime.now()

            # Update the balance of the employee advance
            request_id = plan.request_id
            request = db_session.query(SalaryAdvanceRequest).filter(SalaryAdvanceRequest.request_id == request_id).first()
            if request:
                balance = request.Employee.salary_advance_balance
                print(f"Balance before payment: {balance}")
                if not balance:
                    balance = 0
                amount_paid = plan.planned_amount
                print(f"type of balance: {type(balance)} and type of amount_paid: {type(amount_paid)}")
                # Convert balance if necessary
                if isinstance(balance, float):
                    balance = Decimal(balance)
                    print(f"type of balance: {type(balance)} and type of amount_paid: {type(amount_paid)} after conversion")
                balance -= amount_paid
                print(f"Balance after payment: {balance}")
                request.Employee.salary_advance_balance = balance
            try:
                db_session.commit()
                return True
            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"An error occurred while updating an installment plan: {str(e)}")
                return False
            
        return False
    
class SalaryAdvanceApproval(DynamicBase):
    __tablename__ = "salary_advance_approvals"

    approval_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    request_id = Column(UUID(as_uuid=True), ForeignKey('salary_advance_requests.request_id'), nullable=False)
    approver_id = Column(UUID(as_uuid=True), nullable=False)  # Likely references an Employee ID or a User ID
    approver_role = Column(String(128), nullable=False)  # The role of the approver (e.g., HR Manager, Finance Head)
    status = Column(String(128), nullable=True)
    remarks = Column(Text, nullable=True)  # Optional comments from the approver
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    salary_advance_request = relationship(
        "SalaryAdvanceRequest",
        back_populates="salary_approvals",
        foreign_keys=[request_id]
    )

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        approval_id: {self.approval_id},
        request_id: {self.request_id},
        approver_id: {self.approver_id},
        approver_role: {self.approver_role},
        status: {self.status},
        remarks: {self.remarks},
        created_at: {self.created_at},
        updated_at: {self.updated_at}
        """
    
    def to_dict(self):
        """Convert the object to a dictionary."""
        created_at = self.created_at.strftime('%d/%m/%Y') if self.created_at else None
        updated_at = self.updated_at.strftime('%d/%m/%Y') if self.updated_at else None
        return {
            'approval_id': self.approval_id,
            'request_id': self.request_id,
            'approver_id': self.approver_id,
            'approver_role': self.approver_role,
            'status': self.status,
            'remarks': self.remarks,
            'created_at': created_at,
            'updated_at': updated_at,
            'employee_id': self.salary_advance_request.employee_id,
            'amount': self.salary_advance_request.amount,
            'employee_name': f"{self.salary_advance_request.Employee.first_name} {self.salary_advance_request.Employee.last_name}"
        }
    
    @classmethod
    def create_approval(cls, db_session, request_id, approver_id, approver_role, status, remarks):
        """Create a new approval."""
        from app.models.company_approval_work_flow import ApprovalWorkflow
        new_approval = cls(
            request_id=request_id,
            approver_id=approver_id,
            approver_role=approver_role,
            status=status,
            remarks=remarks
        )
        approval_type = 'salary advance'

        # Fetch Salary Advance Request
        salary_advance_request = db_session.query(SalaryAdvanceRequest).filter(SalaryAdvanceRequest.request_id == request_id).first()
        if not salary_advance_request:
            current_app.logger.error(f"No salary advance request found with ID: {request_id}")
            message = f"Salary advance request not found with ID: {request_id}"
            return message
        
        # if the requests has been either approved or rejected, no further action is allowed
        if salary_advance_request.status != 'pending':
            current_app.logger.error(f"The salary advance request has already been {salary_advance_request.status}")
            message = f"The salary advance request has already been {salary_advance_request.status}"
            return message
        
        # Get the sequence for the approver's role
        workflow = ApprovalWorkflow.get_workflow_by_role_and_approval_type(
            db_session, approver_role, approval_type
        )
        current_app.logger.info(f"Retrieved workflow: {workflow}")
        if not workflow:
            return f"No approval sequence defined for the role: {approver_role}"
        
        sequence_order = workflow['sequence_order']
        current_app.logger.info(f"Retrieved sequence order: {sequence_order}")

        # Check if this role has already approved the leave
        existing_approval = db_session.query(SalaryAdvanceApproval).filter_by(
            request_id=request_id, approver_role=approver_role
        ).first()
        if existing_approval:
            return f"The Salary advance application has already been approved by {approver_role}."
        
        # Get the highest sequence order already approved
        approved_sequences = [
            ApprovalWorkflow.get_workflow_by_role_and_approval_type(
                db_session, approval.approver_role, approval_type
            )['sequence_order']
            for approval in salary_advance_request.salary_approvals
        ]
        highest_approved_sequence = max(approved_sequences, default=0)

        # Check if the current approver is authorized
        if sequence_order != highest_approved_sequence + 1:
            return "You are not authorized to approve this leave at this stage."
        try:
            db_session.add(new_approval)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"An error occurred while creating an approval: {str(e)}")
        
        # Check if this is the final approval
        last_sequence_order = ApprovalWorkflow.get_the_last_sequence_order(db_session, approval_type)
        if sequence_order == last_sequence_order:
            # If this is the final approval, update the status of the leave request
            salary_advance_request.status = status
            # Update the employee's salary advance balance since the salary advance table refrences the employees table
            try:
                balance = salary_advance_request.Employee.salary_advance_balance
                if not balance:
                    balance = 0
                amount_requested = salary_advance_request.amount
                current_app.logger.info(f"type of balance: {type(balance)}")
                current_app.logger.info(f"type of amount_requested: {type(amount_requested)}")
                # convert the balance to float
                balance = float(balance)
                amount_requested = float(amount_requested)
                if status == 'approved':
                    balance += amount_requested
                    salary_advance_request.Employee.salary_advance_balance = balance
            except Exception as e:
                
                current_app.logger.error(f"An error occurred while updating the salary advance balance: {str(e)}")
            db_session.commit()
            message = f"The salary advance request has been {status}"
            return message
        
        else:
            current_app.logger.info(f"Salary advance application {request_id} {status} by {approver_role}.")
            return f"Salary advance application {status} successfully by {approver_role}."
        
    @classmethod
    def get_approvals_for_request(cls, db_session, request_id):
        """Get all approvals for a specific request."""
        approvals = db_session.query(cls).filter(cls.request_id == request_id).all()
        try:
            converted = [approval.to_dict() for approval in approvals]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting approvals to dictionary: {str(e)}")
            if approvals:
                return approvals
            return []
        
    @classmethod
    def get_approvals_for_employee(cls, db_session, employee_id):
        """Get all approvals for a specific employee."""
        # Get all salary advance requests for the employee
        requests = db_session.query(SalaryAdvanceRequest).filter(SalaryAdvanceRequest.employee_id == employee_id).all()
        # Get all approvals for the requests
        approvals = []
        for request in requests:
            approvals.extend(cls.get_approvals_for_request(db_session, request.request_id))
        return approvals
    
    @classmethod
    def get_approvals(cls, db_session):
        """Get all approvals."""
        approvals = db_session.query(cls).all()
        try:
            converted = [approval.to_dict() for approval in approvals]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting approvals to dictionary: {str(e)}")
            if approvals:
                return approvals
            return []
        
    @classmethod
    def get_approvals_for_approver(cls, db_session, approver_id):
        """Get all approvals for a specific approver."""
        approvals = db_session.query(cls).filter(cls.approver_id == approver_id).all()
        try:
            converted = [approval.to_dict() for approval in approvals]
            return converted
        except Exception as e:
            current_app.logger.error(f"An error occurred while converting approvals to dictionary: {str(e)}")
            if approvals:
                return approvals
            return []
