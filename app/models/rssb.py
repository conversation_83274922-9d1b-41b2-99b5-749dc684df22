from app import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import scoped_session, sessionmaker
from flask import current_app
from sqlalchemy.exc import IntegrityError

class NsfContributions(db.Model):
    """Model representing National Social Security Fund contributions.
    Description: This model represents NSF contributions in the system.
    Attributes:
        nsf_id (UUID): A unique identifier for the NSF contribution.
        contribution_name (str): The name of the contribution.
        employee_rate (float): The employee rate of the contribution.
        employer_rate (float): The employer rate of the contribution.
    """
    __tablename__ = 'nsf_contributions'
    __table_args__ = {'extend_existing': True}
       
    nsf_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    contribution_name = db.Column(db.String(255), nullable=False, unique=True)
    employee_rate = db.Column(db.Float, nullable=False)
    employer_rate = db.Column(db.Float, nullable=False)
    

    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        contribution_name: {self.contribution_name}, 
        employee_rate: {self.employee_rate}, 
        employer_rate: {self.employer_rate}
        """
    
    def to_dict(self):
        """Convert NSF contribution object to dictionary."""
        return {
            "nsf_id": self.nsf_id,
            "contribution_name": self.contribution_name,
            "employee_rate": self.employee_rate,
            "employer_rate": self.employer_rate
        }
    
    def insert_nsf_contribution(self):
        """Insert an NSF contribution into the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except IntegrityError as e:
            db.session.rollback()
            print("Error inserting NSF contribution: ", e)
            return False
        
    def get_contributions(self):
        """Get all NSF contributions from the database."""
        contributions = NsfContributions.query.all()
        converted = [contribution.to_dict() for contribution in contributions]
        return converted
        
Base = db.Model 