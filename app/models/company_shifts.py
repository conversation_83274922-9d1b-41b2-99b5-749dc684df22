from app.models.company_base import DynamicBase
import uuid
from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Integer, Time
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
import pytz # For time zone handling
from flask import current_app
from flask import flash
from sqlalchemy import and_
from sqlalchemy import Enum

class Shift(DynamicBase):
    """Shift model class."""
    __tablename__ = "shifts"

    shift_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(128), nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    #Add a column to track how many hours the auto clock out should be
    auto_clock_out_hours = Column(Float, default=1.0, nullable=True)

    def __repr__(self):
        """String representation of the object."""
        return f"""
        "shift_id": "{self.shift_id}",
        "name": "{self.name}",
        "start_time": "{self.start_time}",
        "end_time": "{self.end_time}",
        "auto_clock_out_hours": "{self.auto_clock_out_hours}",
        "created_at": "{self.created_at}",
        "updated_at": "{self.updated_at}"
        """

    def to_dict(self):
        """To dictionary method. Converts the object to a dictionary."""
        # Handle shifts that span overnight
        if self.end_time < self.start_time:
            # Add 24 hours to end_time if it is earlier than start_time
            end_time = datetime.combine(datetime.today(), self.end_time) + timedelta(days=1)
        else:
            end_time = datetime.combine(datetime.today(), self.end_time)

        start_time = datetime.combine(datetime.today(), self.start_time)

        # Calculate the shift duration in hours
        shift_duration = (end_time - start_time).seconds / 3600
        # Round to 2 decimal places
        shift_duration = round(shift_duration, 2)

        return {
            "shift_id": self.shift_id,
            "name": self.name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "auto_clock_out_hours": self.auto_clock_out_hours,
            "shift_duration": shift_duration,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def get_shifts(cls, db_session):
        """Get all shifts."""
        shifts = db_session.query(cls).all()
        return [shift.to_dict() for shift in shifts]

    @classmethod
    def get_shift_by_id(cls, db_session, shift_id):
        """Get a shift by ID."""
        shift = db_session.query(cls).filter_by(shift_id=shift_id).first()
        return shift.to_dict()

    @classmethod
    def create_shift(cls, db_session, name, start_time, end_time, auto_clock_out_hours):
        """Create a shift."""
        # get list of all shifts
        shifts = db_session.query(cls).all()
        # check if the shift already exists
        for shift in shifts:
            if shift.name == name:
                message = f"Shift with name {name} already exists."
                category = "danger"
                return message, category
            # check if the shift overlaps with an existing shift
            if start_time >= shift.start_time and start_time <= shift.end_time:
                message = f"Shift overlaps with an existing shift."
                category = "danger"
                return message, category
            if end_time >= shift.start_time and end_time <= shift.end_time:
                message = f"Shift overlaps with an existing shift."
                category = "danger"
                return message, category
        # create the shift
        new_shift = cls(
            name=name,
            start_time=start_time,
            end_time=end_time,
            auto_clock_out_hours=auto_clock_out_hours
        )
        try:
            db_session.add(new_shift)
            db_session.commit()
            message = "Shift created successfully."
            category = "success"
            return message, category
        except Exception as e:
            current_app.logger.error(f"An error occurred while creating the shift: {str(e)}")
            message = "An error occurred while creating the shift."
            return message

    @classmethod
    def update_shift(cls, db_session, shift_id, name, start_time, end_time, auto_clock_out_hours):
        """Update a shift."""
        # get list of all shifts
        shifts = db_session.query(cls).all()
        # check if the shift already exists
        for shift in shifts:
            # Skip the current shift in the checks
            if str(shift.shift_id) == str(shift_id):
                continue

            if shift.name == name:
                message = f"Shift with name {name} already exists."
                category = "danger"
                return message, category

            # check if the shift overlaps with an existing shift
            if start_time >= shift.start_time and start_time <= shift.end_time:
                message = f"Shift overlaps with an existing shift."
                category = "danger"
                return message, category

            if end_time >= shift.start_time and end_time <= shift.end_time:
                message = f"Shift overlaps with an existing shift."
                category = "danger"
                return message, category

        # update the shift
        shift = db_session.query(cls).filter_by(shift_id=shift_id).first()
        if not shift:
            message = f"Shift with ID {shift_id} not found."
            category = "danger"
            return message, category

        shift.name = name
        shift.start_time = start_time
        shift.end_time = end_time
        shift.auto_clock_out_hours = auto_clock_out_hours
        try:
            db_session.commit()
            message = "Shift updated successfully."
            category = "success"
            return message, category
        except Exception as e:
            current_app.logger.error(f"An error occurred while updating the shift: {str(e)}")
            message = "An error occurred while updating the shift."
            category = "danger"
            return message, category

    @classmethod
    def delete_shift(cls, db_session, shift_id):
        """Delete a shift."""
        shift = db_session.query(cls).filter_by(shift_id=shift_id).first()
        try:
            db_session.delete(shift)
            db_session.commit()
            message = "Shift deleted successfully."
            return message
        except Exception as e:
            current_app.logger.error(f"An error occurred while deleting the shift: {str(e)}")
            message = "An error occurred while deleting the shift."
            return message

    @classmethod
    def get_shifts_by_date(cls, db_session, date):
        """Get all shifts by date."""
        shifts = db_session.query(cls).filter(and_(cls.start_time <= date, cls.end_time >= date)).all()
        return shifts.to_dict()

    @classmethod
    def get_shifts_by_date_range(cls, db_session, start_date, end_date):
        """Get all shifts by date range."""
        shifts = db_session.query(cls).filter(and_(cls.start_time >= start_date, cls.end_time <= end_date)).all()
        return shifts.to_dict()

    @classmethod
    def get_shifts_by_name(cls, db_session, name):
        """Get all shifts by name."""
        shifts = db_session.query(cls).filter_by(name=name).all()
        return shifts.to_dict()

    @classmethod
    def get_shifts_by_start_time(cls, db_session, start_time):
        """Get all shifts by start time."""
        shifts = db_session.query(cls).filter_by(start_time=start_time).all()
        return shifts.to_dict()

    @classmethod
    def get_shifts_by_end_time(cls, db_session, end_time):
        """Get all shifts by end time."""
        shifts = db_session.query(cls).filter_by(end_time=end_time).all()
        return shifts.to_dict()

    @staticmethod
    def calculate_total_hours(cls, db_session):
        """Calculate total hours."""
        shifts = db_session.query(cls).all()
        # Calculate the shift du