import requests
import json
import logging
from dotenv import load_dotenv
import os

load_dotenv()

class Irembo:
    """Irembo API integration class"""
    
    base_url = os.getenv('PAYMENT_BASEURL')
    secret_key = os.getenv('payment_secret_key')

    @classmethod
    def get_headers(cls):
        """Generate authentication headers for requests"""
        return {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "MyAppName",
            "irembopay-secretKey": cls.secret_key,
        }

    @classmethod
    def create_invoice(cls, payload):
        """Create an invoice"""
        url = f"{cls.base_url}/invoices"

        try:
            response = requests.post(url, headers=cls.get_headers(), data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error creating invoice: {e}")
            return None

    @classmethod
    def get_invoice_details(cls, invoice_reference):
        """Get invoice details"""
        url = f"{cls.base_url}/invoices/{invoice_reference}"

        try:
            response = requests.get(url, headers=cls.get_headers())
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error retrieving invoice: {e}")
            return None

    @classmethod
    def update_invoice(cls, invoice_reference, payload):
        """Update invoice"""
        url = f"{cls.base_url}/invoices/{invoice_reference}"

        try:
            response = requests.put(url, headers=cls.get_headers(), data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error updating invoice: {e}")
            return None
    @classmethod
    def initiate_mobile_money_push(cls, phone_number, invoice_reference, payment_provider):
        """Initiate MTN mobile money or Airtel Money push."""
        url = f"{cls.base_url}/transactions/initiate"
        # Ensure phone number is valid
        if not phone_number.startswith("07") or len(phone_number) != 10:
            print("Error: Invalid phone number format.")
            return None
        print(f"mobile number validated: {phone_number}")

        try:
            payload = {
                "accountIdentifier": phone_number,
                "paymentProvider": payment_provider,
                "invoiceNumber": invoice_reference,
                "transactionReference": f"{payment_provider}_{invoice_reference}"
            }
            print(f"payload constructed: {payload}")
        except Exception as e:
            print(f"Error constructing payload: {e}")
            return None
        try:
            response = requests.post(url, headers=cls.get_headers(), json=payload)
            response_data = response
            print(f"Response: {response_data}")

            if response.status_code != 200 or response.status_code != 201:
                print(f"Error: {response.status_code} - {response_data}")

            return response_data
        except requests.exceptions.RequestException as e:
            print(f"Error initiating mobile money push: {e}")
            return None

if __name__ == "__main__":
    #try:
        #result = Irembo.create_invoice()
        #print(result)
       
    #except Exception as e:
        #print(f"Error creating invoice: {e}")
    transaction_id = "*************"
    company_email = "<EMAIL>"
    company_phone = "**********"
    company_name = "Bk Lamane"
    quantity = 1
    price = 1000
    invoice_number = "************"
    phone_number = "**********"
    payment_provider = "MTN"
    invoice_data = {
            "transactionId": transaction_id,
            "paymentAccountIdentifier": "Bk Lamane",
            "customer": {
                "email": company_email,
                "phoneNumber": company_phone,
                "name": company_name,
            },
            "paymentItems": [
                {
                    "code": "PC-baff53d9ea",
                    "quantity": quantity,
                    "unitAmount": price,
                }
            ],
            "description": "This is a test invoice",
            "expiryAt": "2025-09-30T01:00:00+02:00",
            "language": "EN"
        }

    try:
        #result = Irembo.create_invoice(invoice_data)
        result = Irembo.get_invoice_details(invoice_number)
        #result = Irembo.initiate_mobile_money_push(phone_number, invoice_number, payment_provider)
        #string_data = result['data']['description']
        # Split by ", " to get key-value pairs
        #pairs = string_data.split(", ")

        # Convert into dictionary
        #data_dict = {key.strip(): value.strip() for key, value in (pair.split(": ") for pair in pairs)}

        print(result)
    except Exception as e:
        print(f"Error getting invoice details: {e}")