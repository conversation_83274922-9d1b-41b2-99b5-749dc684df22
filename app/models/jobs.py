from app.models.company import Attendance
from flask import current_app as app, session
from app.utils.db_connection import DatabaseConnection

class CronJobs:
    """Class to encapsulate automation job triggers."""

    @classmethod
    def auto_clockout_job(database_name):
        """Job to trigger the auto clockout process."""
        
        with self.db_connection.get_session(database_name) as db_session:
            try:
                # Call the existing auto_clockout method from Attendance
                message = Attendance.auto_clockout(db_session)
                app.logger.info(f"Auto clockout job executed: {message}")
            except Exception as e:
                app.logger.error(f"Error in auto clockout job: {str(e)}")
