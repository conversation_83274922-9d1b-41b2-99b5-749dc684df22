from app import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import scoped_session, sessionmaker
from flask import current_app
from sqlalchemy.exc import IntegrityError
from flask import abort, flash
import hashlib
import secrets
from decimal import Decimal
import os
from sqlalchemy.exc import SQLAlchemyError

class LeaveTypes(db.Model):
    __tablename__ = 'leave_types'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    def __repr__(self):
        return f"""
        id: {self.id},
        name: {self.name},
        description: {self.description},
        created_at: {self.created_at},
        updated_at: {self.updated_at}
        """
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.strftime('%d/%m/%Y') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%d/%m/%Y') if self.updated_at else None
        }
    
    @staticmethod
    def add_leave_type(name, description):
        leave_type = LeaveTypes(name=name, description=description)
        db.session.add(leave_type)
        db.session.commit()
        return leave_type
    
    @staticmethod
    def get_leave_types():
        leave_types = LeaveTypes.query.all()
        converted_leave_types = [leave_type.to_dict() for leave_type in leave_types]
        return converted_leave_types
    
    @staticmethod
    def get_leave_type_by_id(id):
        leave_type = LeaveTypes.query.filter_by(id=id).first()
        if leave_type:
            return leave_type.to_dict()
        return []
    
    @staticmethod
    def update_leave_type(id, name, description):
        leave_type = LeaveTypes.query.filter_by(id=id).first()
        if leave_type:
            leave_type.name = name
            leave_type.description = description
            leave_type.updated_at = db.func.current_timestamp()
            db.session.commit()
            return leave_type.to_dict()
        return []
    
    @staticmethod
    def delete_leave_type(id):
        leave_type = LeaveTypes.query.filter_by(id=id).first()
        if leave_type:
            db.session.delete(leave_type)
            db.session.commit()
            return True
        return False