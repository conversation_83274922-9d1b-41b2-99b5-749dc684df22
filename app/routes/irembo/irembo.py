from flask import Blueprint, request, jsonify, redirect, flash, render_template
from app.models.irembo import Irembo
import uuid
from flask import current_app as app, session
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.models.central_payments import Payments
from app.models.central import Company, Plans
from app.models.company import Employee
from datetime import datetime, timedelta
from app import db
from app import current_app
import requests
import os
from dotenv import load_dotenv

load_dotenv()

irembo_bp = Blueprint('irembo', __name__)

@irembo_bp.route('/pay_invoice/<reference_number>', methods=['GET'])
def pay_invoice(reference_number):
    data = session.get('data')
    total_price = data.get("amount")
    invoice_number = data.get("invoiceNumber")
    payment_items = data.get("paymentItems", [])
    customer = data.get("customer", [])

    if customer:
        customer_email = customer.get("email")
        customer_phone = customer.get("phoneNumber")
        customer_name = customer.get("fullName")



    if payment_items:
        quantity = payment_items[0].get("quantity")
        unit_price = payment_items[0].get("unitAmount")
    else:
        quantity = None
        unit_price = None
    return render_template('irembo/pay_invoice.html', 
                           reference_number=reference_number,
                           customer_email=customer_email,
                           customer_phone=customer_phone,
                           customer_name=customer_name,
                           total_price=total_price,
                           quantity=quantity,
                           unit_price=unit_price,
                           Auxillary=Auxillary)

@irembo_bp.route('/create_invoice', methods=['POST', 'GET'])
@role_required(['hr', 'manager', 'company_hr'])
def create_invoice():
    if request.method == 'POST':
        quantity = request.form.get('quantity', type=int, default=1)
        different_plan = request.form.get('different_plan')
        
        # save different_plan in session
        session['different_plan'] = different_plan
        
        if different_plan == 'yes':
            #redirect to create_invoice1
            current_app.logger.info(f"Redirecting to create_invoice1")
            # return redirect(url_for('irembo.create_invoice1'))
            return jsonify({'success':True, 'message': 'Redirecting to create_invoice1'}), 200

        company = session.get('company')
        company_id = session.get('company_id')
        company_db = Company.get_company_by_id(company_id)
        db_connection = DatabaseConnection()
        db_name = CompanyHelpers.get_company_database_name(company_id)
        with db_connection.get_session(db_name) as db_session:
            employees=[]
            try:
                employees = [emp for emp in Employee.get_employees(db_session) \
                              if str(emp.get("company_id")).strip() == str(company_id).strip()]

            except Exception as e:
                print("THIS IS BIG ERROR: ", e)
        
        # Get price per employee from plan
        existing_plan = Plans.check_plan_existence(different_plan)
        if not existing_plan:
            flash("Invalid plan", "danger")
            return redirect(request.url)
        
        price_per_employee = existing_plan.get("price_per_employee", 0)
        app.logger.info(f"Company: {company} and type: {type(company)}")
        
        try:
            if not isinstance(company, dict):
                company = company.to_dict()
                app.logger.info(f"Company converted to dictionary: {company}")
        except Exception as e:
            app.logger.error(f"Error converting company to dictionary: {e}")
            return jsonify({"error": "Error converting company to dictionary"}), 400
        company_name = session.get('company_name', 'Company Name')
        price = session.get('plan_price', 100000)
        price = float(price) + (len(employees) * float(price_per_employee))
        app.logger.info(f"Price: {price} and type: {type(price)}")
        plan = session.get('plan')
        current_app.logger.info(f"Plan: {plan}")

        try:
            company_phone = company.get('phone_number')
            company_email = company.get('email')
            app.logger.info(f"Company phone: {company_phone} and email: {company_email}")
        except Exception as e:
            app.logger.error(f"Error getting company phone and email: {e}")
            company_phone = "**********"
            company_email = "<EMAIL>"
        user_id = session.get('user_id')
        app.logger.info(f"Company id: {company_id} and user id: {user_id}")
        transaction_id = str(uuid.uuid4().hex)
        app.logger.info(f"Transaction ID: {transaction_id} and the type: {type(transaction_id)}")
        invoice_data = {
            "transactionId": transaction_id,
            "paymentAccountIdentifier": "ACR_RWF",
            "customer": {
                "email": company_email,
                "phoneNumber": company_phone,
                "name": company_name            },
            "paymentItems": [
                {
                    "code": "PC-8b9c38d1cd",
                    "quantity": quantity,
                    "unitAmount": price,
                }
            ],
            "description": f"company_id: {str(company_id)}, user_id: {str(user_id)}, plan: {str(plan)}",
                
            "expiryAt": "2025-09-30T01:00:00+02:00",
            "language": "EN" 
        }
        current_app.logger.info(f"Data for creating invoice: {invoice_data}")

        try:
            headers = Irembo.get_headers()
            app.logger.info(f"Headers: {headers}")
            app.logger.info(f"Request body: {invoice_data}")
            url = f"{Irembo.base_url}/invoices"
            app.logger.info(f"URL used for creating a request: {url}")
            result = Irembo.create_invoice(invoice_data)
            app.logger.info(f"Result of create invoice: {result} and the type: {type(result)}")

            if result.get("success"):
                invoice_number = result["data"]["invoiceNumber"]
                data = result["data"]
                app.logger.info(f"Data from create invoice: {data}")
                # save data in session
                session['data'] = data
                current_app.logger.info(f"Redirecting to pay_invoice with invoice number: {invoice_number}")
                return jsonify({'success':True, 'message': 'Redirecting to pay_invoice', 'invoice_number': invoice_number}), 201
                #return redirect(url_for('irembo.pay_invoice', reference_number=invoice_number))
            else:
                return jsonify({"error": "Failed to create invoice"}), 400

        except Exception as e:
            app.logger.error(f"Error creating invoice: {e}")
            return jsonify({"error": "Error creating invoice"}), 500

    price = session.get('plan_price', 100000)
    current_app.logger.info(f"Price: {price}")
    try:
        plans = Plans.get_plans()
        app.logger.info(f"Plans: {plans}")
    except Exception as e:
        app.logger.error(f"Error getting plans: {e}")
        plans = []
    return render_template('irembo/create_invoice.html', price=price, plans=plans)

@irembo_bp.route('/create_invoice1', methods=['POST', 'GET'])
@role_required(['hr', 'manager', 'company_hr'])
def create_invoice1():
    if request.method == 'POST':
        company_id = session.get('company_id')
        data = request.form
        db_connection = DatabaseConnection()
        db_name = CompanyHelpers.get_company_database_name(company_id)
        with db_connection.get_session(db_name) as db_session:
            employees = []
            try:
                employees = [emp for emp in Employee.get_employees(db_session) \
                              if str(emp.get("company_id")).strip() == str(company_id).strip()]
                
            except Exception as e:
                print("THIS IS BIG ERROR: ", e)

        app.logger.info(f'form data: {data}')
        try:
            quantity = request.form.get('quantity', type=int, default=1)
            app.logger.info(f"Quantity: {quantity}")
        except Exception as e:
            app.logger.error(f"Error getting quantity: {e}")
            return jsonify({"error": "Error getting quantity"}), 400
        try:
            selected_plan = request.form.get('plan_id')
            app.logger.info(f"Selected plan: {selected_plan}")
        except Exception as e:
            app.logger.error(f"Error getting selected plan: {e}")
            return jsonify({"error": "Error getting selected plan"}), 400

        my_company = session.get('company')
        if not isinstance(my_company, dict):
            try:
                company = my_company.to_dict()
                app.logger.info(f"Company converted to dictionary: {company}")
            except Exception as e:
                app.logger.error(f"Error converting company to dictionary: {e}")
                return jsonify({"error": "Error converting company to dictionary"}), 400

        company_name = session.get('company_name', 'Company Name')

        # Fetch plans and get price based on selected plan
        try:
           my_plan = Plans.get_plan_by_id(selected_plan)
           price_per_employee =my_plan.get("price_per_employee", 0)
           price = my_plan.get('price', 100000)
           price = float(price) + (len(employees) * float(price_per_employee))
           app.logger.info(f"Price: {price}")
        except Exception as e:
            app.logger.error(f"Error getting plans: {e}")
            return jsonify({"error": "Error retrieving plans"}), 500
        # update the session with the plan price and plan_id
        session['plan_price'] = price
        session['plan_id'] = selected_plan
        company_phone = company.get('phone_number', "**********")
        company_email = company.get('email')
        user_id = session.get('user_id')
        app.logger.info(f"Company id: {company_id} and user id: {user_id}")
        # Generate a transaction ID with the company_id plus the random part with uuid of 4.
        # We also wanna include the user_id in the transaction_id
        transaction_id = f"{company.get('company_id')}-user_id-{session.get('user_id')}-{uuid.uuid4().hex[:4]}"
        app.logger.info(f"Transaction ID: {transaction_id}")

        invoice_data = {
            "transactionId": transaction_id,
            "paymentAccountIdentifier": "Bk Lamane",
            "customer": {
                "email": company_email,
                "phoneNumber": company_phone,
                "name": company_name,
            },
            "paymentItems": [
                {
                    "code": 'PC-baff53d9ea',
                    "quantity": quantity,
                    "unitAmount": price,
                }
            ],
            "description": "This is a test invoice",
            "expiryAt": "2025-09-30T01:00:00+02:00",
            "language": "EN"
        }
        current_app.logger.info(f"Data for creating invoice: {invoice_data}")
        try:
            result = Irembo.create_invoice(invoice_data)
            current_app.logger.info(f"Result of create invoice: {result} and the type: {type(result)}")
            if result.get("success"):
                # convert into serializable object
                serializable_data = result["data"]
                current_app.logger.info(f"Data from create invoice: {serializable_data}")
                for key, value in serializable_data.items():
                    if isinstance(value, set):
                        serializable_data[key] = list(value)

                session['data'] = serializable_data
                return jsonify({
                    "success": True,
                    "message": "redirected to pay invoice",
                    "invoice_number": serializable_data["invoiceNumber"]
                }), 201
            else:
                return jsonify({"error": "Failed to create invoice"}), 400
        except Exception as e:
            current_app.logger.info(f"Error creating invoice: {e}")
            return jsonify({"error": "Error creating invoice"}), 500
    # Fetch available plans
    try:
        plans = Plans.get_plans()
    except Exception as e:
        app.logger.error(f"Error getting plans: {e}")
        plans = []
    return render_template('irembo/create_invoice1.html', plans=plans)

@irembo_bp.route('/payment_confirmation', methods=['POST'])
@role_required(['hr', 'manager', 'company_hr'])
def payment_confirmation():
    """Handle payment confirmation from IremboPay."""
    user_id = session.get('user_id')
    company_id = session.get('company_id')
    data = request.json  

    app.logger.info(f"Payment confirmation data: {data}")

    message = data.get('message')

    if message == "Payment successful":
        # Get payment data
        payment_data = session.get('data')
        app.logger.info(f"Payment data from session: {payment_data}")

        amount = payment_data.get("amount")
        invoice_number = payment_data.get("invoiceNumber")
        payment_items = payment_data.get("paymentItems", [])
        payment_method = "IremboPay API"

        # Extract number of months paid for
        if payment_items:
            quantity = payment_items[0].get("quantity", 1) 
        else:
            quantity = 1

        num_days = quantity * 30  # Convert months to days

        # Save payment record
        try:
            payment = Payments.create_payment(
                amount=amount, 
                payment_method=payment_method, 
                transaction_id=invoice_number, 
                user_id=user_id, 
                company_id=company_id
            )
            app.logger.info(f"Payment saved: {payment}")
        except Exception as e:
            app.logger.error(f"Error saving payment: {e}")
            return jsonify({"error": "Error saving payment"}), 400

        # Update the company's subscription in the database
        try:
            updated = Company.update_subscription_end_period(company_id, num_days)
            app.logger.info(f"Subscription updated: {updated}")
            if updated:
                # get company from session
                company = session.get('company')
                # update the company with the plain_id
                plan_id = session.get('plan_id')
                different_plan = session.get('different_plan')
                if different_plan == 'yes':
                    try:
                        result = Company.update_company_plan(company_id, plan_id)
                        app.logger.info(f"Company updated: {result}")
                    except Exception as e:
                        app.logger.error(f"Error updating company: {e}")
                        return jsonify({"error": "Error updating company"}), 400
                return jsonify({"message": "Payment recorded and subscription updated successfully"}), 200
            else:
                return jsonify({"error": "Error updating subscription"}), 400
        except Exception as e:
            app.logger.error(f"Error updating subscription: {e}")
            return jsonify({"error": "Error updating subscription"}), 400
        except Exception as e:
            app.logger.error(f"Error updating subscription: {e}")
            return jsonify({"error": "Error updating subscription"}), 400
    else:
        return jsonify({"error": "Payment failed"}), 400  

import hmac
import hashlib
import time
import os

def verify_signature(secret_key, payload, signature_header):
    """Verify the signature from IremboPay."""
    try:
        # Step 1: Extract timestamp and signature
        elements = signature_header.split(',')
        timestamp = None
        received_signature = None

        for element in elements:
            key, value = element.strip().split('=')
            if key == 't':
                timestamp = value.strip()
            elif key == 's':
                received_signature = value.strip()

        if not timestamp or not received_signature:
            return False, "Invalid signature format"

        # Step 2: Construct the signed payload
        signed_payload = f"{timestamp}#{payload.strip()}"

        # Step 3: Compute the expected signature
        computed_signature = hmac.new(
            key=secret_key.encode(),
            msg=signed_payload.encode(),
            digestmod=hashlib.sha256
        ).hexdigest()

        # Step 4: Compare signatures
        if not hmac.compare_digest(computed_signature, received_signature):
            return False, "Signature mismatch"

        # Step 5: Validate timestamp to prevent replay attacks (5-minute window)
        current_time = int(time.time() * 1000)  # Current time in milliseconds
        if abs(int(timestamp) - current_time) > 300 * 1000:
            return False, "Request expired"

        return True, "Valid signature"
    
    except Exception as e:
        return False, f"Error in signature verification: {str(e)}"


@irembo_bp.route('/callback/irembo_payment', methods=['POST'], strict_slashes=False)
def irembo_payment_callback():
    """Handle asynchronous payment confirmation from IremboPay with signature verification."""
    try:
        # Step 1: Extract signature header
        signature_header = request.headers.get("irembopay-signature")
        current_app.logger.info(f"Signature header: {signature_header}")
        if not signature_header:
            return jsonify({"error": "Missing signature header"}), 400

        # Step 2: Extract request body
        request_body = request.get_data(as_text=True)  # Raw request body
        current_app.logger.info(f"Received Payload from callback url: {request_body}")

        # Step 3: Retrieve secret key
        secret_key = os.getenv('payment_secret_key')
        current_app.logger.info(f"Secret key: {secret_key}")
        if not secret_key:
            current_app.logger.error("Missing payment_secret_key in environment variables")
            return jsonify({"error": "Server misconfiguration"}), 500

        # Step 4: Verify signature
        is_valid, message = verify_signature(secret_key, request_body, signature_header)
        current_app.logger.info(f"Signature verification status: is valid: {is_valid}, message: {message}")
        if not is_valid:
            current_app.logger.error(f"Signature verification failed: {message}")
            return jsonify({"error": message}), 400
        current_app.logger.info("Signature verification successful")
        # Step 5: Process payment if signature is valid
        data = request.get_json()
        current_app.logger.info(f"Payment data: {data}")
        if not data:
            current_app.logger.error("Invalid JSON payload")
            return jsonify({"error": "Invalid JSON payload"}), 400

        success = data.get("success")
        payment_data = data.get("data", {})
        payment_status = payment_data.get("paymentStatus")
        current_app.logger.info(f"Payment data: {payment_data}")
        current_app.logger.info(f"Payment status: {success}, Payment status: {payment_status}")
        paymentItems = payment_data.get("paymentItems", [])
        current_app.logger.info(f"Payment items: {paymentItems}")

        if not success or payment_status != "PAID":
            current_app.logger.error("Payment not successful")
            return jsonify({"error": "Data received  but payment not successful"}), 201

        # Post the payment data to https://education.accountants.co.rw/internal_payment_callback
        # check if the code in paymentItems is PC-c334d7fa14
        if paymentItems:
            code = paymentItems[0].get("code")
            current_app.logger.info(f"Code: {code}")
            if code == "PC-c334d7fa14":
                current_app.logger.info("Payment for subscription")
                # Post the payment data to https://education.accountants.co.rw/internal_payment_callback
                url = "https://education.accountants.co.rw/internal_payment_callback"
                # get the learnpipe_api_key from the environment variables
                headers = {"X-API-Key": os.getenv("learnpipe_api_key")}
                current_app.logger.info(f"Headers: {headers}")
                try:
                    response = requests.post(url, headers=headers, json=payment_data)
                    current_app.logger.info(f"Response: {response.json()}")
                except Exception as e:
                    current_app.logger.error(f"Error posting payment data: {str(e)}")

        current_app.logger.info(f"Payment successfully processed: {payment_data}")

        return jsonify({"message": "Payment recorded and subscription updated"}), 200

    except Exception as e:
        current_app.logger.error(f"Internal Server Error: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500
    
@irembo_bp.route('/get_invoice_details/<invoice_reference>', methods=['GET'])
def get_invoice_details(invoice_reference):
    """Get invoice details."""
    try:
        result = Irembo.get_invoice_details(invoice_reference)
        app.logger.info(f"Result of get invoice details: {result}")
        if result.get("success"):
            return jsonify(result), 200
        else:
            return jsonify({"error": "Failed to retrieve invoice"}), 400
    except Exception as e:
        app.logger.error(f"Error retrieving invoice: {e}")
        return jsonify({"error": "Error retrieving invoice"}), 500