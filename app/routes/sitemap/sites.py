from flask import Flask, Response, Blueprint, render_template, current_app,flash, redirect, session, url_for, send_from_directory
from datetime import datetime
from app.models.central_blogs import BlogCategories, BlogPosts, BlogTags

sites_bp = Blueprint('sites', __name__)

@sites_bp.route('/sitemap.xml', methods=['GET'])
def sitemap():
    # Example static pages
    pages = [
        {'loc': 'http://netpipo.com/home', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 1.0},
        {'loc': 'http://netpipo.com/about', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.8},
        {'loc': 'http://netpipo.com/features', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/privacy', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/terms', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/contact', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/register_user', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/login_user', 'lastmod': datetime(2024, 9, 23), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/docs', 'lastmod': datetime(2024, 12, 3), 'changefreq': 'daily', 'priority': 0.7},
        {'loc': 'http://netpipo.com/pricing', 'lastmod': datetime(2024, 12, 3), 'changefreq': 'daily', 'priority': 0.7},
    ]

    # Example dynamic blog posts

    def get_blog_posts():
        blog_posts = BlogPosts.query.all()
        return [post.to_dict() for post in blog_posts]

    blogs = get_blog_posts()

    for post in blogs:
        pages.append({
            'loc': f'http://netpipo.com/blog_details/{post["blog_id"]}',
            'lastmod': datetime(2025, 3, 12),
            'changefreq': 'daily',
            'priority': 0.7
        })

    # Generate an XML stiemap
    xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml_content = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'

    for page in pages:
        xml_content += f'<url>\n'
        xml_content += f'<loc>{page["loc"]}</loc>\n'
        xml_content += f'<lastmod>{page["lastmod"].strftime("%Y-%m-%d")}</lastmod>\n'
        xml_content += f'<changefreq>{page["changefreq"]}</changefreq>\n'
        xml_content += f'<priority>{page["priority"]}</priority>\n'
        xml_content += f'</url>\n'
    
    xml_content += '</urlset>'
    return Response(xml_content, mimetype='text/xml') # Return the XML content with a 'text/xml' mimetype

@sites_bp.route('/google80a0ca52db6dab87.html')
def google_verification():
    return send_from_directory(current_app.static_folder, 'google80a0ca52db6dab87.html')

"""Bing verification"""
@sites_bp.route('/********************************.txt')
def bing_verification():
    return send_from_directory(current_app.static_folder, '********************************.txt')