from flask import Blueprint, render_template, session, url_for, redirect, flash
from app.models.central import EmployeeType
from app.routes.employee_types.forms import EmployeeTypeForm
import logging
from app.decorators.admin_decorator import admin_required

employee_types_bp = Blueprint('employee_types', __name__)

@employee_types_bp.route('/add_employee_types', methods=['GET', 'POST'])
@admin_required
def add_employee_types():
    form = EmployeeTypeForm()
    if form.validate_on_submit():
        employee_type_name = form.employee_type_name.data.lower()
        print("Employee Type Name: ", employee_type_name)

        # Check if the employee type already exists
        employee_type_exists = EmployeeType.query.filter_by(employee_type_name=employee_type_name).first()
        if employee_type_exists:
            flash('Employee type already exists', 'danger')
            return redirect(url_for('employee_types.add_employee_types'))
        
        try:
            # Create a new employee type
            employee_type = EmployeeType(employee_type_name=employee_type_name)
            employee_type.insert_employee_type()
            message = f"Employee type: {employee_type_name} added successfully"
            flash(message)
            logging.info(message)
            return redirect(url_for('employee_types.add_employee_types'))
        except Exception as e:
            message = f"An error occurred: {str(e)}"
            flash(message, 'danger')
            logging.error(message)
            return redirect(url_for('employee_types.add_employee_types'))
    try:
        employee_types = EmployeeType.get_employee_types()
    except Exception as e:
        message = f"An error occurred: {str(e)}"
        flash(message, 'danger')
        logging.error(message)
        employee_types = []
    return render_template('employee_types/add_employee_types.html', employee_types=employee_types, form=form)

@employee_types_bp.route('/update_employee_type/<uuid:employee_type_id>', methods=['GET', 'POST'])
@admin_required
def update_employee_type(employee_type_id):
    employee_type = EmployeeType.query.get_or_404(employee_type_id)
    form = EmployeeTypeForm(obj=employee_type)    
    if form.validate_on_submit():
        try:
            employee_type_name = form.employee_type_name.data.lower()
            employee_type.employee_type_name = employee_type_name
        
            if employee_type.update_employee_type(employee_type_id, employee_type_name):
                message = f"Employee type: {employee_type_name} updated successfully"
                flash(message)
                logging.info(message)
                return redirect(url_for('employee_types.add_employee_types'))
            else:
                message = "Error updating employee type."
                flash(message, 'danger')
                logging.error(message)
                return redirect(url_for('employee_types.add_employee_types'))
        except Exception as e:
            message = f"An error occurred: {str(e)}"
            flash(message, 'danger')
            logging.error(message)
            return redirect(url_for('employee_types.add_employee_types'))
    return render_template('employee_types/update_employee_type.html', form=form)

@employee_types_bp.route('/delete_employee_type/<uuid:employee_type_id>', methods=['GET', 'POST'])
@admin_required
def delete_employee_type(employee_type_id):
    employee_type = EmployeeType.query.get_or_404(employee_type_id)
    try:
        if employee_type.delete_employee_type(employee_type_id):
            message = f"Employee type: {employee_type.employee_type_name} deleted successfully"
            flash(message)
            logging.info(message)
            return redirect(url_for('employee_types.add_employee_types'))
        else:
            message = "Error deleting employee type."
            flash(message, 'danger')
            logging.error(message)
            return redirect(url_for('employee_types.add_employee_types'))
    except Exception as e:
        message = f"An error occurred: {str(e)}"
        flash(message, 'danger')
        logging.error(message)
        return redirect(url_for('employee_types.add_employee_types'))