from flask import Blueprint, render_template, redirect, url_for, flash, request, session, jsonify, current_app
from app import db
from .forms import (
    AgencyRegistrationForm, AgencyUpdateForm, AgencyLogoForm,
    AgencyUserForm, AgencyCompanyForm, AgencyCommissionForm
)
from app.models.central import Agency, User, Company, AgencyCommission
from app.decorators.role_decorator import role_required
from app.helpers.auxillary import Auxillary
import uuid
import os

agency_bp = Blueprint('agency', __name__)


@agency_bp.route('/register_agency', methods=['GET', 'POST'])
@role_required(['admin'])
def register_agency():
    """Register a new agency."""
    form = AgencyRegistrationForm()
    current_app.logger.info(f"Form data: {form.data}")
    current_app.logger.info('before validating')
    if form.validate_on_submit():
        agency_name = form.agency_name.data.strip()
        agency_email = form.agency_email.data.strip()
        agency_phone = form.agency_phone.data.strip()
        agency_address = form.agency_address.data.strip() if form.agency_address.data else None
        commission_rate = form.commission_rate.data

        # Check if agency with the same email already exists
        try:
            agencies = Agency.get_agencies()
            for agency in agencies:
                if agency['agency_email'] == agency_email:
                    flash('Agency with this email already exists', 'danger')
                    return redirect(url_for('agency.register_agency'))
        except Exception as e:
            current_app.logger.error(f"Error checking agency existence: {str(e)}")
            flash('Error checking agency existence', 'danger')
            return redirect(url_for('agency.register_agency'))

        # Register the agency
        try:
            agency = Agency.register_agency(
                agency_name=agency_name,
                agency_email=agency_email,
                agency_phone=agency_phone,
                agency_address=agency_address,
                commission_rate=commission_rate
            )

            if agency:
                flash('Agency registered successfully', 'success')
                return redirect(url_for('agency.view_agencies'))
            else:
                flash('Error registering agency', 'danger')
                return redirect(url_for('agency.register_agency'))
        except Exception as e:
            current_app.logger.error(f"Error registering agency: {str(e)}")
            flash('Error registering agency', 'danger')
            return redirect(url_for('agency.register_agency'))
    try:
        current_app.logger.info('Rendering register_agency template')
        current_app.logger.info(f"Template path: app/templates/agency/register_agency.html")
        current_app.logger.info(f"Form data: {form.data}")
        return render_template('agency/register_agency.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error rendering template: {str(e)}")
        current_app.logger.error(f"Exception type: {type(e)}")
        current_app.logger.error(f"Exception args: {e.args}")
        import traceback
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        return "Error rendering template. Please check the logs for more information."


@agency_bp.route('/view_agencies', methods=['GET'])
@role_required(['admin', 'agency'])
def view_agencies():
    """View all agencies."""
    try:
        # If user is an admin, show all agencies
        if session.get('role') == 'admin':
            agencies = Agency.get_agencies()
        # If user is an agency user, show only their agencies
        else:
            user_id = session.get('user_id')
            user_agencies = Agency.get_agencies_for_user(user_id)
            agencies = [agency.to_dict() for agency in user_agencies]

        try:
            current_app.logger.info("Rendering view_agencies template")
            current_app.logger.info(f"Template path: app/templates/agency/view_agencies.html")
            current_app.logger.info(f"Number of agencies: {len(agencies)}")
            current_app.logger.info(f"Agencies data: {agencies}")
            return render_template('agency/view_agencies.html', agencies=agencies)
        except Exception as e:
            current_app.logger.error(f"Error rendering template: {str(e)}")
            current_app.logger.error(f"Exception type: {type(e)}")
            current_app.logger.error(f"Exception args: {e.args}")
            import traceback
            current_app.logger.error(f"Traceback: {traceback.format_exc()}")
            return "Error rendering template. Please check the logs for more information."
    except Exception as e:
        current_app.logger.error(f"Error getting agencies: {str(e)}")
        flash('Error getting agencies', 'danger')
        return redirect(url_for('admin_data.dashboard'))


@agency_bp.route('/update_agency/<uuid:agency_id>', methods=['GET', 'POST'])
@role_required(['admin', 'agency'])
def update_agency(agency_id):
    """Update an agency."""
    # Check if the user has permission to update this agency
    if session.get('role') == 'agency':
        user_id = session.get('user_id')
        user_agencies = Agency.get_agencies_for_user(user_id)
        user_agency_ids = [str(agency.agency_id) for agency in user_agencies]

        if str(agency_id) not in user_agency_ids:
            flash('You do not have permission to update this agency', 'danger')
            return redirect(url_for('agency.view_agencies'))

    try:
        agency_data = Agency.get_agency_by_id(agency_id)
    except Exception as e:
        current_app.logger.error(f"Error getting agency: {str(e)}")
        flash('Agency not found', 'danger')
        return redirect(url_for('agency.view_agencies'))

    form = AgencyUpdateForm()

    if request.method == 'GET':
        form.agency_name.data = agency_data['agency_name']
        form.agency_email.data = agency_data['agency_email']
        form.agency_phone.data = agency_data['agency_phone']
        form.agency_address.data = agency_data['agency_address']
        form.commission_rate.data = agency_data['commission_rate']
        form.is_active.data = str(agency_data['is_active'])

    if form.validate_on_submit():
        agency_name = form.agency_name.data.strip()
        agency_email = form.agency_email.data.strip()
        agency_phone = form.agency_phone.data.strip()
        agency_address = form.agency_address.data.strip() if form.agency_address.data else None
        commission_rate = form.commission_rate.data
        is_active = form.is_active.data == 'True'

        try:
            result = Agency.update_agency(
                agency_id=agency_id,
                agency_name=agency_name,
                agency_email=agency_email,
                agency_phone=agency_phone,
                agency_address=agency_address,
                commission_rate=commission_rate,
                is_active=is_active
            )

            if result:
                flash('Agency updated successfully', 'success')
                return redirect(url_for('agency.view_agencies'))
            else:
                flash('Error updating agency', 'danger')
                return redirect(url_for('agency.update_agency', agency_id=agency_id))
        except Exception as e:
            current_app.logger.error(f"Error updating agency: {str(e)}")
            flash('Error updating agency', 'danger')
            return redirect(url_for('agency.update_agency', agency_id=agency_id))

    try:
        current_app.logger.info("Rendering update_agency template")
        return render_template('agency/update_agency.html', form=form, agency=agency_data)
    except Exception as e:
        current_app.logger.error(f"Error rendering template: {str(e)}")
        return "Error rendering template. Please check the logs for more information."


@agency_bp.route('/upload_agency_logo/<uuid:agency_id>', methods=['GET', 'POST'])
@role_required(['admin', 'agency'])
def upload_agency_logo(agency_id):
    """Upload an agency logo."""
    # Check if the user has permission to update this agency
    if session.get('role') == 'agency':
        user_id = session.get('user_id')
        user_agencies = Agency.get_agencies_for_user(user_id)
        user_agency_ids = [str(agency.agency_id) for agency in user_agencies]

        if str(agency_id) not in user_agency_ids:
            flash('You do not have permission to update this agency', 'danger')
            return redirect(url_for('agency.view_agencies'))

    form = AgencyLogoForm()

    if form.validate_on_submit():
        logo = request.files['logo']

        if logo.filename == '':
            flash('No selected file', 'danger')
            return redirect(url_for('agency.upload_agency_logo', agency_id=agency_id))

        try:
            result = Agency.upload_agency_logo(agency_id, logo)

            if result['success']:
                flash('Logo uploaded successfully', 'success')
                return redirect(url_for('agency.view_agencies'))
            else:
                flash(result['message'], 'danger')
                return redirect(url_for('agency.upload_agency_logo', agency_id=agency_id))
        except Exception as e:
            current_app.logger.error(f"Error uploading logo: {str(e)}")
            flash('Error uploading logo', 'danger')
            return redirect(url_for('agency.upload_agency_logo', agency_id=agency_id))

    try:
        agency = Agency.get_agency_by_id(agency_id)
        return render_template('agency/upload_logo.html', form=form, agency=agency)
    except Exception as e:
        current_app.logger.error(f"Error getting agency: {str(e)}")
        flash('Agency not found', 'danger')
        return redirect(url_for('agency.view_agencies'))


@agency_bp.route('/agency_dashboard', methods=['GET'])
@role_required(['agency'])
def agency_dashboard():
    """Agency dashboard."""
    user_id = session.get('user_id')

    try:
        # Get the agencies associated with the user
        user_agencies = Agency.get_agencies_for_user(user_id)

        if not user_agencies:
            flash('You are not associated with any agencies', 'warning')
            return redirect(url_for('admin_data.dashboard'))

        # For simplicity, use the first agency if the user is associated with multiple
        agency = user_agencies[0]
        agency_id = agency.agency_id

        # Get the companies associated with the agency
        companies = agency.companies

        # Get the commissions for the agency
        commissions = AgencyCommission.get_commissions_by_agency(agency_id)

        # Calculate total commissions
        total_commissions = sum(commission['amount'] for commission in commissions)
        paid_commissions = sum(commission['amount'] for commission in commissions if commission['is_paid'])
        pending_commissions = total_commissions - paid_commissions

        return render_template(
            'agency/dashboard.html',
            agency=agency.to_dict(),
            companies=companies,
            commissions=commissions,
            total_commissions=total_commissions,
            paid_commissions=paid_commissions,
            pending_commissions=pending_commissions
        )
    except Exception as e:
        current_app.logger.error(f"Error loading agency dashboard: {str(e)}")
        flash('Error loading agency dashboard', 'danger')
        return redirect(url_for('admin_data.dashboard'))


@agency_bp.route('/agency_companies/<uuid:agency_id>', methods=['GET'])
@role_required(['admin', 'agency'])
def agency_companies(agency_id):
    """View companies associated with an agency."""
    # Check if the user has permission to view this agency's companies
    if session.get('role') == 'agency':
        user_id = session.get('user_id')
        user_agencies = Agency.get_agencies_for_user(user_id)
        user_agency_ids = [str(agency.agency_id) for agency in user_agencies]

        if str(agency_id) not in user_agency_ids:
            flash('You do not have permission to view this agency', 'danger')
            return redirect(url_for('agency.view_agencies'))

    try:
        agency = db.session.get(Agency, agency_id)
        if not agency:
            flash('Agency not found', 'danger')
            return redirect(url_for('agency.view_agencies'))

        companies = agency.companies
        return render_template('agency/agency_companies.html', agency=agency.to_dict(), companies=companies)
    except Exception as e:
        current_app.logger.error(f"Error getting agency companies: {str(e)}")
        flash('Error getting agency companies', 'danger')
        return redirect(url_for('agency.view_agencies'))


@agency_bp.route('/assign_company_to_agency', methods=['GET', 'POST'])
@role_required(['admin'])
def assign_company_to_agency():
    """Assign a company to an agency."""
    form = AgencyCompanyForm()

    # Populate the agency choices
    try:
        agencies = Agency.get_agencies()
        form.agency.choices = [(agency['agency_id'], agency['agency_name']) for agency in agencies]
    except Exception as e:
        current_app.logger.error(f"Error getting agencies: {str(e)}")
        form.agency.choices = []

    # Populate the company choices
    try:
        companies = Company.get_companies()
        form.company.choices = [(company['company_id'], company['company_name']) for company in companies]
    except Exception as e:
        current_app.logger.error(f"Error getting companies: {str(e)}")
        form.company.choices = []

    if form.validate_on_submit():
        agency_id = form.agency.data
        company_id = form.company.data

        try:
            result = Agency.assign_company_to_agency(company_id, agency_id)

            if result:
                flash('Company assigned to agency successfully', 'success')
                return redirect(url_for('agency.view_agencies'))
            else:
                flash('Error assigning company to agency', 'danger')
                return redirect(url_for('agency.assign_company_to_agency'))
        except Exception as e:
            current_app.logger.error(f"Error assigning company to agency: {str(e)}")
            flash('Error assigning company to agency', 'danger')
            return redirect(url_for('agency.assign_company_to_agency'))

    return render_template('agency/assign_company.html', form=form)


@agency_bp.route('/remove_company_from_agency/<uuid:agency_id>/<uuid:company_id>', methods=['POST'])
@role_required(['admin'])
def remove_company_from_agency(agency_id, company_id):
    """Remove a company from an agency."""
    try:
        result = Agency.remove_company_from_agency(company_id, agency_id)

        if result:
            flash('Company removed from agency successfully', 'success')
        else:
            flash('Error removing company from agency', 'danger')
    except Exception as e:
        current_app.logger.error(f"Error removing company from agency: {str(e)}")
        flash('Error removing company from agency', 'danger')

    return redirect(url_for('agency.agency_companies', agency_id=agency_id))


@agency_bp.route('/agency_users/<uuid:agency_id>', methods=['GET'])
@role_required(['admin', 'agency'])
def agency_users(agency_id):
    """View users associated with an agency."""
    # Check if the user has permission to view this agency's users
    if session.get('role') == 'agency':
        user_id = session.get('user_id')
        user_agencies = Agency.get_agencies_for_user(user_id)
        user_agency_ids = [str(agency.agency_id) for agency in user_agencies]

        if str(agency_id) not in user_agency_ids:
            flash('You do not have permission to view this agency', 'danger')
            return redirect(url_for('agency.view_agencies'))

    try:
        agency = db.session.get(Agency, agency_id)
        if not agency:
            flash('Agency not found', 'danger')
            return redirect(url_for('agency.view_agencies'))

        users = Agency.get_users_for_agency(agency_id)

        try:
            return render_template('agency/agency_users.html', agency=agency.to_dict(), users=users)
        except Exception as e:
            current_app.logger.error(f"Error rendering template: {str(e)}")
            return "Error rendering template. Please contact the administrator."
    except Exception as e:
        current_app.logger.error(f"Error getting agency users: {str(e)}")
        flash('Error getting agency users', 'danger')
        return redirect(url_for('agency.view_agencies'))


@agency_bp.route('/assign_user_to_agency', methods=['GET', 'POST'])
@role_required(['admin', 'agency'])
def assign_user_to_agency():
    """Assign a user to an agency."""
    form = AgencyUserForm()

    # Populate the agency choices
    if session.get('role') == 'admin':
        try:
            agencies = Agency.get_agencies()
            form.agency.choices = [(agency['agency_id'], agency['agency_name']) for agency in agencies]
        except Exception as e:
            current_app.logger.error(f"Error getting agencies: {str(e)}")
            form.agency.choices = []
    else:
        # If user is an agency user, only show their agencies
        user_id = session.get('user_id')
        try:
            user_agencies = Agency.get_agencies_for_user(user_id)
            form.agency.choices = [(str(agency.agency_id), agency.agency_name) for agency in user_agencies]
        except Exception as e:
            current_app.logger.error(f"Error getting user agencies: {str(e)}")
            form.agency.choices = []
    current_app.logger.info(f"Agency choices: {form.agency.choices}")
    if form.validate_on_submit():
        username = form.email.data.strip()  # Use email as username
        email = form.email.data.strip()
        first_name = form.first_name.data.strip()
        last_name = form.last_name.data.strip()
        phone = form.phone.data.strip()
        agency_id = form.agency.data

        # Check if user already exists
        try:
            existing_user = User.get_user_by_their_email(email)

            if existing_user:
                # User exists, assign to agency
                result = Agency.assign_user_to_agency(existing_user.user_id, agency_id)

                if result:
                    flash('User assigned to agency successfully', 'success')
                    return redirect(url_for('agency.agency_users', agency_id=agency_id))
                else:
                    flash('Error assigning user to agency', 'danger')
                    return redirect(url_for('agency.assign_user_to_agency'))
            else:
                # User doesn't exist, will create a new one
                # User doesn't exist, create new user with 'agency' role
                password = Auxillary.random_password()
                current_app.logger.info(f'Generated password: {password}')

                try:
                    # Check if user with same email, username or phone already exists
                    existing_email = User.query.filter_by(email=email).first()
                    if existing_email:
                        flash(f'User with email {email} already exists', 'danger')
                        return redirect(url_for('agency.assign_user_to_agency'))

                    existing_username = User.query.filter_by(username=username).first()
                    if existing_username:
                        flash(f'User with username {username} already exists', 'danger')
                        return redirect(url_for('agency.assign_user_to_agency'))

                    existing_phone = User.query.filter_by(phone_number=phone).first()
                    if existing_phone:
                        flash(f'User with phone number {phone} already exists', 'danger')
                        return redirect(url_for('agency.assign_user_to_agency'))

                    # Register the user
                    registered = User.register_user(
                        username=username,
                        email=email,
                        role='agency',
                        password=password,
                        phone_number=phone,
                        first_name=first_name,
                        last_name=last_name,
                        is_active=True
                    )
                    current_app.logger.info(f'User registration result: {registered}')

                    if registered:
                        flash('User registered successfully', 'success')
                    else:
                        flash('Failed to register user', 'danger')
                except Exception as e:
                    current_app.logger.error(f'Error registering user: {str(e)}')
                    flash('Error registering user', 'danger')
                    registered = False

                if registered:
                    # Get the newly created user
                    new_user = User.get_user_by_their_email(email)
                    if not new_user:
                        current_app.logger.error(f'Failed to retrieve newly created user with email: {email}')
                        flash('Error retrieving newly created user', 'danger')
                        return redirect(url_for('agency.assign_user_to_agency'))

                    # Verify agency exists
                    agency = db.session.get(Agency, agency_id)
                    if not agency:
                        flash('Agency not found', 'danger')
                        return redirect(url_for('agency.assign_user_to_agency'))

                    # Assign the user to the agency
                    current_app.logger.info(f'Assigning user {new_user.user_id} to agency {agency_id}')

                    try:
                        # Assign user to agency using the model method
                        result = Agency.assign_user_to_agency(new_user.user_id, agency_id)

                        if result:
                            flash('User successfully assigned to agency', 'success')
                            # Redirect to the agency_users page with the correct agency_id
                            return redirect(url_for('agency.agency_users', agency_id=agency_id))
                        else:
                            flash('Failed to assign user to agency', 'danger')
                    except Exception as e:
                        current_app.logger.error(f'Error assigning user to agency: {str(e)}')
                        flash('Error assigning user to agency', 'danger')
                        result = False

                    if result:
                        # Send email with login credentials
                        from app.routes.token.token_manager import TokenManager
                        token_manager = TokenManager()
                        token = token_manager.generate_confirmation_token(email)
                        confirm_url = url_for('token.confirm_email', token=token, _external=True)

                        agency = db.session.get(Agency, agency_id)
                        agency_name = agency.agency_name if agency else "the agency"

                        body = f"""
                        <h3>Hello {first_name},</h3>
                        <p>Welcome to the NetPipo platform. Your account has been created as an agency user for {agency_name}.</p>
                        <p>Your login credentials are:</p>
                        <p>Username: <strong>{username}</strong></p>
                        <p>Password: <strong>{password}</strong></p>
                        <p>Please confirm your email by clicking on the following link: {confirm_url}</p>
                        """
                        subject = "Agency Account Confirmation"

                        try:
                            Auxillary.send_netpipo_email(subject, email, body)
                            flash('User added to agency successfully. Login credentials have been sent to the user.', 'success')
                            return redirect(url_for('agency.agency_users', agency_id=agency_id))
                        except Exception as e:
                            current_app.logger.error(f"Error sending email: {str(e)}")
                            flash('User added to agency successfully, but there was an error sending the email.', 'warning')
                            return redirect(url_for('agency.agency_users', agency_id=agency_id))
                    else:
                        flash('User created but could not be assigned to the agency', 'warning')
                        return redirect(url_for('agency.assign_user_to_agency'))
                else:
                    flash('Error creating user', 'danger')
                    return redirect(url_for('agency.assign_user_to_agency'))
        except Exception as e:
            current_app.logger.error(f"Error assigning user to agency: {str(e)}")
            flash('Error assigning user to agency', 'danger')
            return redirect(url_for('agency.assign_user_to_agency'))
    elif form.errors:
        flash('Please correct the errors in the form', 'danger')
        # log the form errors
        for field, errors in form.errors.items():
            for error in errors:
                current_app.logger.error(f"{field}: {error}")
                flash(f"{field}: {error}", 'danger')

    try:
        return render_template('agency/assign_user.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error rendering template: {str(e)}")
        return "Error rendering template. Please contact the administrator."


@agency_bp.route('/remove_user_from_agency/<uuid:agency_id>/<uuid:user_id>', methods=['POST'])
@role_required(['admin', 'agency'])
def remove_user_from_agency(agency_id, user_id):
    """Remove a user from an agency."""
    # Check if the user has permission to remove users from this agency
    if session.get('role') == 'agency':
        current_user_id = session.get('user_id')
        user_agencies = Agency.get_agencies_for_user(current_user_id)
        user_agency_ids = [str(agency.agency_id) for agency in user_agencies]

        if str(agency_id) not in user_agency_ids:
            flash('You do not have permission to modify this agency', 'danger')
            return redirect(url_for('agency.view_agencies'))

        # Prevent agency users from removing themselves
        if str(current_user_id) == str(user_id):
            flash('You cannot remove yourself from the agency', 'danger')
            return redirect(url_for('agency.agency_users', agency_id=agency_id))

    try:
        result = Agency.remove_user_from_agency(user_id, agency_id)

        if result:
            flash('User removed from agency successfully', 'success')
        else:
            flash('Error removing user from agency', 'danger')
    except Exception as e:
        current_app.logger.error(f"Error removing user from agency: {str(e)}")
        flash('Error removing user from agency', 'danger')

    return redirect(url_for('agency.agency_users', agency_id=agency_id))


@agency_bp.route('/agency_commissions/<uuid:agency_id>', methods=['GET'])
@role_required(['admin', 'agency'])
def agency_commissions(agency_id):
    """View commissions for an agency."""
    # Check if the user has permission to view this agency's commissions
    if session.get('role') == 'agency':
        user_id = session.get('user_id')
        user_agencies = Agency.get_agencies_for_user(user_id)
        user_agency_ids = [str(agency.agency_id) for agency in user_agencies]

        if str(agency_id) not in user_agency_ids:
            flash('You do not have permission to view this agency', 'danger')
            return redirect(url_for('agency.view_agencies'))

    try:
        agency = db.session.get(Agency, agency_id)
        if not agency:
            flash('Agency not found', 'danger')
            return redirect(url_for('agency.view_agencies'))

        commissions = AgencyCommission.get_commissions_by_agency(agency_id)

        # Calculate totals
        total_commissions = sum(commission['amount'] for commission in commissions)
        paid_commissions = sum(commission['amount'] for commission in commissions if commission['is_paid'])
        pending_commissions = total_commissions - paid_commissions

        return render_template(
            'agency/agency_commissions.html',
            agency=agency.to_dict(),
            commissions=commissions,
            total_commissions=total_commissions,
            paid_commissions=paid_commissions,
            pending_commissions=pending_commissions
        )
    except Exception as e:
        current_app.logger.error(f"Error getting agency commissions: {str(e)}")
        flash('Error getting agency commissions', 'danger')
        return redirect(url_for('agency.view_agencies'))


@agency_bp.route('/record_commission', methods=['GET', 'POST'])
@role_required(['admin'])
def record_commission():
    """Record a commission for an agency."""
    form = AgencyCommissionForm()

    # Populate the agency choices
    try:
        agencies = Agency.get_agencies()
        form.agency.choices = [(agency['agency_id'], agency['agency_name']) for agency in agencies]
    except Exception as e:
        current_app.logger.error(f"Error getting agencies: {str(e)}")
        form.agency.choices = []

    # Populate the company choices
    try:
        companies = Company.get_companies()
        form.company.choices = [(company['company_id'], company['company_name']) for company in companies]
    except Exception as e:
        current_app.logger.error(f"Error getting companies: {str(e)}")
        form.company.choices = []

    if form.validate_on_submit():
        agency_id = form.agency.data
        company_id = form.company.data
        amount = form.amount.data
        payment_reference = form.payment_reference.data.strip() if form.payment_reference.data else None
        description = form.description.data.strip() if form.description.data else None

        try:
            # Check if the company is associated with the agency
            agency = db.session.get(Agency, agency_id)
            company = db.session.get(Company, company_id)

            if not agency or not company:
                flash('Agency or company not found', 'danger')
                return redirect(url_for('agency.record_commission'))

            if company not in agency.companies:
                flash('Company is not associated with this agency', 'danger')
                return redirect(url_for('agency.record_commission'))

            # Record the commission
            commission = AgencyCommission.create_commission(
                agency_id=agency_id,
                company_id=company_id,
                amount=amount,
                description=description,
                payment_reference=payment_reference
            )

            if commission:
                flash('Commission recorded successfully', 'success')
                return redirect(url_for('agency.agency_commissions', agency_id=agency_id))
            else:
                flash('Error recording commission', 'danger')
                return redirect(url_for('agency.record_commission'))
        except Exception as e:
            current_app.logger.error(f"Error recording commission: {str(e)}")
            flash('Error recording commission', 'danger')
            return redirect(url_for('agency.record_commission'))

    return render_template('agency/record_commission.html', form=form)


@agency_bp.route('/mark_commission_paid/<uuid:commission_id>', methods=['POST'])
@role_required(['admin'])
def mark_commission_paid(commission_id):
    """Mark a commission as paid."""
    try:
        commission = db.session.get(AgencyCommission, commission_id)
        if not commission:
            flash('Commission not found', 'danger')
            return redirect(url_for('agency.view_agencies'))

        agency_id = commission.agency_id

        result = AgencyCommission.mark_as_paid(commission_id)

        if result:
            flash('Commission marked as paid successfully', 'success')
        else:
            flash('Error marking commission as paid', 'danger')

        return redirect(url_for('agency.agency_commissions', agency_id=agency_id))
    except Exception as e:
        current_app.logger.error(f"Error marking commission as paid: {str(e)}")
        flash('Error marking commission as paid', 'danger')
        return redirect(url_for('agency.view_agencies'))
