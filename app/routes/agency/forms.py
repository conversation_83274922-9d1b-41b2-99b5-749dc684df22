from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, SelectField, DecimalField, FileField
from wtforms.validators import DataRequired, Email, Length, NumberRange, Optional
from flask_wtf.file import FileAllowed


class AgencyRegistrationForm(FlaskForm):
    """Form for registering a new agency."""
    agency_name = StringField('Agency Name', validators=[DataRequired(), Length(min=2, max=255)])
    agency_email = StringField('Agency Email', validators=[DataRequired(), Email()])
    agency_phone = StringField('Agency Phone', validators=[DataRequired(), Length(min=10, max=15)])
    agency_address = StringField('Agency Address', validators=[Optional(), Length(max=255)])
    commission_rate = DecimalField('Commission Rate (%)', validators=[
        DataRequired(), 
        NumberRange(min=0, max=100, message='Commission rate must be between 0 and 100')
    ], default=10.0)
    submit = SubmitField('Register Agency')


class AgencyUpdateForm(FlaskForm):
    """Form for updating an agency."""
    agency_name = StringField('Agency Name', validators=[DataRequired(), Length(min=2, max=255)])
    agency_email = StringField('Agency Email', validators=[DataRequired(), Email()])
    agency_phone = StringField('Agency Phone', validators=[DataRequired(), Length(min=10, max=15)])
    agency_address = StringField('Agency Address', validators=[Optional(), Length(max=255)])
    commission_rate = DecimalField('Commission Rate (%)', validators=[
        DataRequired(), 
        NumberRange(min=0, max=100, message='Commission rate must be between 0 and 100')
    ])
    is_active = SelectField('Status', choices=[('True', 'Active'), ('False', 'Inactive')], validators=[DataRequired()])
    submit = SubmitField('Update Agency')


class AgencyLogoForm(FlaskForm):
    """Form for uploading an agency logo."""
    logo = FileField('Logo', validators=[
        DataRequired(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images only!')
    ])
    submit = SubmitField('Upload Logo')


class AgencyUserForm(FlaskForm):
    """Form for adding a user to an agency."""
    email = StringField('Email', validators=[DataRequired(), Email()])
    first_name = StringField('First Name', validators=[DataRequired(), Length(min=2, max=255)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(min=2, max=255)])
    phone = StringField('Phone', validators=[DataRequired(), Length(min=10, max=15)])
    agency = SelectField('Agency', validators=[DataRequired()], coerce=str)
    submit = SubmitField('Add User')


class AgencyCompanyForm(FlaskForm):
    """Form for adding a company to an agency."""
    company = SelectField('Company', validators=[DataRequired()], coerce=str)
    agency = SelectField('Agency', validators=[DataRequired()], coerce=str)
    submit = SubmitField('Assign Company')


class AgencyCommissionForm(FlaskForm):
    """Form for recording a commission."""
    agency = SelectField('Agency', validators=[DataRequired()], coerce=str)
    company = SelectField('Company', validators=[DataRequired()], coerce=str)
    amount = DecimalField('Amount', validators=[
        DataRequired(), 
        NumberRange(min=0, message='Amount must be greater than 0')
    ])
    payment_reference = StringField('Payment Reference', validators=[Optional(), Length(max=255)])
    description = StringField('Description', validators=[Optional(), Length(max=255)])
    submit = SubmitField('Record Commission')
