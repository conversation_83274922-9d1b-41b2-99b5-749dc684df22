from flask import Blueprint, render_template, request, jsonify, current_app, session
from app.models.company import Site
from app.decorators.role_decorator import role_required
from app.routes.company_locations.forms import SiteForm
from app.utils.db_connection import DatabaseConnection

company_locations_bp = Blueprint('company_locations', __name__)

@company_locations_bp.route('/add_locations', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def add_locations():
    form = SiteForm()
    if request.method == 'POST':
        if not form.validate():
            return jsonify(form.errors), 400
        site_name = form.site_name.data
        location = form.location.data
        latitude = form.latitude.data
        longitude = form.longitude.data
        current_app.logger.info('Site Name: %s', site_name)
        current_app.logger.info('Location: %s', location)
        current_app.logger.info('Latitude: %s', latitude)
        current_app.logger.info('Longitude: %s', longitude)
        
        # Retrieve the database name from session
        database_name = session['database_name']
        
        # Create a database instance
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db:
            try:
                # Add the site to the database
                result = Site.add_site(db, site_name, location, longitude=longitude, latitude=latitude)
                current_app.logger.info('Result: %s', result)
                if result:
                    return jsonify({'message': 'Site added successfully'}), 200
                else:
                    return jsonify({'message': 'Site not added'}), 400
            except Exception as e:
                current_app.logger.error('Error adding site: %s', e)
                return jsonify({'message': 'Error adding site'}), 400
    try:
        current_app.logger.info("Rendering add_locations.html")
        return render_template('company_locations/add_locations.html', form=form)
    except Exception as e:
        current_app.logger.error('Error rendering add_locations.html: %s', e)
        return jsonify({'message': 'Error rendering add_locations.html'}), 400
    
@company_locations_bp.route('/view_locations', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def view_locations():
    try:
        # Retrieve the database name from session
        database_name = session['database_name']
        
        # Create a database instance
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db:
            # Retrieve all sites from the database
            sites = Site.get_sites(db)
            current_app.logger.info('Sites: %s', sites)
            return render_template('company_locations/view_locations.html', sites=sites)
    except Exception as e:
        current_app.logger.error('Error rendering view_locations.html: %s', e)
        return jsonify({'message': 'Error rendering view_locations.html'}), 400
                
@company_locations_bp.route('/update_location/<uuid:site_id>', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def update_location(site_id):
    try:
        # Retrieve the database name from session
        database_name = session['database_name']
        
        # Create a database instance
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db:
            # Retrieve the site from the database
            sites_to_update = Site.get_site_by_id(db, site_id)
            current_app.logger.info('Sites to update: %s', sites_to_update)
            
            if not sites_to_update:
                return jsonify({'message': 'Site not found'}), 404
            
            try:
                 # Access dictionary keys
                site_name = sites_to_update['site_name']
                location = sites_to_update['location']
                current_app.logger.info('Site Name: %s', site_name)
                current_app.logger.info('Location: %s', location)
            except Exception as e:
                current_app.logger.error('Error retrieving site details: %s', e)
                return jsonify({'message': 'Error retrieving site details'}), 400

            # Initialize the form with existing data
            form = SiteForm(data={'site_name': site_name, 'location': location})

            if request.method == 'POST':
                if form.validate_on_submit():  # Validate form on POST
                    site_name = form.site_name.data
                    location = form.location.data
                    current_app.logger.info('Site Name: %s', site_name)
                    current_app.logger.info('Location: %s', location)
                    
                    # Update the site in the database
                    result = Site.update_site(db, site_id, site_name, location)
                    current_app.logger.info('Result: %s', result)
                    
                    if result:
                        return jsonify({'message': 'Site updated successfully'}), 200
                    else:
                        return jsonify({'message': 'Site not updated'}), 400
            
            # Render the form with prepopulated data
            return render_template('company_locations/update_location.html', form=form)
    except Exception as e:
        current_app.logger.error('Error rendering update_location.html: %s', e)
        return jsonify({'message': 'Error rendering update_location.html'}), 400
