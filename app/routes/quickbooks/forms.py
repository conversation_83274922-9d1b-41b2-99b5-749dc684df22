from flask_wtf import FlaskForm, Form
from wtforms import <PERSON>Field, SubmitField, SelectField, HiddenField, <PERSON>oleanField
from wtforms.validators import DataRequired, Length, ValidationError
from wtforms.widgets import TextArea

class ChartOffAccountForm(FlaskForm):
    """Chart of Account form"""
    name = StringField('Name', validators=[DataRequired(), Length(min=2, max=100)])
    description = StringField('Description', widget=TextArea())
    account_type = SelectField('Account Type', choices=[], validators=[DataRequired()])
    account_subtype = SelectField('Detail Type', choices=[], validators=[DataRequired()])
    submit = SubmitField('Submit')

class JournalEntryForm(FlaskForm):
    """Journal Entry form"""
    account = SelectField('Account', choices=[], validators=[DataRequired()])
    journal_data = HiddenField('Journal Data', validators=[DataRequired()])
    submit = SubmitField('Submit')
    
    