from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, SubmitField
from wtforms import IntegerField, TextAreaField, FloatField, SelectField
from wtforms.validators import DataRequired, Length
from uuid import UUID

class AddPlanForm(FlaskForm):
    plan_name = StringField('Plan Name', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('Description', validators=[DataRequired(), Length(min=2, max=100)])
    price = IntegerField('Plan Price', validators=[DataRequired()])
    num_employees = IntegerField('Number of Employees', validators=[DataRequired()])
    price_per_employee = Float<PERSON>ield("Price Per Employee", default=0.00)
    submit = SubmitField('Add Plan')


class EditPlanForm(FlaskForm):
    plan_name = StringField('Plan Name', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('Description', validators=[DataRequired(), Length(min=2, max=100)])
    price = FloatField('Plan Price', validators=[DataRequired()])
    num_employees = IntegerField('Number of Employees', validators=[DataRequired()])
    price_per_employee = FloatField("Price Per Employee")
    submit = SubmitField('Update Plan')

class AddFeatureForm(FlaskForm):
    feature_id = SelectField('Select Feature', validators=[DataRequired()])
    submit = SubmitField('Add Feature')
