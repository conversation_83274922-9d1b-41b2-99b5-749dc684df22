from flask import request, jsonify, Blueprint, current_app, render_template, flash, redirect, url_for
from app.models.central import Plans, Company, Features
from app.routes.plans.forms import AddPlanForm, EditPlanForm, AddFeatureForm
from app.decorators.admin_decorator import admin_required

plans_bp = Blueprint('plans', __name__)

@plans_bp.route('/add_plans', methods=['POST', 'GET'])
@admin_required
def add_plans():
    form = AddPlanForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            return jsonify({'error': form.errors}), 400
        plan_name = form.plan_name.data.lower()
        description = form.description.data
        price = form.price.data
        num_of_employees = form.num_employees.data
        price_per_employee = form.price_per_employee.data

        # Try adding the plan to the database
        try:
            added = Plans.add_plan(plan_name, description, price, num_of_employees, price_per_employee)
            if not added:
                current_app.logger.error(f'Error adding plan {plan_name} to the database')
                return jsonify({'error': 'Plan already exists'}), 400
            if added == 'exists':
                current_app.logger.error(f'Error adding plan {plan_name} to the database')
                message = f"We could not add plan: {plan_name} because it already exists"
                flash(message, 'danger')
                return redirect(url_for('plans.add_plans'))
            flash('Plan added successfully', 'success')
            return redirect(url_for('plans.add_plans'))
        except Exception as e:
            current_app.logger.error(f'Error adding plan {plan_name} to the database: {e}')
            flash('Error adding plan ', 'danger')
            return redirect(url_for('plans.add_plans'))
        
    plans = Plans.get_plans()
        
    return render_template('plans/add_plans.html', form=form, plans=plans)

@plans_bp.route('/edit_plan/<plan_id>', methods=['POST', 'GET'])
@admin_required
def edit_plan(plan_id):
    form = EditPlanForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            return jsonify({'error': form.errors}), 400
        plan_name = form.plan_name.data.lower()
        description = form.description.data
        price = form.price.data
        num_of_employees = form.num_employees.data
        price_per_employee = form.price_per_employee.data
        print(plan_name, price, num_of_employees, price_per_employee)

        # Try adding the plan to the database
        try:
            updated = Plans.update_plan(plan_id, plan_name, description, price, num_of_employees, price_per_employee)
            if not updated:
                current_app.logger.error(f'Error updating plan {plan_name} to the database')
                return jsonify({'error': 'Plan already exists'}), 400
            if updated == 'exists':
                current_app.logger.error(f'Error updating plan {plan_name} to the database')
                message = f"We could not update plan: {plan_name} because it already exists"
                flash(message, 'danger')
                return redirect(url_for('plans.add_plans'))
            flash('Plan updated successfully', 'success')
            return redirect(url_for('plans.add_plans'))
        except Exception as e:
            current_app.logger.error(f'Error updating plan {plan_name} to the database: {e}')
            flash('Error updating plan ', 'danger')
            return redirect(url_for('plans.add_plans'))
    try:
        plan = Plans.get_plan_by_id(plan_id)
        current_app.logger.info('Plan fetched successfully')
    except Exception as e:
        current_app.logger.error('Error getting plan: %s', str(e))
        plan = []
    
    form.plan_name.data = plan['plan_name']
    form.description.data = plan['description']
    form.price.data = plan['price']
    form.num_employees.data = plan['num_of_employees']
    form.price_per_employee.data=plan["price_per_employee"]
   
    return render_template('plans/edit_plans.html', form=form)

@plans_bp.route('/delete_plan/<plan_id>', methods=['POST', 'GET'])
@admin_required
def delete_plan(plan_id):
    try:
        Plans.delete_plan(plan_id)
        flash('Plan deleted successfully', 'success')
        return redirect(url_for('plans.add_plans'))
    except Exception as e:
        current_app.logger.error(f'Error deleting plan {plan_id} from the database: {e}')
        flash('Error deleting plan', 'danger')
        return redirect(url_for('plans.add_plans'))
    

@plans_bp.route('/assign_features', methods=['POST', 'GET'])
@admin_required
def assign_features():
    form = AddFeatureForm()
    plans = Plans.get_plans()
    features = Features.get_features()
    current_app.logger.info(f'plans: {plans}')
    
    plan_features = []
    for plan in plans:
        plan_features.append(plan['features'])

    current_app.logger.info(f'plan features: {plan_features}')

    # Populate the feature selection field
    form.feature_id.choices = [(feature['id'], feature['feature_name']) for feature in features]

    if form.validate_on_submit():
        plan_id = request.form.get('plan_id')
        feature_id = form.feature_id.data
        result = Plans.add_feature(plan_id, feature_id)
        if result == 'exists':
            message = f"Feature already exists in plan"
            flash(message, 'danger')
            return redirect(url_for('plans.assign_features'))
        elif result == 'success':
            flash('Feature added successfully', 'success')
            return redirect(url_for('plans.assign_features'))
        else:
            flash('Failed to add feature.', 'danger')

    return render_template('plans/manage_plan_features.html', form=form, plans=plans)
    
@plans_bp.route('/remove_feature/<plan_id>/<feature_id>', methods=['POST'])
@admin_required
def remove_feature(plan_id, feature_id):
    current_app.logger.info(f'plan_id: {plan_id}, feature_id: {feature_id}')
    try:
        Plans.remove_feature(plan_id, feature_id)
        flash('Feature removed successfully', 'success')
        return redirect(url_for('plans.assign_features'))
    except Exception as e:
        current_app.logger.error(f'Error deleting feature {feature_id} from the database: {e}')
        flash('Error deleting feature', 'danger')
        return redirect(url_for('plans.assign_features'))
    

    
    

