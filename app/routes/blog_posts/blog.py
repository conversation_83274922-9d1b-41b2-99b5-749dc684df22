from flask import Blueprint, render_template, request, redirect, url_for
from flask import jsonify, flash, current_app as app
from app.routes.blog_posts.forms import BlogCategoryForm, BlogTagForm
from app.models.central_blogs import BlogCategories, BlogPosts, BlogTags
import imghdr
from app.decorators.admin_decorator import admin_required

blog_posts = Blueprint('blog_posts', __name__)
@blog_posts.route('/create_blog', methods=['GET', 'POST'])
@admin_required
def create_blog():
    """Create a blog post"""
    try:
        blog_categories = BlogCategories.get_blog_categories()
        app.logger.info(f"Categories: {blog_categories}")
        for category in blog_categories:
            app.logger.info(f"Category Name: {category.category_name}")
            app.logger.info(f"Category Description: {category.category_description}")
            app.logger.info(f"Category ID: {category.category_id}")
    except Exception as e:
        app.logger.error(f"Error getting categories: {str(e)}")
        blog_categories = []

    # Get the tags
    try:
        blog_tags = BlogTags.get_blog_tags()
        app.logger.info(f"Tags: {blog_tags}")
        for tag in blog_tags:
            app.logger.info(f"Tag Name: {tag.tag_name}")
            app.logger.info(f"Tag Description: {tag.tag_description}")
            app.logger.info(f"Tag ID: {tag.tag_id}")
    except Exception as e:
        app.logger.error(f"Error getting tags: {str(e)}")
        blog_tags = []

    if request.method == 'POST':
        data = request.json
        # display the data in form of json
        print(data)
        # Get the data from the request
        title = data.get('title')
        content = data.get('content')
        category = data.get('categories')
        tags = data.get('tags')
        image_path = data.get('imagePath')
        app.logger.info(f'image_path: {image_path} and the type is: {type(image_path)}')
     

        app.logger.info(f"category: {category} and the type is: {type(category)}")
        app.logger.info(f"tags: {tags} and the type is: {type(tags)}")
        # Create the blog
        try:
            result = BlogPosts.create_blog(title, content, image_path=image_path, category_ids=category, tag_ids=tags, user_id=None)
            app.logger.info(f"result: {result}")
        except Exception as e:
            app.logger.error(f"Error creating blog: {e}")
            return jsonify({'message': 'An error occurred while creating the blog'}), 400
        return jsonify({'message': 'Blog created successfully'}), 200
    return render_template('blog_posts/blog_creator.html', blog_categories=blog_categories, blog_tags=blog_tags)

@blog_posts.route('/view_blogs', methods=['GET'])
@admin_required
def view_blogs():
    try:
        blogs = BlogPosts.get_blogs()
        # convert blogs to a dict:
        blogs = [blog.to_dict() for blog in blogs]
        app.logger.info(f"Blogs: {blogs}")
    except Exception as e:
        app.logger.error(f"Error getting blogs: {str(e)}")
        blogs = []
    return render_template('blog_posts/view_blogs.html', blogs=blogs)

@blog_posts.route('/blog_page', methods=['GET'])
def blog_page():
    title = 'Blog Posts'
    """View blog posts"""
    blogs = BlogPosts.get_blogs()
    # convert blogs to a dict:
    if blogs:
        blogs = [blog.to_dict() for blog in blogs]
        app.logger.info(f"Blogs: {blogs}")
    else:
        blogs = []
    return render_template('blog_posts/blog_page.html', blogs=blogs, title=title)

@blog_posts.route('/blog_details/<blog_id>', methods=['GET'])
def blog_details(blog_id):
    """View blog post details"""
    blog = BlogPosts.get_blog_by_id(blog_id)
    if not blog:
        flash('danger', 'Blog not found')
        return redirect(url_for('blog_posts.blog_page'))
    # convert blog to a dict:
    blog = blog.to_dict()
    title = blog.get('title')
    app.logger.info(f"Blog: {blog}")
    return render_template('blog_posts/blog_details.html', blog=blog, title=title)

@blog_posts.route('/upload_image', methods=['GET', 'POST'])
@admin_required
def upload_image():
    """Upload an image via form to DigitalOcean Spaces"""
    if request.method == 'POST':
        file = request.files.get('file')
        if not file:
            flash('No file selected', 'danger')
            return redirect(request.url)

        # Validate file type
        file_type = imghdr.what(file)
        if file_type not in ['jpeg', 'png', 'gif']:
            flash('Invalid file type. Only jpeg, png, and gif allowed.', 'danger')
            return redirect(request.url)

        try:
            image_url = BlogPosts.upload_document(file)
            # Return JSON with the image URL/path
            return jsonify({'image_path': image_url}), 200

        except Exception as e:
            app.logger.error(f"Upload error: {e}")
            return jsonify({'error': f"Error uploading file: {str(e)}"}), 500

    return render_template('blog_posts/upload_image.html')



@blog_posts.route('/update_blog_category/<category_id>', methods=['GET', 'POST'])
@admin_required
def update_blog_category(category_id):
    """Update the blog category"""
    form = BlogCategoryForm()
    # Get the category by id
    category = BlogCategories.get_category_by_id(category_id)
    if not category:
        flash('danger', 'Category not found')
        return redirect(url_for('blog_posts.add_blog_category'))
    
    # Set the form data
    form.name.data = category.category_name
    form.description.data = category.category_description

    if request.method == 'POST':
        data = request.form
        if not form.validate_on_submit():
            message = f"Invalid data: {form.errors}"
            flash('danger', message)
            return render_template('blog_posts/update_category.html', form=form)
        # Get data from the form
        category_name = data.get('name')
        category_description = data.get('description')
        # Add category to the database
        try:
            result = BlogCategories.update_category(category_id, category_name, category_description)
            app.logger.info(f"result: {result}")
            flash('Category updated successfully', 'success')
            return redirect(url_for('blog_posts.add_blog_category'))
        except Exception as e:
            app.logger.error(f"Error updating category: {e}")
            flash('danger', 'An error occurred while updating the category')
            return redirect(url_for('blog_posts.add_blog_category'))
    return render_template('blog_posts/update_category.html', form=form)

@blog_posts.route('/add_blogtag', methods=['GET', 'POST'])
@admin_required
def add_blogtag():
    """Add Blog tags"""
    form = BlogTagForm()
    if request.method == 'POST':
        data = request.form
        if not form.validate_on_submit():
            message = f"Invalid data: {form.errors}"
            flash('danger', message)
            return redirect(url_for('blog_posts.add_blogtag'))
        # Get data from the form
        tag_name = data.get('name')
        tag_description = data.get('description')
        # Add tag to the database
        try:
            result = BlogTags.add_tag(tag_name, tag_description)
            app.logger.info(f"result: {result}")
            flash('Tag added successfully', 'success')
            return redirect(url_for('blog_posts.add_blogtag'))
        except Exception as e:
            app.logger.error(f"Error adding tag: {e}")
            flash('danger', 'An error occurred while adding the tag')
            return redirect(url_for('blog_posts.add_blogtag'))
        
    try:
        blog_tags = BlogTags.get_blog_tags()
        # convert tags to a dict:
        tags = [tag.to_dict() for tag in blog_tags]
        app.logger.info(f"Tags: {tags}")
    except Exception as e:
        app.logger.error(f"Error getting tags: {str(e)}")
        blog_tags = []

    return render_template('blog_posts/add_tag.html', form=form, blog_tags=blog_tags)

@blog_posts.route('/update_blogtag/<tag_id>', methods=['GET', 'POST'])
@admin_required
def update_blogtag(tag_id):
    """Update the blog tag"""
    form = BlogTagForm()
    # Get the tag by id
    tag = BlogTags.get_tag_by_id(tag_id)
    if not tag:
        flash('danger', 'Tag not found')
        return redirect(url_for('blog_posts.add_blogtag'))
    
    # Set the form data
    form.name.data = tag.tag_name
    form.description.data = tag.tag_description

    if request.method == 'POST':
        data = request.form
        if not form.validate_on_submit():
            message = f"Invalid data: {form.errors}"
            flash('danger', message)
            return render_template('blog_posts/update_tag.html', form=form)
        # Get data from the form
        tag_name = data.get('name')
        tag_description = data.get('description')
        # Add tag to the database
        try:
            result = BlogTags.update_tag(tag_id, tag_name, tag_description)
            app.logger.info(f"result: {result}")
            flash('Tag updated successfully', 'success')
            return redirect(url_for('blog_posts.add_blogtag'))
        except Exception as e:
            app.logger.error(f"Error updating tag: {e}")
            flash('danger', 'An error occurred while updating the tag')
            return redirect(url_for('blog_posts.add_blogtag'))
    return render_template('blog_posts/update_tag.html', form=form)

@blog_posts.route('/add_blog_category', methods=['GET', 'POST'])
@admin_required
def add_blog_category():
    """Add Blog categories"""
    form = BlogCategoryForm()
    if request.method == 'POST':
        data = request.form
        if not form.validate_on_submit():
            message = f"Invalid data: {form.errors}"
            flash('danger', message)
            return redirect(url_for('blog_posts.add_blog_category'))
        # Get data from the form
        category_name = data.get('name')
        category_description = data.get('description')
        # Add category to the database
        try:
            result = BlogCategories.add_category(category_name, category_description)
            app.logger.info(f"result: {result}")
            flash('Category added successfully', 'success')
            return redirect(url_for('blog_posts.add_blog_category'))
        except Exception as e:
            app.logger.error(f"Error adding category: {e}")
            flash('danger', 'An error occurred while adding the category')
            return redirect(url_for('blog_posts.add_blog_category'))
        
    try:
        blog_categories = BlogCategories.get_blog_categories()
        # convert categories to a dict:
        categories = [category.to_dict() for category in blog_categories]
        app.logger.info(f"Categories: {categories}")
    except Exception as e:
        app.logger.error(f"Error getting categories: {str(e)}")
        blog_categories = []

    return render_template('blog_posts/add_category.html', form=form, blog_categories=blog_categories)
