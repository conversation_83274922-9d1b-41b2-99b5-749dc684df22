from flask_wtf import FlaskForm
from wtforms.validators import DataRequired
from wtforms import DecimalField, SubmitField, StringField, TextAreaField

class BlogCategoryForm(FlaskForm):
    name = String<PERSON>ield('Name', validators=[DataRequired()])
    description = TextAreaField('Description', validators=[DataRequired()])
    submit = SubmitField('Submit')

class BlogTagForm(FlaskForm):
    name = StringField('Name', validators=[DataRequired()])
    description = TextAreaField('Description')
    submit = SubmitField('Submit')