"""
Database monitoring routes.

This module provides routes to monitor database connections and diagnose connection issues.
"""
from flask import Blueprint, jsonify, current_app, render_template
from app.utils.db_connection import DatabaseConnection
from app.utils.db_monitor import (
    get_active_connections,
    get_connection_details,
    get_connection_stats,
    monitor_connections
)
from app.utils.connection_limiter import get_connection_stats as get_limiter_stats
from app.helpers.company_helpers import CompanyHelpers
from app.decorators.admin_decorator import admin_required
import os
import time
import datetime
import psycopg2
from dotenv import load_dotenv

load_dotenv()

db_monitor_bp = Blueprint('db_monitor', __name__)

@db_monitor_bp.route('/monitor/connections', methods=['GET'])
@admin_required
def monitor_db_connections():
    """Monitor database connections.

    Returns:
        JSON response with connection statistics
    """
    try:
        # Create a database connection
        db_connection = DatabaseConnection()

        # Get all database names
        database_names = CompanyHelpers.get_database_names()

        # Monitor central database
        central_db = os.getenv('DB_NAME', 'netpipo')
        results = [monitor_connections(db_connection, central_db)]

        # Monitor company databases (sample of 5 to avoid too many connections)
        sample_size = min(5, len(database_names))
        sample_dbs = database_names[:sample_size]

        for db_name in sample_dbs:
            result = monitor_connections(db_connection, db_name)
            results.append(result)

        return jsonify({
            'status': 'success',
            'message': f'Monitored {len(results)} databases',
            'results': results
        })
    except Exception as e:
        current_app.logger.error(f"Error monitoring database connections: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Error monitoring database connections: {str(e)}'
        }), 500

@db_monitor_bp.route('/monitor/connection_stats', methods=['GET'])
@admin_required
def get_db_connection_stats():
    """Get database connection statistics.

    Returns:
        JSON response with connection statistics
    """
    try:
        # Create a database connection
        db_connection = DatabaseConnection()

        # Get central database engine
        central_db = os.getenv('DB_NAME', 'netpipo')
        engine = db_connection._get_engine(central_db)

        # Get connection statistics
        stats = get_connection_stats(engine)

        return jsonify({
            'status': 'success',
            'database': central_db,
            'stats': stats
        })
    except Exception as e:
        current_app.logger.error(f"Error getting connection stats: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Error getting connection stats: {str(e)}'
        }), 500

@db_monitor_bp.route('/monitor/connection_details', methods=['GET'])
@admin_required
def get_db_connection_details():
    """Get detailed information about database connections.

    Returns:
        JSON response with connection details
    """
    try:
        # Create a database connection
        db_connection = DatabaseConnection()

        # Get central database engine
        central_db = os.getenv('DB_NAME', 'netpipo')
        engine = db_connection._get_engine(central_db)

        # Get connection details
        details = get_connection_details(engine)

        return jsonify({
            'status': 'success',
            'database': central_db,
            'connection_count': len(details),
            'connections': details
        })
    except Exception as e:
        current_app.logger.error(f"Error getting connection details: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Error getting connection details: {str(e)}'
        }), 500

@db_monitor_bp.route('/monitor/connection_limiter', methods=['GET'])
@admin_required
def get_connection_limiter_stats():
    """Get statistics from the connection limiter.

    Returns:
        JSON response with connection limiter statistics
    """
    try:
        # Get connection limiter stats
        limiter_stats = get_limiter_stats()

        # Get database connection stats for comparison
        db_connection = DatabaseConnection()
        central_db = os.getenv('DB_NAME', 'netpipo')
        engine = db_connection._get_engine(central_db)
        db_stats = get_connection_stats(engine)

        return jsonify({
            'status': 'success',
            'limiter_stats': limiter_stats,
            'database_stats': db_stats,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        current_app.logger.error(f"Error getting connection limiter stats: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Error getting connection limiter stats: {str(e)}'
        }), 500

# HTML Routes for Database Monitoring

@db_monitor_bp.route('/monitor/connections/html', methods=['GET'])
@admin_required
def monitor_db_connections_html():
    """Monitor database connections with HTML output.

    Returns:
        HTML page with connection statistics
    """
    try:
        # Create a database connection
        db_connection = DatabaseConnection()

        # Get all database names
        database_names = CompanyHelpers.get_database_names()

        # Monitor central database
        central_db = os.getenv('DB_NAME', 'netpipo')
        results = [monitor_connections(db_connection, central_db)]

        # Monitor company databases (sample of 5 to avoid too many connections)
        sample_size = min(5, len(database_names))
        sample_dbs = database_names[:sample_size]

        for db_name in sample_dbs:
            result = monitor_connections(db_connection, db_name)
            results.append(result)

        return render_template(
            'db_monitor/overview.html',
            active_page='overview',
            results=results,
            central_db=central_db,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    except Exception as e:
        current_app.logger.error(f"Error monitoring database connections: {str(e)}")
        return render_template(
            'db_monitor/overview.html',
            active_page='overview',
            error=str(e),
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

@db_monitor_bp.route('/monitor/connection_stats/html', methods=['GET'])
@admin_required
def get_db_connection_stats_html():
    """Get database connection statistics with HTML output.

    Returns:
        HTML page with connection statistics
    """
    try:
        # Create a database connection
        db_connection = DatabaseConnection()

        # Get central database engine
        central_db = os.getenv('DB_NAME', 'netpipo')
        engine = db_connection._get_engine(central_db)

        # Get connection statistics
        stats = get_connection_stats(engine)

        return render_template(
            'db_monitor/connection_stats.html',
            active_page='stats',
            database=central_db,
            stats=stats,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    except Exception as e:
        current_app.logger.error(f"Error getting connection stats: {str(e)}")
        return render_template(
            'db_monitor/connection_stats.html',
            active_page='stats',
            error=str(e),
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

@db_monitor_bp.route('/monitor/connection_details/html', methods=['GET'])
@admin_required
def get_db_connection_details_html():
    """Get detailed information about database connections with HTML output.

    Returns:
        HTML page with connection details
    """
    try:
        # Create a database connection
        db_connection = DatabaseConnection()

        # Get central database engine
        central_db = os.getenv('DB_NAME', 'netpipo')
        engine = db_connection._get_engine(central_db)

        # Get connection details
        details = get_connection_details(engine)

        # Process the details to ensure all values are properly formatted
        processed_details = []
        for conn in details:
            # Create a new dictionary with processed values
            processed_conn = {}
            # Handle both dictionary-like objects and mapping objects
            try:
                # First try to iterate over items() method
                for key, value in conn.items():
                    # Convert any non-serializable objects to strings
                    if isinstance(value, (datetime.datetime, datetime.date)):
                        processed_conn[key] = value
                    else:
                        processed_conn[key] = value
            except AttributeError:
                # If items() method is not available, try direct attribute access
                # This handles SQLAlchemy Row objects in newer versions
                current_app.logger.info(f"Processing connection details using direct attribute access")
                for key in ['pid', 'datname', 'usename', 'application_name', 'client_addr',
                           'backend_start', 'state', 'state_change', 'query']:
                    try:
                        value = getattr(conn, key, None)
                        if isinstance(value, (datetime.datetime, datetime.date)):
                            processed_conn[key] = value
                        else:
                            processed_conn[key] = value
                    except Exception as attr_error:
                        current_app.logger.warning(f"Error accessing attribute {key}: {str(attr_error)}")
                        processed_conn[key] = None

            processed_details.append(processed_conn)

        return render_template(
            'db_monitor/connection_details.html',
            active_page='details',
            database=central_db,
            connection_count=len(processed_details),
            connections=processed_details,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    except Exception as e:
        current_app.logger.error(f"Error getting connection details: {str(e)}")
        return render_template(
            'db_monitor/connection_details.html',
            active_page='details',
            error=str(e),
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

@db_monitor_bp.route('/monitor/connection_limiter/html', methods=['GET'])
@admin_required
def get_connection_limiter_stats_html():
    """Get statistics from the connection limiter with HTML output.

    Returns:
        HTML page with connection limiter statistics
    """
    try:
        # Get connection limiter stats
        limiter_stats = get_limiter_stats()

        # Ensure all required attributes exist in limiter_stats
        # This prevents template errors if some attributes are missing
        required_attributes = [
            'current_connections', 'max_connections', 'available_connections',
            'usage_percentage', 'total_connections_created', 'total_connections_released',
            'total_wait_time', 'average_wait_time', 'max_wait_time'
        ]

        for attr in required_attributes:
            if attr not in limiter_stats:
                current_app.logger.warning(f"Missing attribute in limiter_stats: {attr}")
                limiter_stats[attr] = 0  # Set default value to prevent template errors

        # Get database connection stats for comparison
        db_connection = DatabaseConnection()
        central_db = os.getenv('DB_NAME', 'netpipo')
        engine = db_connection._get_engine(central_db)
        db_stats = get_connection_stats(engine)

        return render_template(
            'db_monitor/connection_limiter.html',
            active_page='limiter',
            limiter_stats=limiter_stats,
            database_stats=db_stats,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    except Exception as e:
        current_app.logger.error(f"Error getting connection limiter stats: {str(e)}")
        return render_template(
            'db_monitor/connection_limiter.html',
            active_page='limiter',
            error=str(e),
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

@db_monitor_bp.route('/monitor/pgbouncer/html', methods=['GET'])
@admin_required
def get_pgbouncer_stats_html():
    """Get statistics from PgBouncer with HTML output.

    Returns:
        HTML page with PgBouncer statistics
    """
    try:
        # Get PgBouncer stats using psycopg2 directly
        # Use environment variables for all connection parameters
        pgbouncer_port = os.getenv('PGBOUNCER_PORT', os.getenv('DB_PORT', '6432'))
        pgbouncer_user = os.getenv('PGBOUNCER_USER', os.getenv('DB_USER'))
        pgbouncer_password = os.getenv('PGBOUNCER_PASSWORD', os.getenv('DB_PASSWORD', ''))
        pgbouncer_host = os.getenv('PGBOUNCER_HOST', os.getenv('DB_HOST'))
        central_db = os.getenv('DB_NAME', 'netpipo')

        # Log connection attempt details
        current_app.logger.info(f"Attempting to connect to PgBouncer at {pgbouncer_host}:{pgbouncer_port} as {pgbouncer_user}")

        # Connect to PgBouncer using environment variables
        # Connect to PgBouncer's admin database
        # Set autocommit=True to disable transactions, as PgBouncer admin console doesn't support them
        conn = psycopg2.connect(
            host=pgbouncer_host,
            port=pgbouncer_port,
            user=pgbouncer_user,
            password=pgbouncer_password,
            database='pgbouncer',
            autocommit=True,  # This is crucial for PgBouncer admin console
            connect_timeout=5  # Add a timeout to prevent hanging
        )
        current_app.logger.info(f"Successfully connected to PgBouncer at {pgbouncer_host}")

        # Get pools information
        cursor = conn.cursor()

        # Try different PgBouncer commands based on version and configuration
        try:
            # First try the standard SHOW POOLS command
            cursor.execute("SHOW POOLS")
            current_app.logger.info("Successfully executed SHOW POOLS command")
        except psycopg2.Error as cmd_error:
            # If that fails, try with quotes (some PgBouncer versions require this)
            current_app.logger.warning(f"Error executing SHOW POOLS: {str(cmd_error)}. Trying alternative syntax.")
            cursor.execute('SHOW "POOLS"')
            current_app.logger.info("Successfully executed SHOW \"POOLS\" command")

        columns = [desc[0] for desc in cursor.description]

        # Convert rows to dictionaries with proper type handling
        pools = []
        for row in cursor.fetchall():
            pool_dict = {}
            for i, col in enumerate(columns):
                # Convert values to appropriate Python types
                value = row[i]
                if isinstance(value, (bytes, bytearray)):
                    value = value.decode('utf-8')
                pool_dict[col] = value
            pools.append(pool_dict)

        cursor.close()
        conn.close()

        return render_template(
            'db_monitor/pgbouncer_stats.html',
            active_page='pgbouncer',
            pools=pools,
            central_db=central_db,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    except psycopg2.Error as db_error:
        # Handle database-specific errors
        error_message = f"PgBouncer database error: {db_error.pgerror if hasattr(db_error, 'pgerror') else str(db_error)}"
        current_app.logger.error(error_message)

        # Add connection details to help with debugging
        connection_info = f"Connection attempted to: {pgbouncer_host}:{pgbouncer_port} as {pgbouncer_user}"
        current_app.logger.error(f"Connection details: {connection_info}")

        return render_template(
            'db_monitor/pgbouncer_stats.html',
            active_page='pgbouncer',
            error=error_message,
            connection_info=connection_info,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    except Exception as e:
        # Handle other exceptions
        error_message = f"Error getting PgBouncer stats: {str(e)}"
        current_app.logger.error(error_message)

        # Add connection details to help with debugging
        connection_info = f"Connection attempted to: {pgbouncer_host}:{pgbouncer_port} as {pgbouncer_user}"
        current_app.logger.error(f"Connection details: {connection_info}")

        return render_template(
            'db_monitor/pgbouncer_stats.html',
            active_page='pgbouncer',
            error=error_message,
            connection_info=connection_info,
            timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
