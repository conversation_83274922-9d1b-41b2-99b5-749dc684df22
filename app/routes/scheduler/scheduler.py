from flask import Blueprint, current_app , jsonify
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from pytz import timezone
from app.decorators.role_decorator import role_required
from app.helpers.job_manager import JobManager
from dotenv import load_dotenv
import os
import atexit
import fcntl
from app.helpers.auxillary import Auxillary
load_dotenv()

scheduler_bp = Blueprint('scheduler', __name__)

scheduler = BackgroundScheduler(timezone=timezone('Africa/Kigali'))
scheduler_started = False  # Flag to prevent multiple starts                                     
# File lock to prevent multiple instances
scheduler_started = False  # Global flag
scheduler_lock_file = None  # Store the lock file reference to avoid GC

@scheduler_bp.route('/start_scheduler', methods=['GET'])
def start_scheduler():
    """Start the scheduler with file lock to prevent duplication."""
    global scheduler_started
    global scheduler_lock_file

    # Check for lock
    is_locked, lock_file = Auxillary.is_scheduler_locked()
    if is_locked:
        current_app.logger.warning("🚫 Scheduler is already running. Lock file in use.")
        return {"status": "Scheduler already running"}, 400

    scheduler_lock_file = lock_file  # Save the lock handle
    scheduler_started = True
    current_app.logger.info("✅ Scheduler lock acquired. Starting scheduler...")

    if not scheduler.running:
        scheduler.start()

    # Register lock cleanup on exit
    atexit.register(release_scheduler_lock)

    # Add jobs
    if not scheduler.get_job('auto_clockout'):
        scheduler.add_job(
            func=JobManager.auto_clockout_job,
            trigger=IntervalTrigger(minutes=1),
            id='auto_clockout',
            replace_existing=True
        )
        current_app.logger.info("🕐 Auto clockout job scheduled.")

    if not scheduler.get_job('send_previous_day_attendance'):
        scheduler.add_job(
            func=JobManager.send_previous_day_attendance_job,
            trigger='cron',
            hour=1,
            minute=0,
            id='send_previous_day_attendance',
            replace_existing=True
        )
        current_app.logger.info("📤 Send previous day attendance job scheduled.")

    if not scheduler.get_job('send_email_reminder_to_customers'):
        scheduler.add_job(
            func=JobManager.send_email_reminder_to_customers,
            trigger='cron',
            hour=0,
            minute=0,
            id='send_email_reminder_to_customers',
            replace_existing=True
        )
        current_app.logger.info("📧 Send email reminder to customers job scheduled.")

    if not scheduler.get_job('deactivate_expired_contract_employee'):
        # Run at 3:25
        scheduler.add_job(
            func=JobManager.deactivate_expired_contract_employee,
            trigger='cron',
            hour=15,
            minute=45,
            id='deactivate_expired_contract_employee',
            replace_existing=True
        )
        current_app.logger.info("❌ Deactivate expired contract employee job scheduled.")

    return {"status": "Scheduler started successfully"}, 200


def release_scheduler_lock():
    """Releases the file lock and removes the lock file."""
    global scheduler_lock_file
    try:
        if scheduler_lock_file:
            fcntl.flock(scheduler_lock_file, fcntl.LOCK_UN)
            scheduler_lock_file.close()
            os.remove('/tmp/scheduler.lock')
            current_app.logger.info("🔓 Scheduler lock released.")
    except Exception as e:
        current_app.logger.error(f"Error releasing scheduler lock: {e}")

@scheduler_bp.route('/stop_scheduler', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def stop_scheduler():
    """Stop the scheduler."""
    scheduler.shutdown()
    current_app.logger.info("Scheduler stopped.")
    return jsonify({'message': 'Scheduler stopped'}), 200

@scheduler_bp.route('/view_jobs', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def view_jobs():
    """View all jobs."""
    jobs = scheduler.get_jobs()
    job_list = []
    for job in jobs:
        job_list.append({ 'id': job.id, 'next_run_time': job.next_run_time })
    current_app.logger.info("Viewing all jobs.")
    return jsonify({'jobs': job_list}), 200

@scheduler_bp.route('/delete_job/<job_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def delete_job(job_id):
    """Delete a job."""
    try:
        scheduler.remove_job(job_id)
        current_app.logger.info(f"Job with ID: {job_id} deleted.")
        return jsonify({'message': f'Job with ID: {job_id} deleted'}), 200
    except Exception as e:
        current_app.logger.error(f"Error deleting job with ID: {job_id}: {str(e)}")
        return jsonify({'message': f'Error deleting job with ID: {job_id}'}), 400