from flask import Blueprint, flash, jsonify, render_template, redirect
from flask import request, url_for, current_app
from app.decorators.role_decorator import role_required
from app.models.central import ApprovalType
from app.routes.approval_types.forms import ApprovalTypeForm
from app.decorators.admin_decorator import admin_required

approval_types = Blueprint('approval_types', __name__)

@approval_types.route('/add_approval_type', methods=['POST', 'GET'])
@admin_required
def add_approval_type():
    try:
        approval_types = ApprovalType.get_approval_types()
    except Exception as e:
        current_app.logger.error('Error getting approval_types: %s', e)
        approval_types = []
    form = ApprovalTypeForm()
    if request.method == 'POST':
       if not form.validate_on_submit():
           flash('Please check your input', 'danger')
           return redirect(url_for('approval_types.add_approval_type'))
       name = form.name.data
       description = form.description.data

       try:
           result = ApprovalType.add_approval_type(name, description)
           if result:
               flash('Approval Type added successfully', 'success')
               return redirect(url_for('approval_types.add_approval_type'))
           else:
                flash('Approval Type not added', 'danger')
                return redirect(url_for('approval_types.add_approval_type'))
       except Exception as e:
            current_app.logger.error('Error adding Approval Type: %s', e)
            flash('Error adding Approval Type', 'danger')
            return redirect(url_for('approval_types.add_approval_type'))           
        
    return render_template('approval_types/add_approval_type.html', form=form, approval_types=approval_types)

@approval_types.route('/update_approval_type/<id>', methods=['GET', 'POST'])
@admin_required
def update_approval_type(id):
    form = ApprovalTypeForm()
    try:
        approval_type = ApprovalType.get_approval_type_by_id(id)
    except Exception as e:
        current_app.logger.error('Error getting approval_type: %s', e)
        approval_type = []
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Please check your input', 'danger')
            return redirect(url_for('approval_types.add_approval_type'))
        name = form.name.data
        description = form.description.data
        try:
            result = ApprovalType.update_approval_type(id, name, description)
            if result:
                flash('Approval Type updated successfully', 'success')
                return redirect(url_for('approval_types.add_approval_type'))
            else:
                flash('Approval Type not updated', 'danger')
                return redirect(url_for('approval_types.get_approval_type'))
        except Exception as e:
            current_app.logger.error('Error updating Approval Type: %s', e)
            flash('Error updating Approval Type', 'danger')
            return redirect(url_for('approval_types.add_approval_type'))
    try:
        form.name.data = approval_type['approval_type']
        form.description.data = approval_type['description']
    except Exception as e:
        current_app.logger.error('Error setting form data: %s', e)
        flash('Error setting form data', 'danger')
        return redirect(url_for('approval_types.add_approval_type'))
    
    try:
        current_app.logger.info('rendering approval type update template')
        return render_template('approval_types/update_approval_type.html', form=form, approval_type=approval_type)
    except Exception as e:
        current_app.logger.error('Error rendering template: %s', e)
        flash('Error rendering template', 'danger')
        return redirect(url_for('approval_types.add_approval_type'))
    
@approval_types.route('/delete_approval_type/<id>', methods=['GET'])
@admin_required
def delete_approval_type(id):
    try:
        result = ApprovalType.delete_approval_type(id)
        if result:
            flash('Approval Type deleted successfully', 'success')
            return redirect(url_for('approval_types.add_approval_type'))
        else:
            flash('Approval Type not deleted', 'danger')
            return redirect(url_for('approval_types.add_approval_type'))
    except Exception as e:
        current_app.logger.error('Error deleting Approval Type: %s', e)
        flash('Error deleting Approval Type', 'danger')
        return redirect(url_for('approval_types.add_approval_type'))