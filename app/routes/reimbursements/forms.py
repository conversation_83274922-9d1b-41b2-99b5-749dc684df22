from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, DateField, FloatField, SubmitField, HiddenField, TextAreaField
from wtforms.validators import DataRequired
from wtforms.validators import ValidationError
from flask import flash

def validate_amount(form, field):
    """Validate that the amount """
    # Check if the data is a number
    try:
        float(field.data)
    except ValueError:
        flash('Amount must be a number', 'danger')
        raise ValidationError('Amount must be a number')
    # Check if the data is greater than 0
    if field.data <= 0:
        flash('Amount must be greater than 0', 'danger')
        raise ValidationError('Amount must be greater than 0')


class ReimbursementForm(FlaskForm):
    description = TextAreaField('Description', validators=[DataRequired(), validate_amount])
    reimbursement_amount = FloatField('Amount', validators=[DataRequired()])
    reimbursement_date = DateField('Reimbursement Date', validators=[DataRequired()])
    employee_id = HiddenField('Employee ID', validators=[DataRequired()])
    submit = SubmitField('Save and Close')

class ReimbursementUpdateForm(FlaskForm):
    description = TextAreaField('Description', validators=[DataRequired()])
    reimbursement_amount = FloatField('Amount', validators=[DataRequired(), validate_amount])
    reimbursement_date = DateField('Reimbursement Date', validators=[DataRequired()])
    submit = SubmitField('Save and Close')

    