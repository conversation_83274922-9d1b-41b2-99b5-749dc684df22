from flask import Blueprint, request, jsonify, flash, redirect, url_for, session, current_app
from app.models.company import Reimbursements, Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection
from .forms import ReimbursementForm, ReimbursementUpdateForm
from flask import render_template
from app.decorators.hr_decorator import hr_required
from app.helpers.auxillary import Auxillary
from datetime import datetime
from app.decorators.role_decorator import role_required

reimbursements_bp = Blueprint('reimbursements', __name__)

@reimbursements_bp.route('/add_reimbursements', methods=['GET', 'POST'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_reimbursements():
    company_id = session.get('company_id')
    if not company_id:
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    # Get the company database name
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))
    form = ReimbursementForm()
    
    # Initialize the database connection and get employees
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        if not employees:
            message = "No employees found. Please add employees before recording reimbursements."
            flash(message, 'danger')
            current_app.logger.error("No employees found.")
            return jsonify({'success': False, 'message': message}), 400

        if request.method == 'POST':
            data = request.form
            employee_id = data.get('employee_select')
            description = data.get('description')
            reimbursement_amount = data.get('reimbursement_amount')
            reimbursement_date = data.get('reimbursement_date')

            # Check if the reimbursement date is in the future
            if reimbursement_date > datetime.now().strftime('%Y-%m-%d'):
                message = "The reimbursement date cannot be in the future."
                flash(message, 'info')
                current_app.logger.error(message)
                return jsonify({'success': True, 'message': message}), 200
                       
            # Create a new reimbursement record
            try:
                result = Reimbursements.add_reimbursement(db_session, employee_id, description, reimbursement_amount, reimbursement_date)
                message = f"Reimbursement added successfully."
                current_app.logger.info(f"Reimbursement added successfully: {result}")
                flash(message, 'success')
                return jsonify({'success': True, 'message': message}), 200
            
            except Exception as e:
                current_app.logger.error(f"An error occurred while adding reimbursement: {str(e)}")
                message = f'An error occurred while adding reimbursement: {str(e)}', 'danger'
                flash(message)
                return jsonify({'success': False, 'message': message}), 500
    try:       
        return render_template('reimbursements/add_reimbursements.html', form=form, employees=employees)
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering the template: {str(e)}")
        flash('An error occurred while rendering the template. Please try again.', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    
@reimbursements_bp.route('/reimbursements', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def reimbursements():
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        reimbursements = Reimbursements.get_reimbursements(db_session)
        if not reimbursements:
            current_app.logger.error("No reimbursements found")
            #flash('No reimbursements found.', 'danger')
            reimbursements = []
          
        return render_template('reimbursements/reimbursements.html', 
                               reimbursements=reimbursements, 
                               Employee=Employee, db_session=db_session,
                               Auxillary=Auxillary)
    
@reimbursements_bp.route('/update_reimbursement/<uuid:reimbursement_id>', methods=['GET', 'POST'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_reimbursement(reimbursement_id):
    company_id = session.get('company_id')
    if not company_id:
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    # Get the company database name
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        reimbursement_to_update = Reimbursements.get_reimbursement_by_id(db_session, reimbursement_id)
        if reimbursement_to_update is None:
            current_app.logger.error("No reimbursement found")
            flash('No reimbursement found.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        form = ReimbursementUpdateForm(obj=reimbursement_to_update)

        if request.method == 'POST':
            if not form.validate_on_submit():
                current_app.logger.error("Form validation error")
                flash('Form validation error', 'danger')
                return jsonify({'success': False, 'message': 'Form validation error'}), 400
            data = request.form
            description = data.get('description')
            reimbursement_amount = data.get('reimbursement_amount')
            reimbursement_date = data.get('reimbursement_date')
            try:
                updated = Reimbursements.update_reimbursement(
                    db_session, reimbursement_id, description, 
                    reimbursement_amount, reimbursement_date)
                message = f"Reimbursement updated successfully."
                current_app.logger.info(f"Reimbursement updated successfully: {updated}")
                flash(message, 'success')
                return jsonify({'success': True, 'message': message}), 200
            except Exception as e:
                current_app.logger.error(f"An error occurred while updating reimbursement: {str(e)}")
                flash(f'An error occurred while updating reimbursement: {str(e)}', 'danger')
                return jsonify({'success': False, 'message': 'An error occurred. Please try again later.'}), 500
        try:
            current_app.logger.info("Rendering update reimbursement template")
            return render_template('reimbursements/update_reimbursement.html', 
                                   form=form)
        except Exception as e:
            current_app.logger.error(f"An error occurred while rendering the template: {str(e)}")
            flash('An error occurred while rendering the template. Please try again.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
    
@reimbursements_bp.route('/delete_reimbursement/<uuid:reimbursement_id>', methods=['GET'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_reimbursement(reimbursement_id):
    company_id = session.get('company_id')
    if not company_id:
        return jsonify({'success':False, 'message':'An error occurred. Please try again later.'}), 500
    # Get the company database name
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        reimbursement = Reimbursements.get_reimbursement_by_id(db_session, reimbursement_id)
        if reimbursement is None:
            current_app.logger.error("No reimbursement found")
            flash('No reimbursement found.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        try:
            deleted = Reimbursements.delete_reimbursement(db_session, reimbursement_id)
            message = "Reimbursement deleted successfully."
            current_app.logger.info(f"Reimbursement deleted successfully: {deleted}")
            flash(message, 'success')
            return redirect(url_for('reimbursements.reimbursements'))
        except Exception as e:
            current_app.logger.error(f"An error occurred while deleting reimbursement: {str(e)}")
            flash('An error occurred while deleting reimbursement. Please try again.', 'danger')
            return redirect(url_for('reimbursements.reimbursements'))