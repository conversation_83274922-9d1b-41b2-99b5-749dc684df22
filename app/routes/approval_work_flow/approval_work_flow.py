from flask import Blueprint, request, jsonify, current_app, flash, session, render_template
from app.models.company_approval_work_flow import ApprovalWorkflow
from app.models.company import Employee, User
from app.models.central import User as CentralUser, User<PERSON><PERSON> as CentralUserRole, ApprovalType
from app.decorators.role_decorator import role_required
from app.routes.approval_work_flow.forms import CreateWorkflowForm
from app.utils.db_connection import DatabaseConnection

approval_work_flow = Blueprint("approval_work_flow", __name__)

@approval_work_flow.route("/create_workflow", methods=["POST", "GET"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def create_workflow():
    """Create a new workflow."""
    form = CreateWorkflowForm()
    errors_messages = []
    try:
        # Retrieve all roles from the central database
        roles = CentralUserRole.get_user_roles()
        form.role.choices = [(role["role_name"], role["role_name"]) for role in roles]
    except Exception as e:
        current_app.logger.error(f"Error retrieving roles: {e}")
        form.role.choices = []

    try:
        # Retrieve all approval types from the central database
        approval_types = ApprovalType.get_approval_types()
        current_app.logger.info(f"Approval Types: {approval_types}")
        form.approval_type.choices = [(approval_type["approval_type"], approval_type["approval_type"]) for approval_type in approval_types]
        current_app.logger.info(f"Form Approval Type Choices: {form.approval_type.choices}")
    except Exception as e:
        current_app.logger.error(f"Error retrieving approval types: {e}")
        form.approval_type.choices = []

    try:
        # Retrieve all sequence orders
        sequence_orders = [1, 2, 3, 4, 5]
        form.sequence_order.choices = [(sequence_order, sequence_order) for sequence_order in sequence_orders]
        current_app.logger.info(f"Form Sequence Order Choices: {form.sequence_order.choices}")
    except Exception as e:
        current_app.logger.error(f"Error retrieving sequence orders: {e}")
        form.sequence_order.choices = []
    
    
    if request.method == "POST":
        if not form.validate_on_submit():
            if form.errors:
                for field, errors in form.errors.items():
                    for error in errors:
                        message = f"{field}: {error}"
                        errors_messages.append(message)

            flash("Form validation failed.", "danger")
            return jsonify({"success":False,"message": "Form validation failed."}), 400
        
        approval_type = form.approval_type.data
        role = form.role.data
        sequence_order = form.sequence_order.data
        # Get the database name from session
        database_name = session.get("database_name")

        # Create a new database connection
        db_connection = DatabaseConnection()

        # connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                new_workflow = ApprovalWorkflow.create_workflow(db_session, approval_type, role, sequence_order)
                current_app.logger.info(f"New Workflow: {new_workflow}")
                if len(new_workflow) > 0:
                    if "error" in new_workflow:
                        flash("Workflow already exists.", "danger")
                        message = "Workflow already exists."
                        errors_messages.append(message)
                        return jsonify({"success":False,"messages": errors_messages}), 400
                    flash("Workflow created successfully.", "success")
                    return jsonify({"success":True,"message": "Workflow created successfully."}), 200
                else:
                    flash("Error creating workflow.", "danger")
                    return jsonify({"success":False, "message": "Error creating workflow."}), 400
            except Exception as e:
                current_app.logger.error(f"Error creating workflow: {e}")
                flash("Error creating workflow.", "danger")
                return jsonify({"success":False, "message": "Error creating workflow."}), 400
            
    return render_template("approval_work_flow/create_workflow.html", form=form, errors_messages=errors_messages)

from itertools import groupby
from operator import itemgetter

@approval_work_flow.route("/get_approval_workflow", methods=["GET"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_approval_workflow():
    """Get all approval workflows."""

    # Get the database name from session
    database_name = session.get("database_name")

    # Create a new database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Retrieve workflows from the database
            workflows = ApprovalWorkflow.get_all_workflows(db_session)  # Assuming this returns a list of dicts

            # Log the retrieved workflows
            current_app.logger.info(f"Workflows: {workflows}")

            # Group workflows by `approval_type` (for dictionaries, use `itemgetter`)
            grouped_workflows = {
                k: list(v) for k, v in groupby(sorted(workflows, key=itemgetter("approval_type")), key=itemgetter("approval_type"))
            }
            current_app.logger.info(f"Grouped Workflows: {grouped_workflows}")
            return render_template("approval_work_flow/get_approval_workflow.html", grouped_workflows=grouped_workflows)
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflows: {e}")
            flash("Error retrieving workflows.", "danger")
            return jsonify({"success":False, "message": "Error retrieving workflows."}), 400


        

@approval_work_flow.route("/update_workflow/<workflow_id>", methods=["POST", "GET"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_workflow(workflow_id):
    """Update a workflow."""
    form = CreateWorkflowForm()

    try:
        # Retrieve all roles from the central database
        roles = CentralUserRole.get_user_roles()
        current_app.logger.info(f"Roles: {roles}")
        form.role.choices = [(role["role_name"], role["role_name"]) for role in roles]
    except Exception as e:
        current_app.logger.error(f"Error retrieving roles: {e}")
        form.role.choices = []

    try:
        # Retrieve all approval types from the central database
        approval_types = ApprovalType.get_approval_types()
        form.approval_type.choices = [(approval_type["approval_type"], approval_type["approval_type"]) for approval_type in approval_types]
    except Exception as e:
        current_app.logger.error(f"Error retrieving approval types: {e}")
        form.approval_type.choices = []

    try:
        # Retrieve all sequence orders
        sequence_orders = [1, 2, 3, 4, 5]
        form.sequence_order.choices = [(order, str(order)) for order in sequence_orders]
    except Exception as e:
        current_app.logger.error(f"Error retrieving sequence orders: {e}")
        form.sequence_order.choices = []

    # Get the database name from session
    database_name = session.get("database_name")

    # Create a new database connection
    db_connection = DatabaseConnection()

    # connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            workflow = ApprovalWorkflow.get_workflow_by_id(db_session, workflow_id)
            if len(workflow) > 0:
                form.approval_type.data = workflow["approval_type"]
                form.role.data = workflow["role"]
                form.sequence_order.data = workflow["sequence_order"]
            else:
                return jsonify({"success":False, "message": "Error retrieving workflow."}), 400
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflow by id: {e}")
            workflow = []
    if request.method == "POST":
        if not form.validate_on_submit():
            flash("Form validation failed.", "danger")
            form_data = request.form
            current_app.logger.error(f"Form data: {form_data}")
            current_app.logger.error(f"Form validation failed: {form.errors}")
            return jsonify({"success":False, "message": "Form validation failed."}), 400
        submitted_form_data = request.form
        current_app.logger.info(f"Submitted Form Data: {submitted_form_data}")
        approval_type = submitted_form_data.get("approval_type")
        role = submitted_form_data.get("role")
        sequence_order = submitted_form_data.get("sequence_order")

        current_app.logger.info(f"Approval Type: {approval_type}")
        current_app.logger.info(f"Role: {role}")
        current_app.logger.info(f"Sequence Order: {sequence_order}")

        # Create a new database connection
        db_connection = DatabaseConnection()

        # connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                updated_workflow = ApprovalWorkflow.update_workflow(db_session, workflow_id, approval_type, role, sequence_order)
                current_app.logger.info(f"Updated Workflow: {updated_workflow}")
                if len(updated_workflow) > 0:
                    flash("Workflow updated successfully.", "success")
                    return jsonify({"success":True,"message": "Workflow updated successfully."}), 200
                else:
                    flash("Error updating workflow.", "danger")
                    return jsonify({"success":True, "message": "Error updating workflow."}), 400
            except Exception as e:
                current_app.logger.error(f"Error updating workflow: {e}")
                flash("Error updating workflow.", "danger")
                return jsonify({"success":True, "message": "Error updating workflow."}), 400
            
    return render_template("approval_work_flow/update_workflow.html", form=form)

@approval_work_flow.route("/delete_workflow/<workflow_id>", methods=["POST", "GET"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_workflow(workflow_id):
    """Delete a workflow."""
    # Get the database name from session
    database_name = session.get("database_name")

    # Create a new database connection
    db_connection = DatabaseConnection()

    # connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            deleted_workflow = ApprovalWorkflow.delete_workflow(db_session, workflow_id)
            current_app.logger.info(f"Deleted Workflow: {deleted_workflow}")
            if delete_workflow :
                flash("Workflow deleted successfully.", "success")
                return jsonify({"message": "Workflow deleted successfully."}), 200
            else:
                flash("Error deleting workflow.", "danger")
                return jsonify({"message": "Error deleting workflow."}), 400
        except Exception as e:
            current_app.logger.error(f"Error deleting workflow: {e}")
            flash("Error deleting workflow.", "danger")
            return jsonify({"message": "Error deleting workflow."}), 400
        