from flask_wtf import FlaskForm
from wtforms import SelectField, StringField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange

class CreateWorkflowForm(FlaskForm):
    approval_type = SelectField("Approval Type", choices=[], validators=[DataRequired()])
    role = SelectField("Role", choices=[], validators=[DataRequired()])
    sequence_order = SelectField("Approval Order", choices=[], coerce=int, validators=[DataRequired()])
    submit = SubmitField("Submit")