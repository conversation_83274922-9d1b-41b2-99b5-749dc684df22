from flask import Blueprint, request, jsonify
from app.models.central import Subscription
from app import db
from app import current_app

subscription_bp = Blueprint('subscription', __name__)

@subscription_bp.route('/subscribe', methods=['POST', 'GET'])
def subscribe():
    data = request.get_json()
    email = data.get('email')
    
    if not email:
        current_app.logger.error('Email is absent')
        return jsonify({'error': 'Email is required'}), 400
    
    existing_subscription = Subscription.query.filter_by(email=email).first()
    if existing_subscription:
        current_app.logger.info('Email already subscribed')
        return jsonify({'message': 'Email already subscribed'}), 200
    try:
        new_subscription = Subscription(email=email)
        db.session.add(new_subscription)
        db.session.commit()
        
        return jsonify({'message': 'Subscription successful'}), 201
    except Exception as e:
        current_app.logger.error('Error subscribing email')
        return jsonify({'error': 'Error subscribing email'}), 500