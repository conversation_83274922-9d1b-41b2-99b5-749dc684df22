from flask import Blueprint, jsonify, render_template, request, current_app, flash
from app.models.central import Features
from app.decorators.admin_decorator import admin_required
from app.routes.features.forms import FeatureForm
from flask import url_for, redirect

features_bp = Blueprint('features', __name__)

@features_bp.route('/add_feature', methods=['POST', 'GET'])
@admin_required
def add_feature():
    """Route to add new feature."""
    form = FeatureForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            current_app.logger.error('Error adding feature: %s', form.errors)
            return jsonify({'errors': form.errors})
        feature_name = form.feature_name.data
        description = form.description.data

        try:
            # Add new feature to database
            result = Features.add_feature(feature_name, description)
            if not result:
                current_app.logger.error('Error adding feature: %s', form.errors)
                flash('Error adding feature', 'error')
                return redirect(url_for('features.add_feature'))
            else:
                current_app.logger.info('Feature added successfully')
                flash('Feature added successfully', 'success')
                return redirect(url_for('features.add_feature'))
        except Exception as e:
            current_app.logger.error('Error adding feature: %s', str(e))
            flash('Error adding feature', 'error')
            return redirect(url_for('features.add_feature'))
    # Get all features
    try:
        features = Features.get_features()
        current_app.logger.info('Features fetched successfully')
    except Exception as e:
        current_app.logger.error('Error getting features: %s', str(e))
        features = []
    try:
        current_app.logger.info('Rendering add feature template')
        return render_template('features/add_feature.html', form=form, features=features)
    except Exception as e:
        current_app.logger.error('Error rendering add feature template: %s', str(e))

@features_bp.route('/update_feature/<feature_id>', methods=['POST', 'GET'])
@admin_required
def update_feature(feature_id):
    """Route to edit feature."""
    form = FeatureForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            current_app.logger.error('Error editing feature: %s', form.errors)
            return jsonify({'errors': form.errors})
        feature_name = form.feature_name.data
        description = form.description.data

        try:
            # Edit feature in database
            result = Features.update_feature(feature_id, feature_name, description)
            if not result:
                current_app.logger.error('Error editing feature: %s', form.errors)
                current_app.logger.error(f'result: {result}')
                flash('Error editing feature', 'error')
                return redirect(url_for('features.update_feature', feature_id=feature_id))
            else:
                current_app.logger.info('Feature edited successfully')
                flash('Feature updated successfully', 'success')
                return redirect(url_for('features.add_feature', feature_id=feature_id))
        except Exception as e:
            current_app.logger.error('Error editing feature: %s', str(e))
            flash('Error editing feature', 'error')
            return redirect(url_for('features.add_feature', feature_id=feature_id))
    # get feature by id
    try:
        feature = Features.get_feature_by_id(feature_id)
        current_app.logger.info('Feature fetched successfully')
    except Exception as e:
        current_app.logger.error('Error getting feature: %s', str(e))
        feature = []
    form.feature_name.data = feature['feature_name']
    form.description.data = feature['description']

    try:
        current_app.logger.info('Rendering edit feature template')
        return render_template('features/edit_feature.html', form=form)
    except Exception as e:
        current_app.logger.error('Error rendering edit feature template: %s', str(e))

@features_bp.route('/delete_feature/<feature_id>', methods=['POST', 'GET'])
@admin_required
def delete_feature(feature_id):
    """Route to delete feature."""
    try:
        # Delete feature from database
        result = Features.delete_feature(feature_id)
        if not result:
            current_app.logger.error('Error deleting feature')
            flash('Error deleting feature', 'error')
            return redirect(url_for('features.add_feature'))
        else:
            current_app.logger.info('Feature deleted successfully')
            flash('Feature deleted successfully', 'success')
            return redirect(url_for('features.add_feature'))
    except Exception as e:
        current_app.logger.error('Error deleting feature: %s', str(e))
        flash('Error deleting feature', 'error')
        return redirect(url_for('features.add_feature'))
    
