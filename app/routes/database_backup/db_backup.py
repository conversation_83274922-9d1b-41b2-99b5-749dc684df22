from flask import current_app, Blueprint, jsonify, send_file
from app.automations.backup import backup_all_databases
from app.decorators.admin_decorator import admin_required
import os
from app.helpers.auxillary import Auxillary

db_backup = Blueprint('db_backup', __name__)

@db_backup.route('/backup', methods=['GET'])
@admin_required
def get_dump():
    """Endpoint to trigger a database backup and download the zip file."""
    # Trigger the backup process, which returns the path to the zip file
    zip_file_path = backup_all_databases()

    if not zip_file_path or not os.path.exists(zip_file_path):
        return jsonify({"message": "No backup files created or file not found."}), 500

    try:
        # Send the generated zip file as a download
        return send_file(zip_file_path, as_attachment=True)

    except Exception as e:
        current_app.logger.error(f"Error sending the zip file: {e}")
        return jsonify({"message": "Error sending zip file."}), 500
    
@db_backup.route('/send_backup_email', methods=['GET'])
def send_backup_email():
    """Endpoint to send a database backup via email."""
    zip_file_path = backup_all_databases()
    if not zip_file_path or not os.path.exists(zip_file_path):
        return jsonify({"message": "No backup files created or file not found."}), 500

    try:
        # Send the generated zip file as an email attachment
        email = "<EMAIL>"
        subject = "Database Backup"
        message = "Attached is the latest database backup."
        Auxillary.send_netpipo_email_attachment(subject, email, message, zip_file_path)
        os.remove(zip_file_path)
        return jsonify({"message": "Backup email sent successfully."}), 200
    
    except Exception as e:
        current_app.logger.error(f"Error sending backup email: {e}")
        return jsonify({"message": "Error sending backup email."}), 500
    

    