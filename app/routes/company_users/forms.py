from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SubmitField
from wtforms.validators import DataRequired

class AddCompanyUserForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Username', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired()])
    password = StringField('Password', validators=[DataRequired()])
    phone = StringField('Phone', validators=[DataRequired()])
    role = StringField('Role', validators=[DataRequired()])
    submit = SubmitField('Add User')

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('Username', validators=[DataRequired()])
    password = StringField('Password', validators=[DataRequired()])
    company_tin = StringField('Company TIN', validators=[DataRequired()])
    submit = SubmitField('Login')

class ResetPasswordForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired()])
    company_tin = StringField('Company TIN', validators=[DataRequired()])
    submit = SubmitField('Reset Password')