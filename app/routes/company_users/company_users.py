from flask import Blueprint, request, jsonify, render_template, url_for, current_app, abort
from app.models.company import User, Employee
from app.routes.company_users.forms import AddCompanyUserForm, LoginForm, ResetPasswordForm
from app.decorators.hr_decorator import hr_required
from app.utils.db_connection import DatabaseConnection
from flask import session, redirect, flash
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.models.central import UserRole as Role
import uuid
from datetime import datetime
from app.decorators.role_decorator import role_required
from app.models.central import Company

company_users_bp = Blueprint('company_users', __name__)

@company_users_bp.route('/add_company_user', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr'])
def add_company_user():
    """Add a new company user."""
    database_name = session.get('database_name')
    current_app.logger.info(f'Database name: {database_name}')
    try:
        current_app.logger.info('Adding a new company user')
    except Exception as e:
        current_app.logger.error(f'Error creating add company user form: {str(e)}')
        return redirect(url_for('admin_data.dashboard'))
    try:
        # get all roles
        roles = Role.get_user_roles()
        # Filter out the 'admin' role
        roles = [role for role in roles if role['role_name'] not in ['admin', 'hr', 'company_hr', 'accountant', 'manager']]
        current_app.logger.info(f"Roles: {roles}")
    except Exception as e:
        current_app.logger.error(f'Error getting roles: {str(e)}')
        roles = []
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as connected:
        try:
            # get all employees
            employees = Employee.get_employees(connected)
            for employee in employees:
                name = f"{employee['first_name']} {employee['last_name']}"
                current_app.logger.info(f"Name: {name}")
        except Exception as e:
            current_app.logger.error(f'Error getting employees: {str(e)}')
            employees = []

    if request.method == 'POST':
        errors = []
        db_connection = DatabaseConnection()
        database_name = session.get('database_name')
        current_app.logger.info('Inside POST method')
        # Extract the company tin from the database name, which is the first 9 characters
        company_tin = database_name[:9]
        try:
            data = request.form
            employee_id = data.get('employee')
            role = data.get('role')
            current_app.logger.info(f'Role: {role}, Employee ID: {employee_id}')
        except Exception as e:
            current_app.logger.error(f'Error getting form data: {str(e)}')
            return jsonify({'message': 'Error getting form data!'})

        current_app.logger.info('Before Adding a new company user')
        if not employee_id or not role:
            return jsonify({'success': False,'message': 'Invalid data!'}), 400

        try:
            with db_connection.get_session(database_name) as connected:
                try:
                    current_app.logger.info('Adding a new company user')
                    if employee_id:
                        # get all employees and then get the employee by id
                        my_employee = Employee.get_employee_by_their_id(connected, employee_id)
                        last_name = my_employee['last_name']
                        if not last_name:
                            message = 'Employee last name is required! Please edit the employee information and add their last name'
                            errors.append(message)
                        email = my_employee['email']
                        # Validate the email and make sure email is not None
                        # Also check if the email is not empty
                        if not email:
                            message = 'Employee email is required! Please edit the employee information and add their email'
                            errors.append(message)

                        phone = my_employee['phone']
                        if not phone:
                            message = 'Employee phone is required! Please edit the employee information and add their phone number'
                            errors.append(message)
                        # generate a random part of the username
                        random_part = str(uuid.uuid4())[:5]
                        username = f"{last_name.lower()}{random_part}"

                        if errors:
                            return jsonify({'success': False, 'messages': errors}), 400

                        #Generate a random password
                        password = Auxillary.random_password()
                        current_app.logger.info(f"password: {password}")
                        current_app.logger.info(f'Username: {username}, Password: {password}')
                        current_app.logger.info(f'Email: {email}, Phone: {phone}')
                        # send the username and password to the employee's email
                        recipients = email
                        login_route = url_for('company_users.login_company_users')
                        url = request.url_root
                        login_path = f"{url}{login_route}"
                        body = f"""
                        Dear {last_name}, your username is: {username}
                        and your password is: {password}. Please login to the system
                        with this link: {login_path} with the given credentials.

                        You will also need the company TIN to login.
                        Your company TIN is: {company_tin}. Thank you.
                        """
                    # add a company user
                    result = User.add_user(
                        connected, username, email, password, role, phone, employee_id)
                    try:
                        subject = "Account Setup"
                        sent = Auxillary.send_netpipo_email(subject, recipients, body)
                        current_app.logger.info("Email sent")
                        current_app.logger.info(f"Sent: {sent}")
                    except Exception as e:
                        current_app.logger.error(f"Error sending email: {str(e)}")
                        return jsonify({'success':False,'message': 'Error sending email!'}), 500
                    current_app.logger.info(f'User added: {result}')
                    flash('User added successfully!', 'success')
                    return jsonify({'success':True,'message': 'User added successfully!'}), 200
                except Exception as e:
                    current_app.logger.error(f'Error adding user: {str(e)}')
                    flash('Error adding user!', 'danger')
                    return jsonify({'success':False,'message': 'Error adding user!'}), 500
        except Exception as e:
            current_app.logger.error(f'Error adding user: {str(e)}')
            flash('Error adding user!', 'danger')
            return jsonify({'success':False,'message': 'Error adding user!'}), 500
    try:
        current_app.logger.info('Rendering add company user page')
        return render_template(
            'company/add_company_user.html',
            roles=roles, employees=employees)
    except Exception as e:
        current_app.logger.error(f'Error rendering add company user page: {str(e)}')
        return redirect(url_for('admin_data.dashboard'))

@company_users_bp.route('/login_company_users', methods=['GET', 'POST'])
def login_company_users():
    """Login company users."""
    form = LoginForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            return jsonify({'message': 'Invalid data!'}), 400
        try:
            username = form.username.data
            password = form.password.data
            company_tin = form.company_tin.data

            # Remove the trailing and leading whitespaces
            username = username.strip()
            password = password.strip()
            company_tin = company_tin.strip()
        except Exception as e:
            current_app.logger.error(f'Error getting form data: {str(e)}')
            message = 'Error getting form data'
            flash(message, 'danger')

        try:
            # Get company details given the tin
            company = Company.get_company_by_tin(company_tin)
            if not company:
                flash('Please enter a valid company TIN', 'danger')
                return redirect(url_for('company_users.login_company_users'))
            current_app.logger.info(f'Company details: {company}')
            if not company:
                message = 'Company does not exist!'
                flash(message, 'danger')
                return redirect(url_for('company_users.login_company_users'))
            session['company_id'] = company['company_id']
            session['company_name'] = company['company_name']
            session['database_name'] = company['database_name']
            session['company_plan_id'] = company['plan_id']
            session['company_plan'] = company['plan'].plan_name
            session['company_logo'] = company.get('logo')
            database_name = company['database_name']

        except Exception as e:
            current_app.logger.error(f'Error getting company details: {str(e)}')
            message = 'Something went wrong, try again'
            flash(message, 'danger')
            return redirect(url_for('company_users.login_company_users'))
        db_connection = DatabaseConnection()
        # Save the important data in session
        session['database_name'] = database_name
        session['company_tin'] = company_tin
        session['username'] = username
        try:
            with db_connection.get_session(database_name) as connected:
                try:
                    # Get user by username
                    user = User.get_user_by_username(connected, username)
                    current_app.logger.info(f'User: {user}')
                    role = user['role']
                    user_id = user['user_id']
                    current_app.logger.info(f'Role: {role}')
                    employee_id = user['employee_id']

                    # save the important info in session
                    session['employee_id'] = employee_id
                    session['role'] = role
                    session['user_id'] = user_id
                    session['user'] = user
                    full_name = user['full_name']
                    session['full_name'] = full_name
                    first_name = user['first_name']
                    last_name = user['last_name']
                    session['first_name'] = first_name
                    session['last_name'] = last_name

                     # Set the session as permanent
                    session.permanent = True
                    # Initialize the last_activity timestamp
                    session['last_activity'] = datetime.now().isoformat()
                    if not user:
                        pass
                except Exception as e:
                    current_app.logger.error(f'Error getting user: {str(e)}')

                try:
                    # Login a company user
                    result = User.login_user(connected, username, password)
                    current_app.logger.info(f'User logged in: {result}')
                except Exception as e:
                    current_app.logger.error(f'Error logging in user: {str(e)}')
                if result:
                    if role == 'supervisor':
                        role = session['role']
                        return redirect(url_for('admin_data.supervisor_dashboard'))
                    elif role == 'employee':
                        role = session['role']
                        return redirect(url_for('admin_data.employee_dashboard'))
                    else:
                        return jsonify({'message': 'The role is not defined!'}), 400
                message = 'Invalid login credentials!'
                flash(message, 'danger')
        except Exception as e:
            current_app.logger.error(f'Error logging in user: {str(e)}')
            message = 'Some thing went wrong, try again'
            flash(message, 'danger')
            return redirect(url_for('company_users.login_company_users'))
    try:
        current_app.logger.info('Rendering login company users page')
        return render_template('company/login_company_users.html', form=form)
    except Exception as e:
        current_app.logger.error(f'Error rendering login company users page: {str(e)}')
        message = 'Some thing went wrong, try again'
        flash(message, 'danger')
@company_users_bp.route('/logout_company_users', methods=['GET'])
def logout_company_users():
    """Logout company users."""
    session.clear()
    message = 'You have been logged out'
    flash(message, 'success')
    return redirect(url_for('company_users.login_company_users'))

@company_users_bp.route('/company_users', methods=['GET'])
@role_required(['hr', 'company_hr'])
def company_users():
    """Get all company users."""
    db_connection = DatabaseConnection()
    try:
        database_name = session.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from session: {str(e)}')
        return jsonify({'message': 'Error fetching database name!'}), 500

    try:
        with db_connection.get_session(database_name) as connected:
            try:
                # Get all company users
                users = User.get_users(connected)
                current_app.logger.info('users fetched')
                return render_template('company/company_users.html', users=users)
            except Exception as e:
                current_app.logger.error(f'Error getting users: {str(e)}')
                return jsonify({'message': 'Error getting users!'}), 500
    except Exception as e:
        current_app.logger.error(f'Error getting users: {str(e)}')
        return jsonify({'message': 'Error getting users!'}), 500

@company_users_bp.route('/delete_company_user/<user_id>', methods=['GET'])
@role_required(['hr', 'company_hr'])
def delete_company_user(user_id):
    """Delete a company user."""
    db_connection = DatabaseConnection()
    try:
        database_name = session.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from session: {str(e)}')
        return jsonify({'message': 'Error fetching database name!'}), 500

    try:
        with db_connection.get_session(database_name) as connected:
            try:
                # Delete a company user
                result = User.delete_user(connected, user_id)
                current_app.logger.info(f'User deleted: {result}')
                flash('User deleted successfully!', 'success')
                return jsonify({'success': True, 'message': 'User deleted successfully!'}), 200
            except Exception as e:
                current_app.logger.error(f'Error deleting user: {str(e)}')
                return jsonify({'message': 'Error deleting user!'}), 500
    except Exception as e:
        current_app.logger.error(f'Error deleting user: {str(e)}')
        return jsonify({'message': 'Error deleting user!'}), 500

@company_users_bp.route('/reset_user_password', methods=['POST', 'GET'])
def reset_user_password():
    """Reset password for a company user."""
    title='Reset Password'
    form = ResetPasswordForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            return jsonify({'message': 'Invalid data!'}), 400
        try:
            email = form.email.data
            company_tin = form.company_tin.data
        except Exception as e:
            current_app.logger.error(f'Error getting form data: {str(e)}')
            return jsonify({'message': 'Error getting form data!'})

        db_connection = DatabaseConnection()
        try:
            # get the database_name given the company_tin
            database_name = CompanyHelpers.get_database_name_by_tin(company_tin)
            current_app.logger.info(f'Database name: {database_name}')
            if not database_name:
                return jsonify({'message': 'Company does not exist!'}), 400
        except Exception as e:
            current_app.logger.error(f'Error getting database name: {str(e)}')
            return jsonify({'message': 'Error getting database name!'}), 500

        try:
            with db_connection.get_session(database_name) as connected:
                try:
                    # Reset password for a company user
                    user = User.get_user_by_email(connected, email)
                    current_app.logger.info(f'User: {user}')
                    if not user:
                        return jsonify({'message': 'User does not exist!'}), 400
                    temporary_password = Auxillary.random_password()
                    current_app.logger.info(f'Temporary password: {temporary_password}')
                    updated = user.reset_password(connected, email, temporary_password)
                    current_app.logger.info(f'Password reset: {updated}')
                    if updated:
                        recipients = email
                        body = f"Your password has been reset. Your new password is: {temporary_password}"
                        try:
                            Auxillary.send_netpipo_email("Password Reset", recipients, body)
                            flash("Password reset successful. You new password has been sent to your email.", 'success')
                            current_app.logger.info("Password reset successful.")
                            return redirect(url_for('company_users.login_company_users'))
                        except Exception as e:
                            flash("An error occurred", 'danger')
                            current_app.logger.error(f"Error sending email: {str(e)}")
                            return redirect(url_for('company_users.login_company_users'))

                    return jsonify({'message': 'Error resetting password!'}), 500
                except Exception as e:
                    current_app.logger.error(f'Error resetting password: {str(e)}')
                    return jsonify({'message': 'Error resetting password!'}), 500

        except Exception as e:
            current_app.logger.error(f'Error resetting password: {str(e)}')
            return jsonify({'message': 'Error resetting password!'}), 500
    try:
        current_app.logger.info('Rendering reset password page')
        return render_template('company/reset_password.html', form=form, title=title)
    except Exception as e:
        current_app.logger.error(f'Error rendering reset password page: {str(e)}')
        return jsonify({'message': 'Error rendering reset password page!'}), 500