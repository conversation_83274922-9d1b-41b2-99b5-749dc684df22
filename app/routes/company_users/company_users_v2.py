from flask import Blueprint, request, jsonify, render_template, url_for, current_app, abort
from app.models.company import User, Employee
from app.routes.company_users.forms import AddCompanyUserForm, LoginForm, ResetPasswordForm
from app.decorators.hr_decorator import hr_required
from app.utils.db_connection import DatabaseConnection
from flask import session, redirect, flash
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.models.central import UserRole as Role, User as central_user
import uuid
from datetime import datetime
from app.decorators.role_decorator import role_required
from app.models.central import Company

company_users_bp_v2 = Blueprint('company_users_v2', __name__)

@company_users_bp_v2.route('/v2/add_company_user', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr'])
def add_company_user():
    """Add a new company user."""
    database_name = session.get('database_name')
    current_app.logger.info(f'Database name: {database_name}')
    try:
        current_app.logger.info('Adding a new company user')
    except Exception as e:
        current_app.logger.error(f'Error creating add company user form: {str(e)}')
        return redirect(url_for('admin_data.dashboard'))
    try:
        # get all roles
        roles = Role.get_user_roles()
        # Filter out the 'admin' role
        roles = [role for role in roles if role['role_name'] not in ['admin', 'hr', 'company_hr', 'accountant', 'manager']]
        current_app.logger.info(f"Roles: {roles}")
    except Exception as e:
        current_app.logger.error(f'Error getting roles: {str(e)}')
        roles = []
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as connected:
        try:
            # get all employees
            employees = Employee.get_employees(connected)
            for employee in employees:
                name = f"{employee['first_name']} {employee['last_name']}"
                current_app.logger.info(f"Name: {name}")
        except Exception as e:
            current_app.logger.error(f'Error getting employees: {str(e)}')
            employees = []

    if request.method == 'POST':
        errors = []
        db_connection = DatabaseConnection()
        database_name = session.get('database_name')
        current_app.logger.info('Inside POST method')
        # Extract the company tin from the database name, which is the first 9 characters
        company_tin = database_name[:9]
        try:
            data = request.form
            employee_id = data.get('employee')
            role = data.get('role')
            current_app.logger.info(f'Role: {role}, Employee ID: {employee_id}')
        except Exception as e:
            current_app.logger.error(f'Error getting form data: {str(e)}')
            flash('Error getting form data!', 'danger')
            return redirect(url_for('company_users_v2.add_company_user'))

        current_app.logger.info('Before Adding a new company user')
        if not employee_id or not role:
            flash('Invalid data!', 'danger')
            return redirect(url_for('company_users_v2.add_company_user'))

        try:
            with db_connection.get_session(database_name) as connected:
                try:
                    current_app.logger.info('Adding a new company user')
                    if employee_id:
                        # get all employees and then get the employee by id
                        my_employee = Employee.get_employee_by_their_id(connected, employee_id)
                        last_name = my_employee['last_name']
                        if not last_name:
                            message = 'Employee last name is required! Please edit the employee information and add their last name'
                            errors.append(message)
                        email = my_employee['email']
                        # Validate the email and make sure email is not None
                        if not email:
                            message = 'Employee email is required! Please edit the employee information and add their email'
                            errors.append(message)

                        phone = my_employee['phone']
                        if not phone:
                            message = 'Employee phone is required! Please edit the employee information and add their phone number'
                            errors.append(message)
                        # generate a random part of the username
                        random_part = str(uuid.uuid4())[:5]
                        username = f"{last_name.lower()}{random_part}"

                        if errors:
                            flash('Please correct the errors in the form', 'danger')
                            for error in errors:
                                flash(error, 'danger')
                            return redirect(url_for('company_users_v2.add_company_user'))

                        #Generate a random password
                        password = Auxillary.random_password()
                        current_app.logger.info(f"password: {password}")
                        current_app.logger.info(f'Username: {username}, Password: {password}')
                        # send the username and password to the employee's email
                        recipients = email
                        login_route = url_for('company_users.login_company_users')
                        url = request.url_root
                        login_path = f"{url}{login_route}"
                        body = f"""
                        Dear {last_name}, your username is: {username}
                        and your password is: {password}. Please login to the system
                        with this link: {login_path} with the given credentials.

                        You will also need the company TIN to login.
                        Your company TIN is: {company_tin}. Thank you.
                        """
                    # add a company user
                    result = User.add_user(
                        connected, username, email, password, role, phone, employee_id)
                    try:
                        subject = "Account Setup"
                        sent = Auxillary.send_netpipo_email(subject, email, body)
                        #current_app.logger.info("Email sent")
                        current_app.logger.info(f"Sent: {sent}")
                    except Exception as e:
                        current_app.logger.error(f"Error sending email: {str(e)}")
                        return redirect(url_for('company_users_v2.add_company_user'))
                    current_app.logger.info(f'User added: {result}')
                    flash('User added successfully!', 'success')
                    return redirect(url_for('company_users_v2.company_users'))
                except Exception as e:
                    current_app.logger.error(f'Error adding user: {str(e)}')
                    flash('Error adding user!', 'danger')
                    return redirect(url_for('company_users_v2.add_company_user'))
        except Exception as e:
            current_app.logger.error(f'Error adding user: {str(e)}')
            flash('Error adding user!', 'danger')
            return redirect(url_for('company_users_v2.add_company_user'))
    try:
        current_app.logger.info('Rendering add company user page')
        return render_template(
            'company/add_company_user_v2.html',
            roles=roles, employees=employees)
    except Exception as e:
        current_app.logger.error(f'Error rendering add company user page: {str(e)}')
        return redirect(url_for('admin_data.dashboard'))

@company_users_bp_v2.route('/v2/company_users', methods=['GET'])
@role_required(['hr', 'company_hr'])
def company_users():
    """Get all company users."""
    db_connection = DatabaseConnection()
    try:
        database_name = session.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from session: {str(e)}')
        flash('Company users could not be retrieved', 'danger')
        return redirect(url_for('admin_data.dashboard'))

    try:
        with db_connection.get_session(database_name) as connected:
            try:
                # Get all company users
                users = User.get_users(connected)
                current_app.logger.info('users fetched')
                return render_template('company/company_users_v2.html', users=users)
            except Exception as e:
                current_app.logger.error(f'Error getting users: {str(e)}')
                flash('Company users could not be retrieved', 'danger')
                return redirect(url_for('admin_data.dashboard'))
    except Exception as e:
        current_app.logger.error(f'Error getting users: {str(e)}')
        return jsonify({'message': 'Error getting users!'}), 500

@company_users_bp_v2.route('/v2/delete_company_user/<user_id>', methods=['GET'])
@role_required(['hr', 'company_hr'])
def delete_company_user(user_id):
    """Delete a company user."""
    db_connection = DatabaseConnection()
    try:
        database_name = session.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from session: {str(e)}')
        return redirect(url_for('admin_data.dashboard'))

    try:
        with db_connection.get_session(database_name) as connected:
            try:
                # Delete a company user
                result = User.delete_user(connected, user_id)
                current_app.logger.info(f'User deleted: {result}')
                flash('User deleted successfully!', 'success')
                return redirect(url_for('company_users_v2.company_users'))
            except Exception as e:
                current_app.logger.error(f'Error deleting user: {str(e)}')
                flash('Error deleting user!', 'danger')
                return redirect(url_for('company_users_v2.company_users'))
    except Exception as e:
        current_app.logger.error(f'Error deleting user: {str(e)}')
        return redirect(url_for('company_users_v2.company_users'))
