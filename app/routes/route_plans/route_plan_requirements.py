from flask import Blueprint, request
from flask import render_template, session, jsonify, url_for, redirect, flash, current_app
from app.models.central import RoutePlanRequirement, Plans
from app.decorators.admin_decorator import admin_required
from app.routes.route_plans.forms import AddRoutePlanRequirement

route_plan_requirements = Blueprint('route_plan_requirements', __name__)

@route_plan_requirements.route('/add_route_plan_requirement', methods=['POST', 'GET'])
@admin_required
def add_route_plan_requirement():
    """Add a route plan requirement"""
    form = AddRoutePlanRequirement()

    try:
        # Get plans for form choices and plans display
        plans = Plans.get_plans()
        form.required_plan_id.choices = [(plan['plan_id'], plan['plan_name']) for plan in plans]
    except Exception as e:
        current_app.logger.error(f"Error getting plans: {e}")
        flash('Error getting plans', 'danger')
        plans = []
        form.required_plan_id.choices = []

    # Retrieve and organize route plans by plan name
    try:
        route_plans = RoutePlanRequirement.get_route_plan_requirements()

        organized_route_plans = {}
        for route in route_plans:
            plan_name = route['Plan_name'].strip()  # Remove any extra spaces
            route_path = route['route_path']
            description = route['description']  # Retrieve description
            
            # Initialize the plan in the dictionary if it doesn't exist
            if plan_name not in organized_route_plans:
                organized_route_plans[plan_name] = []

            # Append the route information (path and description) to the list of routes for the plan
            organized_route_plans[plan_name].append({
                'route_path': route_path,
                'description': description
            })

    except Exception as e:
        current_app.logger.error(f"Error getting route plans: {e}")
        flash('Error retrieving route plans', 'danger')
        organized_route_plans = {}

    # Handle form submission
    if request.method == 'POST':
        if not form.validate_on_submit():
            # Flash form errors
            errors = form.errors
            flash(f"Error adding route plan requirement: {errors}", 'danger')
            return redirect(url_for('route_plan_requirements.add_route_plan_requirement'))

        route_path = form.route_path.data
        required_plan_id = form.required_plan_id.data
        description = form.description.data
        try:
            # Attempt to add route plan requirement
            result = RoutePlanRequirement.add_route_plan_requirement(route_path, required_plan_id, description)
            if result:
                flash('Route path requirement added successfully', 'success')
                return redirect(url_for('route_plan_requirements.add_route_plan_requirement'))
            else:
                flash('Route path requirement not added', 'danger')
            return redirect(url_for('route_plan_requirements.add_route_plan_requirement'))
        except Exception as e:
            current_app.logger.error(f"Error adding route plan requirement: {e}")
            flash("Error adding route plan requirement", 'danger')
            return redirect(url_for('route_plan_requirements.add_route_plan_requirement'))
    return render_template('route_plans/add_route_plan_requirement.html', form=form,
                           organized_route_plans=organized_route_plans, plans=plans)
