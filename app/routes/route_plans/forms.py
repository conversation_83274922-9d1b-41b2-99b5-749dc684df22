from wtforms import StringField, SelectField, SubmitField, validators, TextAreaField
from flask_wtf import FlaskForm


class AddRoutePlanRequirement(FlaskForm):
    route_path = StringField('Route Path',[validators.DataRequired()])
    required_plan_id = SelectField('Required Plan', choices=[])
    description = TextAreaField('Description', validators=[validators.Optional()])
    submit = SubmitField('Submit')