from flask import Blueprint, render_template, redirect, url_for, flash
from app.routes.user_roles.forms import UserRoleForm
from app.models.central import UserRole
from app.decorators.admin_decorator import admin_required
from flask import current_app

user_role_bp = Blueprint('user_role', __name__)

@user_role_bp.route('/insert_user_role', methods=['GET', 'POST'])
@admin_required
def insert_user_role():
    """Insert a user role into the database."""
    form = UserRoleForm()
    if form.validate_on_submit():
        role_name = form.role_name.data.lower()
        try:
            user_role = UserRole(role_name=role_name)
        except Exception as e:
            flash("Error creating user role object.", "error")
            return render_template('user_roles/insert_user_role.html', form=form)
        try:
            print("Inserting user role")
            if user_role.insert_user_role():
                print("Inserted user role")
                flash("User role inserted successfully.", "success")
                return redirect(url_for('user_role.insert_user_role'))
        except Exception as e:
            flash("Error inserting user role.", "error")
            print(f"Error inserting user role: {str(e)}")
            return render_template('user_roles/insert_user_role.html', form=form)
    try:
        user_roles = UserRole.query.all()
        current_app.logger.info("User roles: ", user_roles)
        return render_template('user_roles/insert_user_role.html', form=form, user_roles=user_roles)
    except Exception as e:
        flash("Error fetching user roles.", "error")
        current_app.logger.error(f"Error fetching user roles: {str(e)}")
        return redirect(url_for('admin_data.admin_dashboard'))

@user_role_bp.route('/update_user_role/<uuid:id>', methods=['GET', 'POST'])
@admin_required
def update_user_role(id):
    """Update a user role."""
    user_role = UserRole.query.get_or_404(id)
    form = UserRoleForm(obj=user_role)
    print("User role before update: ", user_role.role_name)
    print("Form role_name: ", form.role_name.data)
    if form.validate_on_submit():
        user_role.role_name = form.role_name.data.lower()
        print("Role name after form submit: ", user_role.role_name)
        try:
            if user_role.update_user_role(id, user_role.role_name):
                flash("User role updated successfully.", "success")
                return redirect(url_for('user_role.insert_user_role'))
        except Exception as e:
            print(f"Error: {e}")
            flash("Error updating user role.", "error")
            return render_template('user_roles/update_user_role.html', form=form, user_role=user_role)
    
    return render_template('user_roles/update_user_role.html', form=form, user_role=user_role)

@user_role_bp.route('/delete_user_role/<uuid:id>', methods=['GET', 'POST'])
@admin_required
def delete_user_role(id):
    """Delete a user role."""
    user_role = UserRole.query.get_or_404(id)
    try:
        if user_role.delete_user_role(id):
            flash("User role deleted successfully.", "success")
            return redirect(url_for('user_role.insert_user_role'))
    except Exception as e:
        flash("Error deleting user role.", "error")
        return redirect(url_for('user_role.insert_user_role'))
