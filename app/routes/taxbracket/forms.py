from flask_wtf import FlaskForm
from wtforms import DecimalField, SubmitField
from wtforms.validators import NumberRange, ValidationError

def validate_rate(form, field):
    if field.data is None:
        raise ValidationError('Field is required')
    if field.data < 0 or field.data > 100:
        raise ValidationError('Rate must be between 0 and 100')

class TaxBracketForm(FlaskForm):
    lower_bound = DecimalField('Lower Bound', default=0.0)
    upper_bound = DecimalField('Upper Bound', validators=[NumberRange(min=0)])
    rate = DecimalField('Tax Rate', validators=[validate_rate])
    submit = SubmitField('Submit')
