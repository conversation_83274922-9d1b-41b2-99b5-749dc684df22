from flask import current_app, Blueprint, request, jsonify, render_template, flash, redirect, url_for, session
from app.routes.taxbracket.forms import TaxBracketForm
from app.models.central import ConsultantTaxBracket
import uuid
from app.decorators.role_decorator import role_required

consultant_bp = Blueprint('consultant', __name__)

@consultant_bp.route('/add_consultant_tax_bracket', methods=['GET', 'POST'])
@role_required('admin')
def add_consultant_tax_bracket():
    """Add a tax bracket for consultants."""
    form = TaxBracketForm()
    if form.validate_on_submit():
        lower_bound = form.lower_bound.data
        print("Lower bound: ", lower_bound)
        upper_bound = form.upper_bound.data
        print("Upper bound: ", upper_bound)
        tax_rate = form.rate.data / 100
        print("Tax rate: ", tax_rate)
        try:
            tax_bracket = ConsultantTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=tax_rate)
        except Exception as e:
            flash("Error creating tax bracket object.", "error")
            return redirect(url_for('consultant.add_consultant_tax_bracket'))
        try:
            print("Adding consultant tax bracket")
            if tax_bracket.add_consultant_taxbracket():
                print("Added consultant tax bracket")
                flash("Tax bracket added successfully.", "success")
                return redirect(url_for('consultant.add_consultant_tax_bracket'))
        except Exception as e:
            message = f"An error occurred: {str(e)}"
            flash(message, 'danger')
            return render_template('taxbrackets/add_consultant_tax_bracket.html', form=form)
    try:
        tax_brackets = ConsultantTaxBracket.query.all()
        print("Viewing consultant tax brackets")
        print("Tax brackets: ", tax_brackets)
        return render_template('taxbrackets/add_consultant_tax_bracket.html', form=form, tax_brackets=tax_brackets)
    except Exception as e:
        print("Error retrieving tax brackets", str(e))
        flash("Error retrieving tax brackets.", "error")
        return render_template('taxbrackets/add_consultant_tax_bracket.html', form=form)
    
from uuid import UUID

@consultant_bp.route('/update_consultant_tax_bracket/<uuid:id>', methods=['GET', 'POST'])
@role_required('admin')
def update_consultant_tax_bracket(id):
    tax_bracket = ConsultantTaxBracket.query.get_or_404(id)
    form = TaxBracketForm(obj=tax_bracket)
    

    if form.validate_on_submit():
        lower_bound = form.lower_bound.data
        upper_bound = form.upper_bound.data
        rate = form.rate.data / 100

        if ConsultantTaxBracket.update_consultant_taxbracket(id, lower_bound, upper_bound, rate):
            flash("Tax bracket updated successfully.", "success")
            return redirect(url_for('consultant.add_consultant_tax_bracket'))
        else:
            flash("Error updating tax bracket.", "danger")
            
    # Convert rate to percentage for display
    form.rate.data = tax_bracket.rate * 100

    return render_template('taxbrackets/update_consultant_tax_bracket.html', form=form, tax_bracket=tax_bracket)
