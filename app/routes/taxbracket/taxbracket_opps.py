# routes.py

from flask import current_app, Blueprint, request, jsonify, render_template, flash, redirect, url_for, session
from app.routes.taxbracket.forms import TaxBracketForm
from app.models.central import TaxBracket
import logging

taxbracket_bp = Blueprint('taxbracket', __name__)

@taxbracket_bp.route('/insert_tax_bracket', methods=['GET', 'POST'])
def insert_tax_bracket():
    """Insert a tax bracket into the database."""
    form = TaxBracketForm()
    if form.validate_on_submit():
        lower_bound = form.lower_bound.data
        upper_bound = form.upper_bound.data
        tax_rate = form.rate.data
        tax_rate = tax_rate / 100
        try:
            tax_bracket = TaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=tax_rate)
        except Exception as e:
            flash("Error creating tax bracket object.", "error")
            return render_template('taxbrackets/insert_tax_bracket.html', form=form)
        try:
            if tax_bracket.insert_taxbracket():
                flash("Tax bracket inserted successfully.", "success")
                return redirect(url_for('taxbracket.insert_tax_bracket'))
        except Exception as e:
            flash("Error inserting tax bracket.", "error")
            logging.error(f"Error inserting tax bracket: {str(e)}")
            return redirect(url_for('taxbracket.insert_tax_bracket'))
    try:
        tax_brackets = TaxBracket.query.all()
    except Exception as e:
        flash(f"Error retrieving tax brackets.", "error: {str(e)}")
        return render_template('taxbrackets/insert_tax_bracket.html', form=form)
    return render_template('taxbrackets/insert_tax_bracket.html', form=form, tax_brackets=tax_brackets)
    
@taxbracket_bp.route('/update_permanent_employee_tax_bracket/<uuid:id>', methods=['GET', 'POST'])
def update_permanent_employee_tax_bracket(id):
    tax_bracket = TaxBracket.query.get_or_404(id)
    form = TaxBracketForm(obj=tax_bracket)
    
    if form.validate_on_submit():
        lower_bound = form.lower_bound.data
        upper_bound = form.upper_bound.data
        rate = form.rate.data / 100

        if TaxBracket.update_permanent_employee_taxbracket(id, lower_bound, upper_bound, rate):
            flash("Tax bracket updated successfully.", "success")
            return redirect(url_for('taxbracket.insert_tax_bracket'))
        else:
            flash("Error updating tax bracket.", "error")
            return render_template('taxbrackets/insert_tax_bracket.html', form=form)
    # Convert rate to percentage for display
    form.rate.data = tax_bracket.rate * 100
    return render_template('taxbrackets/update_permanent_employee_tax_bracket.html', form=form, tax_bracket=tax_bracket)
