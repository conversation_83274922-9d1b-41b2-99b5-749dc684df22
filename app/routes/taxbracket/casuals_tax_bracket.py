from flask import current_app, Blueprint, request, jsonify, render_template, flash, redirect, url_for, session
from app.routes.taxbracket.forms import TaxBracketForm
from app.models.central import CasualsTaxBracket
from app.decorators.role_decorator import role_required

casuals_bp = Blueprint('casuals', __name__)

@casuals_bp.route('/insert_casuals_tax_bracket', methods=['GET', 'POST'])
@role_required(['admin'])
def insert_casuals_tax_bracket():
    """Insert a tax bracket into the database."""
    form = TaxBracketForm()
    if form.validate_on_submit():
        lower_bound = form.lower_bound.data
        upper_bound = form.upper_bound.data
        tax_rate = form.rate.data
        try:
            tax_bracket = CasualsTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=tax_rate)
        except Exception as e:
            flash("Error creating tax bracket object.", "error")
            return render_template('taxbrackets/insert_casuals_tax_bracket.html', form=form)
        try:
            print("Inserting casuals tax bracket")
            if tax_bracket.insert_casuals_taxbracket():
                print("Inserted casuals tax bracket")
                flash("Tax bracket inserted successfully.", "success")
                return redirect(url_for('casuals.insert_casuals_tax_bracket'))
        except Exception as e:
            flash("Error inserting tax bracket.", "error")
            return render_template('taxbrackets/insert_casuals_tax_bracket.html', form=form)
    try:
        tax_brackets = CasualsTaxBracket.query.all()
        print("Viewing casuals tax brackets")
        print("Tax brackets: ", tax_brackets)
        return render_template('taxbrackets/insert_casuals_tax_bracket.html', form=form, tax_brackets=tax_brackets)
    except Exception as e:
        print("Error retrieving tax brackets", str(e))
        flash("Error retrieving tax brackets.", "error")
        return render_template('taxbrackets/insert_casuals_tax_bracket.html', form=form)
    
@casuals_bp.route('/update_casuals_tax_bracket/<uuid:id>', methods=['GET', 'POST'])
@role_required(['admin'])
def update_casuals_tax_bracket(id):
    tax_bracket = CasualsTaxBracket.query.get_or_404(id)
    form = TaxBracketForm(obj=tax_bracket)
    
    if form.validate_on_submit():
        lower_bound = form.lower_bound.data
        upper_bound = form.upper_bound.data
        rate = form.rate.data / 100

        if CasualsTaxBracket.update_casuals_taxbracket(id, lower_bound, upper_bound, rate):
            flash("Tax bracket updated successfully.", "success")
            return redirect(url_for('casuals.insert_casuals_tax_bracket'))
        else:
            flash("Error updating tax bracket.", "error")
            return render_template('taxbrackets/insert_casuals_tax_bracket.html', form=form)
    # Convert rate to percentage for display
    form.rate.data = tax_bracket.rate * 100
    return render_template('taxbrackets/update_casuals_tax_bracket.html', form=form, tax_bracket=tax_bracket)