from flask import Blueprint, request, jsonify, render_template, current_app, flash
import requests
import base64
import json
import base64
from dotenv import load_dotenv
import os
from app.routes.attendance.forms import SubjectForm, UpdateLeaveManagementForm, AttendanceForm, RecordsForm, LeaveManagementForm, TimesheetForm
from app.decorators.hr_decorator import hr_required
from app.decorators.role_decorator import role_required
from flask import session, redirect, url_for
from app.helpers.company_helpers import CompanyHelpers
from concurrent.futures import ThreadPoolExecutor
from app.utils.db_connection import DatabaseConnection
from app.models.company import Attendance, Employee, Site, Deductions, NsfContributions, Insurance, User
from datetime import datetime
from calendar import monthrange
from datetime import  timedelta
from decimal import Decimal
import os
from io import BytesIO
from flask import send_file, abort
from app.routes.attendance.forms import AttendanceStatusForm
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
from app.models.central import RoutePlanRequirement, Company
from app.routes.payroll.goal_Seek_mine import SalaryCalculatorGross
from app.helpers.auxillary import Auxillary

executor = ThreadPoolExecutor(max_workers=5)

def make_api_call(url, headers, files):
    """Make a POST request to the Face API."""
    return requests.post(url, headers=headers, files=files)

load_dotenv()

attendance_bp = Blueprint('attendance', __name__)

MICROSERVICE_URL = os.getenv('MICROSERVICE_URL')
MICROSERVICE_KEY = os.getenv('MICROSERVICE_KEY')

BASE_URL = os.getenv('BASE_URL')

@attendance_bp.route('/add_attendance', methods=['GET', 'POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def add_attendance():
    """Render the template on GET and process the image on POST."""
    if request.method == 'GET':
        return render_template('attendance/add_attendance.html')  # Ensure the template exists

    elif request.method == 'POST':

        print("MICROSERVICE_URL: ", MICROSERVICE_URL)
        print("MICROSERVICE_KEY: ", MICROSERVICE_KEY)
        # Receive the uploaded image from frontend
        employee_image = request.files.get('image')
        print("employee_image: ", employee_image)  # Debugging to see the FileStorage object

        if employee_image:
            # Load the reference image (could come from a database, or other storage)
            target_image_path = "/home/<USER>/acr_hrms/app/data/known_faces/rugema.jpeg"  # Example image for recognition

            # Prepare the form-data for the API request
            files = {
                'source_image': ('employee_image.jpg', employee_image.stream, 'image/jpeg'),  # Send as tuple with filename and MIME type
                'target_image': ('target_image.jpg', open(target_image_path, 'rb'), 'image/jpeg')  # Target image for comparison
            }
            headers = {
                'x-api-key': MICROSERVICE_KEY
            }

            # Make the POST request to CompreFace API
            response = requests.post(MICROSERVICE_URL, headers=headers, files=files)
            current_app.logger.info(f"Response status: {response.status_code}")
            current_app.logger.info(f"Response: {response.json()}")
            # Get the message from the response
            message = response.json().get('message')
            current_app.logger.info(f"Message: {message}")

            # Check for response status
            if response.status_code == 200:
                # Parse and return the API response
                result = response.json()
                current_app.logger.info(f"Response: {result}")
                # Retrieve the similarity probability
                try:
                    similarity = None
                    if 'result' in result and len(result['result']) > 0:
                        face_matches = result['result'][0].get('face_matches', [])
                        if len(face_matches) > 0:
                            similarity = face_matches[0].get('similarity')
                except Exception as e:
                    current_app.logger.error(f"Error parsing response: {e}")
                    similarity = 0.0

                # Check if  The similarity is greater than 0.89
                if similarity > 0.89:
                    current_app.logger.info(f"Match found with similarity: {similarity}")
                    return jsonify({"result": "Attendance Verified"})
                else:
                    return jsonify({"result": "No match found. Please try again with a better image."})
            elif message :
                return jsonify({"error": message}), 400
            else:
                return jsonify({"error": "Attendance API Error.", "status_code": response.status_code}), 500
        else:
            return jsonify({"error": "No image uploaded"}), 400

@attendance_bp.route('/clockin', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr', 'employee'])
def clockin():
    """Clock in an employee using facial recognition."""
    if request.method == 'GET':
        try:
            # Log session information for debugging
            current_app.logger.info(f"Session data: {dict(session)}")

            form = AttendanceForm()
            company_id = session.get('company_id')
            current_app.logger.info(f"Company ID from session: {company_id}")

            MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
            current_app.logger.info(f"MICROSERVICE_KEY: {MICROSERVICE_KEY}")
        except Exception as e:
            current_app.logger.error(f"Error initializing clockin route: {e}")
            import traceback
            current_app.logger.error(traceback.format_exc())
            flash(f"Error initializing clockin: {str(e)}", "danger")
            return redirect(url_for('dashboard.dashboard'))

        current_app.logger.info(f"Company ID: {company_id}")

        database_name = session.get('database_name')
        attendance_service = session.get('attendance_service')

        # Check if API KEY is None and redirect to dashboard
        if MICROSERVICE_KEY is None:
            message = f"""
            You are not registered to use this service.
            Please contact <NAME_EMAIL> to be regsitered.
            """
            flash(message, 'danger')
            current_app.logger.error(f"Error: {message}")
            return redirect(url_for('dashboard.dashboard'))

        return render_template('attendance/clockin.html', form=form)

@attendance_bp.route('/api/clockin', methods=['POST'])
def api_clockin():
    """API endpoint for clock in an employee using facial recognition."""
    try:
        # Get session data from request
        session_data = request.form.get('session_data')
        if session_data:
            session_data = json.loads(session_data)
            current_app.logger.info(f"API Clockin - Session data from request: {session_data}")

            # Extract necessary session data
            company_id = session_data.get('company_id')
            database_name = session_data.get('database_name')
            employee_id = session_data.get('user_id')

            current_app.logger.info(f"API Clockin - Company ID: {company_id}, Database: {database_name}, Employee ID: {employee_id}")
        else:
            # Fall back to session if no session data in request
            company_id = session.get('company_id')
            database_name = session.get('database_name')
            employee_id = session.get('user_id')
            current_app.logger.info(f"API Clockin - Using session data: Company ID: {company_id}, Database: {database_name}, Employee ID: {employee_id}")

        # Get the API KEY
        MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
        current_app.logger.info(f"API Clockin - MICROSERVICE_KEY: {MICROSERVICE_KEY}")

        # Check if API KEY is None
        if MICROSERVICE_KEY is None:
            message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
            current_app.logger.error(f"API Clockin - Error: {message}")
            return jsonify({"error": message}), 401

        # Process the uploaded image
        if 'image' not in request.files:
            current_app.logger.error("API Clockin - No image part in the request")
            return jsonify({"error": "No image uploaded"}), 400

        image_file = request.files['image']
        if image_file.filename == '':
            current_app.logger.error("API Clockin - No image selected")
            return jsonify({"error": "No image selected"}), 400

        # Get location data
        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        location = f"{latitude}, {longitude}"
        current_app.logger.info(f"API Clockin - Location: {location}")

        # Get device info
        device_used = request.form.get('device_used')
        if device_used is None:
            device_used = "Web Browser"
            current_app.logger.warning(f"API Clockin - device_used was None, setting default value: {device_used}")

        # Connect to the database
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            # Verify the employee's face
            BASE_URL = os.getenv('COMPREFACE_URL')
            recognition_url = f"{BASE_URL}/api/v1/recognition/recognize"

            headers = {
                'x-api-key': MICROSERVICE_KEY
            }

            try:
                # Convert the image to bytes
                image_bytes = image_file.read()

                # Create a multipart form data payload
                files = {
                    'file': ('image.jpg', image_bytes, 'image/jpeg')
                }

                # Make the API request
                response = requests.post(recognition_url, headers=headers, files=files)
                response.raise_for_status()

                # Parse the response
                result = response.json()
                current_app.logger.info(f"API Clockin - Recognition result: {result}")

                # Check if any faces were detected
                if 'result' in result and len(result['result']) > 0:
                    # Get the first face with the highest similarity
                    faces = result['result']
                    if len(faces) > 0:
                        face = faces[0]
                        subjects = face.get('subjects', [])

                        if len(subjects) > 0:
                            # Get the subject with the highest similarity
                            subject = max(subjects, key=lambda x: x.get('similarity', 0))
                            similarity = subject.get('similarity', 0)
                            subject_id = subject.get('subject', '')

                            current_app.logger.info(f"API Clockin - Subject ID: {subject_id}, Similarity: {similarity}")

                            # Check if the similarity is above the threshold
                            if similarity >= 0.85:  # 85% similarity threshold
                                # Get the employee ID from the subject ID
                                employee_id = subject_id

                                # Check if the employee is allowed to clock in at this location
                                location_name = "Unknown"
                                try:
                                    # Reverse geocode the location
                                    location_name = Attendance.reverse_geocode(latitude, longitude)
                                    current_app.logger.info(f"API Clockin - Location name: {location_name}")
                                except Exception as e:
                                    current_app.logger.error(f"API Clockin - Error reverse geocoding: {e}")

                                # Record the attendance
                                try:
                                    # Convert the location as string
                                    location = str(location)

                                    # Log all parameters for debugging
                                    current_app.logger.info(f"API Clockin - Parameters: employee_id={employee_id}, location={location}, device_used={device_used}, location_name={location_name}")

                                    result = Attendance.clockin(db_session, employee_id, location, device_used, location_name)
                                    current_app.logger.info(f"API Clockin - Attendance recorded: {result}")

                                    if "already" in result:
                                        return jsonify(result), 400
                                    return jsonify(result), 200
                                except Exception as e:
                                    import traceback
                                    current_app.logger.error(f"API Clockin - Error recording attendance: {e}")
                                    current_app.logger.error(traceback.format_exc())
                                    return jsonify({"error": f"Error recording attendance: {str(e)}"}), 500
                            else:
                                message = f"Face similarity too low: {similarity}"
                                current_app.logger.error(f"API Clockin - {message}")
                                return jsonify({"error": message}), 400
                        else:
                            message = "No matching subjects found"
                            current_app.logger.error(f"API Clockin - {message}")
                            return jsonify({"error": message}), 400
                    else:
                        message = "No faces detected in the image"
                        current_app.logger.error(f"API Clockin - {message}")
                        return jsonify({"error": message}), 400
                else:
                    message = "No faces detected in the image"
                    current_app.logger.error(f"API Clockin - {message}")
                    return jsonify({"error": message}), 400
            except requests.exceptions.RequestException as e:
                current_app.logger.error(f"API Clockin - Error making recognition request: {e}")
                return jsonify({"error": f"Error making recognition request: {str(e)}"}), 500
    except Exception as e:
        import traceback
        current_app.logger.error(f"API Clockin - Unexpected error: {e}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": f"Unexpected error: {str(e)}"}), 500
        current_app.logger.info(f"Inside POST method and data posted is: ")
        employee_image = request.files.get('image')
        longitude = request.form.get('longitude', "unknown")
        latitude = request.form.get('latitude', "unknown")
        current_app.logger.info(f"Location: {longitude}, {latitude}")

        if not employee_image:
            return jsonify({"error": "No image uploaded"}), 400

        # Prepare API request to CompreFace
        files = {'file': ('employee_image.jpg', employee_image.stream, 'image/jpeg')}
        headers = {'x-api-key': MICROSERVICE_KEY}

        response = requests.post(MICROSERVICE_URL, headers=headers, files=files)
        message = response.json().get('message', '')
        current_app.logger.info(f"Response status: {response.status_code}")
        current_app.logger.info(f"Message: {message}")

        if response.status_code != 200:
            return jsonify({"error": message or "Attendance API Error.", "status_code": response.status_code}), 500

        try:
            result = response.json()
            subject = result['result'][0]['subjects'][0]['subject']
            similarity = round(result['result'][0]['subjects'][0]['similarity'] * 100, 1)
        except Exception as e:
            current_app.logger.error(f"Error parsing response: {e}")
            return jsonify({"error": "Invalid response from facial recognition service."}), 400

        if similarity <= 89:
            return jsonify({"result": "No match found. Please try again with a better image."}), 400

        employee_id = subject[:36]  # Extract Employee UUID
        subject = subject[36:]

        if longitude in ["unknown", "", None] or latitude in ["unknown", "", None]:
            return jsonify({"error": "Location not found. Please enable location services and try again."}), 400

        location = f"{longitude}, {latitude}"
        device_used = request.headers.get('User-Agent', "Unknown")
        role = session.get('role')
        session_employee_id = str(session.get('employee_id', ''))

        if role == 'employee' and session_employee_id != employee_id:
            return jsonify({"error": "You have not been authorized to clock in for another employee."}), 400

        db_connection = DatabaseConnection()
        with db_connection.get_session(session.get('database_name')) as db_session:
            try:
                locations = CompanyHelpers.allowed_clockin_locations(db_session, company_id) or []
            except Exception as e:
                current_app.logger.error(f"Error getting allowed locations: {e}")
                locations = []

            if locations is None or len(locations) == 0:
                 # Notify the company hr
                message = CompanyHelpers.notify_hr_if_no_locations(company_id)
                return jsonify({"error": message}), 400

            within_perimeter = False
            min_distance = float("inf")

            for location in locations:
                company_latitude = location.get('latitude')
                company_longitude = location.get('longitude')

                perimeter = Attendance.is_within_perimeter(latitude, longitude, company_latitude, company_longitude)

                if perimeter[1]:  # If inside an allowed location
                    within_perimeter = True
                    current_app.logger.info(f"Clock-in valid. Distance: {perimeter[0]:.2f} meters")
                    break
                min_distance = min(min_distance, perimeter[0])
            try:
                clockin_location = Attendance.reverse_geocode(latitude, longitude)
                current_app.logger.info(f"Clockin location: {clockin_location}")
                location_name = clockin_location.get('display_name', 'Unknown location')
            except Exception as e:
                current_app.logger.error(f"Error getting clock-in location: {e}")
                location_name = "Unknown location"

            if not within_perimeter and role == 'employee':

                try:
                    employee = Employee.get_employee_by_id(db_session, employee_id)
                    employee_name = employee.get('full_name')
                except Exception as e:
                    employee_name = 'unknown'
                    current_app.logger.error(f"Error getting employee name: {e}")
                now = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
                formatted_distance = "{:.2f}".format(min_distance)

                # Send HR email alert
                subject = "Employee clock-in outside perimeter"
                message = f"{employee_name} tried to clock in at {location_name} at {now}. Distance from company: {formatted_distance} meters."
                email = "<EMAIL>"  # Replace with HR contact

                try:
                    sent = Auxillary.send_netpipo_email(subject, email, message)
                except Exception as e:
                    current_app.logger.error(f"Error sending email: {e}")
                return jsonify({"error": "You are not allowed to clock in at this location"}), 400

            try:
                # Convert the location as string
                location = str(location)

                # Log all parameters for debugging
                current_app.logger.info(f"Clockin parameters: employee_id={employee_id}, location={location}, device_used={device_used}, location_name={location_name}")

                # Check if device_used is None and set a default value
                if device_used is None:
                    device_used = "Web Browser"
                    current_app.logger.warning(f"device_used was None, setting default value: {device_used}")

                result = Attendance.clockin(db_session, employee_id, location, device_used, location_name)
                current_app.logger.info(f"Attendance recorded: {result}")

                if "already" in result:
                    return jsonify(result), 400
                return jsonify(result), 200
            except Exception as e:
                import traceback
                current_app.logger.error(f"Error recording attendance: {e}")
                current_app.logger.error(traceback.format_exc())
                return jsonify({"error": f"Error recording attendance: {str(e)}"}), 500

@attendance_bp.route('/clockout', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr', 'employee'])
def clockout():
    """Clock out an employee using facial recognition."""
    try:
        # Log session information for debugging
        current_app.logger.info(f"Clockout - Session data: {dict(session)}")

        # Get the API KEY from CompanyHelpers
        MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
        current_app.logger.info(f"Clockout - MICROSERVICE_KEY: {MICROSERVICE_KEY}")

        company_id = session.get('company_id')
        current_app.logger.info(f"Clockout - Company ID from session: {company_id}")

        # Check if API KEY is None and redirect to dashboard
        if MICROSERVICE_KEY is None:
            message = f"""
            You are not registered to use this service.
            Please contact <NAME_EMAIL> to be regsitered.
            """
            flash(message, 'danger')
            current_app.logger.error(f"Clockout - Error: {message}")
            return redirect(url_for('dashboard.dashboard'))

        return render_template('attendance/clockout.html')
    except Exception as e:
        current_app.logger.error(f"Error initializing clockout route: {e}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        flash(f"Error initializing clockout: {str(e)}", "danger")
        return redirect(url_for('dashboard.dashboard'))

@attendance_bp.route('/api/clockout', methods=['POST'])
def api_clockout():
    """API endpoint for clock out an employee using facial recognition."""
    try:
        # Get session data from request
        session_data = request.form.get('session_data')
        if session_data:
            session_data = json.loads(session_data)
            current_app.logger.info(f"API Clockout - Session data from request: {session_data}")

            # Extract necessary session data
            company_id = session_data.get('company_id')
            database_name = session_data.get('database_name')
            employee_id = session_data.get('user_id')

            current_app.logger.info(f"API Clockout - Company ID: {company_id}, Database: {database_name}, Employee ID: {employee_id}")
        else:
            # Fall back to session if no session data in request
            company_id = session.get('company_id')
            database_name = session.get('database_name')
            employee_id = session.get('user_id')
            current_app.logger.info(f"API Clockout - Using session data: Company ID: {company_id}, Database: {database_name}, Employee ID: {employee_id}")

        # Get the API KEY
        MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
        current_app.logger.info(f"API Clockout - MICROSERVICE_KEY: {MICROSERVICE_KEY}")

        # Check if API KEY is None
        if MICROSERVICE_KEY is None:
            message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
            current_app.logger.error(f"API Clockout - Error: {message}")
            return jsonify({"error": message}), 401

        # Process the uploaded image
        if 'image' not in request.files:
            current_app.logger.error("API Clockout - No image part in the request")
            return jsonify({"error": "No image uploaded"}), 400

        image_file = request.files['image']
        if image_file.filename == '':
            current_app.logger.error("API Clockout - No image selected")
            return jsonify({"error": "No image selected"}), 400

        # Get location data
        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        location = f"{latitude}, {longitude}"
        current_app.logger.info(f"API Clockout - Location: {location}")

        # Connect to the database
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            # Verify the employee's face
            BASE_URL = os.getenv('COMPREFACE_URL')
            recognition_url = f"{BASE_URL}/api/v1/recognition/recognize"

            headers = {
                'x-api-key': MICROSERVICE_KEY
            }

            try:
                # Convert the image to bytes
                image_bytes = image_file.read()

                # Create a multipart form data payload
                files = {
                    'file': ('image.jpg', image_bytes, 'image/jpeg')
                }

                # Make the API request
                response = requests.post(recognition_url, headers=headers, files=files)
                response.raise_for_status()

                # Parse the response
                result = response.json()
                current_app.logger.info(f"API Clockout - Recognition result: {result}")

                # Check if any faces were detected
                if 'result' in result and len(result['result']) > 0:
                    # Get the first face with the highest similarity
                    faces = result['result']
                    if len(faces) > 0:
                        face = faces[0]
                        subjects = face.get('subjects', [])

                        if len(subjects) > 0:
                            # Get the subject with the highest similarity
                            subject = max(subjects, key=lambda x: x.get('similarity', 0))
                            similarity = subject.get('similarity', 0)
                            subject_id = subject.get('subject', '')

                            current_app.logger.info(f"API Clockout - Subject ID: {subject_id}, Similarity: {similarity}")

                            # Check if the similarity is above the threshold
                            if similarity >= 0.85:  # 85% similarity threshold
                                # Get the employee ID from the subject ID
                                employee_id = subject_id

                                # Check if the employee is allowed to clock out at this location
                                location_name = "Unknown"
                                try:
                                    # Reverse geocode the location
                                    location_name = Attendance.reverse_geocode(latitude, longitude)
                                    current_app.logger.info(f"API Clockout - Location name: {location_name}")
                                except Exception as e:
                                    current_app.logger.error(f"API Clockout - Error reverse geocoding: {e}")

                                # Record the attendance
                                try:
                                    # Log all parameters for debugging
                                    current_app.logger.info(f"API Clockout - Parameters: employee_id={employee_id}, location={location}, location_name={location_name}")

                                    result = Attendance.clockout(db_session, employee_id, str(location), location_name)
                                    current_app.logger.info(f"API Clockout - Attendance recorded: {result}")

                                    if "you have not" in result:
                                        return jsonify(result), 401
                                    return jsonify(result), 200
                                except Exception as e:
                                    import traceback
                                    current_app.logger.error(f"API Clockout - Error recording attendance: {e}")
                                    current_app.logger.error(traceback.format_exc())
                                    return jsonify({"error": f"Error recording attendance: {str(e)}"}), 500
                            else:
                                message = f"Face similarity too low: {similarity}"
                                current_app.logger.error(f"API Clockout - {message}")
                                return jsonify({"error": message}), 400
                        else:
                            message = "No matching subjects found"
                            current_app.logger.error(f"API Clockout - {message}")
                            return jsonify({"error": message}), 400
                    else:
                        message = "No faces detected in the image"
                        current_app.logger.error(f"API Clockout - {message}")
                        return jsonify({"error": message}), 400
                else:
                    message = "No faces detected in the image"
                    current_app.logger.error(f"API Clockout - {message}")
                    return jsonify({"error": message}), 400
            except requests.exceptions.RequestException as e:
                current_app.logger.error(f"API Clockout - Error making recognition request: {e}")
                return jsonify({"error": f"Error making recognition request: {str(e)}"}), 500
    except Exception as e:
        import traceback
        current_app.logger.error(f"API Clockout - Unexpected error: {e}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": f"Unexpected error: {str(e)}"}), 500
    if request.method == 'GET':
        # Check if API KEY is None and redirect to dashboard
        if MICROSERVICE_KEY is None:
            message = f"""
            You are not registered to use this service.
            Please contact <NAME_EMAIL> to be regsitered.
            """
            flash(message, 'danger')
            current_app.logger.error(f"Error: {message}")
            return jsonify({"error": message}), 401

        return render_template('attendance/clockout.html')
    elif request.method == 'POST':
        # Receive the uploaded image from frontend
        employee_image = request.files.get('image')
        try:
            longitude = request.form.get('longitude')
            latitude = request.form.get('latitude')
            current_app.logger.info(f"Location: {longitude}, {latitude}")
            device_info = request.form.get('device_info')
            if not device_info:
                device_info = "Unknown device"
            current_app.logger.info(f"Device info: {device_info}")
        except Exception as e:
            current_app.logger.error(f"Error getting location: {e}")
            longitude = "unknown"
            latitude = "unknown"

        if employee_image:
            # Prepare the form-data for the API request
            files = {
                'file': ('employee_image.jpg', employee_image.stream, 'image/jpeg')
            }
            headers = {
                'x-api-key': MICROSERVICE_KEY
            }

            # Make the POST request to CompreFace API
            response = requests.post(MICROSERVICE_URL, headers=headers, files=files)
            current_app.logger.info(f"Response status: {response.status_code}")

            # Get the message from the response
            message = response.json().get('message')
            current_app.logger.info(f"Message: {message}")

            # Check for response status
            if response.status_code == 200:
                # Parse and return the API response
                result = response.json()
                current_app.logger.info(f"Response: {result}")

                # Accessing the subject and similarity values
                try:
                    subject = result['result'][0]['subjects'][0]['subject']
                    similarity = result['result'][0]['subjects'][0]['similarity']
                    # Multiply the similarity by 100 to get percentage and format to 1 decimal places
                    similarity = round(similarity * 100, 1)

                    print(f"Subject: {subject}, Similarity: {similarity}")
                except Exception as e:
                    current_app.logger.error(f"Error parsing response: {e}")
                    subject = None
                    similarity = None

                # Check if the similarity is greater than 0.92
                if similarity > 92:
                    similarity = f"{similarity}%"
                    current_app.logger.info(f"Match found with similarity: {similarity}")
                    # Extract the Employee_id from the subject which is a uuid that is the first 36 characters
                    employee_id = subject[:36]

                    # Now the subject is the rest of the string after the first 36 characters
                    subject = subject[36:]

                    # Get the database name from the session
                    database_name = session.get('database_name')

                    # Get the location and device used by detecting geo-location and device used
                    if longitude == "unknown" or latitude == "unknown" or longitude == "" or latitude == "" or longitude is None or latitude is None:
                        return jsonify({"error": "Location not found. Please enable location services and try again."}), 400
                    # Get the location from the longitude and latitude
                    location = f"{longitude}, {latitude}"

                    try:
                        device_used = request.headers.get('User-Agent')
                        print("Device used: ", device_used)
                    except Exception as e:
                        current_app.logger.error(f"Error getting device: {e}")
                        device_used = "Unknown"

                    #Check if the role if employee
                    role = session.get('role')
                    if role == 'employee':
                        session_employee_id = session.get('employee_id')
                        current_app.logger.info(f"session employee_id: {session.get('employee_id')}")
                        current_app.logger.info(f"employee_id from subject: {employee_id}")
                        current_app.logger.info(f"Type of session employee_id: {type(session.get('employee_id'))}")
                        current_app.logger.info(f"Type of employee_id from subject: {type(employee_id)}")
                        # convert  session.get('employee_id') to a string
                        try:
                            session_employee_id = str(session_employee_id)
                            current_app.logger.info(f"Type of session employee_id: {type(session_employee_id)}")
                        except Exception as e:
                            current_app.logger.error(f"Error converting session employee_id to string: {e}")
                            return jsonify({"error": "Error converting session employee_id to string"}), 400

                        try:
                            current_app.logger.info(f"the length of session employee_id: {len(session_employee_id)} while the length of employee_id from subject is {len(employee_id)}")
                        except Exception as e:
                            current_app.logger.error(f"Error getting the length of employee_id: {e}")
                            return jsonify({"error": "Error getting the length of employee_id"}), 400
                        check = (session_employee_id == employee_id)
                        current_app.logger.info(f"Check: {check}")
                        if not check:
                            return jsonify({"error": "You have not been authorized to clockout for another employee"}), 400

                    # Create a DatabaseConnection object
                    db_connection = DatabaseConnection()
                    # Connect to the database and record the attendance of the employee
                    with db_connection.get_session(database_name) as db_session:
                        try:
                            # Get the allowed clockout locations
                            locations = CompanyHelpers.allowed_clockin_locations(db_session, company_id) or []
                            current_app.logger.info(f"Allowed locations: {locations}")
                        except Exception as e:
                            current_app.logger.error(f"Error getting allowed locations: {e}")
                            locations = []

                        # check if the locations is none or an empty list
                        if locations is None or len(locations) == 0:
                            # Notify the company hr
                            message = CompanyHelpers.notify_hr_if_no_locations(company_id)
                            current_app.logger.info(f"Message from the response: {message}")
                            return jsonify({"error": message}), 400

                        current_app.logger.info(f"Allowed locations: {locations}")
                        within_perimeter = False
                        min_distance = float("inf")

                        for location in locations:
                            company_latitude = location.get('latitude')
                            company_longitude = location.get('longitude')
                            current_app.logger.info(f"Company latitude: {company_latitude}, Company longitude: {company_longitude}")

                            perimeter = Attendance.is_within_perimeter(latitude, longitude, company_latitude, company_longitude)

                            if perimeter[1]:
                                within_perimeter = True
                                current_app.logger.info(f"Clock-out valid. Distance: {perimeter[0]:.2f} meters")
                                break

                            min_distance = min(min_distance, perimeter[0])

                        try:
                            # send an email to the hr if the employee is not within the perimeter
                            subject = "Employee clock Out outside perimeter"
                            try:
                                clockin_location = Attendance.reverse_geocode(latitude, longitude)
                                current_app.logger.info(f"Clockin location: {clockin_location}")
                                location_name = clockin_location.get('display_name')
                                current_app.logger.info(f"Location name: {location_name}")
                            except Exception as e:
                                current_app.logger.error(f"Error getting clockin location: {e}")
                                clockin_location = "Unknown location"
                                location_name = "Unknown location"
                            # Get employee name
                            employee = Employee.get_employee_by_id(db_session, employee_id)
                            employee_name = employee.get('full_name')
                            # Get the time of now
                            now = datetime.now()
                            # convert the time in a readable format dd/mm/yyyy hh:mm:ss
                            now = now.strftime("%d/%m/%Y %H:%M:%S")
                            formatted_perimeter = "{:.2f}".format(perimeter[0])
                            message = f"{employee_name} tried to clockout at this location: {location_name} at {now}. The distance from your company is: {formatted_perimeter} Meters."
                            email = "<EMAIL>"
                            if not within_perimeter and role == 'employee':
                                try:
                                    sent = Auxillary.send_netpipo_email(subject, email, message)
                                    current_app.logger.info(f"Email sent: {sent}")

                                except Exception as e:
                                    current_app.logger.error(f"Error sending email: {e}")
                                message = f"You are not allowed to clockout at this location"
                                return jsonify(message), 400
                        except Exception as e:
                            current_app.logger.info(f"error getting perimeter: {str(e)}")
                        try:
                            # Log all parameters for debugging
                            current_app.logger.info(f"Clockout parameters: employee_id={employee_id}, location={location}, location_name={location_name}")

                            result = Attendance.clockout(
                                db_session, employee_id, str(location), location_name)
                            current_app.logger.info(f"Attendance recorded: {result}")
                        except Exception as e:
                            import traceback
                            current_app.logger.error(f"Error recording attendance: {e}")
                            current_app.logger.error(traceback.format_exc())
                            return jsonify({"error": f"Error recording attendance: {str(e)}"}), 500
                    if "you have not" in result:
                        return jsonify(result), 401
                    # Return the result of the attendance
                    return jsonify(result)
                else:
                    return jsonify({"result": "No match found. Please try again with a better image."}), 400
            elif message:
                return jsonify({"error": message}), 400
            else:
                return jsonify({"error": "Attendance API Error.", "status_code": response.status_code}), 500
        else:
            return jsonify({"error": "No image uploaded"}), 400

@attendance_bp.route('/create_subject', methods=['GET', 'POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def create_subject():
    role = session.get('role')
    current_app.logger.info(f"Role: {role}")
    """Create a subject in FaceCollection for an employee and add their photo"""
    form = SubjectForm()
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if request.method == 'GET':
        if MICROSERVICE_KEY is None:
            message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
            flash(message, 'danger')
            current_app.logger.error(f"Error: {message}")
            return jsonify({"success":False,"error": message}), 401
        current_app.logger.info(f"MICROSERVICE_KEY: {MICROSERVICE_KEY}")
        try:
            current_app.logger.info("Rendering create subject template")
            return render_template('attendance/upload2.html', form=form, role=role)
        except Exception as e:
            current_app.logger.error(f"Error rendering create subject template: {e}")
            return jsonify({"error": "Error rendering create subject template"}), 500

    if request.method == 'POST':
        if not form.validate_on_submit():
            return jsonify({'success':False,'error': 'Invalid form data'}), 400

        subject = form.name.data
        image = form.image.data

        files = {
            'file': (image.filename, image.stream, image.mimetype)
        }
        headers = {
            'x-api-key': MICROSERVICE_KEY
        }
        url = f"{BASE_URL}/faces?subject={subject}&det_prob_threshold=0.85"
        print("URL: ", url)

        future = executor.submit(make_api_call, url, headers, files)

        response = future.result()  # This will block until the request is complete
        current_app.logger.info(f"Response status: {response.status_code}")
        # log the response text
        current_app.logger.info(f"Response: {response.text}")
        if response.status_code == 413:
                message = "The image is too large. Please upload an image less than 2MB."
                flash(f"{message}", 'success')
                return jsonify({"error": "The image is too large. Please upload an image less than 2MB."}), 413
        try:
            response_json = response.json()  # Parse response as JSON
            message = response_json.get('message', 'No message found')  # Get the 'message' field, with a default value
            code = response_json.get('code', 'No code found')
            current_app.logger.info(f"Message: {message}")
            current_app.logger.info(f"Code: {code}")
            check = (code == 28)
            current_app.logger.info(f"Checking if code equals 28: {check}")
            current_app.logger.info(f'type of code: {type(code)}')
            if code == 28:
                flash(f"{message}", 'success')
                current_app.logger.info(f"Inside code 28")
                return jsonify({"success":True,"message": 'no Face is found in the image'}), 200
        except Exception as e:
            current_app.logger.error(f"Error parsing response: {e}")
            flash('Error parsing response. Please try again.', 'danger')
            return jsonify({"error": "Error parsing response. Please try again."}), 500

        if response.status_code in range(200, 209):
            result = response.json()
            current_app.logger.info(f"Response: {result}")
            flash('The Image was uploaded successfully.', 'success')
            if role in ['hr', 'company_hr', 'accountant', 'manager']:
                return jsonify({"success":True,"message": "The Image was uploaded successfully."}), 200
            elif role == 'supervisor':
                flash("Image uploaded successfully")
                return jsonify({"success":True,"message": "Image uploaded successfully"}), 200
            else:
                return jsonify({"success":True,"message": "Subject created successfully and the picture has been saved."}), 200
        else:
            flash('Subject creation failed. Please try again.', 'danger')
            return jsonify({"success": False,"error": "Subject creation failed. Please try again."}), 500

@attendance_bp.route('/list_employee_subjects', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def list_employee_subjects():
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
        flash(message, 'danger')
        return jsonify({"error": message}), 401

    headers = {
        'x-api-key': MICROSERVICE_KEY
    }

    subjects_url = f"{BASE_URL}/subjects"
    faces_url = f"{BASE_URL}faces"

    try:
        # Step 1: Get the list of subjects
        subjects_response = requests.get(subjects_url, headers=headers)
        current_app.logger.info(f"Response status: {subjects_response.status_code}")
        current_app.logger.info(f"Response: {subjects_response.text}")

        if subjects_response.status_code not in range(200, 209):
            flash('Error fetching subjects. Please try again.', 'danger')
            return jsonify({"error": "Error fetching subjects. Please try again."}), subjects_response.status_code

        try:
            subjects = subjects_response.json()['subjects']
            current_app.logger.info(f"Subjects: {subjects}")
            current_app.logger.info(f"Number of subjects: {len(subjects)}")
            current_app.logger.info(f"Subjects type: {type(subjects)}")
        except Exception as e:
            current_app.logger.error(f"Error parsing response: {e}")
            subjects = []

        # Initialize an empty list to store names and employee IDs
        names = []
        employee_ids = []
        employee_names = []
        # Loop through the subjects and get the names
        try:
            for subject in subjects:
                # The name comes after the first 36 characters
                name = subject[36:]
                # take out any trailing spaces
                name = name.strip()
                # Append the name to the list
                names.append(name)
                # The employee ID is the first 36 characters
                employee_id = subject[:36]
                # Remove any trailing spaces
                employee_id = employee_id.strip()
                # Append the employee ID to the list
                employee_ids.append(employee_id)
        except Exception as e:
            current_app.logger.error(f"Error getting names: {e}")
            names = []
            employee_ids = []

        try:
            database_name = session.get('database_name')
            # Get the list of employees from the database
            db_connection = DatabaseConnection()
            with db_connection.get_session(database_name) as db_session:
                # Get the list of employees from the database
                employees = Employee.get_employees(db_session)
                for employee in employees:
                    current_app.logger.info(f"Employee_ids: {employee_ids}")
                    check = (str(employee['employee_id']) in employee_ids)
                    current_app.logger.info(f"Check: {check}")
                    current_app.logger.info(f"Employee ID: {employee['employee_id']}")
                    current_app.logger.info(f"type of employee ID: {type(employee['employee_id'])} ")
                    current_app.logger.info(f"type of employee_ids: {type(employee_ids)}")
                    for employee_id in employee_ids:
                        current_app.logger.info(f"Employee ID: {employee_id}")
                        current_app.logger.info(f"type of employee ID: {type(employee_id)} ")

                    # We wanna get the list of employees that have been registered in the face collection
                    if str(employee['employee_id']) in employee_ids:
                        continue
                    else:
                        employee_names.append(employee)
        except Exception as e:
            current_app.logger.error(f"Error getting employee names: {e}")
            employee_names = []

        current_app.logger.info(f"Names: {names}")
        current_app.logger.info(f"Employee IDs: {employee_ids}")
        current_app.logger.info(f"Employee names: {employee_names}")
        # Render the template with the subjects and their images
        return render_template('attendance/subjects.html', employees=employee_names, names=names, employee_ids=employee_ids)

    except Exception as e:
        current_app.logger.error(f"Error making request: {e}")
        flash('Error making request. Please try again.', 'danger')
        return jsonify({"error": "Error making request. Please try again."}), 500

@attendance_bp.route('/download_image', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def download_image():
    """Download an image from the Face API."""
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = f"""You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered."""
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    headers = {
        'x-api-key': MICROSERVICE_KEY
    }
    base_url = os.getenv('base_url')
    faces_url = f"{BASE_URL}faces"

    try:
        faces_response = requests.get(faces_url, headers=headers)
        current_app.logger.info(f"Response status: {faces_response.status_code}")
        current_app.logger.info(f"Response: {faces_response.text}")
        faces = faces_response.json().get('faces', [])
        current_app.logger.info(f"Faces: {faces}")
        my_faces = []
        for face in faces:
            try:
                image_id = face['image_id']
                current_app.logger.info(f"Image ID: {image_id}")
                image_url = f"{faces_url}/{image_id}/img"

                current_app.logger.info(f"Image URL: {image_url}")
                my_faces.append(image_url)
            except Exception as e:
                current_app.logger.error(f"Error getting image: {e}")
                my_faces = []
    except Exception as e:
        current_app.logger.error(f"Error getting faces: {e}")
        my_faces = []

    # check if faces is empty
    if not my_faces:
        return jsonify({"error": "No images found"}), 400

    # Download the first image from the list as an example (you can loop for multiple images)
    for image_url in my_faces:
        try:
            current_app.logger.info(f"Downloading image: {image_url}")
            image_response = requests.get(image_url, headers=headers)
            current_app.logger.info(f"Image response status: {image_response.status_code}")

            if image_response.status_code == 200:
                # Prepare the image to be sent as a file to the user
                image_data = BytesIO(image_response.content)  # Load image into memory
                filename = image_url.split('/')[-2] + ".jpg"  # Use image ID as filename

                # Return the image as a downloadable file
                return send_file(image_data, mimetype='image/jpeg', as_attachment=True, download_name=filename)
            else:
                current_app.logger.error(f"Failed to download image, status: {image_response.status_code}")
        except Exception as e:
            current_app.logger.error(f"Error downloading image: {e}")
            return jsonify({"error": "Error downloading image"}), 500

    return jsonify({"error": "No images could be downloaded"}), 500

@attendance_bp.route('/proxy_image', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def proxy_image():
    """Fetch and serve an image from the Face API using the full URL with the required headers."""
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    image_url = request.args.get('image_url')  # Retrieve the image URL from the query parameter

    if not MICROSERVICE_KEY or not image_url:
        abort(400, "Missing API key or image URL.")

    headers = {'x-api-key': MICROSERVICE_KEY}

    try:
        response = requests.get(image_url, headers=headers)
        if response.status_code == 200:
            # Serve the image as a file response
            image_data = BytesIO(response.content)
            return send_file(image_data, mimetype='image/jpeg')
        else:
            abort(response.status_code, "Failed to fetch the image.")
    except Exception as e:
        current_app.logger.error(f"Error fetching image: {e}")
        abort(500, "Internal server error.")

@attendance_bp.route('/attendance_records', methods=['GET', 'POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def attendance_records():
    form = RecordsForm()
    """Get the attendance records of all employees from the database."""
    # Get the  API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    # Check if API KEY is None and redirect to dashboard
    if MICROSERVICE_KEY is None:
        message = f"""
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be regsitered.
        """
        flash(message, 'danger')
        return jsonify({"error": message}), 401

    # Get the database name from the session
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()
    # Connect to the database and get the attendance records
    with db_connection.get_session(database_name) as db_session:
        if request.method == 'POST':
            if not form.validate_on_submit():
                current_app.logger.error("Invalid form data")
                return jsonify({'error': 'Invalid form data'}), 400
            start_period = form.start_period.data
            end_period = form.end_period.data
            current_app.logger.info(f"Start period: {start_period}, End period: {end_period}")
            try:
                attendance = Attendance.get_all_attendance_in_range(db_session, start_period, end_period)
                current_app.logger.info("Attendance records retrieved")
                current_app.logger.info(f"Start period: {start_period}, End period: {end_period}")
                current_app.logger.info(f"Attendance number: {len(attendance)}")
            except Exception as e:
                current_app.logger.error(f"Error getting attendance: {e}")
                attendance = []
            return render_template('attendance/attendance.html', attendance=attendance, form=form)

        try:
            attendance = Attendance.get_attendance(db_session)
            current_app.logger.info(f"Attendance records retrieved: {attendance}")
            # we wanna group the attendance by clocked_in, field_clockin
            clocked_in = [record for record in attendance if record['time_in'] is not None]
            field_clockin = [record for record in attendance if record['field_in'] is None]
        except Exception as e:
            current_app.logger.error(f"Error getting attendance: {e}")
            attendance = []

    return render_template('attendance/attendance.html',
                           attendance=clocked_in, form=form)
@attendance_bp.route('/timesheet', methods=['GET', 'POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def timesheet():
    """Get the timesheet of all employees from the database."""
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        flash(message, 'danger')
        current_app.logger.error(f"Error: {message}")
        return jsonify({"error": message}), 400

    database_name = session.get('database_name')
    company_name = session.get('company_name')
    form = TimesheetForm()

    # Determine the current month and year based on form input or current date
    if request.method == 'POST' and form.validate_on_submit():
        period = form.period.data
    else:
        period = datetime.now()

    current_month = period.month
    current_year = period.year
    current_month_name = period.strftime('%B')
    total_days_in_month = monthrange(current_year, current_month)[1]
    current_app.logger.info(f"total days in month: {total_days_in_month} and the type is {type(total_days_in_month)}")

    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            employees = Employee.get_employees(db_session)
            current_app.logger.info("Employees retrieved successfully")
        except Exception as e:
            current_app.logger.error(f"Error retrieving employees: {e}")
            employees = []

        employees_attendance = []

        for employee in employees:
            allowances = employee['allowances']
            transport_allowance = employee['transport_allowance']
            emp_deductions = Deductions.get_deduction_for_given_month_and_year_for_employee(db_session, employee['employee_id'],current_month, current_year)
            total_deductions = sum(d['deduction_amount'] for d in emp_deductions)

            # RSSB Contributions fetching logic
            rssb_contributions = NsfContributions.query.all()
            if rssb_contributions is None:
                message = "No RSSB contributions found."
                flash(message, 'danger')
                current_app.logger.error(f"Error: {message}")
                return jsonify({'success':False, 'message': message}), 404
            for contribution in rssb_contributions:
                contribution_name = contribution.contribution_name
                if contribution_name == "maternity":
                    maternity_er_rate = contribution.employer_rate
                    maternity_ee_rate = contribution.employee_rate
                elif contribution_name == "pension":
                    pension_er_rate = contribution.employer_rate
                    pension_ee_rate = contribution.employee_rate
                elif contribution_name == "cbhi":
                    cbhi_er_rate = contribution.employer_rate
                    cbhi_ee_rate = contribution.employee_rate
            try:
                insurance = Insurance.get_insurances(db_session)
                if not insurance or len(insurance) == 0:
                    employee_insurance = 0
                    rama_ee_rate = 0
                    rama_er_rate = 0
                else:
                    employee_insurance = insurance[0]["employee_rate"]
                    employee_insurance = insurance[0]["employer_rate"]
                    # Check if the insurance name is rama
                    if insurance[0]["insurance_name"] == "rama":
                        rama_ee_rate = insurance[0]["employee_rate"]
                        rama_er_rate = insurance[0]["employer_rate"]
                    else:
                        rama_ee_rate = 0
                        rama_er_rate = 0
            except Exception as e:
                current_app.logger.error(f"Error fetching insurance: {e}")
                employee_insurance = Decimal('0.00')
                rama_ee_rate = Decimal('0.00')
                rama_er_rate = Decimal('0.00')
            try:
                # Fetch employee attendance
                attendance = Attendance.get_attendance_for_employee(db_session, employee['employee_id'])
                current_app.logger.info(f"Attendance records retrieved for employee with name: {employee['first_name']} {employee['last_name']} are: {attendance}")
                if not attendance:
                    current_app.logger.warning(f"No attendance records found for employee {employee['employee_id']}")
                    attendance = []
                days_present, days_on_leave, days_off, total_hours_worked = calculate_attendance(
                    attendance, current_year, current_month, total_days_in_month
                )
                current_app.logger.info(f"Days present: {days_present}, Days on leave: {days_on_leave}, Days off: {days_off}, Total hours worked: {total_hours_worked}")
            except Exception as e:
                current_app.logger.error(f"Error processing attendance for employee {employee['employee_id']}: {e}")
                days_present, days_on_leave, days_off, total_hours_worked = set(), set(), set(), timedelta()
            # Calculate attendance details
            try:
                attendance_status = get_attendance_status(days_present, days_on_leave, days_off, total_days_in_month)
            except Exception as e:
                current_app.logger.error(f"Error calculating attendance status: {e}")
                attendance_status = ['A'] * total_days_in_month
            try:
                days_worked = len(days_present)
            except Exception as e:
                current_app.logger.error(f"Error calculating days worked: {e}")
                days_worked = 0
            try:
                days_leave = len(days_on_leave)
            except Exception as e:
                current_app.logger.error(f"Error calculating days on leave: {e}")
                days_leave = 0
            try:
                days_off_count = len(days_off)
            except Exception as e:
                current_app.logger.error(f"Error calculating days off: {e}")
                days_off_count = 0
            try:
                days_absent = total_days_in_month - (days_worked + days_leave + days_off_count)
            except Exception as e:
                current_app.logger.error(f"Error calculating days absent: {e}")
                days_absent = 0
            try:
                total_hours_worked = round(total_hours_worked.total_seconds() / 3600, 2)
            except Exception as e:
                current_app.logger.error(f"Error calculating total hours worked: {e}")
                total_hours_worked = 0
            try:
                paid_days = days_worked + days_leave + days_off_count
            except Exception as e:
                current_app.logger.error(f"Error calculating paid days: {e}")
                paid_days = 0
            try:
                if employee['net_salary']:
                    applicable_net_salary = round((employee['net_salary'] / Decimal(total_days_in_month)) * Decimal(paid_days))
                    current_app.logger.info(f"Applicable net salary: {applicable_net_salary}")
                elif employee['gross_salary']:
                    # Calculate net salary from gross salary

                        calculation = SalaryCalculatorGross(allowances, transport_allowance, total_deductions,
                                                            cbhi_ee_rate, pension_ee_rate, pension_er_rate,
                                                            maternity_ee_rate, maternity_er_rate, rama_ee_rate,
                                                            employee['employee_type'])
                        basic_salary = calculation.calculate_basic_salary(employee['gross_salary'])
                        results = calculation.calculate_all(basic_salary)
                        # Unpack calculated results
                        (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                        maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = map(round, results)
                        applicable_net_salary = round((net_salary_value / total_days_in_month) * paid_days)
                        current_app.logger.info(f"Applicable net salary: {applicable_net_salary}")
                        current_app.logger.info(f"Results: {results}")
                        current_app.logger.info(f"Net salary: {net_salary_value}")
                elif employee['total_staff_cost']:
                    gross_salary = Employee.calculate_gross_based_on_total_staff_cost(db_session, employee, calculation_date=datetime.now().date())
                    calculate = SalaryCalculatorGross(allowances, transport_allowance, total_deductions,
                                                        cbhi_ee_rate, pension_ee_rate, pension_er_rate,
                                                        maternity_ee_rate, maternity_er_rate, rama_ee_rate,
                                                        employee['employee_type'])
                    basic_salary = calculate.calculate_basic_salary(gross_salary)
                    result = calculate.calculate_all(basic_salary)
                    # Unpack calculated results
                    (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                    maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = map(round, result)
                    applicable_net_salary = round((net_salary_value / Decimal(total_days_in_month)) * Decimal(paid_days))
            except Exception as e:
                current_app.logger.error(f"Error calculating applicable net salary: {e}")
                applicable_net_salary = Decimal('0.00')
            employees_attendance.append({
                'employee': employee,
                'attendance_status': attendance_status,
                'days_worked': days_worked,
                'days_absent': days_absent,
                'attendance': attendance,
                "days_leave": days_leave,
                'days_off_count': days_off_count,
                'total_hours_worked': total_hours_worked,
                'paid_days': paid_days,
                'applicable_net_salary': applicable_net_salary
            })

        # Store attendance details in session
        session.update({
            'employees_attendance': employees_attendance,
            'total_days_in_month': total_days_in_month,
            'company_name': company_name,
            'current_month_name': current_month_name,
            'current_year': current_year
        })

        try:
            current_app.logger.info("Rendering timesheet template")
            return render_template(
                'attendance/timesheet.html',
                employees_attendance=employees_attendance,
                total_days_in_month=total_days_in_month,
                company_name=company_name,
                current_month_name=current_month_name,
                current_year=current_year,
                Site=Site,
                db_session=db_session,
                form=form
            )
        except Exception as e:
            current_app.logger.error(f"Error rendering timesheet template: {e}")
            return jsonify({"error": "Error rendering timesheet template"}), 500


def calculate_attendance(attendance, current_year, current_month, total_days_in_month):
    """Calculate attendance details for an employee."""
    days_present = set()
    days_on_leave = set()
    days_off = set()
    total_hours_worked = timedelta()

    if not isinstance(attendance, list):
        current_app.logger.error(f"Invalid attendance data format: {attendance}")
        return days_present, days_on_leave, days_off, total_hours_worked

    for record in attendance:
        try:
            if 'time_in' not in record or 'work_status' not in record:
                current_app.logger.warning(f"Malformed record encountered: {record}")
                continue

            if record['time_in'] is None:
                time_off_days = calculate_time_off_days(record, current_year, current_month, total_days_in_month)
                print()
                print("Time off days: ", time_off_days)
                print()
                if record['work_status'].lower() == 'leave':
                    days_on_leave.update(time_off_days)
                elif record['work_status'].lower() == 'off':
                    days_off.update(time_off_days)
            else:
                record['time_in'] = datetime.strptime(record['time_in'], '%d/%m/%Y %H:%M:%S')
                if record['time_in'].year == current_year and record['time_in'].month == current_month:
                    days_present.add(record['time_in'].day)
                    if record.get('total_duration'):
                        total_hours_worked += record['total_duration']
        except IndexError as ie:
            current_app.logger.error(f"Index error in record processing: {ie}")
        except Exception as e:
            current_app.logger.error(f"Error processing record: {e}")

    return days_present, days_on_leave, days_off, total_hours_worked



def calculate_time_off_days(record, current_year, current_month, total_days_in_month):
    """Calculate the days an employee was on leave or off."""
    try:
        time_off_begin_date = datetime.strptime(record['time_off_begin_date'], '%d/%m/%Y')
        time_off_end_date = datetime.strptime(record['time_off_end_date'], '%d/%m/%Y')

        print()
        print("Time off begin date: ", time_off_begin_date)
        print("Time off end date: ", time_off_end_date)
        print()
        print("Current year: ", current_year)
        print("Current month: ", current_month)
        print("Total days in month: ", total_days_in_month)
        print()
        print(f"Attendance records: {record}")

        # Ensure the time-off period overlaps with the current month
        if (time_off_begin_date.year != current_year or
            time_off_end_date.year != current_year or
            time_off_begin_date.month != current_month and
            time_off_end_date.month != current_month):
            current_app.logger.info("Time-off dates do not fall within the current month.")
            return set()

        # Adjust dates to the current month range
        time_off_begin_date = max(time_off_begin_date, datetime(current_year, current_month, 1))
        time_off_end_date = min(time_off_end_date, datetime(current_year, current_month, total_days_in_month))

        print("Adjusted time off begin date: ", time_off_begin_date)
        print("Adjusted time off end date: ", time_off_end_date)

        # Calculate days
        return {
            (time_off_begin_date + timedelta(days=i)).day
            for i in range((time_off_end_date - time_off_begin_date).days + 1)
        }
    except Exception as e:
        current_app.logger.error(f"Error calculating time off days: {e}")
        return set()



def get_attendance_status(days_present, days_on_leave, days_off, total_days_in_month):
    """Generate attendance status for the days of the month."""
    return [
        'P' if day in days_present else
        'L' if day in days_on_leave else
        'O' if day in days_off else
        'A' for day in range(1, total_days_in_month + 1)
    ]

@attendance_bp.route('/bulk_leave', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def bulk_leave():

    return render_template("attendance/bulk_leave.html")


@attendance_bp.route('/record_leave_or_off', methods=['POST', 'GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def record_leave_or_off():
    """Record leave or off days for an employee."""
    form = LeaveManagementForm()
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        return jsonify({"error": "You are not registered to use this service. Please contact <NAME_EMAIL>"}), 403

    # Get the database name from the session
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")
    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            employees = Employee.get_employees(db_session)
            current_app.logger.info("Employees retrieved")
        except Exception as e:
            current_app.logger.error(f"Error getting employees: {e}")
            employees = []

    if request.method == 'POST':
        data = request.form
        current_app.logger.info(f"Data: {data}")
        if not form.validate_on_submit():
            current_app.logger.error(f"Invalid form data: {form.errors}")
            return jsonify({'error': 'Invalid form data'}), 400
        # Extract data from the form
        employee_id = request.form.get('employee_id')  # Get the employee ID from the form
        work_status = request.form.get('work_status')
        time_off_begin_date = request.form.get('time_off_begin_date')
        time_off_end_date = request.form.get('time_off_end_date')
        remarks = request.form.get('remarks')

        # Connect to the database and record the leave or off days for the employee
        with db_connection.get_session(database_name) as db_session:
            try:
                # Block to give employee leave
                done, result = Attendance.add_work_status(
                    db_session, employee_id, work_status,
                    time_off_begin_date, time_off_end_date)
                if not done:
                    return jsonify({"error": result}), 400 # This result is message as it failed

                message = f"{Auxillary.calculate_days_difference(time_off_begin_date, time_off_end_date)} days recorded successfully for the employee."
                current_app.logger.info(f"Leave or off days recorded: {result}")
                flash(message, 'success')
                return jsonify({"success":True,"message": message}), 200
            except Exception as e:
                current_app.logger.error(f"Error recording leave or off days: {e}")
                return jsonify({"error": "Error recording leave or off days"}), 500
    try:
        current_app.logger.info("Rendering leave management template")
        return render_template('attendance/leave_management.html', form=form, employees=employees)
    except Exception as e:
        current_app.logger.error(f"Error rendering leave management template: {e}")
        return jsonify({"error": "Error rendering leave management template"}), 500

@attendance_bp.route('/get_leave_records', methods=['GET', 'POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def get_leave_records():
    """Get the leave records of all employees from the database."""
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()

    # Connect to the database and get the leave records
    with db_connection.get_session(database_name) as db_session:
        current_app.logger.info("Getting leave records")

        try:
            current_app.logger.info("Inside try block to get leave records")
            leave_records = Attendance.get_leave_records(db_session)
        except Exception as e:
            current_app.logger.error(f"inside except block to get leave records")
            current_app.logger.error(f"Error getting leave records: {e}")
    try:
        return render_template('attendance/leave_records.html',
                               leave_records=leave_records,
                               Site=Site, db_session=db_session, Employee=Employee)
    except Exception as e:
        current_app.logger.error(f"Error rendering leave records template: {e}")
        return jsonify({"error": "Error rendering leave records template"}), 500

@attendance_bp.route('/update_leave_record/<attendance_id>', methods=['POST', 'GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def update_leave_record(attendance_id):
    """Update a leave record for an employee."""
    form =  UpdateLeaveManagementForm()
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()

    if request.method == 'POST':
        if not form.validate_on_submit():
            return jsonify({'error': 'Invalid form data'}), 400

        # Extract data from the form
        time_off_begin_date = request.form.get('time_off_begin_date')
        time_off_end_date = request.form.get('time_off_end_date')
        remarks = request.form.get('remarks')
        work_status = request.form.get('work_status')

        # Connect to the database and update the leave record for the employee
        with db_connection.get_session(database_name) as db_session:
            try:
                result = Attendance.update_leave_record(
                    db_session, attendance_id, time_off_begin_date,
                    time_off_end_date, remarks, work_status)
                current_app.logger.info(f"Leave record updated: {result}")
                message = "Leave record updated successfully."
                flash(message, 'success')
                current_app.logger.info(f"Leave record updated: {result}")
                if result is False:
                    return jsonify({"error": "Failed to update leave record"}), 400
                return jsonify({"success":True,"message": message}), 200
            except Exception as e:
                current_app.logger.error(f"Error updating leave record: {e}")
                return jsonify({"error": "Error updating leave record"}), 500
    try:
        current_app.logger.info("Rendering update leave record template")
        return render_template('attendance/update_leave_record.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error rendering update leave record template: {e}")
        return jsonify({"error": "Error rendering update leave record template"}), 500

@attendance_bp.route('/void_attendance_record/<attendance_id>', methods=['POST', 'GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'supervisor'])
def void_attendance_record(attendance_id):
    """Void an attendance record for an employee."""
    form = AttendanceStatusForm()
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()

    # Connect to the database and void the attendance record for the employee
    with db_connection.get_session(database_name) as db_session:
        if request.method == 'POST':
            if not form.validate_on_submit():
                return jsonify({'error': 'Invalid form data'}), 400

            # Extract data from the form
            status = request.form.get('status')
            reason = request.form.get('reason')

            try:
                result = Attendance.change_attendace_status(db_session, attendance_id, status, reason)
                current_app.logger.info(f"Attendance record voided: {result}")
                if status == "void":
                    message = "Attendance record voided successfully."
                elif status == "unvoid":
                    message = "Attendance record restored."
                else:
                    message = "Attendance record status updated."
                flash(message, 'success')
                current_app.logger.info(f"Attendance record voided: {result}")
                if result is False:
                    return jsonify({"error": "Failed to void attendance record"}), 400
                return jsonify({"success":True,"message": message}), 200
            except Exception as e:
                current_app.logger.error(f"Error voiding attendance record: {e}")
                return jsonify({"error": "Error voiding attendance record"}), 500
    try:
        current_app.logger.info("Rendering void attendance record template")
        return render_template('attendance/void_attendance_record.html', form=form)
    except Exception as e:
        current_app.logger.error(f"Error rendering void attendance record template: {e}")
        return jsonify({"error": "Error rendering void attendance record template"}), 500

@attendance_bp.route('/view_voided_attendance', methods=[ 'GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def view_voided_attendance():
    """View voided attendance records for all employees."""
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()

    # Connect to the database and get the voided attendance records
    with db_connection.get_session(database_name) as db_session:
        try:
            voided_attendance = Attendance.get_voided_attendance(db_session)
            current_app.logger.info("Voided attendance records retrieved")
        except Exception as e:
            current_app.logger.error(f"Error getting voided attendance records: {e}")
            voided_attendance = []

    try:
        return render_template('attendance/voided_attendance.html', voided_attendance=voided_attendance)
    except Exception as e:
        current_app.logger.error(f"Error rendering voided attendance template: {e}")
        return jsonify({"error": "Error rendering voided attendance template"}), 500

from reportlab.lib.pagesizes import A4, landscape

@attendance_bp.route('/export_timesheet_excel', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def export_timesheet_excel():
    """Export the timesheet data to Excel format."""
    print("=== Starting timesheet Excel export ===")
    current_app.logger.info("=== Starting timesheet Excel export ===")

    # Get the data from the session
    employees_attendance = session.get('employees_attendance')
    total_days_in_month = session.get('total_days_in_month')
    company_name = session.get('company_name')
    current_month_name = session.get('current_month_name')
    current_year = session.get('current_year')
    database_name = session.get('database_name')

    print(f"Session data: employees={len(employees_attendance) if employees_attendance else 0}, "
          f"days={total_days_in_month}, company={company_name}, month={current_month_name}")

    if not employees_attendance:
        print("No timesheet data available in session")
        flash("No timesheet data available. Please generate a timesheet first.", "warning")
        return redirect(url_for('attendance.timesheet'))

    try:
        # Import necessary libraries
        import pandas as pd
        from io import BytesIO
        from flask import send_file

        print("Creating simple Excel file")

        # Create a simple DataFrame with just the essential data
        data = []
        for idx, employee in enumerate(employees_attendance, 1):
            emp_name = f"{employee['employee']['first_name']} {employee['employee']['last_name']}"

            # Basic row with employee info
            row = {
                'No': idx,
                'Employee Name': emp_name,
                'Days Worked': employee['days_worked'],
                'Hours Worked': employee['total_hours_worked'],
                'Days Off': employee['days_off_count'],
                'Absent': employee['days_absent'],
                'Leave': employee['days_leave'],
                'Days Payable': employee['paid_days'],
                'Net': employee['applicable_net_salary']
            }

            # Add attendance status for each day
            for day, status in enumerate(employee['attendance_status'], 1):
                row[f'Day {day}'] = status

            data.append(row)

        # Create DataFrame
        print("Creating DataFrame")
        df = pd.DataFrame(data)

        # Create Excel file
        print("Writing to Excel")
        output = BytesIO()
        df.to_excel(output, index=False)
        output.seek(0)

        # Generate filename
        filename = f"{company_name.replace(' ', '_') if company_name else 'Company'}_Timesheet_{current_month_name}_{current_year}.xlsx"
        print(f"Filename: {filename}")

        # Send file
        print("Sending file")
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        import traceback
        print(f"ERROR: {str(e)}")
        print(traceback.format_exc())
        current_app.logger.error(f"Error generating Excel file: {e}")
        current_app.logger.error(traceback.format_exc())
        flash(f"Error generating Excel file: {str(e)}", "danger")
        return redirect(url_for('attendance.timesheet'))

@attendance_bp.route('/download_timesheet', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def download_timesheet():
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    try:
        # Retrieve session data
        database_name = session.get('database_name')
        company_name = session.get('company_name')
        employees_attendance = session.get('employees_attendance')
        current_month_name = session.get('current_month_name')
        current_year = session.get('current_year')
        total_days_in_month = session.get('total_days_in_month')
    except Exception as e:
        current_app.logger.error(f"Error getting session information: {e}")
        return jsonify({"error": "Error getting session information"}), 500

    try:
        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=landscape(A4))
        styles = getSampleStyleSheet()

        elements = [
            Paragraph(f"<strong>{company_name} Timesheet</strong>", styles['Title']),
            Paragraph(f"<strong>{current_month_name} {current_year}</strong>", styles['Title']),
            Paragraph("Company TIN: *********", styles['Normal']),
        ]

        headers = ['NO', 'NAME', 'BRANCH'] + [str(day) for day in range(1, total_days_in_month + 1)] + ['Days Worked', 'Hours', 'Off', 'Absent(A)', 'Leave (L)', 'Days Payable', 'Net']
        table_data = [headers]

        for employee in employees_attendance:
            db_connection = DatabaseConnection()
            with db_connection.get_session(database_name) as db_session:
                try:
                    site_id = employee['employee']['site_id']
                    site = Site.get_site_by_id(db_session, site_id)
                    site_name = site['site_name']
                except Exception as e:
                    current_app.logger.error(f"Error getting site name: {e}")
                    site_name = 'N/A'
            row = [
                len(table_data),
                f"{employee['employee']['first_name']} {employee['employee']['last_name']}",
                site_name,
            ] + employee['attendance_status'] + [
                employee['days_worked'],
                employee['total_hours_worked'],
                employee['days_off_count'],
                employee['days_absent'],
                employee['days_leave'],
                employee['paid_days'],
                employee['applicable_net_salary'],
            ]
            table_data.append(row)

        from reportlab.lib.units import inch

        # Calculate the column widths for landscape layout
        page_width = landscape(A4)[0] - doc.leftMargin - doc.rightMargin  # Update for landscape
        num_columns = len(headers)

        # Define specific column widths
        # Adjust these widths based on the content and layout you want
        col_widths = [
            0.5 * inch,  # Width for 'NO'
            2.0 * inch,  # Width for 'EMPLOYEE NAME'
            1.2 * inch,  # Width for 'LOCATION'
        ] + [0.5 * inch] * (total_days_in_month) + [0.5 * inch] * 7  # Rest of the columns with smaller widths

        # Ensure the total widths do not exceed the available page width
        if sum(col_widths) > page_width:
            # You may need to scale down some widths if they exceed page width
            scale_factor = page_width / sum(col_widths)
            col_widths = [width * scale_factor for width in col_widths]

        # Create the table with the defined column widths
        table = Table(table_data, colWidths=col_widths)

        # Apply styles to the table as before
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),  # Adjust font size here if needed
            ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
            ('TOPPADDING', (0, 1), (-1, -1), 3),
        ]))

        elements.append(table)

        doc.build(elements)
        output.seek(0)

        return send_file(output, as_attachment=True, download_name='Employee_Timesheet.pdf', mimetype='application/pdf')

    except Exception as e:
        current_app.logger.error(f"Error creating or saving PDF: {e}")
        return jsonify({"error": "Error creating PDF"}), 500

@attendance_bp.route('/manual_clockin/<employee_id>', methods=['POST', 'GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def clockin_employee(employee_id):
    """Clock in an employee."""
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """
        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')

    # Create a DatabaseConnection object
    db_connection = DatabaseConnection()

    # Connect to the database and clock in the employee
    with db_connection.get_session(database_name) as db_session:
        try:
            names = f"{session.get('first_name')} {session.get('last_name')}"
            location = f"Attendance recorded by {names}"
            device_used = 'manual entry'
            result = Attendance.clockin(db_session, employee_id, location, device_used)
            current_app.logger.info(f"Employee clocked in: {result}")
            flash(f"{result}", 'success')
            return jsonify({"success": True, "message": result}), 200
        except Exception as e:
            current_app.logger.error(f"Error clocking in employee: {e}")
            return jsonify({"error": "Error clocking in employee"}), 500

@attendance_bp.route('/clockin_employee', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def clockin_employee_list():
    """Render a list of employees for clocking in."""
    database_name = session.get('database_name')
    db_connection = DatabaseConnection()

    # Connect to the database and fetch employees
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        return render_template('attendance/clockin_employee.html', employees=employees)

@attendance_bp.route('/list_daily_attendance', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'supervisor'])
def list_daily_attendance():
    """List the daily attendance records for all employees."""
    form = RecordsForm()
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """

        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')
    db_connection = DatabaseConnection()

    # Connect to the database and fetch daily attendance records
    with db_connection.get_session(database_name) as db_session:
        try:
            daily_attendance = Attendance.get_attendance_by_date(db_session, datetime.now().date())
            current_app.logger.info(f"Daily attendance records retrieved: {daily_attendance}")
            current_app.logger.info("Daily attendance records retrieved")
        except Exception as e:
            current_app.logger.error(f"Error getting daily attendance records: {e}")
            daily_attendance = []

        # get the daily attendance records of clockin
        try:
            clockin_attendance = daily_attendance['clocked_in']
            current_app.logger.info(f"Clockin attendance records retrieved: {clockin_attendance}")
        except Exception as e:
            current_app.logger.error(f"Error getting clockin attendance records: {e}")
            clockin_attendance = []

        # Get the daily field attendance records
        try:
            field_attendance = daily_attendance['field_clockin']
            current_app.logger.info(f"Field attendance records retrieved: {field_attendance}")
        except Exception as e:
            current_app.logger.error(f"Error getting field attendance records: {e}")
            field_attendance = []

    try:
        return render_template('attendance/attendance.html',
                               attendance=clockin_attendance,
                               field_attendance=field_attendance,
                               form=form)
    except Exception as e:
        current_app.logger.error(f"Error rendering daily attendance template: {e}")
        return jsonify({"error": "Error rendering daily attendance template"}), 400

from flask import Response
import json

@attendance_bp.route('/get_previous_day_attendance', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'supervisor'])
def get_previous_day_attendance():
    """Get the attendance records for the previous day."""
    # Get the API KEY from CompanyHelpers
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    if MICROSERVICE_KEY is None:
        message = """
        You are not registered to use this service.
        Please contact <NAME_EMAIL> to be registered.
        """

        flash(message, 'danger')
        return jsonify({"error": message}), 400

    # Get the database name from the session
    database_name = session.get('database_name')
    db_connection = DatabaseConnection()

    # Connect to the database and fetch the attendance records for the previous day
    with db_connection.get_session(database_name) as db_session:
        try:
            #
            previous_day = datetime.now() - timedelta(days=1)
            # Get the previous day's date

            previous_day_attendance = Attendance.get_attendance_by_date(db_session, previous_day.date())

            # send an email to the supervisor of a company
            company_users = User.get_users(db_session)
            users_emails = []
            for supervisor in company_users:
                if supervisor['role'] == 'supervisor':
                    # Append the email of the supervisor to the list
                    users_emails.append(supervisor['email'])

            # Get company users from the central database
            company_id = '6f8faf26-b2fa-48c4-8dd7-5e02f8cf0946'
            central_users = Company.get_users_for_company(company_id)
            current_app.logger.info(f"Central users before converting to a dictionary: {central_users}")
            # convert central users to a dictionary object
            central_users = [user.to_dict() for user in central_users]
            current_app.logger.info(f"Central users after converting to a dictionary: {central_users}")

            for myuser in central_users:
                try:
                    email = myuser['email']
                    if email not in users_emails:
                        current_app.logger.info(f"Sending email to: {email}")
                        users_emails.append(email)
                except Exception as e:
                    current_app.logger.error(f"Error getting email: {e}")

            # Send the email to the users now that we have emails
            subject = f"Attendance records for {previous_day.strftime('%d/%m/%Y')}"
            # Get time_out from the previous day attendance
            for record in previous_day_attendance.get('clocked_in', []):
                if record.get('time_out') is None:
                    time_out = 'Not yet clocked out'
                else:
                    time_out = record.get('time_out')
            # arrange the body to include the attendance records details as a table
            # Generate the email body with an HTML table
            body = f"""
            <html>
            <head>
                <style>
                    body {{
                        font-family: Poppins, sans-serif;
                        background-color: #f4f4f4;
                        color: #333;
                        padding: 2rem;
                    }}
                    h2 {{
                        color: #007BFF;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                    }}
                    th, td {{
                        border: 1px solid #ddd;
                        padding: 10px;
                        text-align: left;
                    }}
                    th {{
                        background-color: #ebfaf0;
                        color:#25a38b;
                    }}
                    .footer {{
                        margin-top: 20px;
                        font-size: 14px;
                        color: #25a38b;
                    }}
                </style>
            </head>
            <body>
            <p>
                Hello, Please find the attendance records for {previous_day.strftime('%d/%m/%Y')} below.
            </p>

                <h3>Clocked In</h3>
                <table>
                    <tr>
                        <th>Employee Name</th>
                        <th>Time In</th>
                        <th>Time Out</th>
                        <th>Duration</th>
                        <th>Clock-in Location</th>
                        <th>Device Used</th>
                    </tr>
                    {''.join(f'<tr><td>{record["employee_name"]}</td><td>{record["time_in"]}</td><td>{time_out}</td><td>{record["total_duration"]}</td><td>{record["clockin_location"]}</td><td>{record["device_used"]}</td></tr>' for record in previous_day_attendance.get("clocked_in", []))}
                </table>

                <h3>On Leave</h3>
                <table>
                    <tr>
                        <th>Employee Name</th>
                        <th>Leave Start</th>
                        <th>Leave End</th>
                    </tr>
                    {''.join(f'<tr><td>{record["employee_name"]}</td><td>{record["time_off_begin_date"]}</td><td>{record["time_off_end_date"]}</td></tr>' for record in previous_day_attendance.get("on_leave", []))}
                </table>
                <h3>On Off</h3>
                <table>
                    <tr>
                        <th>Employee Name</th>
                        <th>Off Start</th>
                        <th>Off End</th>
                    </tr>
                    {''.join(f'<tr><td>{record["employee_name"]}</td><td>{record["time_off_begin_date"]}</td><td>{record["time_off_end_date"]}</td></tr>' for record in previous_day_attendance.get("on_off", []))}
                </table>
            </body>
            </html>
            """
            try:
                # send email to all reciepients
                sent = Auxillary.send_netpipo_email( subject, users_emails, body)
                current_app.logger.info(f"Email sent: {sent}")
            except Exception as e:
                current_app.logger.error(f"Error sending email: {e}")

            # Convert timedelta fields to string
            for record in previous_day_attendance.get('clocked_in', []):
                if isinstance(record.get('total_duration'), timedelta):
                    record['total_duration'] = str(record['total_duration'])

            current_app.logger.info(f"Previous day attendance records retrieved: {previous_day_attendance}")
        except Exception as e:
            current_app.logger.error(f"Error getting previous day attendance records: {e}")
            previous_day_attendance = []

    try:
        return Response(json.dumps(previous_day_attendance, default=str), mimetype="application/json")
    except Exception as e:
        current_app.logger.error(f"Error fetching previous day attendance: {e}")
        return jsonify({"error": "Error fetching previous day attendance"}), 400
