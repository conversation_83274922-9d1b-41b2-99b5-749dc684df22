from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, SubmitField, DateField, SelectField, TextAreaField
from wtforms.validators import DataRequired
from flask_wtf.file import FileField, FileAllowed, FileRequired

class SubjectForm(FlaskForm):
    name = StringField('Name', validators=[DataRequired()])
    image = FileField('Image', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'jpeg', 'png'], 'Images only!')
    ])
    submit = SubmitField('Upload')

class AttendanceForm(FlaskForm):
    image = FileField('Image', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'jpeg', 'png'], 'Images only!')
    ])
    longitude = StringField('Longitude')
    latitude = StringField('Latitude')
    device_info = StringField('Device Info')
    submit = SubmitField('Upload')

class LeaveManagementForm(FlaskForm):
    employee_id = StringField('Employee ID', validators=[DataRequired()])
    time_off_begin_date =DateField('Begin Date', validators=[DataRequired()])
    time_off_end_date = DateField('End Date', validators=[DataRequired()])
    work_status = SelectField('Work Status', choices=[('leave', 'Leave'), ('off', 'Off'), ('annual_leave', 'Annual Leave')])
    remarks = TextAreaField('Remarks')
    submit = SubmitField('Submit')

class UpdateLeaveManagementForm(FlaskForm):
    time_off_begin_date =DateField('Time Off Begin Date', validators=[DataRequired()])
    time_off_end_date = DateField('Time Off End Date', validators=[DataRequired()])
    work_status = SelectField('Work Status', choices=[('leave', 'leave'), ('off', 'off')])
    remarks = TextAreaField('Remarks')
    submit = SubmitField('Update')

class TimesheetForm(FlaskForm):
    period = DateField('Period', validators=[DataRequired()])
    submit = SubmitField('Submit')

class RecordsForm(FlaskForm):
    start_period = DateField('Start Period', validators=[DataRequired()])
    end_period = DateField('End Period', validators=[DataRequired()])
    submit = SubmitField('Search')

class AttendanceStatusForm(FlaskForm):
    status = SelectField('Status', choices=[('void', 'Void'), ('unvoid', 'Unvoid')])
    reason = TextAreaField('Reason', validators=[DataRequired()])
    submit = SubmitField('Submit')
