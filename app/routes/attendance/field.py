import json
import base64
from dotenv import load_dotenv
import os
from app.decorators.hr_decorator import hr_required
from app.decorators.role_decorator import role_required
from flask import session, redirect, url_for
from app.helpers.company_helpers import CompanyHelpers
from concurrent.futures import ThreadPoolExecutor
from app.utils.db_connection import DatabaseConnection
from app.models.company import Attendance, Employee, Site, Deductions, NsfContributions, Insurance, User
from datetime import datetime
from calendar import monthrange
from datetime import  timedelta
import os
from io import BytesIO
from flask import send_file, abort, request, jsonify, Blueprint, current_app, render_template, flash
from app.routes.attendance.forms import AttendanceForm
from app.routes.attendance.forms import AttendanceStatusForm
from app.models.central import RoutePlanRequirement, Company
from app.helpers.auxillary import Auxillary
import requests

load_dotenv()

field_bp = Blueprint('field', __name__)

MICROSERVICE_URL = os.getenv('MICROSERVICE_URL')

@field_bp.route('/field_clockin', methods=['POST', 'GET'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def field_clockin():
    form = AttendanceForm()
    company_id = session.get('company_id')
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    if request.method == 'GET':      
        if MICROSERVICE_KEY is None:
            message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
            flash(message, 'danger')
            current_app.logger.error(f"Error: {message}")
            return jsonify({"error": message}), 401

        return render_template('attendance/field_clockin.html', form=form)
    
    elif request.method == 'POST':
        employee_image = request.files.get('image')
        longitude = request.form.get('longitude', "unknown")
        latitude = request.form.get('latitude', "unknown")
        current_app.logger.info(f"Location: {longitude}, {latitude}")

        if not employee_image:
            return jsonify({"error": "No image uploaded"}), 400

        # Prepare API request to CompreFace
        files = {'file': ('employee_image.jpg', employee_image.stream, 'image/jpeg')}
        headers = {'x-api-key': MICROSERVICE_KEY}

        response = requests.post(MICROSERVICE_URL, headers=headers, files=files)
        message = response.json().get('message', '')
        current_app.logger.info(f"Response status: {response.status_code}")
        current_app.logger.info(f"Message: {message}")

        if response.status_code != 200:
            return jsonify({"error": message or "Attendance API Error.", "status_code": response.status_code}), 500

        try:
            result = response.json()
            subject = result['result'][0]['subjects'][0]['subject']
            similarity = round(result['result'][0]['subjects'][0]['similarity'] * 100, 1)
        except Exception as e:
            current_app.logger.error(f"Error parsing response: {e}")
            return jsonify({"error": "Invalid response from facial recognition service."}), 400

        if similarity <= 89:
            return jsonify({"result": "No match found. Please try again with a better image."}), 400

        employee_id = subject[:36]  # Extract Employee UUID
        subject = subject[36:]

        if longitude in ["unknown", "", None] or latitude in ["unknown", "", None]:
            return jsonify({"error": "Location not found. Please enable location services and try again."}), 400

        location = f"{longitude}, {latitude}"
        device_used = request.headers.get('User-Agent', "Unknown")
        role = session.get('role')
        session_employee_id = str(session.get('employee_id', ''))

        if role == 'employee' and session_employee_id != employee_id:
            return jsonify({"error": "You have not been authorized to clock in for another employee."}), 400

        db_connection = DatabaseConnection()
        with db_connection.get_session(session.get('database_name')) as db_session:

            try:
                employee = Employee.get_employee_by_id(db_session, employee_id)
                employee_name = employee.get('full_name')
            except Exception as e:
                employee_name = 'unknown'
                current_app.logger.error(f"Error getting employee name: {e}")

            try:
                clockin_location = Attendance.reverse_geocode(latitude, longitude)
                current_app.logger.info(f"Clockin location: {clockin_location}")
                location_name = clockin_location.get('display_name', 'Unknown location')
            except Exception as e:
                current_app.logger.error(f"Error getting clock-in location: {e}")
                location_name = "Unknown location"
            try:               
                result = Attendance.field_clockin(db_session, employee_id, location, device_used, location_name)
                current_app.logger.info(f"Attendance recorded: {result}")
                if "already" in result:
                    return jsonify(result), 400
                return jsonify(result), 200
            except Exception as e:
                current_app.logger.error(f"Error recording attendance: {e}")
                return jsonify({"error": "Error recording attendance"}), 500
            
@field_bp.route('/field_clockout', methods=['POST', 'GET'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def field_clockout():
    """Field clockout route"""
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    form = AttendanceForm()
    company_id = session.get('company_id')
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    if request.method == 'GET':
        if MICROSERVICE_KEY is None:
            message = "You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
            flash(message, 'danger')
            current_app.logger.error(f"Error: {message}")
            return jsonify({"error": message}), 401

        return render_template('attendance/field_clockout.html', form=form)
    
    elif request.method == 'POST':
        employee_image = request.files.get('image')
        longitude = request.form.get('longitude', "unknown")
        latitude = request.form.get('latitude', "unknown")
        current_app.logger.info(f"Location: {longitude}, {latitude}")

        if not employee_image:
            return jsonify({"error": "No image uploaded"}), 400

        # Prepare API request to CompreFace
        files = {'file': ('employee_image.jpg', employee_image.stream, 'image/jpeg')}
        headers = {'x-api-key': MICROSERVICE_KEY}

        response = requests.post(MICROSERVICE_URL, headers=headers, files=files)
        message = response.json().get('message', '')
        current_app.logger.info(f"Response status: {response.status_code}")
        current_app.logger.info(f"Message: {message}")

        if response.status_code != 200:
            return jsonify({"error": message or "Attendance API Error.", "status_code": response.status_code}), 500

        try:
            result = response.json()
            subject = result['result'][0]['subjects'][0]['subject']
            similarity = round(result['result'][0]['subjects'][0]['similarity'] * 100, 1)
        except Exception as e:
            current_app.logger.error(f"Error parsing response: {e}")
            return jsonify({"error": "Invalid response from facial recognition service."}), 400

        if similarity <= 89:
            return jsonify({"result": "No match found. Please try again with a better image."}), 400

        employee_id = subject[:36]
        subject = subject[36:]

        if longitude in ["unknown", "", None] or latitude in ["unknown", "", None]:
            return jsonify({"error": "Location not found. Please enable location services and try again."}), 400
        
        location = f"{longitude}, {latitude}"
        device_used = request.headers.get('User-Agent', "Unknown")
        role = session.get('role')
        session_employee_id = str(session.get('employee_id', ''))
        if role == 'employee' and session_employee_id != employee_id:
            return jsonify({"error": "You have not been authorized to clock out for another employee."}), 400
        
        db_connection = DatabaseConnection()
        with db_connection.get_session(session.get('database_name')) as db_session:
            try:
                employee = Employee.get_employee_by_id(db_session, employee_id)
                employee_name = employee.get('full_name')
            except Exception as e:
                employee_name = 'unknown'
                current_app.logger.error(f"Error getting employee name: {e}")

            try:
                clockout_location = Attendance.reverse_geocode(latitude, longitude)
                current_app.logger.info(f"Clockout location: {clockout_location}")
                location_name = clockout_location.get('display_name', 'Unknown location')
            except Exception as e:
                current_app.logger.error(f"Error getting clock-out location: {e}")
                location_name = "Unknown location"
            try:
                result = Attendance.field_clockout(db_session, employee_id, location, device_used, location_name)
                current_app.logger.info(f"Attendance recorded: {result}")
                if "you have not" in result:
                    return jsonify(result), 400
                return jsonify(result), 200
            except Exception as e:
                current_app.logger.error(f"Error recording attendance: {e}")
                return jsonify({"error": "Error recording attendance"}), 500