from flask import Blueprint, render_template, jsonify

errors_bp = Blueprint('errors', __name__)

@errors_bp.app_errorhandler(404)
def page_not_found(e):
    return render_template('400.html'), 404

@errors_bp.app_errorhandler(500)
def internal_server_error(e):
    return render_template('500.html'), 500

@errors_bp.app_errorhandler(415)
def unsupported_media_type(e):
    print('Unsupported Media Type: %s', e)
    return jsonify(success=False, error="Unsupported Media Type. Check the Content-Type header."), 415

@errors_bp.app_errorhandler(400)
def bad_request(e):
    return jsonify(success=False, error="Bad Request"), 400


@errors_bp.app_errorhandler(405)
def unauthorized(e):
    return jsonify(success=False, error="Method Not Allowed"), 405
