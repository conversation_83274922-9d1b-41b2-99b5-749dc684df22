from flask import render_template, redirect, url_for, flash, Blueprint
from app.helpers.token_manager import TokenManager
from app import app, mail
from flask_mail import Message
from app.models.central import User

token_bp = Blueprint('token', __name__)


@token_bp.route('/send-confirmation/<email>')
def send_confirmation(email):
    """Send a confirmation email to the user."""
    token_manager = TokenManager()
    token = token_manager.generate_confirmation_token(email)
    confirm_url = url_for('token.confirm_email', token=token, _external=True)
    
    # Send confirmation email logic
    msg = Message("Confirm Your Email", recipients=[email])
    msg.body = f"Please confirm your email by clicking on the following link: {confirm_url}"
    mail.send(msg)
    
    flash('A confirmation email has been sent.', 'success')
    return redirect(url_for('user_data.login'))

@token_bp.route('/confirm/<token>')
def confirm_email(token):
    """Confirm the user's email."""
    token_manager = TokenManager()
    email = token_manager.confirm_token(token)
    print("email", email)
    if email:
        # Update user to set is_active=True in the database
        try:
            if User.update_is_active(email):
                flash('Your email has been confirmed! Please login', 'success')
            else:
                flash('An error occurred while updating your account.', 'danger')

        except Exception as e:
            print(f"Error updating user: {e}")
            flash('An error occurred while updating your account.', 'danger')

        return redirect(url_for('user_data.login'))
    else:
        flash('The confirmation link is invalid or has expired.', 'danger')
        return redirect(url_for('user_data.login'))
