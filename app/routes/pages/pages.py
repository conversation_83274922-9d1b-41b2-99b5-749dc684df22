from flask import Blueprint, render_template, request,flash, redirect, session, url_for
from app.routes.pages.forms import ContactForm
from app import current_app
from app.models.central import Contact
import dotenv
import requests
dotenv.load_dotenv()
import os
from flask import jsonify
from app.helpers.auxillary import Auxillary

pages_bp = Blueprint('pages', __name__)

@pages_bp.after_request
def set_x_frame_options(response):
    response.headers['X-Frame-Options'] = 'DENY'
    return response
"""
@pages_bp.after_request
def apply_csp(response):
    response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' https://apis.google.com; style-src 'self' https://fonts.googleapis.com style-src 'self' https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css "
    return response
"""

@pages_bp.route('/', methods=['GET'])
@pages_bp.route('/home', methods=['GET'])
def home():
    title='Home'
    return render_template('pages/homepage.html', title=title)
@pages_bp.route('/privacy', methods=['GET'])
def privacy():
    title=''
    return render_template('pages/privacy.html', title=title)

@pages_bp.route('/terms', methods=['GET'])
def terms():
    title=''
    return render_template('pages/terms.html', title=title)
@pages_bp.route('/about', methods=['GET'])
def about():
    title='About Us'
    return render_template('pages/about_us.html', title=title)
@pages_bp.route('/features', methods=['GET','POST'])
def features():
    title='All features'
    return render_template('pages/features.html', title=title)
@pages_bp.route('/contact', methods=['GET','POST'])
def contact():
    title='Contact Us'
    form = ContactForm()
    # Fetch the reCAPTCHA public and private keys
    captcha_public_key = os.getenv('RECAPTCHA_PUBLIC_KEY')
    captcha_private_key = os.getenv('RECAPTCHA_PRIVATE_KEY')
    # Debugging output for keys
    print(f"Captcha Public Key: {captcha_public_key}")
    print(f"Captcha Private Key: {captcha_private_key}")
    try:
        if request.method == 'POST':
            # Verify reCAPTCHA
            try:
                if captcha_public_key and captcha_private_key:
                    # Get the reCAPTCHA response from the form

                    recaptcha_response = request.form.get('g-recaptcha-response')
                    print(f"reCAPTCHA Response: {recaptcha_response}")
                    
                    # Perform reCAPTCHA verification
                    verification_response = requests.post(
                        'https://www.google.com/recaptcha/api/siteverify',
                        data={
                            'secret': captcha_private_key,
                            'response': recaptcha_response
                        }
                    )
                    
                    result = verification_response.json()
                    print(f"reCAPTCHA Verification Result: {result}")
                    
                    if not result.get('success'):
                        message = "reCAPTCHA validation failed. Please try again."
                        flash(message, 'danger')
                        current_app.logger.error(message)
                        print(message)
                        return jsonify({'success': False, 'message': message}), 400
            except Exception as e:
                message = f"Error verifying reCAPTCHA: {e}"
                current_app.logger.error(message)
                print(message)
                flash("An error occurred during reCAPTCHA verification. Please try again.", 'danger')
                return jsonify({'success': False, 'message': message}), 500

            # Collect form data
            name = form.name.data
            email = form.email.data
            phone = form.phone.data
            company = form.company.data
            message_content = form.message.data
            print(f"Form Data - Name: {name}, Email: {email}, Phone: {phone}, Company: {company}, Message: {message_content}")
            # send the email
            my_email = '<EMAIL>'
            subject = 'New Contact Form Submission'
            message = f"{message_content}, \n\n\nfrom {name}, \nEmail: {email}, \nPhone: {phone}, \nCompany: {company}"
            try:
                Auxillary.send_netpipo_email(subject, my_email, message)
                if Auxillary.send_netpipo_email:
                    message = "Your message was sent successfully"
                    flash(message, 'success')
            except Exception as e:
                message = f"Error sending email: {e}"
                current_app.logger.error(message)
                print(message)
                current_app.logger.error(message)
            
            # Save new contact
            try:
                new_contact = Contact(
                    name=name,
                    email=email,
                    phone_number=phone,
                    company_name=company,
                    message=message_content
                )
            except Exception as e:
                message = f"Error creating new contact: {e}"
                current_app.logger.error(message)
                print(message)
                flash("An error occurred while creating the contact. Please try again later.", 'danger')
                return jsonify({'success': False, 'message': message}), 500
            
            # Commit new contact to database
            try:
                Contact.insert_contact(new_contact)
                flash('Message sent successfully!', 'success')
                print("Message sent successfully!")
                return redirect(url_for('pages.contact'))
            except Exception as e:
                message = f"Error saving contact: {e}"
                current_app.logger.error(message)
                print(message)
                flash("An error occurred while saving the contact. Please try again later.", 'danger')
                return jsonify({'success': False, 'message': message}), 500
    except Exception as e:
        message = f"Error processing contact form: {e}"
        current_app.logger.error(message)
        print(message)
        flash("An error occurred while processing the contact form. Please try again later.", 'danger')
        return jsonify({'success': False, 'message': message}), 500
    # Render the contact page with the form and reCAPTCHA key
    return render_template('pages/contact.html', form=form, captcha_public_key=captcha_public_key, title=title)

@pages_bp.route('/docs', methods=['GET', 'POST'])
def docs():
    title = 'User guide'
    return render_template('pages/user_guide.html', title=title)
@pages_bp.route('/pricing', methods=['GET', 'POST'])
def pricing():
    title = 'Pricing'
    return render_template('pages/pricing.html', title=title)

@pages_bp.route('/job_openings', methods=['GET', 'POST'])
def job_openings():
    title = 'Job Openings'
    return render_template('pages/job_openings.html', title=title)

@pages_bp.route('/quickbooks_connect', methods=['GET'])
def quickbooks_connect():
    title = 'How to connect Quickbooks'
    return render_template('pages/connect_quickbooks.html', title=title)
