from flask import Blueprint, render_template, current_app,flash, redirect, session, url_for
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SubmitField, EmailField
from flask_wtf.recaptcha import <PERSON>captcha<PERSON>ield
from wtforms.validators import DataRequired, Email, Length

class ContactForm(FlaskForm):
    name = StringField('Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    phone = StringField('Phone')
    company = StringField('Company')
    message = TextAreaField('Message', validators=[DataRequired(), Length(min=10, max=500)])
    recaptcha = RecaptchaField()
    submit = SubmitField('Send')
