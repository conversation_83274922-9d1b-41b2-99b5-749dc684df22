# app/forms.py
# app/forms.py
import re
from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileRequired, FileAllowed
from wtforms import StringField, SelectField, DateField, FloatField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Email, ValidationError, Optional
from flask import flash
import datetime
from flask import jsonify
from decimal import Decimal

def validate_nid(form, field):
    # Ensure that the field contains only digits and is exactly 16 characters long
    if not field.data.isdigit() or len(field.data) != 16:
        flash('NID must be exactly 16 digits long and contain only numbers.', 'danger')
        raise ValidationError('NID must be exactly 16 digits long and contain only numbers.')

def validate_name(form, field):
    # Ensure that the field contains only alphabets and spaces
    name_check =r"^[A-Za-z]+(?: [A-Za-z]+)?(?:'[A-Za-z]+)?$"
    is_match = re.match(name_check, field.data)
    if not is_match:
        flash('Name must contain only alphabets', 'danger')
        raise ValidationError('Name must contain only alphabets ')

def validate_net_salary(form, field):
    # Ensure the salary is a positive float and not more than 9 digits before the decimal
    if field.data < 0 or len(str(int(field.data))) > 9:
        flash('Net Salary must be a positive number and not more than 9 digits long.', 'danger')
        raise ValidationError('Net Salary must be a positive number and not more than 9 digits long.')

def validate_numeric(form, field):
    try:
        # Attempt to convert field data to float
        value = float(field.data)
    except ValueError:
        # If the conversion fails, raise a validation error
        flash(f'{field.label.text} must be a number.', 'danger')
        raise ValidationError(f'{field.label.text} must be a number.')

    # You can add additional checks here, such as non-negativity or length constraints
    if value < 0:
        flash(f'{field.label.text} must be a positive number.', 'danger')
        raise ValidationError(f'{field.label.text} must be a positive number.')
    if len(str(int(value))) > 9:
        flash(f'{field.label.text} must not be more than 9 digits long.', 'danger')
        raise ValidationError(f'{field.label.text} must not be more than 9 digits long.')

def validate_phone(form, field):
    # Ensure that the field contains only digits and is exactly 10 characters long
    if not field.data.isdigit() or len(field.data) != 10:
        # flash('Phone number must be exactly 10 digits long and contain only numbers.', 'danger')
        raise ValidationError('Phone number must be exactly 10 digits long and contain only numbers.')
def validate_dob(form, field):
    """Validate the date of birth, allowing it to be optional."""
    # Allow empty date of birth
    if not field.data:
        return  # Skip validation if no date is provided

    # Define today's date
    current = datetime.date.today()

    # Check if the date of birth is in the future
    if field.data > current:
        flash('Date of birth cannot be in the future.', 'danger')
        raise ValidationError('Date of birth cannot be in the future.')

    # Check if the date of birth indicates an age less than 16 years
    elif (current.year - field.data.year) < 16 or (
        (current.year - field.data.year) == 16 and (current.month, current.day) < (field.data.month, field.data.day)
    ):
        flash('Employee must be at least 16 years old to work.', 'danger')
        raise ValidationError('Employee must be at least 16 years old to work.')

def validate_hire_date(form, field):
    """Validate the hire date."""
    # define today's date
    current = datetime.date.today()
    # check if the hire date is in the future
    if field.data > current:
        flash('Hire date cannot be in the future.', 'danger')
        raise ValidationError('Hire date cannot be in the future.')
    # check if the hire date is less than 16 years. We check the difference in date, months and years
    elif (current.year - field.data.year) < 0:
        flash('Employee cannot be hired in the future.')
        raise ValidationError('Employee cannot be hired in the future.', 'danger')
    elif (current.year - field.data.year) == 0 and (current.month - field.data.month) < 0:
        flash('Employee cannot be hired in the future.')
        raise ValidationError('Employee cannot be hired in the future.')
    elif (current.year - field.data.year) == 0 and (current.month - field.data.month) == 0 and (current.day - field.data.day) < 0:
        flash('Employee cannot be hired in the future.')
        raise ValidationError('Employee cannot be hired in the future.', 'danger')

def validate_percentage(form, field):
    """Validate percentage fields."""
    if field.data is not None:
        try:
            value = float(field.data)
            if value < 0 or value > 100:
                flash(f'{field.label.text} must be between 0 and 100%.', 'danger')
                raise ValidationError(f'{field.label.text} must be between 0 and 100%.')
        except (ValueError, TypeError):
            flash(f'{field.label.text} must be a valid number.', 'danger')
            raise ValidationError(f'{field.label.text} must be a valid number.')

class EmployeeForm(FlaskForm):
    first_name = StringField('First Name', validators=[DataRequired()])
    last_name = StringField('Last Name', validators=[DataRequired()])
    nid = StringField('NID', validators=[DataRequired()])
    rssb_number = StringField('RSSB Number', validators=[DataRequired()])
    bank_name = StringField('Bank Name')
    bank_account = StringField('Bank Account')
    branch_name = StringField('Branch Name')
    account_name = StringField('Account Name')
    birth_date = DateField('Birth Date', validators=[Optional(), validate_dob])
    marital_status = SelectField('Marital Status', choices=[('single', 'Single'), ('married', 'Married')], validators=[DataRequired()])
    gender = SelectField('Gender', choices=[('male', 'Male'), ('female', 'Female')], validators=[DataRequired()])
    employee_tin = StringField('Employee TIN')
    employee_type = SelectField('Employee Type', choices=[], validators=[DataRequired()])
    department = SelectField('Department', choices=[('', 'Select Department')])
    salary_type = SelectField('Salary Type', choices=[('net_salary', 'Net Salary'), ('gross_salary', 'Gross Salary'), ('total_staff_cost', 'Total Staff Cost')], validators=[DataRequired()])
    salary_amount = FloatField('Salary Amount', validators=[DataRequired(), validate_net_salary])
    transport_allowance = StringField('Transport Allowance', default=0.0, validators=[Optional(),validate_numeric])
    housing_allowance = StringField('Housing Allowance', default=0.0, validators=[validate_numeric])
    communication_allowance = StringField('Communication Allowance', default=0.0, validators=[Optional(),validate_numeric])
    over_time = StringField('Over Time', default=0.0, validators=[validate_numeric])
    other_allowance = StringField('Other Allowance', default=0.0, validators=[validate_numeric])
    email = StringField('Email', validators=[Optional()])
    phone = StringField('Phone', validators=[Optional(), validate_phone])
    job_title = StringField('Job Title', validators=[Optional()])
    hire_date = DateField('Hire Date', validators=[Optional(), validate_hire_date])
    annual_leave_balance = StringField("Annual Leave Balance", validators=[Optional()])
    extra_leave_days = IntegerField("Extra Leave Days", validators=[Optional()])
    contract_end_date = DateField('Contract End Date', validators=[Optional()])
    submit = SubmitField('Register')

class EmployeePercentageForm(FlaskForm):
    """Form for registering employees with percentage-based salary calculations."""
    first_name = StringField('First Name', validators=[DataRequired(), validate_name])
    last_name = StringField('Last Name', validators=[DataRequired(), validate_name])
    nid = StringField('NID', validators=[DataRequired()])
    rssb_number = StringField('RSSB Number', validators=[DataRequired()])
    bank_name = StringField('Bank Name', validators=[Optional()])
    bank_account = StringField('Bank Account', validators=[Optional()])
    branch_name = StringField('Branch Name', validators=[Optional()])
    account_name = StringField('Account Name', validators=[Optional()])
    birth_date = DateField('Birth Date', validators=[Optional(), validate_dob])
    marital_status = SelectField('Marital Status', choices=[('single', 'Single'), ('married', 'Married'), ('divorced', 'Divorced'), ('widowed', 'Widowed')])
    gender = SelectField('Gender', choices=[('male', 'Male'), ('female', 'Female')], validators=[DataRequired()])
    employee_tin = StringField('Employee TIN')
    employee_type = SelectField('Employee Type', choices=[], validators=[DataRequired()])
    department = SelectField('Department', choices=[('', 'Select Department')])

    # Support both gross and net salary for percentage-based calculations
    salary_type = SelectField('Salary Type', choices=[('gross_salary', 'Gross Salary'), ('net_salary', 'Net Salary')], validators=[DataRequired()])
    salary_amount = FloatField('Salary Amount', validators=[DataRequired(), validate_net_salary])

    # Percentage fields
    basic_salary_percentage = FloatField('Basic Salary %', validators=[DataRequired(), validate_percentage])
    transport_allowance_percentage = FloatField('Transport Allowance %', validators=[Optional(), validate_percentage])
    housing_allowance_percentage = FloatField('Housing Allowance %', validators=[Optional(), validate_percentage])
    communication_allowance_percentage = FloatField('Communication Allowance %', validators=[Optional(), validate_percentage])

    # Other allowances (fixed amounts)
    over_time = StringField('Over Time', default=0.0, validators=[validate_numeric])
    other_allowance = StringField('Other Allowance', default=0.0, validators=[validate_numeric])

    # Contact and other information
    email = StringField('Email', validators=[Optional()])
    phone = StringField('Phone', validators=[Optional(), validate_phone])
    job_title = StringField('Job Title', validators=[Optional()])
    hire_date = DateField('Hire Date', validators=[Optional(), validate_hire_date])
    annual_leave_balance = StringField("Annual Leave Balance", validators=[Optional()])
    extra_leave_days = IntegerField("Extra Leave Days", validators=[Optional()])
    contract_end_date = DateField('Contract End Date', validators=[Optional()])
    submit = SubmitField('Register Employee')

class EmployeeUpdateForm(FlaskForm):
    first_name = StringField('First Name', validators=[DataRequired(), validate_name])
    last_name = StringField('Last Name', validators=[DataRequired(), validate_name])
    nid = StringField('NID', validators=[DataRequired()])
    nsf = StringField('RSSB Number', validators=[DataRequired()])
    bank_name = StringField('Bank Name')
    bank_account = StringField('Bank Account')
    branch_name = StringField('Branch Name')
    account_name = StringField('Account Name')
    birth_date = DateField('Birth Date', validators=[Optional(),validate_dob])
    marital_status = SelectField('Marital Status', choices=[('single', 'Single'), ('married', 'Married')], validators=[DataRequired()])
    gender = SelectField('Gender', choices=[('male', 'Male'), ('female', 'Female')], validators=[DataRequired()])
    employee_tin = StringField('Employee TIN', validators=[Optional()])
    employee_type = SelectField('Employee Type', choices=[], validators=[DataRequired()])
    department = SelectField('Department', choices=[('', 'Select Department')])
    salary_type = SelectField('Salary Type', choices=[('net_salary', 'Net Salary'), ('gross_salary', 'Gross Salary'), ('total_staff_cost', 'Total Staff Cost')], validators=[DataRequired()])
    salary_amount = FloatField('Salary Amount', validators=[DataRequired(), validate_net_salary])

    # Salary calculation method
    salary_calculation_method = SelectField('Calculation Method',
        choices=[('fixed_amounts', 'Fixed Amounts'), ('percentage_based', 'Percentage Based')],
        default='fixed_amounts', validators=[DataRequired()])

    # Fixed amount fields
    transport_allowance = StringField('Transport Allowance', default=0.0, validators=[validate_numeric])
    housing_allowance = StringField('Housing Allowance', default=0.0, validators=[validate_numeric])
    communication_allowance = StringField('Communication Allowance', default=0.0, validators=[validate_numeric])

    # Percentage fields
    basic_salary_percentage = FloatField('Basic Salary %', validators=[Optional(), validate_percentage])
    transport_allowance_percentage = FloatField('Transport Allowance %', validators=[Optional(), validate_percentage])
    housing_allowance_percentage = FloatField('Housing Allowance %', validators=[Optional(), validate_percentage])
    communication_allowance_percentage = FloatField('Communication Allowance %', validators=[Optional(), validate_percentage])

    over_time = StringField('Over Time', default=0.0, validators=[validate_numeric])
    other_allowance = StringField('Other Allowance', default=0.0, validators=[validate_numeric])
    email = StringField('Email',validators=[Optional(),Email()])
    phone = StringField('Phone', validators=[Optional(), validate_phone])
    job_title = StringField('Job Title', validators=[Optional()])
    hire_date = DateField('Hire Date', validators=[Optional(), validate_hire_date])
    site_id = StringField('Site')
    is_active = SelectField('Active', choices=[('yes', 'Yes'), ('no', 'No')], validators=[DataRequired()])
    is_brd_sponsored = SelectField('Is BRD Sponsored', choices=[('yes', 'Yes'), ('no', 'No')], validators=[DataRequired()])
    attendance_applicable = SelectField('Attendance Applicable', choices=[('yes', 'Yes'), ('no', 'No')], validators=[DataRequired()])
    annual_leave_balance = StringField("Annual Leave Balance", default=0, validators=[Optional()])
    extra_leave_days = IntegerField("Extra Leave Days", default=0, validators=[Optional()])
    contract_end_date = DateField('Contract End Date', validators=[Optional()])
    submit = SubmitField('Update')

class UploadEmployeeForm(FlaskForm):
    """Form for uploading employee data"""
    file = FileField('File', validators=[FileRequired(), FileAllowed(['xlsx', 'xls'], 'Excel files only!')])

    submit = SubmitField('Upload')