from flask import Blueprint, render_template, session, redirect, url_for, flash, jsonify
from app.models.central import NsfContributions
from app.models.company import Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection 
from app.decorators.hr_decorator import hr_required 
from app.helpers.rssb_contributions import RssbContributionHelpers
from app.routes.rssb.forms import RssbContributionForm
from app.decorators.admin_decorator import admin_required
from app.decorators.role_decorator import role_required
from flask import current_app

rssb_bp = Blueprint('rssb', __name__)

@rssb_bp.route('/get_rssb_rates', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant', 'admin'])
def get_rssb_rates():
    """Get all RSSB contribution rates and employee data."""
    try:
        
        try:
            print("Getting RSSB contribution rates: ")
            # Fetch RSSB contribution rates
            rssb_contributions = NsfContributions.query.all()
            print("rssb_contributions", rssb_contributions)
            print()
        except Exception as e:
            print(f"Error: {e}", "error")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
        
        # Get the company ID from the session
        company_id = session.get('company_id')
        print("company_id: ", company_id)
        if not company_id:
            flash("Company ID is missing in session", "error")
            return jsonify({'message': 'Company_id not found in session.'}), 500

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        print("database_name: ", database_name)
        if not database_name:
            flash("Database name could not be retrieved", "error")
            return jsonify({'message': 'Database name could not be retrieved.'}), 500

        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            print("Getting employees:  ")
            print("employees", employees)
            if not employees:
                flash("No employees found", "error")
                return jsonify({'message': 'No employees found.'}), 404

        try:
            # Calculate contributions
            contributions_data = RssbContributionHelpers.calculate_employee_contributions(employees, rssb_contributions)
        except Exception as e:
            print(f"Error: {e}", "error")
            return jsonify({'message': 'An error occurred. Please try again later. The calculate_employee_contributions method is not working'}), 500

        return render_template('rssb/view_rssb_contributions.html', contributions_data=contributions_data)
    except Exception as e:
        flash(f"Error: {e}", "error")
        return jsonify({'message': 'An error occurred. Please try again later. The get method id not working'}), 500

@rssb_bp.route('/add_rssb_contributions', methods=['GET', 'POST'])
@admin_required
def add_rssb_contributions():
    """Add RSSB contributions."""
    form = RssbContributionForm()
    if form.validate_on_submit():
        contribution_name = form.contribution_name.data
        employee_rate = form.employee_rate.data 
        employer_rate = form.employer_rate.data 

        employee_rate = float(employee_rate) / 100
        print("Employee Rate: ", employee_rate)
        employer_rate = float(employer_rate) / 100
        print("Employer Rate: ", employer_rate)
        start_date = form.start_date.data
        end_date = form.end_date.data
        current_app.logger.info(f"Contribution Name: {contribution_name}")
        try:
            nsf_contribution = NsfContributions(
                contribution_name=contribution_name,
                employee_rate=employee_rate, 
                employer_rate=employer_rate,
                start_date=start_date,
                end_date=end_date
                )
        except Exception as e:
            flash("Error creating NSF contribution object.", "error")
            return render_template('rssb/add_rssb_contributions.html', form=form)
        try:
            if nsf_contribution.insert_nsf_contribution():
                flash("NSF contribution inserted successfully.", "success")
                return redirect(url_for('rssb.add_rssb_contributions'))
        except Exception as e:
            flash("Error inserting NSF contribution.", "error")
            return render_template('rssb/add_rssb_contributions.html', form=form)
    current_app.logger.info("Getting all NSF contributions")
    try:
        rssb_contributions = NsfContributions.query.all()
        # Convert the query object to a dictionary
        rssb_contributions = [contribution.to_dict() for contribution in rssb_contributions]
        current_app.logger.info(f"NSF contributions: {rssb_contributions}")
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        rssb_contributions = []
    try:
        return render_template('rssb/add_rssb_contributions.html', form=form, rssb_contributions=rssb_contributions)
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@rssb_bp.route('/update_rssb_contributions/<uuid:id>', methods=['GET', 'POST'])
@admin_required
def update_rssb_contributions(id):
    """Update RSSB contributions."""
    nsf_contribution = NsfContributions.query.get_or_404(id)
    form = RssbContributionForm(obj=nsf_contribution)
    
    if form.validate_on_submit():
        name = nsf_contribution.contribution_name = form.contribution_name.data
        ee_rate = nsf_contribution.employee_rate = form.employee_rate.data
        er_rate = nsf_contribution.employer_rate = form.employer_rate.data
        try:
            if nsf_contribution.update_nsf_contribution(id, name, ee_rate, er_rate):
                flash("NSF contribution updated successfully.", "success")
                return redirect(url_for('rssb.add_rssb_contributions'))
        except Exception as e:
            print(f"Error: {e}")
            flash("Error updating NSF contribution.", "error")
            return render_template('rssb/update_rssb_contributions.html', form=form, nsf_contribution=nsf_contribution)
    
    return render_template('rssb/update_rssb_contributions.html', form=form, nsf_contribution=nsf_contribution)

@rssb_bp.route('/delete_rssb_contributions/<uuid:id>', methods=['GET', 'POST'])
@admin_required
def delete_rssb_contributions(id):
    """Delete RSSB contributions."""
    nsf_contribution = NsfContributions.query.get_or_404(id)
    try:
        if nsf_contribution.delete_nsf_contribution(id):
            flash("NSF contribution deleted successfully.", "success")
            return redirect(url_for('rssb.add_rssb_contributions'))
    except Exception as e:
        print(f"Error: {e}")
        flash("Error deleting NSF contribution.", "error")
        return redirect(url_for('rssb.add_rssb_contributions'))
    


