from flask_wtf import <PERSON>laskForm
from wtforms import DecimalField, SubmitField, StringField, validators, DateField
from wtforms.validators import NumberRang<PERSON>, ValidationError


def validate_rate(form, field):
    if field.data is None:
        raise ValidationError('Field is required')
    if field.data < 0 or field.data > 100:
        raise ValidationError('Rate must be between 0 and 100')

class RssbContributionForm(FlaskForm):
    contribution_name = StringField('Contribution Name',[validators.DataRequired()])
    employee_rate = DecimalField('Employee Rate', validators=[NumberRange(min=0)])
    employer_rate = DecimalField('Employer Rate', validators=[NumberRange(min=0)])
    start_date = DateField('Start Date')
    end_date = DateField('End Date', validators=[validators.Optional()])
    submit = SubmitField('Submit')