from flask import Blueprint, request, jsonify, current_app, flash, redirect, url_for, render_template
from app.models.central import BrdDeductions
from app.decorators.admin_decorator import admin_required
from app.routes.brd_deductions.forms import BrdDeductionsForm


brd_deductions_bp = Blueprint('brd_deductions', __name__)

@brd_deductions_bp.route('/add_brd_deductions', methods=['POST', 'GET'])
@admin_required
def add_brd_deductions():
    form = BrdDeductionsForm()
    # Retrieve current BRD Rates
    brd_rates = BrdDeductions.get_brd_deductions()
    
    current_app.logger.info(f"BRD deductions: {brd_rates}")
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Please fill in the required fields', 'danger')
            return redirect(url_for('brd_deductions.add_brd_deductions'))
        deduction_rate = request.form.get('deduction_rate')
        # convert the deduction rate a percentage
        deduction_rate = float(deduction_rate) / 100
        try:
            BrdDeductions.add_brd_deduction(deduction_rate)
            flash('BRD deduction added successfully')
            return redirect(url_for('brd_deductions.add_brd_deductions'))
        except Exception as e:
            flash(f"An error occurred: {str(e)}", 'danger')
            return redirect(url_for('brd_deductions.add_brd_deductions'))
    return render_template('brd_deductions/add_brd_deductions.html', form=form, brd_rates=brd_rates)

from uuid import uuid4

@brd_deductions_bp.route('/update_brd_rate/<uuid:id>', methods=['POST', 'GET'])
@admin_required
def update_brd_rate(id):
    """Update the Banque Rwandaise de Development rate."""
    form = BrdDeductionsForm()
    brd_rate = BrdDeductions.query.get_or_404(id)
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Please fill in the required fields', 'danger')
            return redirect(url_for('brd_deductions.update_brd_rate', id=id))
        deduction_rate = request.form.get('deduction_rate')
        # convert the deduction rate a percentage
        deduction_rate = float(deduction_rate) / 100
        try:
            result = BrdDeductions.update_brd_deduction(id, deduction_rate)
            if result:
                flash('BRD rate updated successfully')
                return redirect(url_for('brd_deductions.add_brd_deductions'))
            else:
                flash('An error occurred while updating the BRD rate', 'danger')
                return redirect(url_for('brd_deductions.update_brd_rate', id=id))
            
        except Exception as e:
            flash(f"An error occurred: {str(e)}", 'danger')
            return redirect(url_for('brd_deductions.update_brd_rate', id=id))
    return render_template('brd_deductions/update_brd_rate.html', form=form, brd_rate=brd_rate)

@brd_deductions_bp.route('/delete_brd_rate/<uuid:id>', methods=['POST', 'GET'])
@admin_required
def delete_brd_rate(id):
    """Delete a BRD rate."""
    try:
        result = BrdDeductions.delete_brd_deduction(id)
        if result:
            flash('BRD rate deleted successfully')
            return redirect(url_for('brd_deductions.add_brd_deductions'))
        else:
            flash('An error occurred while deleting the BRD rate', 'danger')
            return redirect(url_for('brd_deductions.add_brd_deductions'))
    except Exception as e:
        flash(f"An error occurred: {str(e)}", 'danger')
        return redirect(url_for('brd_deductions.add_brd_deductions'))
