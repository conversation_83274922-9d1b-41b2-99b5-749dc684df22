from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, DateField, FloatField, SubmitField, HiddenField, TextAreaField
from wtforms.validators import DataRequired

class DeductionForm(FlaskForm):
    description = StringField('Description', validators=[DataRequired()])
    amount = FloatField('Amount', validators=[DataRequired()])
    deduction_date = DateField('Deduction Date', validators=[DataRequired()])
    employee_id = HiddenField('Employee ID', validators=[DataRequired()])
    submit = SubmitField('Save and Close')

class DeductionUpdateForm(FlaskForm):
    description = StringField('Description', validators=[DataRequired()])
    deduction_amount = FloatField('Amount', validators=[DataRequired()])
    deduction_date = DateField('Deduction Date', validators=[DataRequired()])
    submit = SubmitField('Update Deduction')