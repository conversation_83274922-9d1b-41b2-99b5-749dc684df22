from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from app.decorators.hr_decorator import hr_required
from app.models.company import Deductions, Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection
from .forms import DeductionForm, DeductionUpdateForm
import logging
from flask import current_app
from app.helpers.auxillary import Auxillary
from datetime import datetime
from app.decorators.role_decorator import role_required

deductions_v2_bp = Blueprint('deductions_v2', __name__)

@deductions_v2_bp.route('/v2/add_deductions', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_deductions():
    company_id = session.get('company_id')
    if not company_id:
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    # Get the company database name
    database_name = CompanyHelpers.get_company_database_name(company_id)
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    form = DeductionForm()

    # Initialize the database connection and get employees
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        if not employees:
            message = "No employees found. Please add employees before adding deductions."
            current_app.logger.error(message)
            flash(message, 'danger')
            return redirect(url_for('deductions_v2.deductions'))

        if request.method == 'POST':
            data = request.form
            employee_id = data.get('employee_select')
            description = data.get('description')
            amount = data.get('amount')
            deduction_date = data.get('deduction_date')

            # Check if the deduction date is in the future
            if deduction_date > datetime.now().strftime('%Y-%m-%d'):
                message = "Sorry! You can only add deductions of the past and present dates."
                flash(message, 'info')
                current_app.logger.error(message)
                return redirect(url_for('deductions_v2.add_deductions'))
            # Create a new deduction record
            new_deduction = Deductions(description=description, deduction_amount=amount, deduction_date=deduction_date, employee_id=employee_id)
            try:
                db_session.add(new_deduction)
                db_session.commit()
                message = "Deduction added successfully"
                flash(message, 'success')
                return redirect(url_for('deductions_v2.deductions'))
            except Exception as e:
                db_session.rollback()
                message = "An error occurred while adding a deduction. Please try again later."
                current_app.logger.error(f"An error occurred while adding a deduction: {e}")
                flash(message, 'danger')
                return redirect(url_for('deductions_v2.add_deductions'))
    return render_template('deductions/add_deductions_v2.html', form=form, employees=employees)

@deductions_v2_bp.route('/v2/deductions', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def deductions():
    company_id = session.get('company_id')
    if not company_id:
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    # Get the company database name
    database_name = CompanyHelpers.get_company_database_name(company_id)
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    form = DeductionForm()

    # Initialize the database connection and fetch deductions
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        deductions = db_session.query(Deductions).all()
        if not deductions:
            logging.error("No deductions found")
        # Convert deduction objects to dictionaries
        deductions_list = [deduction.to_dict() for deduction in deductions]

        try:
            for personal_deduction in deductions_list:
                # Retrieve the corresponding employee object using employee_id
                employee = db_session.query(Employee).filter_by(employee_id=personal_deduction['employee_id']).first()

                if employee:
                    # Add the employee's full name to the dictionary
                    personal_deduction['employee_name'] = f"{employee.first_name} {employee.last_name}"

                # Remove unnecessary fields
                personal_deduction.pop('employee_id', None)  # Safely remove 'employee_id'
                personal_deduction.pop('company_id', None)   # Safely remove 'company_id'
                current_app.logger.info(f"Personal Deduction: {personal_deduction}")
        except Exception as e:
            current_app.logger.error(f"An error occurred while processing deductions list: {str(e)}")
            flash('An error occurred. Please try again later or contact support.', 'danger')
            return redirect(url_for('admin_data.dashboard'))
        print("Before storing the deductions list")
        # Store deductions list in session
        session['deductions_list'] = deductions_list

        try:
            # Render the deductions template
            current_app.logger.info("Rendering deductions template")
            return render_template('deductions/deductions_v2.html',
                                   employees=employees, deductions_list=deductions_list,
                                   form=form, Auxillary=Auxillary)

        except Exception as e:
            current_app.logger.error(f"An error occurred while rendering deductions template: {e}")
            flash(f"An error occurred while rendering deductions template: {e}", 'danger')
            return redirect(url_for('admin_data.dashboard'))


@deductions_v2_bp.route('/v2/deductions/list', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def list_deductions():
    company_id = session.get('company_id')
    if not company_id:
        current_app.logger.error("An error occurred while fetching company ID")
        return redirect(url_for('admin_data.dashboard'))

    # Get the company database name
    database_name = CompanyHelpers.get_company_database_name(company_id)
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))

    # Initialize the database connection and fetch deductions
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        deductions = db_session.query(Deductions).all()
        if not deductions:
            current_app.logger.error("No deductions found")
            return redirect(url_for('admin_data.dashboard'))

        deductions_list = [deduction.to_dict() for deduction in deductions]

    current_app.logger.info("Rendering list deductions template")
    return render_template('deductions/list_deductions_v2.html', deductions=deductions_list)

@deductions_v2_bp.route('/v2/update_deductions/<uuid:deduction_id>', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_deductions(deduction_id):
    """Update the deduction."""
    try:
        database_name = session.get('database_name')
        if database_name is None:
            flash('An error occurred. Please try again later.', 'danger')
            current_app.logger.error("An error occurred while fetching company database name")
            return redirect(url_for('admin_data.dashboard'))

        db_connection = DatabaseConnection()

        # Fetch the deduction object within a session context
        with db_connection.get_session(database_name) as db_session:
            deduction_to_update = Deductions.get_deduction_by_id(db_session, deduction_id)
            if deduction_to_update is None:
                flash("Deduction not found.", 'danger')
                current_app.logger.error("Deduction not found")
                return redirect(url_for('admin_data.dashboard'))

            form = DeductionUpdateForm(obj=deduction_to_update)

            #fetch the form data from the javascript
            if request.method == 'POST':
                description = request.form.get('description')
                deduction_amount = request.form.get('deduction_amount')
                deduction_date = request.form.get('deduction_date')
                #update the deduction object
                deduction_to_update.description = description
                deduction_to_update.deduction_amount = deduction_amount
                deduction_to_update.deduction_date = deduction_date

                try:
                    db_session.commit()
                    message = f"Deduction updated successfully"
                    flash(message, 'success')
                    return redirect(url_for('deductions_v2.deductions'))
                except Exception as e:
                    db_session.rollback()
                    current_app.logger.error(f"An error occurred while updating deductions: {e}")
                    flash('An error occurred. Please try again later.', 'danger')
                    return redirect(url_for('deductions_v2.update_deductions', deduction_id=deduction_id))

            # Render the update deductions template
            current_app.logger.info("Rendering update deductions template")
            return render_template('deductions/update_deductions_v2.html', form=form)
    except Exception as e:
        current_app.logger.error(f"An error occurred while updating deductions: {e}")
        flash("OOPS!! Something went wrong. Please try again later.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

@deductions_v2_bp.route('/v2/delete_deductions/<uuid:deduction_id>', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_deductions(deduction_id):
    """Delete the deduction."""
    try:
        database_name = session.get('database_name')
        if database_name is None:
            flash('An error occurred. Please try again later.', 'danger')
            current_app.logger.error("An error occurred while fetching company database name")
            return redirect(url_for('admin_data.dashboard'))

        db_connection = DatabaseConnection()

        # Fetch the deduction object within a session context
        with db_connection.get_session(database_name) as db_session:
            deduction_to_delete = Deductions.get_deduction_by_id(db_session, deduction_id)
            if deduction_to_delete is None:
                flash("Deduction not found.", 'danger')
                current_app.logger.error("Deduction not found")
                return redirect(url_for('admin_data.dashboard'))

            try:
                db_session.delete(deduction_to_delete)
                db_session.commit()
                message = f"deduction deleted successfully"
                flash(message, 'success')
                return redirect(url_for('deductions_v2.deductions'))
            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"An error occurred while deleting deductions: {e}")
                flash("An error occurred while deleting deductions. Please try again later.", 'danger')
                return redirect(url_for('admin_data.dashboard'))
    except Exception as e:
        current_app.logger.error(f"An error occurred while deleting deductions: {e}")
        flash("OOPS!! Something went wrong. Please try again later.", 'danger')
        return redirect(url_for('admin_data.dashboard'))



