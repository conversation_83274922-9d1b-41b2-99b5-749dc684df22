from flask import Blueprint, render_template, session, redirect, url_for, flash, jsonify
from app.models.central import NsfContributions, Company, TaxBracket  # Ensure TaxBracket is imported
from app.models.company import Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection 
from app.decorators.hr_decorator import hr_required 
from app.decorators.role_decorator import role_required

rra_bp = Blueprint('rra', __name__)

@rra_bp.route('/get_tax_rates', methods=['GET', 'POST'])
@hr_required
def get_tax_rates():
    """Get all RSSB contribution rates and employee data."""
    try:
        print("Getting RSSB contribution rates: ")
        # Fetch RSSB contribution rates
        rssb_contributions = NsfContributions.query.all()
        print("rssb_contributions", rssb_contributions)
        
        # Get the company ID from the session
        company_id = session.get('company_id')
        print("company_id: ", company_id)
        if not company_id:
            flash("Company ID is missing in session", "error")
            return jsonify({'message': 'Company_id not found in session.'}), 500

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        print("database_name: ", database_name)
        if not database_name:
            flash("Database name could not be retrieved", "error")
            return jsonify({'message': 'Database name could not be retrieved.'}), 500

        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            print("Getting employees:  ")
            print("employees", employees)
            if not employees:
                flash("No employees found", "error")
                return jsonify({'message': 'No employees found.'}), 404

        try:
            # Calculate contributions and PAYE
            contributions_data = calculate_employee_contributions(employees, rssb_contributions)
        except Exception as e:
            print(f"Error: {e}", "error")
            return jsonify({'message': 'An error occurred. Please try again later. The calculate_employee_contributions method is not working'}), 500

        return render_template('rra/view_withholding.html', contributions_data=contributions_data)
    except Exception as e:
        flash(f"Error: {e}", "error")
        return jsonify({'message': 'An error occurred. Please try again later. The get method is not working'}), 500

def calculate_paye(gross_salary):
    """Calculate Pay As You Earn (PAYE) for a given gross salary based on tax brackets."""
    tax_brackets = TaxBracket.get_taxbrackets()
    tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])  # Ensure tax brackets are sorted

    tax = 0.0
    for bracket in tax_brackets:
        if gross_salary > bracket['lower_bound']:
            taxable_income = min(gross_salary, bracket['upper_bound']) - bracket['lower_bound']
            tax += round(taxable_income * bracket['rate'])
        else:
            break

    return tax

def calculate_employee_contributions(employees, rssb_contributions):
    contributions_data = []

    for employee in employees:
        print("Processing employee:", employee)

        # Calculate PAYE for the employee
        paye_amount = calculate_paye(employee['gross_salary'])

        # Get initial contributions data structure
        employee_contributions = get_initial_contributions(employee, paye_amount)

        # Process each contribution for the employee
        process_contributions(employee, rssb_contributions, employee_contributions)

        # Calculate and add net pay
        net_pay = calculate_net_pay(employee, employee_contributions)
        employee_contributions['net_pay'] = net_pay

        # Append the final contributions data for the employee
        contributions_data.append(employee_contributions)
        print("Employee contributions:", employee_contributions)

    print("Final contributions data:", contributions_data)
    return contributions_data

def get_initial_contributions(employee, paye_amount):
    return {
        'employee': employee,
        'paye': paye_amount,
        'contributions': []
    }

def process_contributions(employee, rssb_contributions, employee_contributions):
    employee_salary = employee['gross_salary']
    employee_transport_allowance = employee['transport_allowance']
    paye_amount = employee_contributions['paye']

    # Temporary storage for special calculation
    pension_employee_amount = 0
    maternity_employee_amount = 0
    community_health_insurance_contribution = None

    for rssb_contribution in rssb_contributions:
        print("Processing RSSB contribution:", rssb_contribution)

        contribution_name = rssb_contribution.contribution_name
        print("contribution_name:", contribution_name)

        if contribution_name == 'community based health insurance':
            # Store the special case contribution for later calculation
            community_health_insurance_contribution = rssb_contribution
        else:
            # Calculate and add regular contributions
            if contribution_name == 'pension':
                pension_employee_amount = calculate_employee_pension_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employee_rate)
                employer_pension_amount = calculate_employer_pension_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employer_rate)
                add_contribution(employee_contributions, contribution_name, pension_employee_amount, employer_pension_amount)
            elif contribution_name == 'maternity':
                maternity_employee_amount = calculate_employee_maternity_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employee_rate)
                employer_maternity_amount = calculate_employer_maternity_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employer_rate)
                add_contribution(employee_contributions, contribution_name, maternity_employee_amount, employer_maternity_amount)
            else:
                # Default calculation for other contributions
                employee_contribution_amount = calculate_contribution_amount(employee_salary, employee_transport_allowance, rssb_contribution.employee_rate)
                employer_contribution_amount = calculate_contribution_amount(employee_salary, employee_transport_allowance, rssb_contribution.employer_rate)
                add_contribution(employee_contributions, contribution_name, employee_contribution_amount, employer_contribution_amount)

    # Calculate the Community Based Health Insurance contribution
    if community_health_insurance_contribution:
        community_health_insurance_amount_employee = calculate_cbhi_employee(employee_salary, paye_amount, pension_employee_amount, maternity_employee_amount, community_health_insurance_contribution.employee_rate)
        community_health_insurance_amount_employer = calculate_cbhi_employer(employee_salary, paye_amount, pension_employee_amount, maternity_employee_amount, community_health_insurance_contribution.employer_rate)
        add_contribution(employee_contributions, community_health_insurance_contribution.contribution_name, community_health_insurance_amount_employee, community_health_insurance_amount_employer)

def calculate_contribution_amount(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employee_pension_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employer_pension_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employee_maternity_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employer_maternity_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_cbhi_employee(salary, paye, pension, maternity, rate):
    return round((salary - paye - pension - maternity) * rate)

def calculate_cbhi_employer(salary, paye, pension, maternity, rate):
    return round((salary - paye - pension - maternity) * rate)

def add_contribution(employee_contributions, name, employee_amount, employer_amount):
    employee_contributions['contributions'].append({
        'contribution_name': name,
        'employee_contribution_amount': employee_amount,
        'employer_contribution_amount': employer_amount
    })

def calculate_net_pay(employee, employee_contributions):
    gross_salary = employee['gross_salary']
    paye = employee_contributions['paye']

    pension_employee = next((c['employee_contribution_amount'] for c in employee_contributions['contributions'] if c['contribution_name'] == 'pension'), 0)
    maternity_employee = next((c['employee_contribution_amount'] for c in employee_contributions['contributions'] if c['contribution_name'] == 'maternity'), 0)
    cbhi_employee = next((c['employee_contribution_amount'] for c in employee_contributions['contributions'] if c['contribution_name'] == 'community based health insurance'), 0)

    net_pay = gross_salary - paye - pension_employee - maternity_employee - cbhi_employee
    return net_pay
