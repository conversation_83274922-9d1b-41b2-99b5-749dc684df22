from flask_wtf import form, FlaskForm
from wtforms import StringField, FileField, SelectField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Optional

class DocumentForm(FlaskForm):
    """
    Form for uploading documents related to employees or companies.
    """
    file_label = StringField('File Label', validators=[Optional()])

    file = FileField('File', validators=[DataRequired()])

    submit = SubmitField('Upload')

class EmployeeDocumentForm(DocumentForm):
    """
    Form for HR to upload documents for specific employees.
    Extends the base DocumentForm with an employee selection field.
    """
    employee_id = <PERSON><PERSON>ield('Select Employee', validators=[DataRequired()], coerce=str)
