from flask import Blueprint, jsonify, request, redirect, url_for, current_app
from flask import session, render_template, flash, send_file, after_this_request
from app.models.company_documents import Document
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from app.routes.documents.forms import DocumentForm, EmployeeDocumentForm
from app.models.company import User, Employee
import os
import traceback

db_connection = DatabaseConnection()

document = Blueprint('document', __name__)

@document.route('/upload_documents', methods=['POST', 'GET'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def upload_documents():
    """
    Uploads documents related to employees or companies.
    """
    form = DocumentForm()
    user_id = session.get('user_id')
    company_id = session.get('company_id')

    role = session.get('role', 'employee')
    current_app.logger.info("accessing upload_documents")
    if request.method == 'POST':
        # Get the uploaded file from the request
        file = form.file.data
        file_label = form.file_label.data

        # Check if the file is present
        if not file:
            return jsonify({'error': 'No file provided'}), 400
        # Define document type based on role
        if role in ['hr', 'manager', 'accountant', 'company_hr']:
            document_type = 'company'
            employee_id = None
        elif role in ['employee', 'supervisor']:
            document_type = 'employee'
            employee_id = session.get('employee_id')
        else:
            document_type = 'company'

        #connect to the right database
        database_name = session.get('database_name')

        with db_connection.get_session(database_name) as db_session:
            # Get user information to store with the document
            try:
                uploader_name = "Unknown User"
                uploader_role = role  # Default to the role from session

                # Determine user type and get appropriate information
                if role in ['hr', 'manager', 'accountant', 'company_hr']:
                    # These roles are typically in the central database
                    from app.models.central import User as CentralUser
                    try:
                        central_user = CentralUser.get_user_by_id(user_id, abort_on_not_found=False)
                        if central_user:
                            uploader_name = f"{central_user.get('first_name', '')} {central_user.get('last_name', '')}".strip() or central_user.get('username')
                            uploader_role = central_user.get('role', role)
                    except Exception as e:
                        current_app.logger.warning(f"Could not retrieve central user info: {e}")

                elif role in ['employee', 'supervisor']:
                    # These roles are typically in the company-specific database
                    try:
                        # First try to get the user from company database
                        user = db_session.query(User).filter_by(user_id=user_id).first()
                        if user:
                            user_info = user.to_dict()
                            uploader_name = user_info.get('full_name') or user_info.get('username')
                            uploader_role = user_info.get('role', role)

                        # If we have an employee_id, try to get more detailed information
                        if employee_id:
                            from app.models.company import Employee
                            employee = db_session.query(Employee).filter_by(employee_id=employee_id).first()
                            if employee:
                                uploader_name = f"{employee.first_name} {employee.last_name}"
                    except Exception as e:
                        current_app.logger.warning(f"Could not retrieve company user info: {e}")

                # As a fallback, if we still don't have a name, try both databases
                if uploader_name == "Unknown User":
                    # Try company database
                    try:
                        user = db_session.query(User).filter_by(user_id=user_id).first()
                        if user:
                            user_info = user.to_dict()
                            uploader_name = user_info.get('full_name') or user_info.get('username')
                    except Exception:
                        pass

                    # Try central database
                    if uploader_name == "Unknown User":
                        try:
                            from app.models.central import User as CentralUser
                            central_user = CentralUser.get_user_by_id(user_id, abort_on_not_found=False)
                            if central_user:
                                uploader_name = f"{central_user.get('first_name', '')} {central_user.get('last_name', '')}".strip() or central_user.get('username')
                        except Exception:
                            pass

                # Format the uploader information
                uploader_info = f"{uploader_name} ({uploader_role})"
                current_app.logger.info(f"Document being uploaded by: {uploader_info}")

                # Upload the document to DigitalOcean Spaces and save the DB record
                result = Document.upload_document(
                    db_session,
                    company_id,
                    document_type,
                    file,
                    uploader_info,  # Pass the formatted uploader info instead of just user_id
                    employee_id=employee_id,
                    file_label=file_label
                )
                current_app.logger.info(f"Document uploaded successfully: {result}")
                message = f"Document uploaded successfully"
                flash(message, 'success')
                return jsonify({'success': True, 'message': message}), 200
            except Exception as e:
                current_app.logger.error(f"Error uploading document: {e}")
                message = "Error uploading document"
                return jsonify({'error': message}), 400

    try:
        current_app.logger.info("Rendering upload_documents template")
        return render_template('documents/upload_documents.html', form=form, role=role)
    except Exception as e:
        current_app.logger.error(f"Error rendering template: {e}")
        return jsonify({'error': 'Failed to render template'}), 400

@document.route('/view_documents', methods=['GET', 'POST'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def view_documents():
    """
    View documents related to employees or companies.
    Provides comprehensive document information and filtering options.
    - GET: Renders the document view with filters applied via query parameters.
    - POST: Handles form submissions or JSON data for filtering or other actions.
    """
    user_id = session.get('user_id')
    company_name = session.get('company_name')
    company_id = session.get('company_id')
    role = session.get('role', 'employee')
    current_app.logger.info("Accessing view_documents")

    # Initialize filter parameters
    document_type_filter = None
    uploader_filter = None
    date_filter = None
    employee_filter = None

    # Handle GET or POST request
    if request.method == 'POST':
        # For POST, expect form data or JSON payload
        if request.is_json:
            # Handle JSON payload
            data = request.get_json()
            document_type_filter = data.get('document_type')
            uploader_filter = data.get('uploader')
            date_filter = data.get('date_range')
            employee_filter = data.get('employee_id')
        else:
            # Handle form data
            document_type_filter = request.form.get('document_type')
            uploader_filter = request.form.get('uploader')
            date_filter = request.form.get('date_range')
            employee_filter = request.form.get('employee_id')
        return jsonify({
            'success': True,
            'message': 'Filters applied successfully',
            'document_type': document_type_filter,
            'uploader': uploader_filter,
            'date_range': date_filter,
            'employee_id': employee_filter
        }), 200
    else:
        # For GET, use query parameters
        document_type_filter = request.args.get('document_type')
        uploader_filter = request.args.get('uploader')
        date_filter = request.args.get('date_range')
        employee_filter = request.args.get('employee_id')

    # Connect to the right database
    database_name = session.get('database_name')
    my_documents = []

    # Log who is viewing documents
    current_app.logger.info(f"User {user_id} with role {role} viewing documents")
    current_app.logger.info(f"Employee ID: {employee_filter if employee_filter else 'N/A'}")
    current_app.logger.info(f"Filters applied - Document Type: {document_type_filter}, Uploader: {uploader_filter}, Date Range: {date_filter}")
    with db_connection.get_session(database_name) as db_session:
        try:
            # Determine which documents to fetch based on role
            if role in ['hr', 'manager', 'accountant', 'company_hr']:
                # HR and management roles can see all documents (company and employee)
                if document_type_filter == 'employee':
                    # If filtering for employee documents
                    if employee_filter:
                        # If filtering for a specific employee
                        current_app.logger.info(f"Filtering documents for employee ID: {employee_filter}")
                        documents = Document.get_documents(db_session, document_type='employee', employee_id=employee_filter)
                    else:
                        # All employee documents
                        documents = Document.get_documents(db_session, document_type='employee')
                elif document_type_filter == 'company':
                    # If filtering for company documents
                    documents = Document.get_documents(db_session, document_type='company')
                else:
                    # No filter - get all documents (both company and employee)
                    documents = Document.get_documents(db_session)
            elif role in ['employee', 'supervisor']:
                # Employees can ONLY see their own documents, not company documents or other employees' documents
                employee_id = session.get('employee_id')

                # Regardless of filter, employees can only see their own documents
                if document_type_filter == 'company':
                    # If employee tries to filter for company documents, show them nothing
                    documents = []
                    flash("You don't have permission to view company documents", "warning")
                else:
                    # Only show employee's own documents
                    documents = Document.get_documents(db_session, document_type='employee', employee_id=employee_id)

                # Log the restricted access
                current_app.logger.info(f"Employee {employee_id} restricted to viewing only their own documents")
            else:
                return jsonify({'error': 'Invalid role'}), 400

            # Get list of employees for HR filter dropdown
            employees_list = []
            if role in ['hr', 'manager', 'accountant', 'company_hr']:
                try:
                    from app.models.company import Employee
                    employees = db_session.query(Employee).all()
                    for employee in employees:
                        employees_list.append({
                            'id': employee.employee_id,
                            'name': f"{employee.first_name} {employee.last_name}"
                        })
                except Exception as e:
                    current_app.logger.warning(f"Could not retrieve employees list: {e}")

            # Process documents and apply additional filters
            for document in documents:
                # Format the date for better display
                formatted_date = document.uploaded_at.strftime('%Y-%m-%d %H:%M') if document.uploaded_at else 'Unknown'

                # Apply uploader filter if provided
                if uploader_filter and uploader_filter.lower() not in document.uploaded_by.lower():
                    continue

                # Apply date filter if provided
                if date_filter and document.uploaded_at:
                    from datetime import datetime, timedelta
                    now = datetime.now()

                    if date_filter == 'today' and document.uploaded_at.date() != now.date():
                        continue
                    elif date_filter == 'week':
                        week_ago = now - timedelta(days=7)
                        if document.uploaded_at < week_ago:
                            continue
                    elif date_filter == 'month':
                        month_ago = now - timedelta(days=30)
                        if document.uploaded_at < month_ago:
                            continue

                # Get employee name if it's an employee document
                employee_name = None
                if document.document_type == 'employee' and document.employee_id:
                    try:
                        from app.models.company import Employee
                        employee = db_session.query(Employee).filter_by(employee_id=document.employee_id).first()
                        if employee:
                            employee_name = f"{employee.first_name} {employee.last_name}"
                    except Exception:
                        pass

                # Add document to the list with comprehensive information
                my_documents.append({
                    'document_id': document.document_id,
                    'file_name': document.file_name,
                    'file_label': document.file_label,
                    'file_url': document.file_url,
                    'uploaded_by': document.uploaded_by,
                    'uploaded_at': formatted_date,
                    'document_type': document.document_type,
                    'employee_id': document.employee_id,
                    'employee_name': employee_name
                })

            current_app.logger.info(f"Retrieved {len(my_documents)} documents successfully")

            # Sort documents by upload date (newest first)
            my_documents.sort(key=lambda x: x['uploaded_at'], reverse=True)
            
        except Exception as e:
            current_app.logger.error(f"Error retrieving documents: {e}")
            return jsonify({'error': str(e)}), 400

        try:
            if request.method == 'POST' and request.is_json:
                # For JSON POST requests, return JSON response
                return jsonify({
                    'success': True,
                    'documents': my_documents,
                    'message': 'Documents retrieved successfully'
                }), 200
            else:
                # For GET or form-based POST, render the template
                current_app.logger.info("Rendering documents template")
                return render_template(
                    'documents/documents.html',
                    documents=my_documents,
                    role=role,
                    company_name=company_name,
                    document_type_filter=document_type_filter,
                    uploader_filter=uploader_filter,
                    date_filter=date_filter,
                    employees=employees_list,
                    employee_filter=employee_filter
                )
        except Exception as e:
            current_app.logger.error(f"Error rendering documents template: {e}")
            return jsonify({'error': 'Failed to render documents template'}), 400
@document.route("/download_documents", methods=["GET"])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def download_document_route():
    """
    Download a document by its ID.
    Handles document retrieval, access control, and cleanup of temporary files.
    """
    document_id = request.args.get("document_id")
    current_app.logger.info(f"Download requested for document ID: {document_id}")

    if not document_id:
        flash("Missing document ID", "error")
        return redirect(url_for('document.view_documents'))

    # Get the database name from the session
    database_name = session.get('database_name')
    role = session.get('role', 'employee')
    employee_id = session.get('employee_id')

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # First, check if the document exists and if the user has permission to access it
            doc = Document.get_document_by_id(db_session, document_id)
            if not doc:
                flash("Document not found", "error")
                return redirect(url_for('document.view_documents'))

            # Check permissions based on role and document type
            if role in ['employee', 'supervisor']:
                # Employees can only download their own documents
                if doc.document_type == 'company' or (doc.document_type == 'employee' and doc.employee_id != employee_id):
                    flash("You don't have permission to access this document", "error")
                    current_app.logger.warning(f"Access denied: Employee {employee_id} attempted to download document {document_id} which they don't have permission for")
                    return redirect(url_for('document.view_documents'))
            # HR and other management roles can download any document

            # Log the download attempt with user information
            current_app.logger.info(f"User {session.get('user_id')} ({role}) downloading document {doc.file_name}")

            # Download the document
            local_path = Document.download_document(db_session, document_id)
            current_app.logger.info(f"Document downloaded successfully to: {local_path}")

            # Clean up the temporary file after the response is sent
            @after_this_request
            def cleanup(response):
                try:
                    os.remove(local_path)
                    current_app.logger.info(f"Temporary file {local_path} removed")
                except Exception as e:
                    current_app.logger.error(f"Error removing temp file: {e}")
                return response

            # Send the file to the user
            return send_file(
                local_path,
                as_attachment=True,
                download_name=os.path.basename(local_path)
            )
        except Exception as e:
            current_app.logger.error(f"Error downloading document: {e}")
            flash(f"Error downloading document: {str(e)}", "error")
            return redirect(url_for('document.view_documents'))

@document.route('/upload_employee_document', methods=['GET', 'POST'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def upload_employee_document():
    """
    Allows HR and management roles to upload documents for specific employees.
    Displays a form with employee selection and file upload fields.
    """
    # Log access to this route
    current_app.logger.info("Accessing upload_employee_document route")

    # Check if user is logged in and has required session data
    user_id = session.get('user_id')
    if not user_id:
        current_app.logger.error("User ID not found in session")
        flash("Please log in to access this feature", "danger")
        return redirect(url_for('user_data.login'))

    company_id = session.get('company_id')
    if not company_id:
        current_app.logger.error("Company ID not found in session")
        flash("Company information not found", "danger")
        return redirect(url_for('user_data.login'))

    role = session.get('role')
    current_app.logger.info(f"User {user_id} with role {role} accessing upload_employee_document")

    database_name = session.get('database_name')
    if not database_name:
        current_app.logger.error("Database name not found in session")
        flash("Database information not found", "danger")
        return redirect(url_for('user_data.login'))

    # Create the form
    form = EmployeeDocumentForm()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the list of employees for the dropdown
        try:
            current_app.logger.info("Retrieving employees for dropdown")
            employees = Employee.get_employees(db_session)
            if not employees:
                current_app.logger.warning("No employees found in database")
                flash("No employees found", "warning")
                return redirect(url_for('admin_data.dashboard'))

            employee_choices = [(str(emp['employee_id']), f"{emp['first_name']} {emp['last_name']}") for emp in employees]
            form.employee_id.choices = employee_choices
            current_app.logger.info(f"Successfully loaded {len(employee_choices)} employees for dropdown")
        except Exception as e:
            current_app.logger.error(f"Error retrieving employees: {e}")
            current_app.logger.error(traceback.format_exc())
            flash("Error retrieving employees", "danger")
            return redirect(url_for('admin_data.dashboard'))

        if request.method == 'POST' and form.validate_on_submit():
            # Get form data
            file = form.file.data
            file_label = form.file_label.data
            employee_id = form.employee_id.data

            # Check if the file is present
            if not file:
                flash("No file provided", "danger")
                return redirect(url_for('document.upload_employee_document'))

            try:
                # Get uploader information
                uploader_name = "Unknown User"
                uploader_role = role

                # Try to get user info from central database
                from app.models.central import User as CentralUser
                try:
                    central_user = CentralUser.get_user_by_id(user_id, abort_on_not_found=False)
                    if central_user:
                        uploader_name = f"{central_user.get('first_name', '')} {central_user.get('last_name', '')}".strip() or central_user.get('username')
                        uploader_role = central_user.get('role', role)
                except Exception as e:
                    current_app.logger.warning(f"Could not retrieve central user info: {e}")

                # Format the uploader information
                uploader_info = f"{uploader_name} ({uploader_role})"

                # Upload the document
                current_app.logger.info(f"Uploading document for employee ID: {employee_id}")
                Document.upload_document(
                    db_session,
                    company_id,
                    'employee',  # Document type is always 'employee' for this route
                    file,
                    uploader_info,
                    employee_id=employee_id,
                    file_label=file_label
                )
                current_app.logger.info(f"Document uploaded successfully for employee ID: {employee_id}")

                # Get employee name for the success message
                employee = next((emp for emp in employees if str(emp['employee_id']) == employee_id), None)
                employee_name = f"{employee['first_name']} {employee['last_name']}" if employee else "Unknown Employee"

                message = f"Document uploaded successfully for {employee_name}"
                flash(message, 'success')
                return jsonify({'success': True, 'message': message}), 200

            # Redirect to view documents filtered for this employee

            except Exception as e:
                current_app.logger.error(f"Error uploading document: {e}")
                current_app.logger.error(traceback.format_exc())
                flash(f"Error uploading document: {str(e)}", "danger")
                return redirect(url_for('document.upload_employee_document'))

    # GET request - render the form
    try:
        current_app.logger.info("Rendering upload_employee_document template")
        return render_template('documents/upload_employee_document.html', form=form, role=role)
    except Exception as e:
        current_app.logger.error(f"Error rendering upload_employee_document template: {e}")
        current_app.logger.error(traceback.format_exc())
        flash("Error rendering the form. Please try again.", "danger")
        return redirect(url_for('document.view_documents'))
