from flask import Blueprint, jsonify, current_app, request, session, render_template, flash
from app.models.company import FeedBack
from app.routes.feedback.forms import FeedbackForm
from app.utils.db_connection import DatabaseConnection
import logging
from datetime import datetime
from app.decorators.hr_decorator import hr_required
from app.decorators.admin_decorator import admin_required
from app.helpers.company_helpers import CompanyHelpers
from app.models.central import User
from app.helpers.auxillary import Auxillary
from app.decorators.role_decorator import role_required

# Create a Blueprints
feedback = Blueprint('feedback', __name__)

@feedback.route('/add_feedback', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant', 'employee', 'supervisor'])
def add_feedback():
    """Add new feedback"""
    try:
        form = FeedbackForm()
    except Exception as e:
        logging.error(f"Error creating feedback form: {e}")
        return jsonify({'success':False,'message':'An error occurred. Please try again later.'}), 500

    if request.method == 'POST':
        if form.validate_on_submit():
            data = request.form
            subject = data.get('subject')
            message = data.get('message')
            # send the email
            try:
                my_email = '<EMAIL>'
                subject = 'New Feedback Submission'
                message = f"{message}, \n\n\nfor more information, login to your admin account"
                Auxillary.send_netpipo_email(subject, my_email, message)
            except Exception as e:
                current_app.logger.error(f"Error sending email: {e}")
            try:
                company_id = session.get('company_id')
                if not company_id:
                    current_app.logger("Company ID not found in session")
                    return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
            except Exception as e:
                current_app.logger.error(f"Error getting company ID: {e}")
                return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
            try:
                database_name = session.get('database_name')
                if not database_name:
                    return jsonify({'message': 'An error occurred. Please try again later.'}), 500
            except Exception as e:
                current_app.logger.error(f"Error getting database name: {e}")
                return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
            try:
                user_id = session.get('user_id')
                if not user_id:
                    return jsonify({'message': 'An error occurred. Please try again later.'}), 500
            except Exception as e:
                current_app.logger.error(f"Error getting user ID: {e}")
                return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
            try:
                # Initialize the database connection and save the employee record
                db_connection = DatabaseConnection()
            except Exception as e:
                current_app.logger.error(f"Error initializing database connection: {e}")
                return jsonify({'success':False,'message':'An error occurred. Please try again later.'}), 500
            try:
                with db_connection.get_session(database_name) as db_session:
                    result = FeedBack.insert_feedback(db_session, company_id, user_id, subject, message)
                    current_app.logger.info(f"Feedback saved successfully: {result}")
            except Exception as e:
                current_app.logger.error(f"Error saving feedback: {e}")
                return jsonify({'success':False,'message':'An error occurred. Please try again later.'}), 500
            
            message = 'Thank you for your feedback. You can submit another one.'
            flash(message, 'success')
            return jsonify({'success':True,'message':'Feedback saved successfully.'}), 200
    return render_template('feedback/add_feedback.html', form=form)

@feedback.route('/view_feedback', methods=['GET'])
@admin_required
def view_feedback():
    """View feedback from different companies."""

    # Get companies
    try:
        companies = CompanyHelpers.get_companies()
        if not companies:
            current_app.logger.error("No companies found")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
        current_app.logger.info(f"{len(companies)} companies found")
    except Exception as e:
        current_app.logger.error(f"Error getting companies: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    
    # Get feedback from different companies
    feedbacks = []

    for company in companies:
        try:
            database_name = company.database_name
            db_connection = DatabaseConnection()
            with db_connection.get_session(database_name) as db_session:
                feedback = FeedBack.get_feedbacks(db_session)
                feedbacks.append(feedback)
        except Exception as e:
            current_app.logger.error(f"Error getting feedback: {e}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    try:
        current_app.logger.info(f"{len(feedbacks)} feedbacks found")
    except Exception as e:
        current_app.logger.error(f"Error getting feedbacks: {e}")
    return render_template(
        'feedback/view_feedback.html', 
        feedbacks=feedbacks, 
        CompanyHelpers=CompanyHelpers, User=User)