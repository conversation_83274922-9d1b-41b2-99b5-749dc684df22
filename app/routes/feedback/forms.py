from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, FloatField, TextAreaField, SelectField
from wtforms.validators import DataRequired

class FeedbackForm(FlaskForm):
    """Form to add new feedback."""
    subject = SelectField('Subject', choices=[('', 'Select the Subject'),('General', 'General'), ('<PERSON><PERSON>lain<PERSON>', '<PERSON><PERSON>lain<PERSON>'), ('Suggestion', 'Suggestion'), ('Feature Request', 'Feature Request')], validators=[DataRequired()])
    message = TextAreaField('Description', validators=[DataRequired()])
    submit = SubmitField('Submit')