from flask import Blueprint, request, render_template, url_for, current_app
from app.models.company import User, Employee, Attendance, LeaveApplication, LeaveApproval
from app.helpers.company_helpers import CompanyHelpers
from app.decorators.role_decorator import role_required
from app.routes.leave_applications.forms import LeaveApplicationForm, LeaveApplicationApprovalForm
from flask import session, redirect, flash
from app.utils.db_connection import DatabaseConnection
from app.models.central import User as CentralUser
from app.models.company_approval_work_flow import ApprovalLog
from app.helpers.route_helpers import restrict_based_on_plan
from app.helpers import auxillary

leave_applications_v2 = Blueprint('leave_applications_v2', __name__)
#leave_applications_v2.before_request(restrict_based_on_plan)

@leave_applications_v2.route('/v2/apply_for_leave', methods=['GET', 'POST'])
@role_required('employee')
def apply_for_leave():
    """Apply for a leave or off day."""
    current_app.logger.info('In apply_for_leave route')
    try:
        form = LeaveApplicationForm()
        current_app.logger.info('Form created')
    except Exception as e:
        current_app.logger.error(f"Error creating form: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.employee_dashboard'))

    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.employee_dashboard'))

    database_name = session.get('database_name')
    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    if request.method == 'POST':
        if not form.validate_on_submit():
            current_app.logger.error('Invalid form data: %s', form.errors)
            flash('Invalid form data', 'danger')
            return redirect(url_for('leave_applications_v2.apply_for_leave'))

        start_date = form.start_date.data
        end_date = form.end_date.data
        leave_type = form.leave_type.data
        reason = form.reason.data
        current_app.logger.info(f"form data: {form.data}")

        # Initialize the Database Connection
        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                # Get the employee record
                user = User.get_user_by_id(db_session, user_id)
                employee_id = str(user['employee_id'])

                if leave_type == "annual_leave":
                    try:
                        days_difference = auxillary.Auxillary.calculate_days_difference(start_date, end_date)
                        current_app.logger.info(f"Days difference: {days_difference}")
                    except Exception as e:
                        current_app.logger.error(f"Error calculating days difference: {e}")
                        flash("An error occurred. Please try again later.", "danger")
                        return redirect(url_for('leave_applications_v2.apply_for_leave'))

                    try:
                        is_eligible, message = Employee.can_request_leave_or_off(db_session, employee_id, days_difference)
                        current_app.logger.info(f"Is eligible: {is_eligible}")
                        current_app.logger.info(f"Message: {message}")
                    except Exception as e:
                        current_app.logger.error(f"Error checking eligibility: {e}")
                        flash("An error occurred. Please try again later.", "danger")
                        return redirect(url_for('leave_applications_v2.apply_for_leave'))

                    if not is_eligible:
                        flash(message, "danger")
                        current_app.logger.info(f"Error applying leave: {message}")
                        return redirect(url_for('leave_applications_v2.apply_for_leave'))

                current_app.logger.info(f"User: {user}")
                current_app.logger.info(f"Employee ID: {employee_id}")

                # Save the leave application
                try:
                    result = LeaveApplication.insert_leave_application(
                        db_session, employee_id, leave_type, start_date, end_date, reason
                    )
                    current_app.logger.info(f"Leave Application saved: {result}")
                except Exception as e:
                    current_app.logger.error(f"Error saving leave application: {e}")
                    flash("An error occurred. Please try again later.", "danger")
                    return redirect(url_for('leave_applications_v2.apply_for_leave'))

                current_app.logger.info(f"Leave Application saved: {result}")
                if not result:
                    flash('An error occurred. Please try again later.', 'danger')
                    return redirect(url_for('leave_applications_v2.apply_for_leave'))

                flash('Leave application submitted successfully', 'success')
                return redirect(url_for('leave_applications_v2.view_leave_applications'))
            except Exception as e:
                current_app.logger.error(f"Error saving leave application: {e}")
                flash('An error occurred. Please try again later.', 'danger')
                return redirect(url_for('leave_applications_v2.apply_for_leave'))

    current_app.logger.info('Rendering apply_for_leave_v2.html')
    return render_template('leave_applications/apply_for_leave_v2.html', form=form)

@leave_applications_v2.route('/v2/view_leave_applications', methods=['GET'])
@role_required('employee')
def view_leave_applications():
    """View all leave applications."""
    current_app.logger.info('In view_leave_applications route')
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.employee_dashboard'))

    database_name = session.get('database_name')
    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")
    role = session.get('role')

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the employee record
        try:
            user = session.get('user')
            current_app.logger.info(f"User info: {user}")
            employee_id = user['employee_id']
            current_app.logger.info(f"Employee ID: {employee_id}")
        except Exception as e:
            current_app.logger.error(f"Error getting employee ID: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.employee_dashboard'))

        # Get the employee's leave applications
        try:
            leave_applications = LeaveApplication.get_leave_application_for_employee(db_session, employee_id)
            current_app.logger.info(f"Leave Applications: {leave_applications}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave applications: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.employee_dashboard'))

    try:
        current_app.logger.info('Rendering view_leave_applications_v2.html')
        return render_template('leave_applications/view_leave_applications_v2.html', leave_applications=leave_applications)
    except Exception as e:
        current_app.logger.error(f"Error rendering view_leave_applications_v2.html: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.employee_dashboard'))

@leave_applications_v2.route('/v2/get_leave_applications_for_employees', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def get_leave_application_for_employees():
    """Get all leave applications for employees in a company."""
    current_app.logger.info('In get_leave_application_for_employees route')
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

    database_name = session.get('database_name')
    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get all leave applications
        try:
            leave_applications = LeaveApplication.view_leave_applications(db_session)
            current_app.logger.info(f"Leave Applications: {leave_applications}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave applications: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

    try:
        current_app.logger.info('Rendering get_leave_applications_v2.html')
        return render_template('leave_applications/get_leave_applications_v2.html',
                               leave_applications=leave_applications)
    except Exception as e:
        current_app.logger.error(f"Error rendering get_leave_applications_v2.html: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@leave_applications_v2.route('/v2/approve_leave_application/<leave_application_id>', methods=['GET', 'POST'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def approve_leave_application(leave_application_id):
    """Approve a leave application."""
    form = LeaveApplicationApprovalForm()
    current_app.logger.info('In approve_leave_application route')
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

    database_name = session.get('database_name')
    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the leave application
        try:
            leave_application = LeaveApplication.get_leave_application_by_id(db_session, leave_application_id)
            current_app.logger.info(f"Leave Application: {leave_application}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave application: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('leave_applications_v2.get_leave_application_for_employees'))

        # Get the leave details
        try:
            leave_id = leave_application['leave_id']
            leave_type = leave_application['leave_type']
            approver_id = user_id
            approver_role = session.get('role')
        except Exception as e:
            current_app.logger.error(f"Error getting leave details: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('leave_applications_v2.get_leave_application_for_employees'))

        if request.method == 'POST':
            if not form.validate_on_submit():
                current_app.logger.error('Invalid form data: %s', form.errors)
                flash('Invalid form data', 'danger')
                return redirect(url_for('leave_applications_v2.approve_leave_application', leave_application_id=leave_application_id))

            approval = form.approval.data
            remarks = form.remarks.data
            current_app.logger.info(f"approval: {approval}")
            current_app.logger.info(f"remarks: {remarks}")

            # Save the leave approval
            try:
                current_app.logger.info('Saving leave approval')
                result = LeaveApproval.approve_leave_application(
                    db_session, leave_id, approver_id, approver_role,
                    approval, remarks=remarks, leave_type=leave_type
                )
                current_app.logger.info(f"Leave Approval saved: {result}")

                if len(result) > 0:
                    if 'An error occurred' in result:
                        flash(f'{result}', 'danger')
                        return redirect(url_for('leave_applications_v2.approve_leave_application', leave_application_id=leave_application_id))

                    if f'Leave application {approval} successfully by' in result:
                        # Update the Approval logs
                        try:
                            log = ApprovalLog.create_log(
                                db_session, leave_application_id,
                                approver_role, approver_id, approval
                            )
                            current_app.logger.info(f"Approval Log saved: {log}")
                        except Exception as e:
                            current_app.logger.error(f"Error saving approval log: {e}")

                    message = f"{result}"
                    flash(f"{message}", 'success')
                    return redirect(url_for('leave_applications_v2.view_approval_logs'))
                else:
                    flash('An error occurred. Please try again later.', 'danger')
                    return redirect(url_for('leave_applications_v2.approve_leave_application', leave_application_id=leave_application_id))
            except Exception as e:
                current_app.logger.error(f"Error saving leave approval: {e}")
                flash('An error occurred. Please try again later.', 'danger')
                return redirect(url_for('leave_applications_v2.approve_leave_application', leave_application_id=leave_application_id))

    try:
        current_app.logger.info('Rendering approve_leave_application_v2.html')
        return render_template('leave_applications/approve_leave_application_v2.html', form=form, leave_application=leave_application)
    except Exception as e:
        current_app.logger.error(f"Error rendering approve_leave_application_v2.html: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@leave_applications_v2.route('/v2/view_leave_approvals', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def view_leave_approvals():
    """View all leave approvals."""
    current_app.logger.info('In view_leave_approvals route')
    current_app.logger.info(f"User role: {session.get('role')}")
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()
    current_app.logger.info("Database connection initialized")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info("Getting leave approvals")
            leave_approvals = LeaveApproval.get_leave_approvals(db_session)
            current_app.logger.info(f"Leave approvals: {leave_approvals}")
            current_app.logger.info(f"Number of leave approvals: {len(leave_approvals)}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave approvals: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        try:
            current_app.logger.info('Rendering view_leave_approvals_v2.html')
            current_app.logger.info(f"Template variables: leave_approvals={bool(leave_approvals)}")
            return render_template('leave_applications/view_leave_approvals_v2.html',
                                   leave_approvals=leave_approvals)
        except Exception as e:
            current_app.logger.error(f"Error rendering view_leave_approvals_v2.html: {e}")
            current_app.logger.error(f"Exception details: {str(e)}")
            import traceback
            current_app.logger.error(f"Traceback: {traceback.format_exc()}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

@leave_applications_v2.route('/v2/view_approval_logs', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def view_approval_logs():
    """View all approval logs."""
    current_app.logger.info('In view_approval_logs route')
    current_app.logger.info(f"User role: {session.get('role')}")
    database_name = session.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()
    current_app.logger.info("Database connection initialized")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info("Getting approval logs")
            approval_logs = ApprovalLog.get_all_logs(db_session)
            current_app.logger.info(f"Approval logs: {approval_logs}")
            current_app.logger.info(f"Number of approval logs: {len(approval_logs)}")

            # Get approver names for each log
            for log in approval_logs:
                approver_id = log.get('approver_id')
                approver_role = log.get('approver_role')

                if approver_id:
                    # Use the existing get_approver_name method from LeaveApproval class
                    try:
                        current_app.logger.info(f"Getting approver name for ID: {approver_id}, role: {approver_role}")
                        approver_name = LeaveApproval.get_approver_name(approver_id, approver_role, db_session)

                        if approver_name:
                            log['approver_name'] = approver_name
                            current_app.logger.info(f"Set approver name to: {log['approver_name']}")
                        else:
                            log['approver_name'] = 'Unknown'
                            current_app.logger.info(f"Could not get approver name, using 'Unknown'")
                    except Exception as e:
                        current_app.logger.error(f"Error getting approver name: {e}")
                        log['approver_name'] = 'Unknown'
                else:
                    log['approver_name'] = 'System'
                    current_app.logger.info("No approver ID, using 'System' as name")

        except Exception as e:
            current_app.logger.error(f"Error getting approval logs: {e}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        try:
            # Group approval logs by application_id
            grouped_logs = {}
            for log in approval_logs:
                app_id = log.get('application_id')
                if app_id not in grouped_logs:
                    grouped_logs[app_id] = {
                        'application_id': app_id,
                        'approvers': []
                    }

                # Add approver info to the group
                grouped_logs[app_id]['approvers'].append({
                    'approver_role': log.get('approver_role'),
                    'approver_name': log.get('approver_name'),
                    'status': log.get('status'),
                    'created_at': log.get('created_at')
                })

            # Get leave application details for each group
            with db_connection.get_session(database_name) as db_session:
                for app_id, group in grouped_logs.items():
                    try:
                        leave_app = LeaveApplication.get_leave_application_by_id(db_session, app_id)
                        if leave_app:
                            # Get employee name
                            if 'employee_name' in leave_app:
                                group['employee_name'] = leave_app.get('employee_name', 'Unknown')
                            else:
                                # Try to get employee name from Employee object if available
                                employee_id = leave_app.get('employee_id')
                                if employee_id:
                                    employee = Employee.get_employee_by_id(db_session, employee_id)
                                    if employee:
                                        group['employee_name'] = f"{employee.get('first_name', '')} {employee.get('last_name', '')}"
                                    else:
                                        group['employee_name'] = 'Unknown'
                                else:
                                    group['employee_name'] = 'Unknown'

                            # Get leave type
                            group['leave_type'] = leave_app.get('leave_type', 'Unknown')

                            # Get start and end dates
                            if 'start_date' in leave_app and 'end_date' in leave_app:
                                group['start_date'] = leave_app.get('start_date', 'Unknown')
                                group['end_date'] = leave_app.get('end_date', 'Unknown')
                            elif 'time_off_begin_date' in leave_app and 'time_off_end_date' in leave_app:
                                group['start_date'] = leave_app.get('time_off_begin_date', 'Unknown')
                                group['end_date'] = leave_app.get('time_off_end_date', 'Unknown')
                            else:
                                group['start_date'] = 'Unknown'
                                group['end_date'] = 'Unknown'
                    except Exception as e:
                        current_app.logger.error(f"Error getting leave application details: {e}")
                        group['employee_name'] = 'Unknown'
                        group['leave_type'] = 'Unknown'
                        group['start_date'] = 'Unknown'
                        group['end_date'] = 'Unknown'

            # Convert the dictionary to a list for the template
            grouped_logs_list = list(grouped_logs.values())

            # Add a sequential ID to each leave application for display purposes
            for i, group in enumerate(grouped_logs_list, 1):
                group['display_id'] = f"LEA-{i:04d}"

            current_app.logger.info('Rendering view_approval_logs_v2.html')
            current_app.logger.info(f"Template variables: grouped_logs_list={bool(grouped_logs_list)}")
            return render_template('leave_applications/view_approval_logs_v2.html', grouped_logs=grouped_logs_list)
        except Exception as e:
            current_app.logger.error(f"Error rendering view_approval_logs_v2.html: {e}")
            current_app.logger.error(f"Exception details: {str(e)}")
            import traceback
            current_app.logger.error(f"Traceback: {traceback.format_exc()}")
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))


@leave_applications_v2.route('/v2/increment_annual_leave_balance', methods=['GET'])
def increment_annual_leave_balance():
    """
    This endpoint is scheduled to run every 1st day of the month at 12 am.

    Increments the annual leave balance for employees across all companies by
    retrieving the list of companies, updating employee records.

    Returns:
        A success message or an empty page in case of an error.
    """
    try:
        companies_db = CompanyHelpers.get_database_names()
        all_employees = CompanyHelpers.get_employees_for_all_companies(companies_db)

        updated_rows = Employee.update_employees_for_all_companies(companies_db, all_employees)
        flash(f"Successfully updated {updated_rows} employee records", "success")
        return redirect(url_for('admin_data.dashboard'))
    except Exception as e:
        current_app.logger.error(f"Error incrementing annual leave balance: {e}")
        flash('An error occurred while incrementing annual leave balance.', 'danger')
        return redirect(url_for('admin_data.dashboard'))

@leave_applications_v2.route("/v2/increment_extra_leave_days", methods=['GET'])
def increment_extra_leave_days():
    """
    This endpoint is scheduled to run every 12 am every day of the year(based on employee hire date) at 12 am.

    Increments the extra leave days for employees based on their hire date.
    Employees who have completed a year of service will have their extra_leave_days incremented.
    """
    try:
        companies_db = CompanyHelpers.get_database_names()
        all_employees = CompanyHelpers.get_employees_for_all_companies(companies_db)

        updated_rows = Employee.update_extra_leave_days_for_all_companies(companies_db, all_employees)
        flash(f"Successfully updated extra leave days for {updated_rows} employee records", "success")
        return redirect(url_for('admin_data.dashboard'))
    except Exception as e:
        current_app.logger.error(f"Error incrementing extra leave days: {e}")
        flash('An error occurred while incrementing extra leave days.', 'danger')
        return redirect(url_for('admin_data.dashboard'))