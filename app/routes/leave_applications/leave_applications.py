from flask import Blueprint, request, jsonify, render_template, url_for, current_app
from app.models.company import User, Employee, Attendance, LeaveApplication, LeaveApproval
from app.helpers.company_helpers import CompanyHelpers
from app.decorators.role_decorator import role_required
from app.routes.leave_applications.forms import LeaveApplicationForm, LeaveApplicationApprovalForm
from flask import session, redirect, flash
from app.utils.db_connection import DatabaseConnection
from app.models.central import User as CentralUser
from app.models.company_approval_work_flow import ApprovalLog
from app.helpers.route_helpers import restrict_based_on_plan
from app.helpers import auxillary

leave_applications = Blueprint('leave_applications', __name__)
leave_applications.before_request(restrict_based_on_plan)

@leave_applications.route('/apply_for_leave', methods=['GET', 'POST'])
@role_required('employee')
def apply_for_leave():
    """Apply for a leave or off day."""
    current_app.logger.info('In apply_for_leave route')
    try:
        form = LeaveApplicationForm()
        current_app.logger.info('Form created')
    except Exception as e:  
        current_app.logger.error(f"Error creating form: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    database_name = session.get('database_name')

    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")


    if request.method == 'POST' :
        if not form.validate_on_submit():
            current_app.logger.error('Invalid form data: %s', form.errors)
            return jsonify({'error': 'Invalid form data'}), 400
        start_date = form.start_date.data
        end_date = form.end_date.data
        leave_type = form.leave_type.data
        reason = form.reason.data
        current_app.logger.info(f"form data: {form.data}")
        
        # Initialize the Database Connection
        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                # Get the employee record            
                user = User.get_user_by_id(db_session, user_id)
                employee_id = str(user['employee_id'])
                leave_type_for_check = "None"
                if leave_type == "annual_leave":
                    try:
                        days_difference = auxillary.Auxillary.calculate_days_difference(start_date, end_date)
                        current_app.logger.info(f"Days difference: {days_difference}")
                    except Exception as e:
                        current_app.logger.error(f"Error calculating days difference: {e}")
                        flash("An error occurred. Please try again later.", "danger")
                        #return jsonify({'error': 'An error occurred. Please try again later.'}), 400
                    try:
                        is_eligible, message = Employee.can_request_leave_or_off(db_session, employee_id, days_difference)
                        current_app.logger.info(f"Is eligible: {is_eligible}")
                        current_app.logger.info(f"Message: {message}")
                    except Exception as e:
                        current_app.logger.error(f"Error checking eligibility: {e}")
                        flash("An error occurred. Please try again later.", "danger")
                        #return jsonify({'error': 'An error occurred. Please try again later.'}), 400

                    if not is_eligible:
                        flash(message, "danger")
                        current_app.logger.info(f"Error applying leave: {message}")
                        #return jsonify({'error': message}), 400
                
                current_app.logger.info(f"User: {user}")
                current_app.logger.info(f"Employee ID: {employee_id}")
                
                # Save the leave application
                try:
                    result = LeaveApplication.insert_leave_application(db_session, employee_id, 
                                                                   leave_type, start_date, 
                                                                   end_date, reason)
                    current_app.logger.info(f"Leave Application saved: {result}")
                except Exception as e:
                    current_app.logger.error(f"Error saving leave application: {e}")
                    flash("An error occurred. Please try again later.", "danger")
                    #return jsonify({'error': 'An error occurred. Please try again later.'}), 400
                current_app.logger.info(f"Leave Application saved: {result}")
                if not result:
                    flash('An error occurred. Please try again later.', 'danger')
                    return jsonify({'success': False, 'message': 'An error occurred. Please try again later.'}), 400
                flash('Leave application submitted successfully', 'success')
                return jsonify({'success': True, 'message': 'Leave application submitted successfully'}), 200
            except Exception as e:
                current_app.logger.error(f"Error saving leave application: {e}")
                return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    current_app.logger.info('Rendering apply_for_leave.html')
    return render_template('leave_applications/apply_for_leave.html', form=form)
    
@leave_applications.route('/view_leave_applications', methods=['GET'])
@role_required(['employee', 'manager', 'company_hr', 'accountant', 'hr'])
def view_leave_applications():
    """View all leave applications."""
    current_app.logger.info('In view_leave_applications route')
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    database_name = session.get('database_name')

    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the employee record
        try:
            user = session.get('user')
            employee_id = user['employee_id']
            current_app.logger.info(f"User info: {user}")
            current_app.logger.info(f"Employee ID: {employee_id}")
        except Exception as e:
            current_app.logger.error(f"Error getting employee ID: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400

        # Get the employee's leave applications
        try:
            leave_applications = LeaveApplication.get_leave_application_for_employee(db_session, employee_id)
            current_app.logger.info(f"Leave Applications: {leave_applications}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave applications: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    try:
        current_app.logger.info('Rendering view_leave_applications.html')
        return render_template('leave_applications/view_leave_applications.html', leave_applications=leave_applications)
    except Exception as e:
        current_app.logger.error(f"Error rendering view_leave_applications.html: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    
@leave_applications.route('/get_leave_applications_for_employees', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def get_leave_application_for_employees():
    """Get all leave applications for employees in a company."""
    current_app.logger.info('In get_leave_application_for_employees route')
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    database_name = session.get('database_name')

    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get all leave applications
        try:
            leave_applications = LeaveApplication.view_leave_applications(db_session)
            current_app.logger.info(f"Leave Applications: {leave_applications}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave applications: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    try:
        current_app.logger.info('Rendering view_leave_applications.html')
        return render_template('leave_applications/get_leave_applications.html', 
                               leave_applications=leave_applications)
    except Exception as e:
        current_app.logger.error(f"Error rendering view_leave_applications.html: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    
@leave_applications.route('/approve_leave_application/<leave_application_id>', methods=['GET', 'POST'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def approve_leave_application(leave_application_id):
    """Approve a leave application."""
    form = LeaveApplicationApprovalForm()
    current_app.logger.info('In approve_leave_application route')
    try:
        # Retrieve important info from the session
        user_id = session.get('user_id')
        current_app.logger.info(f"User ID: {user_id}")
    except Exception as e:
        current_app.logger.error(f"Error getting user ID: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    database_name = session.get('database_name')

    current_app.logger.info(f"User ID: {user_id}")
    current_app.logger.info(f"Database Name: {database_name}")

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the leave application
        try:
            leave_application = LeaveApplication.get_leave_application_by_id(db_session, leave_application_id)
            current_app.logger.info(f"Leave Application: {leave_application}")
        except Exception as e:
            current_app.logger.error(f"Error getting leave application: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400

        # Get the leave details
        try:
            employee_id = leave_application['employee_id']
            leave_id = leave_application['leave_id']
            leave_type = leave_application['leave_type']
            approver_id = user_id
            approver_role = session.get('role')
        except Exception as e:
            current_app.logger.error(f"Error getting leave details: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400
        
        if request.method == 'POST':
            if not form.validate_on_submit():
                current_app.logger.error('Invalid form data: %s', form.errors)
                return jsonify({'error': 'Invalid form data'}), 400
            approval = form.approval.data
            remarks = form.remarks.data
            current_app.logger.info(f"approval: {approval}")
            current_app.logger.info(f"remarks: {remarks}")
            # Save the leave approval
            try:
                current_app.logger.info('Saving leave approval')
                result = LeaveApproval.approve_leave_application(db_session, leave_id, approver_id, 
                                                             approver_role, approval, remarks=remarks, leave_type=leave_type)
                current_app.logger.info(f"Leave Approval saved: {result}")
                if len(result) > 0 :
                    if 'An error occurred' in result:
                        flash(f'{result}', 'danger')
                        return jsonify({'success': False, 'message': f"{result}"}), 400
                    
                    if f'Leave application {approval} successfully by' in result:
                        #Update the Approval logs
                        try:
                            log = ApprovalLog.create_log(
                                db_session, leave_application_id, 
                                approver_role, approver_id, approval)
                            current_app.logger.info(f"Approval Log saved: {log}")
                        except Exception as e:
                            current_app.logger.error(f"Error saving approval log: {e}")
                    message = f"{result}"
                    flash(f"{message}", 'success')
                    return jsonify({'success': True, 'message': f"{message}"}), 200
                else:
                    flash('An error occurred. Please try again later.', 'danger')
                    return jsonify({'success': False, 'message': 'An error occurred. Please try again later.'}), 400
            except Exception as e:
                current_app.logger.error(f"Error saving leave approval: {e}")
                return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    try:
        current_app.logger.info('Rendering approve_leave_application.html')
        return render_template('leave_applications/approve_leave_application.html', form=form, leave_application=leave_application)
    except Exception as e:
        current_app.logger.error(f"Error rendering approve_leave_application.html: {e}")
        return jsonify({'error': 'An error occurred. Please try again later.'}), 400
    
@leave_applications.route('/view_leave_approvals', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def view_leave_approvals():
    """View all leave approvals."""
    current_app.logger.info('In view_leave_approvals route')
    database_name = session.get('database_name')

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            leave_approvals = LeaveApproval.get_leave_approvals(db_session)                        
        except Exception as e:
            current_app.logger.error(f"Error getting leave approvals: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400
        try:
            current_app.logger.info('Rendering view_leave_approvals.html')
            return render_template('leave_applications/view_leave_approvals.html', 
                                   leave_approvals=leave_approvals, 
                                   db_session=db_session,
                                   LeaveApproval=LeaveApproval)
        except Exception as e:
            current_app.logger.error(f"Error rendering view_leave_approvals.html: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400
        
@leave_applications.route('/view_approval_logs', methods=['GET'])
@role_required(['manager', 'company_hr', 'accountant', 'hr', 'supervisor'])
def view_approval_logs():
    """View all approval logs."""
    current_app.logger.info('In view_approval_logs route')
    database_name = session.get('database_name')

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            approval_logs = ApprovalLog.get_all_logs(db_session)    
            current_app.logger.info(f"approval logs: {approval_logs}")                    
        except Exception as e:
            current_app.logger.error(f"Error getting approval logs: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400
        try:
            current_app.logger.info('Rendering view_approval_logs.html')
            return render_template('leave_applications/view_approval_logs.html', approval_logs=approval_logs)
        except Exception as e:
            current_app.logger.error(f"Error rendering view_approval_logs.html: {e}")
            return jsonify({'error': 'An error occurred. Please try again later.'}), 400

       
@leave_applications.route('/increment_annual_leave_balance', methods=['POST'])
def increment_annual_leave_balance():
    """
    This endpoint is scheduled to run every 1st day of the month at 12 am.

    Increments the annual leave balance for employees across all companies by 
    retrieving the list of companies, updating employee records.

    Returns:
        JSON response indicating success or an empty dictionary in case of an error.
    """
    
    try:
        companies_db = CompanyHelpers.get_database_names()
        all_employees = CompanyHelpers.get_employees_for_all_companies(companies_db)

        updated_rows = Employee.update_employees_for_all_companies(companies_db, all_employees)
        return jsonify(message="Good", updated_rows=updated_rows)
    except Exception as e:
        current_app.logger.error(f"Error incrementing annual leave balance: {e}")
        return {}
    
@leave_applications.route("/increment_extra_leave_days", methods=['POST'])
def increment_extra_leave_days():
    """
    This endpoint is scheduled to run every 12 am every day of the year(based on employee hire date) at 12 am.

    Increments the extra leave days for employees based on their hire date. 
    Employees who have completed a year of service will have their extra_leave_days incremented.
    """
    try:
        companies_db = CompanyHelpers.get_database_names()
        all_employees = CompanyHelpers.get_employees_for_all_companies(companies_db)

        updated_rows = Employee.update_extra_leave_days_for_all_companies(companies_db, all_employees)
        return jsonify(message="Extra leave days incremented successfully", updated_rows=updated_rows)
    except Exception as e:
        current_app.logger.error(f"Error incrementing extra leave days: {e}")
        return {}