from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, TextAreaField, SubmitField, SelectField, DateField
from wtforms.validators import DataRequired, Optional

class LeaveApplicationForm(FlaskForm):
    start_date = DateField('Start Date', validators=[DataRequired()])
    end_date = DateField('End Date', validators=[DataRequired()])
    leave_type = SelectField('Leave Type', choices=[('leave', 'Leave'), ('off', 'Off'), ('annual_leave', 'Annual Leave')], validators=[DataRequired()])
    reason = TextAreaField('Reason', validators=[DataRequired()])
    submit = SubmitField('Submit')

class LeaveApplicationApprovalForm(FlaskForm):
    approval = SelectField('Approval', choices=[('approved', 'Approve'), ('rejected', 'Reject')], validators=[DataRequired()])
    remarks = TextAreaField('Remarks', validators=[Optional()])
    submit = SubmitField('Submit')