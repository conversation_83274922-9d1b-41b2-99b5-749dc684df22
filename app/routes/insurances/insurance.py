from flask import Blueprint, request, jsonify, render_template, redirect, url_for, session, flash
from app.models.company import Insurance
from app.routes.insurances.forms import InsuranceForm
from app.decorators.hr_decorator import hr_required
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection
import logging
from datetime import datetime
from app import current_app
from app.decorators.role_decorator import role_required

insurance_bp = Blueprint('insurance', __name__)

@insurance_bp.route('/add_insurance', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_insurance():
    """Add new insurance company"""
    try:  
        form = InsuranceForm()
    except Exception as e:
        logging.error(f"Error creating insurance form: {e}")
        flash("An error occurred. Please try again later.", 'danger')
        return redirect(url_for('admin_data.dashboard'))
    
    if request.method == 'POST':

        data = request.form
        insurance_name = data.get('insurance_name')
        employee_rate = float(data.get('employee_rate'))/100
        employer_rate = float(data.get('employer_rate'))/100

        # Make sure the insurance name is lowercase
        insurance_name = insurance_name.lower()
        try:
            company_id = session.get('company_id')
            if not company_id:
                current_app.logger("Company ID not found in session")
                flash("An error occurred. Please try again later.", 'danger')
                return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
        except Exception as e:
            current_app.logger.error(f"Error getting company ID: {e}")
            flash("An error occurred. Please try again later.", 'danger')
            return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
        try:
            database_name = session.get('database_name')
            if not database_name:
                return jsonify({'message': 'An error occurred. Please try again later.'}), 500
        except Exception as e:
            current_app.logger.error(f"Error getting database name: {e}")
            flash("An error occurred. Please try again later.", 'danger')
            return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200
        try:
            # Initialize the database connection and save the employee record
            db_connection = DatabaseConnection()
        except Exception as e:
            current_app.logger.error(f"Error initializing database connection: {e}")
            flash(f'An error occurred. Please try again later: {e}.', 'danger')
            return jsonify({'success':False,'message':'An error occurred. Please try again later.'}), 500
        try:
            with db_connection.get_session(database_name) as db_session:
                
                new_insurance = Insurance(
                    insurance_name=insurance_name,
                    employee_rate=employee_rate,
                    employer_rate=employer_rate,
                    created_at=datetime.now()
                )
                db_session.add(new_insurance)
                db_session.commit()
                message = "Insurance added successfully"
                flash(message, 'success')
                return jsonify({'success':True,'message': message}), 200
        except Exception as e:
            current_app.logger.error(f"Error adding insurance company: {e}")
            flash("An error occurred. Please try again later.", 'danger')
            return jsonify({'success':True,'message':'An error occurred. Please try again later.'}), 200

    database_name = session.get('database_name')
    # Initialize the database connection and fetch deductions
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        insurances = db_session.query(Insurance).all()
        if not insurances:
            logging.error("No insurance companies found")                             
        insurances_list = [insurance.to_dict() for insurance in insurances if insurance] 
        for insurance in insurances_list:
            id = insurance['insurance_id']
            
    try:
        return render_template('insurance/insert_insurance.html', form=form, insurances_list=insurances_list)
    except Exception as e:
        logging.error(f"Error rendering template: {e}")
        flash("An error occurred. Please try again later.", 'danger')
        return redirect(url_for('admin_data.dashboard'))
    
@insurance_bp.route('/update_insurance/<uuid:insurance_id>', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_insurance(insurance_id):
    """Update the insurance company."""
    try:
        database_name = session.get('database_name')
        if database_name is None:
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        db_connection = DatabaseConnection()

        # Fetch the insurance object within a session context
        with db_connection.get_session(database_name) as db_session:
            insurance_to_update = Insurance.get_insurance_by_id(db_session, insurance_id)
            # Multipy the rates by 100 to get the percentage
            insurance_to_update.employee_rate = insurance_to_update.employee_rate * 100
            insurance_to_update.employer_rate = insurance_to_update.employer_rate * 100
            if insurance_to_update is None:
                flash("Insurance company not found.", 'danger')
                return redirect(url_for('admin_data.dashboard'))

            form = InsuranceForm(obj=insurance_to_update)
           
            if request.method == 'POST':
                data = request.form
                insurance_name = data.get('insurance_name')
                employee_rate = float(data.get('employee_rate'))/100
                employer_rate = float(data.get('employer_rate'))/100
                # update the insurance company data in the database
                insurance_to_update.insurance_name = insurance_name.lower()
                insurance_to_update.employee_rate = employee_rate
                insurance_to_update.employer_rate = employer_rate
                
                try:
                    db_session.commit()
                    message = "Insurance company updated successfully"
                    flash(message, 'success')
                    return jsonify({'success':True,'message': message}), 200
                except Exception as e:
                    db_session.rollback()
                    logging.error(f"An error occurred while updating insurance company: {e}")
                    flash('An error occurred. Please try again later.', 'danger')
                    return jsonify({'success':False,'message': 'An error occurred. Please try again later.'}), 500
        return render_template('insurance/update_insurance.html', form=form)
    except Exception as e:
        logging.error(f"An error occurred while updating insurance company: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return "something went wrong"
    
@insurance_bp.route('/delete_insurance/<uuid:insurance_id>', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_insurance(insurance_id):
    """Delete the insurance company."""
    try:
        database_name = session.get('database_name')
        if database_name is None:
            flash('An error occurred. Please try again later.', 'danger')
            return redirect(url_for('admin_data.dashboard'))

        db_connection = DatabaseConnection()

        # Fetch the insurance object within a session context
        with db_connection.get_session(database_name) as db_session:
            insurance_to_delete = Insurance.get_insurance_by_id(db_session, insurance_id)
            if insurance_to_delete is None:
                flash("Insurance company not found.", 'danger')
                return redirect(url_for('admin_data.dashboard'))

            db_session.delete(insurance_to_delete)
            db_session.commit()
            flash('Insurance company deleted successfully', 'success')
            return jsonify({'success':True,'message':'Insurance company deleted successfully'}), 200
    except Exception as e:
        logging.error(f"An error occurred while deleting insurance company: {e}")
        flash('An error occurred. Please try again later.', 'danger')
        return redirect(url_for('admin_data.dashboard'))
    
@insurance_bp.route('/insurance', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def insurance():
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))
    
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            insurances = Insurance.get_insurances(db_session)
            print("departments: ", insurances)
            if not insurances:
                #flash('No departments found!', 'danger')
                current_app.logger.error('No insurances found!')
                insurances = []
            return render_template('insurance/insurance.html', insurances=insurances)
        except Exception as e:
            flash('An error occurred while fetching insurances!', 'danger')
            current_app.logger.error(f'An error occurred while fetching insurances: {str(e)}')
            return redirect(url_for('admin_data.dashboard'))


