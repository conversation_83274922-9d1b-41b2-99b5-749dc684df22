from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, FloatField
from wtforms.validators import DataRequired

class InsuranceForm(FlaskForm):
    """Form to add new insurance company."""
    insurance_name = StringField('Insurance  Name', validators=[DataRequired()])
    employee_rate = FloatField('Employee Rate(%)', validators=[DataRequired()])
    employer_rate = FloatField('Employer Rate(%)', validators=[DataRequired()])
    submit = SubmitField('Update')
    