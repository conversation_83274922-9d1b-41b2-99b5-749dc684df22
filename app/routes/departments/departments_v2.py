from flask import render_template, url_for, flash, redirect, Blueprint, current_app, request, session
from app.decorators.hr_decorator import hr_required
from app.models.company import Departments
from . forms import DepartmentForm, DepartmentUpdateForm
from app.utils.db_connection import DatabaseConnection
from app.decorators.role_decorator import role_required

departments_v2_bp = Blueprint('departments_v2', __name__)

@departments_v2_bp.route('/v2/add_department', methods=['GET', 'POST'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_department():
    form = DepartmentForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            message = 'An error occurred while validating the form'
            current_app.logger.error(message)
            flash(message, 'danger')
            return redirect(url_for('departments_v2.departments'))
        data = request.form
        department_name = data.get('department_name')
        try:
            department_name = department_name.strip().lower()
        except Exception as e:
            department_name = department_name
            current_app.logger.error(f'An error occurred while stripping department name: {str(e)}')
        database_name = session.get('database_name')
        if database_name is None:
            current_app.logger.error("An error occurred while fetching company database name")
            message = f'An error occurred while fetching company database name'
            flash(message, 'danger')
            return redirect(url_for('departments_v2.departments'))
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            try:
                # Add department to the database
                added = Departments.insert_department(db_session, department_name)
                if added == "Department already exists":
                    message = f'{department_name.upper()} department already exists!'
                    flash(message, 'warning')
                    current_app.logger.error(message)
                    return redirect(url_for('departments_v2.add_department'))
                elif added:
                    message = f'Department {department_name.upper()} added successfully!'
                    flash(message, 'success')
                    current_app.logger.info(message)
                    return redirect(url_for('departments_v2.departments'))
                else:
                    message = f'An error occurred while adding the department'
                    flash(message, 'danger')
                    current_app.logger.error(message)
                    return redirect(url_for('departments_v2.departments'))
            except Exception as e:
                message = f'An error occurred while adding the department: {str(e)}'
                flash(message, 'danger')
                current_app.logger.error(message)
                return redirect(url_for('departments_v2.departments'))
    try:
        return render_template('departments/add_department_v2.html', form=form)
    except Exception as e:
        flash('An error occurred while rendering the page!', 'danger')
        current_app.logger.error(f'An error occurred while rendering the page: {str(e)}')
        return redirect(url_for('admin_data.dashboard'))

@departments_v2_bp.route('/v2/departments', methods=['GET'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def departments():
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))

    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            departments = Departments.get_departments(db_session)
            print("departments: ", departments)
            if not departments:
                #flash('No departments found!', 'danger')
                current_app.logger.error('No departments found!')
                departments = []
            return render_template('departments/departments_v2.html', departments=departments)
        except Exception as e:
            flash('An error occurred while fetching departments!', 'danger')
            current_app.logger.error(f'An error occurred while fetching departments: {str(e)}')
            return redirect(url_for('admin_data.dashboard'))

@departments_v2_bp.route('/v2/delete_department/<uuid:department_id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_department(department_id):
    database_name = session.get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))

    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            # Delete department from the database
            deleted = Departments.delete_department(db_session, department_id)
            if deleted:
                flash('Department deleted successfully!', 'success')
                current_app.logger.info('Department deleted successfully!')
                return redirect(url_for('departments_v2.departments'))
        except Exception as e:
            flash('An error occurred while deleting the department!', 'danger')
            current_app.logger.error(f'An error occurred while deleting the department: {str(e)}')
            return redirect(url_for('departments_v2.departments'))

@departments_v2_bp.route('/v2/update_department/<uuid:department_id>', methods=['GET', 'POST'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_department(department_id):
    database_name = session.get('database_name')
    if database_name is None:
        flash('An error occurred, Please contact support for assistance.', 'danger')
        current_app.logger.error("An error occurred while fetching company database name")
        return redirect(url_for('admin_data.dashboard'))
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        department_to_update = Departments.get_department_by_id(db_session, department_id)
        print("department_to_update: ", department_to_update)
        if department_to_update is None:
            current_app.logger.error("No department found")
            flash('No department found.', 'danger')
            return redirect(url_for('departments_v2.departments'))
        form = DepartmentUpdateForm(obj=department_to_update)

        if request.method == 'POST':
            if not form.validate_on_submit():
                current_app.logger.error("Form validation error")
                flash('Form validation error', 'danger')
                return redirect(url_for('departments_v2.departments'))
            data = request.form
            department_name = data.get('department_name')
            try:
                department_name = department_name.strip().lower()
            except Exception as e:
                department_name = department_name
                current_app.logger.error(f'An error occurred while stripping department name: {str(e)}')
            try:
                updated = Departments.update_department(db_session, department_id, department_name)
                message = f"Department updated successfully."
                current_app.logger.info(f"Department updated successfully: {updated}")
                flash(message, 'success')
                return redirect(url_for('departments_v2.departments'))
            except Exception as e:
                current_app.logger.error(f"An error occurred while updating department: {str(e)}")
                flash(f'An error occurred while updating department: {str(e)}', 'danger')
                return redirect(url_for('departments_v2.departments'))
        try:
            current_app.logger.info("Rendering update department template")
            return render_template('departments/update_department_v2.html',
                                   form=form)
        except Exception as e:
            message = 'An error occurred while rendering the template. Please try again.'
            current_app.logger.error(f"An error occurred while rendering the template: {str(e)}")
            flash(message, 'danger')
            return redirect(url_for('departments_v2.departments'))



