from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SubmitField, SelectField, TextAreaField, DecimalField, IntegerField, DateField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.fields import FieldList # Import FieldList to handle multiple form fields of the same type in a form 
from wtforms import validators, ValidationError



class SalaryAdvanceRequestForm(FlaskForm):
    amount = DecimalField('Amount Requested', validators=[DataRequired(), NumberRange(min=1000, max=100000000)])
    installment_amounts = FieldList(DecimalField('Installment Amount', [validators.DataRequired()]), min_entries=1)
    due_dates = FieldList(
    DateField('Due Date', validators=[DataRequired()]), 
    min_entries=1
    )
    reason = TextAreaField('Reason for Advance', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Submit')

    
class SalaryApprovalForm(FlaskForm):
    approval = SelectField('Approval', choices=[('approved', 'Approve'), ('rejected', 'Reject')], validators=[DataRequired()])
    remarks = TextAreaField('Remarks', validators=[Optional()])
    submit = SubmitField('Submit')

class SalaryAdvanceForm(FlaskForm):
    employee_id = SelectField('Employee', validators=[DataRequired()])
    amount = DecimalField('Amount', validators=[DataRequired(), NumberRange(min=0, message="Amount must be positive")])
    advance_date = DateField('Payment Date', validators=[DataRequired()])
    reason = TextAreaField('Reason', validators=[Optional()])
    submit = SubmitField('Submit')