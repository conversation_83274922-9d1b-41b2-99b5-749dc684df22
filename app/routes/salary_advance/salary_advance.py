from app.models.company import Employee, LeaveApproval
from app.models.company_salary_advance import SalaryAdvanceRequest, SalaryAdvanceApproval
from flask import render_template, redirect, url_for, flash, request, jsonify, session
from flask import current_app, Blueprint
from app.routes.salary_advance.forms import SalaryAdvanceRequestForm, SalaryApprovalForm, SalaryAdvanceForm
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from datetime import datetime
from app.helpers.auxillary import Auxillary
from app.helpers.route_helpers import restrict_based_on_plan
import re
advance_requests = Blueprint('advance_requests', __name__)
advance_requests.before_request(restrict_based_on_plan)

@advance_requests.route('/apply_for_advance', methods=['GET', 'POST'])
@role_required('employee')
def apply_for_advance():
    error_messages = []

    if request.method == 'POST':
        # Log the form data
        data = request.form
        current_app.logger.info(f"Form data: {data}")
        
        # Get the employee from the session
        employee_id = session.get('employee_id')
        
        # Retrieve the form data
        amount = data.get('amount')
        reason = data.get('reason')
         # Fetch all installment amounts and due dates
        installment_amounts = []
        due_dates = []

        # Use pattern matching to extract dynamic keys for installments and due dates
        for key in data.keys():
            if key.startswith('installment_amounts'):
                installment_amounts.append(data.get(key))
            elif key.startswith('due_dates'):
                due_dates.append(data.get(key))

        current_app.logger.info(f"installment_amounts: {installment_amounts}, due dates: {due_dates}")
        current_app.logger.info(f"Type of installment_amounts: {type(installment_amounts)}")
        current_app.logger.info(f"Length of installments: {len(installment_amounts)}")

        # Pair installment amounts with due dates
        installments = []
        if installment_amounts and due_dates:
            for inst_amount, due_date in zip(installment_amounts, due_dates):
                installments.append({'amount': inst_amount, 'due_date': due_date})

        # Log details for debugging
        current_app.logger.info(f"Amount: {amount}, Reason: {reason}, Installments: {installments}")
        length = len(installments)
        current_app.logger.info(f"Length of installments: {length}")
        # Get database name from session
        database_name = session.get('database_name')
        current_app.logger.info(f"Database name: {database_name}")

        current_app.logger.info(f"Installments: {installment_amounts} type: {type(installment_amounts)}")
        sum_installments = 0
        # Check if the installments' sum equals the requested amount
        for i in installment_amounts:
            # add the installment to the sum
            try:
                converted_amount = int(i)
            except Exception as e:
                message = f"Please enter a valid number"
                flash(message, 'success')
                return jsonify({'success': True, 'message': message}), 200
            sum_installments += converted_amount

        # make sure the amount is a valid number too.
        try:
            converted_amount = int(amount)
        except Exception as e:
            message = f"Please enter a valid number"
            flash(message, 'success')
            return jsonify({'success': True, 'message': message}), 200    
        check = (sum_installments == converted_amount)
        current_app.logger.info(f"checking: {check}")
        if not check:
            message = f"The amount requested: {amount} does not equall to the total installments: {sum_installments}"
            flash(message, 'success')
            return jsonify({'success': True, 'message': message}), 200


        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                current_app.logger.info("before adding salary advance request")
                result = SalaryAdvanceRequest.create_salary_advance_request(
                    db_session, employee_id, amount, installments, reason
                )
                current_app.logger.info(f"Result after creating the salary advance request: {result}")
                
                if result:
                    message = "Salary advance request created successfully"
                    flash(message, 'success')
                    return jsonify({'success': True, 'message': message}), 200

                message = "An error occurred while creating the salary advance request"
                flash(message, 'danger')
                return jsonify({'message': message}), 400

            except Exception as e:
                current_app.logger.error(f"An error occurred while creating a salary advance request: {str(e)}")
                message = "An error occurred while creating the salary advance request"
                flash(message, 'danger')
                error_messages.append(message)
                return jsonify({'message': message}), 400

    # Handle GET request to render the page
    try:
        current_app.logger.info("Rendering the salary advance page")
        return render_template('salary_advance/salary_advance.html')
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
        message = "An error occurred while rendering the salary advance page"
        flash(message, 'danger')
        error_messages.append(message)
        if error_messages:
            return jsonify({'success': False, 'messages': error_messages}), 400
        return jsonify({'success': False, 'message': message}), 400
    
@advance_requests.route('/add_advance', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_advance():
    """Add a salary advance."""
    current_app.logger.info("Adding a salary advance")
    database_name = session.get('database_name')
    if not database_name:
        current_app.logger.error("Failed to fetch company database name")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    current_app.logger.info(f"Database name: {database_name}")
    form = SalaryAdvanceForm()

    # Fetch employees for the dropdown
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        if not employees:
            message = "No employees found. Please add employees before adding advances."
            flash(message, 'danger')
            return render_template('salary_advance/add_advance.html', form=form, employees=[])

        current_app.logger.info(f"populating the employee dropdown")
        # Populate employee dropdown
        try:
            form.employee_id.choices = [(e['employee_id'], f"{e['first_name']} {e['last_name']}") for e in employees]
            current_app.logger.info(f"Employee choices: {form.employee_id.choices}")
        except Exception as e:
            current_app.logger.error(f"Error populating the employee dropdown: {e}")
            message = "An error occurred while populating the employee dropdown"
            flash(message, 'danger')
            return jsonify({'message': message}), 400

        if form.validate_on_submit():
            try:
                employee_id = form.employee_id.data
                amount = form.amount.data
                reason = form.reason.data
                due_date = form.advance_date.data

                installments = [{'amount': amount, 'due_date': due_date}]
                # Create new salary advance
                new_advance = SalaryAdvanceRequest.manually_create_salary_advance_request(
                    db_session, employee_id, amount, installments, reason
                )
                current_app.logger.info(f"New salary advance: {new_advance}")

                message = "Salary advance added successfully"
                flash(message, 'success')
                return jsonify({'success': True, 'message': message}), 200
            except Exception as e:
                db_session.rollback()
                current_app.logger.error(f"Error adding salary advance: {e}")
                message = "An error occurred while adding the salary advance"
                return jsonify({'message': 'An error occurred. Please try again later.'}), 400

    try:
        current_app.logger.info("Rendering the salary advance page")
        return render_template('salary_advance/add_advance.html', form=form)
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
        message = "An error occurred while rendering the salary advance page"
        flash(message, 'danger')
        return jsonify({'message': message}), 400

@advance_requests.route('/view_advance_requests', methods=['GET'])
@role_required('employee')
def view_advance_requests():
    try:
        # Get the employee from the session
        employee_id = session.get('employee_id')
        # get database name from session
        database_name = session.get('database_name')

        current_app.logger.info(f"Database name: {database_name}")

        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            # Get the employee's salary advance requests
            try:
                salary_advance_requests = SalaryAdvanceRequest.get_salary_advance_request_for_employee(db_session, employee_id)
                for request in salary_advance_requests:
                   full_name = request['full_name']
                   current_app.logger.info(f"Full name: {full_name}")
            except Exception as e:
                current_app.logger.error(f"error terieving salary advances: {str(e)}")
            return render_template('salary_advance/view_advance_requests.html', salary_advance_requests=salary_advance_requests)
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
        message = "An error occurred while rendering the salary advance page"
        flash(message, 'danger')
        return jsonify({'message': message}), 400
    

    
@advance_requests.route('/view_all_advance_requests', methods=['GET'])
@role_required(['hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def view_all_advance_requests():
    try:
        # Get database name from session
        database_name = session.get('database_name')
        current_app.logger.info(f"Database name: {database_name}")

        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            # Get all salary advance requests
            salary_advance_requests = SalaryAdvanceRequest.get_salary_advance_requests(db_session)

            # Group the requests by employee_id
            grouped_requests = {}
            for request in salary_advance_requests:
                employee_id = request.get('employee_id')
                if employee_id not in grouped_requests:
                    grouped_requests[employee_id] = {
                        'full_name': request.get('full_name', 'Unknown'),
                        'requests': []
                    }
                grouped_requests[employee_id]['requests'].append(request)
            current_app.logger.info(f"Grouped requests: {grouped_requests}")
            return render_template(
                'salary_advance/view_all_advance_requests.html',
                grouped_requests=grouped_requests
            )
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
        message = "An error occurred while rendering the salary advance page"
        flash(message, 'danger')
        return jsonify({'message': message}), 400
    
@advance_requests.route('/approve_advance_request/<request_id>', methods=['POST', 'GET'])
@role_required(['hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def approve_advance_request(request_id):
    """Approve a salary advance request."""
    form = SalaryApprovalForm()
    error_messages = []
    
    database_name = session.get('database_name')

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the salary advance request
            salary_advance_request = SalaryAdvanceRequest.get_salary_advance_request_by_id(db_session, request_id)
            if not salary_advance_request:
                message = "Salary advance request not found"
                flash(message, 'danger')
                return jsonify({'message': message}), 404

            if request.method == 'POST':
                if not form.validate_on_submit():
                    message = f"Invalid data: {form.errors}"
                    flash(message, 'danger')
                    return jsonify({'message': message}), 400

                # Retrieve the form data
                approval = form.approval.data
                remarks = form.remarks.data

                approver_id = session.get('user_id')
                approver_role = session.get('role')

                # Approve or reject the salary advance request
                try:
                    result = SalaryAdvanceApproval.create_approval(
                        db_session, request_id, approver_id,approver_role, approval, remarks)
                    current_app.logger.info(f"Result after approving the salary advance request: {result}")
                    if result:
                        message = "Salary advance request approved successfully"
                        flash(result, 'success')
                        return jsonify({'success':True,'message': result}), 200
                    message = "An error occurred while approving the salary advance request"
                    flash(message, 'danger')
                    return jsonify({'message': message}), 400
                except Exception as e:
                    current_app.logger.error(f"An error occurred while approving a salary advance request: {str(e)}")
                    message = "An error occurred while approving the salary advance request"
                    flash(message, 'danger')
                    error_messages.append(message)
                    return jsonify({'message': message}), 400
                
            return render_template('salary_advance/approve_advance_request.html', form=form, salary_advance_request=salary_advance_request)
        except Exception as e:
            current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
            message = "An error occurred while rendering the salary advance page"
            flash(message, 'danger')
            error_messages.append(message)
            return jsonify({'message': message}), 400
        
@advance_requests.route('/view_advance_approvals', methods=['GET'])
@role_required(['hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def view_advance_approvals():
    """View all salary advance approvals."""
    try:
        # Get database name from session
        database_name = session.get('database_name')
        current_app.logger.info(f"Database name: {database_name}")

        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            # Get all salary advance approvals
            salary_advance_approvals = SalaryAdvanceApproval.get_approvals(db_session)

            # Prepare structured data for rendering
            grouped_approvals = []
            for approval in salary_advance_approvals:
                employee_id = approval.get('employee_id')
                request_id = approval.get('request_id')

                # Find or create entry for employee
                employee_entry = next(
                    (entry for entry in grouped_approvals if entry['employee_id'] == employee_id),
                    None
                )
                if not employee_entry:
                    employee_entry = {
                        'employee_id': employee_id,
                        'full_name': approval.get('employee_name', 'Unknown'),
                        'requests': []
                    }
                    grouped_approvals.append(employee_entry)

                # Find or create entry for request_id
                request_entry = next(
                    (req for req in employee_entry['requests'] if req['request_id'] == request_id),
                    None
                )
                if not request_entry:
                    request_entry = {
                        'request_id': request_id,
                        'amount': approval.get('amount'),
                        'approvals': []
                    }
                    employee_entry['requests'].append(request_entry)

                # Add approval details
                request_entry['approvals'].append({
                    'approver_id': approval.get('approver_id'),
                    'status': approval.get('status'),
                    'created_at': approval.get('created_at'),
                    'remarks': approval.get('remarks'),
                    'approver_role': approval.get('approver_role')

                })

            # Add rowspan values for each employee and request
            for employee in grouped_approvals:
                employee['rowspan'] = sum(len(req['approvals']) for req in employee['requests'])
                for request in employee['requests']:
                    request['rowspan'] = len(request['approvals'])
            current_app.logger.info(f"Grouped approvals: {grouped_approvals}")
            return render_template(
                'salary_advance/view_advance_approvals.html',
                grouped_approvals=grouped_approvals,
                LeaveApproval=LeaveApproval, db_session=db_session
            )
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
        message = "An error occurred while rendering the salary advance page"
        flash(message, 'danger')
        return jsonify({'message': message}), 400
    
@advance_requests.route('/update_advance_request/<request_id>', methods=['POST', 'GET'])
@role_required(['employee', 'hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def update_advance_request(request_id):
    """Update a salary advance request."""
    database_name = session.get('database_name')
    role = session.get('role')
    user_id = session.get('user_id')
    
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            # Fetch the salary advance request with installment details
            salary_advance_request = SalaryAdvanceRequest.get_salary_advance_request_by_id(db_session, request_id)
            current_app.logger.info(f"Salary advance request: {salary_advance_request}")
            email = salary_advance_request['employee_email']
            current_app.logger.info(f"Email: {email}")
            full_name = salary_advance_request['full_name']
            due_dates = []
            installment_plans = salary_advance_request['installment_plans']
            for date in installment_plans:
                current_app.logger.info(f"Date: {date['due_date']}")
                current_app.logger.info(f"Type of date: {type(date['due_date'])}")
                due_dates.append(date['due_date'])
            current_app.logger.info(f"Due dates: {due_dates}")
            if not salary_advance_request:
                flash("Salary advance request not found", 'danger')
                return redirect(url_for('advance_requests.view_advance_requests'))
            
             # get the name of the user updating the request
            try:
                approver_name = LeaveApproval.get_approver_name(user_id, role, db_session)
                current_app.logger.info(f"Approver name: {approver_name}")
            except Exception as e:
                current_app.logger.error(f"An error occurred while getting the approver name: {str(e)}")
                approver_name = 'Unknown'

            if request.method == 'GET':
                installment_plans = salary_advance_request['installment_plans']
                current_app.logger.info(f"Installment plans: {installment_plans}")
                # Pass data to the template
                return render_template(
                    'salary_advance/edit_salary_advance_request.html',
                    salary_advance_request=salary_advance_request,
                    installment_plans=installment_plans,
                )

            elif request.method == 'POST':
                try:
                    data = request.form
                    current_app.logger.info(f"Form data: {data}")
                    
                    # Extract main amount and installment details from the form
                    amount = request.form.get('amount', type=float)
                    installment_amounts = request.form.getlist('installment_amounts[]')  # Note the key with []
                    due_dates = request.form.getlist('due_dates[]')  # Note the key with []

                    current_app.logger.info(f"Amount: {amount}, Installment amounts: {installment_amounts}, Due dates: {due_dates}")
                    
                    # Validate data
                    if not amount or len(installment_amounts) != len(due_dates):
                        flash("Invalid data. Please check your input.", 'danger')
                        return redirect(url_for('advance_requests.update_advance_request', request_id=request_id))
                    
                    # get installment amounts
                    installment_amounts = [float(inst_amount) for inst_amount in installment_amounts]   
                    summation = sum(installment_amounts)
                    if summation != float(amount):
                        message = "The sum of the installment amounts must equal the requested amount."
                        flash(message, 'danger')
                        return jsonify({'success':True,'message': message}), 200

                    # Pair installment amounts with due dates
                    installments = [
                        {'amount': float(inst_amount), 'due_date': due_date}
                        for inst_amount, due_date in zip(installment_amounts, due_dates)
                    ]

                    current_app.logger.info(f"Installments: {installments}")

                    # Update the salary advance request
                    result = SalaryAdvanceRequest.update_salary_advance_request(
                        db_session, request_id, amount, installments)
                    
                    current_app.logger.info(f"Result after updating the salary advance request: {result}")
                    
                    # Format installments for readability
                    formated_installments = [str(float(installment['amount'])) for installment in installments]
                    if result:
                        message = "Salary advance request updated successfully"
                        # send an email to the employee
                        try:
                            if email and role != 'employee':
                                subject = 'Salary Advance modified'
                                message2 = f"""
                                Dear {full_name},Your salary advance request has been modified 
                                by {approver_name} with the following details: Amount Requested: {amount},  
                                Installments: {installments}.Regards, {session.get('company_name')}"
                                """
                                sent = Auxillary.send_netpipo_email(subject, email, message2)
                                current_app.logger.info(f"Email sent: {sent}")
                        except Exception as e:
                            current_app.logger.error(f"An error occurred while sending an email: {str(e)}")
                            flash(message, 'danger')
                        flash(message, 'success')
                        return jsonify({'success':True,'message': message}), 200
                    message = "An error occurred while updating the salary advance request"
                    flash(message, 'danger')
                    return jsonify({'success':False,'message': message}), 400

                except Exception as e:
                    current_app.logger.error(f"Error updating request: {e}")
                    flash("An error occurred. Please try again.", 'danger')
                    return redirect(url_for('advance_requests.view_advance_requests'))
                
        except Exception as e:
            current_app.logger.error(f"Error updating request: {e}")
            flash("An error occurred. Please try again.", 'danger')
            return redirect(url_for('advance_requests.view_advance_requests'))


@advance_requests.route('/delete_advance_request/<request_id>', methods=['POST', 'GET'])
@role_required('employee')
def delete_advance_request(request_id):
    """Delete a salary advance request."""
    error_messages = []
    
    database_name = session.get('database_name')

    # Initialize the Database Connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the salary advance request
            salary_advance_request = SalaryAdvanceRequest.get_salary_advance_request_by_id(db_session, request_id)
            if not salary_advance_request:
                message = "Salary advance request not found"
                flash(message, 'danger')
                return jsonify({'message': message}), 404
            
            # Check if the request has been approved
            if salary_advance_request['status'] == 'approved':
                message = "You cannot delete an approved salary advance request"
                flash(message, 'danger')
                return jsonify({'message': message}), 400

            # Delete the salary advance request
            try:
                result = SalaryAdvanceRequest.delete_salary_advance_request(db_session, request_id)
                current_app.logger.info(f"Result after deleting the salary advance request: {result}")
                if result:
                    message = "Salary advance request deleted successfully"
                    flash(message, 'success')
                    return jsonify({'success':True,'message': message}), 200
                message = "An error occurred while deleting the salary advance request"
                flash(message, 'danger')
                return jsonify({'message': message}), 400
            except Exception as e:
                current_app.logger.error(f"An error occurred while deleting a salary advance request: {str(e)}")
                message = "An error occurred while deleting the salary advance request"
                flash(message, 'danger')
                error_messages.append(message)
                return jsonify({'message': message}), 400
        except Exception as e:
            current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
            message = "An error occurred while rendering the salary advance page"
            flash(message, 'danger')
            error_messages.append(message)
            return jsonify({'message': message}), 400