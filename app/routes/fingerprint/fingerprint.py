from app.models.company import Employee, Attendance
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from flask import current_app, Blueprint, jsonify, request, make_response, url_for, session
from flask import flash
import requests
import json
from dotenv import load_dotenv
import os
from rapidfuzz import fuzz
from app.helpers.auxillary import Auxillary
import uuid
from app.routes.fingerprint.form import FingerprintForm
from app.models.central import Company
from flask import render_template, redirect
from app.decorators.admin_decorator import admin_required
from app.helpers.company_helpers import CompanyHelpers

load_dotenv()
netpipo_base_url = os.getenv('NETPIPO_BASE_URL')

fingerprint = Blueprint('fingerprint', __name__)

@fingerprint.route('/upload_employees_data', methods=['POST', 'GET'])
@role_required('hr')
def upload_employees_data():
    try:
        # Get database name from session
        database_name = session['database_name']

        company_id = "24e8a889-ab3f-474f-96bb-61ae0a9b5b6f"
        current_app.logger.info(f"company_id: {company_id}")

        #Initialize database connection
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db_session:
            # get employees
            try:
                employees = Employee.get_employees(db_session)
                current_app.logger.info(f"employees retrieved: {employees}")
            except Exception as e:
                current_app.logger.info(f"error retrieving employees: {str(e)}")
                employees = []
        if request.method == 'GET':
            if len(employees) > 0:
                #Loop through the employees and save the data in the database
                for employee in employees:
                    employee_id = employee['employee_id']
                    # convert employee_id to string
                    employee_id = str(employee_id)
                    roll_id = 0
                    name = employee['full_name']

                    # convert the employee data to a json object
                    employee_data = {
                        'employee_id': employee_id,
                        'privilege': roll_id,
                        'name': name,
                        'company_id': company_id
                    }
                    # Post the data to the endpoint
                    url = f"{netpipo_base_url}/addEmployee"
                    current_app.logger.info(f"employee data: {employee_data} being posted to {url}")
                    try:
                        response = requests.post(url, json=employee_data)
                        current_app.logger.info(f"response: {response.json()}")
                    except Exception as e:
                        current_app.logger.info(f"error posting employee data: {str(e)}")
                    # Extract userId from the response
                    user_id = response.json().get('userId')
                    current_app.logger.info(f"user_id: {user_id}")
                    # send the employee to the device
                    url2 = f"{netpipo_base_url}/setOneUserJson"
                    payload = {
                        "enrollId": user_id,
                        "backupNum": -1,
                        "deviceSn": "AYSK26020937"
                    }
                    try:
                        response2 = requests.post(url2, json=payload)
                        current_app.logger.info(f"response2: {response2.json()}")
                    except Exception as e:
                        current_app.logger.info(f"error sending data to device: {str(e)}")
                return make_response(jsonify({'message': 'Employees uploaded successfully'}), 200)
            else:
                return make_response(jsonify({'message': 'No employees to upload'}), 200)
        else:
            return make_response(jsonify({'message': 'Invalid request method'}), 400)
    except Exception as e:
        current_app.logger.info(f"error uploading employees: {str(e)}")
        return make_response(jsonify({'message': 'An error occurred'}), 500)

@fingerprint.route('/send_fingerprint_data', methods=['GET'])
def send_fingerprint_data():
    """Send fingerprint data to the fingerprint device"""
    url1 = f"{netpipo_base_url}/setOneUserJson"
    url2 = f"{netpipo_base_url}/get_users"
    company_id = "afe58ab2-cee6-43a6-b114-876ef5fcf87e"


    # Retrieve employees data from the database with the company_id as the argument
    args = {
        "company_id": company_id
    }
    response = requests.get(url2, params=args)
    current_app.logger.info(f"response: {response}")
    try:
        data = response.json()  # Convert response to JSON
        current_app.logger.info(f"data: {data}")
    except Exception as e:
        current_app.logger.info(f"error getting data: {str(e)}")

    # Extracting person details
    persons_data = data.get("extend", {}).get("persons", [])
    current_app.logger.info(f"persons_data: {persons_data}")

    for person in persons_data:
        enrollId = person.get("id")
        backupNum = -1
        deviceSn = "AYSK26020938"

        # Send the data to the device
        payload = {
            "enrollId": enrollId,
            "backupNum": backupNum,
            "deviceSn": deviceSn
        }
        try:
            response = requests.post(url1, json=payload)
            current_app.logger.info(f"response: {response.json()}")
        except Exception as e:
            current_app.logger.info(f"error sending data: {str(e)}")

    return make_response(jsonify({'message': 'Fingerprint data sent successfully'}), 200)

@fingerprint.route('/get_daily_fingerprint_data', methods=['GET'])
def get_daily_fingerprint_data():
    """Get daily fingerprint data from the fingerprint device"""
    url = f"{netpipo_base_url}/daily_records"
    url2 = f"{netpipo_base_url}/get_companies"
    all_results = []

    try:
        # Get all companies in a single request
        response = requests.get(url2)
        companies_data = response.json().get("companies", [])
        current_app.logger.info(f"Retrieved {len(companies_data)} companies")

        # Filter companies to only those with non-empty devices list
        companies_with_devices = [company for company in companies_data if company.get("devices", [])]
        current_app.logger.info(f"Filtered to {len(companies_with_devices)} companies with devices")

        if not companies_with_devices:
            return make_response(jsonify({'message': 'No companies with devices found'}), 404)

        # Create a single DatabaseConnection instance to be reused
        db_connection = DatabaseConnection()

        # Process each company with devices
        processed_count = 0
        for company in companies_with_devices:
            company_id = str(company["company_id"])
            database_name = company["database_name"]
            devices = company.get("devices", [])
            device_sns = [device["device_sn"] for device in devices]
            current_app.logger.info(f"Processing company_id: {company_id}, database_name: {database_name}, devices: {device_sns}")

            try:
                # Get fingerprint records for this company
                params = {"company_id": company_id}
                response = requests.get(url, params=params)

                if response.status_code != 200:
                    current_app.logger.error(f"Error getting records for company {company_id}: {response.text}")
                    continue

                records = response.json().get('extend', {}).get('records', [])

                if not records:
                    current_app.logger.info(f"No records found for company {company_id}")
                    continue

                current_app.logger.info(f"Found {len(records)} records for company {company_id}")

                # Process records for this company
                company_results = process_company_records(db_connection, database_name, records)
                all_results.extend(company_results)
                processed_count += 1

            except Exception as e:
                current_app.logger.error(f"Error processing company {company_id}: {str(e)}")
                # Continue with next company instead of returning an error
                continue

        return make_response(jsonify({
            'message': 'Fingerprint data retrieved successfully',
            'results': all_results,
            'companies_processed': processed_count,
            'records_processed': len(all_results)
        }), 200)

    except Exception as e:
        current_app.logger.error(f"Error in get_daily_fingerprint_data: {str(e)}")
        return make_response(jsonify({'message': f'An error occurred: {str(e)}'}), 500)

def process_company_records(db_connection, database_name, records):
    """Process fingerprint records for a specific company.

    Args:
        db_connection: DatabaseConnection instance
        database_name: The company's database name
        records: List of fingerprint records

    Returns:
        List of processed results
    """
    results = []
    current_app.logger.info(f"Processing {len(records)} records for database {database_name}")

    # Log all records for debugging
    for i, record in enumerate(records):
        current_app.logger.info(f"Record {i+1}: employee_id={record.get('employee_id')}, name={record.get('name')}, time={record.get('records_time')}")

    try:
        # Use a single database session for all operations
        with db_connection.get_session(database_name) as db_session:
            # Get all employees once
            employees = Employee.get_employees(db_session)
            current_app.logger.info(f"Retrieved {len(employees)} employees from database")

            # Create a lookup dictionary for faster matching
            employee_lookup = {str(emp['employee_id']): emp for emp in employees}
            current_app.logger.info(f"Employee lookup keys: {list(employee_lookup.keys())}")

            # Process each record
            for record in records:
                record_employee_id = record.get('employee_id')
                record_name = record.get('name', 'Unknown')

                if not record_employee_id:
                    current_app.logger.info(f"Record for {record_name} has no employee_id")
                    continue

                # Convert to string for comparison
                record_employee_id = str(record_employee_id)
                current_app.logger.info(f"Processing record for employee_id: {record_employee_id}, name: {record_name}")

                # Check if employee exists using the lookup dictionary (O(1) operation)
                if record_employee_id in employee_lookup:
                    employee = employee_lookup[record_employee_id]
                    employee_id = employee['employee_id']
                    current_app.logger.info(f"Found employee in database: {employee['full_name']}")

                    try:
                        # Record attendance
                        device_used = "Netpipo"
                        location = "Office"
                        record_time = record['records_time']
                        current_app.logger.info(f"Recording attendance for {employee['full_name']} at {record_time}")

                        # Clock in
                        result = Attendance.clockin2(db_session, employee_id, location, device_used, record_time)
                        current_app.logger.info(f"Clockin2 result for {employee['full_name']}: {result}")

                        results.append({
                            'employee_id': employee_id,
                            'full_name': employee['full_name'],
                            'result': result
                        })

                        # Handle clock-out if necessary
                        if "already clocked in and have not clocked out yet" in result:
                            try:
                                current_app.logger.info(f"Attempting to clock out {employee['full_name']}")
                                clock_out_result = Attendance.clockout2(db_session, employee_id, location, record_time)
                                current_app.logger.info(f"Clock-out recorded for {employee['full_name']}: {clock_out_result}")
                            except Exception as e:
                                current_app.logger.error(f"Error recording clock-out for {employee['full_name']}: {str(e)}")

                    except Exception as e:
                        current_app.logger.error(f"Error recording attendance for {employee['full_name']}: {str(e)}")
                else:
                    # Employee not found in database - log a more detailed warning
                    current_app.logger.warning(f"Employee with ID {record_employee_id} (name: {record_name}) not found in database")
                    current_app.logger.warning(f"This employee exists in the fingerprint device but not in the database.")
                    current_app.logger.warning(f"To fix this, add the employee to the database with ID: {record_employee_id}")

                    # Add to results so the issue is visible in the API response
                    results.append({
                        'employee_id': record_employee_id,
                        'full_name': record_name,
                        'result': f"Employee not found in database. Add employee with ID {record_employee_id} to the database."
                    })

    except Exception as e:
        current_app.logger.error(f"Error processing records for database {database_name}: {str(e)}")

    current_app.logger.info(f"Processed {len(results)} results: {results}")
    return results

@fingerprint.route('/update_fingerprint_data', methods=['GET'])
#@role_required('hr')
def update_fingerprint_data():
    """Update fingerprint data to the database."""
    database_name = os.getenv('company_database')
    db_connection = DatabaseConnection()
    # Get the data from devive
    url1 = f"{netpipo_base_url}/get_persons"
    try:
        response = requests.get(url1)
        current_app.logger.info(f"response: {response.json()}")
        data = response.json().get('extend', {}).get('persons', [])
        current_app.logger.info(f"data: {data}")

    except Exception as e:
        current_app.logger.info(f"error getting data from device: {str(e)}")
        return make_response(jsonify({'message': 'An error occurred'}), 500)
    # Get the data from the database
    with db_connection.get_session(database_name) as db_session:
        try:
            employees = Employee.get_employees(db_session)
            current_app.logger.info(f"employees retrieved: {employees}")
            # get employee full names and employee ids
            employee_names = [employee['full_name'] for employee in employees]
            employee_ids = [employee['employee_id'] for employee in employees]
            # combine the employee names and employee ids
            employee_data = dict(zip(employee_names, employee_ids))
            current_app.logger.info(f"employee data: {employee_data}")
            message1 = f"employees: {employee_data}"
            # Loop through the data from the device and update the database. We wanna update the employee_id
            for person in data:
                person_name = person['name']
                best_match = None
                best_score = 0

                for employee_name in employee_data.keys():
                    similarity_score = fuzz.ratio(person_name.lower(), employee_name.lower())

                    if similarity_score > best_score:
                        best_score = similarity_score
                        best_match = employee_name

                if best_match and best_score > 80:  # Threshold for a good match
                    current_app.logger.info(f"Best match for {person_name}: {best_match} with score {best_score}")
                    employee_id = employee_data[best_match]
                    current_app.logger.info(f"employee_id for {person_name}: {employee_id}")
                    # Update the employee_id in the device
                    url2 = f"{netpipo_base_url}/update_person"
                    data2 = {
                        "id": person['id'],
                        "employee_id": str(employee_id)
                    }
                    try:
                        # Make data into a json object
                        data2 = json.dumps(data2)
                        headers = {'Content-Type': 'application/json'}
                        response2 = requests.post(url2, data=data2, headers=headers)
                        current_app.logger.info(f"response2: {response2.json()}")
                    except Exception as e:
                        current_app.logger.info(f"error updating data: {str(e)}")
                else:
                    current_app.logger.info(f"No strong match found for {person_name}")
                    # return the employee data and the response from the device
            return make_response(jsonify({'message': message1, 'response': data}), 200)
        except Exception as e:
            current_app.logger.info(f"error retrieving employees: {str(e)}")
            employees = []
            employee_data = {}

@fingerprint.route('/register_device', methods=['POST', 'GET'])
@admin_required
def register_device():
    """Register a device with the fingerprint device."""
    url = f"{netpipo_base_url}/add_company_device"
    form = FingerprintForm()
    companies = Company.get_companies()
    #current_app.logger.info(f"companies: {companies}")
    company_choices = [(company['company_id'], company['company_name']) for company in companies]
    form.company_id.choices = company_choices
    url2 = f"{netpipo_base_url}/get_companies"
    if request.method == 'GET':
        # Get companies from the database
        try:
            my_companies = requests.get(url2)
            current_app.logger.info(f"my_companies: {my_companies.json()}")
            companies = my_companies.json().get('companies', [])
            current_app.logger.info(f"companies: {companies}")
        except Exception as e:
            current_app.logger.info(f"error getting companies: {str(e)}")
            companies = []
        return render_template('fingerprint/register_device.html', form=form, companies=companies)
    if request.method == 'POST':
        current_app.logger.info(f"request method is POST before validating")
        form_data = request.form
        #current_app.logger.info(f"form data: {form_data}")
        try:
            check = form.validate_on_submit()
            current_app.logger.info(f"check: {check}")
        except Exception as e:
            current_app.logger.info(f"error validating form: {str(e)}")
        if form.validate_on_submit():
            current_app.logger.info(f"form validated")
            current_app.logger.info(f"form validated and the data is")
            device_sn = form.device_sn.data
            company_id = form.company_id.data
            data = {
                "device_sn": device_sn,
                "company_id": company_id
            }
            current_app.logger.info(f"data to be posted: {data}")
            # First check if the company_id exists in the database
            try:
                response = requests.get(url2)
                current_app.logger.info(f"response: {response.json()}")
                companies = response.json().get('companies', [])
                current_app.logger.info(f"companies: {companies}")
                # Create a dictionary for fast lookup
                company_data = {company['company_id']: company['database_name'] for company in companies}
                current_app.logger.info(f"company_data: {company_data}")

                # Check if company exists
                database_name = company_data.get(company_id)
                current_app.logger.info(f"database_name: {database_name}")
                if database_name:
                    try:
                        response2 = requests.post(url, json=data)
                        current_app.logger.info(f"response2: {response2.json()}")
                        # check if the device was registered successfully
                        registered = response2.json().get('message')
                        current_app.logger.info(f"registered: {registered}")
                        if 'added successfully' in registered:
                            current_app.logger.info("inside the if statement where device was registered successfully")
                            # Upload employees data
                            try:
                                result = CompanyHelpers.process_and_send_employees(database_name, company_id, device_sn)
                                current_app.logger.info(f"result: {result}")
                                if len(result) > 0:
                                    flash('Device registered successfully', 'success')
                                    return redirect(url_for('fingerprint.register_device'))
                                else:
                                    flash('An error occurred', 'danger')
                                    return redirect(url_for('fingerprint.register_device'))
                            except Exception as e:
                                current_app.logger.info(f"error uploading employees: {str(e)}")
                                flash('An error occurred', 'danger')
                                return redirect(url_for('fingerprint.register_device'))

                    except Exception as e:
                        current_app.logger.info(f"error registering device: {str(e)}")
                        flash('An error occurred', 'danger')
                        return redirect(url_for('admin_data.dashboard'))
                else:
                    current_app.logger.info(f"The company is not registered")
                    # Create the company and register the device if the company_id does not exist
                    url3 = f"{netpipo_base_url}/add_company"
                    # Get the database of the company
                    company = Company.get_company_by_id(company_id)
                    database_name = company['database_name']
                    company_name = company['company_name']
                    data2 = {
                        "company_id": company_id,
                        "company_name": company_name,
                        "database_name": database_name
                    }
                    current_app.logger.info(f"data2 to be posted during company registration: {data2}")
                    try:
                        # add the company in the database
                        response3 = requests.post(url3, json=data2)
                        current_app.logger.info(f"response3: {response3.json()}")
                        # Register the device to company_device table
                        response4 = requests.post(url, json=data)
                        current_app.logger.info(f"response4: {response4.json()}")
                        # check if the device was registered successfully
                        registered = response4.json().get('message')
                        current_app.logger.info(f"registered response: {registered}")
                        if 'added successfully' in registered:
                            current_app.logger.info("inside the if statement where device was registered successfully")
                            # Upload employees data
                            try:
                                result = CompanyHelpers.process_and_send_employees(database_name, company_id, device_sn)
                                current_app.logger.info(f"result: {result}")
                                if len(result) > 0:
                                    flash('Device registered successfully', 'success')
                                    return redirect(url_for('fingerprint.register_device'))
                                else:
                                    flash('An error occurred', 'danger')
                                    return redirect(url_for('fingerprint.register_device'))
                            except Exception as e:
                                current_app.logger.info(f"error uploading employees: {str(e)}")
                                flash('An error occurred', 'danger')
                                return redirect(url_for('fingerprint.register_device'))
                        flash(response4.json()['message'], 'success')
                        return redirect(url_for('fingerprint.register_device'))
                    except Exception as e:
                        current_app.logger.info(f"error registering device: {str(e)}")
                        return redirect(url_for('admin_data.dashboard'))
            except Exception as e:
                current_app.logger.info(f"error getting companies: {str(e)}")
                return redirect(url_for('admin_data.dashboard'))
        else:
            flash('Invalid data', 'danger')
            current_app.logger.info(f"form data is invalid" )
            return redirect(url_for('admin_data.dashboard'))