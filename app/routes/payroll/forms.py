from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField,SelectField, FloatField, DateField, RadioField
from wtforms.validators import DataRequired, Length, Email


class PayrollCalculator(FlaskForm):
    """Form to calculate payroll."""
    employee_name = StringField('Employee Name', validators=[DataRequired(message="Enter Valid Employee Name ex: <PERSON><PERSON><PERSON>")])
    id_number = StringField('ID Number', validators=[Length(min=0, max=16, message="Enter Valid ID Number ex: ****************")], default='' )
    email = StringField('Email')
    rssb_number = StringField('RSSB Number')
    phone = StringField('Phone Number')
    bank_account = StringField('Bank Account')
    bank_branch = StringField('Bank Branch')
    bank_name = StringField('Bank Name')
    account_number = StringField('Account Number')
    address = StringField('Address')
    department = StringField('Department')
    position = StringField('Position')
    company_name = StringField('Company Name')
    company_address = StringField('Company Address')
    company_tin = StringField('Company TIN')
    company_phone = StringField('Company Phone')
    company_email = StringField('Company Email')
    company_rssb = StringField('Company RSSB')
    joining_date = DateField('Joining Date', format='%Y-%m-%d')
    day_one = DateField('First day of the month', format='%Y-%m-%d')
    pay_date = DateField('Pay Date', format='%Y-%m-%d')
    net_salary = FloatField('Net Salary',default=0.0, validators=[DataRequired(message="Enter Valid Net Salary ex: 100000")])
    transport_allowance = FloatField('Transport Allowance',default=0.0)
    other_allowances = FloatField('Total other Allowances', default=0.0)
    deductions = FloatField('Deductions',default=0.0)
    medical_insurance = FloatField('Medical Insurance %', default=0.0)
    employee_type = SelectField('Employement Type', choices=[('permanent', 'Permanent Employee'), 
                    ('consultant', 'Consultant (Man-power)'), ('casual', 'Casual Employee'),
                    ('second_employee', 'Second Employee')], validators=[DataRequired()])
    
    submit = SubmitField('Calculate')

class PayrollForm(FlaskForm):
    pay_date = DateField('Pay Date', format='%Y-%m-%d', validators=[DataRequired()])
    # Display this timesheet applicable only if attendance_service is True
    timesheet_applicable = StringField('Timesheet Applicable')
    submit = SubmitField('Generate')

class UpdatePayrollForm(FlaskForm):
    net_salary = FloatField('Net Salary', validators=[DataRequired()])
    transport_allowance = FloatField('Transport Allowance', validators=[DataRequired()])
    other_allowances = FloatField('Other Allowances', validators=[DataRequired()])    
    submit = SubmitField('Update Payroll')

class PayrollApprovalForm(FlaskForm):
    status = RadioField('Status', choices=[('Approved', 'Approved'), ('Rejected', 'Rejected')], validators=[DataRequired()])
    remarks = StringField('Remarks')
    submit = SubmitField('Submit')

    