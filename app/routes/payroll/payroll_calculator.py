from flask import Blueprint, render_template, flash, redirect, url_for, session, request
from .forms import PayrollCalculator
import scipy.optimize as opt
import logging
from app.models.company import Employee, Deductions, Insurance, Payroll, Reimbursements
from app.models.central import NsfContributions
from app.routes.payroll.goal_Seek_mine import SalaryCalculator
from app import app
from num2words import num2words
from datetime import datetime, timedelta
from flask import jsonify

payroll_calculator_bp = Blueprint('payroll', __name__)

@payroll_calculator_bp.route('/payroll_calculation', methods=['GET', 'POST'])
def payroll_calculator():
    form = PayrollCalculator()
    try:
        if form.validate_on_submit():
            employee_name = form.employee_name.data
            net_salary = float(form.net_salary.data)
            transport_allowance = float(form.transport_allowance.data)
            other_allowances = float(form.other_allowances.data)
            deductions = float(form.deductions.data)
            medical_insurance = float(form.medical_insurance.data)
            employee_type = form.employee_type.data
            rssb_number = form.rssb_number.data
            email = form.email.data
            id_number = form.id_number.data
            phone = form.phone.data
            bank_account = form.bank_account.data
            bank_name = form.bank_name.data
            bank_branch = form.bank_branch.data
            address = form.address.data
            department = form.department.data
            position = form.position.data
            company_name = form.company_name.data
            company_address= form.company_address.data
            company_tin = form.company_tin.data
            company_phone = form.company_phone.data
            company_email = form.company_email.data
            company_rssb = form.company_rssb.data
            joining_date = form.joining_date.data
            day_one = form.day_one.data
            pay_date = form.pay_date.data

            # log all the values types from the form
            app.logger.info((f"Employee name type: {type(employee_name)}\nNet salary type: {type(net_salary)}\nTransport allowance type: {type(transport_allowance)}\nOther allowances type: {type(other_allowances)}\nDeductions type: {type(deductions)}\nMedical insurance type: {type(medical_insurance)}\nEmployee type type: {type(employee_type)}\nRSSB number type: {type(rssb_number)}\nEmail type: {type(email)}\nID number type: {type(id_number)}\nPhone type: {type(phone)}\nBank account type: {type(bank_account)}\nBank name type: {type(bank_name)}\nBank branch type: {type(bank_branch)}\nAddress type: {type(address)}\nDepartment type: {type(department)}\nPosition type: {type(position)}\nCompany name type: {type(company_name)}\nCompany address type: {type(company_address)}\nCompany TIN type: {type(company_tin)}\nCompany phone type: {type(company_phone)}\nCompany email type: {type(company_email)}\nCompany RSSB type: {type(company_rssb)}\nJoining date type: {type(joining_date)}\nDay one type: {type(day_one)}\nPay date type: {type(pay_date)}"))










            # reset the form
            # form = PayrollCalculator()

            return redirect(url_for('payroll.result',
                            employee_name=employee_name,
                            net_salary=net_salary,
                            rssb_number=rssb_number,
                            transport_allowance=transport_allowance,
                            other_allowances=other_allowances,
                            deductions=deductions,
                            medical_insurance=medical_insurance,
                            employee_type=employee_type,
                            email=email, id_number=id_number, phone=phone, address=address, department=department, position=position,
                            company_name=company_name, company_address=company_address, company_tin=company_tin, company_phone=company_phone,
                            company_email=company_email, company_rssb=company_rssb,
                            bank_account=bank_account, bank_name=bank_name, bank_branch=bank_branch,
                            joining_date=joining_date, day_one=day_one, pay_date=pay_date
                            ))
    except Exception as e:
        app.logger.error(f'An error occurred during generating form data: {e}')
    #return render_template('payroll/payroll_calculator.html', form=form, tittle="Payroll Calculator")
    return render_template('500.html')

@payroll_calculator_bp.route('/payroll-result', methods=['GET', 'POST'])

def result():
    information_list = []
    try:
        rssb_contributions = NsfContributions.query.all()
        for contribution in rssb_contributions:
            contribution_name = contribution.contribution_name
            if contribution_name == "maternity":
                maternity_er_rate = float(contribution.employer_rate)
                maternity_ee_rate = float(contribution.employee_rate)
            elif contribution_name == "pension":
                pension_er_rate = float(contribution.employer_rate)
                pension_ee_rate = float(contribution.employee_rate)
            elif contribution_name == "cbhi":
                cbhi_er_rate = float(contribution.employer_rate)
                cbhi_ee_rate = float(contribution.employee_rate)
        
        employee_name = request.args.get('employee_name') # retrieve employee name from the form
        if request.args.get('net_salary') == None:
            flash('Please provide the net salary', 'danger')
            app.logger.error('Net salary not provided')
            return redirect(url_for('payroll.payroll_calculator'))
        else:
            desired_net = float(request.args.get('net_salary'))
        transport_allowance = float(request.args.get('transport_allowance')) # value of the transport allowance from the form
        allowances = float(request.args.get('other_allowances')) # value of the other allowances from the form
        other_deductions = float(request.args.get('deductions')) # value of the other deductions from the form
        employee_type = request.args.get('employee_type') # value of the employee type from the form
        medical_insurance = float(request.args.get('medical_insurance')) / 100
        pay_date_str = request.args.get('pay_date')
        day_one = request.args.get('day_one')
        joining_date = request.args.get('joining_date')
        company_name = request.args.get('company_name')
        company_address = request.args.get('company_address')
        company_tin = request.args.get('company_tin')
        company_phone = request.args.get('company_phone')
        company_email = request.args.get('company_email')
        company_rssb = request.args.get('company_rssb')
        bank_account = request.args.get('bank_account')
        bank_name = request.args.get('bank_name')
        bank_branch = request.args.get('bank_branch')
        address = request.args.get('address')
        department = request.args.get('department')
        position = request.args.get('position')
        email = request.args.get('email')
        id_number = request.args.get('id_number')
        phone = request.args.get('phone')
        rssb_number = request.args.get('rssb_number')
        pay_date = datetime.strptime(pay_date_str, '%Y-%m-%d')
        print(type(pay_date))

        # log all the values from the form
        app.logger.info(f"Employee_name : {employee_name}\nNet_salary: {desired_net}\nTransport_allowance: {transport_allowance}\nOther_allowances: {allowances}\nDeductions: {other_deductions}\nMedical_insurance: {medical_insurance}\nEmployee_type: {employee_type}")

        contributions_rate = cbhi_ee_rate
        target_value = desired_net
        initial_guess = 1
        # Create an instance of the SalaryCalculator class
        calculator = None
        try:
            calculator = SalaryCalculator(
                allowances,
                transport_allowance,
                other_deductions,
                contributions_rate,
                pension_ee_rate,
                pension_er_rate,
                maternity_ee_rate,
                maternity_er_rate,
                medical_insurance,
                employee_type,
            )
        except Exception as e:
            logging.error(f'Error occurred: {e}')
            flash('An error occurred while initializing the calculator. Please try again.', 'danger')
            return redirect(url_for('payroll.payroll_calculator'))
        
        if calculator:
            results = calculator.goalseek(target_value, initial_guess)
            basic_needed, gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value = results

            total_payroll_details = (gross_needed + pension_er_value + maternity_er_value + (medical_insurance * basic_needed))
            app.logger.info(f"Employee_name : {employee_name}\nNet_salary: {desired_net}\nTransport_allowance: {transport_allowance}\nOther_allowances: {allowances}\nDeductions: {other_deductions}\nMedical_insurance: {medical_insurance}\nEmployee_type: {employee_type}\nGross_salary: {gross_needed}\PAYE: {paye}\nMonthly_pay: {net_salary_value - other_deductions}\nTotal_staff_cost: {total_payroll_details}")
            app.logger.info('=======================Payroll result page accessed========================')
            joining_date_formatted = datetime.strptime(joining_date, '%Y-%m-%d').strftime('%d/%m/%Y'),
            day_one_formatted = datetime.strptime(day_one, '%Y-%m-%d').strftime('%d/%m/%Y'),
            pay_date_formatted = datetime.strptime(pay_date_str, '%Y-%m-%d').strftime('%d/%m/%Y'),
            all_calculations_details = {
                "employee_name": "employee_name",
                "basic_needed": "{:,.0f}".format(round(basic_needed)),
                "gross_needed": "{:,.0f}".format(round(gross_needed)),
                "rama_ee": "{:,.0f}".format(round(rama_ee)),
                "cbhi_value": "{:,.0f}".format(round(cbhi_value)),
                "paye": "{:,.0f}".format(round(paye)),
                "net_bcbhi": "{:,.0f}".format(round(net_bcbhi)),
                "net_cbhi": "{:,.0f}".format(round(net_cbhi)),
                "pension_ee_value": "{:,.0f}".format(round(pension_ee_value)),
                "pension_er_value": "{:,.0f}".format(round(pension_er_value)),
                "total_pension_value": "{:,.0f}".format(round(pension_er_value + pension_ee_value)),
                "maternity_ee_value": "{:,.0f}".format(round(maternity_ee_value)),
                "maternity_er_value": "{:,.0f}".format(round(maternity_er_value)),
                "total_deductions_value": "{:,.0f}".format(round(total_deductions_value)),
                "net_salary_value": "{:,.0f}".format(round(net_salary_value)),
                "medical_insurance_value": "{:,.0f}".format(round(medical_insurance * basic_needed) * 2),
                "medical_insurance_er": "{:,.0f}".format(round(medical_insurance * basic_needed)),
                "total_maternity_value": "{:,.0f}".format(round(maternity_er_value + maternity_ee_value)),
                "medical_total": (medical_insurance * 200),
                "monthly_pyt": "{:,.0f}".format(round(net_salary_value - other_deductions)),
                "other_deductions": "{:,.0f}".format(other_deductions),
                "employee_type": "employee_type",
                "total_payroll_summary": "{:,.0f}".format(round(gross_needed + pension_er_value + maternity_er_value + (medical_insurance * basic_needed))),
                "total_payroll_details": "{:,.0f}".format(round(total_payroll_details))
                }
            information_list.append(all_calculations_details)
            return render_template('employees/simulator_payslip.html',
                transport_allowance = "{:,.0f}".format(round(transport_allowance)),
                employee_name=employee_name,
                basic_needed = "{:,.0f}".format(round(basic_needed)),
                gross_needed="{:,.0f}".format(round(gross_needed)),
                rama_ee="{:,.0f}".format(round(rama_ee)),
                cbhi_value="{:,.0f}".format(round(cbhi_value)),
                paye="{:,.0f}".format(round(paye)),
                net_bcbhi="{:,.0f}".format(round(net_bcbhi)),
                net_cbhi="{:,.0f}".format(round(net_cbhi)),
                pension_ee_value="{:,.0f}".format(round(pension_ee_value)),
                pension_er_value="{:,.0f}".format(round(pension_er_value)),
                total_pension_value="{:,.0f}".format(round(pension_er_value + pension_ee_value)),
                maternity_ee_value="{:,.0f}".format(round(maternity_ee_value)),
                maternity_er_value="{:,.0f}".format(round(maternity_er_value)),
                net_salary_val="{:,.0f}".format(round(net_salary_value - other_deductions)),
                amount_to_credit = "{:,.0f}".format(round(net_salary_value)),
                medical_insurance_value= all_calculations_details["medical_insurance_value"],
                medical_insurance_er = "{:,.0f}".format(round(medical_insurance * basic_needed)),
                total_maternity_value="{:,.0f}".format(round(maternity_er_value + maternity_ee_value)),
                medical_total=(medical_insurance * 200),
                monthly_pyt= all_calculations_details["monthly_pyt"],
                other_deductions="{:,.0f}".format(other_deductions),
                employee_type=employee_type,
                total_payroll_summary="{:,.0f}".format(round(gross_needed + pension_er_value + maternity_er_value + (medical_insurance * basic_needed))),
                total_payroll_details="{:,.0f}".format(round(total_payroll_details)),
                tittle="Payroll Results",
                allowances = "{:,.0f}".format(round(allowances)),
                month_name = pay_date.strftime('%B'),
                month_year = pay_date.strftime('%Y'),
                last_day_of_month = pay_date_formatted[0],
                day_one = day_one_formatted[0],
                days_worked = (pay_date - datetime.strptime(day_one, '%Y-%m-%d')).days + 1,
                joining_date = joining_date_formatted[0],
                # format the joining date to day/month/year
                company_name = company_name,
                company_address = company_address,
                company_tin = company_tin,
                company_phone = company_phone,
                company_email = company_email,
                company_rssb = company_rssb,
                bank_account = bank_account,
                bank_name = bank_name,
                bank_branch = bank_branch,
                address = address,
                department = department,
                position = position,
                email = email,
                id_number = id_number,
                phone = phone,
                rssb_number = rssb_number,
                total_payroll_deductions ="{:,.0f}".format(round(total_deductions_value + cbhi_value + other_deductions)),
                salary_in_words = num2words(round(net_salary_value - other_deductions), lang='en').title()
            )
        else:
            app.logger.error(f'An error occurred during the payroll calculation for {employee_name}')
            flash('An error occurred during the payroll calculation. Please try again.', 'danger')
            return redirect(url_for('payroll.payroll_calculator'))
    except Exception as e:
        logging.exception (f"An error occurred during the payroll calculation: {e}")
        app.logger.error(f'An error occurred during the payroll calculation: {e}')
        flash('An error occurred during the payroll calculation. Please try again.', 'danger')
        return redirect(url_for('payroll.payroll_calculator'))
