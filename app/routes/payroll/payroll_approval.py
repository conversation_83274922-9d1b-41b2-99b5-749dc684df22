from flask import flash, request, jsonify, redirect, url_for, render_template, session, Blueprint, send_file
from app.models.company_payroll_approval import PayrollApproval
from app.models.company import Payroll
from app.decorators.role_decorator import role_required
from app.utils.db_connection import DatabaseConnection
from flask import current_app as app
from app.routes.payroll.forms import PayrollApprovalForm
from datetime import datetime
import calendar
from sqlalchemy import extract, func
from app import db
from io import BytesIO
import pandas as pd
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch


payroll_approval = Blueprint('payroll_approval', __name__)



@payroll_approval.route('/pending_payroll', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def pending_payroll():
    """Display pending payroll Data."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Filter for only pending payrolls
            payroll_objects = (db_session.query(Payroll)
                       .filter(Payroll.status == 'Pending')
                       .all())
            payrolls = [payroll.to_dict() for payroll in payroll_objects]
        except Exception as e:
            app.logger.error(f"An error occurred while fetching pending payrolls: {str(e)}")
            payrolls = []
    try:
        app.logger.info(f"Rendering pending_payroll.html")
        return render_template('payroll/pending_payroll.html', payrolls=payrolls)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering pending_payroll.html: {str(e)}")
        flash("An error occurred while rendering the page.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

@payroll_approval.route('/approve_payroll/<payroll_id>', methods=['GET', 'POST'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def approve_payroll(payroll_id):
    """Displaying the approve payroll form."""
    form = PayrollApprovalForm()
    if request.method == 'POST':
        # Get the form data

        approver_id = session.get('user_id')
        approver_role = session.get('role')
        status = request.form.get('status')
        remarks = request.form.get('remarks')

        app.logger.info(f"status: {status}, remarks: {remarks}")

        # Get important details from the session
        database_name = session.get('database_name')


        # Create a database connection
        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                response = PayrollApproval.create_approval(
                    db_session, payroll_id, approver_id, approver_role, status, remarks
                )
                app.logger.info(f"response: {response}")
                if response == "Approval created successfully." or "successfully" in response.lower():
                    flash("Payroll request approved successfully.", 'success')
                elif "has already approved" in response:
                    flash(response, 'info')
                elif "not authorized" in response.lower():
                    flash(response, 'warning')
                elif "not found" in response.lower():
                    flash(response, 'error')
                else:
                    flash(response, 'warning')
            except Exception as e:
                app.logger.error(f"An error occurred while approving payroll request: {str(e)}")
                flash("An error occurred while approving payroll request.", 'danger')
    return render_template('payroll/approve_payroll.html', form=form)

@payroll_approval.route('/bulk_payroll_approval', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def bulk_payroll_approval():
    """Display the bulk payroll approval page with month/year selection."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Get available months/years with pending payrolls
    available_periods = []

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get distinct month/year combinations for pending payrolls
            pending_periods = (db_session.query(
                extract('year', Payroll.pay_date).label('year'),
                extract('month', Payroll.pay_date).label('month'),
                func.count(Payroll.payroll_id).label('payroll_count')
            )
            .filter(Payroll.status == 'Pending')
            .group_by(extract('year', Payroll.pay_date), extract('month', Payroll.pay_date))
            .order_by(extract('year', Payroll.pay_date).desc(), extract('month', Payroll.pay_date).desc())
            .all())

            for period in pending_periods:
                available_periods.append({
                    'year': int(period.year),
                    'month': int(period.month),
                    'month_name': calendar.month_name[int(period.month)],
                    'payroll_count': period.payroll_count,
                    'period_display': f"{calendar.month_name[int(period.month)]} {int(period.year)}"
                })

        except Exception as e:
            app.logger.error(f"An error occurred while fetching available periods: {str(e)}")
            available_periods = []

    try:
        app.logger.info(f"Rendering bulk_payroll_approval.html")
        return render_template('payroll/bulk_payroll_approval.html', available_periods=available_periods)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering bulk_payroll_approval.html: {str(e)}")
        flash("An error occurred while rendering the page.", 'danger')
        return redirect(url_for('admin_data.dashboard'))

@payroll_approval.route('/monthly_payroll_approval/<int:year>/<int:month>', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def monthly_payroll_approval(year, month):
    """Display pending payrolls for a specific month/year for bulk approval"""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Filter for pending payrolls in the specified month/year
            payroll_objects = (db_session.query(Payroll)
                       .filter(
                           Payroll.status == 'Pending',
                           extract('year', Payroll.pay_date) == year,
                           extract('month', Payroll.pay_date) == month
                       )
                       .order_by(Payroll.employee_name)
                       .all())
            payrolls = [payroll.to_dict() for payroll in payroll_objects]

            # Calculate comprehensive summary statistics
            total_employees = len(payrolls)
            total_basic_salary = sum(float(p.get('basic_salary', 0)) for p in payrolls)
            total_transport_allowance = sum(float(p.get('transport_allowance', 0)) for p in payrolls)
            total_housing_allowance = sum(float(p.get('housing_allowance', 0)) for p in payrolls)
            total_gross_salary = sum(float(p.get('gross_salary', 0)) for p in payrolls)

            # Pension totals
            total_employer_pension = sum(float(p.get('employer_pension', 0)) for p in payrolls)
            total_employee_pension = sum(float(p.get('employee_pension', 0)) for p in payrolls)
            total_pension = total_employer_pension + total_employee_pension

            # Maternity totals
            total_employer_maternity = sum(float(p.get('employer_maternity', 0)) for p in payrolls)
            total_employee_maternity = sum(float(p.get('employee_maternity', 0)) for p in payrolls)
            total_maternity = total_employer_maternity + total_employee_maternity

            # Other deductions
            total_paye = sum(float(p.get('payee', 0)) for p in payrolls)
            total_cbhi = sum(float(p.get('cbhi', 0)) for p in payrolls)
            total_deductions = sum(float(p.get('total_deductions', 0)) for p in payrolls)
            total_other_deductions = sum(float(p.get('other_deductions', 0)) for p in payrolls)
            total_advances = sum(float(p.get('advance', 0)) for p in payrolls)

            # Additional deductions and adjustments
            total_reimbursements = sum(float(p.get('reimbursement', 0)) for p in payrolls)
            total_brd_deductions = sum(float(p.get('brd_deductions', 0)) for p in payrolls)

            # Final totals
            total_net_salary = sum(float(p.get('net_salary', 0)) for p in payrolls)

            # Calculate Net To Pay: net_salary - other_deductions + reimbursements - brd_deductions - advances
            total_net_to_pay = total_net_salary - total_other_deductions + total_reimbursements - total_brd_deductions - total_advances

            summary = {
                'total_employees': total_employees,
                'total_basic_salary': total_basic_salary,
                'total_transport_allowance': total_transport_allowance,
                'total_housing_allowance': total_housing_allowance,
                'total_gross_salary': total_gross_salary,
                'total_employer_pension': total_employer_pension,
                'total_employee_pension': total_employee_pension,
                'total_pension': total_pension,
                'total_employer_maternity': total_employer_maternity,
                'total_employee_maternity': total_employee_maternity,
                'total_maternity': total_maternity,
                'total_paye': total_paye,
                'total_cbhi': total_cbhi,
                'total_deductions': total_deductions,
                'total_other_deductions': total_other_deductions,
                'total_advances': total_advances,
                'total_reimbursements': total_reimbursements,
                'total_brd_deductions': total_brd_deductions,
                'total_net_salary': total_net_salary,
                'total_net_to_pay': total_net_to_pay,
                'month_name': calendar.month_name[month],
                'year': year,
                'period_display': f"{calendar.month_name[month]} {year}"
            }

        except Exception as e:
            app.logger.error(f"An error occurred while fetching monthly payrolls: {str(e)}")
            payrolls = []
            summary = {}

    try:
        app.logger.info(f"Rendering monthly_payroll_approval.html for {month}/{year}")
        return render_template('payroll/monthly_payroll_approval.html',
                             payrolls=payrolls,
                             summary=summary,
                             year=year,
                             month=month)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering monthly_payroll_approval.html: {str(e)}")
        flash("An error occurred while rendering the page.", 'danger')
        return redirect(url_for('payroll_approval.bulk_payroll_approval'))

@payroll_approval.route('/bulk_approve_payroll', methods=['POST'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def bulk_approve_payroll():
    """Bulk approve or reject payrolls for a specific month/year."""
    try:
        # Get form data
        year = int(request.form.get('year'))
        month = int(request.form.get('month'))
        action = request.form.get('action')  # 'approve' or 'reject'
        remarks = request.form.get('remarks', '')

        # Get approver details from session
        approver_id = session.get('user_id')
        approver_role = session.get('role')

        # Validate action
        if action not in ['approve', 'reject']:
            flash("Invalid action specified.", 'danger')
            return redirect(url_for('payroll_approval.monthly_payroll_approval', year=year, month=month))

        # Set status based on action
        status = 'Approved' if action == 'approve' else 'Rejected'

        # Get important details from the session
        database_name = session.get('database_name')

        # Create a database connection
        db_connection = DatabaseConnection()

        # Connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                # Get all pending payrolls for the specified month/year
                payroll_objects = (db_session.query(Payroll)
                           .filter(
                               Payroll.status == 'Pending',
                               extract('year', Payroll.pay_date) == year,
                               extract('month', Payroll.pay_date) == month
                           )
                           .all())

                if not payroll_objects:
                    flash(f"No pending payrolls found for {calendar.month_name[month]} {year}.", 'warning')
                    return redirect(url_for('payroll_approval.bulk_payroll_approval'))

                # Process each payroll
                success_count = 0
                error_count = 0
                error_messages = []
                skipped_count = 0

                for payroll in payroll_objects:
                    try:
                        response = PayrollApproval.create_approval(
                            db_session, payroll.payroll_id, approver_id, approver_role, status, remarks
                        )

                        if response == "Approval created successfully." or "successfully" in response.lower():
                            success_count += 1
                        else:
                            error_count += 1
                            app.logger.error(f"Failed to {action} payroll for {payroll.employee_name}: {response}")

                            # Check if this is a "already approved" error
                            if "has already approved" in response:
                                skipped_count += 1
                                error_messages.append(f"{payroll.employee_name}: {response}")
                            else:
                                error_messages.append(f"{payroll.employee_name}: {response}")

                    except Exception as e:
                        error_count += 1
                        app.logger.error(f"Error processing payroll for {payroll.employee_name}: {str(e)}")
                        error_messages.append(f"{payroll.employee_name}: {str(e)}")

                # Show results
                if success_count > 0:
                    action_word = "approved" if action == 'approve' else "rejected"
                    flash(f"Successfully {action_word} {success_count} payroll(s) for {calendar.month_name[month]} {year}.", 'success')

                if skipped_count > 0:
                    flash(f"Skipped {skipped_count} payroll(s) that were already approved by the same role.", 'info')

                if error_count > 0:
                    # Show specific error messages for the first few errors
                    if error_messages:
                        if len(error_messages) <= 5:
                            # Show all errors if 5 or fewer
                            for error_msg in error_messages:
                                flash(error_msg, 'warning')
                        else:
                            # Show first 3 errors and a summary
                            for error_msg in error_messages[:3]:
                                flash(error_msg, 'warning')
                            flash(f"... and {len(error_messages) - 3} more errors. Check logs for full details.", 'warning')
                    else:
                        flash(f"Failed to process {error_count} payroll(s). Please check the logs for details.", 'warning')

                # Redirect back to bulk approval page
                return redirect(url_for('payroll_approval.bulk_payroll_approval'))

            except Exception as e:
                app.logger.error(f"An error occurred during bulk approval: {str(e)}")
                flash("An error occurred while processing the bulk approval.", 'danger')
                return redirect(url_for('payroll_approval.monthly_payroll_approval', year=year, month=month))

    except Exception as e:
        app.logger.error(f"An error occurred in bulk_approve_payroll: {str(e)}")
        flash("An error occurred while processing your request.", 'danger')
        return redirect(url_for('payroll_approval.bulk_payroll_approval'))
    
@payroll_approval.route('/payroll_approval_history', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def payroll_approval_history():
    """Display payroll approval history as a page with month/year selection."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Get available months/years with payroll approvals
    available_periods = []

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get distinct month/year combinations for payroll approvals
            approval_periods = (db_session.query(
                extract('year', PayrollApproval.created_at).label('year'),
                extract('month', PayrollApproval.created_at).label('month'),
                func.count(PayrollApproval.approval_id).label('approval_count')
            )
            .group_by(extract('year', PayrollApproval.created_at), extract('month', PayrollApproval.created_at))
            .order_by(extract('year', PayrollApproval.created_at).desc(), extract('month', PayrollApproval.created_at).desc())
            .all())

            for period in approval_periods:
                available_periods.append({
                    'year': int(period.year),
                    'month': int(period.month),
                    'month_name': calendar.month_name[int(period.month)],
                    'approval_count': period.approval_count,
                    'period_display': f"{calendar.month_name[int(period.month)]} {int(period.year)}"
                })

        except Exception as e:
            app.logger.error(f"An error occurred while fetching available periods: {str(e)}")
            available_periods = []

    try:
        app.logger.info(f"Rendering payroll_approval_history.html")
        return render_template('payroll/payroll_approval_history.html', available_periods=available_periods)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering payroll_approval_history.html: {str(e)}")
        flash("An error occurred while rendering the page.")
        return redirect(url_for('admin_data.dashboard'))
    
@payroll_approval.route('/payroll_approval_history/<int:year>/<int:month>', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def payroll_approval_history_month(year, month):
    """Display payroll approval history for a specific month/year."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Filter for payroll approvals in the specified month/year
            approval_objects = (db_session.query(PayrollApproval)
                       .filter(
                           extract('year', PayrollApproval.approval_date) == year,
                           extract('month', PayrollApproval.approval_date) == month
                       )
                       .order_by(PayrollApproval.approval_date.desc())
                       .all())
            approvals = [approval.to_dict() for approval in approval_objects]

        except Exception as e:
            app.logger.error(f"An error occurred while fetching payroll approvals: {str(e)}")
            approvals = []

    try:
        app.logger.info(f"Rendering payroll_approval_history_month.html for {month}/{year}")
        return render_template('payroll/payroll_approval_history_month.html',
                             approvals=approvals,
                             year=year,
                             month=month)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering payroll_approval_history_month.html: {str(e)}")
        flash("An error occurred while rendering the page.")
        return redirect(url_for('payroll_approval.payroll_approval_history'))
    
@payroll_approval.route('/approved_payroll_history', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def approved_payroll_history():
    """Display approved payroll history with month/year selection."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Get available months/years with approved payrolls
    available_periods = []

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get distinct month/year combinations for approved payrolls
            approved_periods = (db_session.query(
                extract('year', Payroll.pay_date).label('year'),
                extract('month', Payroll.pay_date).label('month'),
                func.count(Payroll.payroll_id).label('payroll_count')
            )
            .filter(Payroll.status == 'Approved')
            .group_by(extract('year', Payroll.pay_date), extract('month', Payroll.pay_date))
            .order_by(extract('year', Payroll.pay_date).desc(), extract('month', Payroll.pay_date).desc())
            .all())

            app.logger.info(f"Approved periods: {approved_periods}")
            for period in approved_periods:
                available_periods.append({
                    'year': int(period.year),
                    'month': int(period.month),
                    'month_name': calendar.month_name[int(period.month)],
                    'payroll_count': period.payroll_count,
                    'period_display': f"{calendar.month_name[int(period.month)]} {int(period.year)}"
                })

        except Exception as e:
            app.logger.error(f"An error occurred while fetching approved periods: {str(e)}")
            available_periods = []

    try:
        app.logger.info(f"Rendering approved_payroll_history.html")
        return render_template('payroll/approved_payroll_history.html', available_periods=available_periods)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering approved_payroll_history.html: {str(e)}")
        flash("An error occurred while rendering the page.")
        return redirect(url_for('admin_data.dashboard'))

@payroll_approval.route('/approved_payroll_history/<int:year>/<int:month>', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def monthly_approved_payroll(year, month):
    """Display approved payrolls for a specific month/year with approval details."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Filter for approved payrolls in the specified month/year with approval details
            from app.models.company import LeaveApproval

            payroll_objects = (db_session.query(Payroll)
                       .filter(
                           Payroll.status == 'Approved',
                           extract('year', Payroll.pay_date) == year,
                           extract('month', Payroll.pay_date) == month
                       )
                       .order_by(Payroll.employee_name)
                       .all())

            payrolls = []
            approval_summary = {}

            for payroll in payroll_objects:
                payroll_dict = payroll.to_dict()

                # Get "prepared by" information
                prepared_by_name = "Unknown"
                if payroll.created_by:
                    try:
                        prepared_by_name = LeaveApproval.get_approver_name(
                            payroll.created_by,
                            'hr',  # Default role for payroll creators
                            db_session
                        )
                        if not prepared_by_name or prepared_by_name == "Unknown":
                            prepared_by_name = "Unknown"
                    except Exception as e:
                        app.logger.warning(f"Could not fetch prepared by name for {payroll.created_by}: {str(e)}")
                        prepared_by_name = "Unknown"

                # Add prepared by information to payroll data
                payroll_dict.update({
                    'prepared_by': prepared_by_name,
                    'prepared_date': payroll.created_at.strftime('%d/%m/%Y %H:%M') if payroll.created_at else 'N/A'
                })

                # Get ALL approval details for this payroll
                approvals = (db_session.query(PayrollApproval)
                           .filter(PayrollApproval.payroll_id == payroll.payroll_id)
                           .filter(PayrollApproval.status == 'Approved')
                           .order_by(PayrollApproval.created_at.asc())
                           .all())

                approval_history = []
                if approvals:
                    for approval in approvals:
                        # Use the existing helper function to get approver name
                        try:
                            approver_name = LeaveApproval.get_approver_name(
                                approval.approver_id,
                                approval.approver_role,
                                db_session
                            )
                            if not approver_name or approver_name == "Unknown":
                                approver_name = "Unknown Approver"
                        except Exception as e:
                            app.logger.warning(f"Could not fetch approver name for {approval.approver_id}: {str(e)}")
                            approver_name = "Unknown Approver"

                        approval_history.append({
                            'approver_name': approver_name,
                            'approver_role': approval.approver_role,
                            'approval_date': approval.created_at.strftime('%d/%m/%Y %H:%M') if approval.created_at else 'N/A',
                            'approval_remarks': approval.remarks or 'No remarks',
                            'approval_id': approval.approval_id
                        })

                        # Track approval summary for the period
                        if approver_name not in approval_summary:
                            approval_summary[approver_name] = {
                                'count': 0,
                                'role': approval.approver_role,
                                'latest_date': approval.created_at
                            }
                        approval_summary[approver_name]['count'] += 1
                        if approval.created_at > approval_summary[approver_name]['latest_date']:
                            approval_summary[approver_name]['latest_date'] = approval.created_at

                    # Add approval details to payroll data (for backward compatibility, use the last approver)
                    last_approval = approval_history[-1] if approval_history else None
                    if last_approval:
                        payroll_dict.update({
                            'approver_name': last_approval['approver_name'],
                            'approver_role': last_approval['approver_role'],
                            'approval_date': last_approval['approval_date'],
                            'approval_remarks': last_approval['approval_remarks'],
                            'approval_id': last_approval['approval_id']
                        })
                else:
                    # Fallback for payrolls without approval records
                    payroll_dict.update({
                        'approver_name': 'System',
                        'approver_role': 'Automatic',
                        'approval_date': 'N/A',
                        'approval_remarks': 'No approval record found',
                        'approval_id': None
                    })

                # Add complete approval history
                payroll_dict['approval_history'] = approval_history

                payrolls.append(payroll_dict)

            # Calculate comprehensive summary statistics
            total_employees = len(payrolls)
            total_basic_salary = sum(float(p.get('basic_salary', 0)) for p in payrolls)
            total_transport_allowance = sum(float(p.get('transport_allowance', 0)) for p in payrolls)
            total_housing_allowance = sum(float(p.get('housing_allowance', 0)) for p in payrolls)
            total_gross_salary = sum(float(p.get('gross_salary', 0)) for p in payrolls)

            # Pension totals
            total_employer_pension = sum(float(p.get('employer_pension', 0)) for p in payrolls)
            total_employee_pension = sum(float(p.get('employee_pension', 0)) for p in payrolls)
            total_pension = total_employer_pension + total_employee_pension

            # Maternity totals
            total_employer_maternity = sum(float(p.get('employer_maternity', 0)) for p in payrolls)
            total_employee_maternity = sum(float(p.get('employee_maternity', 0)) for p in payrolls)
            total_maternity = total_employer_maternity + total_employee_maternity

            # Other deductions
            total_paye = sum(float(p.get('payee', 0)) for p in payrolls)
            total_cbhi = sum(float(p.get('cbhi', 0)) for p in payrolls)
            total_deductions = sum(float(p.get('total_deductions', 0)) for p in payrolls)
            total_other_deductions = sum(float(p.get('other_deductions', 0)) for p in payrolls)
            total_advances = sum(float(p.get('advance', 0)) for p in payrolls)

            # Additional deductions and adjustments
            total_reimbursements = sum(float(p.get('reimbursement', 0)) for p in payrolls)
            total_brd_deductions = sum(float(p.get('brd_deductions', 0)) for p in payrolls)

            # Final totals
            total_net_salary = sum(float(p.get('net_salary', 0)) for p in payrolls)

            # Calculate Net To Pay: net_salary - other_deductions + reimbursements - brd_deductions - advances
            total_net_to_pay = total_net_salary - total_other_deductions + total_reimbursements - total_brd_deductions - total_advances

            summary = {
                'total_employees': total_employees,
                'total_basic_salary': total_basic_salary,
                'total_transport_allowance': total_transport_allowance,
                'total_housing_allowance': total_housing_allowance,
                'total_gross_salary': total_gross_salary,
                'total_employer_pension': total_employer_pension,
                'total_employee_pension': total_employee_pension,
                'total_pension': total_pension,
                'total_employer_maternity': total_employer_maternity,
                'total_employee_maternity': total_employee_maternity,
                'total_maternity': total_maternity,
                'total_paye': total_paye,
                'total_cbhi': total_cbhi,
                'total_deductions': total_deductions,
                'total_other_deductions': total_other_deductions,
                'total_advances': total_advances,
                'total_reimbursements': total_reimbursements,
                'total_brd_deductions': total_brd_deductions,
                'total_net_salary': total_net_salary,
                'total_net_to_pay': total_net_to_pay,
                'month_name': calendar.month_name[month],
                'year': year,
                'period_display': f"{calendar.month_name[month]} {year}"
            }

        except Exception as e:
            app.logger.error(f"An error occurred while fetching approved payrolls: {str(e)}")
            payrolls = []
            summary = {}

    try:
        app.logger.info(f"Rendering monthly_approved_payroll.html for {month}/{year}")
        return render_template('payroll/monthly_approved_payroll.html',
                             payrolls=payrolls,
                             summary=summary,
                             approval_summary=approval_summary,
                             year=year,
                             month=month,
                             selected_year=year,
                             selected_month=month)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering monthly_approved_payroll.html: {str(e)}")
        flash("An error occurred while rendering the page.")
        return redirect(url_for('payroll_approval.approved_payroll_history'))

@payroll_approval.route('/payroll_approval_details/<payroll_id>', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def payroll_approval_details(payroll_id):
    """Display detailed approval history for a specific payroll."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            from app.models.company import LeaveApproval

            # Get the payroll record
            payroll = db_session.query(Payroll).filter(Payroll.payroll_id == payroll_id).first()
            if not payroll:
                flash("Payroll record not found.", 'error')
                return redirect(url_for('payroll_approval.approved_payroll_history'))

            # Get all approval records for this payroll
            approvals = (db_session.query(PayrollApproval)
                        .filter(PayrollApproval.payroll_id == payroll_id)
                        .order_by(PayrollApproval.created_at.asc())
                        .all())

            approval_history = []
            for approval in approvals:
                # Use the existing helper function to get approver name
                try:
                    approver_name = LeaveApproval.get_approver_name(
                        approval.approver_id,
                        approval.approver_role,
                        db_session
                    )
                    if not approver_name or approver_name == "Unknown":
                        approver_name = "Unknown Approver"
                except Exception as e:
                    app.logger.warning(f"Could not fetch approver name for {approval.approver_id}: {str(e)}")
                    approver_name = "Unknown Approver"

                approval_history.append({
                    'approval_id': approval.approval_id,
                    'approver_name': approver_name,
                    'approver_role': approval.approver_role,
                    'status': approval.status,
                    'remarks': approval.remarks or 'No remarks',
                    'created_at': approval.created_at,
                    'updated_at': approval.updated_at
                })

            payroll_data = payroll.to_dict()

            # Get "prepared by" information
            prepared_by_name = "Unknown"
            if payroll.created_by:
                try:
                    prepared_by_name = LeaveApproval.get_approver_name(
                        payroll.created_by,
                        'hr',  # Default role for payroll creators
                        db_session
                    )
                    if not prepared_by_name or prepared_by_name == "Unknown":
                        prepared_by_name = "Unknown"
                except Exception as e:
                    app.logger.warning(f"Could not fetch prepared by name for {payroll.created_by}: {str(e)}")
                    prepared_by_name = "Unknown"

            # Add prepared by information to payroll data
            payroll_data.update({
                'prepared_by': prepared_by_name,
                'prepared_date': payroll.created_at.strftime('%d/%m/%Y %H:%M') if payroll.created_at else 'N/A'
            })

        except Exception as e:
            app.logger.error(f"An error occurred while fetching approval details: {str(e)}")
            flash("An error occurred while fetching approval details.", 'danger')
            return redirect(url_for('payroll_approval.approved_payroll_history'))

    try:
        app.logger.info(f"Rendering payroll_approval_details.html for payroll {payroll_id}")
        return render_template('payroll/payroll_approval_details.html',
                             payroll=payroll_data,
                             approval_history=approval_history)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering payroll_approval_details.html: {str(e)}")
        flash("An error occurred while rendering the page.")
        return redirect(url_for('payroll_approval.approved_payroll_history'))

@payroll_approval.route('/export_approved_payroll_excel/<int:year>/<int:month>', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def export_approved_payroll_excel(year, month):
    """Export approved payrolls for a specific month/year to Excel."""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            from app.models.company import LeaveApproval

            # Filter for approved payrolls in the specified month/year
            payroll_objects = (db_session.query(Payroll)
                       .filter(
                           Payroll.status == 'Approved',
                           extract('year', Payroll.pay_date) == year,
                           extract('month', Payroll.pay_date) == month
                       )
                       .order_by(Payroll.employee_name)
                       .all())

            if not payroll_objects:
                flash('No approved payrolls found for the selected period.', 'warning')
                return redirect(url_for('payroll_approval.approved_payroll_history'))

            # Prepare data for Excel
            excel_data = []

            for payroll in payroll_objects:
                # Get "prepared by" information
                prepared_by_name = "Unknown"
                if payroll.created_by:
                    try:
                        prepared_by_name = LeaveApproval.get_approver_name(
                            payroll.created_by,
                            'hr',
                            db_session
                        )
                        if not prepared_by_name or prepared_by_name == "Unknown":
                            prepared_by_name = "Unknown"
                    except Exception as e:
                        app.logger.warning(f"Could not fetch prepared by name for {payroll.created_by}: {str(e)}")
                        prepared_by_name = "Unknown"

                # Get ALL approval details
                approvals = (db_session.query(PayrollApproval)
                           .filter(PayrollApproval.payroll_id == payroll.payroll_id)
                           .filter(PayrollApproval.status == 'Approved')
                           .order_by(PayrollApproval.created_at.asc())
                           .all())

                # Collect all approver information
                approver_names = []
                approver_roles = []
                approval_dates = []

                if approvals:
                    for approval in approvals:
                        try:
                            approver_name = LeaveApproval.get_approver_name(
                                approval.approver_id,
                                approval.approver_role,
                                db_session
                            )
                            if not approver_name or approver_name == "Unknown":
                                approver_name = "Unknown Approver"
                        except Exception as e:
                            app.logger.warning(f"Could not fetch approver name for {approval.approver_id}: {str(e)}")
                            approver_name = "Unknown Approver"

                        approver_names.append(approver_name)
                        approver_roles.append(approval.approver_role)
                        approval_dates.append(approval.created_at.strftime('%d/%m/%Y %H:%M') if approval.created_at else 'N/A')

                # Format approver information for Excel
                all_approvers = " | ".join(approver_names) if approver_names else "Unknown Approver"
                all_roles = " | ".join(approver_roles) if approver_roles else "Unknown"
                all_dates = " | ".join(approval_dates) if approval_dates else "N/A"

                # Calculate net to pay
                net_to_pay = (payroll.net_salary or 0) - (payroll.other_deductions or 0) + (payroll.reimbursement or 0) - (payroll.brd_deductions or 0) - (payroll.advance or 0)

                excel_data.append([
                    payroll.employee_name,
                    payroll.job_title or 'N/A',
                    int(payroll.basic_salary or 0),
                    int(payroll.transport_allowance or 0),
                    int(payroll.housing_allowance or 0),
                    int(payroll.gross_salary or 0),
                    int(payroll.employer_pension or 0),
                    int(payroll.employee_pension or 0),
                    int(payroll.employer_maternity or 0),
                    int(payroll.employee_maternity or 0),
                    int(payroll.payee or 0),
                    int(payroll.cbhi or 0),
                    int(payroll.total_deductions or 0),
                    int(payroll.net_salary or 0),
                    int(payroll.other_deductions or 0),
                    int(payroll.advance or 0),
                    int(payroll.reimbursement or 0),
                    int(payroll.brd_deductions or 0),
                    int(net_to_pay),
                    prepared_by_name,
                    payroll.created_at.strftime('%d/%m/%Y %H:%M') if payroll.created_at else 'N/A',
                    all_approvers,
                    all_roles,
                    all_dates,
                    len(approvals) if approvals else 0,
                    payroll.pay_date.strftime('%d/%m/%Y') if payroll.pay_date else 'N/A'
                ])

        except Exception as e:
            app.logger.error(f"An error occurred while preparing Excel data: {str(e)}")
            flash("An error occurred while preparing export data.", 'danger')
            return redirect(url_for('payroll_approval.approved_payroll_history'))

    # Create DataFrame
    columns = [
        'Employee Name', 'Job Title', 'Basic Salary', 'Transport Allowance', 'Housing Allowance',
        'Gross Salary', 'Employer Pension', 'Employee Pension', 'Employer Maternity', 'Employee Maternity',
        'PAYE', 'CBHI', 'Total Deductions', 'Net Salary', 'Other Deductions', 'Advances',
        'Reimbursements', 'BRD Deductions', 'Net To Pay', 'Prepared By', 'Prepared Date',
        'All Approvers', 'Approver Roles', 'Approval Dates', 'Number of Approvals', 'Pay Date'
    ]

    df = pd.DataFrame(excel_data, columns=columns)

    # Create Excel file
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name=f'Approved Payroll {calendar.month_name[month]} {year}')

        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets[f'Approved Payroll {calendar.month_name[month]} {year}']

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # Create filename
    filename = f"Approved_Payroll_{calendar.month_name[month]}_{year}.xlsx"

    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

# PDF Export - Commented out
# @payroll_approval.route('/export_approved_payroll_pdf/<int:year>/<int:month>', methods=['GET'])
# @role_required(['hr', 'manager', 'accountant', 'company_hr'])
# def export_approved_payroll_pdf(year, month):
#     """Export approved payrolls for a specific month/year to PDF."""
#     # Get important details from the session
#     database_name = session.get('database_name')
#     company_id = session.get('company_id')
#
#     # Create a database connection
#     db_connection = DatabaseConnection()
#
#     # Connect to the database
#     with db_connection.get_session(database_name) as db_session:
#         try:
#             from app.models.company import LeaveApproval, Company
#
#             # Get company information
#             company_data = Company.get_company_by_id(company_id)
#             company_name = company_data.get('company_name', 'Company') if company_data else 'Company'
#
#             # Filter for approved payrolls in the specified month/year
#             payroll_objects = (db_session.query(Payroll)
#                        .filter(
#                            Payroll.status == 'Approved',
#                            extract('year', Payroll.pay_date) == year,
#                            extract('month', Payroll.pay_date) == month
#                        )
#                        .order_by(Payroll.employee_name)
#                        .all())
#
#             if not payroll_objects:
#                 flash('No approved payrolls found for the selected period.', 'warning')
#                 return redirect(url_for('payroll_approval.approved_payroll_history'))
#
#             # Create PDF
#             buffer = BytesIO()
#             doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)
#
#             # Container for the 'Flowable' objects
#             elements = []
#
#             # Define styles
#             styles = getSampleStyleSheet()
#             title_style = ParagraphStyle(
#                 'CustomTitle',
#                 parent=styles['Heading1'],
#                 fontSize=16,
#                 spaceAfter=30,
#                 alignment=1  # Center alignment
#             )
#
#             # Add title
#             title = Paragraph(f"{company_name}<br/>Approved Payroll Report<br/>{calendar.month_name[month]} {year}", title_style)
#             elements.append(title)
#             elements.append(Spacer(1, 20))

#             # Prepare table data
#             table_data = []
#
#             # Headers
#             headers = [
#                 'Employee', 'Job Title', 'Basic Salary', 'Transport', 'Housing', 'Gross',
#                 'PAYE', 'CBHI', 'Net Salary', 'Deductions', 'Advances', 'Reimbursements',
#                 'Net To Pay', 'Prepared By', 'Approved By', 'Pay Date'
#             ]
#             table_data.append(headers)
#
#             # Data rows
#             for payroll in payroll_objects:
#                 # Get "prepared by" information
#                 prepared_by_name = "Unknown"
#                 if payroll.created_by:
#                     try:
#                         prepared_by_name = LeaveApproval.get_approver_name(
#                             payroll.created_by,
#                             'hr',
#                             db_session
#                         )
#                         if not prepared_by_name or prepared_by_name == "Unknown":
#                             prepared_by_name = "Unknown"
#                     except Exception as e:
#                         app.logger.warning(f"Could not fetch prepared by name for {payroll.created_by}: {str(e)}")
#                         prepared_by_name = "Unknown"
#
#                 # Get approval details
#                 approval = (db_session.query(PayrollApproval)
#                            .filter(PayrollApproval.payroll_id == payroll.payroll_id)
#                            .filter(PayrollApproval.status == 'Approved')
#                            .order_by(PayrollApproval.created_at.desc())
#                            .first())
#
#                 approver_name = "Unknown"
#                 if approval:
#                     try:
#                         approver_name = LeaveApproval.get_approver_name(
#                             approval.approver_id,
#                             approval.approver_role,
#                             db_session
#                         )
#                         if not approver_name or approver_name == "Unknown":
#                             approver_name = "Unknown"
#                     except Exception as e:
#                         app.logger.warning(f"Could not fetch approver name for {approval.approver_id}: {str(e)}")
#
#                 # Calculate net to pay
#                 net_to_pay = (payroll.net_salary or 0) - (payroll.other_deductions or 0) + (payroll.reimbursement or 0) - (payroll.brd_deductions or 0) - (payroll.advance or 0)
#
#                 row = [
#                     payroll.employee_name[:20] if payroll.employee_name else 'N/A',  # Truncate long names
#                     payroll.job_title[:15] if payroll.job_title else 'N/A',
#                     f"{payroll.basic_salary:,.0f}" if payroll.basic_salary else '0',
#                     f"{payroll.transport_allowance:,.0f}" if payroll.transport_allowance else '0',
#                     f"{payroll.housing_allowance:,.0f}" if payroll.housing_allowance else '0',
#                     f"{payroll.gross_salary:,.0f}" if payroll.gross_salary else '0',
#                     f"{payroll.payee:,.0f}" if payroll.payee else '0',
#                     f"{payroll.cbhi:,.0f}" if payroll.cbhi else '0',
#                     f"{payroll.net_salary:,.0f}" if payroll.net_salary else '0',
#                     f"{payroll.other_deductions:,.0f}" if payroll.other_deductions else '0',
#                     f"{payroll.advance:,.0f}" if payroll.advance else '0',
#                     f"{payroll.reimbursement:,.0f}" if payroll.reimbursement else '0',
#                     f"{net_to_pay:,.0f}",
#                     prepared_by_name[:15],  # Truncate long names
#                     approver_name[:15],     # Truncate long names
#                     payroll.pay_date.strftime('%d/%m/%Y') if payroll.pay_date else 'N/A'
#                 ]
#                 table_data.append(row)

#             # Create table
#             table = Table(table_data)
#
#             # Style the table
#             table.setStyle(TableStyle([
#                 # Header row styling
#                 ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
#                 ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
#                 ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
#                 ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
#                 ('FONTSIZE', (0, 0), (-1, 0), 8),
#                 ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
#
#                 # Data rows styling
#                 ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
#                 ('FONTSIZE', (0, 1), (-1, -1), 7),
#                 ('GRID', (0, 0), (-1, -1), 1, colors.black),
#                 ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
#
#                 # Alternating row colors
#                 ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
#             ]))
#
#             elements.append(table)
#
#             # Add summary
#             elements.append(Spacer(1, 20))
#             total_employees = len(payroll_objects)
#             total_net_pay = sum((p.net_salary or 0) - (p.other_deductions or 0) + (p.reimbursement or 0) - (p.brd_deductions or 0) - (p.advance or 0) for p in payroll_objects)
#
#             summary_text = f"Total Employees: {total_employees} | Total Net Pay: {total_net_pay:,.0f} RWF"
#             summary = Paragraph(summary_text, styles['Normal'])
#             elements.append(summary)
#
#             # Build PDF
#             doc.build(elements)
#
#         except Exception as e:
#             app.logger.error(f"An error occurred while generating PDF: {str(e)}")
#             flash("An error occurred while generating PDF.")
#             return redirect(url_for('payroll_approval.approved_payroll_history'))
#
#     buffer.seek(0)
#
#     # Create filename
#     filename = f"Approved_Payroll_{calendar.month_name[month]}_{year}.pdf"
#
#     return send_file(
#         buffer,
#         as_attachment=True,
#         download_name=filename,
#         mimetype='application/pdf'
#     )

@payroll_approval.route('/bulk_payroll_history', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def bulk_payroll_history():
    """Redirect to the new approved payroll history route."""
    return redirect(url_for('payroll_approval.approved_payroll_history'))

@payroll_approval.route('/historical_payroll_data', methods=['GET'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def historical_payroll_data():
    """Display historical payroll data with month/year selection"""
    # Get important details from the session
    database_name = session.get('database_name')

    # Create a database connection
    db_connection = DatabaseConnection()

    # Get available months/years with payroll data
    available_periods = []

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get distinct month/year combinations for payroll data
            payroll_periods = (db_session.query(
                extract('year', Payroll.pay_date).label('year'),
                extract('month', Payroll.pay_date).label('month'),
                func.count(Payroll.payroll_id).label('payroll_count')
            )
            .group_by(extract('year', Payroll.pay_date), extract('month', Payroll.pay_date))
            .order_by(extract('year', Payroll.pay_date).desc(), extract('month', Payroll.pay_date).desc())
            .all())
            app.logger.info(f"Payroll periods: {payroll_periods}")
            available_periods = []
            for period in payroll_periods:
                available_periods.append({
                    'year': int(period.year),
                    'month': int(period.month),
                    'month_name': calendar.month_name[int(period.month)],
                    'payroll_count': period.payroll_count,
                    'period_display': f"{calendar.month_name[int(period.month)]} {int(period.year)}"
                })
            app.logger.info(f"Available periods: {available_periods}")

        except Exception as e:
            app.logger.error(f"An error occurred while fetching available periods: {str(e)}")
            available_periods = []

    try:
        app.logger.info(f"Rendering historical_payroll_data.html")
        return render_template('payroll/historical_payroll_data.html', available_periods=available_periods)
    except Exception as e:
        app.logger.error(f"An error occurred while rendering historical_payroll_data.html: {str(e)}")
        flash("An error occurred while rendering the page.")
        return redirect(url_for('admin_data.dashboard'))
