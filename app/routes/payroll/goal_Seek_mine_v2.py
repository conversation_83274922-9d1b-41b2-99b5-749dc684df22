import scipy.optimize as opt
from app.models.central import TaxBracket, CasualsTaxBracket, SecondEmployeeTaxBracket, ConsultantTaxBracket
from datetime import datetime, date
class SalaryCalculator:
    def __init__(self, allowances, transport_allowance, 
                 other_deductions, contributions_rate, 
                 pension_ee_rate, pension_er_rate, maternity_ee_rate, 
                 maternity_er_rate, medical_insurance, 
                 employee_type, calculation_date=None):
        self.allowances = float(allowances)
        self.transport_allowance = float(transport_allowance)
        self.other_deductions = float(other_deductions)
        self.contributions_rate = float(contributions_rate)
        self.pension_ee_rate = float(pension_ee_rate)
        self.pension_er_rate = float(pension_er_rate)
        self.maternity_ee_rate = float(maternity_ee_rate)
        self.maternity_er_rate = float(maternity_er_rate)
        self.medical_insurance = float(medical_insurance)
        self.employee_type = employee_type
         # If no date is provided, default to today's date
        self.calculation_date = calculation_date if calculation_date else datetime.now().date()

        def __str__(self):
            return f"""
            SalaryCalculator({self.allowances}, {self.transport_allowance}, {self.other_deductions},
            {self.contributions_rate}, {self.pension_ee_rate}, {self.pension_er_rate}, {self.maternity_ee_rate}, 
            {self.maternity_er_rate}, {self.medical_insurance}, {self.employee_type}, {self.calculation_date})
            """                                                                                                                                                                                                                                                                                          

    def gross_salary(self, basic_salary):
        return basic_salary + self.allowances + self.transport_allowance

    def calculate_PAYE(self, gross_salary, employee_type):
        """Calculate PAYE for a given gross salary and employee type
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: PAYE for the employee
        """
        # Check if the employee type and get the tax brackets
        # And compute the PAYE based on the tax brackets of that type
        tax_brackets = []
        if employee_type == 'permanent':
            tax_brackets = TaxBracket.get_taxbrackets()
            tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])
        elif employee_type == 'casual':
            casuals_tax_bracket_instance = CasualsTaxBracket()
            tax_brackets = casuals_tax_bracket_instance.get_casuals_taxbrackets()
            tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])

        elif employee_type == 'second_employee':
            tax_brackets = SecondEmployeeTaxBracket.get_second_employee_taxbrackets(self)
            tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])

        elif employee_type == 'consultant':
            tax_brackets = ConsultantTaxBracket.get_consultant_taxbrackets()

        tax = 0.0
        for bracket in tax_brackets:
            if gross_salary > bracket['lower_bound']:
                taxable_income = min(gross_salary, bracket['upper_bound']) - bracket['lower_bound']
                tax += (taxable_income * bracket['rate'])
            else:
                break

        return tax

    def calculate_net_bcbhi(self, gross_salary, total_deductions):
        """Calculate net salary before CBHI
        args:
            gross_salary(float): Gross salary of the employee
            total_deductions(float): Total deductions of the employee
            returns:
                float: Net salary before CBHI
        """
        return gross_salary - total_deductions

    def calculate_net_after_cbhi(self, net_bcbhi, employee_type):
        """Calculate net salary after CBHI
        args:
            net_bcbhi(float): Net salary before CBHI
            employee_type(str): Type of the employee
            returns:
                float: Net salary after CBHI
        """
        # Check if the employee type is consultant and set the CBHI rate = 0
        if employee_type == 'consultant':
            net_after_cbhi = net_bcbhi
            return net_after_cbhi
        else:
            return net_bcbhi - (net_bcbhi * self.contributions_rate)

    def net_salary(self, net_bcbhi, cbhi):
        return net_bcbhi - cbhi

    def pension_ee(self, gross_salary, employee_type):
        """Calculate employee pension based on the applicable formula."""
        if employee_type == 'consultant':
            return 0.0

        if self.calculation_date >= date(2025, 1, 1):  # New formula after Jan 1, 2025
            return gross_salary * self.pension_ee_rate
        else:  # Old formula (before Jan 1, 2025)
            if not hasattr(self, 'transport_allowance'):
                raise ValueError("Missing transport allowance for old pension formula.")
            return (gross_salary - self.transport_allowance) * self.pension_ee_rate

    def pension_er(self, gross_salary, employee_type):
        """Calculate employer pension based on the applicable formula."""
        if employee_type == 'consultant':
            return 0.0

        if self.calculation_date >= date(2025, 1, 1):  # New formula after Jan 1, 2025
            # Calculate the occupation hazard contribution
            # This is 2% of the gross salary
            return ((gross_salary * self.pension_er_rate) + ((gross_salary - self.transport_allowance) * 0.02))
        else:  # Old formula (before Jan 1, 2025)
            if not hasattr(self, 'transport_allowance'):
                raise ValueError("Missing transport allowance for old pension formula.")
            return (gross_salary - self.transport_allowance) * self.pension_er_rate
        

    def maternity_ee(self, gross_salary, employee_type):
        """Calculate employee maternity
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: Employee maternity
        """
        # Check if the employee type is consultant and set the maternity rate = 0
        if employee_type == 'consultant':
            return 0.0
        else:
            return (gross_salary - self.transport_allowance) * self.maternity_ee_rate

    def maternity_er(self, gross_salary, employee_type):
        """Calculate employer maternity
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: Employer maternity
        """
        # Check if the employee type is consultant and set the maternity rate = 0
        if employee_type == 'consultant':
            return 0.0
        else:
            return (gross_salary - self.transport_allowance) * self.maternity_er_rate

    def total_deductions(self, paye, pension_ee, maternity_ee, rama_ee):
        return pension_ee + paye + maternity_ee + rama_ee

    def cbhi(self, net_bcbhi, net_after_cbhi, employee_type):
        """Calculate CBHI
        args:
            net_bcbhi(float): Net salary before CBHI
            net_after_cbhi(float): Net salary after CBHI
            employee_type(str): Type of the employee
            returns:
                float: CBHI
        """
        # Check if the employee type is consultant and set the CBHI rate = 0
        # Otherwise, calculate the CBHI
        if employee_type == 'consultant':
            return 0.0
        else:
            return net_bcbhi - net_after_cbhi

    def calculate_all(self, basic_salary):
        gross = self.gross_salary(basic_salary)
        paye = self.calculate_PAYE(gross, self.employee_type)
        pension_ee_value = self.pension_ee(gross, self.employee_type)
        pension_er_value = self.pension_er(gross, self.employee_type)
        maternity_ee_value = self.maternity_ee(gross, self.employee_type)
        maternity_er_value = self.maternity_er(gross, self.employee_type)
        if self.employee_type == 'consultant':
            rama_ee = 0.0
        else:
            rama_ee = (gross - self.allowances - self.transport_allowance) * self.medical_insurance
        total_deductions_value = self.total_deductions(paye, pension_ee_value, maternity_ee_value, rama_ee)
        net_bcbhi = gross - total_deductions_value
        net_cbhi = self.calculate_net_after_cbhi(net_bcbhi, self.employee_type)
        cbhi_value = self.cbhi(net_bcbhi, net_cbhi, self.employee_type)
        net_salary_value = self.net_salary(net_bcbhi, cbhi_value)
        return gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value

    def goalseek(self, target_net_salary, initial_guess):
        def equation_to_solve(basic_salary):
            _, _, _, _, _, _, _, _, _, _, _, net_salary_value = self.calculate_all(basic_salary)
            return net_salary_value - target_net_salary

        solution = opt.fsolve(equation_to_solve, initial_guess)
        gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value = self.calculate_all(solution[0])
        return solution[0], gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value
    