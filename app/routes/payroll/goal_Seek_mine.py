import scipy.optimize as opt
from app.models.central import TaxBracket, CasualsTaxBracket, SecondEmployeeTaxBracket, ConsultantTaxBracket
from flask import current_app
from datetime import datetime, date
from decimal import Decimal
import numpy as np

class SalaryCalculator:
    def __init__(self, allowances, transport_allowance, other_deductions, contributions_rate, pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate, rama_contributions_rate, employee_type, calculation_date=None, rama_er_rate=None):
        """
        Initialize the SalaryCalculator with the given parameters.
        """
        from app.helpers.auxillary import Auxillary
        self.allowances = Auxillary.to_decimal(allowances)
        self.transport_allowance = Auxillary.to_decimal(transport_allowance)
        self.other_deductions = Auxillary.to_decimal(other_deductions)
        self.contributions_rate = Auxillary.to_decimal(contributions_rate)
        self.pension_ee_rate = Auxillary.to_decimal(pension_ee_rate)
        self.pension_er_rate = Auxillary.to_decimal(pension_er_rate)
        self.maternity_ee_rate = Auxillary.to_decimal(maternity_ee_rate)
        self.maternity_er_rate = Auxillary.to_decimal(maternity_er_rate)
        self.rama_contributions_rate = Auxillary.to_decimal(rama_contributions_rate)
        self.rama_er_rate = Auxillary.to_decimal(rama_er_rate)
        self.employee_type = employee_type
        self.calculation_date = calculation_date if calculation_date else datetime.now().date()

    def __str__(self):
        return f"""
        SalaryCalculator({self.allowances}, {self.transport_allowance}, {self.other_deductions},
        {self.contributions_rate}, {self.pension_ee_rate}, {self.pension_er_rate}, {self.maternity_ee_rate},
        {self.maternity_er_rate}, {self.employee_type}, {self.calculation_date})
        """

    def gross_salary(self, basic_salary):
        """Calculate gross salary based on the basic salary
        args:
            basic_salary(float or ndarray): Basic salary of the employee
        returns:
            float: Gross salary of the employee
        """
        # current_app.logger.info(f"Calculating gross salary from basic salary: {basic_salary} and the type {type(basic_salary)}")

        # If basic_salary is a numpy ndarray, extract the scalar value
        if isinstance(basic_salary, np.ndarray):
            basic_salary = basic_salary.item()  # Get the scalar value

        # Convert basic_salary to Decimal if it is not already
        if not isinstance(basic_salary, Decimal):
            basic_salary = Decimal(str(basic_salary))

        return basic_salary + self.allowances + self.transport_allowance

    def calculate_PAYE(self, gross_salary, employee_type):
        """Calculate PAYE for a given gross salary and employee type
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: PAYE for the employee
        """
        # Check if the employee type and get the tax brackets
        # And compute the PAYE based on the tax brackets of that type
        employee_type = employee_type.strip()
        #current_app.logger.info(f"type of gross salary {type(gross_salary)} and the value {gross_salary}")
        # Ensure gross_salary is a single Decimal value, not a NumPy array
        #if isinstance(gross_salary, np.ndarray):
            #gross_salary = gross_salary.item()  # Extracts the single element

        # Ensure it remains a Decimal
        #if not isinstance(gross_salary, Decimal):
            #gross_salary = Decimal(str(gross_salary))
        if employee_type == 'permanent':
            # current_app.logger.info('inside permanent')
            try:
                tax_brackets = TaxBracket.get_taxbrackets()
                # current_app.logger.info(f"tax brackets {tax_brackets} and the type {type(tax_brackets)}")
                # Sort the tax brackets by lower_bound
                tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])
                # current_app.logger.info(f"sorted tax brackes {tax_brackets} and the type {type(tax_brackets)}")
            except Exception as e:
                current_app.logger.error(f"Error getting tax brackets: {e}")
                raise
        elif employee_type == 'casual':
            tax_brackets = CasualsTaxBracket.get_casuals_taxbrackets()
            tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])

        elif employee_type == 'second employee':
            tax_brackets = SecondEmployeeTaxBracket.get_second_employee_taxbrackets()
            tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])

        elif employee_type == 'consultant':
            tax_brackets = ConsultantTaxBracket.get_consultant_taxbrackets()

        tax = Decimal('0.00')
        #current_app.logger.info(tax_brackets)
        for bracket in tax_brackets:
            if gross_salary > bracket['lower_bound']:
                # current_app.logger.info('calculating taxable income')
                upper_bound = bracket['upper_bound']
                lower_bound = bracket['lower_bound']
                rate = bracket['rate']
                # current_app.logger.info(f"rate {rate} and the type {type(rate)}")
                # current_app.logger.info(f"upper_bound: {upper_bound} and the type is {type(upper_bound)}, lower_bound: {lower_bound} and the type {type(lower_bound)} , gross_salary: {gross_salary} and the type {type(gross_salary)}")
                try:
                    taxable_income = min((gross_salary), bracket['upper_bound']) - bracket['lower_bound']
                    # current_app.logger.info(f"taxable income: {taxable_income} and the type {type(taxable_income)}")
                    tax += (taxable_income * bracket['rate'])
                    # current_app.logger.info(f"Taxable income: {taxable_income}, Tax: {tax}")
                except Exception as e:
                    current_app.logger.error(f"Error calculating taxable income: {e}")
            else:
                break

        try:
            # current_app.logger.info(f"Final PAYE: {tax}")
            return tax
        except Exception as e:
            current_app.logger.error(f"Error calculating PAYE: {e}")
            return 0.0

    def calculate_net_bcbhi(self, gross_salary, total_deductions):
        """Calculate net salary before CBHI
        args:
            gross_salary(float): Gross salary of the employee
            total_deductions(float): Total deductions of the employee
            returns:
                float: Net salary before CBHI
        """
        return gross_salary - total_deductions

    def calculate_net_after_cbhi(self, net_bcbhi, employee_type):
        """Calculate net salary after CBHI
        args:
            net_bcbhi(float): Net salary before CBHI
            employee_type(str): Type of the employee
            returns:
                float: Net salary after CBHI
        """
        # Check if the employee type is consultant and set the CBHI rate = 0
        if employee_type == 'consultant':
            net_after_cbhi = net_bcbhi
            return net_after_cbhi
        else:
            return net_bcbhi - (net_bcbhi * self.contributions_rate)

    def net_salary(self, net_bcbhi, cbhi):
        return net_bcbhi - cbhi

    def pension_ee(self, gross_salary, employee_type):
        """Calculate employee pension based on the applicable formula."""
        if employee_type == 'consultant':
            return Decimal('0.00')

        # Ensure gross_salary is Decimal using your pattern
        from app.helpers.auxillary import Auxillary
        gross_decimal = Auxillary.to_decimal(gross_salary)

        if self.calculation_date >= date(2025, 1, 1):  # New formula after Jan 1, 2025
            result = gross_decimal * self.pension_ee_rate
        else:  # Old formula (before Jan 1, 2025)
            if not hasattr(self, 'transport_allowance'):
                raise ValueError("Missing transport allowance for old pension formula.")
            result = (gross_decimal - self.transport_allowance) * self.pension_ee_rate

        # Round to whole number for consistency using your method
        return Auxillary.round_to_decimal(result)

    def pension_er(self, gross_salary, employee_type):
        """Calculate employer pension based on the applicable formula."""
        if employee_type == 'consultant':
            return Decimal('0.00')

        # Ensure gross_salary is Decimal using your pattern
        from app.helpers.auxillary import Auxillary
        gross_decimal = Auxillary.to_decimal(gross_salary)
        occupation_hazard = Decimal("0.02")

        # current_app.logger.info(f"occupation hazard {occupation_hazard} and the type {type(occupation_hazard)}")
        # current_app.logger.info(f"gross salary {gross_decimal} and the type {type(gross_decimal)}")
        # current_app.logger.info(f"transport allowance {self.transport_allowance} and the type {type(self.transport_allowance)}")
        # current_app.logger.info(f"pension er rate {self.pension_er_rate} and the type {type(self.pension_er_rate)}")

        if self.calculation_date >= date(2025, 1, 1):  # New formula after Jan 1, 2025
            # Calculate the occupation hazard contribution
            # This is 2% of the gross salary minus transport allowance
            result = (gross_decimal * self.pension_er_rate) + ((gross_decimal - self.transport_allowance) * occupation_hazard)
        else:  # Old formula (before Jan 1, 2025)
            if not hasattr(self, 'transport_allowance'):
                raise ValueError("Missing transport allowance for old pension formula.")
            result = (gross_decimal - self.transport_allowance) * self.pension_er_rate

        # Round to whole number for consistency using your method
        return Auxillary.round_to_decimal(result)


    def maternity_ee(self, gross_salary, employee_type):
        """Calculate employee maternity
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: Employee maternity
        """
        # current_app.logger.info(f"inside maternity ee method")
        # Check if the employee type is consultant and set the maternity rate = 0
        if employee_type == 'consultant':
            return Decimal('0.00')
        else:
            try:
                # current_app.logger.info(f"gross salary {gross_salary} and the type {type(gross_salary)}")
                # current_app.logger.info(f"transport allowance {self.transport_allowance} and the type {type(self.transport_allowance)}")
                # current_app.logger.info(f"maternity ee rate {self.maternity_ee_rate} and the type {type(self.maternity_ee_rate)}")
                # current_app.logger.info(f"maternity er rate {self.maternity_er_rate} and the type {type(self.maternity_er_rate)}")
                # Ensure all values are Decimal for consistent calculation
                gross_decimal = Decimal(str(gross_salary)) if not isinstance(gross_salary, Decimal) else gross_salary
                transport_decimal = Decimal(str(self.transport_allowance)) if not isinstance(self.transport_allowance, Decimal) else self.transport_allowance
                rate_decimal = Decimal(str(self.maternity_ee_rate)) if not isinstance(self.maternity_ee_rate, Decimal) else self.maternity_ee_rate
                # Calculate with proper Decimal arithmetic
                result = (gross_decimal - transport_decimal) * rate_decimal
                # Round to whole number for consistency
                from app.helpers.auxillary import Auxillary
                return Auxillary.round_to_decimal(result)
            except Exception as e:
                current_app.logger.error(f"Error calculating maternity ee: {e}")
                return Decimal('0.00')

    def maternity_er(self, gross_salary, employee_type):
        """Calculate employer maternity
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: Employer maternity
        """
        # Check if the employee type is consultant and set the maternity rate = 0
        if employee_type == 'consultant':
            return Decimal('0.00')
        else:
            try:
                # current_app.logger.info(f"gross salary {gross_salary} and the type {type(gross_salary)}")
                # current_app.logger.info(f"transport allowance {self.transport_allowance} and the type {type(self.transport_allowance)}")
                # current_app.logger.info(f"maternity er rate {self.maternity_er_rate} and the type {type(self.maternity_er_rate)}")
                # current_app.logger.info(f"maternity ee rate {self.maternity_ee_rate} and the type {type(self.maternity_ee_rate)}")
                # Ensure all values are Decimal for consistent calculation
                gross_decimal = Decimal(str(gross_salary)) if not isinstance(gross_salary, Decimal) else gross_salary
                transport_decimal = Decimal(str(self.transport_allowance)) if not isinstance(self.transport_allowance, Decimal) else self.transport_allowance
                rate_decimal = Decimal(str(self.maternity_er_rate)) if not isinstance(self.maternity_er_rate, Decimal) else self.maternity_er_rate
                # Calculate with proper Decimal arithmetic
                result = (gross_decimal - transport_decimal) * rate_decimal
                # Round to whole number for consistency
                from app.helpers.auxillary import Auxillary
                rounded_result = Auxillary.round_to_decimal(result)
                # current_app.logger.info(f"result {rounded_result} and the type {type(rounded_result)}")
                return rounded_result
            except Exception as e:
                current_app.logger.error(f"Error calculating maternity er: {e}")
                return Decimal('0.00')

    def rama_er(self, gross_salary, employee_type):
        """Calculate employer rama contribution
        args:
            gross_salary(float): Gross salary of the employee
            employee_type(str): Type of the employee
            returns:
                float: Employer rama contribution
        """
        # Check if the employee type is consultant and set the rama rate = 0
        if employee_type == 'consultant':
            return Decimal('0.00')
        else:
            try:
                # current_app.logger.info(f"calculating rama er from gross salary: {gross_salary}")
                # Ensure all values are Decimal for consistent calculation
                gross_decimal = Decimal(str(gross_salary)) if not isinstance(gross_salary, Decimal) else gross_salary
                allowances_decimal = Decimal(str(self.allowances)) if not isinstance(self.allowances, Decimal) else self.allowances
                transport_decimal = Decimal(str(self.transport_allowance)) if not isinstance(self.transport_allowance, Decimal) else self.transport_allowance

                # Use the rama_er_rate from the constructor
                rama_er_rate = Decimal(str(self.rama_er_rate)) if not isinstance(self.rama_er_rate, Decimal) else self.rama_er_rate

                # Calculate with proper Decimal arithmetic
                result = (gross_decimal - allowances_decimal - transport_decimal) * rama_er_rate
                # Round to whole number for consistency
                from app.helpers.auxillary import Auxillary
                rounded_result = Auxillary.round_to_decimal(result)
                # current_app.logger.info(f"rama er calculated: {rounded_result}")
                return rounded_result
            except Exception as e:
                current_app.logger.error(f"Error calculating rama er: {e}")
                return Decimal('0.00')

    def total_deductions(self, paye, pension_ee, maternity_ee, rama_ee):
        return pension_ee + paye + maternity_ee + rama_ee

    def cbhi(self, net_bcbhi, net_after_cbhi, employee_type):
        """Calculate CBHI
        args:
            net_bcbhi(Decimal): Net salary before CBHI
            net_after_cbhi(Decimal): Net salary after CBHI
            employee_type(str): Type of the employee
            returns:
                Decimal: CBHI
        """
        # Check if the employee type is consultant and set the CBHI rate = 0
        # Otherwise, calculate the CBHI
        if employee_type == 'consultant':
            return Decimal('0.00')
        else:
            # Ensure both values are Decimal using your pattern
            from app.helpers.auxillary import Auxillary
            net_bcbhi_decimal = Auxillary.to_decimal(net_bcbhi)
            net_after_cbhi_decimal = Auxillary.to_decimal(net_after_cbhi)
            result = net_bcbhi_decimal - net_after_cbhi_decimal

            # Round to whole number for consistency using your method
            return Auxillary.round_to_decimal(result)

    def calculate_all(self, basic_salary):
        try:
            # current_app.logger.info(f"calculating gross salary from basic salary: {basic_salary}")
            gross = self.gross_salary(basic_salary)
            # current_app.logger.info(f"gross salary calculated: {gross}")
        except Exception as e:
            current_app.logger.error(f"Error calculating gross salary: {e}")
            gross = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating PAYE from gross salary: {gross}")
            # Calculate PAYE
            paye = self.calculate_PAYE(gross, self.employee_type)
            # current_app.logger.info(f"PAYE calculated: {paye}")
        except Exception as e:
            current_app.logger.error(f"Error calculating PAYE: {e}")
            paye = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating pension ee from gross salary: {gross}")
            pension_ee_value = self.pension_ee(gross, self.employee_type)
            # current_app.logger.info(f"pension ee calculated: {pension_ee_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating pension ee: {e}")
            pension_ee_value = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating pension ee from gross salary: {gross}")
            pension_er_value = self.pension_er(gross, self.employee_type)
            # current_app.logger.info(f"pension er calculated: {pension_er_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating pension er: {e}")
            pension_er_value = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating maternity ee from gross salary: {gross}")
            maternity_ee_value = self.maternity_ee(gross, self.employee_type)
            # current_app.logger.info(f"maternity ee calculated: {maternity_ee_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating maternity ee: {e}")
            maternity_ee_value = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating maternity er from gross salary: {gross}")
            maternity_er_value = self.maternity_er(gross, self.employee_type)
            # current_app.logger.info(f"maternity er calculated: {maternity_er_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating maternity er: {e}")
            maternity_er_value = Decimal('0.00')
        if self.employee_type == 'consultant':
            rama_ee = Decimal('0.00')
        else:
            try:
                # current_app.logger.info(f"calculating rama ee from gross salary: {gross}")
                rama_ee = (gross - self.allowances - self.transport_allowance) * self.rama_contributions_rate
                # current_app.logger.info(f"rama ee calculated: {rama_ee}")
            except Exception as e:
                current_app.logger.error(f"Error calculating rama ee: {e}")
                rama_ee = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating total deductions from gross salary: {gross}")
            total_deductions_value = self.total_deductions(paye, pension_ee_value, maternity_ee_value, rama_ee)
            # current_app.logger.info(f"total deductions calculated: {total_deductions_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating total deductions: {e}")
            total_deductions_value = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating net bcbhi from gross salary: {gross}")
            net_bcbhi = gross - total_deductions_value
            # current_app.logger.info(f"net bcbhi calculated: {net_bcbhi}")
        except Exception as e:
            current_app.logger.error(f"Error calculating net bcbhi: {e}")
            net_bcbhi = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating net cbhi from gross salary: {gross}")
            net_cbhi = self.calculate_net_after_cbhi(net_bcbhi, self.employee_type)
            # current_app.logger.info(f"net cbhi calculated: {net_cbhi}")
        except Exception as e:
            current_app.logger.error(f"Error calculating net cbhi: {e}")
            net_cbhi = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating cbhi from gross salary: {gross}")
            cbhi_value = self.cbhi(net_bcbhi, net_cbhi, self.employee_type)
            # current_app.logger.info(f"cbhi calculated: {cbhi_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating cbhi: {e}")
            cbhi_value = Decimal('0.00')
        try:
            # current_app.logger.info(f"calculating net salary from gross salary: {gross}")
            net_salary_value = self.net_salary(net_bcbhi, cbhi_value)
            # current_app.logger.info(f"net salary calculated: {net_salary_value}")
        except Exception as e:
            current_app.logger.error(f"Error calculating net salary: {e}")
            net_salary_value = Decimal('0.00')
        try:
            # current_app.logger.info(f"returning all values")
            return gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value
        except Exception as e:
            current_app.logger.error(f"Error returning all values: {e}")
            #return and turple with zero values
            return (Decimal('0.00'),) * 12

    def goalseek(self, target_net_salary, initial_guess):
        def equation_to_solve(basic_salary):
            # Convert input to Decimal for precision
            basic_salary_decimal = Decimal(str(basic_salary[0]))
            _, _, _, _, _, _, _, _, _, _, _, net_salary_value = self.calculate_all(basic_salary_decimal)

            # Convert result to float for fsolve
            return float(net_salary_value - target_net_salary)

        # Convert initial guess to float for fsolve
        solution = opt.fsolve(equation_to_solve, float(initial_guess))

        # Convert solution back to Decimal before further calculations
        solution_decimal = Decimal(str(solution[0]))

        gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value = self.calculate_all(solution_decimal)

        return solution_decimal, gross, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value
    
    def calculate_total_staff_cost(self, basic_salary):
        """Calculate total staff cost based on the basic salary."""
        
        (
            gross, rama_ee, _, _, _, _, _, pension_er, _, maternity_er, _, _
        ) = self.calculate_all(basic_salary)

        #log the calculated values
        current_app.logger.info(f"Gross: {gross}, Rama EE: {rama_ee}, Pension ER: {pension_er}, Maternity ER: {maternity_er}") 

        # Assume RAMA ER = RAMA EE, unless employee is a consultant
        rama_er = Decimal('0.00')
        if self.employee_type != 'consultant':
            rama_er = rama_ee

        return gross + pension_er + maternity_er + rama_er

class SalaryCalculatorGross(SalaryCalculator):
    """Calculate the salary based on the employee gross salary."""
    def calculate_basic_salary(self, gross_salary):
        """Calculate the basic salary based on the gross salary."""
        try:
            # current_app.logger.info(f"Calculating basic salary from gross salary: {gross_salary}")
            # Calculate the basic salary
            basic_salary = gross_salary - self.allowances - self.transport_allowance
            # current_app.logger.info(f"Basic salary calculated: {basic_salary}")
            return basic_salary
        except Exception as e:
            current_app.logger.error(f"Error calculating basic salary: {e}")
            return None
