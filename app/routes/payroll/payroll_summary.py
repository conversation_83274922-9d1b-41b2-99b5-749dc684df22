from flask import Blueprint, request,render_template, session, jsonify, url_for, redirect, flash, send_file
import logging
from app.decorators.hr_decorator import hr_required
from app.utils.db_connection import DatabaseConnection
from app.models.company import Employee, Deductions, Insurance, Payroll, Reimbursements, Attendance
from app.models.central import NsfContributions
from app.routes.payroll.goal_Seek_mine import SalaryCalculator, SalaryCalculatorGross
import datetime
from app.routes.payroll.forms import PayrollForm, UpdatePayrollForm
from decimal import Decimal
import pandas as pd
from io import BytesIO
import calendar
from datetime import datetime, timedelta
from app.models.central import TaxBracket, CasualsTaxBracket, SecondEmployeeTaxBracket, ConsultantTaxBracket, BrdDeductions
from flask import current_app
from app.models.central import Company
from app.helpers.auxillary import Auxillary
from flask import Response
import pandas as pd
from openpyxl import Workbook
from openpyxl.drawing.image import Image
import os
import PIL.Image as PILImage
import traceback
import tempfile
import uuid
from app.decorators.role_decorator import role_required
from app.helpers.pdf_generator import PDFGenerator
from app.models.company_salary_advance import SalaryAdvanceRequest, InstallmentPlan
from app.models.company_payroll_approval import PayrollApproval
from app.decorators.subscription_decorator import require_subscription

payroll_summary_bp = Blueprint('payroll_summary', __name__)

@payroll_summary_bp.route('/payroll_summary', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
@require_subscription
def payroll_summary():
    form = PayrollForm()

    # Retrieve attendance_service status from the session
    attendance_service = session.get('attendance_service')

    # If the request is POST, validate the form and process the selected date
    if form.validate_on_submit():
        form_data = form.data
        pay_date = form.pay_date.data
        if attendance_service:
            timesheet_applicable = form.timesheet_applicable.data
        else:
            timesheet_applicable = 'no'

        company_id = session.get('company_id')
        if not company_id:
            message = "Company ID is missing in session"
            flash(message, 'danger')
            return jsonify({'success': False, 'message': message}), 404

        database_name = session.get('database_name')
        if database_name is None:
            flash("something went wrong, try again", 'danger')
            current_app.logger.error("Database name is missing in session")
            return jsonify({'success': False, 'message': message}), 404

        # RSSB Contributions fetching logic
        try:
            rssb_contributions = NsfContributions.get_valid_contributions(pay_date)
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting RSSB contributions: {e}")
            rssb_contributions = NsfContributions.get_nsf_contributions()
        if rssb_contributions is None:
            message = "No RSSB contributions found."
            flash(message, 'danger')
            return jsonify({'success':False, 'message': message}), 400
        for contribution in rssb_contributions:
            contribution_name = contribution.contribution_name
            if contribution_name.lower() == "maternity":
                maternity_er_rate = contribution.employer_rate
                maternity_ee_rate = contribution.employee_rate
            elif contribution_name.lower() == "pension":
                pension_er_rate = contribution.employer_rate
                pension_ee_rate = contribution.employee_rate
            elif contribution_name.lower() == "cbhi":
                cbhi_er_rate = contribution.employer_rate
                cbhi_ee_rate = contribution.employee_rate

        # DB Connection logic
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)

            if not employees:
                message = "No employees found."
                flash(message, 'danger')
                current_app.logger.error("No employees found.")
                return jsonify({'success': False, 'message': message}), 404

            employees_with_deductions = []

            for employee in employees:
                employee_status = employee.get('is_active')
                employee_gross = employee.get('gross_salary')
                employee_salary = employee['gross_salary']

                approved_advances = []
                for advance in employee['salary_advance_requests']:
                    if advance['status'] == 'approved':
                        # Append only the approved requests
                        approved_advances.append(advance)
                        # send the approved advances plans to an email
                        try:
                            subject = f"Approved Salary Advance for {employee['first_name']} {employee['last_name']}"
                            message = f"Dear {employee['first_name']},\n\nYour salary advance request has been approved. Please check your payroll summary for details.\n\nBest regards,\nHR Team"
                            current_app.logger.info(f"Sending email to {employee['email']} with subject: {subject}")
                            # Here you would call your email sending function
                            email = "<EMAIL>"
                            sent = Auxillary.send_netpipo_email(email, subject, message)
                        except Exception as e:
                            current_app.logger.error(f"An error occurred while sending email: {e}")
                            sent = False
                            

                due_installments = []
                try:
                    if len(approved_advances) > 0:
                        for advance in approved_advances:
                            for installment in advance['installment_plans']:
                                if installment['due_date'].month == current_month and installment['due_date'].year == current_year:
                                    due_installments.append(installment)
                        # Calculate the total amount due for the month
                        total_due = sum(installment['planned_amount'] for installment in due_installments)
                    else:
                        total_due = 0
                except Exception as e:
                    current_app.logger.error(f"An error occurred while getting due installments: {e}")
                    total_due = 0

                if employee_status == 'no':
                    continue
                try:
                    insurance = Insurance.get_insurances(db_session)
                    if not insurance or len(insurance) == 0:
                        employee_insurance = 0
                        rama_ee_rate = 0
                        rama_er_rate = 0
                    else:
                        employee_insurance = insurance[0]["employee_rate"]
                        employee_insurance = insurance[0]["employer_rate"]
                        # Check if the insurance name is rama
                        if insurance[0]["insurance_name"] == "rama" and employee['employee_type'] != 'casual':
                            rama_ee_rate = insurance[0]["employee_rate"]
                            rama_er_rate = insurance[0]["employer_rate"]
                        else:
                            rama_ee_rate = 0
                            rama_er_rate = 0

                    # get the current months and year , number of days in the month from the pay_date
                    current_month = pay_date.month
                    current_year = pay_date.year
                    total_days_in_month = calendar.monthrange(current_year, current_month)[1]

                    # Initialize variables to avoid undefined errors
                    appliacable_net_salary = None
                    appliacable_gross_salary = None
                    total_days = total_days_in_month  # Default to full month

                    if attendance_service:
                        try:
                            # Retrieve Timesheet information
                            timesheet = Attendance.get_employee_attendance(db_session, employee, current_month, current_year, total_days_in_month)

                            # Get the total number of days worked by the employee
                            if timesheet:
                                total_days = timesheet['paid_days']
                                # Check if the employee salary is affected by attendance
                                attendance_applicable = employee['attendance_applicable']

                                if attendance_applicable == 'yes':
                                    appliacable_net_salary = timesheet['applicable_net_salary']
                                    current_app.logger.info(f"Employee {employee['employee_id']} has attendance-adjusted net salary: {appliacable_net_salary}")
                                elif attendance_applicable == 'no':
                                    if employee['net_salary'] is not None:
                                        appliacable_net_salary = employee['net_salary']
                                        current_app.logger.info(f"Employee {employee['employee_id']} has fixed net salary: {appliacable_net_salary}")
                                    elif employee['gross_salary'] is not None:
                                        appliacable_gross_salary = employee['gross_salary']
                                        current_app.logger.info(f"Employee {employee['employee_id']} has fixed gross salary: {appliacable_gross_salary}")
                                    else:
                                        current_app.logger.error(f"Employee {employee['employee_id']} has no salary information")
                                        continue
                                elif attendance_applicable is None:
                                    appliacable_net_salary = timesheet['applicable_net_salary']
                                    current_app.logger.info(f"Employee {employee['employee_id']} has default attendance-adjusted net salary: {appliacable_net_salary}")
                            else:
                                current_app.logger.error(f"No timesheet found for employee {employee['employee_id']}")
                                # Instead of skipping, use the full salary
                                if employee['net_salary'] is not None:
                                    appliacable_net_salary = employee['net_salary']
                                    current_app.logger.info(f"Using full net salary for employee {employee['employee_id']}: {appliacable_net_salary}")
                                elif employee['gross_salary'] is not None:
                                    appliacable_gross_salary = employee['gross_salary']
                                    current_app.logger.info(f"Using full gross salary for employee {employee['employee_id']}: {appliacable_gross_salary}")
                                else:
                                    current_app.logger.error(f"Employee {employee['employee_id']} has no salary information and no timesheet")
                                    continue
                        except Exception as e:
                            current_app.logger.error(f"An error occurred while getting timesheet data for employee {employee['employee_id']}: {e}")
                            import traceback
                            current_app.logger.error(traceback.format_exc())

                            # Instead of skipping, use the full salary as fallback
                            if employee['net_salary'] is not None:
                                appliacable_net_salary = employee['net_salary']
                                current_app.logger.info(f"Using full net salary as fallback for employee {employee['employee_id']}: {appliacable_net_salary}")
                            elif employee['gross_salary'] is not None:
                                appliacable_gross_salary = employee['gross_salary']
                                current_app.logger.info(f"Using full gross salary as fallback for employee {employee['employee_id']}: {appliacable_gross_salary}")
                            else:
                                current_app.logger.error(f"Employee {employee['employee_id']} has no salary information")
                                continue

                    employee_id = employee['employee_id']
                    transport_allowance = employee["transport_allowance"]
                    allowances = employee["allowances"]
                    salary_advance = employee["salary_advance_balance"]

                    contributions_rate = cbhi_ee_rate

                    #set the target value based on the response from the user
                    if timesheet_applicable == 'no':
                        target_value = employee["net_salary"]
                    elif timesheet_applicable == 'yes':
                        # Make sure applicable_net_salary is defined for employees with net salary
                        if employee['attendance_applicable'] == 'yes' and 'applicable_net_salary' in locals():
                            target_value = appliacable_net_salary
                        else:
                            # If applicable_net_salary is not defined, use the employee's net_salary
                            target_value = employee["net_salary"]

                        # Adjust allowances based on days worked
                        transport_allowance = (employee["transport_allowance"] / total_days_in_month) * total_days
                        allowances = (employee["allowances"] / total_days_in_month) * total_days
                    else:
                        target_value = employee["net_salary"]

                    # Log the target value for debugging
                    current_app.logger.info(f"Target value for employee {employee['employee_id']}: {target_value}")
                    initial_guess = Decimal('1.00')
                    employee_type = employee["employee_type"]

                    # Retrieve deductions and reimbursements
                    emp_deductions = Deductions.get_deduction_for_given_month_and_year_for_employee(db_session, employee_id,pay_date.month, pay_date.year)
                    total_deductions = sum(d['deduction_amount'] for d in emp_deductions)
                    emp_reimbursements = Reimbursements.get_reimbursement_for_given_month_and_year_for_employee(db_session, employee_id, pay_date.month, pay_date.year)
                    total_reimbursements = sum(r['reimbursement_amount'] for r in emp_reimbursements)


                    # check if the salary info
                    net_salary = employee["net_salary"]
                    gross = employee['gross_salary']
                    total_staff_cost = employee['total_staff_cost']

                    # Check if net_salary is not None (even if it's 0, we should process it)
                    if net_salary is not None:
                        try:
                            # Create an instance of the SalaryCalculator class
                            try:
                                total_deductions = Decimal(total_deductions)

                                # Log the target value for debugging
                                current_app.logger.info(f"Processing employee with net_salary: {net_salary}, target_value: {target_value}")

                                calculator = SalaryCalculator(allowances, transport_allowance, total_deductions, contributions_rate, pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate, rama_ee_rate, employee_type)

                            except Exception as e:
                                current_app.logger.error(f"Error creating SalaryCalculator instance: {str(e)}")
                                current_app.logger.error(f"Employee: {employee['first_name']} {employee['last_name']}, net_salary: {net_salary}")
                                import traceback
                                current_app.logger.error(traceback.format_exc())

                            try:
                                results = calculator.goalseek(target_value, initial_guess)
                                # Initialize some variables with zero value and add them to the values as we loop
                                (basic_needed, gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                                maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = results

                                current_app.logger.info(f"Successfully calculated salary for employee with net_salary: {employee['employee_id']}")

                            except Exception as e:
                                current_app.logger.error(f"Error calculating salary for employee {employee['employee_id']}: {str(e)}")
                                current_app.logger.error(f"Employee: {employee['first_name']} {employee['last_name']}, net_salary: {net_salary}, target_value: {target_value}")
                                import traceback
                                current_app.logger.error(traceback.format_exc())

                        except Exception as e:
                            current_app.logger.error(f"Error calculating salary for employee {employee['employee_id']}: {str(e)}")
                            current_app.logger.error(f"Employee: {employee['first_name']} {employee['last_name']}, net_salary: {net_salary}")
                            flash("Error calculating salary", "danger")
                            continue
                    elif gross:
                        # sample gross
                        sample_gross = gross
                        # Salary calculations
                        try:
                            if timesheet_applicable == 'yes':
                                # Calculate gross salary based on the timesheet
                                sample_gross = sample_gross / total_days_in_month * total_days
                                # Calculate transport allowance and allowances based on the timesheet
                                transport_allowance = transport_allowance / total_days_in_month * total_days
                                allowances = allowances / total_days_in_month * total_days

                            else:
                                sample_gross = sample_gross
                                transport_allowance = transport_allowance
                                allowances = allowances
                            calculate = SalaryCalculatorGross(
                                allowances, transport_allowance, total_deductions,
                                contributions_rate, pension_ee_rate, pension_er_rate,
                                maternity_ee_rate, maternity_er_rate, rama_ee_rate, employee_type, pay_date)
                            result10 = calculate.calculate_basic_salary(sample_gross)
                            # Calculate all
                            try:
                                all_results = calculate.calculate_all(result10)
                                # Unpack calculated results
                                (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                                maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = map(Auxillary.round_to_decimal, all_results)
                                basic_needed = Auxillary.round_to_decimal(result10)
                            except Exception as e:
                                current_app.logger.error(f"An error occurred while calculating all results: {e}")

                        except Exception as e:
                            current_app.logger.error(f"An error occurred while calculating the basic salary: {e}")
                    elif total_staff_cost:
                        # Calculate the gross salary based on total staff cost
                        try:
                            total_other_allowances = employee['total_allowances']
                            gross_salary = Employee.calculate_gross_based_on_total_staff_cost(db_session, employee, calculation_date=pay_date.date())
                            current_app.logger.info(f"Calculated gross_salary from total_staff_cost: {gross_salary}")

                            if timesheet_applicable == 'yes':
                                # Calculate the total_other_allowances based on timesheet
                                total_other_allowances = Decimal(total_other_allowances) / Decimal(total_days_in_month) * Decimal(total_days)
                                # Calculate the gross salary based on timesheet
                                gross_salary = Decimal(gross_salary) / Decimal(total_days_in_month) * Decimal(total_days)
                                # Calculate transport allowance and other allowances based on timesheet
                                allowances = Decimal(allowances) / Decimal(total_days_in_month) * Decimal(total_days)
                                transport_allowance = Decimal(transport_allowance) / Decimal(total_days_in_month) * Decimal(total_days)
                            else:
                                # No adjustments needed for non-timesheet employees
                                gross_salary = Decimal(gross_salary)
                                total_other_allowances = Decimal(total_other_allowances)
                                allowances = Decimal(allowances)
                                transport_allowance = Decimal(transport_allowance)
                            try:

                                # Use the same SalaryCalculatorGross as other salary types for consistency
                                calculation = SalaryCalculatorGross(allowances, transport_allowance, total_deductions, contributions_rate, pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate, rama_ee_rate, employee_type, pay_date)

                                # Calculate basic salary correctly: basic = gross - allowances - transport
                                result20 = calculation.calculate_basic_salary(gross_salary)
                                current_app.logger.info(f"Calculated basic_salary from gross_salary: {result20}")
                                # Calculate all
                                try:
                                    my_results = calculation.calculate_all(result20)
                                    # Unpack calculated results
                                    (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi, pension_ee_value, pension_er_value,
                                    maternity_ee_value, maternity_er_value, total_deductions_value, net_salary_value) = map(Auxillary.round_to_decimal, my_results)
                                    basic_needed = Auxillary.round_to_decimal(result20)
                                except Exception as e:
                                    current_app.logger.error(f"An error occurred while calculating all results for total_staff_cost: {e}")
                                    current_app.logger.error(f"Employee: {employee['first_name']} {employee['last_name']}, total_staff_cost: {total_staff_cost}")
                                    import traceback
                                    current_app.logger.error(traceback.format_exc())

                            except Exception as e:
                                current_app.logger.error(f"An error occurred while calculating the basic salary from gross_salary: {e}")
                                current_app.logger.error(f"Employee: {employee['first_name']} {employee['last_name']}, gross_salary: {gross_salary}")
                                import traceback
                                current_app.logger.error(traceback.format_exc())
                        except Exception as e:
                            current_app.logger.error(f"Error calculating gross salary from total_staff_cost: {str(e)}")
                            current_app.logger.error(f"Employee: {employee['first_name']} {employee['last_name']}, total_staff_cost: {total_staff_cost}")
                            import traceback
                            current_app.logger.error(traceback.format_exc())
                    # Check if the employee is a casual and set Rama to 0
                    if employee['employee_type'] == 'casual':
                        rama_ee = 0
                    # Check if employee is brd_sponsored and calculate the BRD deduction
                    if employee['is_brd_sponsored'] == 'yes':
                        #Get BRD Rate
                        rate = BrdDeductions.get_brd_deductions()
                        if rate is None:
                            brd_rate = Decimal('0.00')
                        else:
                            brd_rate = rate['deduction_rate']
                        brd_deduction = round(gross_needed * brd_rate)
                    else:
                        brd_deduction = Decimal('0.00')

                    # Round the values to 0 decimal places
                    try:
                        deductions = emp_deductions
                        total_deductions = Auxillary.round_to_decimal(total_deductions)
                        total_reimbursements = Auxillary.round_to_decimal(total_reimbursements)
                        basic_needed = Auxillary.round_to_decimal(basic_needed)
                    except Exception as e:
                        current_app.logger.error(f"An error occurred while processing employee data: {e}")
                    try:
                        # Store the employee data
                        employee_data = {
                            'employee': employee,
                            'deductions': emp_deductions,
                            'total_deductions': Auxillary.round_to_decimal(total_deductions),
                            'total_reimbursements': Auxillary.round_to_decimal(total_reimbursements),
                            'basic_needed': Auxillary.round_to_decimal(basic_needed),
                            'gross_needed': Auxillary.round_to_decimal(gross_needed),
                            'rama_ee': Auxillary.round_to_decimal(rama_ee),
                            'cbhi_value': Auxillary.round_to_decimal(cbhi_value),
                            'paye': Auxillary.round_to_decimal(paye),
                            'net_bcbhi': Auxillary.round_to_decimal(net_bcbhi),
                            'net_cbhi': Auxillary.round_to_decimal(net_cbhi),
                            'pension_ee_value': Auxillary.round_to_decimal(pension_ee_value),
                            'pension_er_value': Auxillary.round_to_decimal(pension_er_value),
                            'maternity_ee_value': Auxillary.round_to_decimal(maternity_ee_value),
                            'maternity_er_value': Auxillary.round_to_decimal(maternity_er_value),
                            'total_maternity': Auxillary.round_to_decimal(maternity_ee_value + maternity_er_value),
                            'total_deductions_value': Auxillary.round_to_decimal(total_deductions_value),
                            'net_salary_value': Auxillary.round_to_decimal(net_salary_value),
                            'employee_type': employee_type,
                            'total_pension': Auxillary.round_to_decimal(pension_ee_value + pension_er_value),
                            'total_rama': Auxillary.round_to_decimal(rama_ee + rama_ee),
                            'brd_deduction': Auxillary.round_to_decimal(brd_deduction),
                            'salary_advance': Auxillary.round_to_decimal(total_due),
                            'due_installments': due_installments
                        }

                        # Append employee data to the list
                        employees_with_deductions.append(employee_data)
                    except Exception as e:
                        current_app.logger.error(f"An error occurred while processing employee data: {e}")

                except Exception as e:
                    current_app.logger.error(f"An error occurred while processing employee data: {e}")

            # Store data in session for further use
            session['employees_with_deductions'] = employees_with_deductions
            session['database_name'] = database_name
            session['pay_date'] = pay_date
        try:
            message = "Payroll summary data generated successfully."
            flash(message, 'success')
            return jsonify({'success': True, 'message': message}), 200
        except Exception as e:
            logging.error(f"An error occurred while rendering the template: {e}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    try:
        # If GET request, display the form only
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            employees_with_deductions = []
            # Initialize all total variables to 0 when no deductions are present
            total_pension_ee = total_pension_er = total_maternity_ee = total_maternity_er = 0
            total_gross = total_nbcbhi = total_other_allowances = total_paye = total_net_cbhi = 0
            total_payroll_deductions = total_pension = total_maternity = total_rama = 0
            total_deductions = total_reimbursements = total_net_salary = total_cbhi_value = 0
            total_transport_allowance = total_basic_salary = total_net_salary_after_deductions = 0
            total_brd_deductions = 0
            total_advances = 0
        else:
            total_pension_ee = Auxillary.round_to_decimal(sum(e['pension_ee_value'] for e in employees_with_deductions))
            total_pension_er = Auxillary.round_to_decimal(sum(e['pension_er_value'] for e in employees_with_deductions))
            total_maternity_ee = Auxillary.round_to_decimal(sum(e['maternity_ee_value'] for e in employees_with_deductions))
            total_maternity_er = Auxillary.round_to_decimal(sum(e['maternity_er_value'] for e in employees_with_deductions))
            total_gross = Auxillary.round_to_decimal(sum(e['gross_needed'] for e in employees_with_deductions))
            total_nbcbhi = Auxillary.round_to_decimal(sum(e['net_bcbhi'] for e in employees_with_deductions))
            total_other_allowances = Auxillary.round_to_decimal(sum(e['employee']['allowances'] for e in employees_with_deductions))
            total_paye = Auxillary.round_to_decimal(sum(e['paye'] for e in employees_with_deductions))
            total_net_cbhi = Auxillary.round_to_decimal(sum(e['net_cbhi'] for e in employees_with_deductions))
            total_payroll_deductions = Auxillary.round_to_decimal(sum(e['total_deductions_value'] for e in employees_with_deductions))
            total_pension = Auxillary.round_to_decimal(sum(e['pension_ee_value'] + e['pension_er_value'] for e in employees_with_deductions))
            total_maternity = Auxillary.round_to_decimal(sum(e['maternity_ee_value'] + e['maternity_er_value'] for e in employees_with_deductions))
            total_rama = Auxillary.round_to_decimal(sum(e['rama_ee'] for e in employees_with_deductions))
            total_deductions = Auxillary.round_to_decimal(sum(e['total_deductions'] for e in employees_with_deductions))
            try:
                total_reimbursements = Auxillary.round_to_decimal(sum(e['total_reimbursements'] for e in employees_with_deductions))
            except Exception as p:
                logging.error(f"An error occurred while getting total reimbursements: {p}")
                total_reimbursements = 0
            total_net_salary = Auxillary.round_to_decimal(sum(e['net_salary_value'] for e in employees_with_deductions))
            total_cbhi_value = Auxillary.round_to_decimal(sum(e['cbhi_value'] for e in employees_with_deductions))
            total_transport_allowance = Auxillary.round_to_decimal(sum(e['employee']['transport_allowance'] for e in employees_with_deductions))
            total_basic_salary = Auxillary.round_to_decimal(sum(e['basic_needed'] for e in employees_with_deductions))
            # Calculate the sum before rounding
            total_before_rounding = sum(e['net_salary_value'] - e['total_deductions'] + e['total_reimbursements'] - e['brd_deduction'] - e['salary_advance'] for e in employees_with_deductions)
            current_app.logger.info(f"Total net salary after deductions before rounding: {total_before_rounding}")

            # Apply rounding
            total_net_salary_after_deductions = Auxillary.round_to_decimal(total_before_rounding)
            current_app.logger.info(f"Total net salary after deductions after rounding: {total_net_salary_after_deductions}")
            total_brd_deductions = Auxillary.round_to_decimal(sum(e['brd_deduction'] for e in employees_with_deductions))
            total_advances = Auxillary.round_to_decimal(sum(e['salary_advance'] for e in employees_with_deductions))
            # store the total net salary after deductions in the session
            session['total_net_salary_after_deductions'] = total_net_salary_after_deductions

        pay_date = session.get('pay_date')
        # Make sure the pay_date is not before 31/10/2023

        if not pay_date:
            pay_date = datetime.now()
        return render_template('payroll/payroll_summary.html', form=form,
                               employees_with_deductions=employees_with_deductions,
                               total_brd_deductions=total_brd_deductions,
                               Auxillary=Auxillary, pay_date=pay_date,
                               total_cbhi_value=total_cbhi_value, total_net_cbhi=total_net_cbhi,
                                total_pension_ee=total_pension_ee, total_pension_er=total_pension_er,
                               total_gross=total_gross, total_paye=total_paye,
                               total_payroll_deductions=total_payroll_deductions,
                               total_maternity_ee=total_maternity_ee, total_maternity_er=total_maternity_er,
                               total_pension=total_pension, total_maternity=total_maternity,
                               total_rama=total_rama, total_deductions=total_deductions,
                               total_reimbursements=total_reimbursements, total_net_salary=total_net_salary,
                               total_net_salary_after_deductions=total_net_salary_after_deductions,
                               total_nbcbhi=total_nbcbhi, total_other_allowances=total_other_allowances,
                               total_transport_allowance= total_transport_allowance,
                               total_basic_salary=total_basic_salary,
                               total_advances=total_advances,
                               attendance_service=attendance_service
                                 )

    except Exception as e:
        logging.error(f"An error occurred while rendering the template: {str(e)}")
        logging.error("".join(traceback.format_exc()))
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@payroll_summary_bp.route('/download_payroll_summary', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def download_payroll_summary():
    """Download payroll summary data as an Excel file with totals."""
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            current_app.logger.error("No payroll summary data available.")
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    excel_data = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        try:
            excel_data.append([
                idx, employee['first_name'], employee['last_name'], employee['job_title'],
                employee['employee_type'], employee_data['basic_needed'], employee['transport_allowance'],
                employee['allowances'], employee_data['gross_needed'], employee_data['paye'],
                employee_data['pension_ee_value'], employee_data['pension_er_value'],
                (Decimal(employee_data['pension_ee_value']) + Decimal(employee_data['pension_er_value'])),
                employee_data['maternity_ee_value'], employee_data['maternity_er_value'],
                (Decimal(employee_data['maternity_ee_value']) + Decimal(employee_data['maternity_er_value'])),
                employee_data['rama_ee'], employee_data['total_deductions_value'],
                employee_data['net_bcbhi'], employee_data['cbhi_value'], employee_data['net_cbhi'],
                employee_data['total_deductions'], employee_data['total_reimbursements'],
                employee_data['brd_deduction'],
                employee_data['net_salary_value'],
                # Calculate net salary after deductions with the same order as in payroll_summary.py
                (lambda net_val, deductions, reimb, brd, adv: (
                    # Log the calculation for debugging
                    current_app.logger.info(f"Employee {employee['first_name']} {employee['last_name']} download_payroll_summary calculation: net_salary_value={net_val}, total_deductions={deductions}, total_reimbursements={reimb}, brd_deduction={brd}, salary_advance={adv}"),
                    current_app.logger.info(f"Before rounding: {net_val - deductions + reimb - brd - adv}"),
                    # Return the calculated value
                    Auxillary.round_to_decimal(net_val - deductions + reimb - brd - adv)
                )[-1])(
                    Decimal(str(employee_data['net_salary_value'])),
                    Decimal(str(employee_data['total_deductions'])),
                    Decimal(str(employee_data['total_reimbursements'])),
                    Decimal(str(employee_data['brd_deduction'])),
                    Decimal(str(employee_data.get('salary_advance', 0)))
                )
            ])
        except Exception as e:
            current_app.logger.error(f"An error occurred while appending employee data: {e}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 400

    # Convert excel_data to DataFrame
    df = pd.DataFrame(excel_data, columns=[
        'Index', 'First Name', 'Last Name', 'Job Title', 'Employee Type', 'Basic Salary',
        'Transport Allowance', 'Allowances', 'Gross Salary', 'PAYE', 'Employee Pension',
        'Employer Pension', 'Total Pension', 'Employee Maternity', 'Employer Maternity',
        'Total Maternity', 'Employee RAMA', 'Total Deductions', 'Net Salary Before CBHI',
        'CBHI Value', 'Net Salary After CBHI', 'Total Deductions', 'Total Reimbursements',
        'BRD Deduction',
        'Net Salary', 'Net Salary After Deductions'
    ])

    try:
        # Create a total row with the correct number of columns
        total_values = df.iloc[:, 5:].apply(pd.to_numeric, errors='coerce').sum().tolist()
        total_row = ['TOTAL'] + [''] * 4 + total_values  # Ensure it matches df.columns
        df.loc[len(df)] = total_row
    except Exception as e:
        current_app.logger.error(f"An error occurred while appending the total row: {e}")
        return jsonify({'message': 'An error occurred. Please try again later'}), 500


    # Save the DataFrame to a BytesIO object
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Payroll Summary')

    output.seek(0)

    try:
        # Send the Excel file
        return send_file(output, as_attachment=True, download_name='Payroll_Summary.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        current_app.logger.error(f"An error occurred while sending the Excel file: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@payroll_summary_bp.route('/download_journal_entry', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def download_journal_entry():
    """Download journal entry data as an Excel file."""
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No journal entry data available.'}), 400
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting journal entry data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    # Dictionary to store total amounts per account
    account_totals = {}

    def add_to_account(account, dr=Decimal('0'), cr=Decimal('0')):
        if account not in account_totals:
            account_totals[account] = {'DR': Decimal('0'), 'CR': Decimal('0')}
        account_totals[account]['DR'] += Decimal(str(dr))
        account_totals[account]['CR'] += Decimal(str(cr))

    # Loop through employees and accumulate totals per account
    for employee_data in employees_with_deductions:
        # Debit Entries (Expenses)
        add_to_account('Gross salary', dr=Decimal(str(employee_data['gross_needed'])))
        add_to_account('Pension ER-Contr-8%', dr=Decimal(str(employee_data['pension_er_value'])))
        add_to_account('Maternity ER-Contr-0.3%', dr=Decimal(str(employee_data['maternity_er_value'])))
        add_to_account('Medical ER-Contr-7.5%', dr=Decimal(str(employee_data['rama_ee'])))
        add_to_account('Other staff expenses', dr=Decimal(str(employee_data['total_reimbursements'])))

        # Credit Entries (Liabilities)
        add_to_account('PAYE Payable', cr=Decimal(str(employee_data['paye'])))

        # Combine employer and employee pension into a single "Pension Payable"
        pension_ee_value = Decimal(str(employee_data['pension_ee_value']))
        pension_er_value = Decimal(str(employee_data['pension_er_value']))
        pension_payable = pension_ee_value + pension_er_value
        pension_payable = Auxillary.round_to_decimal(pension_payable)
        add_to_account('Pension Payable', cr=pension_payable)

        # Combine employer and employee maternity into a single "Maternity Payable"
        maternity_ee_value = Decimal(str(employee_data['maternity_ee_value']))
        maternity_er_value = Decimal(str(employee_data['maternity_er_value']))
        maternity_payable = maternity_ee_value + maternity_er_value
        maternity_payable = Auxillary.round_to_decimal(maternity_payable)
        add_to_account('Maternity Payable', cr=maternity_payable)

        # Calculate net salary payable with consistent rounding
        net_salary_value = Decimal(str(employee_data['net_salary_value']))
        total_deductions = Decimal(str(employee_data['total_deductions']))
        total_reimbursements = Decimal(str(employee_data['total_reimbursements']))
        brd_deduction = Decimal(str(employee_data['brd_deduction']))
        salary_advance = Decimal(str(employee_data['salary_advance']))

        # Log the values before calculation for debugging
        current_app.logger.info(f"Employee {employee_data['employee'].get('first_name', '')} {employee_data['employee'].get('last_name', '')}: net_salary_value={net_salary_value}, total_deductions={total_deductions}, total_reimbursements={total_reimbursements}, brd_deduction={brd_deduction}, salary_advance={salary_advance}")

        # Calculate with proper Decimal arithmetic - using the same order as in payroll_summary.py
        net_salary_payable = net_salary_value - total_deductions + total_reimbursements - brd_deduction - salary_advance
        current_app.logger.info(f"Before rounding: net_salary_payable={net_salary_payable}")

        # Round to whole number for consistency
        net_salary_payable = Auxillary.round_to_decimal(net_salary_payable)
        current_app.logger.info(f"After rounding: net_salary_payable={net_salary_payable}")
        add_to_account('Net Salary Payable', cr=net_salary_payable)
        add_to_account('CBHI Payable', cr=Decimal(str(employee_data['cbhi_value'])))
        add_to_account('RAMA (Medical) Payable', cr=Decimal(str(employee_data['total_rama'])))
        add_to_account('BRD Payable', cr=Decimal(str(employee_data['brd_deduction'])))
        add_to_account('Salary Advance', cr=Decimal(str(employee_data['salary_advance'])))
        add_to_account('Other Deductions', cr=Decimal(str(employee_data['total_deductions'])))

    # Convert dictionary to DataFrame
    try:
        df = pd.DataFrame([
            {'Account': account, 'DR': values['DR'] if values['DR']>0 else None, 'CR': values['CR'] if values['CR']>0 else None}
            for account, values in account_totals.items()
        ])
    except Exception as e:
        current_app.logger.error(f"An error occurred while creating the DataFrame: {e}")
        flash(f"An error occurred while creating the DataFrame: {e}", 'danger')
        return jsonify({'message': 'An error occurred. Please try again later.'}), 400

    # Append total row (also rounded)
    total_dr = Auxillary.round_to_decimal(df['DR'].sum())
    total_cr = Auxillary.round_to_decimal(df['CR'].sum())

    # Check if debits and credits balance
    if total_dr != total_cr:
        current_app.logger.warning(f"Journal entries do not balance: DR={total_dr}, CR={total_cr}, Difference={total_dr-total_cr}")
        # Adjust the Net Salary Payable to balance the journal entry
        net_salary_idx = df[df['Account'] == 'Net Salary Payable'].index
        if len(net_salary_idx) > 0:
            difference = total_dr - total_cr
            current_net_salary = df.loc[net_salary_idx[0], 'CR']
            df.loc[net_salary_idx[0], 'CR'] = current_net_salary + difference
            # Recalculate totals
            total_dr = Auxillary.round_to_decimal(df['DR'].sum())
            total_cr = Auxillary.round_to_decimal(df['CR'].sum())
            current_app.logger.info(f"Journal entries balanced by adjusting Net Salary Payable: New DR={total_dr}, CR={total_cr}")

    total_row = pd.DataFrame([{'Account': 'Total', 'DR': total_dr, 'CR': total_cr}])
    df = pd.concat([df, total_row], ignore_index=True)

    # We wanna make sure the name of the document contains the name of the company and the date
    company_id = session.get('company_id')
    if not company_id:
        logging.error("Company ID is missing in session")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 400
    # Get company by company_id
    company = Company.get_company_by_id(company_id)
    if company is None:
        return jsonify({'message': 'An error occurred. Please try again later.'}), 400

    company_name = company.get('company_name', 'Company')
    # If the company contains spaces, replace them with underscores
    company_name = company_name.replace(" ", "_")

    # get the pay_date from the session
    pay_date = session.get('pay_date')
    if not pay_date:
        logging.error("Pay date is missing in session")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 400
    # get the months, date and year from the pay_date
    month = pay_date.strftime("%B")
    year = pay_date.strftime("%Y")
    journal_period = f"{month} {year}"

    #document name
    document_name = f"{company_name}_Journal_Entry_{journal_period}.xlsx"
    # Save to Excel
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Journal Entry')

    output.seek(0)

    try:
        return send_file(output, as_attachment=True, download_name=document_name, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        current_app.logger.error(f"An error occurred while sending the Excel file: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500


"""Download payroll payments"""
@payroll_summary_bp.route('/download_payroll_payments', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def download_payroll_payments():
    """Download the payroll payments as an Excel file with company information."""

    # Retrieve the company_id from the session
    company_id = session.get('company_id')
    if not company_id:
        logging.error("Company ID is missing in session")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    # Get company by company_id
    company = Company.get_company_by_id(company_id)
    if company is None:
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    try:
        # Retrieve payroll data from session
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'success': False, 'message': 'No payroll data available.'}), 404
    except Exception as e:
        logging.error(f"Error retrieving payroll data: {e}")
        return jsonify({'success': False, 'message': 'An error occurred. Please try again later.'}), 500

    # Collect payroll data for each employee
    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data.get('employee', {})
        try:
            lines.append([
                idx,
                employee.get('first_name', ''),
                employee.get('last_name', ''),
                employee.get('nid', ''),
                employee_data['net_salary_value'],
                employee.get('bank_name', ''),
                employee.get('bank_account', '')
            ])
        except Exception as e:
            logging.error(f"Error processing employee data at index {idx}: {e}")
            return jsonify({'success': False, 'message': 'An error occurred while processing employee data.'}), 500

    # Create an Excel workbook and add payroll data along with company details
    output = BytesIO()
    try:
        workbook = Workbook()
        sheet = workbook.active
        sheet.title = "Payroll Payments"

        # Add company details
        sheet['B1'] = "Company Name:"
        sheet['C1'] = company.get('company_name')
        #sheet['B2'] = company.get('country'), company.get('province'), company.get('district'), company.get('sector')
        # get the path of the logo
        UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER')
        logo_path = os.path.join(UPLOAD_FOLDER, company.get('logo'))
        """Convert the logo to PNG format if it's not already in that format."""
        def convert_image(logo_path):
            with PILImage.open(logo_path) as img:
                new_path = logo_path.rsplit('.', 1)[0] + '.png'
                img.save(new_path, 'PNG')
            return new_path

        if not logo_path.lower().endswith('.png'):
            logo_path = convert_image(logo_path)

        # Insert company logo if available
        if logo_path:
            img = Image(logo_path)
            img.height, img.width = 100, 100  # Adjust as needed
            sheet.add_image(img, "F1")

        # Write payroll data headers starting from row 6
        headers = ['No', 'First Name', 'Last Name', 'NID', 'Net Salary', 'Bank Name', 'Bank Account']
        for col_num, header in enumerate(headers, start=1):
            sheet.cell(row=6, column=col_num, value=header)

        # Write employee data starting from row 7
        for row_num, line in enumerate(lines, start=7):
            for col_num, value in enumerate(line, start=1):
                sheet.cell(row=row_num, column=col_num, value=value)

        # Save the workbook to BytesIO
        workbook.save(output)
        output.seek(0)

        return send_file(
            output,
            as_attachment=True,
            download_name='PayrollPayments.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        current_app.logger.error(f"Error generating the Excel file: {e}")
        return jsonify({'success': False, 'message': 'An error occurred while generating the Excel file.'}), 500

@payroll_summary_bp.route('/add_payroll', methods=['POST', 'GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_payroll():
    company_id = session.get('company_id')
    if not company_id:
        logging.error("Company ID is missing in session")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    database_name = session.get('database_name')
    if database_name is None:
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    # Get pay date from session (set during payroll generation)
    pay_date = session.get('pay_date')
    if not pay_date:
        flash('Pay date information is missing. Please generate payroll first.', 'warning')
        return redirect(url_for('payroll_summary.payroll_summary'))

    # Get current user ID for tracking who created the payroll
    current_user_id = session.get('user_id')

    db_connection = DatabaseConnection()

    if request.method == 'POST':
        with db_connection.get_session(database_name) as db_session:
            employees_with_deductions = session.get('employees_with_deductions')
            if not employees_with_deductions:
                logging.error("No employees with deductions found")
                return jsonify({'message': 'No employees with deductions found.'}), 404

            for data in employees_with_deductions:
                try:
                    employee_id = data['employee']['employee_id']
                    employee_name = data['employee']['first_name'] + " " + data['employee']['last_name']
                    job_title = data['employee']['job_title']
                    basic_salary = data['basic_needed']
                    gross_salary = data['gross_needed']
                    rama_ee = data['rama_ee']
                    cbhi_value = data['cbhi_value']
                    paye = data['paye']
                    net_bcbhi = data['net_bcbhi']
                    net_cbhi = data['net_cbhi']
                    pension_ee_value = data['pension_ee_value']
                    pension_er_value = data['pension_er_value']
                    maternity_ee_value = data['maternity_ee_value']
                    maternity_er_value = data['maternity_er_value']
                    total_deductions_value = data['total_deductions_value'] if data['total_deductions_value'] else 0
                    net_salary_value = data['net_salary_value']
                    employee_type = data['employee_type']
                    transport_allowance = data['employee']['transport_allowance']
                    housing_allowance = data['employee']['housing_allowance']
                    try:
                        salary_advance = data['salary_advance'] if data['salary_advance'] else 0
                    except Exception as e:
                        current_app.logger.error(f"An error occurred while getting salary advance: {e}")
                        salary_advance = 0
                    reimbursements = data['total_reimbursements'] if data['total_reimbursements'] else 0
                    try:
                        bonus = data['employee']['bonus']
                    except Exception as e:
                        logging.error(f"An error occurred while getting bonus: {e}")
                        bonus = 0
                    total_deductions = data['total_deductions']
                    try:
                        # Calculate net pay with consistent rounding
                        net_salary_value = Decimal(str(data['net_salary_value']))
                        total_reimbursements = Decimal(str(data['total_reimbursements']))
                        total_deductions = Decimal(str(data['total_deductions']))
                        brd_deduction = Decimal(str(data['brd_deduction']))
                        salary_advance = Decimal(str(data['salary_advance']))

                        # Log the values before calculation for debugging
                        current_app.logger.info(f"Employee {data['employee'].get('first_name', '')} {data['employee'].get('last_name', '')} add_payroll calculation: net_salary_value={net_salary_value}, total_deductions={total_deductions}, total_reimbursements={total_reimbursements}, brd_deduction={brd_deduction}, salary_advance={salary_advance}")

                        # Calculate with proper Decimal arithmetic - using the same order as in payroll_summary.py
                        net_pay_calc = net_salary_value - total_deductions + total_reimbursements - brd_deduction - salary_advance
                        current_app.logger.info(f"Before rounding: net_pay_calc={net_pay_calc}")

                        # Round to whole number for consistency
                        net_pay = Auxillary.round_to_decimal(net_pay_calc)
                        current_app.logger.info(f"After rounding: net_pay={net_pay}")
                    except Exception as e:
                        current_app.logger.error(f"An error occurred while calculating net pay: {e}")
                        net_pay = 0

                    try:
                        brd_deductions = data['brd_deduction']
                    except Exception as e:
                        current_app.logger.error(f"An error occurred while getting BRD deductions: {e}")
                        brd_deductions = 0

                except Exception as e:
                    message = f"An error occurred while getting employee data: {e}"
                    logging.error(message)
                    flash("An error occurred while getting employee data", 'danger')
                    return redirect(url_for('payroll_summary.payroll_summary'))

                # get the payment months and year , number of days in the month from the pay_date
                payment_month = pay_date.month
                payment_year = pay_date.year

                # Retrieve employee payroll data for the given month and year
                try:
                    payroll_data = Payroll.get_employee_payroll_for_a_specific_month(db_session, employee_id, payment_month, payment_year)
                except Exception as e:
                    message = f"An error occurred while getting payroll data: {e}"
                    current_app.logger.error(message)
                    payroll_data = []

                # Check if the employee has already been paid for the given month
                if payroll_data:
                    message = f"Payroll data for {employee_name} already exists for the month of {pay_date.strftime('%B')}"
                    flash(f'Payroll data for {employee_name} already exists for the month of {pay_date.strftime("%B")}', 'danger')
                    continue
                try:
                    # Save payroll data to the database
                    payroll = Payroll.add_payroll(db_session, employee_id, employee_name, job_title, basic_salary, transport_allowance, housing_allowance, bonus, rama_ee, gross_salary, pension_er_value, pension_ee_value, maternity_er_value, maternity_ee_value, paye, cbhi_value, total_deductions_value, net_pay, total_deductions, pay_date, net_salary_value, reimbursements, salary_advance, brd_deductions)
                    for installment in data['due_installments']:
                        try:
                            installment_id = installment['installment_id']
                            planned_amount = installment['planned_amount']
                            # Update installment that has been paid
                            InstallmentPlan.update_installment_plan_payment(db_session, installment_id, pay_date, is_paid=True)
                        except Exception as e:
                            current_app.logger.error(f"An error occurred while updating installment status: {e}")
                            continue
                    message = f"Payroll data for {employee_name} saved successfully on {pay_date}"
                    flash(f'Payroll data for {employee_name} saved successfully on {pay_date}', 'success')
                except Exception as e:
                    flash("An error occurred while saving payroll data", 'danger')
                    logging.error(f"An error occurred while saving payroll data: {e}")
        flash(message, 'success')
        return redirect(url_for('payroll_summary.payroll_summary'))
    return render_template('payroll/add_payroll.html', form=form)

@payroll_summary_bp.route('/payroll_summary_history', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def payroll_summary_history():
    company_id = session.get('company_id')
    if not company_id:
        logging.error("Company ID is missing in session")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    database_name = session.get('database_name')
    if database_name is None:
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        try:
            # Only get payrolls that have been explicitly approved
            payroll_objects = (db_session.query(Payroll)
                       .filter(Payroll.status == 'Approved')
                       .all())

            if not payroll_objects:
                flash("No approved payroll records found.", "info")
                return render_template('payroll/payroll_summary_history.html', payrolls=[])

            # Convert Payroll objects to dictionaries to ensure methods are called
            payrolls = []
            for payroll in payroll_objects:
                # Calculate total_pension and total_maternity explicitly
                payroll_dict = {
                    "payroll_id": payroll.payroll_id,
                    "employee_id": payroll.employee_id,
                    "employee_name": payroll.employee_name,
                    "job_title": payroll.job_title,
                    "basic_salary": payroll.basic_salary,
                    "transport_allowance": payroll.transport_allowance,
                    "house_allowance": payroll.housing_allowance,
                    "gross_salary": payroll.gross_salary,
                    "employer_pension": payroll.employer_pension,
                    "employee_pension": payroll.employee_pension,
                    "total_pension": payroll.employer_pension + payroll.employee_pension,
                    "employer_maternity": payroll.employer_maternity,
                    "employee_maternity": payroll.employee_maternity,
                    "total_maternity": payroll.employer_maternity + payroll.employee_maternity,
                    "payee": payroll.payee,
                    "cbhi": payroll.cbhi,
                    "total_deductions": payroll.total_deductions,
                    "other_deductions": payroll.other_deductions,
                    "brd_deductions": payroll.brd_deductions,
                    "advance": payroll.advance,
                    "reimbursement": payroll.reimbursement,
                    "net_salary": payroll.net_salary,
                    "net_pay": payroll.net_pay,
                    "pay_date": payroll.pay_date,
                    "status": payroll.status
                }
                payrolls.append(payroll_dict)

            return render_template('payroll/payroll_summary_history.html', payrolls=payrolls)

        except Exception as e:
            current_app.logger.error(f"An error occurred while fetching approved payrolls: {str(e)}")
            flash("An error occurred while fetching payroll history.", "danger")
            return redirect(url_for('admin.dashboard'))

@payroll_summary_bp.route('/generate_payslip_from_history/<uuid:payroll_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def generate_payslip_from_history(payroll_id):
    """Generate a payslip PDF for a specific payroll record from history.

    Args:
        payroll_id (UUID): The unique identifier of the payroll record.

    Returns:
        A downloadable PDF file containing the payslip.
    """
    # Get important details from the session
    database_name = session.get('database_name')
    company_id = session.get('company_id')

    if not database_name or not company_id:
        flash("Session information is missing. Please log in again.", "danger")
        return redirect(url_for('admin.dashboard'))

    # Create a database connection
    db_connection = DatabaseConnection()

    try:
        with db_connection.get_session(database_name) as db_session:
            # Get the payroll record
            payroll = Payroll.get_payroll_by_id(db_session, payroll_id)

            if not payroll:
                flash("Payroll record not found.", "danger")
                return redirect(url_for('payroll_summary.payroll_summary_history'))

            # Get the employee data
            employee = db_session.query(Employee).filter(Employee.employee_id == payroll.employee_id).first()

            if not employee:
                flash("Employee not found.", "danger")
                return redirect(url_for('payroll_summary.payroll_summary_history'))

            # Get company data
            company_data = Company.get_company_by_id(company_id)
            current_app.logger.info(f"Company data type: {type(company_data)}")

            if not company_data:
                flash("Company data not found.", "danger")
                return redirect(url_for('payroll_summary.payroll_summary_history'))

            # Calculate the pay period dates
            pay_date = payroll.pay_date
            first_day = pay_date.replace(day=1)
            _, days_in_month = calendar.monthrange(pay_date.year, pay_date.month)
            last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

            # Format dates as DD/MM/YYYY
            formatted_first_day = first_day.strftime("%d/%m/%Y")
            formatted_last_day = last_day.strftime("%d/%m/%Y")

            # Create employee data dictionary for PDF generation
            employee_dict = employee.to_dict()
            current_app.logger.info(f"Employee data type: {type(employee_dict)}")

            employee_data = {
                'employee': employee_dict,
                'basic_needed': payroll.basic_salary,
                'gross_needed': payroll.gross_salary,
                'paye': payroll.payee,
                'pension_ee_value': payroll.employee_pension,
                'pension_er_value': payroll.employer_pension,
                'maternity_ee_value': payroll.employee_maternity,
                'maternity_er_value': payroll.employer_maternity,
                'rama_ee': payroll.medical_fee,
                'cbhi_value': payroll.cbhi,
                'total_deductions_value': payroll.total_deductions,
                'net_salary_value': payroll.net_salary,
                'total_reimbursements': payroll.reimbursement or 0,
                'total_deductions': payroll.other_deductions or 0,
                'brd_deduction': payroll.brd_deductions or 0,
                'salary_advance': payroll.advance or 0
            }

            current_app.logger.info(f"Employee data keys: {employee_data.keys()}")

            # Generate PDF
            prepared_by = session.get('username', 'HR System')

            # Check if company_data is already a dictionary or if it has a to_dict method
            if isinstance(company_data, dict):
                company_dict = company_data
            else:
                company_dict = company_data.to_dict()

            pdf_buffer = PDFGenerator.generate_payslip_pdf(
                employee_data,
                company_dict,
                pay_date,
                formatted_first_day,
                formatted_last_day,
                days_in_month,
                prepared_by
            )

            # Create a custom filename with employee name and pay date
            employee_first_name = employee.first_name.replace(' ', '_')
            employee_last_name = employee.last_name.replace(' ', '_')
            month_year = pay_date.strftime('%B_%Y')  # Format: January_2025

            # Create a sanitized filename
            custom_filename = f"Payslip_{employee_first_name}_{employee_last_name}_{month_year}.pdf"
            custom_filename = ''.join(c for c in custom_filename if c.isalnum() or c in ['_', '-', '.'])

            # Return the PDF as a downloadable file
            return send_file(
                pdf_buffer,
                as_attachment=True,
                download_name=custom_filename,
                mimetype='application/pdf'
            )

    except Exception as e:
        current_app.logger.error(f"An error occurred while generating payslip: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        flash("An error occurred while generating the payslip.", "danger")
        return redirect(url_for('payroll_summary.payroll_summary_history'))

@payroll_summary_bp.route('/payroll_summary_text', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def payroll_summary_text():
    """Generate a text file containing payroll summary data.
    for permanent employees only."""
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        print("Employees with deductions: ", employees_with_deductions)
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        # Make sure this concerns only permanent employees
        if employee['employee_type'] != 'permanent':
           continue
        try:
            pensionable = employee_data['gross_needed'] - employee['transport_allowance']
            print("pensionable: ", pensionable)
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            basic_salary = 0
        total_pension = employee_data['pension_ee_value'] + employee_data['pension_er_value']

        line = (
            f"{idx}|{employee['nsf']}|{employee['nid']}|{employee['last_name']}|"
            f"{employee['first_name']}|{employee['gender']}|O|N|{employee_data['basic_needed']}|"
            f"0|0|0|{employee['transport_allowance']}|0|{employee['allowances']}|0|0|0|"
            f"{employee_data['gross_needed']}|{employee_data['paye']}|"
            f"{employee_data['gross_needed'] - employee['transport_allowance']}|{employee_data['pension_ee_value']}|"
            f"{employee_data['pension_ee_value']}|{round(pensionable * 0.02)}|{total_pension}|"
            f"{employee_data['maternity_ee_value']}|{employee_data['maternity_er_value']}|"
            f"{employee_data['maternity_ee_value'] + employee_data['maternity_er_value']}|"
            f"{employee_data['rama_ee']}|{employee_data['rama_ee']}|{employee_data['rama_ee'] + employee_data['rama_ee']}|"
            f"{employee_data['cbhi_value']}|"
        )

        lines.append(line)

    text_output = "\n".join(lines)
    return Response(text_output, mimetype='text/plain', headers={'Content-Disposition': 'attachment;filename=UNIFIED PAYE Permanent Employees_Ver_5.1.txt'})

@payroll_summary_bp.route('/payroll_summary_excel', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def payroll_summary_excel():
    """Generate an Excel file containing payroll summary data.
    for permanent employees only."""
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    excel_data = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        if employee['employee_type'] != 'permanent':
            continue
        try:
            pensionable = employee_data['gross_needed'] - employee['transport_allowance']
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0
        total_pension = employee_data['pension_ee_value'] + employee_data['pension_er_value']

        excel_data.append([
            idx, employee['nsf'], employee['nid'], employee['last_name'], employee['first_name'],
            employee['gender'], 'O', 'N', employee_data['basic_needed'], 0, 0, 0,
            employee['transport_allowance'], 0, employee['allowances'], 0, 0, 0,
            employee_data['gross_needed'], employee_data['paye'], pensionable,
            employee_data['pension_ee_value'], employee_data['pension_ee_value'],
            round(pensionable * 0.02), total_pension,
            employee_data['maternity_ee_value'], employee_data['maternity_er_value'],
            employee_data['maternity_ee_value'] + employee_data['maternity_er_value'],
            employee_data['rama_ee'], employee_data['rama_ee'], employee_data['rama_ee'] + employee_data['rama_ee'],
            employee_data['cbhi_value']
        ])

    # Create a DataFrame for the Excel file
    df = pd.DataFrame(excel_data, columns=[
        'Index', "Employee RSSB No/No d'affiliation de l'employé",
        "Employee National ID/ No de la carte d'identité",
        "Employee Last Name/ Nom de l'employé", "Employee First Name/Prenom de l'employé",
        "Sex   M-Male F- Female", "Return Type/Type de declaration O-Original   R- Revised",
        "Is the Employer RAMA member ? If Yes put 'Y'or if No put 'N'",
        "Basic Salary/Salaire de Base", "Benefit in Kind Transport/ Indemnités de transport en nature",
        "Benefit in Kind House/ Indemnités de logement en nature",
        "Other Benefits in Kind/ Autre indemnités en nature",
        "Cash Allowance Transport/Indemnités de transport en numéraire",
        "Cash Allowance House/ Indemnités de logement en numéraire",
        "Other cash Allowance/ Autre Indemnités en numéraire",
        "Terminal Benefits-end contract/ Décomptes finals fin de contrant",
        "Retirement Benefits / Décomptes finals à retraite",
        "Other recognized medical deductions / Autre déductions medicales reconnues",
        "PAYE Taxable Base/Salaire Brut",
        "PAYE Due/TPR à payer","Pension Base/ Assiette des cotisations en Pension",
        "Employee 3% Pension/ Part personnel", "Employer 3%  Pension/ Part Patronale ",
        "Employer 2%  Occupational Hazards(OH)/ Part Patronale aux risques professionels",
        "Total PENSION & OH Contributions/ Cotisations Totales en Pension et au RP",
        "Employee 0.3% Maternity( (T-O)0.3%) )/Part Personnel des cotisations Maternité",
        "Employer 0.3% Maternity( (T-O)0.3%) )/Part Patronales des Cotisations Maternité",
        "Total Maternity leave  Contributions(0.6%)/Cotisations Totales Maternité",
         "Employee 7.5% RAMA/ Part Personnel RAMA", "Employer 7.5% RAMA/ Part Patronale RAMA",
         "Total RAMA (15%)/ Cotisations Totales en RAMA","Employee CBHI Subsidies (0.5%)/Subventions au regime des Mutuelles de Santé."
    ])

    # Save the DataFrame to a BytesIO object
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Payroll Summary')

    output.seek(0)

    # Send the Excel file
    return send_file(output, as_attachment=True, download_name='Unified_PAYE_PENSION_MEDICAL_MATERNITY_CBHI_Annexure_2.1b.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@payroll_summary_bp.route('/maternity_anneex_excel', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def maternity_anneex_excel():
    """Generate an Excel File for martenity contributions of of employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')

        if not employees_with_deductions:
            return redirect(url_for('payroll_summary.payroll_summary'))
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        flash(f"An error occurred while getting payroll summary data: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

    excel_data = []
    for  employee_data in employees_with_deductions:
        employee = employee_data['employee']

        # Make sure we calculate for employees who are either permanent, or casual
        # or second employee
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue

        try:
            pensionable = Decimal(employee_data['gross_needed']) - Decimal(employee['transport_allowance'])
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0

        # Find the number of days in the current month
        num_days = calendar.monthrange(datetime.now().year, datetime.now().month)[1]

        excel_data.append([
            employee['nsf'], employee['last_name'], employee['first_name'],
            pensionable ,num_days,'E', employee_data['maternity_ee_value']
        ])

    # Create a DataFrame for the Excel file
    df = pd.DataFrame(excel_data, columns=[
        'RSSB_Employee_Number', 'Family_Name_Of_Employee',
         'First_Name_Of_Employee', 'Amount_Remuner_Month1',
         'Number_Days_Month1','Status(N/E/L)', 'Total Contributions'
    ])

    # Save the DataFrame to a BytesIO object
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Maternity')

    output.seek(0)

    try:
        # Send the Excel file
        return send_file(output, as_attachment=True, download_name='Maternity_Annexure_2.0.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        logging.error(f"An error occurred while sending the Excel file: {e}")
        flash(f"An error occurred while sending the Excel file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

@payroll_summary_bp.route('/maternity_anneex_txt', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def maternity_anneex_txt():
    """Generate a text file containing maternity contributions of employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        # Make sure this concerns only permanent employees
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue
        try:
            pensionable = Decimal(employee_data['gross_needed']) - Decimal(employee['transport_allowance'])
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0

        # Find the number of days in the current month
        num_days = calendar.monthrange(datetime.now().year, datetime.now().month)[1]

        line = (
            f"{idx}|{employee['nsf']}|{employee['last_name'].upper()}|{employee['first_name'].upper()}|{pensionable}|{num_days}|E|{employee_data['maternity_ee_value']}"
        )

        lines.append(line)

    text_output = "\n".join(lines)
    return Response(text_output, mimetype='text/plain',
                    headers={'Content-Disposition': 'attachment;filename=Maternity_ver2.0.txt'}
                    )

@payroll_summary_bp.route('/pension_anneex_excel', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def pension_anneex_excel():
    """Generate an Excel File for pension contributions of of employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')

        if not employees_with_deductions:
            return redirect(url_for('payroll_summary.payroll_summary'))
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        flash(f"An error occurred while getting payroll summary data: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

    excel_data = []
    for  employee_data in employees_with_deductions:
        employee = employee_data['employee']

        # Make sure we calculate for employees who are either permanent, or casual
        # or second employee
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue

        try:
            pensionable = Decimal(employee_data['gross_needed']) - Decimal(employee['transport_allowance'])

        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0

        # Find the number of days in the current month
        num_days = calendar.monthrange(datetime.now().year, datetime.now().month)[1]

        excel_data.append([
            employee['nsf'], employee['last_name'], employee['first_name'],
            pensionable ,num_days,'E', employee_data['pension_ee_value']
        ])

    # Create a DataFrame for the Excel file
    df = pd.DataFrame(excel_data, columns=[
        'RSSB_Employee_Number', 'Family_Name_Of_Employee',
         'First_Name_Of_Employee', 'Amount_Remuner_Month1',
         'Number_Days_Month1','Status(N/E/L)', 'Total Contributions'
    ])

    # Save the DataFrame to a BytesIO object
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Pension')

    output.seek(0)

    try:
        # Send the Excel file
        return send_file(output, as_attachment=True, download_name='PensionMonthlyAnnexure_1.0.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        logging.error(f"An error occurred while sending the Excel file: {e}")
        flash(f"An error occurred while sending the Excel file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

@payroll_summary_bp.route('/pension_anneex_txt', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def pension_anneex_txt():
    """Generate a text file containing pension contributions of employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        # Make sure this concerns only permanent employees
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue
        try:
            pensionable = Decimal(employee_data['gross_needed']) - Decimal(employee['transport_allowance'])
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0

        # Find the number of days in the current month
        num_days = calendar.monthrange(datetime.now().year, datetime.now().month)[1]

        line = (
            f"{idx}|{employee['nsf']}|{employee['last_name'].upper()}|{employee['first_name'].upper()}|{pensionable}|{num_days}|E|{employee_data['pension_ee_value']}"
        )

        lines.append(line)

    text_output = "\n".join(lines)
    return Response(text_output, mimetype='text/plain',
                    headers={'Content-Disposition': 'attachment;filename=Pension_Monthly_ver2.0.txt'}
                    )

@payroll_summary_bp.route('/pension_anneex_ver3_excell', methods=['GET'])
#@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def pension_anneex_ver30():
    """Generate an Excel File for pension contributions of of employees.
    """
    role = session.get('role')
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return redirect(url_for('payroll_summary.payroll_summary'))
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        flash(f"An error occurred while getting payroll summary data: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

    excel_data = []
    for  employee_data in employees_with_deductions:
        employee = employee_data['employee']

        # Make sure we calculate for employees who are either permanent, or casual
        # or second employee
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue

        excel_data.append([
            employee['nsf'], employee['last_name'], employee['first_name'],
            employee_data['gross_needed'],employee['transport_allowance'],employee_data['pension_ee_value'],employee_data['pension_ee_value'],
            (Decimal(employee_data['pension_er_value']) - Decimal(employee_data['pension_ee_value'])),
            (Decimal(employee_data['pension_ee_value']) + Decimal(employee_data['pension_er_value']))
        ])
    # Create a DataFrame for the Excel file
    df = pd.DataFrame(excel_data, columns=[
        'RSSB_Employee_Number', 'Family_Name_Of_Employee','First_Name_Of_Employee',
         'Gross Salary','Transport (Cash/in kind/lumpsum)', 'Employee_Pension contribution (6%) D*6%',
         'Employer_Pension contribution (6%) D*6%','Employer_ Occupational Hazards contribution   (2%) (D-E)*2%',
         'Total Contributions (F+G+H)'
    ])

    # Save the DataFrame to a BytesIO object
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Pension')

    output.seek(0)

    try:
        # Send the Excel file
        return send_file(output, as_attachment=True, download_name='PensionMonthlyAnnexure_3.0.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        logging.error(f"An error occurred while sending the Excel file: {e}")
        flash(f"An error occurred while sending the Excel file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

@payroll_summary_bp.route('/pension_anneex_ver3.0_txt', methods=['GET'])
@hr_required
def pension_anneex_ver3():
    """Generate a text file containing pension contributions of employees."""
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        # Make sure this concerns only permanent employees
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue
        transport_allowance = Decimal(employee['transport_allowance'])


        line = (
            f"{idx}|{employee['nsf']}|{employee['last_name'].upper()}|{employee['first_name'].upper()}|{employee_data['gross_needed']}|"
            f"{transport_allowance}|{employee_data['pension_ee_value']}|{employee_data['pension_ee_value']}|"
            f"{Decimal(employee_data['pension_er_value']) + Decimal(employee_data['pension_ee_value'])}|"
            f"{Decimal(employee_data['pension_ee_value']) + Decimal(employee_data['pension_er_value'])}"
        )

        lines.append(line)

    text_output = "\n".join(lines)
    return Response(text_output, mimetype='text/plain',
                    headers={'Content-Disposition': 'attachment;filename=Pension_Monthly_ver3.0.txt'}
                    )

@payroll_summary_bp.route('/cbhi_anneex_excel', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def cbhi_anneex_excel():
    """Generate an Excel File for CBHI contributions of of employees.
    """
    print("Inside CBHI")
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        print("Employees with deductions retrieved")

        if not employees_with_deductions:
            return redirect(url_for('payroll_summary.payroll_summary'))
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        flash(f"An error occurred while getting payroll summary data: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

    excel_data = []
    print("excel data created")
    for  employee_data in employees_with_deductions:
        employee = employee_data['employee']

        # Assign Values based on employee type
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue
        if employee['employee_type'] == 'second employee':
            emp_type = 'S'
        elif employee['employee_type'] == 'casual':
            emp_type = 'C'
        elif employee['employee_type'] == 'permanent':
            emp_type = 'P'
        else:
            emp_type = 'E'

        # check if employee is a ram member
        if employee_data['rama_ee'] > 0:
            rama_member = 'Y'
        else:
            rama_member = 'N'

        try:
            excel_data.append([
                employee['nsf'], employee['nid'], employee['last_name'],
                employee['first_name'], emp_type, rama_member, employee_data['basic_needed'], 0, 0, 0,
                employee['transport_allowance'] , 0, employee['other_allowance'], 0,
                0, 0, employee_data['gross_needed'],employee_data['paye'],
                employee_data['cbhi_value']
            ])
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            print("An error occurred while getting basic salary: ", e)
            flash(f"An error occurred while getting basic salary: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))

    # conditionally display the pay_due column with a different name based on the pay_date
    paydate = session.get('pay_date')
    if paydate:
        # we want to check if the paydate startes in January, 1st, 2025
        # Paydate is a datetime object
        try:
            check = paydate >= datetime(2025, 1, 1).date()
        except Exception as e:
            current_app.logger.error(f"An error occurred while checking the paydate: {e}")
            check = False
        if check:
            pay_due_column = "PAYE Deductable/TPR à Considerer"
        else:
            pay_due_column = "PAYE Due/TPR à payer"
    else:
        pay_due_column = "PAYE Deductable/TPR à Considerer"


    # Create a DataFrame for the Excel file
    df = pd.DataFrame(excel_data, columns= [
        "Employee RSSB No/No d'affiliation de l'employé", "Employee National ID/ No de la carte d'identité",
        "Employee Last Name/ Nom de l'employé", "Employee First Name/Prenom de l'employé",
        "Employment status /Categorie de l'emploi (“P” for Permanent, “C” for Casual , “S” for employees with second employer and “E” for Expatriates Non-PAYE taxable)",
        "Is the Employer RAMA member ? If Yes put 'Y'or if No put 'N'",
        "Basic Salary/Salaire de Base", "Benefit in Kind Transport/ Indemnités de transport en nature",
        "Benefit in Kind House/ Indemnités de logement en nature",
        "Other Benefits in Kind/ Autre indemnités en nature",
        "Cash Allowance Transport/Indemnités de transport en numéraire",
        "Cash Allowance House/ Indemnités de logement en numéraire",
        "Other cash Allowance/ Autre Indemnités en numéraire",
        "Terminal Benefits-end contract/ Décomptes finals fin de contrant",
        "Retirement Benefits / Décomptes finals à retraite",
        "Other recognized medical deductions / Autre déductions medicales reconnues",
        "PAYE Taxable Base/Salaire Brut", pay_due_column,
        "Employee CBHI Subsidies (0.5%)/Subventions au regime des Mutuelles de Santé."
    ])

    # Save the DataFrame to a BytesIO object
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='CBHI')

    output.seek(0)

    try:
        # Send the Excel file
        return send_file(output, as_attachment=True, download_name='CBHI_Annexure_3.0_new.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        logging.error(f"An error occurred while sending the Excel file: {e}")
        flash(f"An error occurred while sending the Excel file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

@payroll_summary_bp.route('/cbhi_anneex_txt', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def cbhi_anneex_txt():
    """Generate a text file containing CBHI contributions of employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']

        # Assign Values based on employee type
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue
        if employee['employee_type'] == 'second employee':
            emp_type = 'S'
        elif employee['employee_type'] == 'casual':
            emp_type = 'C'
        elif employee['employee_type'] == 'permanent':
            emp_type = 'P'
        else:
            emp_type = 'E'

        rama_ee = employee_data['rama_ee']
        if rama_ee > 0:
            rama_member = 'Y'
        else:
            rama_member = 'N'

        # Make sure this concerns only permanent employees
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue

        line = (
            f"{idx}|{employee['nsf']}|{employee['nid']}|{employee['last_name'].upper()}|{employee['first_name'].upper()}|{emp_type}|{rama_member}|{employee_data['basic_needed']}|0|0|0|{employee['transport_allowance']}|0|{employee['allowances']}|0|0|0|{employee_data['gross_needed']}|{employee_data['paye']}|{employee_data['cbhi_value']}"
        )

        lines.append(line)

    pay_date = session.get('pay_date')
    # check if paydata is before 2025
    if pay_date:

        if pay_date >= datetime(2025, 1, 1).date() and pay_date <= datetime(2025,1,31).date():
            file_name = 'CBHI_Annexure_3.0_new.txt'
        elif pay_date >= datetime(2025, 2, 1).date():
            file_name = 'CBHI_ver4.0.txt'
        else:
            file_name = 'CBHI_Annexure_3.0_new.txt'

    text_output = "\n".join(lines)
    return Response(text_output, mimetype='text/plain',
                    headers={'Content-Disposition': 'attachment;filename='+file_name}
                    )

@payroll_summary_bp.route('/paye_anneex_excel', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def paye_anneex_excel():
    """Generate an Excel File for PAYE contributions of employees."""
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return redirect(url_for('payroll_summary.payroll_summary'))
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting payroll summary data: {e}")
        flash(f"An error occurred while getting payroll summary data: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

    permanent_data = []
    casual_data = []
    second_employee_data = []

    for employee_data in employees_with_deductions:
        employee = employee_data['employee']
        if employee['employee_type'] not in ['permanent', 'casual', 'second employee']:
            continue

        if employee['employee_type'] == 'second employee':
            emp_type = 'S'
            target_list = second_employee_data
        elif employee['employee_type'] == 'casual':
            emp_type = 'C'
            target_list = casual_data
        elif employee['employee_type'] == 'permanent':
            emp_type = 'P'
            target_list = permanent_data
        try:
            pensionable = Decimal(employee_data['gross_needed']) - Decimal(employee['transport_allowance'])
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0
        try:
            num_days = calendar.monthrange(datetime.now().year, datetime.now().month)[1]
            start_date = datetime.now().replace(day=1)
            date = start_date.strftime("%d/%m/%Y")
            end_date = datetime.now().replace(day=num_days).strftime("%d/%m/%Y")
            dob = employee['birth_date']
            # Convert the birth_date to a datetime object if it's a string
            if isinstance(dob, str):
                dob = datetime.strptime(dob, "%d/%m/%Y")
            elif isinstance(dob, datetime):
                dob = dob.strftime("%d/%m/%Y")
            else:
                dob = None
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting dates: {e}")
            flash(f"An error occurred while getting dates: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))

        try:
            if employee['employee_type'] == 'permanent':
                tax_brackets = TaxBracket.get_taxbrackets()
            elif employee['employee_type'] == 'casual':
                tax_brackets = CasualsTaxBracket.get_casuals_taxbrackets()
            elif employee['employee_type'] == 'second employee':
                tax_brackets = SecondEmployeeTaxBracket.get_second_employee_taxbrackets()
            # Find the first non-zero tax bracket
            zero_bracket = next((bracket for bracket in tax_brackets if bracket['rate'] == 0.0), None)
            non_taxable_amount = zero_bracket['upper_bound'] if zero_bracket else 0
            if employee_data['gross_needed'] <= 60000:
                taxable_amount = 0
            else:
                taxable_amount = Decimal(employee_data['gross_needed']) - Decimal(non_taxable_amount)
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting tax brackets: {e}")
            flash(f"An error occurred while getting tax brackets: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))

        if employee['gender'] == 'male':
            gender = 'M'
        else:
            gender = 'F'

        try:
            target_list.append([
                employee['employee_tin'], employee['nsf'], employee['nid'], employee['last_name'],
                employee['first_name'], 'B', gender, emp_type, date, end_date,
                dob, employee_data['basic_needed'], 0, 0, 0, employee['transport_allowance'],
                0, employee['allowances'], taxable_amount, employee_data['paye'], pensionable,
                employee_data['pension_ee_value'], employee_data['pension_er_value'],
                employee_data['total_pension'], employee_data['rama_ee'], employee_data['rama_ee'],
                employee_data['total_rama']
            ])
        except Exception as e:
            current_app.logger.error(f"An error occurred while adding data: {e}")
            flash(f"An error occurred while adding data: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))

    columns = [
        "Employee TIN or No/NIF de l'Employé ou N° d'Affiliation",
        "Employee NSSF No/N° d' Affiliation de l' Employe",
        "Employee National ID/C.I. de l'Employé", "Employee Last Name/Nom de l'Employé",
        "Employee First Name/Prenom de l'Employé",
        "Return Type/Type de Declaration B - Basic / Base C - Complimentary/ Complementaire",
        "Sex/Sexe M-Male/  Masculin F- Female/ Féminin",
        "Job Type/   Type d' Emploi P- Permanent/ Permanent",
        "Start Date (DD/MM/YYYY)/ Date de Début / (jj/mm/aaaa)",
        "End Date (DD/MM/YYYY)/ Date de Fin / (jj/mm/aaaa)",
        "Birth Date (DD/MM/YYYY)/ Date de Naissance / (jj/mm/aaaa)",
        "Basic Salary/Salaire de Base",
        "Benefit in Kind Transport/ Indemnités de transport en nature",
        "Benefit in Kind House/ Indemnités de logement en nature",
        "Benefit in Kind Others/ Autre indemnités en nature",
        "Cash Allowance Transport/Indemnités de transport en numéraire",
        "Cash Allowance House/ Indemnités de logement en numéraire",
        "Cash Allowance Others/ Autre Indemnités en numéraire",
        "PAYE Taxable Base/Salaire Brut",
        "PAYE Due/TPR à payer",
        "RSSB Base/Assiètte de Cotisation Caisse Sociale",
        "Employee3% NSSF/ Contribution de l' Emplyé 3%",
        "Employer 5% NSSF/ Contribution de l' Employeur 5%",
        "Total RSSB/ Total Contribution Caisse Sociale",
        "Employee 7.5% RAMA/Contribution RAMA 7.5%",
        "Employer 7.5% RAMA/ Contribution RAMA 7.5%",
        "Total RAMA (15%)/Total RAMA 15%"
    ]
    try:
        permanent_df = pd.DataFrame(permanent_data, columns=columns)
        casual_df = pd.DataFrame(casual_data, columns=columns)
        second_employee_df = pd.DataFrame(second_employee_data, columns=columns)
    except Exception as e:
        current_app.logger.error(f"Error creating data: {str(e)}")

    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        permanent_df.to_excel(writer, index=False, sheet_name='PAYE PERMENENT EMPLOYEES')
        casual_df.to_excel(writer, index=False, sheet_name='PAYE CASUAL EMPLOYEES')
        second_employee_df.to_excel(writer, index=False, sheet_name='PAYE SECOND EMPLOYER')

    output.seek(0)

    try:
        return send_file(output, as_attachment=True, download_name='PayeAnnexure.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        current_app.logger.error(f"An error occurred while sending the Excel file: {e}")
        flash(f"An error occurred while sending the Excel file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))


@payroll_summary_bp.route('/paye_permanent_anneex_txt', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def paye_permanent_anneex_txt():
    """Generate a text file containing PAYE contributions of permanent employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        if employee['employee_type'] != 'permanent':
            continue
        try:
            pensionable = Decimal(employee_data['gross_needed']) - Decimal(employee['transport_allowance'])
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0
        pay_date = session.get('pay_date')
        num_days = calendar.monthrange(pay_date.year, pay_date.month)[1]
        start_date = pay_date.replace(day=1)
        date = start_date.strftime("%d/%m/%Y")
        end_date = pay_date.replace(day=num_days).strftime("%d/%m/%Y")
        dob = employee['birth_date']
        # Convert the birth_date to a datetime object if it's a string
        if isinstance(dob, str):
            dob = datetime.strptime(dob, "%d/%m/%Y")
        elif isinstance(dob, datetime):
            dob = dob.strftime("%d/%m/%Y")
        else:
            dob = "1/01/2000"

        emp_type = 'P'
        if employee['gender'] == 'male':
            gender = 'M'
        else:
            gender = 'F'

        try:
            tax_brackets = TaxBracket.get_taxbrackets()
            # Find the first non-zero tax bracket
            zero_bracket = next((bracket for bracket in tax_brackets if bracket['rate'] == 0.0), None)
            non_taxable_amount = zero_bracket['upper_bound'] if zero_bracket else 0
            taxable_amount = Decimal(employee_data['gross_needed']) - Decimal(non_taxable_amount)
        except Exception as e:
            logging.error(f"An error occurred while getting tax brackets: {e}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500

        line = (
            f"{idx}|{employee['employee_tin']}|{employee['nsf']}|{employee['nid']}|"
            f"{employee['last_name'].upper()}|{employee['first_name'].upper()}|B|{gender}|{emp_type}|"
            f"{date}|{end_date}|{dob}|{employee_data['basic_needed']}|0|0|0|"
            f"{employee['transport_allowance']}|0|{employee['allowances']}|"
            f"{taxable_amount}|{employee_data['paye']}|{pensionable}|"
            f"{employee_data['pension_ee_value']}|{employee_data['pension_er_value']}|"
            f"{employee_data['total_pension']}|{employee_data['rama_ee']}|"
            f"{employee_data['rama_ee']}|{employee_data['total_rama']}"
        )
        lines.append(line)


    text_output = "\n".join(lines)
    try:
        return Response(text_output, mimetype='text/plain',
                        headers={'Content-Disposition': 'attachment;filename=PAYE Permanent Employees_Ver_1.1.txt'}
                        )
    except Exception as e:
        logging.error(f"An error occurred while sending the text file: {e}")
        flash(f"An error occurred while sending the text file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

@payroll_summary_bp.route('/paye_casual_anneex_txt', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def paye_casual_anneex_txt():
    """Generate a text file containing PAYE contributions of casual employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        if employee['employee_type'] != 'casual':
            continue
        try:
            pensionable = employee_data['gross_needed'] - employee['transport_allowance']
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0

        occupational_hazard = pensionable * 0.02
        # Round off the occupational hazard to the nearest whole number
        occupational_hazard = round(occupational_hazard)
        total_maternity = employee_data['maternity_ee_value'] + employee_data['maternity_er_value']
        total_pension = employee_data['pension_ee_value'] + employee_data['pension_er_value']
        try:
            pay_date = session.get('pay_date')
            num_days = calendar.monthrange(pay_date.year, pay_date.month)[1]
            start_date = pay_date.replace(day=1)
            date = start_date.strftime("%d/%m/%Y")
            end_date = pay_date.replace(day=num_days).strftime("%d/%m/%Y")
            dob = employee['birth_date']
            # Convert the birth_date to a datetime object if it's a string
            if isinstance(dob, str):
                try:
                    # First, try parsing as YYYY-MM-DD (common format)
                    dob = datetime.strptime(dob, "%Y-%m-%d")
                except ValueError:
                    try:
                        # If that fails, try parsing as D/M/YYYY
                        dob = datetime.strptime(dob, "%d/%m/%Y")
                    except ValueError:
                        current_app.logger.error(f"Unknown date format: {dob}")
                        dob = datetime(2000, 1, 1)  # Default fallback date

                dob = dob.strftime("%d/%m/%Y")  # Convert to required format
            elif dob == None:
                dob = "1/01/2000"
            else:
                dob = dob.strftime("%d/%m/%Y")
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting dates: {e}")
            flash(f"An error occurred while getting dates: {e}", 'danger')
            return redirect(url_for('admin_data.hr_dashboard'))

        emp_type = 'C'
        if employee['gender'] == 'male':
            gender = 'M'
        else:
            gender = 'F'
        try:
            tax_brackets = CasualsTaxBracket.get_casuals_taxbrackets()
            # Find the first non-zero tax bracket
            zero_bracket = next((bracket for bracket in tax_brackets if bracket['rate'] == 0.0), None)
            non_taxable_amount = zero_bracket['upper_bound'] if zero_bracket else 0
            print("Non taxable amount: ", non_taxable_amount)
            taxable_amount = Decimal(employee_data['gross_needed']) - Decimal(non_taxable_amount)
        except Exception as e:
            logging.error(f"An error occurred while getting tax brackets: {e}")
            flash(f"An error occurred while getting tax brackets: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))
        try:
            print("Taxable amount: ", taxable_amount)

            line = (
                f"{idx}|{employee['employee_tin']}|{employee['nsf']}|{employee['nid']}|"
                f"{employee['last_name'].upper()}|{employee['first_name'].upper()}|B|{gender}|{emp_type}|"
                f"{date}|{end_date}|{dob}|{employee_data['gross_needed']}|{round(taxable_amount)}|{employee_data['paye']}|"
                f"{round(pensionable)}|{employee_data['pension_ee_value']}|"
                f"{(Decimal(occupational_hazard) + Decimal(employee_data['pension_er_value']))}|{total_pension}"
            )
            lines.append(line)
        except Exception as e:
            current_app.logger.error(f"An error occurred while adding data: {e}")
            flash(f"An error occurred while adding data: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))

    text_output = "\n".join(lines)
    try:
        return Response(text_output, mimetype='text/plain',
                        headers={'Content-Disposition': 'attachment;filename=PAYE Casual Employees_Ver_1.1.txt'}
                        )
    except Exception as e:
        logging.error(f"An error occurred while sending the text file: {e}")
        flash(f"An error occurred while sending the text file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))


@payroll_summary_bp.route('/paye_second_employee_anneex_txt', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def paye_second_employee_anneex_txt():
    """Generate a text file containing PAYE contributions of second employees.
    """
    try:
        employees_with_deductions = session.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify({'message': 'No payroll summary data available.'}), 404
    except Exception as e:
        logging.error(f"An error occurred while getting payroll summary data: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    lines = []
    for idx, employee_data in enumerate(employees_with_deductions, start=1):
        employee = employee_data['employee']
        if employee['employee_type'] != 'second employee':
            continue
        try:
            pensionable = employee_data['gross_needed'] - employee['transport_allowance']
        except Exception as e:
            logging.error(f"An error occurred while getting basic salary: {e}")
            pensionable = 0

        num_days = calendar.monthrange(datetime.now().year, datetime.now().month)[1]
        start_date = datetime.now().replace(day=1)
        date = start_date.strftime("%d/%m/%Y")
        end_date = datetime.now().replace(day=num_days).strftime("%d/%m/%Y")
        dob = employee['birth_date']
        # Convert the birth_date to a datetime object if it's a string
        if isinstance(dob, str):
            dob = datetime.strptime(dob, "%d/%m/%Y")
        elif dob == None:
            dob = "1/01/2000"
        else:
            dob = dob.strftime("%d/%m/%Y")
        emp_type = 'S'
        if employee['gender'] == 'male':
            gender = 'M'
        else:
            gender = 'F'

        try:
            tax_brackets = SecondEmployeeTaxBracket.get_second_employee_taxbrackets()
            # Find the first non-zero tax bracket
            zero_bracket = next((bracket for bracket in tax_brackets if bracket['rate'] == 0.0), None)
            non_taxable_amount = zero_bracket['upper_bound'] if zero_bracket else 0
            taxable_amount = employee_data['gross_needed'] - non_taxable_amount
        except Exception as e:
            logging.error(f"An error occurred while getting tax brackets: {e}")
            flash(f"An error occurred while getting tax brackets: {e}", 'danger')
            return redirect(url_for('payroll_summary.payroll_summary'))

        line = (
            f"{idx}|{employee['employee_tin']}|{employee['nsf']}|{employee['nid']}|"
            f"{employee['last_name'].upper()}|{employee['first_name'].upper()}|B|{gender}|{emp_type}|"
            f"{date}|{end_date}|{dob}|{employee_data['basic_needed']}|0|0|0|"
            f"{employee['transport_allowance']}|0|{employee['allowances']}|"
            f"{taxable_amount}|{employee_data['paye']}|{pensionable}|"
            f"{employee_data['pension_ee_value']}|{employee_data['pension_er_value']}|"
            f"{employee_data['total_pension']}|{employee_data['rama_ee']}|"
            f"{employee_data['rama_ee']}|{employee_data['total_rama']}"
        )
        lines.append(line)

    text_output = "\n".join(lines)
    try:
        return Response(text_output, mimetype='text/plain',
                        headers={'Content-Disposition': 'attachment;filename=PAYE Second Employees_Ver_1.1.txt'}
                        )
    except Exception as e:
        logging.error(f"An error occurred while sending the text file: {e}")
        flash(f"An error occurred while sending the text file: {e}", 'danger')
        return redirect(url_for('payroll_summary.payroll_summary'))

@payroll_summary_bp.route('/ishema', methods=['GET'])
def ishema():
    """Generate an Excel File for RSSB ishema format."""
    employees_with_deductions = session.get('employees_with_deductions')

    if not employees_with_deductions:
        return redirect(url_for('payroll_summary.payroll_summary'))

    excel_data = []

    for employee_data in employees_with_deductions:
        employee = employee_data['employee']
        employee_last_name = employee.get('last_name', '')
        employee_first_name = employee.get('first_name', '')
        employee_nsf = employee.get('nsf', '')
        employee_nid = employee.get('nid', '')
        employee_type = employee.get('employee_type', '').lower()

        category = {
            'permanent': 'P',
            'casual': 'C',
            'second employee': 'S'
        }.get(employee_type, 'E')

        other_allowance = employee.get('other_allowance', 0)
        house_allowance = employee.get('housing_allowance', 0)
        transport_allowance = employee.get('transport_allowance', 0)

        house_benefit_in_kind = 0
        transport_benefit_in_kind = 0
        other_benefit_in_kind = 0
        lump_sum_transport = 0
        other_medical_deductions = 0
        terminal_benefit_end_contract = 0
        retirement_benefit = 0
        ejoheza = 0
        other_pension_funds = 0

        basic_salary = employee_data.get('basic_needed', 0)
        rama_ee = employee_data.get('rama_ee', 0)
        rama_member = 'N' if rama_ee == 0 else 'Y'

        # Append employee data
        excel_data.append([
            employee_last_name, employee_first_name, employee_nsf, employee_nid,
            category, rama_member, basic_salary, transport_benefit_in_kind, house_benefit_in_kind,
            other_benefit_in_kind, transport_allowance, house_allowance, other_allowance,
            lump_sum_transport, other_medical_deductions, terminal_benefit_end_contract,
            retirement_benefit, ejoheza, other_pension_funds
        ])

    column_headers = [
        'FAMILY NAME', 'OTHER NAME', 'RSSB NUMBER', 'NID OR PASSPORT',
        'EMPLOYEE CATEGORY P = PERMANENT,C = CASUAL, E = EXEMPTED, S = SECOND EMPLOYER',
        'IS EMPLOYEE A RAMA MEMBER ?Y = YES, N = NO', 'BASIC SALARY',
        'TRANSPORT BENEFIT IN KIND', 'HOUSE BENEFIT IN KIND', 'OTHER BENEFIT IN KIND',
        'TRANSPORT CASH ALLOWANCES', 'HOUSE CASH ALLOWANCES', 'OTHER CASH ALLOWANCES',
        'LUMPSUM TRANSPORT', 'OTHER MEDICAL DEDUCTIONS', 'TERMINAL BENEFIT END CONTRACT',
        'RETIREMENT BENEFITS', 'EJO-HEZA CONTRIBUTION', 'OTHER PENSION FUNDS'
    ]

    df = pd.DataFrame(excel_data, columns=column_headers)

    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        workbook = writer.book
        worksheet = workbook.create_sheet(title="Ishema")

        # Write custom headers
        worksheet.append(["EMPLOYEES' SALARY BREAKDOWN"])
        worksheet.append(["IMISHAHARA Y'ABAKOZI | ISHEMA PLATFORM"])
        worksheet.append([])  # Blank row for spacing
        worksheet.append(column_headers)  # Column names

        # Append employee data
        for row in df.itertuples(index=False, name=None):
            worksheet.append(row)

        writer._save()  # Save the Excel file

    output.seek(0)

    try:
        return send_file(output, as_attachment=True, download_name='Payroll-February-2025-golden-tree-mining-limited-119790791.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        current_app.logger.error(f"An error occurred while sending the Excel file: {e}")
        return redirect(url_for('payroll_summary.payroll_summary'))
