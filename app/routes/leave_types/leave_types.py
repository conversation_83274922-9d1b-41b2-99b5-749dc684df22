from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from app.models.central_leave_types import LeaveTypes
from app.routes.leave_types.forms import LeaveTypeForm
from app.decorators.role_decorator import role_required
from app.decorators.admin_decorator import admin_required

leave_types = Blueprint('leave_types', __name__)

@leave_types.route('/add_leave_type', methods=['GET', 'POST'])
@admin_required
def add_leave_type():
    form = LeaveTypeForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Invalid data provided', 'danger')
            return redirect(url_for('leave_types.add_leave_type'))
        name = form.name.data
        description = form.description.data

        try:
            LeaveTypes.add_leave_type(name, description)
            flash('Leave Type added successfully', 'success')
            return redirect(url_for('leave_types.add_leave_type'))
        except Exception as e:
            flash('An error occurred while adding Leave Type', 'danger')
            current_app.logger.error(f'An error occurred while adding Leave Type: {str(e)}')
            return redirect(url_for('leave_types.add_leave_type'))
        
    # Retrieve leave types
    try:
        leave_types = LeaveTypes.get_leave_types()
        current_app.logger.info('Retrieved leave types successfully')
    except Exception as e:
        flash('An error occurred while retrieving leave types', 'danger')
        current_app.logger.error(f'An error occurred while retrieving leave types: {str(e)}')
        leave_types = []
    return render_template('leave_types/add_leave_type.html', form=form, leave_types=leave_types)

@leave_types.route('/edit_leave_type/<id>', methods=['GET', 'POST'])
@role_required(['admin'])
def edit_leave_type(id):
    form = LeaveTypeForm()
    leave_type = LeaveTypes.get_leave_type_by_id(id)
    
    if not leave_type:
        flash('Leave Type not found', 'danger')
        return redirect(url_for('leave_types.add_leave_type'))
    
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Invalid data provided', 'danger')
            return redirect(url_for('leave_types.edit_leave_type'))
        name = form.name.data
        description = form.description.data

        try:
            result = LeaveTypes.update_leave_type(id, name, description)
            current_app.logger.info(f"Leave Type updated successfully: {result}")
            if result:
                flash('Leave Type updated successfully', 'success')
                return redirect(url_for('leave_types.add_leave_type'))
            flash('An error occurred while updating Leave Type', 'danger')
        except Exception as e:
            flash('An error occurred while updating Leave Type', 'danger')
            current_app.logger.error(f'An error occurred while updating Leave Type: {str(e)}')
            return redirect(url_for('leave_types.edit_leave_type', id=id))
    
    form.name.data = leave_type['name']
    form.description.data = leave_type['description']
    return render_template('leave_types/edit_leave_type.html', form=form, leave_type=leave_type)

@leave_types.route('/delete_leave_type/<id>', methods=['POST', 'GET'])
@admin_required
def delete_leave_type(id):
    try:
        result = LeaveTypes.delete_leave_type(id)
        current_app.logger.info(f"Leave Type deleted successfully: {result}")
        if result:
            flash('Leave Type deleted successfully', 'success')
            return redirect(url_for('leave_types.add_leave_type'))
        flash('An error occurred while deleting Leave Type', 'danger')
    except Exception as e:
        flash('An error occurred while deleting Leave Type', 'danger')
        current_app.logger.error(f'An error occurred while deleting Leave Type: {str(e)}')
        return redirect(url_for('leave_types.add_leave_type'))
    
    return redirect(url_for('leave_types.add_leave_type'))