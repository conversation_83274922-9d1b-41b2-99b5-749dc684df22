from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SubmitField, IntegerField, TimeField, FloatField
from wtforms.validators import DataRequired, Length, NumberRange

class ShiftForm(FlaskForm):
    """Shift form class."""
    name = StringField('Shift Name', validators=[DataRequired(), Length(min=3, max=128)])
    # Using TimeField to capture time in HH:MM format
    start_time = TimeField('Start Time', validators=[DataRequired()])
    end_time = TimeField('End Time', validators=[DataRequired()])
    auto_clock_out_hours = FloatField('Auto Clock Out Hours', validators=[DataRequired()])
    submit = SubmitField('Submit')