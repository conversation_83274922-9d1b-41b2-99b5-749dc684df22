from flask import flash, render_template, redirect, url_for, Blueprint, request, session
from app.models.company_shifts import Shift
from app.decorators.role_decorator import role_required
from app.routes.shifts.forms import ShiftForm
from app.utils.db_connection import DatabaseConnection
from flask import current_app as app
from app.models.company import Attendance

shifts_v2 = Blueprint('shifts_v2', __name__)

@shifts_v2.route('/v2/create_shift', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager'])
def create_shift():
    """Create a shift."""
    form = ShiftForm()

    #Get database name
    database_name = session.get('database_name')
    if request.method == 'POST':
        data = request.form
        app.logger.info(f"Data: {data}")
        if not form.validate_on_submit():
            # Get the form errors
            message = f"Invalid data: {form.errors}"
            flash(message, 'danger')
            return redirect(url_for('shifts_v2.create_shift'))
        # Create the shift
        name = form.name.data
        start_time = form.start_time.data
        end_time = form.end_time.data
        auto_clock_out_hours = form.auto_clock_out_hours.data
        app.logger.info(f"Name: {name}, Start Time: {start_time}, End Time: {end_time}, Auto Clock Out Hours: {auto_clock_out_hours}")

        # Create a new database connection
        db_connection = DatabaseConnection()

        with db_connection.get_session(database_name) as db_session:
            try:
                app.logger.info("Creating shift")
                message, category = Shift.create_shift(db_session, name, start_time, end_time, auto_clock_out_hours)
                flash(message, category)
                app.logger.info(message)
                return redirect(url_for('shifts_v2.view_shifts'))
            except Exception as e:
                message = f"An error occurred while creating the shift: {str(e)}"
                flash(message, 'danger')
                app.logger.error(message)
                return redirect(url_for('shifts_v2.create_shift'))
    try:
        app.logger.info("Rendering create_shift.html")
        return render_template('shifts/create_shift_v2.html', form=form)
    except Exception as e:
        message = f"An error occurred while rendering create_shift.html: {str(e)}"
        flash(message, 'danger')
        app.logger.error(message)
        return redirect(url_for('admin_data.dashboard'))

@shifts_v2.route('/v2/view_shifts', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def view_shifts():
    """View all shifts."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            app.logger.info("Getting all shifts")
            shifts = Shift.get_shifts(db_session)
            app.logger.info(f"Shifts: {shifts}")
            return render_template('shifts/view_shifts_v2.html', shifts=shifts)
        except Exception as e:
            message = f"An error occurred while getting all shifts: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('admin_data.dashboard'))

@shifts_v2.route('/v2/delete_shift/<shift_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def delete_shift(shift_id):
    """Delete a shift."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            app.logger.info(f"Deleting shift with ID: {shift_id}")
            message = Shift.delete_shift(db_session, shift_id)
            flash(message, 'success')
            app.logger.info(message)
            return redirect(url_for('shifts_v2.view_shifts'))
        except Exception as e:
            message = f"An error occurred while deleting the shift: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('shifts_v2.view_shifts'))

@shifts_v2.route('/v2/auto_clockout', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def auto_clockout():
    """Auto clock out employees."""
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            app.logger.info("Auto clocking out employees")
            message = Attendance.auto_clockout(db_session)
            flash(message, 'success')
            app.logger.info(message)
            return redirect(url_for('shifts_v2.view_shifts'))
        except Exception as e:
            message = f"An error occurred while auto clocking out employees: {str(e)}"
            flash(message, 'danger')
            app.logger.error(message)
            return redirect(url_for('shifts_v2.view_shifts'))

@shifts_v2.route('/v2/update_shift/<shift_id>', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager'])
def update_shift(shift_id):
    """Update a shift."""
    form = ShiftForm()
    # Get database name
    database_name = session.get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    if request.method == 'GET':
        with db_connection.get_session(database_name) as db_session:
            try:
                app.logger.info(f"Getting shift with ID: {shift_id}")
                shift = Shift.get_shift_by_id(db_session, shift_id)
                app.logger.info(f"Shift: {shift}")
                form.name.data = shift['name']
                form.start_time.data = shift['start_time']
                form.end_time.data = shift['end_time']
                form.auto_clock_out_hours.data = shift['auto_clock_out_hours']
            except Exception as e:
                message = f"An error occurred while getting the shift: {str(e)}"
                flash(message, 'danger')
                app.logger.error(message)
                return redirect(url_for('shifts_v2.view_shifts'))
    elif request.method == 'POST':
        data = request.form
        app.logger.info(f"Data: {data}")
        if not form.validate_on_submit():
            # Get the form errors
            message = f"Invalid data: {form.errors}"
            flash(message, 'danger')
            return redirect(url_for('shifts_v2.update_shift', shift_id=shift_id))
        # Update the shift
        name = form.name.data
        start_time = form.start_time.data
        end_time = form.end_time.data
        auto_clock_out_hours = form.auto_clock_out_hours.data
        app.logger.info(f"Name: {name}, Start Time: {start_time}, End Time: {end_time}, Auto Clock Out Hours: {auto_clock_out_hours}")

        with db_connection.get_session(database_name) as db_session:
            try:
                app.logger.info("Updating shift")
                message = Shift.update_shift(db_session, shift_id, name, start_time, end_time, auto_clock_out_hours)
                if isinstance(message, tuple):
                    flash(message[0], message[1])
                else:
                    flash(message, 'success')
                app.logger.info(message)
                return redirect(url_for('shifts_v2.view_shifts'))
            except Exception as e:
                message = f"An error occurred while updating the shift: {str(e)}"
                flash(message, 'danger')
                app.logger.error(message)
                return redirect(url_for('shifts_v2.view_shifts'))

    try:
        app.logger.info("Rendering update_shift.html")
        return render_template('shifts/update_shift_v2.html', form=form, shift_id=shift_id)
    except Exception as e:
        message = f"An error occurred while rendering update_shift.html: {str(e)}"
        flash(message, 'danger')
        app.logger.error(message)
        return redirect(url_for('admin_data.dashboard'))
