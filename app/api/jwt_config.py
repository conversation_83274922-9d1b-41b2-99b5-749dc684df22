import json
import redis
from flask_jwt_extended import JWTManager

jwt = JWTManager()

# Setup our redis connection for storing the blocklisted tokens
redis_client = redis.StrictRedis(
    host="localhost", port=6379, db=0, decode_responses=True
)

# ================== JWT MANAGEMENT ===================
@jwt.user_lookup_loader
def user_lookup_callback(_jwt_header, jwt_payload: dict):
    """
    Callback function to load a user from a JWT payload.

    The payload from the JWT is passed to this function and it is expected to
    return a user object. If the user is not found, it should return None.

    :param _jwt_header: The decoded JWT header.
    :param jwt_payload: The decoded JWT payload.
    :return: The user object if found, otherwise None.
    """
    from app.models.central import User as CentralUser
    from app.models.company import User as CompanyUser
    from app.utils.db_connection import DatabaseConnection
    identity = json.loads(jwt_payload.get("sub"))
    
    if identity.get("employee_id"):
        # User is employee
        db_connection = DatabaseConnection()
        with db_connection.get_session(jwt_payload.get("database_name")) as db_session:
            user = CompanyUser.get_user_by_id(db_session, identity.get("user_id"))
    else:
        user = CentralUser.query.get(identity.get("user_id")).to_dict()
    return user


@jwt.user_identity_loader
def user_identity_callback(user):
    """
    Callback function to convert a user object into a JSON serializable form.

    The user object is expected to have the following attributes:
        - user_id (int): The id of the user.
        - employee_id (int, optional): The id of the employee.

    :param user: The user object to be converted.
    :return: The JSON serializable form of the user object.
    """
    if user.get("employee_id"):
        user['employee_id'] = str(user.get("employee_id"))
    user['user_id'] = str(user.get("user_id"))
    return json.dumps(user)


# Callback function to check if a JWT exists in the redis blocklist
@jwt.token_in_blocklist_loader
def check_if_token_is_revoked(jwt_header, jwt_payload: dict):
    jti = jwt_payload["jti"]
    token_in_redis = redis_client.get(jti)
    return token_in_redis is not None
