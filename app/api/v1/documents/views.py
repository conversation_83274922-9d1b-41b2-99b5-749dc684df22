import os
import json
import traceback
from flask import Blueprint, jsonify, request, current_app, after_this_request, send_file
from flask_jwt_extended import get_jwt, current_user
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import <PERSON><PERSON><PERSON>elper
from app.models.company import Employee
from app.models.company_documents import Document
from app.utils.db_connection import DatabaseConnection


document_api_bp = Blueprint('document', __name__)
db_connection = DatabaseConnection()

@document_api_bp.route('/view_documents', methods=['GET', 'POST'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def view_documents():
    """
    View documents related to employees or companies.
    Provides comprehensive document information and filtering options.
    - GET: Returns JSON response with filtered documents.
    - POST: Handles form submissions or JSON data for filtering or other actions.
    """
    jwt_data = get_jwt()
    role = current_user.get("role")
    current_app.logger.info("Accessing view_documents")

    # Initialize filter parameters
    document_type_filter = uploader_filter = date_filter = employee_filter = None

    if request.method == 'POST':
        data = request.get_json()
        document_type_filter = data.get('document_type') or ""
        uploader_filter = data.get('uploader') or ""
        date_filter = data.get('date_range') or ""
        employee_filter = data.get('employee_id') or ""
    

    # Connect to the right database
    database_name = jwt_data.get('database_name')
    my_documents = []

    # Log who is viewing documents
    current_app.logger.info(f"User {current_user.get('user_id')} with role {role} viewing documents")
    current_app.logger.info(f"Employee ID: {employee_filter if employee_filter else 'N/A'}")
    current_app.logger.info(f"Filters applied - Document Type: {document_type_filter}, Uploader: {uploader_filter}, Date Range: {date_filter}")

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get list of employees for HR filter dropdown
            employees = db_session.query(Employee).all()
            employees_list = {str(emp.employee_id): {"employee_id": str(emp.employee_id), 'name': f"{emp.first_name} {emp.last_name}"} for emp in employees}

            if role not in ['hr', 'manager', 'accountant', 'company_hr', 'employee', 'supervisor']: 
                # Log the restricted access
                current_app.logger.info(f"{current_user} restricted to viewing only their own documents") 
                return jsonify(success=False, error='Invalid role'), 400

            if role in ['hr', 'manager', 'accountant', 'company_hr']:
                # HR and management roles can see all documents (company and employee)
                current_app.logger.info(f"Filtering documents for employee ID: {employee_filter}")
                documents = Document.get_documents(db_session, document_type=document_type_filter.strip(), employee_id=employee_filter)
                
            if role in ['employee', 'supervisor'] and document_type_filter == 'company':
                # Regardless of filter, employees can only see their own documents
                documents = []
            else:
                # Only show employee's own documents
                documents = Document.get_documents(db_session, 
                                                    document_type=document_type_filter.strip() if document_type_filter else None, 
                                                    employee_id=current_user.get("employee_id"))

            # Apply additional filters
            my_documents = [
                {
                    'document_id': doc.document_id,
                    'file_name': doc.file_name,
                    'file_label': doc.file_label,
                    'file_url': doc.file_url,
                    'uploaded_by': doc.uploaded_by,
                    'uploaded_at': doc.uploaded_at.strftime('%Y-%m-%d %H:%M') if doc.uploaded_at else 'Unknown',
                    'document_type': doc.document_type,
                    'employee_id': doc.employee_id,
                    'employee_name': employees_list.get(str(doc.employee_id), {}).get('name')
                } for doc in documents if (not uploader_filter or uploader_filter.lower() in doc.uploaded_by.lower()) and \
                    (not date_filter or ApiHelper.filter_by_date(doc.uploaded_at, date_filter))
            ]
            current_app.logger.info(f"Retrieved {len(my_documents)} documents successfully")

            # Sort documents by upload date (newest first)
            # my_documents.sort(key=lambda x: x['uploaded_at'], reverse=True)
            return jsonify(success=True, documents=my_documents, message="Documents retrieved successfully"), 200
        except Exception as e:
            current_app.logger.exception(f"Error retrieving documents: {e}")
            return jsonify(success=False, error="Error retrieving documents"), 400

# create endpoint to retreive only one document
@document_api_bp.route('/view_document/<uuid:document_id>', methods=['GET'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def view_document(document_id):
    current_app.logger.info("Accessing view_document")
    database_name = get_jwt().get('database_name')
    with db_connection.get_session(database_name) as db_session:
        try:
            document = Document.get_document_by_id(db_session, document_id)
            if not document:
                return jsonify(success=False, error='Document not found'), 404
            
            # If current user is an employee protect access to company documents and others ones.
            if (current_user.get('role') in ['employee', 'supervisor'] and document.document_type == 'company')\
                or (document.employee_id and document.employee_id != current_user.get('employee_id')):
                return jsonify(success=False, error='You do not have permission to view this document'), 403

            return jsonify(success=True, document=document.to_dict(), message="Document retrieved successfully"), 200
        except Exception as e:
            current_app.logger.exception(f"Error retrieving document: {e}")
            return jsonify(success=False, error="Error retrieving document"), 400

@document_api_bp.route('/upload_documents', methods=['POST'])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def upload_documents():
    """ Uploads documents related to employees or companies. """
    current_app.logger.info("accessing upload_documents")

    jwt_data = get_jwt()
    role = current_user.get("role")
    uploader_role = role 

    #handle file
    file = request.files.get("file")
    file_label = request.form.get("file_label")
    if not file:
        return jsonify(success=False, error='No file provided'), 400
    if file and not ApiHelper.allowed_file(file.filename):
        return jsonify(success=False, error="File type not allowed. Only DOCX and PDF files are allowed"), 400
    
    # Define document type based on role
    if role in ['hr', 'manager', 'accountant', 'company_hr']:
        document_type = 'company'
        employee_id = None
        #this will be updated when dealing with hr authentication
        uploader_name = f"{current_user.get('first_name')} {current_user.get('last_name')}" or current_user.get("username")
    elif role in ['employee', 'supervisor']:
        document_type = 'employee'
        employee_id = current_user.get("employee_id")
        uploader_name = f"{current_user.get('first_name')} {current_user.get('last_name')}" or current_user.get("username")
    else:
        document_type = 'company'

    #connect to the right database
    database_name = jwt_data.get('database_name')
    with db_connection.get_session(database_name) as db_session:
        try:
            # Format the uploader information
            uploader_info = f"{uploader_name} ({uploader_role})"
            current_app.logger.info(f"Document being uploaded by: {uploader_info}")

            # Upload the document to DigitalOcean Spaces and save the DB record
            result = Document.upload_document(
                db_session,
                jwt_data.get("company_id"),
                document_type,
                file,
                uploader_info,  # Pass the formatted uploader info instead of just user_id
                employee_id=employee_id,
                file_label=file_label
            )
            current_app.logger.info(f"Document uploaded successfully: {result}")
            return jsonify(success=True, data=result.to_dict(), message="Document uploaded successfully"), 201
        except Exception as e:
            current_app.logger.error(f"Error uploading document: {e}")
            return jsonify(success=False, error="Error uploading document"), 500


@document_api_bp.route("/download_documents", methods=["GET"])
@role_required(['hr', 'employee', 'manager', 'accountant', 'company_hr'])
def download_document_route():
    """
    Download a document by its ID.
    Handles document retrieval, access control, and cleanup of temporary files.
    """
    # Get the JWT data
    jwt_data = get_jwt()
    identity = json.loads(jwt_data.get('sub'))

    document_id = request.args.get("document_id")
    current_app.logger.info(f"Download requested for document ID: {document_id}")

    if not document_id:
        return jsonify(success=False, error="Missing document"), 404
    
    database_name = jwt_data.get('database_name')
    role = identity.get('role', 'employee')
    employee_id = identity.get('employee_id')

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            doc = Document.get_document_by_id(db_session, document_id)
            if not doc:
                return jsonify(success=False, error="Document not found"), 404

            # Check permissions based on role and document type
            if role in ['employee', 'supervisor']:
                # Employees can only download their own documents
                if doc.document_type == 'company' or (doc.document_type == 'employee' and doc.employee_id != employee_id):
                    current_app.logger.warning(f"""Access denied: Employee {employee_id} attempted to download document
                                               {document_id} which they don't have permission for""")
                    return jsonify(success=False, error="Access denied"), 403

            # Download the document
            local_path = Document.download_document(db_session, document_id)
            current_app.logger.info(f"Document downloaded successfully to: {local_path}")

            # Clean up the temporary file after the response is sent
            @after_this_request
            def cleanup(response):
                try:
                    os.remove(local_path)
                    current_app.logger.info(f"Temporary file {local_path} removed")
                except Exception as e:
                    current_app.logger.error(f"Error removing temp file: {e}")
                return response
            
            # Send the file to the user
            return send_file(
                local_path,
                as_attachment=True,
                download_name=os.path.basename(local_path)
            )
        except Exception as e:
            current_app.logger.error(f"Error downloading document: {e}")
            return jsonify(success=False, error=f"Error downloading document"), 500


@document_api_bp.route('/upload_employee_document', methods=['POST'])
@role_required(['hr', 'manager', 'accountant', 'company_hr'])
def upload_employee_document():
    data = request.form
    jwt_data = get_jwt()
    identity = json.loads(jwt_data.get('sub'))

    if 'file' not in request.files or not request.files.get('file'):
        return jsonify(success=False, error='No file uploaded'), 400
    
    file = request.files.get('file')
    file_label = data.get('file_label')
    employee_id = data.get('employee_id')

    # Get uploader information & Format the uploader information
    uploader_name = f"{current_user.get('first_name')} {current_user.get('last_name')}".strip() or current_user.get("username")
    uploader_role = identity.get('role')
    uploader_info = f"{uploader_name} ({uploader_role})"

    with db_connection.get_session(jwt_data.get('database_name')) as db_session:
        try:
            # Upload the document
            employee = Employee.get_employee_by_id(db_session, employee_id)
            if not employee:
                return jsonify(success=False, error='Employee not found'), 404

            current_app.logger.info(f"Uploading document for employee ID: {employee_id}")
            Document.upload_document(
                db_session,
                jwt_data.get("company_id"),
                'employee',  # Document type is always 'employee' for this route
                file,
                uploader_info,
                employee_id=employee_id,
                file_label=file_label
            )
            current_app.logger.info(f"Document uploaded successfully for employee ID: {employee_id}")

            # Get employee name for the success message
            employee_name = f"{employee['first_name']} {employee['last_name']}"
            message = f"Document uploaded successfully for {employee_name}"
            return jsonify(success=True, message=message), 201
        except Exception as e:
            current_app.logger.error(f"Error uploading document: {e}")
            current_app.logger.error(traceback.format_exc())
            return jsonify(success=False, error="Error uploading document"), 500