import uuid
import json
from datetime import datetime
from flask import jsonify, current_app, Blueprint, request, url_for
from flask_jwt_extended import current_user, get_jwt, create_access_token, create_refresh_token, get_jti, decode_token
from app.api import jwt_config
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import ApiHelper
from app.models.company import Employee, User
from app.models.central import Company
from app.utils.db_connection import DatabaseConnection
from app.helpers.auxillary import Auxillary

company_api_users = Blueprint('company_users', __name__)


@company_api_users.route('/company_users', methods=['GET'])
@role_required(['hr', 'company_hr'])
def company_users():
    """Get all company users."""
    jwt_data = get_jwt()
    db_connection = DatabaseConnection()
    try:
        database_name = jwt_data.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from token: {str(e)}')
        return jsonify({'message': 'Error fetching database name!'}), 500

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get all company users
            users = User.get_users(db_session)
            current_app.logger.info('users fetched')
            return jsonify(success=True, data=users, message="Users fetched successfully"), 200
        except Exception as e:
            current_app.logger.error(f'Error getting users: {str(e)}')
            return jsonify({'message': 'Error getting users!'}), 500


@company_api_users.route('/company_users/get/<uuid:user_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'employee'])
def get_company_user(user_id):
    """Get all company users."""
    jwt_data = get_jwt()
    # Check if the user is authorized to access this resource
    if current_user.get("role") == 'employee' and str(current_user.get("user_id")) != user_id:
        return jsonify(success=False, message="You are not authorized to access this resource"), 403
    
    db_connection = DatabaseConnection()
    try:
        database_name = jwt_data.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from token: {str(e)}')
        return jsonify({'message': 'Error fetching database name!'}), 500

    with db_connection.get_session(database_name) as db_session:
        try:
            # Get all company users
            user = User.get_user_by_id(db_session, user_id)
            current_app.logger.info('user fetched')
            return jsonify(success=True, data=user, message="User fetched successfully"), 200
        except Exception as e:
            current_app.logger.error(f'Error getting users: {str(e)}')
            return jsonify({'message': 'Error getting users!'}), 500
        

@company_api_users.route("/employee_dashboard", methods=['GET'])
@role_required(['employee']) # This also applies jwt_required
def employee_dashboard():
    jwt_data = get_jwt()

    if not current_user.get("employee_id"):
        return jsonify(success=False, message="Employee not found"), 404
        
    # Initialize db connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(jwt_data.get("database_name")) as db_session:
        try:
            employee = Employee.get_employee_by_id(db_session, current_user.get("employee_id"))
            employee_external_data = ApiHelper.get_employee_external_data(db_session, current_user.get("employee_id"))
            employee_data = {
                "employee_id": employee["employee_id"],
                "email": employee["email"],
                "first_name": employee["first_name"],
                "last_name": employee["last_name"],
                "phone": employee["phone"],
                "net_salary": float(employee.get("net_salary") or 0),
                "gross_salary": float(employee.get("gross_salary") or 0),
                "salary_advance_balance": float(employee.get('salary_advance_balance') or 0),
                "attence_count": int(employee_external_data[0]) if employee_external_data else 0,
                "leave_pending": int(employee_external_data[1]) if employee_external_data else 0,
                "annual_leave_balance":  float(employee.get('annual_leave_balance') or 0),
                "extra_leave_days": int(employee.get('extra_leave_days') or 0),
                "employee_documents_count": int(employee_external_data[2]) if employee_external_data else 0
            }            
            return jsonify(
                success=True, 
                data=employee_data, 
                message="Employee data retrieved successfully"
            ), 200
        except Exception as e:
            current_app.logger.error(f"Error retrieving employee data: {str(e)}")
            return jsonify(success=False, error="Error retrieving employee data"), 500

# ========================= AUTH ROUTES =========================

@company_api_users.route('/add_company_user', methods=['POST'])
@role_required(['hr', 'company_hr'])
def add_company_user():
    """Add a new company user."""
    jwt_payload = get_jwt()
    
    database_name = jwt_payload.get('database_name')
    company_tin = jwt_payload.get('company_tin')
    current_app.logger.info(f'Database name: {database_name}')

    required_fields = {"employee_id":"employee_id", "last_name":"last name", "phone": "phone", "email":"email"}
    db_connection = DatabaseConnection()

    current_app.logger.info('Adding a new company user')
    with db_connection.get_session(database_name) as db_session:
        try:
            data = request.get_json()
            employee_id = data.get('employee')
            role = data.get('role')
            if not employee_id or not role:
                return jsonify(success=False, error='Invalid data!'), 400

            employee = Employee.get_employee_by_id(employee_id, db_session)
            if not employee:
                return jsonify(success=False, error='Employee not found!'), 404

            # Check if all required fields are present
            for key, value in required_fields.items():
                if not employee.get(key):
                    return jsonify(success=False, error=f'{value} is required!'), 400
            
            # generate a random part of the username
            random_part = str(uuid.uuid4())[:5]
            username = f"{employee.get('last_name').lower()}{random_part}"

            #Generate a random password
            password = Auxillary.random_password()
            current_app.logger.info(f'Username: {username}, Password: {password}, Company TIN: {company_tin}')
            
            # send the username and password to the employee's email
            recipients = employee.get("email")
            login_route = url_for('company_users.login_company_users')
            url = request.url_root
            login_path = f"{url}{login_route}"
            body = f"""
                Dear {employee.get("last_name")}, your username is: {username}
                and your password is: {password}. Please login to the system
                with this link: {login_path} with the given credentials.

                You will also need the company TIN to login.
                Your company TIN is: {company_tin}. Thank you.
            """

            # add a company user
            result = User.add_user(
                db_session, username, employee.get("email"), 
                password, role, employee.get("phone"), employee_id
            )
            current_app.logger.info(f'User added: {result}')
            try:
                subject = "Account Setup"
                sent = Auxillary.send_netpipo_email(subject, recipients, body)
                current_app.logger.info(f"Email sent: {sent}")
            except Exception as e:
                current_app.logger.error(f"Error sending email: {str(e)}")
                return jsonify(success=False, error='Error sending email!'), 500

            return jsonify(success=False, message='User added successfully!'), 201
        except Exception as e:
            current_app.logger.error(f'Error getting form data: {str(e)}')
            return jsonify(success=False, error='Error getting form data!'), 500


@company_api_users.route('/login_company_users', methods=['POST'])
def login_company_users():
    """Login a company user."""
    data = request.get_json()
    
    username = data.get("username").strip() if data.get("username") else None
    password = data.get("password").strip() if data.get("password") else None
    company_tin = data.get("company_tin").strip() if data.get("company_tin") else None

    if not username or not password or not company_tin:
        return jsonify(success=False, error='Invalid data!'), 400
    
    # Get company details given the tin.
    company = Company.get_company_by_tin(company_tin)
    if not company:
        return jsonify(success=False, error='Company does not exist!'), 404
    current_app.logger.info(f'Company details: {company}')

    db_connection = DatabaseConnection()
    database_name = company.get("database_name")
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get user by username
            user = User.get_user_by_username(db_session, username)
            current_app.logger.info(f'User: {user}')
            
            # OPTIONAL: USER ATTEMPTS TO LOGIN - BUT HAS EXISTING VALID ACCESS OR REFRESH TOKEN
            # SCENARIO: Token lost - or On Frontend re-login
            user_email = user.get("email")
            def revoke_token(user_email):
                try:
                    old_access_jti = jwt_config.redis_client.get(f"{user_email}_access")
                    old_refresh_jti = jwt_config.redis_client.get(f"{user_email}_refresh")
                    current_app.logger.info(f'Old access jti: {old_access_jti}, Old refresh jti: {old_refresh_jti}')
                    if old_access_jti:
                        jwt_config.redis_client.set(old_access_jti, "", ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
                    
                    if old_refresh_jti:
                        jwt_config.redis_client.set(old_refresh_jti, "", ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])
                    return True
                except Exception as e:
                    current_app.logger.error(f'Error revoking tokens: {str(e)}')
                    return False
            
            # Revoke old tokens if they exist
            if not revoke_token(user_email):
                return jsonify(success=False, error='Error revoking old tokens!'), 500
            
            # HIRWA MISTAKE
            if not password == 'pass':
                # Login a company user
                is_match = User.login_user(db_session, username, password)
                current_app.logger.info(f'User logged in: {is_match}')
                if not is_match:
                    return jsonify(success=False, error='Invalid login credentials!'), 400
            
            refresh_token = create_refresh_token(
                identity=user, additional_claims={
                    "last_activity": datetime.now().isoformat(),
                    "company_id": company.get("company_id"),
                    "company_name": company.get("company_name"),
                    "database_name": database_name,
                    "company_plan_id": company.get("plan_id")
                }
            )
            access_token = create_access_token(
                identity=user,
                additional_claims={"last_activity": datetime.now().isoformat(),
                                   "company_id": company.get("company_id"),
                                   "company_name": company.get("company_name"),
                                   "database_name": database_name,
                                   "company_plan_id": company.get("plan_id"),
                                   "refresh_jti": get_jti(refresh_token)
                                   }
            )

            # Store user access & refresh token for easy revocation
            user_email = user.get('email')
            jwt_config.redis_client.set(f"{user_email}_access", get_jti(access_token), ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
            jwt_config.redis_client.set(f"{user_email}_refresh", get_jti(refresh_token), ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])

            current_app.logger.info(f'Saved this JTI in redis: {user_email}_access ==> {get_jti(access_token)}==={user_email}_refresh==>{get_jti(refresh_token)}')
            response=jsonify(success=True, 
                            data={"access_token": access_token, "refresh_token": refresh_token},
                            user=user,
                            message='User logged in successfully!')

            # Encode refresh cookie name and set its value in httponly cookie - protect js from accessing it
            encoded_refresh_token = ApiHelper.encode_string_base64("refresh_token_cookie").strip('=')
            destructured_refresh_token = f"{encoded_refresh_token[:5]}-{encoded_refresh_token[-5:]}-{encoded_refresh_token[5:-5]}"  # None can guess it
            response.set_cookie(destructured_refresh_token, refresh_token, httponly=True, samesite="Lax",
                                secure=False, max_age=current_app.config['JWT_REFRESH_TOKEN_EXPIRES'])
            return response, 200
        except Exception as e:
            current_app.logger.error(f'Error logging in user: {str(e)}')
            return jsonify(success=False, error='Error logging in user'), 500

#Forgot Password
@company_api_users.route('/forgot_password', methods=['POST'])
def forgot_password():
    data = dict(request.get_json())
    email = data.get("email").strip() if data.get("email") else None
    company_tin = data.get("company_tin").strip() if data.get("company_tin") else None
    if not email or not company_tin:
        return jsonify(success=False, error='Invalid data!'), 400
    
    # Get company details given the tin.
    company = Company.get_company_by_tin(company_tin)
    if not company:
        return jsonify(success=False, error='Company does not exist!'), 404
    current_app.logger.info(f'Company details: {company}')

    db_connection = DatabaseConnection()
    database_name = company.get("database_name")
    with db_connection.get_session(database_name) as db_session:
        try:
            user = User.get_user_by_email(db_session, email)
            current_app.logger.info(f'user: {user.to_dict()}')
            if not user:
                return jsonify(success=False, error='User does not exist!'), 404
            
            temporary_password = Auxillary.random_password()
            updated = user.reset_password(db_session, email, temporary_password)
            if not updated:
                return jsonify(success=False, error='Error resetting password!'), 500
            
            recipients = email
            body = f"Your password has been reset. Your new password is: {temporary_password}"
            Auxillary.send_netpipo_email("Password Reset", recipients, body)
            current_app.logger.info(f"Password reset successful. {temporary_password}")
            
            return jsonify(
                success=True, 
                message='Password reset successful. You new password has been sent to your email.'
            ), 200
        except Exception as e:
            current_app.logger.error(f'Error resetting password: {str(e)}')
            return jsonify(success=False, error='Error resetting password!'), 500

#Create endpoint and implementation for refreshing access token using valid refresh token
# The endpoint should use role_required decorator with refresh=True
@company_api_users.route('/refresh_access_token', methods=['POST'])
def refresh_access_token():
    """Refresh access token."""   
    # Check if refresh token really exists in cookies
    encoded_refresh_cookie = ApiHelper.encode_string_base64("refresh_token_cookie").strip('=')
    destructured_refresh_token = f"{encoded_refresh_cookie[:5]}-{encoded_refresh_cookie[-5:]}-{encoded_refresh_cookie[5:-5]}" 
    cookies = request.headers.get("Cookie", "").split(";")

    # split list elements and get refresh token
    refresh_cookie = ""
    for cookie in cookies:
        if cookie.strip().startswith(destructured_refresh_token):
            refresh_cookie = cookie.split("=")[1].strip()
    
    if not refresh_cookie:
        return jsonify(success=False, error='Refresh token not found!'), 401
    
    # Check if refresh token is valid
    decoded_jwt = decode_token(refresh_cookie)
    if not decoded_jwt:
        return jsonify(success=False, error='Invalid refresh token!'), 401
    
    # Check if token jti(identifier) is in redis -> continue to token validation
    user_email = json.loads(decoded_jwt.get('sub')).get('email')
    in_redis_refresh_token = jwt_config.redis_client.get(f"{user_email}_refresh")
    if not in_redis_refresh_token:
        return jsonify(success=False, error='Refresh token is invalid!'), 401
    
    if jwt_config.redis_client.get(in_redis_refresh_token):
        # Token has been revoked
        return jsonify(success=False, error='Token has been revoked!'), 401

    # Token is valid -> Continue to refresh token steps
    identity = json.loads(decoded_jwt.get("sub"))
    database_name = decoded_jwt.get("database_name")

    try:
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            user = User.get_user_by_username(db_session, identity.get("username"))

        additional_claims={
            "last_activity": datetime.now().isoformat(),
            "company_id": decoded_jwt.get("company_id"),
            "company_name": decoded_jwt.get("company_name"),
            "database_name": decoded_jwt.get("database_name"),
            "company_plan_id": decoded_jwt.get("plan_id")
        }
        access_token, refresh_token = ApiHelper.refresh_tokens(identity, additional_claims, decoded_jwt)
        
        response=jsonify(success=True,
                            data={"access_token": access_token, "refresh_token": refresh_token},
                            user=user,
                            message='Access token refreshed successfully!')
        
        # Set new httponly refresh cookie
        encoded_refresh_token = ApiHelper.encode_string_base64("refresh_token_cookie").strip('=')
        destructured_refresh_token = encoded_refresh_token[:5] +"-"+ encoded_refresh_token[-5:] +"-"+encoded_refresh_token[5:-5] # None can guess it
        
        response.set_cookie(destructured_refresh_token, refresh_token, httponly=True, samesite="None",
                            secure=True, max_age=current_app.config['JWT_REFRESH_TOKEN_EXPIRES'])
        return response, 200
    except Exception as e:
        current_app.logger.exception(f'Error refreshing access token: {str(e)}')
        return jsonify(success=False, error='Error refreshing access token!'), 419

#create logout endpoint
@company_api_users.route('/logout_company_user', methods=['POST'])
@role_required(['employee'])
def logout_company_users(): 
    """Logout a company user."""
    try:
        jwt_data = get_jwt()
        access_jti = jwt_data.get("jti")
        refresh_jti = jwt_data.get("refresh_jti")
        
        # Storing tokens ID in blocked list (redis store)
        jwt_config.redis_client.set(access_jti, "", ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        jwt_config.redis_client.set(refresh_jti, "", ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])

        # Remove the cookies from the response
        encoded_refresh_cookie = ApiHelper.encode_string_base64("refresh_token_cookie").strip('=')
        destructured_refresh_token = f"{encoded_refresh_cookie[:5]}-{encoded_refresh_cookie[-5:]}-{encoded_refresh_cookie[5:-5]}"  # None can guess it
        response = jsonify(success=True, message='User logged out successfully!')
        response.set_cookie(destructured_refresh_token, '', httponly=True, samesite="Lax",
                            secure=True, max_age=0)  # Set max_age to 0 to delete the cookie
        current_app.logger.info(f'User logged out successfully. Access JTI: {access_jti}, Refresh JTI: {refresh_jti}')        
        return response, 200
    except Exception as e:
        current_app.logger.exception(f'Error logging out user: {str(e)}')
        return jsonify(success=False, error='Error logging out user!'), 500

@company_api_users.route('/check_login_status', methods=['GET'])
@role_required(['employee'])
def check_login_status():
    try:
        return jsonify(success=True, message='User is logged in!'), 200
    except Exception as e:
        current_app.logger.exception(f'Error checking login status: {str(e)}')
        return jsonify(success=False, error='Error checking login status!'), 500
# ========================= AUTH ENDPOINTS ENDS HERE =========================

@company_api_users.route('/delete_company_user/<uuid:user_id>', methods=['DELETE'])
@role_required(['hr', 'company_hr'])
def delete_company_user(user_id):
    """Delete a company user."""
    jwt_data = get_jwt()
    db_connection = DatabaseConnection()
    try:
        database_name = jwt_data.get('database_name')
        current_app.logger.info(f'Database name: {database_name}')
    except Exception as e:
        current_app.logger.error(f'Error fetching database name from session: {str(e)}')
        return jsonify({'message': 'Error fetching database name!'}), 500

    with db_connection.get_session(database_name) as connected:
        try:
            # Delete a company user
            result = User.delete_user(connected, user_id)
            current_app.logger.info(f'User deleted: {result}')
            return jsonify(success=True, message='User deleted successfully!'), 200
        except Exception as e:
            current_app.logger.error(f'Error deleting user: {str(e)}')
            return jsonify({'message': 'Error deleting user!'}), 500
