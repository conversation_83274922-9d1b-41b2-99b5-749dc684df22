import os
import calendar
import traceback
import requests
import tempfile
import pandas as pd
from uuid import UUID
from io import BytesIO
from openpyxl import load_workbook
from datetime import datetime, timedelta
from flask import Blueprint, current_app, request, jsonify, send_file
from werkzeug.utils import secure_filename
from flask_jwt_extended import get_jwt, current_user
from app.models.company import Employee, Departments, Site, Attendance, Payroll
from app.utils.db_connection import DatabaseConnection
from app.models.central import Company, Plans
from app.api_helpers.ApiHelpers import UserInputValidator
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.api.v1.decorators.auth_decorators import role_required
from app.api.jwt_config import redis_client
from app.helpers.route_helpers import jwt_restrict_based_on_plan
from app.helpers.pdf_generator import PDFGenerator


netpipo_base_url = os.getenv('NETPIPO_BASE_URL')
db_connection = DatabaseConnection()

employees_api_bp = Blueprint('employees', __name__)
employees_api_bp.before_request(jwt_restrict_based_on_plan)


@employees_api_bp.route('/get_employees_list', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employees_list():
    """Get all employees(active) in the company."""
    MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
    BASE_URL = os.getenv('BASE_URL')

    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')

    # Get the company_id from the session
    current_app.logger.info(f"Company ID: {company_id}")
    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400

    # Get the company database name
    current_app.logger.info(f"Database name: {database_name}")
    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
    
    try:
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            current_app.logger.info(f"Employees: {employees}")
            if not MICROSERVICE_KEY:
                # If company is not using face recognition for attendance return employees as they are
                return jsonify(success=True, employees=employees, message="Employees retrieved successfully"), 200

            images = Attendance.get_images_by_subject(MICROSERVICE_KEY, BASE_URL)
            current_app.logger.info('Images retrieved')

            # Create a mapping of employee IDs to image URLs
            image_map = {image['subject_name'][:36]: image['image_url'] for image in images}

            # Build employees_info with image URLs
            employees = [{**employee, "image_url": image_map.get(str(employee['employee_id']), None)} for employee in employees]
            return jsonify(success=True, data=employees, message="Employees retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error getting employees list: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    

@employees_api_bp.route('/get_inactive_employees', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def get_inactive_employees():
    """Get all inactive employees in the company."""
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')

    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        return jsonify(success=False, error='Try again or re-login'), 400

    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        return jsonify(success=False, error='Try again or re-login again'), 400
    
    try:
        
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_inactive_employees(db_session)
        return jsonify(success=True, data=employees, message="Employees retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    

@employees_api_bp.route('/get_employee/<uuid:employee_id>', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def get_employee(employee_id):
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')

    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        return jsonify(success=False, error='Try again or re-login'), 400

    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        return jsonify(success=False, error='Try again or re-login again'), 400
    
    try:
        with db_connection.get_session(database_name) as db_session:
            employee = Employee.get_employee_by_id(db_session, employee_id)
        return jsonify(success=True, data=employee, message="Employee retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@employees_api_bp.route('/employee_leave_balance', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employee_leave_balance_list():
    """Get all employees in the company."""
    try:
        # Get the company_id from the session
        company_id = get_jwt().get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        current_app.logger.info(f"Database name: {database_name}")
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
        # Initialize the database connection and get all employees
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            # Manipulating annual_leave_balance (rounding it to 2 decimal places)
            employees_info = [{**employee, "annual_leave_balance": round(employee.get("annual_leave_balance") or 0, 2)} for employee in employees]
            current_app.logger.info(f"Employees: {employees}")
        return jsonify(success=True, data=employees_info, message="Employees retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@employees_api_bp.route('/register_employees', methods=['POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def register_employee():
    """Register employee in the company."""
    jwt_data = get_jwt()
    data = request.get_json()
    plan_id = jwt_data.get('company_plan_id')
    database_name = jwt_data.get('database_name')
    company_id = jwt_data.get('company_id')
    
    # Initial validation
    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400
    
    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400

    current_app.logger.info(f"Plan ID: {plan_id}")
    current_app.logger.info(f"database_name: {database_name}")

    # Additional validation on user input
    is_valid, errors = UserInputValidator.validate(data, 'register_employee')
    if not is_valid:
        return jsonify(success=False, error=errors), 400

    # Extract the data
    first_name = data.get('first_name')
    last_name = data.get('last_name')
    nid = data.get('nid')
    rssb_number = data.get('rssb_number')
    bank_name = data.get('bank_name')
    bank_account = data.get('bank_account')
    branch_name = data.get('branch_name')
    account_name = data.get('account_name')
    birth_date = data.get('birth_date')
    marital_status = data.get('marital_status')
    gender = data.get('gender')
    employee_tin = data.get('employee_tin')
    employee_type = data.get('employee_type')
    department = data.get('department')
    salary_type = data.get('salary_type')
    salary_amount = data.get('salary_amount')
    transport_allowance = data.get('transport_allowance')
    housing_allowance = data.get('housing_allowance')
    communication_allowance = data.get('communication_allowance')
    over_time = data.get('over_time')
    other_allowance = data.get('other_allowance')
    email = data.get('email', None)
    phone = data.get('phone', None)
    job_title = data.get('job_title')
    hire_date = data.get('hire_date')
    annual_leave_balance = float(data.get("annual_leave_balance") or 0)
    extra_leave_days = int(data.get("extra_leave_days") or 0)
    contract_end_date = data.get('contract_end_date')

    current_app.logger.info(f"email: {email}, Annual Leave Balance before: {annual_leave_balance}, \
                            extra leave days: {extra_leave_days}")

    if salary_type == 'net_salary':
        # set the salary amount depending on the salary type
        net_salary = salary_amount
        gross_salary = None
        total_staff_cost = None
    
    if salary_type == 'gross_salary':
        gross_salary = salary_amount
        net_salary = None
        total_staff_cost = None
    
    if salary_type == 'total_staff_cost':
        total_staff_cost = salary_amount
        gross_salary = None
        net_salary = None

    try:
        with db_connection.get_session(database_name) as db_session:
            # First check for any unique constraints before adding the employee
            unique = Employee.check_unique(db_session, nid, rssb_number, email, phone)
            if unique:
                current_app.logger.error(f"Error while saving employee: {unique}")
                return jsonify(success=False, error="Employee already exists"), 400
            
            # Get company plan to check number of employees
            plan = Plans.get_plan_by_id(plan_id)
            print("This is plan: ", plan)
            number_of_employees = plan.get('num_of_employees', 0)
            # Retrieve the employees so that we can count them
            employees = Employee.get_employees(db_session)
            current_app.logger.info(f"length of employees: {len(employees)}")
            current_app.logger.info(f"number of employees: {number_of_employees}")
            if len(employees) >= number_of_employees:
                return jsonify(success=False, error=f"Your plan allows you add up to {number_of_employees} employees. You have reached the limit."), 400

            register = Employee.add_employee(
                db_session, company_id,
                first_name, last_name, nid, rssb_number,
                bank_name, bank_account, branch_name,
                account_name, birth_date, marital_status, gender,
                employee_tin, employee_type, department, net_salary,
                transport_allowance, housing_allowance,
                communication_allowance, over_time,
                other_allowance, email, phone, job_title,
                hire_date, annual_leave_balance, extra_leave_days,
                gross_salary, total_staff_cost, contract_end_date
            )
            current_app.logger.info(f"Register: {register} and the type is {type(register)}")
            
            # An error occurred while creating new employee
            if not register:
                current_app.logger.error(f"Error while saving employee: {register}")
                return jsonify(success=False, error="An error occured. Try again"), 400
            
            # Registration successfully
            current_app.logger.info("after accessing the if statement when an employee has been registered")
            name = f"{first_name} {last_name}"
            message= f"Employee {name} was registered succesfully"
            current_app.logger.info(message)
            current_app.logger.info(f"Name: {name}")
            
            # send the registered employee to the device database
            try:
                current_app.logger.info("before retriving employee_id from register")
                
                employee_id = str(register.employee_id)
                current_app.logger.info(f"Employee ID: {employee_id}")
                
                # construct the employee data as a json object
                roll_id = 0
                company_id = str(company_id)
                url = f"{netpipo_base_url}/get_database_name_by_id"
                data = {'company_id': company_id}
                try:
                    # Check if the company with the company-id exists in the companies
                    response = requests.get(url, params=data)
                    database_name = response.json().get('database_name')
                    current_app.logger.info(f"Database name: {database_name}")
                except Exception as e:
                    current_app.logger.error(f"Error fetching database name: {e}")
                    database_name = None
                
                if database_name == None:
                    current_app.logger.error("Database name is missing")
                    return jsonify(success=False, error="An error occurred. Try again"), 400
                
                employee_data = {
                    'employee_id': employee_id, 'privilege': roll_id,
                    'name': name, 'company_id': company_id
                }
                current_app.logger.info(f"Employee data: {employee_data}")
                # send the employee data to the device database
                url = f"{netpipo_base_url}/addEmployee"
                current_app.logger.info(f"Posting employee data: {employee_data} to {url}")

                # Post employee data to the endpoint
                try:
                    response = requests.post(url, json=employee_data)
                    current_app.logger.info(f"Response: {response}")
                    #response.raise_for_status()  # Raise an exception for HTTP errors
                    response_data = response.json()
                    current_app.logger.info(f"Response jsonified: {response_data}")
                except requests.exceptions.RequestException as e:
                    current_app.logger.error(f"Error posting employee data: {e}")

                user_id = response_data['userId']
                # Send employee data to the device
                url2 = f"{netpipo_base_url}/setOneUserJson"
                # get the devices associated with the company given the companyu_id
                url3 = f"{netpipo_base_url}/get_company_devices"
                try:
                    response = requests.get(url3, params={'company_id': company_id})
                    current_app.logger.info(f"Response: {response}")
                    #response.raise_for_status()  # Raise an exception for HTTP errors
                    response_data = response.json()
                    devices = response_data.get('devices')
                    current_app.logger.info(f"Devices: {devices}")
                except Exception as e:
                    current_app.logger.error(f"Error fetching devices: {e}")
                    devices = []
                if len(devices) > 0:
                    for device_sn in devices:
                        current_app.logger.info(f"Device SN: {device_sn}")
                        payload = {
                            "enrollId": user_id,
                            "backupNum": -1,
                            "deviceSn": device_sn
                        }
                        try:
                            response2 = requests.post(url2, json=payload)
                            #response2.raise_for_status()
                            current_app.logger.info(f"employee sent to the device: {response2.json()}")
                        except Exception as e:
                            current_app.logger.error(f"Error sending data to device: {str(e)}")
            except Exception as e:
                # Failed to send user info to the device db
                current_app.logger.error(f"Error: {e}")
                return jsonify(success=False, error='An error occurred. Please try again later.'), 500
            
            # All went well
            return jsonify(success=True, message=message), 200
    except Exception as e:
        current_app.logger.error(f"Error saving employee: {e}")
        return jsonify(success=False, error='An error occurred. y Please try again later.'), 500


@employees_api_bp.route('/upload_employees', methods=['POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def upload_employees():
    """Upload employees from an Excel file to the database."""
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')

    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400

    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400

    try:
        with db_connection.get_session(database_name) as db_session:
            # Access uploaded file
            if not 'file' in request.files:
                return jsonify(success=False, error='No file part'), 400
            
            file = request.files['file']
            # check if file is excel extension
            filename = secure_filename(file.filename)
            if filename.rsplit('.', 1)[1].lower() != 'xlsx':
                return jsonify(success=False, error='File must be an Excel file'), 400
            
            employees_df = pd.read_excel(file)
            count = 0
            addition = False
            errors = []
            for idx, row in employees_df.iterrows():
                if pd.isna(row['First Name']) and pd.isna(row['Last Name']):
                    message = f"Row {idx+2}: Missing both First Name and Last Name."
                    #flash(f"Row {idx+1}: Missing both First Name and Last Name.", 'danger')
                    errors.append(message)
                    continue

                first_name = row['First Name']
                last_name = row['Last Name']
                nid = str(row['nid']).split('.')[0]
                rssb_number = str(row['nsf']).split('.')[0]
                bank_name = None if pd.isna(row['Bank Name']) else row['Bank Name']
                account_number = None if pd.isna(row['Account Number']) else str(row['Account Number']).split('.')[0]
                branch_name = None if pd.isna(row['Branch Name']) else row.get('Branch Name', '')
                account_name = None if pd.isna(row['Account Name']) else row.get('Account Name', '')
                birth_date = None if pd.isna(row['birth date']) else pd.to_datetime(row['birth date'], errors='coerce')
                marital_status = None if pd.isna(row['marital status']) else row['marital status']
                gender = row['gender']
                employee_tin = str(row['employee_tin']).split('.')[0]
                employee_type = row['employee_type']
                department = None if pd.isna(row['department']) else row['department']
                salary_type = row['Salary Type']
                salary_amount = row['Salary Amount']
                transport_allowance = row['transport_allowance'] if not pd.isna(row['transport_allowance']) else 0
                housing_allowance = row['housing_allowance'] if not pd.isna(row['housing_allowance']) else 0
                communication_allowance = row['communication_allowance'] if not pd.isna(row['communication_allowance']) else 0
                overtime = row['overtime'] if not pd.isna(row['overtime']) else 0
                other_allowances = row['other_allowances'] if not pd.isna(row['other_allowances']) else 0
                email = None if pd.isna(row.get("email")) else row.get("email"),
                phone = None if pd.isna(row.get("phone")) else str(row.get("phone")).split('.')[0],
                job_title = None if pd.isna(row.get("job_title")) else row.get("job_title"),
                hire_date = None if pd.isna(row.get("hire_date")) else pd.to_datetime(row.get("hire_date"), errors="coerce"),
                contract_end_date = None if pd.isna(row.get("contract_end_date")) else pd.to_datetime(row.get("contract_end_date"), errors="coerce"),
                
                check1 = (salary_type == 'net salary')
                check2 = (salary_type == 'gross salary')
                current_app.logger.info(f"Check1: {check1}")
                current_app.logger.info(f"Check2: {check2}")
                
                if salary_type == 'net salary':
                    net_salary = salary_amount
                    gross_salary = None
                    total_staff_cost = None
                
                if salary_type == 'gross salary':
                    gross_salary = salary_amount
                    net_salary = None
                    total_staff_cost = None
                
                if salary_type == 'total staff cost':
                    total_staff_cost = salary_amount
                    gross_salary = None
                    net_salary = None
                
                new_employee = Employee(
                    first_name=first_name,
                    last_name=last_name,
                    nid=nid,
                    nsf=rssb_number,
                    bank_name=bank_name,
                    bank_account=account_number,
                    branch_name=branch_name,
                    account_name=account_name,
                    birth_date=birth_date,
                    marital_status=marital_status,
                    gender=gender,
                    employee_tin=employee_tin,
                    employee_type=employee_type,
                    department=department,
                    net_salary=net_salary,
                    gross_salary=gross_salary,
                    total_staff_cost=total_staff_cost,
                    transport_allowance=transport_allowance,
                    housing_allowance=housing_allowance,
                    communication_allowance=communication_allowance,
                    over_time=overtime,
                    other_allowance=other_allowances,
                    email=email,
                    phone=phone,
                    job_title=job_title,
                    hire_date=hire_date,
                    contract_end_date=contract_end_date
                )
                    # Get the companies plan from session
                plan_id = jwt_data.get('company_plan_id')
                current_app.logger.info(f"Plan ID: {plan_id}")

                # Check the plan details
                try:
                    plan = Plans.get_plan_by_id(plan_id)
                    current_app.logger.info(f"Plan: {plan}")
                    if not plan:
                        current_app.logger.error("No plan found")

                    number_of_employees = plan['num_of_employees']
                    all_employees = Employee.get_employees(db_session)
                    count_employees = len(all_employees)

                    current_app.logger.info(f"Number of employees: {number_of_employees}")
                    current_app.logger.info(f"all employees: {all_employees}")

                    if count_employees >= number_of_employees:
                        error = f"Error in Row {idx+2}: Your plan allows you add up to {number_of_employees} employees. You have reached the limit."
                        return jsonify(success=False, error=error), 400
                except Exception as e:
                    current_app.logger.error(f"Error fetching plan: {e}")
                    number_of_employees = 0

                # Check for mandatory fields
                mandatory = Employee.check_mandatory(db_session, new_employee)
                if mandatory:
                    error = f"Error in Row {idx+2}: {mandatory}"
                    errors.append(error)
                    continue

                # Check for unique fields
                unique = Employee.check_unique(db_session, nid, rssb_number, email, phone)
                if unique:
                    error = f"Error in Row {idx+2}: {unique}"
                    errors.append(error)
                    continue

                # Check if the department is available
                if department and not pd.isna(department):
                    departments = Departments.get_departments(db_session)
                    department_names = [dept['department_name'] for dept in departments]
                    available = Employee.check_if_department_available(db_session, department.strip().lower())
                    if not available:
                        error = f"""
                        Error in Row {idx+2}: Department {department} in your Excel file does not exist in your company
                        departments. If it is a new department, Kindly add it.
                        """
                        error = f"Error in Row {idx+2}: Department {department} in your Excel file does not exist in your company departments. your departments: {department_names}"
                        errors.append(error)
                        continue
                # Make sure the salary amount is a number and a positive number
                try:
                    salary_amount = float(salary_amount)
                    if salary_amount < 0:
                        error = f"Error in Row {idx+2}: Salary amount must be a positive number."
                        errors.append(error)
                        continue
                except Exception as e:
                    error = f"Error in Row {idx+2}: Salary amount must be a number."
                    errors.append(message)
                    continue

                if salary_type == 'net salary':
                    net_salary = salary_amount
                    gross_salary = None
                    total_staff_cost = None
                elif salary_type == 'gross salary':
                    gross_salary = salary_amount
                    net_salary = None
                    total_staff_cost = None
                elif salary_type == 'total staff cost':
                    total_staff_cost = salary_amount
                    gross_salary = None
                    net_salary = None
                else:
                    message = f"Error in Row {idx+2}: Salary type must be either 'Net Salary' or 'Gross Salary' or 'Total Staff Cost'."
                    errors.append(message)
                    continue
                
                try:
                    # Set default values for annual leave balance and extra leave days if not provided
                    annual_leave_balance = 0  # Default value
                    extra_leave_days = 0      # Default value

                    addition = Employee.add_employee(db_session, company_id, first_name, last_name, nid,
                                        rssb_number, bank_name, account_number, branch_name, account_name,
                                        birth_date, marital_status, gender,
                                        employee_tin, employee_type, department, net_salary,
                                        transport_allowance, housing_allowance,
                                        communication_allowance, overtime, other_allowances,
                                        email, phone, job_title, hire_date, annual_leave_balance, extra_leave_days,
                                        gross_salary, total_staff_cost)
                    count += 1
                    message=(f"*Employee {first_name} {last_name}'s data were uploaded successfully.*")
                    current_app.logger.info(message)
                    errors.append(message)
                except Exception as e:
                    current_app.logger.error(f"Row {idx+2}: Error saving employee: {e}")
                    message = f"Error in Row {idx+2}: Third error occurred. Please try again later."
                    errors.append(message)
                    continue

            if addition:
                message = f"*{count} Employee(s) were uploaded successfully.*"
                return jsonify(success=True, message=message), 200
            else:
                # pop the last error message
                errors.pop()
                message = "No employees were uploaded, check the file and try again."
                errors.append(message)
            # Return the errors
            return jsonify(success=False, error=errors), 400
    except Exception as e:
        current_app.logger.error(f"Error saving employees: {e}")
        current_app.logger.error("".join(traceback.format_exc()))
        return jsonify(success=False, error='An error occurred . Please try again later.'), 500
    

@employees_api_bp.route('/update_employee/<uuid:employee_id>', methods=['PUT'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def update_employee(employee_id):
    """Update an employee's details."""
    data = request.get_json()
    current_app.logger.info(f"Form data: {data}")

    company_id = get_jwt().get('company_id')
    if not company_id:
        current_app.logger.error("Company ID is missing in session")
        return jsonify({'success': False, 'message': 'Company ID is missing in session.'}), 500

    # Get the company database name
    database_name = CompanyHelpers.get_company_database_name(company_id)
    if not database_name:
        current_app.logger.error("Database name could not be retrieved")
        return jsonify(success=False, error='Database name could not be retrieved.'), 500

    # Additional validation
    # is_valid, errors = UserInputValidator.validate(data, 'update_employee')
    # if not is_valid:
    #     return jsonify(success=False, error=errors), 400
        
    with db_connection.get_session(database_name) as db_session:
        employee = Employee.get_employee_by_id(db_session, employee_id)
        if not employee:
            current_app.logger.error("Employee not found")
            return jsonify(success=False, error='Employee not found.'), 404

        # Retrieve Sites from the database
        try:
            sites = Site.get_sites(db_session)
            current_app.logger.info('Sites: %s', sites)
            site_id = request.form.get('site_id')

            if site_id:
                site_id = UUID(site_id)
                current_app.logger.info(f'site id: {site_id}')
            else:
                site_id = None
        except Exception as e:
            current_app.logger.error(f"Error fetching sites: {e}")
            sites = []


        # Update the employee details
        try:
            first_name = data.get('first_name') or employee.get("first_name")
            last_name = data.get('last_name') or employee.get("last_name")
            nid = data.get('nid') or employee.get("nid")
            nsf = data.get('nsf') or employee.get("nsf")
            bank_name = data.get('bank_name') or employee.get("bank_name")
            bank_account = data.get('bank_account') or employee.get("bank_account")
            branch_name = data.get('branch_name') or employee.get("branch_name")
            account_name = data.get('account_name') or employee.get("account_name")
            birth_date = data.get('birth_date') or employee.get("birth_date")
            marital_status = data.get('marital_status') or employee.get("marital_status")
            gender = data.get('gender') or employee.get("gender")
            employee_tin = data.get('employee_tin') or employee.get("employee_tin")
            employee_type = data.get('employee_type') or employee.get("employee_type")
            department = data.get('department') or employee.get("department")
            salary_type = data.get('salary_type') or employee.get("salary_type")
            salary_amount = data.get('salary_amount') or employee.get("salary_amount")
            transport_allowance = data.get('transport_allowance') or employee.get("transport_allowance")
            housing_allowance = data.get('housing_allowance') or employee.get("housing_allowance")
            communication_allowance = data.get('communication_allowance') or employee.get("communication_allowance")
            over_time = data.get('over_time') or employee.get("over_time")
            other_allowance = data.get('other_allowance') or employee.get("other_allowance")
            email = data.get('email') or employee.get("email")
            phone = data.get('phone') or employee.get("phone")
            job_title = data.get('job_title') or employee.get("job_title")
            hire_date = data.get('hire_date') or employee.get("hire_date")
            is_active = data.get('is_active') or employee.get("is_active")
            is_brd_sponsored = data.get('is_brd_sponsored') or employee.get("is_brd_sponsored")
            attendance_applicable = data.get('attendance_applicable') or employee.get("attendance_applicable")
            annual_leave_balance = float(data.get("annual_leave_balance") or employee.get("annual_leave_balance") or 0)
            extra_leave_days = int(data.get("extra_leave_days") or employee.get("extra_leave_days") or 0)
            contract_end_date = data.get('contract_end_date') or employee.get("contract_end_date")

            current_app.logger.info(f"job_title: {job_title}")
            current_app.logger.info(f"hire_date: {hire_date}")
            if hire_date:
                try:
                    hire_date = datetime.strptime(hire_date, '%Y-%m-%d')  # Adjust format to match your input (e.g., '2024-11-22').
                except Exception as e:
                    current_app.logger.error(f"Error converting hire date: {e}")
                    hire_date = None
            if birth_date:
                try:
                    birth_date = datetime.strptime(birth_date, '%Y-%m-%d')  # Adjust format to match your input (e.g., '2024-11-22').
                except Exception as e:
                    current_app.logger.error(f"Error converting hire date: {e}")
                    birth_date = None
            
            net_salary = None
            gross_salary = None
            total_staff_cost = None
            if salary_type == 'net_salary':
                # set the salary amount depending on the salary type
                net_salary= salary_amount
                gross_salary = None
                total_staff_cost = None
            
            if salary_type == 'gross_salary':
                gross_salary = salary_amount
                net_salary = None
                total_staff_cost = None
            
            if salary_type == 'total_staff_cost':
                total_staff_cost = salary_amount
                gross_salary = None
                net_salary = None

            try:
                result = Employee.update_employee(
                    db_session, employee_id, first_name,
                    last_name, nid, nsf, bank_name, bank_account, branch_name, account_name,
                    birth_date, marital_status, gender, employee_tin, employee_type, department,
                    net_salary, gross_salary, total_staff_cost, transport_allowance, housing_allowance,
                    communication_allowance, over_time, other_allowance, email, phone, job_title,
                    hire_date, site_id, is_active, is_brd_sponsored, attendance_applicable,
                    annual_leave_balance, extra_leave_days, contract_end_date
                )

                current_app.logger.info(f"Result: {result}")
                message = f"Employee {employee['first_name']} {employee['last_name']} was updated successfully."
                return jsonify(success=True, message=message), 200
            except Exception as e:
                db_session.rollback()
                current_app.logger.exception(f"Error updating employee - {salary_type}: {e}")
                return jsonify(success=False, error='An error occurred while updating the employee. Please try again later.'), 500
        except Exception as e:
            db_session.rollback()
            current_app.logger.exception(f"Error updating employee {salary_type}: {e}")
            return jsonify(success=False, error='An error occurred while updating the employee. Please try again later.'), 500


@employees_api_bp.route('/flag_as_inactive/<uuid:employee_id>', methods=['POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def flag_as_inactive(employee_id):
    """Flag an employee as inactive."""
    database_name = current_user.get("database_name")

    try:
        with db_connection.get_session(database_name) as db_session:
            employee = Employee.get_employee_by_id(db_session, employee_id)
            if not employee:
                return jsonify(success=False, error='Employee not found.'), 404
            
            flagged_user = Employee.flag_as_inactive(db_session, employee_id)
            return jsonify(success=True, data=flagged_user, message='Employee was successfully flagged as inactive.'), 200
    except Exception as e:
        current_app.logger.error(f"Error flagging employee as inactive: {e}")
        return jsonify(success=False, error='An error occurred while flagging the employee as inactive. Please try again later.'), 500

@employees_api_bp.route('/make_employee_active/<uuid:employee_id>', methods=['POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def make_employee_active(employee_id):
    """Flag an employee as inactive."""
    database_name = current_user.get("database_name")

    try:
        with db_connection.get_session(database_name) as db_session:
            employee = Employee.get_employee_by_id(db_session, employee_id)
            if not employee:
                return jsonify(success=False, error='Employee not found.'), 404
            
            active_user = Employee.make_employee_active(db_session, employee_id)
            return jsonify(success=True, data=active_user, message='Employee was successfully flagged as inactive.'), 200
    except Exception as e:
        current_app.logger.error(f"Error flagging employee as inactive: {e}")
        return jsonify(success=False, error='An error occurred while flagging the employee as inactive. Please try again later.'), 500


@employees_api_bp.route('/download_employees_template', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def download_employees_template():
    """Download an Excel template for uploading employees.
    Description:
    The template is located in the static folder and is named employees_template.xlsx.
    we want the user to download this template so that they can fill in the employee details
    """
    try:
        # Load the Excel template
        template_path = 'app/routes/employees/Employees data template Sample.xlsx'
        wb = load_workbook(template_path)
        ws = wb.active

        # Download the template available in the path.
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        return send_file(output, as_attachment=True, download_name='sample_employees_data_template.xlsx')
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@employees_api_bp.route('/employees_payslip', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def employees_payslip():
    """Generate a payslip for all employees."""
    # Get important information from redis store
    current_date = redis_client.get('pay_date')
    employees_with_deductions = redis_client.get('employees_with_deductions')
    
    if not current_date:
        current_date = datetime.now()
        current_app.logger.error("Current date is missing.")
    
    if not employees_with_deductions:
        current_app.logger.error("No payroll summary data available.")
        return jsonify(success=False, error="No payroll summary data available."), 404
    
    # Logging
    current_app.logger.info("Information found in session with employee deductions.")
    
    first_day = current_date.replace(day=1)
    _, days_in_month = calendar.monthrange(current_date.year, current_date.month)
    last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

    # Format dates as DD/MM/YYYY
    formatted_first_day = first_day.strftime("%d/%m/%Y")
    formatted_last_day = last_day.strftime("%d/%m/%Y")
    
    try:
        company_id = get_jwt().get('company_id')
        company_data = Company.get_company_by_id(company_id)
        first_name = current_user.get('first_name')
        last_name = current_user.get('last_name')
        pay_date = current_user.get('pay_date')
        if not pay_date:
            pay_date = current_date
            current_app.logger.error("Pay date is missing.")
        current_app.logger.info("Company data retrieved.")
        
        employees_payslip_data = { 
            "employees_with_deductions": employees_with_deductions, "first_day": formatted_first_day,
            "last_day": formatted_last_day, "days_in_month": days_in_month,
            "pay_date": pay_date, "first_name": first_name.upper(),
            "last_name": last_name.upper(), "company_data": company_data
        }
        return jsonify(success=True, data = employees_payslip_data, message="Employee payslip generated successfully."), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting company data: {e}")
        company_data = None
        return jsonify(success=False, error="Employee payslip generating error."), 500

@employees_api_bp.route('/single_employee_payslip/<uuid:employee_id>', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def single_employee_payslip(employee_id):
    """Generate a payslip for a single employee."""
    try:
        # Get important information from redis store
        pay_date = redis_client.get('pay_date')
        employees_data = redis_client.get('employees_with_deductions')

        if not pay_date:
            pay_date = datetime.now()
            current_app.logger.error("Current date is missing.")
        
        if not employees_data:
            current_app.logger.error("No payroll summary data available.")
            return jsonify(success=False, error="No payroll summary data available."), 404
    
        first_day = pay_date.replace(day=1)
        _, days_in_month = calendar.monthrange(pay_date.year, pay_date.month)
        last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        # Format dates as DD/MM/YYYY
        formatted_first_day = first_day.strftime("%d/%m/%Y")
        formatted_last_day = last_day.strftime("%d/%m/%Y")

    except Exception as e:
        current_app.logger.error(f"An error occurred while getting current date: {e}")
        return jsonify(success=False, error="An error occurred while getting current date."), 500
    
    try:
        company_id = get_jwt().get('company_id')
        company_data = Company.get_company_by_id(company_id)
        logo = company_data['logo']
        first_name = current_user.get('first_name')
        last_name = current_user.get('last_name')
        pay_date = redis_client.get('pay_date')
        current_app.logger.info("Company data retrieved.")

    except Exception as e:
        current_app.logger.error(f"An error occurred while getting company data: {e}")
        company_data = None

    # Get deductions for a specific employee
    # Filter the list
    filtered_employee = next((item for item in employees_data if item['employee']['employee_id'] == employee_id), None)
    if not filtered_employee:
        current_app.logger.error("Employee not found.")
        return jsonify(success=False, error="Employee not found."), 404
    
    current_app.logger.info("Filtered employee: ", filtered_employee)
    
    try:
        current_app.logger.info("Rendering single employee payslip.")
        employee_payslip_data = {
            "emp_data": filtered_employee, "first_name": first_name.upper(),
            "last_name": last_name.upper(), "days_in_month": days_in_month,
            "last_day": formatted_last_day, "first_day": formatted_first_day,
            "company_data": company_data, "logo": logo, "pay_date": pay_date
        }
        return jsonify(success=True, data=employee_payslip_data, message="Employee payslip generated successfully."), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred while rendering single employee payslip: {e}")
        return jsonify(success=False, error="An error occurred while rendering single employee payslip."), 500

@employees_api_bp.route('/send_payslip_email/<uuid:employee_id>', methods=['POST'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def send_payslip_email(employee_id):
    """Send a payslip as a PDF attachment to an employee.
    """
    try:
        current_date = redis_client.get('pay_date')
        employees_data = redis_client.get('employees_with_deductions')
        
        if not current_date:
            current_app.logger.error("Current date is missing.")
            current_date = datetime.now()

        if not employees_data:
            current_app.logger.error("No payroll summary data available.")
            return jsonify(success=False, error="No payroll summary data available."), 404
        
        first_day = current_date.replace(day=1)
        _, days_in_month = calendar.monthrange(current_date.year, current_date.month)
        last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        # Format dates as DD/MM/YYYY
        formatted_first_day = first_day.strftime("%d/%m/%Y")
        formatted_last_day = last_day.strftime("%d/%m/%Y")
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting current date: {e}")
        return jsonify(success=False, error="An error occurred while getting current date."), 500

    try:
        company_id = get_jwt().get('company_id')
        company_data = Company.get_company_by_id(company_id)
        first_name = current_user.get('first_name')
        last_name = current_user.get('last_name')
        pay_date = redis_client.get('pay_date')
        if not pay_date:
            pay_date = current_date
            current_app.logger.error("Pay date is missing.")
        current_app.logger.info("Company data retrieved.")
    except Exception as e:
        current_app.logger.error(f"An error occurred while getting company data: {e}")
        company_data = None
        return jsonify(success=False, error="An error occurred while getting company data."), 500

    # Get deductions for a specific employee
    filtered_employee = next((item for item in employees_data if item['employee']['employee_id'] == employee_id), None)
    if not filtered_employee:
        current_app.logger.error("Employee not found.")
        return jsonify(success=False, error="Employee not found."), 404

    # Check if employee has an email
    employee_email = filtered_employee['employee'].get('email')
    if not employee_email:
        current_app.logger.error("Employee does not have an email address.")
        return jsonify(success=False, error="Employee does not have an email address. Please update the employee profile."), 400

    try:
        # Generate comprehensive PDF payslip without using number_to_words
        try:
            # Log company data for debugging
            current_app.logger.info(f"Company data for PDF: {company_data}")
            if 'logo' in company_data:
                current_app.logger.info(f"Company logo found: {company_data['logo']}")
            else:
                current_app.logger.warning("No logo found in company data")

            # Use the PDFGenerator class but with a modified version that doesn't use number_to_words
            prepared_by = f"{first_name.upper()} {last_name.upper()}"
            pdf_buffer = PDFGenerator.generate_payslip_pdf(
                filtered_employee,
                company_data,
                pay_date,
                formatted_first_day,
                formatted_last_day,
                days_in_month,
                prepared_by
            )
            current_app.logger.info("Comprehensive PDF generated successfully")
        except Exception as e:
            current_app.logger.error(f"Error generating comprehensive PDF: {e}")
            # Create a simple fallback PDF
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib.units import inch
            from io import BytesIO

            pdf_buffer = BytesIO()
            doc = SimpleDocTemplate(pdf_buffer, pagesize=A4)
            styles = getSampleStyleSheet()

            elements = []
            elements.append(Paragraph("PAYSLIP", styles['Title']))
            elements.append(Spacer(1, 0.2*inch))
            elements.append(Paragraph(f"Company: {company_data.get('company_name', '')}", styles['Normal']))
            elements.append(Paragraph(f"Employee: {filtered_employee['employee'].get('first_name', '')} {filtered_employee['employee'].get('last_name', '')}", styles['Normal']))
            elements.append(Paragraph(f"Period: {formatted_first_day} to {formatted_last_day}", styles['Normal']))

            doc.build(elements)
            pdf_buffer.seek(0)

        # Create a custom filename with employee name and pay date
        employee_first_name = filtered_employee['employee'].get('first_name', '').replace(' ', '_')
        employee_last_name = filtered_employee['employee'].get('last_name', '').replace(' ', '_')
        month_year = pay_date.strftime('%B_%Y')  # Format: January_2025

        # Create a sanitized filename
        custom_filename = f"Payslip_{employee_first_name}_{employee_last_name}_{month_year}.pdf"
        custom_filename = ''.join(c for c in custom_filename if c.isalnum() or c in ['_', '-', '.'])

        current_app.logger.info(f"Generated custom filename: {custom_filename}")

        # Save PDF to a temporary file with the custom filename
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, custom_filename)

        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(pdf_buffer.read())

        # Prepare email content
        subject = f" {employee_first_name} {employee_last_name} Payslip for {pay_date.strftime('%B %Y')}"
        body = f"""
            Dear {filtered_employee['employee'].get('first_name', '')},

            Please find attached your payslip for {pay_date.strftime('%B %Y')}.

            If you have any questions regarding your payslip, please contact the HR department.

            Best regards,
            {company_data.get('company_name', '')} HR Team
        """

        # Send email with attachment
        try:
            Auxillary.send_netpipo_email_attachment(
                subject=subject,
                recipient=employee_email,
                body=body,
                attachment_path=temp_file_path
            )

            # Delete temporary file
            os.unlink(temp_file_path)
            current_app.logger.info(f"Payslip sent to {employee_email} successfully.")

            return jsonify(success=True, message=f"Payslip sent to {employee_email} successfully."), 200
        except Exception as e:
            # Delete temporary file in case of error
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

            current_app.logger.error(f"An error occurred while sending email: {e}")
            return jsonify(success=False, error="An error occurred while sending the payslip email."), 500
    except Exception as e:
        current_app.logger.error(f"An error occurred while generating payslip PDF: {e}")
        return jsonify(success=False, error="An error occurred while generating the payslip PDF."), 500


@employees_api_bp.route('/generate_payslips_from_approved_payroll', methods=['POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def generate_payslips_from_approved_payroll():
    """Generate payslips from approved payroll data.

    This route allows HR to generate payslips for all employees with approved payroll
    for a specific month and year. It can also send the payslips to employees via email.
    """
    jwt_data = get_jwt()
    database_name = jwt_data.get('database_name')

    # Get current month and year as default values
    current_date = datetime.now()
    default_month = current_date.month
    default_year = current_date.year

    # Get company data
    company_data = {}
    try:
        company_id = jwt_data.get('company_id')
        company = Company.get_company_by_id(company_id)
        if company:
            company_data = company
    except Exception as e:
        current_app.logger.error(f"Error fetching company data: {e}")

    # Get form data
    month = int(request.form.get('month', default_month))
    year = int(request.form.get('year', default_year))
    send_email = request.form.get('send_email') == 'on'

    current_app.logger.info(f"Month: {month}, Year: {year}, Send Email: {send_email}")

    # Validate month and year
    if month < 1 or month > 12:
        return jsonify(success=False, error="Invalid month selected."), 400

    if year < 2000 or year > 2100:
        return jsonify(success=False, error="Invalid year selected."), 400

    # Get approved payroll data for the selected month and year
    try:
        with db_connection.get_session(database_name) as db_session:
            # Get all payrolls for the selected month and year with status 'Approved'
            payrolls = db_session.query(Payroll).filter(
                Payroll.pay_date >= datetime(year, month, 1),
                Payroll.pay_date <= datetime(year, month, calendar.monthrange(year, month)[1]),
                Payroll.status == 'Approved'
            ).all()
            current_app.logger.info(f"Payrolls: {payrolls}")
            if not payrolls:
                return jsonify(success=False, error=f"No approved payroll data found for {calendar.month_name[month]} {year}."), 404

            # Process each payroll record
            success_count = 0
            error_count = 0
            email_success_count = 0
            email_error_count = 0

            for payroll in payrolls:
                try:
                    # Get employee data
                    employee = db_session.query(Employee).filter(Employee.employee_id == payroll.employee_id).first()

                    if not employee:
                        current_app.logger.error(f"Employee not found for payroll ID: {payroll.payroll_id}")
                        error_count += 1
                        continue

                    # Prepare data for PDF generation
                    pay_date = payroll.pay_date
                    first_day = datetime(year, month, 1)
                    last_day = datetime(year, month, calendar.monthrange(year, month)[1])
                    formatted_first_day = first_day.strftime('%d/%m/%Y')
                    formatted_last_day = last_day.strftime('%d/%m/%Y')
                    days_in_month = calendar.monthrange(year, month)[1]

                    # Create employee data dictionary similar to what's used in single_employee_payslip
                    employee_data = {
                        'employee': employee.to_dict(),
                        'basic_needed': payroll.basic_salary,
                        'gross_needed': payroll.gross_salary,
                        'paye': payroll.payee,
                        'pension_ee_value': payroll.employee_pension,
                        'pension_er_value': payroll.employer_pension,
                        'maternity_ee_value': payroll.employee_maternity,
                        'maternity_er_value': payroll.employer_maternity,
                        'rama_ee': payroll.medical_fee,
                        'cbhi_value': payroll.cbhi,
                        'total_deductions_value': payroll.total_deductions,
                        'net_salary_value': payroll.net_salary,
                        'total_reimbursements': payroll.reimbursement or 0,
                        'total_deductions': payroll.other_deductions or 0,
                        'brd_deduction': payroll.brd_deductions or 0,
                        'salary_advance': payroll.advance or 0
                    }
                    current_app.logger.info(f"Employee data: {employee_data}")

                    # Generate PDF
                    prepared_by = f"{current_user.get('first_name')} {current_user.get('last_name')}"
                    pdf_buffer = PDFGenerator.generate_payslip_pdf(
                        employee_data,
                        company_data,
                        pay_date,
                        formatted_first_day,
                        formatted_last_day,
                        days_in_month,
                        prepared_by
                    )

                    # Create a custom filename with employee name and pay date
                    employee_first_name = employee.first_name.replace(' ', '_')
                    employee_last_name = employee.last_name.replace(' ', '_')
                    month_year = pay_date.strftime('%B_%Y')

                    # Create a sanitized filename
                    custom_filename = f"Payslip_{employee_first_name}_{employee_last_name}_{month_year}.pdf"
                    custom_filename = ''.join(c for c in custom_filename if c.isalnum() or c in ['_', '-', '.'])

                    # Save PDF to a temporary file with the custom filename
                    temp_dir = tempfile.gettempdir()
                    temp_file_path = os.path.join(temp_dir, custom_filename)

                    with open(temp_file_path, 'wb') as temp_file:
                        temp_file.write(pdf_buffer.read())

                    success_count += 1

                    # Send email if requested and employee has an email address
                    if not send_email:
                        # Delete temporary file if not sending email
                        if os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)
                    if not employee.email:
                        email_error_count += 1
                        current_app.logger.warning(f"Employee {employee.first_name} {employee.last_name} does not have an email address. Skipping email.")

                    try:
                        # Prepare email content
                        subject = f"{employee_first_name} {employee_last_name} Payslip for {pay_date.strftime('%B %Y')}"
                        body = f"""
                        <p>Dear {employee.first_name},</p>

                            <p>Please find attached your payslip for <strong>{pay_date.strftime('%B %Y')}</strong>.</p>

                            <p>This payslip contains details of your salary, allowances, deductions, and net pay for the period from <strong>{formatted_first_day}</strong> to <strong>{formatted_last_day}</strong>.</p>

                            <p>If you have any questions regarding your payslip, please contact the HR department.</p>

                            <p>Best regards,<br>
                            <strong>{company_data.get('company_name', '')}</strong> HR Team</p>
                        """

                        # Send email with attachment
                        Auxillary.send_netpipo_email_attachment(
                            subject=subject,
                            recipient=employee.email,
                            body=body,
                            attachment_path=temp_file_path
                        )

                        email_success_count += 1
                        current_app.logger.info(f"Payslip sent to {employee.email} successfully.")
                    except Exception as e:
                        email_error_count += 1
                        current_app.logger.error(f"Error sending email to {employee.email}: {e}")

                    # Delete temporary file after sending email
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                except Exception as e:
                    error_count += 1
                    current_app.logger.error(f"Error generating payslip for employee {payroll.employee_name}: {e}")
                    traceback.print_exc()

            # Show summary message
            if success_count > 0 or email_success_count > 0:
                return jsonify(success=True, message=f"Successfully generated {success_count or email_success_count} payslips."), 200

            if error_count > 0 or email_error_count > 0:
                return jsonify(success=False, error=f"Failed to generate {error_count or email_error_count} payslips."), 500
    except Exception as e:
        current_app.logger.error(f"Error processing payroll data: {e}")
        traceback.print_exc()
        return jsonify(success=False, error="An error occurred while processing payroll data."), 500


# This endpoint will also be usd for bulk update
@employees_api_bp.route('/search_employee', methods=['GET'])
@role_required(['hr', 'supervisor', 'accountant', 'manager', 'company_hr'])
def search_employee():
    try:
        param = request.args.get('search-param')        
        company_id = get_jwt().get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")
        
        if not company_id:
            current_app.logger.error("Company ID is missing in session")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500

        # Get the company database name
        database_name = CompanyHelpers.get_company_database_name(company_id)
        current_app.logger.info(f"Database name: {database_name}")
        if not database_name:
            current_app.logger.error("Database name could not be retrieved")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        
        with db_connection.get_session(database_name) as db_session:
            # Get different list based on whether param is passed or not
            if param:
                employees = Employee.search_employee(db_session, param)
            else:
                employees = Employee.get_employees(db_session)

            leave_records = Attendance.get_leave_records(db_session)
            leave_records_map = {}
            for record in leave_records:
                employee_id = str(record["employee_id"])
                if employee_id not in leave_records_map:
                    leave_records_map[employee_id] = []
                leave_records_map[employee_id].append({"time_off_end_date": record["time_off_end_date"],
                                                        "time_off_begin_date":record["time_off_begin_date"]}) 
            for emp in employees:
                employee_id = str(emp["employee_id"])
                matching_leave_record = leave_records_map.get(employee_id)  # O(1) lookup
                if matching_leave_record:
                    emp["additionals"]=matching_leave_record
                else:
                    continue
            current_app.logger.info(f"Employees: {employees}")
            return jsonify(success=True, employees=employees, message="Employees retrieved successfully")
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    

@employees_api_bp.route('/delete_employee/<uuid:employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    database_name = get_jwt().get('database_name')
    try:
        with db_connection.get_session(database_name) as db_session:
            _ = Employee.delete_employee(db_session, employee_id) # return value is not used anywhere
            return jsonify(success=True, message="Employee deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500