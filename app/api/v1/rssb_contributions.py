from app.models.rssb import NsfContributions
from flask import current_app, Blueprint, request, jsonify

rssb_contribution_bp = Blueprint('rssb_contribution', __name__)

@rssb_contribution_bp.route('/api/v1/insert_rssb_contribution', methods=['POST'])
def insert_rssb_contribution():
    """Insert rssb contributions into the database.."""
    data = request.get_json()
    contribution_name = data.get('contribution_name')
    employee_rate = data.get('employee_rate')
    employer_rate = data.get('employer_rate')
    try:
        nsf_contribution = NsfContributions(
            contribution_name=contribution_name,
            employee_rate=employee_rate, 
            employer_rate=employer_rate
            )
        print("nsf_contribution", nsf_contribution)
    except Exception as e:
        print("Error creating NSF contribution object: ", e)
        return jsonify({"message": "Error creating NSF contribution object."}), 500
    try:
        if nsf_contribution.insert_nsf_contribution():
            return jsonify({"message": "NSF contribution inserted successfully."}), 201
    except Exception as e:
        return jsonify({"message": "Error inserting NSF contribution."}), 500
    
@rssb_contribution_bp.route('/api/v1/get_rssb_contributions', methods=['GET'])
def get_rssb_contributions():
    """Get all rssb contributions from the database.
    Returns:
        list: A list of all rssb contributions.
    """
    try:
        contributions = NsfContributions().get_contributions()
        print(contributions)
        print()
        for contribution in contributions:
            print(f"""
                  Contribution Id: {contribution['nsf_id']}, 
                  contribution_name: {contribution['contribution_name']},
                  employee_rate: {contribution['employee_rate']},
                  employer_rate: {contribution['employer_rate']}
                  """
                  )
                
        return jsonify(contributions), 200
    except Exception as e:
        message = f"""Error getting NSF contributions: {str(e)}"""
        return jsonify(message), 500
