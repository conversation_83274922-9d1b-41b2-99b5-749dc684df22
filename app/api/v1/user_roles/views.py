from flask import Blueprint, jsonify, current_app, request
from app.models.central import UserRole
from app.api.v1.decorators.auth_decorators import role_required

user_role_api_bp = Blueprint('user_role', __name__)


@user_role_api_bp.route('/user_roles', methods=['GET'])
@role_required('admin')
def get_user_roles():
    """Fetch all user roles."""
    try:
        user_roles = UserRole.get_user_roles()
        current_app.logger.info("Fetched user roles successfully.")
        return jsonify(success=True, data=user_roles, message="User roles retrieved successfully."), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching user roles: {str(e)}")
        return jsonify(success=False, error="Error fetching user roles."), 500

@user_role_api_bp.route('/user_role/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_user_role(id):
    """Fetch a specific user role by ID."""
    try:
        user_role = UserRole.get_user_role_by_id(id)
        return jsonify(success=True, data=user_role, message="User role retrieved successfully."), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching user role with ID {id}: {str(e)}")
        return jsonify(success=False, error="Error fetching user role."), 500

@user_role_api_bp.route('/insert_user_role', methods=['POST'])
@role_required('admin')
def insert_user_role():
    """Insert a new user role."""
    data = request.get_json()
    role_name = data.get('role_name', '').strip().lower()
    
    if not role_name:
        return jsonify(success=False, error="Role name is required."), 400
    
    try:
        user_role = UserRole(role_name=role_name)
        is_inserted = user_role.insert_user_role()
        if is_inserted:
            current_app.logger.info("User role inserted successfully.")
            return jsonify(success=True, message="User role inserted successfully."), 201
        else:
            return jsonify(success=False, error="Failed to insert user role."), 500
    except Exception as e:
        current_app.logger.error(f"Error inserting user role: {str(e)}")
        return jsonify(success=False, error="Error inserting user role."), 500

@user_role_api_bp.route('/update_user_role/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_user_role(id):
    data = request.get_json()
    role_name = data.get('role_name', '').strip().lower()

    if not role_name:
        return jsonify(success=False, error="Role name is required."), 400

    try:
        user_role = UserRole.get_user_role_by_id(id)
        if not user_role:
            return jsonify(success=False, error="User role not found."), 404

        is_updated = UserRole.update_user_role(id, role_name)
        if is_updated:
            current_app.logger.info("User role updated successfully.")
            return jsonify(success=True, message="User role updated successfully."), 200
        else:
            return jsonify(success=False, error="Failed to update user role."), 500
    except Exception as e:
        current_app.logger.error(f"Error updating user role: {str(e)}")
        return jsonify(success=False, error="Error updating user role."), 500

@user_role_api_bp.route('/delete_user_role/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_user_role(id):
    """Delete a user role."""
    try:
        user_role = UserRole.get_user_role_by_id(id)
        if not user_role:
            return jsonify(success=False, error="User role not found."), 404

        is_deleted = UserRole.delete_user_role(id)
        if is_deleted:
            current_app.logger.info("User role deleted successfully.")
            return jsonify(success=True, message="User role deleted successfully."), 200
        else:
            return jsonify(success=False, error="Failed to delete user role."), 500
    except Exception as e:
        current_app.logger.error(f"Error deleting user role: {str(e)}")
        return jsonify(success=False, error="Error deleting user role."), 500