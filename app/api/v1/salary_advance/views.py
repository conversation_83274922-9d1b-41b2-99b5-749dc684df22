from flask import Blueprint, jsonify, current_app, request
from flask_jwt_extended import get_jwt, current_user
from app.api.v1.decorators.auth_decorators import role_required
from app.models.company import SalaryAdvanceRequest
from app.models.company_salary_advance import SalaryAdvanceApproval
from app.utils.db_connection import DatabaseConnection
from app.helpers.auxillary import Auxillary
from app.api_helpers.ApiHelpers import UserInputValidator

advance_api_bp = Blueprint('advance_req', __name__)
db_connection = DatabaseConnection()

@advance_api_bp.route('/view_advance_requests', methods=['GET'])
@role_required(['employee', 'hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def view_advance_requests():
    jwt_data = get_jwt()
    employee_id = current_user.get("employee_id")
    database_name = jwt_data.get('database_name')

    current_app.logger.info(f"Database name: {database_name}")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get the employee's salary advance requests
        try:
            response = []
            if current_user.get('role') == 'employee':
                response = SalaryAdvanceRequest.get_salary_advance_request_for_employee(db_session, employee_id)
            else:
                salary_advance_requests = SalaryAdvanceRequest.get_salary_advance_requests(db_session)
                # Group the requests by employee_id
                grouped_requests = {}
                for request in salary_advance_requests:
                    employee_id = str(request.get('employee_id'))
                    if employee_id not in grouped_requests:
                        grouped_requests[employee_id] = {
                            'full_name': request.get('full_name', 'Unknown'),
                            'requests': []
                        }
                    grouped_requests[employee_id]['requests'].append(request)
                response = grouped_requests
            current_app.logger.info(f"Advance requests: {response}")
            
            return jsonify(success=True, 
                            data=response, 
                            message='Salary advance requests retrieved successfully!'), 200
        except Exception as e:
            current_app.logger.exception(f"error retrieving salary advances: {str(e)}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    

@advance_api_bp.route('/view_advance_approvals', methods=['GET'])
@role_required(['hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def view_advance_approvals():
    """View all salary advance approvals."""
    jwt_data = get_jwt()
    # Get database name from session
    database_name = jwt_data.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        # Get all salary advance approvals
        salary_advance_approvals = SalaryAdvanceApproval.get_approvals(db_session)

        # Prepare structured data for rendering
        grouped_approvals = []
        for approval in salary_advance_approvals:
            employee_id = approval.get('employee_id')
            request_id = approval.get('request_id')

            # Find or create entry for employee
            employee_entry = next(
                (entry for entry in grouped_approvals if entry['employee_id'] == employee_id),
                None
            )
            if not employee_entry:
                employee_entry = {
                    'employee_id': employee_id,
                    'full_name': approval.get('employee_name', 'Unknown'),
                    'requests': []
                }
                grouped_approvals.append(employee_entry)

            # Find or create entry for request_id
            request_entry = next(
                (req for req in employee_entry['requests'] if req['request_id'] == request_id),
                None
            )
            if not request_entry:
                request_entry = {
                    'request_id': request_id,
                    'amount': approval.get('amount'),
                    'approvals': []
                }
                employee_entry['requests'].append(request_entry)

            # Add approval details
            request_entry['approvals'].append({
                'approver_id': approval.get('approver_id'),
                'status': approval.get('status'),
                'created_at': approval.get('created_at'),
                'remarks': approval.get('remarks'),
                'approver_role': approval.get('approver_role')

            })

        # Add rowspan values for each employee and request
        for employee in grouped_approvals:
            employee['rowspan'] = sum(len(req['approvals']) for req in employee['requests'])
            for request in employee['requests']:
                request['rowspan'] = len(request['approvals'])
        current_app.logger.info(f"Grouped approvals: {grouped_approvals}")
        return jsonify(success=False, data=grouped_approvals, message='Salary advance approvals retrieved successfully!')
    
# ========================================================================================
@advance_api_bp.route('/apply_for_advance', methods=['POST'])
@role_required('employee')
def apply_for_advance():
    jwt_data = get_jwt()
    employee_id = current_user.get('employee_id')
    data = request.get_json()
    total_installments = len([k for k in data.keys() if k.startswith('installment_amounts')])
    total_due_dates = len([date for date in data.keys() if date.startswith('due_dates')])
   
    if not data.get("amount") or not data.get("reason") or total_installments != total_due_dates:
        return jsonify(success=False, error="Missing required fields"), 400
    # Fetch all installment amounts and due dates
    installment_amounts = []
    due_dates = []

    # Use pattern matching to extract dynamic keys for installments and due dates
    for key in data.keys():
        if key.startswith('installment_amounts'):
            installment_amounts.append(data.get(key))
        elif key.startswith('due_dates'):
            due_dates.append(data.get(key))

    current_app.logger.info(f"Form data: {data}")
    current_app.logger.info(f"installment_amounts: {installment_amounts}, due dates: {due_dates}")
    current_app.logger.info(f"Type of installment_amounts: {type(installment_amounts)}")
    current_app.logger.info(f"Length of installments: {len(installment_amounts)}")

    # Pair installment amounts with due dates
    installments = []
    if installment_amounts and due_dates:
        for inst_amount, due_date in zip(installment_amounts, due_dates):
            installments.append({'amount': inst_amount, 'due_date': due_date})

    # Log details for debugging
    current_app.logger.info(f"Amount: {data.get('amount')}, Reason: {data.get('reason')}, Installments: {installments}")
    length = len(installments)
    current_app.logger.info(f"Length of installments: {length}")
    # Get database name from jwt_data
    database_name = jwt_data.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")

    sum_installments = 0
    # Check if the installments' sum equals the requested amount
    for i in installment_amounts:
        # add the installment to the sum
        try:
            sum_installments += int(i)
        except Exception as e:
            current_app.logger.error(f"Error during installment addition: {str(e)}")
            return jsonify(success=False, error="Please enter a valid number"), 400

    # make sure the amount is a valid number too.
    try:
        converted_amount = int(data.get("amount"))
    except Exception as e:
        current_app.logger.error(f"Error during amount conversion: {str(e)}")
        return jsonify(success=False, error="Please enter a valid number"), 400
    
    check = (sum_installments == converted_amount)
    current_app.logger.info(f"checking: {check}")
    if not check:
        return jsonify(success=False, error=f"The amount requested: {data.get('amount')} does not equall to the total installments: \
                       {sum_installments}"), 400


    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info("before adding salary advance request")
            result = SalaryAdvanceRequest.create_salary_advance_request(
                db_session, employee_id, data.get("amount"), installments, data.get("reason")
            )
            current_app.logger.info(f"Result after creating the salary advance request: {result}")
            
            if result:
                return jsonify(success=True, message="Salary advance request created successfully"), 200
            return jsonify(success=False, error="An error occurred while creating the salary advance request"), 500

        except Exception as e:
            current_app.logger.error(f"An error occurred while creating a salary advance request: {str(e)}")
            return jsonify(success=False, error="An error occurred while creating the salary advance request")


# add_advance - new route yet to test
@advance_api_bp.route('/add_advance', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_advance():
    """Add a salary advance."""
    current_app.logger.info("Adding a salary advance")
    data = request.get_json()
    jwt_data = get_jwt()
    employee_id = data.get('employee_id')
    amount = data.get('amount')
    reason = data.get('reason')
    due_dates = data.get('due_dates')
    installment_amounts = data.get('installment_amounts')
    database_name = jwt_data.get('database_name')

    if not database_name:
        current_app.logger.error("Failed to fetch company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    current_app.logger.info(f"Database name: {database_name}")

    is_valid, error = UserInputValidator.validate({
        "employee_id": employee_id, 
        "amount": amount,
        "reason": reason,
        "due_dates": due_dates,
        "installment_amounts": installment_amounts
    }, "add_advance")

    if not is_valid:
        return jsonify(success=False, error=error), 400

    print("Comparison: ", total_due_dates, total_installments)
    # Fetch all installment amounts and due dates
    total_installments = len(data.get('installment_amounts'))
    total_due_dates = len(data.get('due_dates'))
    
    if total_installments != total_due_dates:
        return jsonify(success=False, error="Bad installment structure"), 400
    
    # Check if the installments' sum equals the requested amount
    if sum([int(x) for x in installment_amounts]) != int(amount):
        return jsonify(success=False, error="The total installment amount does not equal the requested amount"), 400
    
    with db_connection.get_session(database_name) as db_session:
        try:

            installments = [{'amount': inst_amount, 'due_date': due_date} for inst_amount, due_date in zip(installment_amounts, due_dates)]
            # Create new salary advance
            new_advance = SalaryAdvanceRequest.manually_create_salary_advance_request(
                db_session, employee_id, amount, installments, reason, is_using_jwt=True
            )
            current_app.logger.info(f"New salary advance: {new_advance}")
            if not new_advance:
                return jsonify(success=False, error="An error occurred while adding the salary advance"), 500
            return jsonify(success=True, message="Salary advance added successfully"), 200
        except Exception as e:
            db_session.rollback()
            current_app.logger.error(f"Error adding salary advance: {e}")
            return jsonify(success=False, error="An error occurred while adding the salary advance"), 500


@advance_api_bp.route('/approve_advance_request/<uuid:request_id>', methods=['POST'])
@role_required(['hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def approve_advance_request(request_id):
    """Approve a salary advance request."""
    data = request.get_json()
    jwt_data = get_jwt()
    approval = data.get('approval')
    remarks = data.get('remarks')
    approver_id = current_user.get('employee_id')
    approver_role = current_user.get('role')
    database_name = jwt_data.get('database_name')

    is_valid, errors = UserInputValidator.validate({
        "approval": approval,
        "remarks": remarks,
        "approver_id": approver_id,
        "approver_role": approver_role
    }, 'leave_approval')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the salary advance request
            salary_advance_request = SalaryAdvanceRequest.get_salary_advance_request_by_id(db_session, request_id)
            if not salary_advance_request:
                return jsonify(success=False, error="Salary advance request not found"), 404

            if salary_advance_request['status'] in ['approved', 'rejected']:
                return jsonify(success=False, error="Salary advance request already approved/rejected"), 400
            # Approve or reject the salary advance request
            try:
                result = SalaryAdvanceApproval.create_approval(db_session, request_id, approver_id, approver_role, approval, remarks)
                current_app.logger.info(f"Result after approving the salary advance request: {result}")
                if result:
                    return jsonify(success=True, message=result), 200
                return jsonify(success=False, error="An error occurred while approving the salary advance request"), 400
            except Exception as e:
                current_app.logger.error(f"An error occurred while approving a salary advance request: {str(e)}")
                return jsonify(success=False, error="An error occurred while approving the salary advance request"), 400                
        except Exception as e:
            current_app.logger.error(f"An error occurred while rendering the salary advance page: {str(e)}")
            return jsonify(success=False, error="An error occurred while rendering the salary advance page"), 400
        
# ==============================================================================================
@advance_api_bp.route('/update_advance_request/<uuid:id>', methods=['PUT'])
@role_required(['employee', 'hr', 'manager', 'supervisor', 'accountant', 'company_hr'])
def update_advance_request(id):
    """Update a salary advance request."""
    jwt_data = get_jwt()
    database_name = jwt_data.get('database_name')
    current_app.logger.info(f"Database name: {database_name}")
    
    with db_connection.get_session(database_name) as db_session:
        try:
            # Fetch the salary advance request with installment details
            salary_advance_request = SalaryAdvanceRequest.get_salary_advance_request_by_id(db_session, id)
            if not salary_advance_request:
                return jsonify(success=False, error="Salary advance request not found"), 404
            # Check if the user is authorized to update this salary advance request
            if current_user.get('role') == 'employee' and salary_advance_request['employee_id'] != current_user.get('employee_id'):
                current_app.logger.info(f"User is not authorized to update this salary advance request: {current_user}")
                return jsonify(success=False, error="You are not authorized to update this salary advance request"), 403
                
            full_name = salary_advance_request['full_name']
            installment_plans = salary_advance_request['installment_plans']
            email = salary_advance_request['employee_email']
            due_dates = []

            for date in installment_plans:
                current_app.logger.info(f"Date: {date['due_date']}")
                current_app.logger.info(f"Type of date: {type(date['due_date'])}")
                due_dates.append(date['due_date'])

            current_app.logger.info(f"Salary advance request: {salary_advance_request}")
            current_app.logger.info(f"Email: {email}")
            current_app.logger.info(f"Due dates: {due_dates}")
            
            try:
                data = request.get_json()
                current_app.logger.info(f"Form data: {data}")
                
                # Extract main amount and installment details from the JS - it will be prepared by the front end
                amount = float(data.get('amount')) if data.get('amount') else None 
                installment_amounts = data.get('installment_amounts')
                due_dates = data.get('due_dates')
                current_app.logger.info(f"Amount: {amount}, Installment amounts: {installment_amounts}, Due dates: {due_dates}")
                
                # Validate data
                if not amount or len(installment_amounts) != len(due_dates):
                    return jsonify(success=False, error="Invalid data. Please check your input."), 400
                
                # get installment amounts
                summation = sum([float(inst_amount) for inst_amount in installment_amounts])
                if summation != float(amount):
                    return jsonify(success=False, error="The sum of the installment amounts must equal the requested amount."), 400

                # Pair installment amounts with due dates
                installments = [
                    {'amount': float(inst_amount), 'due_date': due_date}
                    for inst_amount, due_date in zip(installment_amounts, due_dates)
                ]
                current_app.logger.info(f"Installments: {installments}")

                # Update the salary advance request
                result = SalaryAdvanceRequest.update_salary_advance_request(db_session, id, amount, installments)
                current_app.logger.info(f"Result after updating the salary advance request: {result}")
                
                if not result:
                    return jsonify(success=False, error="An error occurred while updating the salary advance request"), 500
                
                # send an email to the employee
                try:
                    if email and current_user.get("role") == 'employee':
                        return jsonify(success=True, message="Salary advance request updated successfully"), 200
                    
                    # get the name of the user updating the request
                    approver_name = current_user.get("full_name")
                    current_app.logger.info(f"Approver name: {approver_name}")
                    subject = 'Salary Advance modified'
                    message2 = f"""
                        Dear {full_name},Your salary advance request has been modified 
                        by {approver_name} with the following details: Amount Requested: {amount},  
                        Installments: {installments}.Regards, {jwt_data.get('company_name')}"
                    """
                    sent = Auxillary.send_netpipo_email(subject, email, message2)
                    current_app.logger.info(f"Email sent: {sent}")
                    
                except Exception as e:
                    current_app.logger.error(f"An error occurred while sending an email: {str(e)}")
                    return jsonify(success=False, error="An error occurred while updating the salary advance request"), 500
            except Exception as e:
                current_app.logger.exception(f"Error updating request: {e}")
                return jsonify(success=False, error="An error occurred. Please try again."), 500
                
        except Exception as e:
            current_app.logger.error(f"Error updating request: {e}")
            return jsonify(success=False, error="An error occurred. Please try again."), 500

# ====================================================================================================

@advance_api_bp.route('/delete_advance_request/<uuid:id>', methods=['DELETE'])
@role_required('employee')
def delete_advance_request(id):
    """Delete a salary advance request."""
    jwt_data = get_jwt()    
    database_name = jwt_data.get('database_name')

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Get the salary advance request
            salary_advance_request = SalaryAdvanceRequest.get_salary_advance_request_by_id(db_session, id)
            if not salary_advance_request:
                return jsonify(success=False, error="Salary advance request not found"), 404
            
            if salary_advance_request['employee_id'] != current_user.get('employee_id'):
                return jsonify(success=False, error="You are not authorized to delete this salary advance request"), 403
            
            # Check if the request has been approved
            if salary_advance_request['status'] == 'approved':
                return jsonify(success=False, error="You cannot delete an approved salary advance request"), 400

            # Delete the salary advance request
            result = SalaryAdvanceRequest.delete_salary_advance_request(db_session, id)
            current_app.logger.info(f"Result after deleting the salary advance request: {result}")
            if not result:
                return jsonify(success=False, error="An error occurred while deleting the salary advance request"), 500
            return jsonify(success=True, message="Salary advance request deleted successfully"), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while deleting the salary advance request: {str(e)}")
            return jsonify(success=False, error="An error occurred while deleting the salary advance request"), 500