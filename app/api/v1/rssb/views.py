from flask import Blueprint, jsonify, current_app, request
from flask_jwt_extended import get_jwt
from app.models.central import NsfContributions
from app.models.company import Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection 
from app.helpers.rssb_contributions import RssbContributionHelpers
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator

rssb_api_bp = Blueprint('rssb', __name__)


@rssb_api_bp.route('/add_rssb_contributions', methods=['POST'])
@role_required('admin')
def add_rssb_contributions():
    """Add RSSB contributions."""
    data = request.get_json()
    contribution_name = data.get("contribution_name")
    employee_rate = data.get("employee_rate")
    employer_rate = data.get("employer_rate")
    start_date = data.get("start_date")
    end_date = data.get("end_date")

    is_valid, errors = UserInputValidator.validate({
        "contribution_name": contribution_name,
        "employee_rate": employee_rate,
        "employer_rate": employer_rate,
        "start_date": start_date,
        "end_date": end_date
    }, 'add_rssb_contributions')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    employee_rate = float(employee_rate) / 100
    employer_rate = float(employer_rate) / 100

    try:
        nsf_contribution = NsfContributions(
            contribution_name=contribution_name,
            employee_rate=employee_rate, 
            employer_rate=employer_rate,
            start_date=start_date,
            end_date=end_date
        )

        is_created = nsf_contribution.insert_nsf_contribution()
        if not is_created:
            return jsonify(success=False, error="Error creating NSF contribution object."), 500
        
        return jsonify(success=True, data=nsf_contribution.to_dict(), message='NSF contribution created successfully'), 200
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error="Error creating NSF contribution object."), 500
    

@rssb_api_bp.route('/update_rssb_contributions/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_rssb_contributions(id):
    """Update RSSB contributions."""
    data = request.get_json()
    contribution_name = data.get("contribution_name")
    employee_rate = data.get("employee_rate")
    employer_rate = data.get("employer_rate")
    start_date = data.get("start_date")
    end_date = data.get("end_date")

    is_valid, errors = UserInputValidator.validate({
        "contribution_name": contribution_name,
        "employee_rate": employee_rate,
        "employer_rate": employer_rate,
        "start_date": start_date,
        "end_date": end_date
    }, 'add_rssb_contributions')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    employee_rate = float(employee_rate) / 100
    employer_rate = float(employer_rate) / 100

    try:
        nsf_contribution:NsfContributions = NsfContributions.query.get_or_404(id)
        if not nsf_contribution:
            return jsonify(success=False, error="NSF contribution not found."), 404
        
        is_updated = nsf_contribution.update_nsf_contribution(id, contribution_name, employee_rate, employer_rate, start_date, end_date)
        if not is_updated:
            return jsonify(success=False, error="Error updating NSF contribution."), 500
        
        return jsonify(success=True, data=nsf_contribution.to_dict(), message='NSF contribution updated successfully'), 200
    except Exception as e:
        current_app.logger.error(f"Error: {e}")
        return jsonify(success=False, error="Error updating NSF contribution."), 500
    

@rssb_api_bp.route('/delete_rssb_contributions/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_rssb_contributions(id):
    """Delete RSSB contributions."""
    nsf_contribution = NsfContributions.query.get_or_404(id)
    if not nsf_contribution:
        return jsonify(success=False, error="NSF contribution not found."), 404
    
    try:
        is_deleted = nsf_contribution.delete_nsf_contribution(id)
        if is_deleted:
            return jsonify(success=True, message='NSF contribution deleted successfully'), 200
        
        return jsonify(success=False, error="Error deleting NSF contribution."), 500
    except Exception as e:
        current_app.logger.exception(f"Error Deleting RSSB Contribution: {e}")
        return jsonify(success=False, error="Error deleting NSF contribution."), 500