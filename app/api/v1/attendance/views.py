import json
import os
import requests
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt, current_user
from app.models.company import Attendance, Employee
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.api.v1.decorators.auth_decorators import role_required

attendance_api_bp = Blueprint('attendance', __name__)


@attendance_api_bp.route('/clockin', methods=['POST'])
@role_required('employee')
def api_clockin():
    """API endpoint for clock in an employee using facial recognition."""
    try:
        jwt_data = get_jwt()
        # Fall back to session if no session data in request
        company_id = jwt_data.get('company_id')
        database_name = jwt_data.get('database_name')
        employee_id = current_user.employee_id
        user_id = str(current_user.user_id)

        # Get the API KEY
        MICROSERVICE_KEY = CompanyHelpers.get_company_compreface_api_key()
        if MICROSERVICE_KEY is None:
            error="You are not registered to use this service. Please contact <NAME_EMAIL> to be registered."
            current_app.logger.error(f"API Clockin - Error: {error}")
            return jsonify(succes=False, error=error), 401

        current_app.logger.info(f"API Clockin - Using session data: Company ID: {company_id}, Database: {database_name}, Employee ID: {employee_id}")
        current_app.logger.info(f"API Clockin - MICROSERVICE_KEY: {MICROSERVICE_KEY}")

        # Process the uploaded image
        if ('image' not in request.files) or (request.files['image'] and request.files['image'].filename == ''):
            current_app.logger.error("API Clockin - No image part in the request")
            return jsonify(success=False, error="No image selected"), 400

        # Grab the image from request
        image_file = request.files['image']

        # Get location data
        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        location = f"{latitude}, {longitude}"
        current_app.logger.info(f"API Clockin - Location: {location}")

        # Get device info
        device_used = request.form.get('device_used')
        if device_used is None:
            device_used = "Web Browser"
            current_app.logger.warning(f"API Clockin - device_used was None, setting default value: {device_used}")

        # Connect to the database
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            # Protect user to send request when he/she already clockedin
            user = Employee.get_employee_by_email(db_session, current_app['email'])
            user_attendance = user.attendance
            if user_attendance.time_out == None and user_attendance.work_status == None and user_attendance.is_void == False:
                current_app.logger.error(f"API Clockin - Error: You have already clocked in")
                return jsonify(success=False, error="You have already clocked in"), 400
            
            # Verify the employee's face
            BASE_URL = os.getenv('COMPREFACE_URL')
            recognition_url = f"{BASE_URL}/api/v1/recognition/recognize"

            headers = {
                'x-api-key': MICROSERVICE_KEY
            }

            try:
                # Convert the image to bytes
                image_bytes = image_file.read()

                # Create a multipart form data payload
                files = {
                    'file': ('image.jpg', image_bytes, 'image/jpeg')
                }

                # Make the API request
                response = requests.post(recognition_url, headers=headers, files=files)
                response.raise_for_status()

                # Parse the response
                result = response.json()
                current_app.logger.info(f"API Clockin - Recognition result: {result}")

                # Check if any faces were detected
                if ('result' in result and len(result['result']) < 0) or ('result' not in result):
                    current_app.logger.error(f"API Clockin - No faces detected in the image")
                    return jsonify(success=False, error="No faces detected in the image"), 400
                
                # Get the first face with the highest similarity
                faces = result['result']
                if len(faces) < 0:
                    current_app.logger.error(f"API Clockin - No faces detected in the image")
                    return jsonify(success=False, error="No faces detected in the image"), 400
                
                # Get returned face
                face = faces[0]
                subjects = face.get('subjects', [])

                if len(subjects) < 0:
                    current_app.logger.error(f"API Clockin - No matching subjects found")
                    return jsonify(success=False, error="No matching subjects found"), 400
                
                # Get the subject with the highest similarity
                subject = max(subjects, key=lambda x: x.get('similarity', 0))
                similarity = subject.get('similarity', 0)
                subject_id = subject.get('subject', '')

                current_app.logger.info(f"API Clockin - Subject ID: {subject_id}, Similarity: {similarity}")

                # Check if the similarity is above the threshold
                if similarity <= 0.85:  # 85% similarity threshold
                    current_app.logger.error(f"API Clockin - Face similarity too low: {similarity}")
                    return jsonify(success=False, error=f"Face similarity too low: {similarity}"), 400
                
                # Get the employee ID from the subject ID
                employee_id = subject_id

                # Check if the employee is allowed to clock in at this location
                location_name = "Unknown"
                try:
                    # Reverse geocode the location
                    location_name = Attendance.reverse_geocode(latitude, longitude)
                    current_app.logger.info(f"API Clockin - Location name: {location_name}")
                except Exception as e:
                    current_app.logger.error(f"API Clockin - Error reverse geocoding: {e}")

                # Record the attendance
                try:
                    # Convert the location as string
                    location = str(location)
                    # Log all parameters for debugging
                    current_app.logger.info(f"API Clockin - Parameters: employee_id={employee_id}, location={location}, device_used={device_used}, location_name={location_name}")

                    result = Attendance.clockin(db_session, employee_id, location, device_used, location_name)
                    current_app.logger.info(f"API Clockin - Attendance recorded: {result}")

                    return jsonify(success=True, message=result), 200
                except Exception as e:
                    import traceback
                    current_app.logger.error(f"API Clockin - Error recording attendance: {e}")
                    current_app.logger.error(traceback.format_exc())
                    return jsonify({"error": f"Error recording attendance: {str(e)}"}), 500
            except requests.exceptions.RequestException as e:
                current_app.logger.error(f"API Clockin - Error making recognition request: {e}")
                return jsonify({"error": f"Error making recognition request: {str(e)}"}), 500
    except Exception as e:
        import traceback
        current_app.logger.error(f"API Clockin - Unexpected error: {e}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": f"Unexpected error: {str(e)}"}), 500