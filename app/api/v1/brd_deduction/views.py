from flask import Blueprint, request, jsonify, current_app
from app.models.central import BrdDeductions
from app.api.v1.decorators.auth_decorators import role_required

brd_deductions_api_bp = Blueprint('brd_deductions', __name__)


@brd_deductions_api_bp.route('/brd_deductions', methods=['GET'])
@role_required('admin')
def get_brd_deductions():
    """Get the BRD deductions."""
    try:
        brd_rates = BrdDeductions.query.all()
        current_app.logger.info(f"BRD deductions: {brd_rates}")
        
        return jsonify(success=True, data=[b.to_dict() for b in brd_rates], message='BRD deductions retrieved successfully'), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, error='An error occurred'), 500


@brd_deductions_api_bp.route('/add_brd_deductions', methods=['POST'])
@role_required('admin')
def add_brd_deductions():
    deduction_rate = request.get_json().get('deduction_rate')
    # convert the deduction rate a percentage
    deduction_rate = float(deduction_rate) / 100

    # Strict one allowed BRD deduction
    brd_rates = BrdDeductions.get_brd_deductions()
    if brd_rates:
        return jsonify(success=False, error='BRD deductions already exist'), 400
    
    current_app.logger.info(f"BRD deductions: {brd_rates}")
    try:
        is_added = BrdDeductions.add_brd_deduction(deduction_rate)
        if is_added:
            current_app.logger.info("BRD deduction added successfully")
            return jsonify(success=True, message='BRD deduction added successfully'), 200
        else:
            current_app.logger.error("An error occurred while adding the BRD deduction")
            return jsonify(success=False, error='An error occurred while adding the BRD deduction'), 500
    except Exception as e:
        current_app.looger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, error='An error occurred'), 500


@brd_deductions_api_bp.route('/update_brd_rate/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_brd_rate(id):
    """Update the Banque Rwandaise de Development rate."""
    deduction_rate = request.get_json().get('deduction_rate')
    # convert the deduction rate a percentage
    deduction_rate = float(deduction_rate) / 100
    brd_rate = BrdDeductions.query.get_or_404(id)
    current_app.logger.info(f"BRD rate: {brd_rate}")

    if not brd_rate:
        return jsonify(success=False, error='BRD rate not found'), 404

    try:
        result = BrdDeductions.update_brd_deduction(id, deduction_rate)
        if result:
            current_app.logger.info("BRD rate updated successfully")
            return jsonify(success=True, message='BRD rate updated successfully'), 200
        else:
            current_app.logger.error("An error occurred while updating the BRD rate")
            return jsonify(success=False, error='An error occurred while updating the BRD rate'), 500
        
    except Exception as e:
        current_app.logger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, error='An error occurred'), 500


@brd_deductions_api_bp.route('/delete_brd_rate/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_brd_rate(id):
    """Delete a BRD rate."""
    try:
        exists = BrdDeductions.query.get_or_404(id)
        if not exists:
            return jsonify(success=False, error='BRD rate not found'), 404
        
        result = BrdDeductions.delete_brd_deduction(id)
        if result:
            current_app.logger.info("BRD rate deleted successfully")
            return jsonify(success=True, message='BRD rate deleted successfully'), 200
        else:
            current_app.logger.error("An error occurred while deleting the BRD rate")
            return jsonify(success=False, error='An error occurred while deleting the BRD rate'), 500
    except Exception as e:
        current_app.logger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, error='An error occurred'), 500