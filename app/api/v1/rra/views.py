from flask import Blueprint, jsonify
from flask_jwt_extended import get_jwt
from app.models.central import NsfContributions, TaxBracket
from app.models.company import Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection 
from app.api.v1.decorators.auth_decorators import role_required


rra_api_bp = Blueprint('rra', __name__)

@rra_api_bp.route('/get_rra', methods=['GET'])
@role_required('hr')
def get_rra():
    jwt_data = get_jwt()
    rssb_contributions = NsfContributions.query.all()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get("database_name")
    print("rssb_contributions", rssb_contributions)
    
    if not company_id:
        return jsonify(success=False, error='An error occurred. Please try again later or re-login.'), 400

    if not database_name:
        return jsonify(success=False, error='An error occurred. Please try again later or re-login.'), 400
    
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        if not employees:
            return jsonify(success=False, error="No employees found"), 404
        
    try:
        # Calculate contributions and PAYE
        contributions_data = calculate_employee_contributions(employees, rssb_contributions)
        return jsonify(success=True, data=contributions_data, message="Contributions and PAYE calculated successfully"), 200
    except Exception as e:
        print(f"Error: {e}", "error")
        return jsonify(success=False, error="An error occurred. Please try again later"), 500

def calculate_paye(gross_salary):
    """Calculate Pay As You Earn (PAYE) for a given gross salary based on tax brackets."""
    tax_brackets = TaxBracket.get_taxbrackets()
    tax_brackets = sorted(tax_brackets, key=lambda x: x['lower_bound'])  # Ensure tax brackets are sorted

    tax = 0.0
    for bracket in tax_brackets:
        # ERROR - ON MY SIDE, GROSS SALARY IS NONE - THEN ERROR IS HERE OF NONETYPE - DECIMAL
        print("CALCULATING PAYE DEBUGGING", type(gross_salary), type(bracket['lower_bound']))
        if gross_salary:
            if gross_salary > bracket['lower_bound']:
                taxable_income = min(gross_salary, bracket['upper_bound']) - bracket['lower_bound']
                tax += round(taxable_income * bracket['rate'])
            else:
                break

    return tax
def get_initial_contributions(employee, paye_amount):
    return {
        'employee': employee,
        'paye': paye_amount,
        'contributions': []
    }

def process_contributions(employee, rssb_contributions, employee_contributions):
    employee_salary = employee['gross_salary']
    employee_transport_allowance = employee['transport_allowance']
    paye_amount = employee_contributions['paye']

    # Temporary storage for special calculation
    pension_employee_amount = 0
    maternity_employee_amount = 0
    community_health_insurance_contribution = None

    for rssb_contribution in rssb_contributions:
        print("Processing RSSB contribution:", rssb_contribution)

        contribution_name = rssb_contribution.contribution_name
        print("contribution_name:", contribution_name)

        if contribution_name == 'community based health insurance':
            # Store the special case contribution for later calculation
            community_health_insurance_contribution = rssb_contribution
        else:
            # Calculate and add regular contributions
            if contribution_name == 'pension':
                pension_employee_amount = calculate_employee_pension_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employee_rate)
                employer_pension_amount = calculate_employer_pension_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employer_rate)
                add_contribution(employee_contributions, contribution_name, pension_employee_amount, employer_pension_amount)
            elif contribution_name == 'maternity':
                maternity_employee_amount = calculate_employee_maternity_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employee_rate)
                employer_maternity_amount = calculate_employer_maternity_contribution(employee_salary, employee_transport_allowance, rssb_contribution.employer_rate)
                add_contribution(employee_contributions, contribution_name, maternity_employee_amount, employer_maternity_amount)
            else:
                # Default calculation for other contributions
                employee_contribution_amount = calculate_contribution_amount(employee_salary, employee_transport_allowance, rssb_contribution.employee_rate)
                employer_contribution_amount = calculate_contribution_amount(employee_salary, employee_transport_allowance, rssb_contribution.employer_rate)
                add_contribution(employee_contributions, contribution_name, employee_contribution_amount, employer_contribution_amount)
    # Calculate the Community Based Health Insurance contribution
    if community_health_insurance_contribution:
        community_health_insurance_amount_employee = calculate_cbhi_employee(employee_salary, paye_amount, pension_employee_amount, maternity_employee_amount, community_health_insurance_contribution.employee_rate)
        community_health_insurance_amount_employer = calculate_cbhi_employer(employee_salary, paye_amount, pension_employee_amount, maternity_employee_amount, community_health_insurance_contribution.employer_rate)
        add_contribution(employee_contributions, community_health_insurance_contribution.contribution_name, community_health_insurance_amount_employee, community_health_insurance_amount_employer)

def calculate_contribution_amount(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employee_pension_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employer_pension_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employee_maternity_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_employer_maternity_contribution(salary, transport_allowance, rate):
    return round((salary - transport_allowance) * rate)

def calculate_cbhi_employee(salary, paye, pension, maternity, rate):
    return round((salary - paye - pension - maternity) * rate)

def calculate_cbhi_employer(salary, paye, pension, maternity, rate):
    return round((salary - paye - pension - maternity) * rate)

def add_contribution(employee_contributions, name, employee_amount, employer_amount):
    employee_contributions['contributions'].append({
        'contribution_name': name,
        'employee_contribution_amount': employee_amount,
        'employer_contribution_amount': employer_amount
    })

def calculate_net_pay(employee, employee_contributions):
    gross_salary = employee['gross_salary']
    paye = employee_contributions['paye']

    pension_employee = next((c['employee_contribution_amount'] for c in employee_contributions['contributions'] if c['contribution_name'] == 'pension'), 0)
    maternity_employee = next((c['employee_contribution_amount'] for c in employee_contributions['contributions'] if c['contribution_name'] == 'maternity'), 0)
    cbhi_employee = next((c['employee_contribution_amount'] for c in employee_contributions['contributions'] if c['contribution_name'] == 'community based health insurance'), 0)

    # EERRROORR 1
    print("GET TYPES: ", type(gross_salary), type(paye), type(pension_employee), type(maternity_employee), type(cbhi_employee))
    if gross_salary:
        net_pay =  float(gross_salary) - paye - pension_employee - maternity_employee - cbhi_employee
    else:
        net_pay =  paye - pension_employee - maternity_employee - cbhi_employee
    return net_pay

def calculate_employee_contributions(employees, rssb_contributions):
    contributions_data = []

    try:
        for employee in employees:
            # Calculate PAYE for the employee
            paye_amount = calculate_paye(employee['gross_salary'])

            # Get initial contributions data structure
            employee_contributions = get_initial_contributions(employee, paye_amount)

            # Process each contribution for the employee
            process_contributions(employee, rssb_contributions, employee_contributions)

            # Calculate and add net pay
            net_pay = calculate_net_pay(employee, employee_contributions)
            employee_contributions['net_pay'] = net_pay

            # Append the final contributions data for the employee
            contributions_data.append(employee_contributions)
            # print("Employee contributions:", employee_contributions)
    except Exception as e:
        print("Here is error from debugging: ", e)
        return None
    return contributions_data
