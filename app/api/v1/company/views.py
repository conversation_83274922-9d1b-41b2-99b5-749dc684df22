import json
import uuid
from datetime import datetime, timedelta, timezone
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import current_user, get_jwt
from app import db
from app.models.central import Company, User
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.automations.create_dbdynamically import create_database
from app.api_helpers.ApiHelpers import UserInputValidator, ApiHelper
from app.api import jwt_config
from app.api.v1.decorators.auth_decorators import role_required
from app.api.v1.decorators.acl_decorators import acl_required


company_api_bp = Blueprint('company', __name__)


# ================= START OF GET REQUESTS ==================
@company_api_bp.route('/get_companies', methods=['GET'])
@role_required('admin')
def get_companies():
    current_app.logger.info('Getting companies route accessed')
    try:
        # Plan is a nested object, so I need to convert it to a dictionary
        companies = [{**comp, 'plan': comp.get("plan").to_dict()} for comp in Company.get_companies()]
        current_app.logger.info(f"Companies: {companies}")
        return jsonify(success=True, data=companies, message='Companies retrieved successfully'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting companies: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@company_api_bp.route('/get_company_by_id/<uuid:company_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_company_by_id(company_id):
    current_app.logger.info(f'Getting company by id {company_id} route accessed')
    # Protect user from accesing another company
    if company_id not in [str(company.company_id) for company in current_user.companies]:
        return jsonify(success=False, error='Forbidden'), 403
    
    try:
        company = Company.get_company_by_id(company_id)
        """This will be put into place in future"""
        # if not company.get("has_access"):
        #     current_app.logger.error(f"Subscription has expired: {company.get('company_name')}")
        #     return jsonify(success=False, error="Your subscription has expired"), 403
        
        company = {**company, 'plan': company.get("plan").to_dict()}
        if not company:
            return jsonify(success=False, error='Company not found'), 404
        return jsonify(success=True, data=company, message="Company retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error getting company by id: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    
# ================= END OF GET REQUESTS =====================
@company_api_bp.route('/register_company', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def register():
    current_app.logger.info('Registering company route accessed')
    data = request.get_json()
    company_name = data.get('company_name').strip()
    company_tin = data.get('company_tin').strip()
    rssb_number = data.get('rssb_number').strip()
    phone_number = data.get('phone_number').strip()
    email = data.get('email')
    number_of_employees = data.get('number_of_employees')
    company_type = data.get('company_type')
    country = data.get('country')
    province = data.get('province')
    district = data.get('district')
    sector = data.get('sector')
    cell = data.get('cell')
    village = data.get('village')
    plan_id = data.get('plan_id')

    # Validate the input data
    is_valid, errors = UserInputValidator.validate({
        "company_name":company_name, "company_tin":company_tin, "rssb_number":rssb_number,
        "phone_number":phone_number,"email":email, "number_employee":number_of_employees,
        "company_type":company_type, "country":country, "province":province,
        "district":district, "sector":sector, "cell":cell,
        "village":village, "plan_id":plan_id
    }, "company_registration")
    if not is_valid:
        current_app.logger.info('Invalid input data')
        return jsonify(success=False, error=errors), 400
    

    # Randomly generate the company alias
    random_part = str(uuid.uuid4())[:4]
    company_alias = f"{company_tin}_{random_part}"
    current_app.logger.info(f"Company alias: {company_alias}")
    new_company = Company(
        company_name=company_name, company_tin=company_tin,
        company_alias=company_alias, database_name=f"{company_alias}_db",
        rssb_number=rssb_number, phone_number=phone_number,
        email=email.strip(), number_employee=number_of_employees,
        company_type=company_type, country=country,
        province=province, district=district,
        sector=sector, cell=cell,
        village=village, plan_id=plan_id,
        trial_until=datetime.now(timezone.utc) + timedelta(days=30)
    )
    try:
        try:
            # Check if the tin number, email, company alias, rssb number, phonee number already exists
            if CompanyHelpers.check_company_exists(new_company.company_tin, new_company.email, new_company.company_alias, new_company.rssb_number, new_company.phone_number):
                return jsonify(success=False, message='Company already exists'), 409
        except Exception as e:
            current_app.logger.error(f"An error occurred while checking company existence: {str(e)}")
            return jsonify(success=False, message='Error checking company existence'), 500

        # Register the company
        is_created = CompanyHelpers.register_company(new_company)
        if not is_created:
            current_app.logger.info('Company registration failed')
            return jsonify(success=False, message='Company registration failed'), 500

        created_by = current_user.first_name + ' ' + current_user.last_name
        created_company = Company.query.filter_by(company_alias=company_alias).first() # Get the company that was just created
        """Check if the user is already registered to a company"""
        # Check if the user is already associated with the company via the relationship
        if created_company in current_user.companies:
            return jsonify(success=False, message='User already registered to this company'), 409
        else:
            # Associate the user with the company
            current_user.companies.append(created_company)

        try:
            # Create a new database for the company
            create_database(created_company.database_name)
        except Exception as e:
            current_app.logger.error(f"An error occurred: {str(e)}")
            return jsonify(success=False, message='Error creating database'), 500

        try:
            db.session.commit()
            current_app.logger.info('User registered to company successfully')
            # send email to the system admins so that they can know the company details
            message = f"""
                A new company has been registered by {created_by} with the following details:
                Company Name: {created_company.company_name},
                Company TIN: {created_company.company_tin},
                Phone Number: {created_company.phone_number},
                Email: {created_company.email},
                Number of Employees: {created_company.number_employee},
                plan: {created_company.plan.plan_name},
                Province: {created_company.province}, District: {created_company.district}, sector: {created_company.sector}, cell: {created_company.cell}, Village: {created_company.village}
                Please plan accordingly.
            """
            subject = 'New Company Registration'
            receipients = ['<EMAIL>','<EMAIL>','<EMAIL>']
            try:
                sent = Auxillary.send_netpipo_email(subject, receipients, message)
                message = f"Email sent successfully to {receipients}!"
                current_app.logger.info(message)
                message2 = f"sent response: {sent}"
                current_app.logger.info(message2)
            except Exception as e:
                current_app.logger.error(f"Error sending email: {e}")

            user_auth_info = {"username"}
            return jsonify(success=True, data=user_auth_info, message='User registered to company successfully'), 201
        except Exception as e:
            return jsonify(success=False, message='An error occurred'), 500
        
    except Exception as e:
        current_app.logger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, message='An error occurred'), 500


@company_api_bp.route('/upload_company_logo/<uuid:company_id>', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def upload_company_logo():
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    logo = request.files['logo']

    if logo.filename == '':
        current_app.logger.error('No selected file')
        return jsonify(success=False, error='No selected file'), 400

    if not CompanyHelpers.allowed_file(logo.filename):
        current_app.logger.error('Invalid file type')
        return jsonify(success=False, error='Invalid file type'), 400
    
    try:
        # Upload the logo to DigitalOcean Spaces
        current_app.logger.info(f"Uploading logo for company ID: {company_id}")
        result = Company.upload_company_logo(company_id, logo)
        if result['success']:
            message = result['message']
            current_app.logger.info(f"{message} - URL: {result.get('url')}")
            return jsonify({'success': True, 'message': message, 'url': result.get('url')}), 200
        else:
            message = result['message']
            current_app.logger.error(message)
            return jsonify({'success': False, 'message': message}), 500
    except Exception as e:
        current_app.logger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, error='An error occurred'), 500


@company_api_bp.route('/edit_company_profile', methods=['POST'])
@role_required(['hr', 'company_hr'])
def edit_company_profile():
    data = request.get_json()
    company_name = data.get('company_name')
    company_tin = data.get('company_tin')
    rssb_number = data.get('rssb_number')
    phone_number = data.get('phone_number')
    email = data.get('email')
    number_employee = data.get('number_employee')
    company_type = data.get('company_type')
    country = data.get('country')
    province = data.get('province')
    district = data.get('district')
    sector = data.get('sector')
    cell = data.get('cell')
    village = data.get('village')
    initial_qualification_period = int(data.get('initial_qualification_period') or 0)
    increment_policy = int(data.get('increment_policy') or 0)

    # Validate input data
    is_valid, errors = UserInputValidator.validate({
        'company_name': company_name,
        'company_tin': company_tin,
        'rssb_number': rssb_number,
        'phone_number': phone_number,
        'email': email,
        'number_employee': number_employee,
        'company_type': company_type,
        'country': country,
        'province': province,
        'district': district,
        'sector': sector,
        'cell': cell,
        'village': village,
        'initial_qualification_period': initial_qualification_period,
        'increment_policy': increment_policy
    }, 'company_registration')
    if not is_valid:
        current_app.logger.error(f"Validation errors: {errors}")
        return jsonify(success=False, errors=errors), 400
    
    # Check if current_user has access to modify company info
    companies_id = [str(company.company_id) for company in current_user.companies]
    if get_jwt().get("company_id") not in companies_id:
        return jsonify(success=False, error='Forbidden'), 403
        
    
    try:
        result = Company.update_company(
            get_jwt().get("company_id"), company_name, company_tin,
            rssb_number, phone_number, email,
            number_employee, company_type, country,
            province, district, sector, cell, village,
            initial_qualification_period,
            increment_policy
        )
        if result:
            current_app.logger.info('Company updated successfully')
            return jsonify(success=True, message='Company updated successfully'), 200
        else:
            current_app.logger.error('Error updating company')
            return jsonify(success=False, message='Error updating company'), 500
    except Exception as e:
        current_app.logger.error(f"Error updating company: {str(e)}")
        return jsonify(succes=False, message='Error updating company'), 500

@company_api_bp.route('/switch_company', methods=['POST'])
@role_required(['hr', 'admin'])
def switch_company():
    jwt_data = get_jwt()
    identity = json.loads(jwt_data.get("sub"))
    selected_company_id = request.get_json().get('company_id')
    
    if selected_company_id == jwt_data.get('company_id'):
        return jsonify(success=False, error="You cannot switch to the same company"), 400
    # Check if current_user has access to modify company info
    companies_id = [str(company.company_id) for company in current_user.companies]
    if get_jwt().get("company_id") not in companies_id or selected_company_id not in companies_id:
        return jsonify(success=False, error='Forbidden'), 403
    
    try:
        # Get the selected company ID from the form
        current_app.logger.info(f"Selected company ID: {selected_company_id}")
        if not selected_company_id:
            return jsonify(success=False, error="No company selected"), 400
        
        # Switch to the selected company database
        selected_company = Company.query.get(selected_company_id)
        if not selected_company:
            return jsonify(success=False, error="Company not found"), 404
        
        current_app.logger.info(f"Company switched successfully to: {selected_company.company_name}")
        current_app.logger.info(f"Company plan: {selected_company.plan.plan_name}")
        current_app.logger.info(f"Company plan id: {selected_company.plan.plan_id}")
        current_app.logger.info(f"Company plan price: {selected_company.plan.price}")
        current_app.logger.info(f"Company has access: {selected_company.to_dict().get('has_access', False)}")

        # Re-Login user again with new company
        additional_claims = {
            "last_activity": datetime.now().isoformat(),
            "company_id": str(selected_company.company_id),
            "company_plan_id": selected_company.plan_id,
            "database_name": selected_company.database_name
        }
        access_token, refresh_token = ApiHelper.refresh_tokens(identity, additional_claims, jwt_data)
        return jsonify(success=True, 
                    data={"access_token": access_token, "refresh_token": refresh_token}, 
                    message='Company switched successfully!'), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred: {str(e)}")
        return jsonify(success=False, error='An error occured'), 500



@company_api_bp.route('/company_location', methods=['POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def update_company_location():
    """Get company location"""
    jwt_data = get_jwt()
    data = request.get_json()
    longitude = data.get('longitude')
    latitude = data.get('latitude')
    current_app.logger.info(f"Longitude: {longitude}, Latitude: {latitude}")
    try:
        # Update the company location
        company_id = str(jwt_data.get('company_id'))
        if not company_id:
            jsonify(success=False, error='Company not found'), 400

        result = Company.add_company_location(company_id, latitude, longitude)
        current_app.logger.info(f"Result after adding location: {result}")
        if result:
            current_app.logger.info('Location added successfully')
            return jsonify(success=True, message='Location added successfully'), 200
        else:
            current_app.logger.error('Error adding location')
            return jsonify(success=False, error='Error adding location'), 400
    except Exception as e:
        current_app.logger.error(f"Error adding location: {str(e)}")
        return jsonify(success=False, error='Error adding location'), 400
    
@company_api_bp.route('/delete_company', methods=['POST'])
@role_required('admin')
def delete_company():
    """Delete a company"""
    data = request.get_json()
    company_id = data.get('company_id')    
    if not company_id:
        return jsonify(success=False, error='Company ID is required'), 400    
    try:
        current_app.logger.info(f"Deleting company with ID: {company_id}")
        result = Company.delete_company_and_database(company_id)
        # Check the result of the deletion
        if result.get('error'):
            return jsonify(success=False, error=result['error']), 400
        return jsonify(success=True, message=result['message']), 200
    except Exception as e:
        current_app.logger.error(f"Error deleting company: {str(e)}")
        return jsonify(success=False, error='An error occurred while deleting the company'), 500
