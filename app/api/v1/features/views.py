from flask import Blueprint, jsonify, request, current_app
from app.models.central import Features
from app.api.v1.decorators.auth_decorators import role_required


features_api_bp = Blueprint('features_api', __name__)


@features_api_bp.route('/features', methods=['GET'])
@role_required('admin')
def get_features():
    try:
        features = Features.get_features()
        return jsonify(success=True, data=features, message="Features retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error('Error getting features: %s', str(e))
        return jsonify(success=False, error='Error getting features'), 500


@features_api_bp.route('/features/<feature_id>', methods=['GET'])
@role_required('admin')
def get_feature(feature_id):
    try:
        feature = Features.get_feature_by_id(feature_id)
        if not feature:
            return jsonify(success=False, error='Feature not found'), 404

        return jsonify(success=True, data=feature, message="Feature retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error('Error getting feature: %s', str(e))
        return jsonify(success=False, error='Error getting feature'), 500


@features_api_bp.route('/add_feature', methods=['POST'])
@role_required('admin')
def add_feature():
    data = request.get_json()
    feature_name = data.get("feature_name")
    description = data.get("description")

    if not feature_name or not description:
        return jsonify(success=False, error='Missing required fields'), 400

    try:
        is_feature_added = Features.add_feature(feature_name, description)
        if is_feature_added:
            current_app.logger.error('Feature added successfully')
            return jsonify(success=True, data=feature_name, message="Feature added successfully"), 200
        else:
            current_app.logger.error('Error editing feature: %s', is_feature_added)
            return jsonify(success=False, error='Error adding feature'), 500
    except Exception as e:
        current_app.logger.error('Error adding feature: %s', str(e))
        return jsonify(success=False, error='Error adding feature'), 500


@features_api_bp.route("/update_feature/<feature_id>", methods=["PUT"])
@role_required("admin")
def update_feature(feature_id):
    data = request.get_json()
    feature_name = data.get("feature_name")
    description = data.get("description")

    if not feature_name or not description:
        return jsonify(success=False, error='Missing required fields (feature_name, description)'), 400

    is_found = Features.get_feature_by_id(feature_id)
    if not is_found:
        return jsonify(success=False, error='Feature not found'), 404
    
    try:
        is_feature_updated = Features.update_feature(feature_id, feature_name, description)
        if is_feature_updated:
            current_app.logger.error('Feature updated successfully')
            return jsonify(success=True, data=feature_name, message="Feature updated successfully"), 200
        else:
            current_app.logger.error('Error updating feature: %s', is_feature_updated)
            return jsonify(success=False, error='Error updating feature'), 500
    except Exception as e:
        current_app.logger.error('Error updating feature: %s', str(e))
        return jsonify(success=False, error='Error updating feature'), 500


@features_api_bp.route("/delete_feature/<feature_id>", methods=["DELETE"])
@role_required('admin')
def delete_feature(feature_id):
    is_found = Features.get_feature_by_id(feature_id)
    if not is_found:
        return jsonify(success=False, error='Feature not found'), 404
    
    try:
        _ = Features.delete_feature(feature_id) # Return value is used nowhere
        current_app.logger.error('Feature deleted successfully')
        return jsonify(success=True, data=feature_id, message="Feature deleted successfully"), 200
    except Exception as e:
        current_app.logger.error('Error deleting feature: %s', str(e))
        return jsonify(success=False, error='Error deleting feature'), 500