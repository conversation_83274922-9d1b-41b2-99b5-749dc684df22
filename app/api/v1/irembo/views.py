import os
import json
import uuid
import hmac
import hashlib
import time
import requests
from dotenv import load_dotenv
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.models.irembo import Irembo
from app.models.central_payments import Payments
from app.models.central import Company, Plans
from app.models.company import Employee
from app.api.jwt_config import redis_client
from app.api.v1.decorators.auth_decorators import role_required


load_dotenv()
irembo_api_bp = Blueprint('irembo', __name__)

@irembo_api_bp.route('/create_invoice', methods=['POST'])
@role_required(['hr', 'manager', 'company_hr'])
def create_invoice():
    """Create an invoice"""
    jwt_data = get_jwt()
    data = request.get_json()
    quantity = data.get('quantity')
    different_plan = data.get('different_plan')
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    company_plan_id = jwt_data.get('company_plan_id')

    if not company_id or not database_name or not company_plan_id:
        current_app.logger.error(f"Company ID/Database Name/Company Plan ID not found in JWT\ncompany_id: {company_id}, database_name: {database_name}, company_plan_id: {company_plan_id}")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400
    
    company = Company.get_company_by_id(company_id)
    db_connection = DatabaseConnection()
    with db_connection.get_session(database_name) as db_session:
        employees=[]
        try:
            employees = [emp for emp in Employee.get_employees(db_session) \
                            if str(emp.get("company_id")).strip() == str(company_id).strip()]
        except Exception as e:
            current_app.logger.error(f"Error getting employees: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later'), 500
        
    # If client wants different plan
    if different_plan == 'yes':
        new_plan_id = data.get('plan_id')
        if not new_plan_id:
            return jsonify(success=False, error='Invalid plan'), 400
        
        new_plan = Plans.get_plan_by_id(new_plan_id)
        if not new_plan:
            return jsonify(success=False, error='Invalid plan'), 400
        
        # Get the price per employee based on new selected plan
        price_per_employee = new_plan.get("price_per_employee", 0)
        price = new_plan.get('price')
        if not price:
            return jsonify(success=False, error='Invalid plan'), 400
        
        price = float(price) + (len(employees) * float(price_per_employee))
        current_app.logger.info(f"New plan. Price: {price} and type: {type(price)}")
    else:
        # Get price per employee from plan
        company_plan_id = jwt_data['company_plan_id']
        existing_plan = Plans.get_plan_by_id(company_plan_id)
        if not existing_plan:
            return jsonify(success=False, error='Invalid plan'), 400
        
        price_per_employee = existing_plan.get("price_per_employee", 0)
        current_app.logger.info(f"Company: {company} and type: {type(company)}")

        price = existing_plan.get('price')
        if not price:
            return jsonify(success=False, error='Invalid plan'), 400
        
        price = float(price) + (len(employees) * float(price_per_employee))
        current_app.logger.info(f"Price: {price} and type: {type(price)}")

    company_phone = company.get('phone_number')
    company_email = company.get('email')
    company_name =  company.get("company_name")
    user_id = jwt_data.get('user_id')
    transaction_id = str(uuid.uuid4().hex)
    current_app.logger.info(f"Company id: {company_id} and user id: {user_id}")

    invoice_data = {
        "transactionId": transaction_id,
        "paymentAccountIdentifier": "ACR_RWF",
        "customer": {
            "email": company_email,
            "phoneNumber": company_phone,
            "name": company_name            
        },
        "paymentItems": [
            {
                "code": "PC-8b9c38d1cd",
                "quantity": quantity,
                "unitAmount": price,
            }
        ],
        "description": f"company_id: {str(company_id)}, user_id: {str(user_id)}, plan: {str(existing_plan)}",

        "expiryAt": "2025-09-30T01:00:00+02:00",
        "language": "EN"
    }
    current_app.logger.info(f"Data for creating invoice: {invoice_data}")

    try:
        # headers = Irembo.get_headers()
        # url = f"{Irembo.base_url}/invoices"
        result = Irembo.create_invoice(invoice_data)
        current_app.logger.info(f"Result of create invoice: {result} and the type: {type(result)}")

        if not result.get("success"):
            return jsonify(success=False, error="Failed to create invoice. Please try again."), 500
        
        invoice_number = result["data"]["invoiceNumber"]
        data = result["data"]
        current_app.logger.info(f"Data from create invoice: {data}")
        
        try:
            # save data in redis for further payment processing under invoice number as key
            redis_client.set(invoice_number, json.dumps(data))
            current_app.logger.info(f"Data saved in redis for pay_invoice with invoice number: {invoice_number}")

            # On successful payment initialization, set if it is different plan in redis for further processing during payment confirmation
            if different_plan == 'yes':
                redis_client.set(f"{invoice_number}_different_plan", "yes", ex=3600) # 1 hour to confirm payment

            return jsonify(success=True, invoice_number=invoice_number), 200
        except Exception as e:
            current_app.logger.error(f"Error saving data in redis: {e}")
            return jsonify(success=False, error="Something went wrong. Please try again."), 500
    except Exception as e:
        current_app.logger.error(f"Error creating invoice: {e}")
        return jsonify(success=False, error="Error creating invoice"), 500
    
@irembo_api_bp.route('/pay_invoice/<reference_number>', methods=['GET'])
def pay_invoice(reference_number):
    data = redis_client.get(reference_number)
    if not data:
        return jsonify(success=False, error="Invoice not found"), 404
    
    data = json.loads(data)
    total_price = data.get("amount")
    payment_items = data.get("paymentItems", [])
    customer = data.get("customer", [])

    if customer:
        customer_email = customer.get("email")
        customer_phone = customer.get("phoneNumber")
        customer_name = customer.get("fullName")

    if payment_items:
        quantity = payment_items[0].get("quantity")
        unit_price = payment_items[0].get("unitAmount")
    else:
        quantity = None
        unit_price = None
    
    data = {
        "success": True,
        "reference_number": reference_number,
        "customer_email": customer_email,
        "customer_phone": customer_phone,
        "customer_name": customer_name,
        "total_price": total_price,
        "quantity": quantity,
        "unit_price": unit_price,
    }
    return jsonify(success=True, data=data, message="Invoice found"), 200

@irembo_api_bp.route('/payment_confirmation/<string:invoice_number>', methods=['POST'])
@role_required(['hr', 'manager', 'company_hr'])
def payment_confirmation(invoice_number):
    """Handle payment confirmation from IremboPay."""
    jwt_data = get_jwt()
    user_id = jwt_data.get('user_id')
    company_id = jwt_data.get('company_id')
    if not user_id or not company_id:
        return jsonify(success=False, error="Try again later or re-login"), 400
    
    data = request.get_json()
    current_app.logger.info(f"Payment confirmation data: {data}")

    message = data.get('message')
    if message != "Payment successful":
        return jsonify(success=False, error="Payment not successful"), 500
    
    # Get payment data
    payment_data = redis_client.get('invoice_number')
    current_app.logger.info(f"Payment data from session: {payment_data}")

    amount = payment_data.get("amount")
    payment_items = payment_data.get("paymentItems", [])
    payment_method = "IremboPay API"

    # Extract number of months paid for
    if payment_items:
        quantity = payment_items[0].get("quantity", 1)
    else:
        quantity = 1

    num_days = quantity * 30  # Convert months to days

    # Save payment record
    try:
        payment = Payments.create_payment(
            amount=amount,
            payment_method=payment_method,
            transaction_id=invoice_number,
            user_id=user_id,
            company_id=company_id
        )
        current_app.logger.info(f"Payment saved: {payment}")
    except Exception as e:
        current_app.logger.error(f"Error saving payment: {e}")
        return jsonify(success=False, error="Failed to proccess payment. Please try again."), 500
    
    # Update the company's subscription in the database
    try:
        updated = Company.update_subscription_end_period(company_id, num_days)
        current_app.logger.info(f"Subscription updated: {updated}")
        if not updated:
            current_app.logger.error("Failed to update the subscription")
            return jsonify(success=False, error="Failed to update the subscription"), 500
        
        # update the company with the plan_id if different_plan is yes
        plan_id = jwt_data.get('plan_id')
        different_plan = redis_client.get(f'{invoice_number}_different_plan')
        if different_plan == 'yes':
            try:
                is_plan_updated = Company.update_company_plan(company_id, plan_id)
                current_app.logger.info(f"Company updated: {is_plan_updated}")
                
                if not is_plan_updated:
                    current_app.logger.error("Failed to update the company plan")
                    return jsonify(success=False, error="Failed to update the company plan"), 500
            
            except Exception as e:
                current_app.logger.error(f"Error updating company: {e}")
                return jsonify(success=False, error="Failed to update subscription"), 500

        return jsonify(success=True, message="Payment recorded and subscription updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating subscription: {e}")
        return jsonify(success=False, error="Failed to update subscription"), 500
    

def verify_signature(secret_key, payload, signature_header):
    """Verify the signature from IremboPay."""
    try:
        # Step 1: Extract timestamp and signature
        elements = signature_header.split(',')
        timestamp = None
        received_signature = None

        for element in elements:
            key, value = element.strip().split('=')
            if key == 't':
                timestamp = value.strip()
            elif key == 's':
                received_signature = value.strip()

        if not timestamp or not received_signature:
            return False, "Invalid signature format"

        # Step 2: Construct the signed payload
        signed_payload = f"{timestamp}#{payload.strip()}"

        # Step 3: Compute the expected signature
        computed_signature = hmac.new(
            key=secret_key.encode(),
            msg=signed_payload.encode(),
            digestmod=hashlib.sha256
        ).hexdigest()

        # Step 4: Compare signatures
        if not hmac.compare_digest(computed_signature, received_signature):
            return False, "Signature mismatch"

        # Step 5: Validate timestamp to prevent replay attacks (5-minute window)
        current_time = int(time.time() * 1000)  # Current time in milliseconds
        if abs(int(timestamp) - current_time) > 300 * 1000:
            return False, "Request expired"

        return True, "Valid signature"

    except Exception as e:
        return False, f"Error in signature verification: {str(e)}"

@irembo_api_bp.route('/callback/irembo_payment', methods=['POST'], strict_slashes=False)
def irembo_payment_callback():
    """Handle asynchronous payment confirmation from IremboPay with signature verification."""
    try:
        # Step 1: Extract signature header
        signature_header = request.headers.get("irembopay-signature")
        current_app.logger.info(f"Signature header: {signature_header}")
        if not signature_header:
            # For API endpoints that need to return JSON, we'll keep jsonify
            # This is an exception since this is an API endpoint called by Irembo
            return jsonify(success=False, error="Missing signature header"), 400

        # Step 2: Extract request body
        request_body = request.get_data(as_text=True)  # Raw request body
        current_app.logger.info(f"Received Payload from callback url: {request_body}")

        # Step 3: Retrieve secret key
        secret_key = os.getenv('payment_secret_key')
        current_app.logger.info(f"Secret key: {secret_key}")
        if not secret_key:
            current_app.logger.error("Missing payment_secret_key in environment variables")
            return jsonify(success=False, error="Server misconfiguration"), 500

        # Step 4: Verify signature
        is_valid, message = verify_signature(secret_key, request_body, signature_header)
        current_app.logger.info(f"Signature verification status: is valid: {is_valid}, message: {message}")
        if not is_valid:
            current_app.logger.error(f"Signature verification failed: {message}")
            return jsonify(success=False, error=message), 400

        current_app.logger.info("Signature verification successful")
        # Step 5: Process payment if signature is valid
        data = request.get_json()
        current_app.logger.info(f"Payment data: {data}")
        if not data:
            current_app.logger.error("Invalid JSON payload")
            return jsonify(success=False, error="Invalid JSON payload"), 400

        success = data.get("success")
        payment_data = data.get("data", {})
        payment_status = payment_data.get("paymentStatus")
        current_app.logger.info(f"Payment data: {payment_data}")
        current_app.logger.info(f"Payment status: {success}, Payment status: {payment_status}")
        paymentItems = payment_data.get("paymentItems", [])
        current_app.logger.info(f"Payment items: {paymentItems}")

        if not success or payment_status != "PAID":
            current_app.logger.error("Payment not successful")
            return jsonify(success=False, error="Data received but payment not successful"), 400

        # Post the payment data to https://education.accountants.co.rw/internal_payment_callback
        # check if the code in paymentItems is PC-c334d7fa14
        if paymentItems:
            code = paymentItems[0].get("code")
            current_app.logger.info(f"Code: {code}")
            if code == "PC-c334d7fa14":
                current_app.logger.info("Payment for subscription")
                # Post the payment data to https://education.accountants.co.rw/internal_payment_callback
                url = "https://education.accountants.co.rw/internal_payment_callback"
                # get the learnpipe_api_key from the environment variables
                headers = {"X-API-Key": os.getenv("learnpipe_api_key")}
                current_app.logger.info(f"Headers: {headers}")
                try:
                    response = requests.post(url, headers=headers, json=payment_data)
                    current_app.logger.info(f"Response: {response.json()}")
                except Exception as e:
                    current_app.logger.error(f"Error posting payment data: {str(e)}")

        current_app.logger.info(f"Payment successfully processed: {payment_data}")

        return jsonify(success=True, message="Payment recorded and subscription updated"), 200

    except Exception as e:
        current_app.logger.error(f"Internal Server Error: {str(e)}")
        return jsonify(success=False, error="Internal server error"), 500
    
@irembo_api_bp.route('/get_invoice_details/<invoice_reference>', methods=['GET'])
def get_invoice_details(invoice_reference):
    """Get invoice details."""
    try:
        result = Irembo.get_invoice_details(invoice_reference)
        current_app.logger.info(f"Result of get invoice details: {result}")
        if result.get("success"):
            # This is an API endpoint, so we'll return JSON directly
            return jsonify(success=True, data=result, message="Successfully retrieved invoice details"), 200
        else:
            return jsonify(success=False, error="Failed to retrieve invoice details"), 500
    except Exception as e:
        current_app.logger.error(f"Error retrieving invoice: {e}")
        return jsonify(success=False, error="Error retrieving invoice"), 500