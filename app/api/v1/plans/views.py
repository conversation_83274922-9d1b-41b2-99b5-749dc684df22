from flask import request, jsonify, Blueprint, current_app
from app.models.central import Plans, Features
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


plans_api_bp = Blueprint('plans', __name__)


@plans_api_bp.route('/get_plans', methods=['GET'])
@role_required("admin")
def get_plans():
    try:
        plans = Plans.get_plans()
        return jsonify(success=True, data=plans, message="Plans retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f'Error getting plans: {e}')
        return jsonify(success=False, error='Failed to fetch plans'), 500

@plans_api_bp.route('/get_plan/<uuid:plan_id>', methods=['GET'])
@role_required("admin")
def get_plan(plan_id):
    try:
        plan = Plans.get_plan_by_id(plan_id)
        if not plan:
            return jsonify(success=False, error='Plan not found'), 404
        
        return jsonify(success=True, data=plan, message="Plan retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f'Error getting plan: {e}')
        return jsonify(success=False, error='Failed to fetch plan'), 500

@plans_api_bp.route('/add_plans', methods=['POST'])
@role_required("admin")
def add_plans():
    data = request.get_json()
    plan_name = data.get("plan_name")
    description = data.get("description")
    price = data.get("price")
    num_of_employees = data.get("num_of_employees")
    price_per_employee = data.get("price_per_employee")

    is_valid, errors = UserInputValidator.validate({
        'plan_name': plan_name,
        'description': description,
        'price': price,
        'num_of_employees': num_of_employees,
        'price_per_employee': price_per_employee
    }, 'add_plan')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    # Modify the plan name to lowercase
    plan_name = plan_name.lower()

    try:
        added = Plans.add_plan(plan_name, description, price, num_of_employees, price_per_employee)
        if isinstance(added, str):
            current_app.logger.error(f'Error adding plan {plan_name} to the database')
            return jsonify(success=False, error='Plan already exists'), 400
        
        if not added:
            current_app.logger.error(f'Error adding plan {plan_name} to the database')
            return jsonify(success=False, error='Failed to add plan'), 500
        
        return jsonify(success=True, message="Plan added successfully"), 200
    except Exception as e:
        current_app.logger.error(f'Error adding plan {plan_name} to the database: {e}')
        return jsonify(success=False, error="Plan added successfully"), 500
        
@plans_api_bp.route('/edit_plan/<uuid:plan_id>', methods=['PUT'])
@role_required("admin")
def edit_plan(plan_id):
    data = request.get_json()

    # Check if plan exists
    plan = Plans.get_plan_by_id(plan_id)
    if not plan:
        return jsonify(success=False, error='Plan not found'), 404
    
    plan_name = data.get("plan_name")
    description = data.get("description")
    price = data.get("price")
    num_of_employees = data.get("num_of_employees")
    price_per_employee = data.get("price_per_employee")
    
    is_valid, errors = UserInputValidator.validate({
        'plan_name': plan_name,
        'description': description,
        'price': price,
        'num_of_employees': num_of_employees,
        'price_per_employee': price_per_employee
    }, 'add_plan')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Modify the plan name to lowercase
    plan_name = plan_name.lower()

    try:
        updated = Plans.update_plan(plan_id, plan_name, description, price, num_of_employees, price_per_employee)
        if not updated:
            current_app.logger.error(f'Error updating plan {plan_name} to the database')
            return jsonify({'error': 'Plan already exists'}), 400
        
        current_app.logger.info('Plan updated successfully')
        return jsonify(success=True, message="Plan updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f'Error updating plan {plan_name} to the database: {e}')
        return jsonify(success=False, error="Plan updated successfully"), 500

@plans_api_bp.route('/delete_plan/<uuid:plan_id>', methods=['DELETE'])
@role_required("admin")
def delete_plan(plan_id):
    plan = Plans.get_plan_by_id(plan_id)
    if not plan:
        return jsonify(success=False, error='Plan not found'), 404
    
    try:
        is_deleted = Plans.delete_plan(plan_id)
        if not is_deleted:
            current_app.logger.error(f'Error deleting plan {plan_id} from the database')
            return jsonify(success=False, error='Failed to delete plan'), 500
        return jsonify(success=True, message="Plan deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f'Error deleting plan {plan_id} from the database: {e}')
        return jsonify(success=False, error='Failed to delete plan'), 500
    

@plans_api_bp.route('/plan/<uuid:plan_id>/assign_features', methods=['POST'])
@role_required("admin")
def assign_features(plan_id):
    data = request.get_json()
    plan = Plans.get_plan_by_id(plan_id)
    if not plan:
        return jsonify(success=False, error='Plan not found'), 404
    
    feature_id = data.get("feature_id")
    if not feature_id:
        return jsonify(success=False, error='feature_id is required'), 400
    
    feature = Features.get_feature_by_id(feature_id)
    if not feature:
        return jsonify(success=False, error='Feature not found'), 404
    
    result = Plans.add_feature(plan_id, feature_id)
    if result == 'exists':
        return jsonify(success=False, error='Feature already exists'), 400
    elif result == 'success':
        return jsonify(success=True, message="Feature added successfully"), 200
    else:
        return jsonify(success=False, error='Failed to add feature'), 500

    
@plans_api_bp.route('/remove_feature/<uuid:plan_id>/<uuid:feature_id>', methods=['POST'])
@role_required("admin")
def remove_feature(plan_id, feature_id):
    current_app.logger.info(f'plan_id: {plan_id}, feature_id: {feature_id}')
    try:
        plan = Plans.query.get(plan_id)
        if not plan:
            return jsonify(success=False, error='Plan not found'), 404
        
        feature = Features.get_feature_by_id(feature_id)
        if not feature:
            return jsonify(success=False, error='Feature not found'), 404
        
        # If features is not associated with plan
        if not plan.features or feature_id not in [f.feature_id for f in plan.features]:
            return jsonify(success=False, error='Feature not found'), 404
        
        is_removed = Plans.remove_feature(plan_id, feature_id)
        if not is_removed:
            current_app.logger.error(f'Error deleting feature {feature_id} from the database')
            return jsonify(success=False, error='Failed to delete feature'), 500
        
        current_app.logger.info('Feature removed successfully', 'success')
        return jsonify(success=True, message="Feature removed successfully"), 200
    except Exception as e:
        current_app.logger.error(f'Error deleting feature {feature_id} from the database: {e}')
        return jsonify(success=False, error='Failed to delete feature'), 500
