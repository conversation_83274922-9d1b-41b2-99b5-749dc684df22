from flask import Blueprint, request, jsonify
from app import db
from app.models.central import Company
from sqlalchemy import create_engine
from app.automations.create_dbdynamically import create_database


company_bp = Blueprint('company', __name__)

@company_bp.route('/api/v1/register_company', methods=['POST'])
def register_company():
    data = request.get_json()
    company_name = data.get('company_name')
    company_alias = data.get('company_alias')
    company_tin = data.get('company_tin')
    rssb_number = data.get('rssb_number')
    phone_number = data.get('phone_number')
    email = data.get('email')
    city = data.get('city')
    country = data.get('country', 'Rwanda')
    number_employee = data.get('number_employee')
    company_type = data.get('company_type')


    database_name = f"{company_alias.lower()}_db"
    print(f"Database name: {database_name}")
    
    try:

        # Create a new company record
        new_company = Company(
        company_name=company_name,
        company_alias=company_alias,
        database_name=database_name,
        company_tin=company_tin,
        rssb_number=rssb_number,
        phone_number=phone_number,
        email=email,
        city=city,
        country=country,
        number_employee=number_employee,
        company_type=company_type
        )
        print(new_company)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    try:
        db.session.add(new_company)
        db.session.commit()
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    try:
        # Create a new database for the company
        create_database(database_name)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    return jsonify({'message': f'Company {company_name} registered successfully!'}), 201



@company_bp.route('/api/v1/company/<uuid:company_id>', methods=['GET'])
def get_company(company_id):
    """Get a company.
    Description: This endpoint returns a single company in the system.
    Args:
        company_id (UUID): The unique identifier of the company.
        Returns:
        A dictionary containing company data     
    """
    try:
        company = Company.query.get(company_id)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    if company is None:
        return jsonify({'message': 'Company not found'}), 404
    try:
        # Get the company data as a dictionary
        company_data = company.to_dict()
        print(company_data)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    return jsonify(company_data), 200

@company_bp.route('/api/v1/companies', methods=['GET'])
def get_companies():
    """Get all companies.
    Description: This endpoint returns a list of all companies in the system.
    Returns:
        A list of dictionaries containing company data. 
    """
    try:
        companies = Company.query.all()
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    if not companies:
        return jsonify({'message': 'No companies found'}), 404
    try:
        companies_data = [company.to_dict() for company in companies]
        print(companies_data)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    return jsonify(companies_data), 200

@company_bp.route('/api/v1/company_database/<uuid:company_id>', methods=['GET'])
def get_company_database_name(company_id):
    """Get a company database name.
    Description: This endpoint returns the database name of a company.
    Args:
        company_id (UUID): The unique identifier of the company.
    Returns:
        A dictionary containing the database name of the company.
    """
    try:
        company = Company.query.get(company_id)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    if company is None:
        return jsonify({'message': 'Company not found'}), 404
    try:
        company_database = company.database_name
        print(company_database)
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    return jsonify(company_database), 200