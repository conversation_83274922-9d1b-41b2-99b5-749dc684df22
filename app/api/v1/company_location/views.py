from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt
from app.models.company import Site
from app.utils.db_connection import DatabaseConnection
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator

db_connection = DatabaseConnection()
company_locations_api_bp = Blueprint('company_locations', __name__)


@company_locations_api_bp.route('/view_locations', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def view_locations():
    """Retrieve and return all company locations."""

    jwt_data = get_jwt()
    database_name = jwt_data['database_name']
    try:
        with db_connection.get_session(database_name) as db:
            # Retrieve all sites from the database
            sites = Site.get_sites(db)
            current_app.logger.info('Sites: %s', sites)

            return jsonify(success=True, data=sites, message='Sites retrieved successfully'), 200
    except Exception as e:
        current_app.logger.error('Error rendering view_locations.html: %s', e)
        return jsonify({'message': 'Error rendering view_locations.html'}), 400

@company_locations_api_bp.route('/add_locations', methods=['GET', 'POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def add_locations():
    data = request.get_json()
    jwt_data = get_jwt()
    site_name = data.get("site_name")
    location = data.get("location")
    latitude = data.get("latitude")
    longitude = data.get("longitude")
    # Retrieve the database name from JWT
    database_name = jwt_data['database_name']
    
    current_app.logger.info('Site Name: %s', site_name)
    current_app.logger.info('Location: %s', location)
    current_app.logger.info('Latitude: %s', latitude)
    current_app.logger.info('Longitude: %s', longitude)
    
    is_valid, errors = UserInputValidator.validate({
        "site_name": site_name,
        "location": location,
        "latitude": latitude,
        "longitude": longitude
    }, 'company_location')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    with db_connection.get_session(database_name) as db:
        try:
            # Add the site to the database
            result = Site.add_site(db, site_name, location, longitude=longitude, latitude=latitude)
            current_app.logger.info('Result: %s', result)
            if result:
                return jsonify(success=True, data=result.to_dict(), message='Site added successfully'), 200
            else:
                return jsonify(scuccess=False, error='Site not added'), 400
        except Exception as e:
            current_app.logger.error('Error adding site: %s', e)
            return jsonify(success=False, error='Error adding site'), 400

@company_locations_api_bp.route('/update_location/<uuid:site_id>', methods=['PUT'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def update_location(site_id):
    jwt_data = get_jwt()
    data = request.get_json()
    site_name = data.get("site_name")
    location = data.get("location")
    latitude = data.get("latitude")
    longitude = data.get("longitude")
    current_app.logger.info('Site Name: %s', site_name)
    current_app.logger.info('Location: %s', location)
    # Retrieve the database name from JWT
    database_name = jwt_data['database_name']
    
    # Input validation
    is_valid, errors = UserInputValidator.validate({
        "site_name": site_name,
        "location": location,
        "latitude": latitude,
        "longitude": longitude
    }, 'company_location')

    if not is_valid:
        jsonify(success=False, error=errors), 400
        
    with db_connection.get_session(database_name) as db:
        sites_to_update = Site.get_site_by_id(db, site_id)
        current_app.logger.info('Sites to update: %s', sites_to_update)
        
        if not sites_to_update:
            return jsonify(success=False, error="Site not found"), 404
        
        try:          
            # Update the site in the database
            result = Site.update_site(db, site_id, site_name, location)
            current_app.logger.info('Result: %s', result)
            
            if result:
                return jsonify({'message': 'Site updated successfully'}), 200
            else:
                return jsonify({'message': 'Site not updated'}), 400
        except Exception as e:
            current_app.logger.error('Error updating site: %s', e)
            return jsonify({'message': 'Error updating site'}), 400
