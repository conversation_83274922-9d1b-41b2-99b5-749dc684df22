import json
from flask import Blueprint, jsonify, current_app, request
from flask_jwt_extended import get_jwt
from app.models.central import User
from app.helpers.auxillary import Auxillary
from app.models.company import FeedBack
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.api.v1.decorators.auth_decorators import role_required


feedback_api_bp = Blueprint('feedback_api', __name__)

@feedback_api_bp.route('/feedbacks', methods=['GET'])
@role_required('admin')
def get_feedbacks():
    """View feedback from different companies."""
    try:
        companies = CompanyHelpers.get_companies()
        if not companies:
            current_app.logger.error("No companies found")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        current_app.logger.info(f"{len(companies)} companies found")
    except Exception as e:
        current_app.logger.error(f"Error getting companies: {e}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500
    
    # Get feedback from different companies
    feedbacks = []

    for company in companies:
        try:
            database_name = company.database_name
            db_connection = DatabaseConnection()
            with db_connection.get_session(database_name) as db_session:
                feedback = FeedBack.get_feedbacks(db_session)
                feedbacks.append(feedback)
        except Exception as e:
            current_app.logger.error(f"Error getting feedback: {e}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500

    return jsonify(success=True, data=feedbacks, message='Feedbacks retrieved successfully'), 200


@feedback_api_bp.route('/add_feedback', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant', 'employee', 'supervisor'])
def add_feedback():
    # JWT data
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    user_id = json.loads(jwt_data.get('sub')).get('user_id')

    if not company_id or not database_name or not user_id:
        return jsonify(success=False, error="An errorr occurred. Try again or re-login"), 400
    
    # Submitted data
    data = request.get_json()
    subject = data.get('subject')
    message = data.get('message')

    if not subject or not message:
        return jsonify(success=False, error='Subject and message are required'), 400
    

    # send the email
    try:
        my_email = '<EMAIL>'
        subject = 'New Feedback Submission'
        message = f"{message}, \n\n\nfor more information, login to your admin account"
        is_sent = Auxillary.send_netpipo_email(subject, my_email, message)
        
        if not is_sent:
            current_app.logger.error("Email not sent")
    except Exception as e:
        current_app.logger.error(f"Error sending email: {e}")

    db_connection = DatabaseConnection()
    try:
        with db_connection.get_session(database_name) as db_session:
            feedback = FeedBack.insert_feedback(db_session, company_id, user_id, subject, message)
            current_app.logger.info(f"Feedback saved successfully: {feedback}")
            message = 'Thank you for your feedback. You can submit another one.'

            return jsonify(success=True, message='Feedback saved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error saving feedback: {e}")
        return jsonify({'success':False,'message':'An error occurred. Please try again later.'}), 500
