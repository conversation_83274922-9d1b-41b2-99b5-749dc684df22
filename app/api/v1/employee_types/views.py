from flask import Blueprint, current_app, jsonify ,request
from app.models.central import EmployeeType
from app.api.v1.decorators.auth_decorators import role_required


brd_deductions_api_bp = Blueprint('employee_types', __name__)


@brd_deductions_api_bp.route('/get_employee_types', methods=['GET'])
@role_required('admin')
def get_employee_types():
    try:
        employee_types = EmployeeType.get_employee_types()
        return jsonify(success=True, data=employee_types, message="Employee types retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error getting employee types: {str(e)}")
        return jsonify(success=False, error="An error occurred. Please try again later."), 500


@brd_deductions_api_bp.route('/get_employee_type/<uuid:employee_type_id>', methods=['GET'])
@role_required('admin')
def get_employee_type(employee_type_id):
    try:
        employee_type = EmployeeType.query.get_or_404(employee_type_id)
        return jsonify(success=True, data=employee_type.to_dict(), message="Employee type retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error getting employee type: {str(e)}")
        return jsonify(success=False, error="An error occurred. Please try again later."), 500


@brd_deductions_api_bp.route('/add_employee_types', methods=['POST'])
@role_required('admin')
def add_employee_types():
    employee_type_name = request.get_json().get('employee_type_name')
    if not employee_type_name:
        return jsonify(success=False, error="Employee type name is required"), 400
    employee_type_name = employee_type_name.lower()
    
    # Check if the employee type already exists
    employee_type_exists = EmployeeType.query.filter_by(employee_type_name=employee_type_name).first()
    if employee_type_exists:
        return jsonify(success=False, error="Employee type already exists"), 400
    
    try:
        # Create a new employee type
        employee_type = EmployeeType(employee_type_name=employee_type_name)
        employee_type.insert_employee_type()
        current_app.logger.info(f"Employee type: {employee_type_name} added successfully")

        return jsonify(success=True, data=employee_type.to_dict(), message="Employee type added successfully"), 201
    except Exception as e:
        current_app.logger.error(f"Error adding employee type: {str(e)}")
        return jsonify(success=False, error=f"An error occurred: {str(e)}"), 500

@brd_deductions_api_bp.route('/update_employee_type/<uuid:employee_type_id>', methods=['PUT'])
@role_required('admin')
def update_employee_type(employee_type_id):
    employee_type_name = request.get_json().get('employee_type_name')
    # Input validation
    if not employee_type_name:
        return jsonify(success=False, error="Employee type name is required"), 400
    
    employee_type_name = employee_type_name.lower()
    employee_type = EmployeeType.query.get_or_404(employee_type_id)
    if not employee_type:
        return jsonify(success=False, error="Employee type not found"), 404
    
    try:
        # Updating the employee type
        employee_type.employee_type_name = employee_type_name
        is_created = employee_type.update_employee_type(employee_type_id, employee_type_name)
        if not is_created:
            return jsonify(success=False, error="Error updating employee type"), 500
        current_app.logger.info(f"Employee type: {employee_type_name} updated successfully")

        return jsonify(success=True, data=[employee_type.to_dict()], message="Employee type updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating employee type: {str(e)}")
        return jsonify(success=False, error="An error occured while updating employee type"), 500


@brd_deductions_api_bp.route('/delete_employee_type/<uuid:employee_type_id>', methods=['DELETE'])
@role_required('admin')
def delete_employee_type(employee_type_id):
    employee_type = EmployeeType.query.get_or_404(employee_type_id)
    if not employee_type:
        return jsonify(success=False, error='Employee type not found'), 404
    
    try:
        is_deleted = employee_type.delete_employee_type(employee_type_id)

        if not is_deleted:
            error = "Error deleting employee type."
            current_app.logger.error(error)
            return jsonify(success=False, error=error), 500
        
        message = f"Employee type: {employee_type.employee_type_name} deleted successfully"
        current_app.logger.info(message)
        return jsonify(success=True, message=message), 200
    except Exception as e:
        error = f"An error occurred: {str(e)}"
        current_app.logger.error(error)
        return jsonify(success=False, error=error), 500