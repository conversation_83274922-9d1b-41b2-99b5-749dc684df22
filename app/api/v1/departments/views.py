from flask import Blueprint, current_app, request, jsonify
from flask_jwt_extended import get_jwt
from app.models.company import Departments
from app.utils.db_connection import DatabaseConnection
from app.api.v1.decorators.auth_decorators import role_required


db_connection = DatabaseConnection()
department_api_bp = Blueprint('department', __name__)


@department_api_bp.route('/get_departments', methods=['GET'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_departments():
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
    
    with db_connection.get_session(database_name) as db_session:
        try:
            departments = Departments.get_departments(db_session)
            current_app.logger.info(f"Departments: {departments}")
            return jsonify(success=True, data=departments, message='Departments fetched successfully.'), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while fetching departments: {str(e)}')
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@department_api_bp.route('/get_department/<uuid:department_id>', methods=['GET'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_department(department_id):
    database_name = get_jwt().get('database_name')
    if not database_name:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
    
    with db_connection.get_session(database_name) as db_session:
        try:
            department = Departments.get_department_by_id(db_session, department_id)
            if not department:
                return jsonify(success=False, error='Department not found.'), 404
            
            current_app.logger.info(f"Department: {department}")
            return jsonify(success=True, data=department.to_dict(), message='Departments fetched successfully.'), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while fetching departments: {str(e)}')
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        

@department_api_bp.route('/add_department', methods=['POST'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_department():
    data = request.get_json()
    department_name = data.get('department_name')

    if not department_name:
        return jsonify(success=False, error='Department name is required.'), 400
    
    department_name = department_name.strip().lower()
    database_name = get_jwt().get('database_name')
    if not database_name:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred! Try again or re-login'), 500
    
    with db_connection.get_session(database_name) as db_session:               
        try:
            # Add department to the database
            added = Departments.insert_department(db_session, department_name)
            if isinstance(added, str):
                error = f'Department {department_name.upper()} already exists!'
                current_app.logger.error(error)
                return jsonify(success=False, error=error), 400
            
            if isinstance(added, Departments):
                message = f'Department {department_name.upper()} added successfully!'
                current_app.logger.info(message)
                return jsonify(success=True, message=message), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while adding the department: {str(e)}')
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        

@department_api_bp.route('/update_department/<uuid:department_id>', methods=['PUT'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_department(department_id):
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
    
    data = request.get_json()
    department_name = data.get('department_name')
    if not department_name:
        return jsonify(success=False, error='Department name is required.'), 400
    # Clean received department_name
    department_name = department_name.strip().lower()

    with db_connection.get_session(database_name) as db_session:
        try:
            department_to_update = Departments.get_department_by_id(db_session, department_id)
            current_app.logger.info("department_to_update: ", department_to_update)

            if department_to_update is None:
                current_app.logger.error("No department found")
                return jsonify(success=False, error='No department found'), 404
            
            updated = Departments.update_department(db_session, department_id, department_name)
            
            # Departments.update_department retuns str or Departments
            if isinstance(updated, str):
                error = f'Department {department_name.upper()} already exists!'
                current_app.logger.error(error)
                return jsonify(success=False, error=error), 400
            
            if isinstance(updated, Departments):
                message = f"Department updated successfully."
                current_app.logger.info(f"Department updated successfully: {updated}")
                return jsonify(success=True, message=message), 200           
        except Exception as e:
            current_app.logger.error(f"An error occurred while updating department: {str(e)}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@department_api_bp.route('/delete_department/<uuid:department_id>', methods=['DELETE'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_department(department_id):
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
    
    with db_connection.get_session(database_name) as db_session:
        try:
            department = Departments.get_department_by_id(db_session, department_id)
            if not department:
                return jsonify(success=False, error='Department not found.'), 404
            
            # Delete department from the database
            deleted = Departments.delete_department(db_session, department_id)
            if deleted:
                current_app.logger.info('Department deleted successfully!')
                return jsonify(success=True, message='Department deleted successfully!'), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while deleting the department: {str(e)}')
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500