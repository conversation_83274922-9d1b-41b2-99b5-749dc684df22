from flask import current_app, Blueprint, request, jsonify
from app.models.central import TaxBracket, CasualsTaxBracket, SecondEmployeeTaxBracket
import logging


tax_bracket_bp = Blueprint('tax_bracket', __name__)

@tax_bracket_bp.route('/api/v1/insert_taxbracket', methods=['POST'])
def insert_tax_bracket():
    """Insert a tax bracket into the database."""
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    tax_rate = data.get('rate')
    try:
        tax_bracket = TaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=tax_rate)
    except Exception as e:
        return jsonify({"message": "Error creating tax bracket object."}), 500
    try:
        if tax_bracket.insert_taxbracket():
            return jsonify({"message": "Tax bracket inserted successfully."}), 201
    except Exception as e:
        return jsonify({"message": "Error inserting tax bracket."}), 500
    return jsonify({"message": "Error inserting tax bracket."}), 500

@tax_bracket_bp.route('/api/v1/get_taxbrackets', methods=['GET'])
def get_tax_brackets():
    """Get all tax brackets from the database."""
    try:
        tax_brackets = TaxBracket.get_taxbrackets()
    except Exception as e:
        logging.error(e)
        return jsonify({"message": "Error getting tax brackets."}), 500
    return jsonify({"tax_brackets": tax_brackets}), 200


@tax_bracket_bp.route('/api/v1/insert_casuals_taxbracket', methods=['POST'])
def insert_casuals_tax_bracket():
    """Insert a casuals tax bracket into the database."""
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    tax_rate = data.get('rate')
    try:
        tax_bracket = CasualsTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=tax_rate)
    except Exception as e:
        return jsonify({"message": "Error creating tax bracket object."}), 500
    try:
        if tax_bracket.insert_casuals_taxbracket():
            return jsonify({"message": "Casuals tax bracket inserted successfully."}), 201
    except Exception as e:
        return jsonify({"message": "Error inserting casuals tax bracket."}), 500
    return jsonify({"message": "Error inserting casuals tax bracket."}), 500

@tax_bracket_bp.route('/api/v1/insert_second_employee_taxbracket', methods=['POST'])
def insert_second_employee_tax_bracket():
    """Insert a second employee tax bracket into the database."""
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    tax_rate = data.get('rate')
    try:
        tax_bracket = SecondEmployeeTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=tax_rate)
    except Exception as e:
        return jsonify({"message": "Error creating tax bracket object."}), 500
    try:
        if tax_bracket.insert_second_employee_taxbracket():
            return jsonify({"message": "Second employee tax bracket inserted successfully."}), 201
    except Exception as e:
        return jsonify({"message": "Error inserting second employee tax bracket."}), 500
    return jsonify({"message": "Error inserting second employee tax bracket."}), 500

