from decimal import Decimal
from dotenv import load_dotenv
from flask import Blueprint, request, jsonify, url_for, current_app
from flask_jwt_extended import get_jwt
from app.models.central import Company
from app.models.quickbooks import QuickBooks
from app.helpers.auxillary import Auxillary
from app.utils.db_connection import DatabaseConnection
from app.models.company_quickbooks import QuickbooksAuditLogs
from app.helpers.quickbooks_helpers import QuickBooksHelper
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator
from app.api.jwt_config import redis_client


load_dotenv()
# Initialize the database connection
db_connection = DatabaseConnection()

quickbooks_api_bp = Blueprint('quickbooks', __name__)


@quickbooks_api_bp.route('/create_account', methods=['POST'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def create_account():
    """Create an account."""
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    if not company_id:
        return jsonify(success=False, error="Company ID not found in JWT."), 400
    
    qb = QuickBooks(company_id=company_id)

    # Captures JS request data
    data = request.get_json()
    name = data.get('name')
    description = data.get('description')
    account_type = data.get('account_type')
    account_subtype = data.get('account_subtype')

    is_valid, errors = UserInputValidator.validate({
        "name": name,
        "description": description,
        "account_type": account_type,
        "account_subtype": account_subtype
    }, 'create_qb_account')

    if not is_valid:
        current_app.logger.error(f"Input data validation errors: {errors}")
        return jsonify(success=False, errors=errors), 400

    # Create the QuickBooks account
    try:
        account_data = {
            "name": name,
            "description": description,
            "account_type": account_type,
            "account_subtype": account_subtype
        }
        account = qb.create_account(qb.realm_id, account_data)
        if isinstance(account, str):
            current_app.logger.error(f"Error creating QuickBooks account: {account}")
            return jsonify(success=False, error=account), 500
        
        current_app.logger.info(f"QuickBooks account created: {account}")
        return jsonify(success=True, data=account, message="QuickBooks account created successfully."), 201
    except Exception as e:
        current_app.logger.error(f"Error creating QuickBooks account: {e}")
        return jsonify(success=False, error="Failed to create QuickBooks account."), 500
    
@quickbooks_api_bp.route('/journal_entry_constraints', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def journal_entry_constraints():
    """Get constraints for journal entry creation."""
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    if not company_id:
        return jsonify(success=False, error="Company ID not found in JWT."), 400
    
    try:
        employees_with_deductions = redis_client.get('employees_with_deductions')
        if not employees_with_deductions:
            return jsonify(success=False, error="No employees with deductions found."), 404
    except Exception as e:
        current_app.logger.error(f"Error fetching employees with deductions from Redis: {e}")
        return jsonify(success=False, error="Failed to fetch employees with deductions."), 500
    
    try:
        qb = QuickBooks(company_id=company_id)
    except Exception as e:
        current_app.logger.error(f"Error initializing QuickBooks: {e}")
        return jsonify(success=False, error="Failed to initialize QuickBooks."), 500
    
    # First check if the company has QuickBooks tokens
    if qb.access_token is None or qb.refresh_token is None:
        current_app.logger.error("QuickBooks integration not set up.")
        # Redirect to the authorization URL
        return jsonify(success=False, error="QuickBooks integration not set up. Please authorize the app.", redirect_url=url_for('quickbooks.get_auth_url'))
    
    try:
        vendors = qb.get_vendors(qb.realm_id)
        my_vendors = vendors.get('QueryResponse', {}).get('Vendor', [])
    except Exception as e:
        current_app.logger.error(f"Error getting vendors: {e}")
        my_vendors = []  # Make sure it's always defined
    
    # Get QuickBooks locations (departments in QB) and departments (classes in QB)
    locations = []
    departments = []
    try:
        # Get locations from QuickBooks (these are called "Departments" in QuickBooks)
        locations_response = qb.get_locations(qb.realm_id)
        if 'QueryResponse' in locations_response and 'Department' in locations_response['QueryResponse']:
            locations = locations_response['QueryResponse']['Department']
            current_app.logger.info(f"Retrieved {len(locations)} locations from QuickBooks")
        else:
            current_app.logger.warning("No locations found in QuickBooks or unexpected response format")

        # Get departments from QuickBooks (these are called "Classes" in QuickBooks)
        departments_response = qb.get_departments(qb.realm_id)
        if 'QueryResponse' in departments_response and 'Class' in departments_response['QueryResponse']:
            departments = departments_response['QueryResponse']['Class']
            current_app.logger.info(f"Retrieved {len(departments)} departments from QuickBooks")
        else:
            current_app.logger.warning("No departments found in QuickBooks or unexpected response format")
    except Exception as e:
        current_app.logger.error(f"Error getting locations and departments from QuickBooks: {e}")
        locations = []
        departments = []

    account_totals = {}

    def add_to_account(name, dr=0, cr=0):
        dr = Auxillary.round_to_decimal(dr)
        cr = Auxillary.round_to_decimal(cr)

        # Skip the account if both dr and cr are 0
        if dr == 0 and cr == 0:
            return
        # If the account doesn't exist, create it
        if name not in account_totals:
            account_totals[name] = {'DR': 0, 'CR': 0}
        account_totals[name]['DR'] += dr
        account_totals[name]['CR'] += cr
    
    for e in employees_with_deductions:
        add_to_account('Gross salary', dr=e['gross_needed'])
        add_to_account('Pension ER-Contr-8%', dr=e['pension_er_value'])
        add_to_account('Maternity ER-Contr-0.3%', dr=e['maternity_er_value'])
        add_to_account('Medical ER-Contr-7.5%', dr=e['rama_ee'])
        add_to_account('Other staff expenses', dr=e['total_reimbursements'])
        add_to_account('PAYE Payable', cr=e['paye'])

        # Combine employer and employee pension into a single "Pension Payable"
        pension_payable = Decimal(str(e['pension_ee_value'])) + Decimal(str(e['pension_er_value']))
        pension_payable = Auxillary.round_to_decimal(pension_payable)
        add_to_account('Pension Payable', cr=pension_payable)

        # Combine employer and employee maternity into a single "Maternity Payable"
        maternity_payable = Decimal(str(e['maternity_er_value'])) + Decimal(str(e['maternity_ee_value']))
        maternity_payable = Auxillary.round_to_decimal(maternity_payable)
        add_to_account('Maternity Payable', cr=maternity_payable)

        # Calculate net salary payable with consistent rounding (same as download_journal_entry)
        net_salary_value = Decimal(str(e['net_salary_value']))
        total_deductions = Decimal(str(e['total_deductions']))
        total_reimbursements = Decimal(str(e['total_reimbursements']))
        brd_deduction = Decimal(str(e['brd_deduction']))
        salary_advance = Decimal(str(e['salary_advance']))

        # Log the values before calculation for debugging
        current_app.logger.info(f"Employee {e['employee'].get('first_name', '')} {e['employee'].get('last_name', '')}: net_salary_value={net_salary_value}, total_deductions={total_deductions}, total_reimbursements={total_reimbursements}, brd_deduction={brd_deduction}, salary_advance={salary_advance}")

        # Calculate with proper Decimal arithmetic - using the same order as in payroll_summary.py
        net_salary_payable = net_salary_value - total_deductions + total_reimbursements - brd_deduction - salary_advance
        current_app.logger.info(f"Before rounding: net_salary_payable={net_salary_payable}")

        # Round to whole number for consistency
        net_salary_payable = Auxillary.round_to_decimal(net_salary_payable)
        current_app.logger.info(f"After rounding: net_salary_payable={net_salary_payable}")
        add_to_account('Net Salary Payable', cr=net_salary_payable)

        add_to_account('CBHI Payable', cr=e['cbhi_value'])
        add_to_account('RAMA (Medical) Payable', cr=e['total_rama'])
        add_to_account('BRD Payable', cr=e['brd_deduction'])
        add_to_account('Salary Advance', cr=e['salary_advance'])
        add_to_account('Other Deductions', cr=e['total_deductions'])
    
    # Check if debits and credits balance
    total_dr = sum(values['DR'] for values in account_totals.values())
    total_cr = sum(values['CR'] for values in account_totals.values())

    # Round totals for comparison
    total_dr = Auxillary.round_to_decimal(total_dr)
    total_cr = Auxillary.round_to_decimal(total_cr)

    if total_dr != total_cr:
        current_app.logger.warning(f"Journal entries do not balance: DR={total_dr}, CR={total_cr}, Difference={total_dr-total_cr}")
        # Adjust the Net Salary Payable to balance the journal entry
        if 'Net Salary Payable' in account_totals:
            difference = total_dr - total_cr
            account_totals['Net Salary Payable']['CR'] += difference
            # Recalculate totals
            total_dr = Auxillary.round_to_decimal(sum(values['DR'] for values in account_totals.values()))
            total_cr = Auxillary.round_to_decimal(sum(values['CR'] for values in account_totals.values()))
            current_app.logger.info(f"Journal entries balanced by adjusting Net Salary Payable: New DR={total_dr}, CR={total_cr}")

    try:
        accounts = qb.get_accounts(qb.realm_id)
        my_accounts = accounts['QueryResponse']['Account']
        current_app.logger.info(f"Accounts that have been fetched: {accounts}")
        current_app.logger.info("")
        current_app.logger.info(f"Accounts that have been retrieved: {my_accounts}")

        account_choices = []
        for acc in my_accounts:
            current_app.logger.info("")
            current_app.logger.info(f"Account inside the for loop: {acc}")
            account_info = {
                "Id": acc['Id'],
                "Name": acc['FullyQualifiedName'],
                "AccountType": acc.get('AccountSubType'),
                "Currency": acc['CurrencyRef']['name']
            }
            account_choices.append(account_info)
        current_app.logger.info(f"Account choices: {account_choices}")
    except Exception as e:
        current_app.logger.error(f"Error fetching accounts: {e}")
        account_choices = []
    
    # Handle POST request
    if request.method == 'POST':
        lines = []
        mappings = request.get_json()
        current_app.logger.info(f"Received mappings: {mappings}")
        # Get the journal memo from the request body/data
        journal_memo = mappings.get('journal_memo', "Payroll Journal Entry")
        current_app.logger.info(f"Journal memo: {journal_memo}")

        for name, amounts in mappings.items():
            account_id = mappings.get(name)
            current_app.logger.info(f"Account ID for {name}: {account_id}")
            if not account_id:
                return jsonify(success=False, error=f"Account ID for {name} is required."), 400
            
            # Check if the account is of type "Accounts Payable" and add vendor reference
            vendor_reference = None
            account_info = next((acc for acc in account_choices if acc['Id'] == account_id), None)
            # Get vendor key from the mappings dict
            vendor_key = f'vendor_{name}'  # name corresponds to account name like 'PAYE Payable'
            vendor_id = mappings.get(vendor_key)

            if vendor_id:
                vendor_info = next((v for v in my_vendors if v['Id'] == vendor_id), None)
                vendor_name = vendor_info['DisplayName'] if vendor_info else "Unknown Vendor"
                current_app.logger.info(f"Vendor ID: {vendor_id}, Vendor Name: {vendor_name}")

                vendor_reference = {
                    "Entity": {
                        "Type": "Vendor",
                        "EntityRef": {
                            "value": vendor_id
                        }
                    }
                }
            # Get the custom description from the form
            description_key = f'desc_{name}'
            custom_description = mappings.get(description_key, name)
            current_app.logger.info(f"Description for {name}: {custom_description}")

            # Get line-specific location and department
            line_location_id = mappings.get(f'location_{name}')
            line_department_id = mappings.get(f'department_{name}')

            # Get location and department names for description
            line_location_name = None
            line_department_name = None

            if line_location_id:
                location_obj = next((loc for loc in locations if loc.get('Id') == line_location_id), None)
                if location_obj:
                    line_location_name = location_obj.get('Name')

            if line_department_id:
                department_obj = next((dept for dept in departments if dept.get('Id') == line_department_id), None)
                if department_obj:
                    line_department_name = department_obj.get('Name')

            # Add location/department to description if available
            line_description = custom_description
            if line_location_name:
                line_description += f" - {line_location_name}"
            if line_department_name:
                line_description += f" - {line_department_name}"

            if amounts['DR']:
                # Round the amount using Auxillary.round_to_decimal for consistency
                dr_amount = Auxillary.round_to_decimal(amounts['DR'])

                # Create the line detail with department reference if available
                line_detail = {
                    "PostingType": "Debit",
                    "AccountRef": {"value": account_id}
                }

                # Add class (department) reference if available
                if line_department_id:
                    line_detail["ClassRef"] = {
                        "value": line_department_id
                    }

                # Add department (location) reference if available
                if line_location_id:
                    line_detail["DepartmentRef"] = {
                        "value": line_location_id
                    }

                lines.append({
                    "DetailType": "JournalEntryLineDetail",
                    "Amount": float(dr_amount),
                    "Description": line_description,
                    "JournalEntryLineDetail": line_detail
                })
            
            if amounts['CR']:
                # Round the amount using Auxillary.round_to_decimal for consistency
                cr_amount = Auxillary.round_to_decimal(amounts['CR'])

                # Create the line detail with department reference if available
                line_detail = {
                    "PostingType": "Credit",
                    "AccountRef": {"value": account_id},
                    **(vendor_reference if vendor_reference else {})
                }

                # Add class (department) reference if available
                if line_department_id:
                    line_detail["ClassRef"] = {
                        "value": line_department_id
                    }

                # Add department (location) reference if available
                if line_location_id:
                    line_detail["DepartmentRef"] = {
                        "value": line_location_id
                    }

                lines.append({
                    "DetailType": "JournalEntryLineDetail",
                    "Amount": float(cr_amount),
                    "Description": line_description,
                    "JournalEntryLineDetail": line_detail
                })

            try:
                # Calculate total debits and credits to ensure they balance
                total_debits = sum(float(line["Amount"]) for line in lines if line["JournalEntryLineDetail"]["PostingType"] == "Debit")
                total_credits = sum(float(line["Amount"]) for line in lines if line["JournalEntryLineDetail"]["PostingType"] == "Credit")

                # Use Auxillary.round_to_decimal for consistent rounding
                total_debits = Auxillary.round_to_decimal(total_debits)
                total_credits = Auxillary.round_to_decimal(total_credits)

                current_app.logger.info(f"Total debits: {total_debits}, Total credits: {total_credits}")

                # Check if debits and credits are balanced
                if total_debits != total_credits:
                    current_app.logger.error(f"Debits and credits are not balanced: Debits={total_debits}, Credits={total_credits}")
                    return jsonify(success=False, error=f"Journal entry is not balanced. Debits ({total_debits}) must equal Credits ({total_credits})."), 400

                # This try/except block helps to make sure that quickbook audit is saved regardless of the response
                try:
                    # Create journal entry with memo
                    journal_entry_data = {
                        "PrivateNote": journal_memo,
                        "Line": lines
                    }

                    current_app.logger.info(f"Journal entry data: {journal_entry_data}")
                    response = qb.create_journal_entry(qb.realm_id, journal_entry_data)
                    current_app.logger.info(f"Journal entry posted: {response}")
                    # Process the response from QuickBooks
                    message, status = QuickBooksHelper.handle_quickbooks_response(response)
                    current_app.logger.info(f"QuickBooks response processed: message='{message}', status='{status}'")

                    if status == "Success":
                        current_app.logger.info(f"Journal entry posted successfully: {message}")
                        return jsonify(success=True, message="Journal entry posted successfully")
                    else:
                        current_app.logger.error(f"Error posting journal entry: {message}")
                        return jsonify(success=False, error=message), 500
                except Exception as e:
                    current_app.logger.error(f"Error processing QuickBooks response: {str(e)}")
                    return jsonify(success=False, error="Error processing QuickBooks response"), 500
                finally:
                    # Save the audit trail logs in the database so that we can later track
                    database_name = qb.database_name
                    # connect to the database
                    # get the user id from the session
                    user_id = jwt_data.get('user_id')
                    with db_connection.get_session(database_name) as db_session:
                        # Add the audit log
                        log_entry = {
                            "action_type": "Post Journal Entry",
                            "operation_status": status,
                            "error_message": message if status == "Failure" else None,
                            "request_payload": journal_entry_data,
                            "response_payload": response,
                            "user_id": user_id
                        }
                        try:
                            result = QuickbooksAuditLogs.add_quickbooks_audit_log(db_session, **log_entry)
                            current_app.logger.info(f"Audit log entry created: {result}")
                        except Exception as e:
                            current_app.logger.error(f"Error creating audit log entry: {e}")
                            return jsonify(success=False, error="Failed to create audit log entry, but journal entry was posted successfully."), 500

            except Exception as e:
                   current_app.logger.error(f"Error creating journal entry: {str(e)}")
                   return jsonify(success=False, error="Error creating journal entry"), 500
    
    # If 'GET' request
    return jsonify(success=True,
                   data={
                       "account_totals": account_totals,
                       "vendors": my_vendors,
                       "locations": locations,
                       "departments": departments,
                       "accounts": account_choices
                   },
                   message="Journal entry data prepared successfully."), 200

@quickbooks_api_bp.route('/get_company_info', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def get_company_info():
    """Get company info."""
    qb = QuickBooks(company_id=get_jwt().get('company_id'))
    try:
        current_app.logger.info('Getting company info')
        company_info = qb.get_company_info(qb.realm_id)
        current_app.logger.info(f"Company info: {company_info}")
        return jsonify(success=True, data=company_info, message="Company info retrieved successfully."), 200
    except Exception as e:
        current_app.logger.error(f"Error getting company info: {e}")
        return jsonify(success=False, errpr='Error getting company info'), 500

@quickbooks_api_bp.route('/get_accounts', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def get_accounts():
    """Get accounts."""
    qb = QuickBooks(company_id=get_jwt().get('company_id'))
    try:
        current_app.logger.info('Getting accounts')
        accounts = qb.get_accounts(qb.realm_id)
        current_app.logger.info(f"Accounts length: {len(accounts)}")
        return jsonify(success=True, data=accounts, message="Accounts retrieved successfully."), 200
    except Exception as e:
        current_app.logger.error(f"Error getting accounts: {e}")
        return jsonify(success=False, error='Error getting accounts'), 500

@quickbooks_api_bp.route('/get_vendors', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def get_vendors():
    """Get vendors."""
    qb = QuickBooks(company_id=get_jwt().get('company_id'))
    try:
        current_app.logger.info('Getting vendors')
        vendors = qb.get_vendors(qb.realm_id)
        current_app.logger.info(f"Vendors length: {len(vendors)}")
        return jsonify(success=True, data=vendors, message="Vendors retrieved successfully."), 200
    except Exception as e:
        current_app.logger.error(f"Error getting vendors: {e}")
        return jsonify(success=False, error='Error getting vendors'), 500

@quickbooks_api_bp.route('/get_auth_url', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def get_auth_url():
    """Get the QuickBooks OAuth2 authorization URL."""
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    if not company_id:
        current_app.logger.error("No company ID found in session")
        return jsonify(success=False, error='No company ID found in session'), 400
    
    try:
        # get the quickbooks object
        qb = QuickBooks(company_id=company_id)
        current_app.logger.info(f"QuickBooks client: {qb}")
    except Exception as e:
        current_app.logger.error(f"Error getting QuickBooks client: {e}")
        return jsonify(success=False, error='Error getting QuickBooks client'), 400
    
    try:
        auth_url = qb.get_authorization_url()
        current_app.logger.info(f"Authorization URL: {auth_url}")
        # save the audit logs
        database_name = qb.database_name
        # connect to the database
        # get the user id from the session
        user_id = jwt_data.get('user_id')
        """with db_connection.get_session(database_name) as db_session:
            # Add the audit log
            log_entry = {
                "action_type": "Get Authorization URL",
                "operation_status": "Success",
                "request_payload": None,
                "response_payload": auth_url,
                "user_id": user_id
            }
            try:
                result = QuickbooksAuditLogs.add_quickbooks_audit_log(db_session, **log_entry)
                current_app.logger.info(f"Audit log entry created: {result}")
            except Exception as e:
                current_app.logger.error(f"Error creating audit log entry: {e}")
                flash("Failed to create audit log entry.", "danger")
            """
        return jsonify(success=True, redirect_url=auth_url), 200
    except Exception as e:
        current_app.logger.error(f"Error getting authorization URL: {e}")
        return jsonify(success=False, error='Error getting authorization URL'), 400

@quickbooks_api_bp.route('/disconnect', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def disconnect():
    """Disconnect from QuickBooks."""
    company_id = get_jwt().get('company_id')
    if not company_id:
        current_app.logger.error("No company ID found in JWT")
        return jsonify(success=False, error="Try again or re-login"), 400
    
    try:
        # get the quickbooks object
        qb = QuickBooks(company_id=company_id)
        current_app.logger.info(f"QuickBooks client: {qb}")
    except Exception as e:
        current_app.logger.error(f"Error getting QuickBooks client: {e}")
        return jsonify({'error': 'Error getting QuickBooks client'}), 500
    
    try:
        qb.disconnect_app()
        message = "Netpick integration disconnected successfully!"
        return jsonify({'success': True, 'message': message}), 200
    except Exception as e:
        current_app.logger.error(f"Error disconnecting from QuickBooks: {e}")
        message = "Error disconnecting from QuickBooks"
        return jsonify({'success': False, 'message': message}), 500

@quickbooks_api_bp.route('/webhook', methods=['GET'])
@role_required(['hr', 'company_hr', 'accountant', 'manager'])
def webhook():
    """Callback route for QuickBooks OAuth2."""
    jwt_data = get_jwt()
    data = request.args
    code = data.get('code')
    state = data.get('state')
    error = data.get('error')
    realm_id = data.get('realmId')
    
    current_app.logger.info(f"Callback realm ID: {realm_id}")
    current_app.logger.info(f"Callback state: {state}")
    current_app.logger.info(f"Callback code: {code}")
    current_app.logger.info(f"Callback error: {error}")

    is_valid, errors = UserInputValidator.validate({
        'code': code,
        'state': state,
        'error': error,
        'realmId': realm_id
    }, 'quickbooks_webhook')
    if not is_valid:
        return jsonify(success=False, error=errors), 400

    company_id = jwt_data.get('company_id')
    if not company_id:
        current_app.logger.error("No company ID found in JWT")
        return jsonify(success=False, error="Try again or re-login"), 400
    
    try:
        current_app.logger.info(f"Getting QuickBooks client for company ID: {company_id}")
        qb = QuickBooks(company_id=company_id)
        current_app.logger.info(f"QuickBooks client: {qb}")
    except Exception as e:
        current_app.logger.error(f"Error getting QuickBooks client: {e}")
        return jsonify(success=False, error='Error getting QuickBooks client'), 400
    
    try:
        tokens = qb.get_quickbooks_access_token(code)
        current_app.logger.info(f"Access token: {tokens['access_token']}")
        current_app.logger.info(f"Refresh token: {tokens['refresh_token']}")
        current_app.logger.info(f"Company ID: {company_id}")

        # Update the data in the company table
        is_updated = Company.update_company_data(company_id,
                                                 quickbooks_access_token=QuickBooksHelper.encrypt(tokens['access_token']),
                                                 quickbooks_refresh_token=QuickBooksHelper.encrypt(tokens['refresh_token']),
                                                 quickbooks_authorization_code=QuickBooksHelper.encrypt(code),
                                                 quickbooks_realm_id=realm_id)
        if is_updated:
            current_app.logger.info(f"Updated QuickBooks tokens for company ID {company_id} with a result of {is_updated}")
            return jsonify(success=True, message="QuickBooks tokens updated successfully!"), 200
        else:
            current_app.logger.error(f"Failed to update QuickBooks tokens for company ID {company_id}")
            return jsonify(success=False, error='Failed to update QuickBooks tokens'), 500
    except Exception as e:
        current_app.logger.error(f"Error getting access token: {e}")
        return jsonify(success=False, error="Error getting access token"), 500
