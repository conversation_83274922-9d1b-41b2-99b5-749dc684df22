from flask import current_app, Blueprint, request, jsonify
from app.models.central import CasualsTaxBracket, TaxBracket, SecondEmployeeTaxBracket, ConsultantTaxBracket
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


taxbracket_api_bp = Blueprint('taxbracket_api', __name__)


# ==================== CASUAL ===========================
@taxbracket_api_bp.route('/casuals_tax_brackets', methods=['GET'])
@role_required('admin')
def casuals_tax_bracket():
    try:
        casual_tax_brackets = CasualsTaxBracket.get_casuals_taxbrackets()
        return jsonify(success=True, data=casual_tax_brackets, message="Casuals tax brackets retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving casuals tax brackets: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve casuals tax brackets"), 500

@taxbracket_api_bp.route('/casuals_tax_bracket/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_casuals_tax_bracket(id):
    try:
        casual_tax_bracket = CasualsTaxBracket.get_single_casual_taxbrackets(id)
        if not casual_tax_bracket:
            return jsonify(success=False, error="Casuals tax bracket not found"), 404
        return jsonify(success=True, data=casual_tax_bracket, message="Casuals tax bracket retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving casuals tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve casuals tax bracket"), 500
    

@taxbracket_api_bp.route('/insert_casuals_tax_bracket', methods=['POST'])
@role_required('admin')
def insert_casuals_tax_bracket():
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')

    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')
    if not is_valid:
        return jsonify(success=False, errors=errors), 400
    
    # Convert rate to percentage
    rate = float(rate) / 100

    try:
        casual_tax_bracket = CasualsTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=rate)
        is_inserted = casual_tax_bracket.insert_casuals_taxbracket()
        if not is_inserted:
            return jsonify(success=False, error="Failed to insert casuals tax bracket"), 500
        
        current_app.logger.info("Casuals tax bracket inserted successfully")
        return jsonify(success=True, message="Casuals tax bracket inserted successfully"), 201
    except Exception as e:
        current_app.logger.error(f"Error inserting casuals tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to insert casuals tax bracket"), 500

@taxbracket_api_bp.route('/update_casuals_tax_bracket/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_casuals_tax_bracket(id):
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')

    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')
    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    # Convert rate to percentage
    rate = float(rate) / 100

    try:
        casual_tax_bracket = CasualsTaxBracket.get_single_casual_taxbrackets(id)
        if not casual_tax_bracket:
            return jsonify(success=False, error="Casuals tax bracket not found"), 404

        is_updated = CasualsTaxBracket.update_casuals_taxbracket(id, lower_bound, upper_bound, rate)
        if not is_updated:
            return jsonify(success=False, error="Failed to update casuals tax bracket"), 500

        current_app.logger.info("Casuals tax bracket updated successfully")
        return jsonify(success=True, message="Casuals tax bracket updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating casuals tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to update casuals tax bracket"), 500

@taxbracket_api_bp.route('/delete_casuals_tax_bracket/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_casuals_tax_bracket(id):
    try:
        casual_tax_bracket = CasualsTaxBracket.get_single_casual_taxbrackets(id)
        if not casual_tax_bracket:
            return jsonify(success=False, error="Casuals tax bracket not found"), 404
        
        is_deleted = CasualsTaxBracket.delete_casuals_taxbracket(id)
        if not is_deleted:
            return jsonify(success=False, error="Failed to delete casuals tax bracket"), 500
        current_app.logger.info("Casuals tax bracket deleted successfully")
        return jsonify(success=True, message="Casuals tax bracket deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving casuals tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve casuals tax bracket"), 500

# ========================= CONSULTANT ===========================
@taxbracket_api_bp.route('/consultant_tax_brackets', methods=['GET'])
@role_required('admin')
def consultant_tax_brackets():
    try:
        consultant_tax_brackets = ConsultantTaxBracket.get_consultant_taxbrackets()
        return jsonify(success=True, data=consultant_tax_brackets, message="Consultant tax brackets retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving consultant tax brackets: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve consultant tax brackets"), 500

@taxbracket_api_bp.route('/consultant_tax_bracket/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_consultant_tax_bracket(id):
    try:
        consultant_tax_bracket = ConsultantTaxBracket.get_single_consultant_taxbrackets(id)
        if not consultant_tax_bracket:
            return jsonify(success=False, error="Consultant tax bracket not found"), 404
        return jsonify(success=True, data=consultant_tax_bracket, message="Consultant tax bracket retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving consultant tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve consultant tax bracket"), 500

@taxbracket_api_bp.route('/add_consultant_tax_bracket', methods=['POST'])
@role_required('admin')
def add_consultant_tax_bracket():
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')

    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')
    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    # Convert rate to percentage
    rate = float(rate) / 100

    try:
        consultant_tax_bracket = ConsultantTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=rate)
        is_inserted = consultant_tax_bracket.add_consultant_taxbracket()
        if not is_inserted:
            return jsonify(success=False, error="Failed to insert consultant tax bracket"), 500

        current_app.logger.info("Consultant tax bracket inserted successfully")
        return jsonify(success=True, message="Consultant tax bracket inserted successfully"), 201
    except Exception as e:
        current_app.logger.error(f"Error inserting consultant tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to insert consultant tax bracket"), 500

@taxbracket_api_bp.route('/update_consultant_tax_bracket/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_consultant_tax_bracket(id):
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')

    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')
    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    # Convert rate to percentage
    rate = float(rate) / 100

    try:
        consultant_tax_bracket = ConsultantTaxBracket.get_single_consultant_taxbrackets(id)
        if not consultant_tax_bracket:
            return jsonify(success=False, error="Consultant tax bracket not found"), 404

        is_updated = ConsultantTaxBracket.update_consultant_taxbracket(id, lower_bound, upper_bound, rate)
        if not is_updated:
            return jsonify(success=False, error="Failed to update consultant tax bracket"), 500

        current_app.logger.info("Consultant tax bracket updated successfully")
        return jsonify(success=True, message="Consultant tax bracket updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating consultant tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to update consultant tax bracket"), 500

@taxbracket_api_bp.route('/delete_consultant_tax_bracket/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_consultant_tax_bracket(id):
    try:
        consultant_tax_bracket = ConsultantTaxBracket.get_single_consultant_taxbrackets(id)
        if not consultant_tax_bracket:
            return jsonify(success=False, error="Consultant tax bracket not found"), 404
        
        is_deleted = ConsultantTaxBracket.delete_consultant_taxbracket(id)
        if not is_deleted:
            return jsonify(success=False, error="Failed to delete consultant tax bracket"), 500
        
        current_app.logger.info("Consultant tax bracket deleted successfully")
        return jsonify(success=True, message="Consultant tax bracket deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving consultant tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve consultant tax bracket"), 500



# ========================= SECOND EMPLOYEE ===========================
@taxbracket_api_bp.route('/second_employee_tax_brackets', methods=['GET'])
@role_required('admin')
def second_employee_tax_bracket():
    try:
        second_employee_tax_brackets = SecondEmployeeTaxBracket.get_second_employee_taxbrackets()
        return jsonify(success=True, data=second_employee_tax_brackets, message="Second employee tax brackets retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving second employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve second employee tax bracket"), 500

@taxbracket_api_bp.route('/second_employee_tax_bracket/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_second_employee_tax_bracket(id):
    try:
        second_employee_tax_bracket = SecondEmployeeTaxBracket.get_single_second_employee_taxbracket(id)
        if not second_employee_tax_bracket:
            return jsonify(success=False, error="Second employee tax bracket not found"), 404

        return jsonify(success=True, data=second_employee_tax_bracket, message="Second employee tax bracket retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving second employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve second employee tax bracket"), 500

@taxbracket_api_bp.route('/insert_second_employee_tax_bracket', methods=['POST'])
def insert_second_employee_tax_bracket():
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')
    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')
    if not is_valid:
        return jsonify(success=False, errors=errors), 400
    # Convert rate to percentage
    rate = float(rate) / 100
    try:
        second_employee_tax_bracket = SecondEmployeeTaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=rate)
        is_inserted = second_employee_tax_bracket.insert_second_employee_taxbracket()
        if not is_inserted:
            return jsonify(success=False, error="Failed to insert second employee tax bracket"), 500
        
        current_app.logger.info("Second employee tax bracket inserted successfully")
        return jsonify(success=True, message="Second employee tax bracket inserted successfully"), 201
    except Exception as e:
        current_app.logger.error(f"Error inserting second employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to insert second employee tax bracket"), 500

@taxbracket_api_bp.route('/update_second_employee_tax_bracket/<uuid:id>', methods=['PUT'])
def update_second_employee_tax_bracket(id):
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')

    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')

    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    # Convert rate to percentage
    rate = float(rate) / 100

    try:
        second_employee_tax_bracket = SecondEmployeeTaxBracket.get_single_second_employee_taxbracket(id)
        if not second_employee_tax_bracket:
            return jsonify(success=False, error="Second employee tax bracket not found"), 404

        is_updated = SecondEmployeeTaxBracket.update_second_employee_taxbracket(id, lower_bound, upper_bound, rate)
        if not is_updated:
            return jsonify(success=False, error="Failed to update second employee tax bracket"), 500

        current_app.logger.info("Second employee tax bracket updated successfully")
        return jsonify(success=True, message="Second employee tax bracket updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating second employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to update second employee tax bracket"), 500

@taxbracket_api_bp.route('/delete_second_employee_tax_bracket/<uuid:id>', methods=['DELETE'])
def delete_second_employee_tax_bracket(id):
    try:
        second_employee_tax_bracket = SecondEmployeeTaxBracket.get_single_second_employee_taxbracket(id)
        if not second_employee_tax_bracket:
            return jsonify(success=False, error="Second employee tax bracket not found"), 404

        is_deleted = SecondEmployeeTaxBracket.delete_second_employee_taxbracket(id)
        if not is_deleted:
            return jsonify(success=False, error="Failed to delete second employee tax bracket"), 500

        current_app.logger.info("Second employee tax bracket deleted successfully")
        return jsonify(success=True, message="Second employee tax bracket deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error deleting second employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to delete second employee tax bracket"), 500


# =================================== TAX BRACKETS ==================================
@taxbracket_api_bp.route('/tax_brackets', methods=['GET'])
def tax_bracket():
    try:
        tax_brackets = TaxBracket.get_taxbrackets()
        return jsonify(success=True, data=tax_brackets, message="Tax brackets retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving tax brackets: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve tax brackets"), 500

@taxbracket_api_bp.route('/tax_bracket/<uuid:id>', methods=['GET'])
def get_tax_bracket(id):
    try:
        tax_bracket = TaxBracket.get_single_taxbracket(id)
        if not tax_bracket:
            return jsonify(success=False, error="Tax bracket not found"), 404
        return jsonify(success=True, data=tax_bracket, message="Tax bracket retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error retrieving tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to retrieve tax bracket"), 500
    
@taxbracket_api_bp.route('/insert_tax_bracket', methods=['POST'])
def insert_tax_bracket():
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')

    print("lower_bound: ", lower_bound)
    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')

    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    # Convert rate to percentage
    rate = float(rate) / 100

    try:
        tax_bracket = TaxBracket(lower_bound=lower_bound, upper_bound=upper_bound, rate=rate)
        is_inserted = tax_bracket.insert_taxbracket()
        if not is_inserted:
            return jsonify(success=False, error="Failed to insert tax bracket"), 500

        current_app.logger.info("Tax bracket inserted successfully")
        return jsonify(success=True, message="Tax bracket inserted successfully"), 201
    except Exception as e:
        current_app.logger.error(f"Error inserting tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to insert tax bracket"), 500

@taxbracket_api_bp.route('/update_permanent_employee_tax_bracket/<uuid:id>', methods=['PUT'])
def update_permanent_employee_tax_bracket(id):
    data = request.get_json()
    lower_bound = data.get('lower_bound')
    upper_bound = data.get('upper_bound')
    rate = data.get('rate')
    is_valid, errors = UserInputValidator.validate({
        'lower_bound': lower_bound,
        'upper_bound': upper_bound,
        'rate': rate
    }, 'add_tax_bracket')
    if not is_valid:
        return jsonify(success=False, errors=errors), 400
    # Convert rate to percentage
    rate = float(rate) / 100
    try:
        tax_bracket = TaxBracket.get_single_taxbracket(id)
        if not tax_bracket:
            return jsonify(success=False, error="Permanent employee tax bracket not found"), 404
        
        is_updated = TaxBracket.update_permanent_employee_taxbracket(id, lower_bound, upper_bound, rate)
        if not is_updated:
            return jsonify(success=False, error="Failed to update permanent employee tax bracket"), 500
        
        return jsonify(success=True, message="Permanent employee tax bracket updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating permanent employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to update permanent employee tax bracket"), 500

@taxbracket_api_bp.route('/delete_permanent_employee_tax_bracket/<uuid:id>', methods=['DELETE'])
def delete_permanent_employee_tax_bracket(id):
    try:
        tax_bracket = TaxBracket.get_single_taxbracket(id)
        if not tax_bracket:
            return jsonify(success=False, error="Permanent employee tax bracket not found"), 404
        
        is_deleted = TaxBracket.delete_permanent_employee_taxbracket(id)
        if not is_deleted:
            return jsonify(success=False, error="Failed to delete permanent employee tax bracket"), 500
        
        current_app.logger.info("Permanent employee tax bracket deleted successfully")
        return jsonify(success=True, message="Permanent employee tax bracket deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error deleting permanent employee tax bracket: {str(e)}")
        return jsonify(success=False, error="Failed to delete permanent employee tax bracket"), 500