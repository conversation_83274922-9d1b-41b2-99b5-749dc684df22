import imghdr
from flask import Blueprint, jsonify, request, current_app
from app.models.central_blogs import BlogCategories, BlogPosts, BlogTags
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


blog_post_api_bp = Blueprint('blog_post_api', __name__)


@blog_post_api_bp.route('/get_all_blog_posts', methods=['GET'])
@role_required('admin')
def get_all_blog_posts():
    """View blog posts"""
    try:
        blogs = BlogPosts.get_blogs()
        blogs = [blog.to_dict() for blog in blogs]
        current_app.logger.info(f"Blogs: {blogs}")

        return jsonify(success=True, data=blogs, message='Blogs retrieved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting blogs: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@blog_post_api_bp.route('/get_blog_post/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_blog_post(id):
    """View blog post details"""
    try:
        blog = BlogPosts.get_blog_by_id(id)
        if not blog:
            return jsonify(success=False, error='Blog not found'), 404
        
        blog = blog.to_dict()
        current_app.logger.info(f"Blog: {blog}")
        return jsonify(success=True, data=blog, message='Blog retrieved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting blog: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@blog_post_api_bp.route('/get_all_blog_categories', methods=['GET'])
@role_required('admin')
def get_all_blog_categories():
    try:
        blog_categories = [blog_category.to_dict() for blog_category in BlogCategories.get_blog_categories()]
        current_app.logger.info(f"Categories: {blog_categories}")
        
        return jsonify(success=True, data=blog_categories, message='Categories retrieved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting categories: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@blog_post_api_bp.route('/get_blog_category/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_blog_category(id):
    try:
        blog_category = BlogCategories.get_category_by_id(id)
        if not blog_category:
            return jsonify(success=False, error='Category not found'), 404
        
        current_app.logger.info(f"Categories: {blog_category.to_dict()}")
        return jsonify(success=True, data=blog_category.to_dict(), message='Categories retrieved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting categories: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@blog_post_api_bp.route('/get_all_blog_tags', methods=['GET'])
@role_required('admin')
def get_all_blog_tags():
    try:
        blog_tags = [blog_tag.to_dict() for blog_tag in BlogTags.get_blog_tags()]
        current_app.logger.info(f"Tags: {blog_tags}")

        return jsonify(success=True, data=blog_tags, message='Tags retrieved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting tags: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@blog_post_api_bp.route('/get_blog_tag/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_blog_tag(id):
    try:
        blog_tag = BlogTags.get_tag_by_id(id)
        if not blog_tag:
            return jsonify(success=False, error='Tag not found'), 404
        
        current_app.logger.info(f"Tags: {blog_tag.to_dict()}")
        return jsonify(success=True, data=blog_tag.to_dict(), message='Tags retrieved successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error getting tags: {str(e)}")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 500

@blog_post_api_bp.route('/upload_image', methods=['POST'])
@role_required('admin')
def upload_image():
    """Upload an image via form to DigitalOcean Spaces"""
    file = request.files.get('file')
    if not file:
        return jsonify(success=False, error='No file selected'), 400
    
    # Validate file type
    file_type = imghdr.what(file)
    if file_type not in ['jpeg', 'png', 'gif']:
        return jsonify(success=False, error='Invalid file type. Only jpeg, png, and gif allowed.'), 400
    
    try:
        image_url = BlogPosts.upload_document(file)
        # Return JSON with the image URL/path
        return jsonify(success=True, data={'image_path': image_url}, message='Image uploaded successfully.'), 200

    except Exception as e:
        current_app.logger.error(f"Upload error: {e}")
        return jsonify(success=False, error=f"Error uploading file"), 500


@blog_post_api_bp.route('/create_blog_tag', methods=['POST'])
@role_required('admin')
def create_blog_tag():
    data = request.get_json()
    name = data.get('name')
    description = data.get('description')

    is_valid, errors = UserInputValidator.validate({
        'name': name,
        'description': description
    }, 'blog_tag')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    try:
        # check if it already exists
        tag = BlogTags.get_tag_by_name(name)
        if tag:
            return jsonify(success=False, error='Tag already exists'), 400
        
        result = BlogTags.add_tag(name, description)
        current_app.logger.info(f"result: {result}")
        
        return jsonify(success=True, data=result.to_dict(), message='Tag created successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error creating blog: {e}")
        return jsonify(success=False, error='An error occurred while creating the blog'), 500


@blog_post_api_bp.route('/create_blog_category', methods=['POST'])
@role_required('admin')
def create_blog_category():
    """Create Blog categories"""
    data = request.get_json()

    category_name = data.get('name')
    category_description = data.get('description')

    is_valid, errors = UserInputValidator.validate({
        'name': category_name,
        'description': category_description
    }, 'blog_tag')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    try:
        # Check existance
        category = BlogCategories.get_category_by_name(category_name.strip())
        if category:
            return jsonify(success=False, error='Category already exists'), 400
        
        result = BlogCategories.add_category(category_name, category_description)
        current_app.logger.info(f"result: {result}")
        
        return jsonify(success=True, data=result.to_dict(), message='Category created successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error creating blog: {e}")
        return jsonify(success=False, error='An error occurred while creating the blog'), 500


@blog_post_api_bp.route('/create_blog_post', methods=['POST'])
@role_required('admin')
def create_blog_post():
    data = request.get_json()

    title = data.get('title')
    content = data.get('content')
    categories = data.get('categories')
    tags = data.get('tags')
    image_path = data.get('imagePath')

    is_valid, errors = UserInputValidator.validate({
        'title': title,
        'content': content,
        'categories': categories,
        'tags': tags,
        'imagePath': image_path
    }, 'blog_post')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    current_app.logger.info(f'image_path: {image_path} and the type is: {type(image_path)}')
    current_app.logger.info(f"categories: {categories} and the type is: {type(categories)}")
    current_app.logger.info(f"tags: {tags} and the type is: {type(tags)}")

    try:
        result = BlogPosts.create_blog(title, content, image_path=image_path, category_ids=categories, tag_ids=tags, user_id=None)
        current_app.logger.info(f"result: {result}")
        
        return jsonify(success=True, data=result.to_dict(), message='Blog created successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error creating blog: {e}")
        return jsonify({'message': 'An error occurred while creating the blog'}), 500


@blog_post_api_bp.route('/update_blog_tag/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_blogtag(id):
    data = request.get_json()

    tag = BlogTags.get_tag_by_id(id)
    if not tag:
        return jsonify(success=False, error='Tag not found'), 404
    
    tag_name = data.get('name')
    tag_description = data.get('description')

    # Validate input data
    is_valid, errors = UserInputValidator.validate({
        'name': tag_name,
        'description': tag_description
    }, 'blog_tag')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    try:
        result = BlogTags.update_tag(id, tag_name, tag_description)
        current_app.logger.info(f"result: {result}")
        return jsonify(success=True, data=result.to_dict(), message='Tag updated successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error updating blog: {e}")
        return jsonify(success=False, error='An error occurred while updating the blog'), 500

@blog_post_api_bp.route('/update_blog_category/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_blogcategory(id):
    data = request.get_json()

    category = BlogCategories.get_category_by_id(id)
    if not category:
        return jsonify(success=False, error='Category not found'), 404
    
    category_name = data.get('name')
    category_description = data.get('description')

    # Validate input data
    is_valid, errors = UserInputValidator.validate({
        'name': category_name,
        'description': category_description
    }, 'blog_tag')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    try:
        result = BlogCategories.update_category(id, category_name, category_description)
        current_app.logger.info(f"result: {result}")
        
        return jsonify(success=True, data=result.to_dict(), message='Category updated successfully.'), 200
    except Exception as e:
        current_app.logger.error(f"Error updating category: {e}")
        return jsonify(success=False, error='An error occurred while updating the blog'), 500

@blog_post_api_bp.route('/update_blog_post/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_blog_post(id):
    return {"id": id}

@blog_post_api_bp.route('/delete_blog_post/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_blog_post(id):
    return {"id": id}