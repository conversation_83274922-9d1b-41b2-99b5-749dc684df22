import json
from functools import wraps
from flask import current_app, jsonify, request
from flask_jwt_extended import verify_jwt_in_request, get_jwt, jwt_required

def role_required(roles: str | list=None, use_refresh: bool=False):
    """
    A decorator to check if the user has one of the allowed roles.

    Args:
        roles (list): A list of allowed roles.

    Returns:
        A decorator that checks if the user has one of the allowed roles.
    """
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                if not isinstance(roles, (list, str)) or not isinstance(use_refresh, bool):
                    return jsonify(success=False, message="Access denied. Invalid or Missing JWT token or Invalid User Role."), 403
            
                if callable(roles):
                    """ If role is function itself """
                    return jsonify(success=False, message="Access denied. Invalid or Missing JWT token or Invalid User Role."), 403
                
                _, _ = verify_jwt_in_request(refresh=use_refresh)
                jwt_payload = get_jwt()
                print("Role Based JWT_PAYLOAD: ", jwt_payload)

                # Check if the user has one of the allowed roles or exact role
                identity = json.loads(jwt_payload.get("sub"))
                if isinstance(roles, list) and identity.get("role") not in roles:
                    return jsonify(success=False, message="Access denied. Invalid or Missing JWT token."),401
                if isinstance(roles, str) and identity.get("role") != roles:
                    return jsonify(success=False, message="Access denied. Invalid or Missing JWT token."),401
                
                # Call the decorated function if the user has one of the allowed roles
                return jwt_required(refresh=use_refresh)(func)(*args, **kwargs)
            except Exception as e:
                current_app.logger.exception("Error in role_required decorator: %s", str(e))
                if "415 Unsupported Media Type" in str(e):
                    return jsonify(success=False, message="Unsupported Media Type. Check the Content-Type header."), 415
                if "400 Bad Request" in str(e):
                    return jsonify(success=False, message="Bad Request. Check the request body."), 400
                return jsonify(sucess=False, message="Access denied. Invalid or Missing JWT token."), 401
                    
        return wrapper
        
    return decorator