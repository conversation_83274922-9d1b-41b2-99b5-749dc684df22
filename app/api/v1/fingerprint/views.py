import os
import requests
from rapidfuzz import fuzz
from dotenv import load_dotenv
from flask import current_app, Blueprint, jsonify, request
from flask_jwt_extended import get_jwt
from app.models.central import Company
from app.models.company import Employee, Attendance
from app.utils.db_connection import DatabaseConnection
from app.helpers.company_helpers import CompanyHelpers
from app.helpers.auxillary import Auxillary
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


load_dotenv()
netpipo_base_url = os.getenv('NETPIPO_BASE_URL')

fingerprint_api_bp = Blueprint('fingerprint', __name__)
db_connection = DatabaseConnection()


@fingerprint_api_bp.route('/upload_employees_data', methods=['POST'])
@role_required('hr')
def upload_employees_data():
    jwt_data = get_jwt()

    # Get database name from jwt
    database_name = jwt_data.get('database_name')
    if not database_name:
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400
    company_id = "24e8a889-ab3f-474f-96bb-61ae0a9b5b6f"
    current_app.logger.info(f"company_id: {company_id}")

    try:
        with db_connection.get_session(database_name) as db_session:
            # get employees
            try:
                employees = Employee.get_employees(db_session)
                current_app.logger.info(f"employees retrieved: {employees}")
                if len(employees) < 0:
                    return jsonify({'message': 'No employees to upload'}), 200
            except Exception as e:
                current_app.logger.info(f"error retrieving employees: {str(e)}")
                return jsonify(success=False, error='Failed to load employees'), 500
            
            #Loop through the employees and save the data in the database
            for employee in employees:
                employee_id = employee['employee_id']
                
                # convert employee_id to string
                employee_id = str(employee_id)
                roll_id = 0
                name = employee['full_name']

                # convert the employee data to a json object
                employee_data = {
                    'employee_id': employee_id,
                    'privilege': roll_id,
                    'name': name,
                    'company_id': company_id
                }

                # Post the data to the endpoint
                url = f"{netpipo_base_url}/addEmployee"
                current_app.logger.info(f"employee data: {employee_data} being posted to {url}")
                try:
                    response = requests.post(url, json=employee_data)
                    current_app.logger.info(f"response: {response.json()}")

                    # Extract userId from the response
                    user_id = response.json().get('userId')
                    current_app.logger.info(f"user_id: {user_id}")
                except Exception as e:
                    current_app.logger.info(f"error posting employee data: {str(e)}")
                    return jsonify(success=False, error='Failed to upload employees'), 500
                
                try:
                    # send the employee to the device
                    url2 = f"{netpipo_base_url}/setOneUserJson"
                    payload = {
                        "enrollId": user_id,
                        "backupNum": -1,
                        "deviceSn": "AYSK26020937"
                    }
                    
                    response2 = requests.post(url2, json=payload)
                    current_app.logger.info(f"response2: {response2.json()}")
                except Exception as e:
                    current_app.logger.info(f"error sending data to device: {str(e)}")
                    return jsonify(success=False, error='Failed to upload employees'), 500

            return jsonify(success=True, message='Employees uploaded successfully'), 200
    except Exception as e:
        current_app.logger.info(f"error uploading employees: {str(e)}")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 500


@fingerprint_api_bp.route('/send_fingerprint_data', methods=['GET'])
def send_fingerprint_data():
    """Send fingerprint data to the fingerprint device"""
    url1 = f"{netpipo_base_url}/setOneUserJson"
    url2 = f"{netpipo_base_url}/get_users"
    company_id = "afe58ab2-cee6-43a6-b114-876ef5fcf87e"

    # Retrieve employees data from the database with the company_id as the argument
    args = { "company_id": company_id }
    try:
        response = requests.get(url2, params=args)
        data = response.json()  # Convert response to JSON
        current_app.logger.info(f"response: {response}")
        current_app.logger.info(f"data: {data}")
    except Exception as e:
        current_app.logger.info(f"error getting data: {str(e)}")
        return jsonify(success=False, error='Error getting data'), 500

    # Extracting person details
    persons_data = data.get("extend", {}).get("persons", [])
    current_app.logger.info(f"persons_data: {persons_data}")

    for person in persons_data:
        enrollId = person.get("id")
        backupNum = -1
        deviceSn = "AYSK26020938"

        # Send the data to the device
        payload = {
            "enrollId": enrollId,
            "backupNum": backupNum,
            "deviceSn": deviceSn
        }
        try:
            response = requests.post(url1, json=payload)
            current_app.logger.info(f"response: {response.json()}")
        except Exception as e:
            current_app.logger.info(f"error sending data: {str(e)}")
            continue # Skip current post request giving out error

    return jsonify(success=True, message='Fingerprint data sent successfully'), 200


@fingerprint_api_bp.route('/get_daily_fingerprint_data', methods=['GET'])
def get_daily_fingerprint_data():
    """Get daily fingerprint data from the fingerprint device"""
    url = f"{netpipo_base_url}/daily_records"
    url2 = f"{netpipo_base_url}/get_companies"
    all_results = []

    try:
        # Get all companies in a single request
        response = requests.get(url2)
        companies_data = response.json().get("companies", [])
        current_app.logger.info(f"Retrieved {len(companies_data)} companies")

        # Filter companies to only those with non-empty devices list
        companies_with_devices = [company for company in companies_data if company.get("devices", [])]
        current_app.logger.info(f"Filtered to {len(companies_with_devices)} companies with devices")

        if not companies_with_devices:
            return jsonify(success=False, error='No companies with devices found'), 404
        
        # Process each company with devices
        processed_count = 0
        for company in companies_with_devices:
            company_id = str(company["company_id"])
            database_name = company["database_name"]
            devices = company.get("devices", [])
            device_sns = [device["device_sn"] for device in devices]
            current_app.logger.info(f"Processing company_id: {company_id}, database_name: {database_name}, devices: {device_sns}")

            try:
                # Get fingerprint records for this company
                params = {"company_id": company_id}
                response = requests.get(url, params=params)

                if response.status_code != 200:
                    current_app.logger.error(f"Error getting records for company {company_id}: {response.text}")
                    continue

                records = response.json().get('extend', {}).get('records', [])

                if not records:
                    current_app.logger.info(f"No records found for company {company_id}")
                    continue

                current_app.logger.info(f"Found {len(records)} records for company {company_id}")

                # Process records for this company
                company_results = process_company_records(db_connection, database_name, records)
                all_results.extend(company_results)
                processed_count += 1
            except Exception as e:
                current_app.logger.error(f"Error processing company {company_id}: {str(e)}")
                # Continue with next company instead of returning an error
                continue
        response_data = {
            'results': all_results,
            'companies_processed': processed_count,
            'records_processed': len(all_results)
        }
        # Everything goes well, return response to the client
        return jsonify(success=True, data=response_data, message='Fingerprint data retrieved successfully'), 200
    except Exception as e:
        current_app.logger.error(f"Error in get_daily_fingerprint_data: {str(e)}")
        return jsonify(success=False, error='An error occurred. Try again later'), 500


@fingerprint_api_bp.route('/register_device', methods=['POST'])
@role_required('admin')
def register_device():
    """Register a device with the fingerprint device."""
    request_payload = request.get_json()

    # input request_payload validation
    is_valid, errors = UserInputValidator.validate(request_payload, "register_device")
    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    company_id = request_payload.get("company_id")
    device_sn = request_payload.get("device_sn")

    ADD_COMPANY_DEVICE_URL = f"{netpipo_base_url}/add_company_device"
    GET_COMPANIES_URL = f"{netpipo_base_url}/get_companies"
    ADD_COMPANY_URL = f"{netpipo_base_url}/add_company"

    companies = Company.get_companies()

    try:
        response = requests.get(GET_COMPANIES_URL)
        current_app.logger.info(f"response: {response.json()}")
        companies = response.json().get('companies', [])
        # Create a dictionary for fast lookup
        company_data = {company['company_id']: company['database_name'] for company in companies}
        
        # Check if company exists
        database_name = company_data.get(company_id)
        if database_name:
            try:
                response2 = requests.post(ADD_COMPANY_DEVICE_URL, json=request_payload)
                current_app.logger.info(f"response2: {response2.json()}")
                # check if the device was registered successfully
                registered = response2.json().get('message')
                current_app.logger.info(f"registered: {registered}")
                if 'added successfully' not in registered:
                    current_app.logger.info("inside the if statement where device was not registered successfully")
                    return jsonify(success=False, error='An error occurred. Try again later'), 500
                
                current_app.logger.info("inside the if statement where device was registered successfully")
                # Upload employees data
                try:
                    result = CompanyHelpers.process_and_send_employees(database_name, company_id, device_sn)
                    current_app.logger.info(f"result: {result}")
                    if len(result) > 0:
                        return jsonify(success=True, message='Device registered successfully'), 200

                    jsonify(success=False, error='An error occurred. Try again later'), 500
                except Exception as e:
                    current_app.logger.info(f"error uploading employees: {str(e)}")
                    return jsonify(success=False, error='An error occurred. Try again later'), 500
            except Exception as e:
                current_app.logger.info(f"error registering device: {str(e)}")
                return jsonify(success=False, error='An error occurred. Try again later'), 500
        else:
            current_app.logger.info(f"The company is not registered")
            # Create the company and register the device if the company_id does not exist            
            # Get the database of the company
            company = Company.get_company_by_id(company_id)
            database_name = company['database_name']
            company_name = company['company_name']
            data2 = {
                "company_id": company_id,
                "company_name": company_name,
                "database_name": database_name
            }
            current_app.logger.info(f"data2 to be posted during company registration: {data2}")
            try:
                # add the company in the database
                add_company_response = requests.post(ADD_COMPANY_URL, json=data2)
                current_app.logger.info(f"response3: {add_company_response.json()}")
                
                # Register the device to company_device table
                add_company_device_response = requests.post(ADD_COMPANY_DEVICE_URL, json=request_payload)
                current_app.logger.info(f"response4: {add_company_device_response.json()}")
                
                # check if the device was registered successfully
                registered = add_company_device_response.json().get('message')
                current_app.logger.info(f"registered response: {registered}")

                if 'added successfully' not in registered:
                    return jsonify(success=False, error=add_company_device_response.json()['message']), 400

                # Upload employees data
                try:
                    result = CompanyHelpers.process_and_send_employees(database_name, company_id, device_sn)
                    current_app.logger.info(f"result: {result}")
                    if len(result) > 0:
                        return jsonify(success=True, message='Device registered successfully'), 200
                    return jsonify(success=False, error='An error occurred. Try again later'), 500
                except Exception as e:
                    current_app.logger.info(f"error uploading employees: {str(e)}")
                    return jsonify(success=False, error='An error occurred. Try again later'), 500
            except Exception as e:
                current_app.logger.info(f"error registering device: {str(e)}")
                return jsonify(success=False, error='An error occurred. Try again later'), 500
    except Exception as e:
        current_app.logger.info(f"error getting companies: {str(e)}")
        return jsonify(success=False, error='An error occurred. Try again later'), 500


def process_company_records(db_connection, database_name, records):
    """Process fingerprint records for a specific company.

    Args:
        db_connection: DatabaseConnection instance
        database_name: The company's database name
        records: List of fingerprint records

    Returns:
        List of processed results
    """
    results = []
    current_app.logger.info(f"Processing {len(records)} records for database {database_name}")

    # Log all records for debugging
    for i, record in enumerate(records):
        current_app.logger.info(f"Record {i+1}: employee_id={record.get('employee_id')}, name={record.get('name')}, time={record.get('records_time')}")

    try:
        # Use a single database session for all operations
        with db_connection.get_session(database_name) as db_session:
            # Get all employees once
            employees = Employee.get_employees(db_session)
            current_app.logger.info(f"Retrieved {len(employees)} employees from database")

            # Create a lookup dictionary for faster matching
            employee_lookup = {str(emp['employee_id']): emp for emp in employees}
            current_app.logger.info(f"Employee lookup keys: {list(employee_lookup.keys())}")

            # Process each record
            for record in records:
                record_employee_id = record.get('employee_id')
                record_name = record.get('name', 'Unknown')

                if not record_employee_id:
                    current_app.logger.info(f"Record for {record_name} has no employee_id")
                    continue

                # Convert to string for comparison
                record_employee_id = str(record_employee_id)
                current_app.logger.info(f"Processing record for employee_id: {record_employee_id}, name: {record_name}")

                # Check if employee exists using the lookup dictionary (O(1) operation)
                if record_employee_id in employee_lookup:
                    employee = employee_lookup[record_employee_id]
                    employee_id = employee['employee_id']
                    current_app.logger.info(f"Found employee in database: {employee['full_name']}")

                    try:
                        # Record attendance
                        device_used = "Netpipo"
                        location = "Office"
                        record_time = record['records_time']
                        current_app.logger.info(f"Recording attendance for {employee['full_name']} at {record_time}")

                        # Clock in
                        result = Attendance.clockin2(db_session, employee_id, location, device_used, record_time)
                        current_app.logger.info(f"Clockin2 result for {employee['full_name']}: {result}")

                        results.append({
                            'employee_id': employee_id,
                            'full_name': employee['full_name'],
                            'result': result
                        })

                        # Handle clock-out if necessary
                        if "already clocked in and have not clocked out yet" in result:
                            try:
                                current_app.logger.info(f"Attempting to clock out {employee['full_name']}")
                                clock_out_result = Attendance.clockout2(db_session, employee_id, location, record_time)
                                current_app.logger.info(f"Clock-out recorded for {employee['full_name']}: {clock_out_result}")
                            except Exception as e:
                                current_app.logger.error(f"Error recording clock-out for {employee['full_name']}: {str(e)}")

                    except Exception as e:
                        current_app.logger.error(f"Error recording attendance for {employee['full_name']}: {str(e)}")
                else:
                    # Employee not found in database - log a more detailed warning
                    current_app.logger.warning(f"Employee with ID {record_employee_id} (name: {record_name}) not found in database")
                    current_app.logger.warning(f"This employee exists in the fingerprint device but not in the database.")
                    current_app.logger.warning(f"To fix this, add the employee to the database with ID: {record_employee_id}")

                    # Add to results so the issue is visible in the API response
                    results.append({
                        'employee_id': record_employee_id,
                        'full_name': record_name,
                        'result': f"Employee not found in database. Add employee with ID {record_employee_id} to the database."
                    })

    except Exception as e:
        current_app.logger.error(f"Error processing records for database {database_name}: {str(e)}")

    current_app.logger.info(f"Processed {len(results)} results: {results}")
    return results