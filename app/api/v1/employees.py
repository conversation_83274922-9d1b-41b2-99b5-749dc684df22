from flask import Blueprint, request, jsonify, current_app
from app.models.central import Company
from app.models.company import DynamicBase, Employee
from app.utils.db_connection import DatabaseConnection
import logging
from app.helpers.company_helpers import CompanyHelpers

employee_bp = Blueprint('employee', __name__)

@employee_bp.route('/api/v1/register_employee', methods=['POST'])
def register_employee():
    """Register a new employee."""
    data = request.get_json()

    try:
        # Extract employee details from the request data
        company_id = data.get('company_id')
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        nid = data.get('nid')
        nsf = data.get('nsf')
        birth_date = data.get('birth_date')
        marital_status = data.get('marital_status')
        gender = data.get('gender')
        employee_tin = data.get('employee_tin')
        employee_type = data.get('employee_type')
        basic_salary = data.get('basic_salary')
        transport_allowance = data.get('transport_allowance', 0.0)
        housing_allowance = data.get('housing_allowance', 0.0)
        communication_allowance = data.get('communication_allowance', 0.0)
        over_time = data.get('over_time', 0.0)
        other_allowance = data.get('other_allowance', 0.0)
        email = data.get('email')
        phone = data.get('phone')
        job_title = data.get('job_title')
        hire_date = data.get('hire_date')

        # Initialize the database connection
        db_connection = DatabaseConnection()

        # Create a new employee record
        new_employee = Employee(
            company_id=company_id,
            first_name=first_name,
            last_name=last_name,
            nid=nid,
            nsf=nsf,
            birth_date=birth_date,
            marital_status=marital_status,
            gender=gender,
            employee_tin=employee_tin,
            employee_type=employee_type,
            basic_salary=basic_salary,
            transport_allowance=transport_allowance,
            housing_allowance=housing_allowance,
            communication_allowance=communication_allowance,
            over_time=over_time,
            other_allowance=other_allowance,
            email=email,
            phone=phone,
            job_title=job_title,
            hire_date=hire_date
        )

        try:
            # Get the company database name
            database_name = CompanyHelpers.get_company_database_name(company_id)
            print(f"Database name: {database_name}")
        except Exception as e:
            logging.error(f"Error getting company database name: {e}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
        
        try:
            with db_connection.get_session(database_name) as session:
                session.add(new_employee)
                session.commit()

            return jsonify({'message': 'Employee registered successfully.'}), 201
        except Exception as e:
            logging.error(f"Error In adding employee: {e}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500

    except Exception as e:
        logging.error(f"Error: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@employee_bp.route('/api/v1/get_employees/<uuid:company_id>', methods=['GET'])
def get_employees(company_id):
    """Get all employees from the database."""
    try:
        # Initialize the database connection
        db_connection = DatabaseConnection()

        database_name = CompanyHelpers.get_company_database_name(company_id)

        with db_connection.get_session(database_name) as session:
            employees = session.query(Employee).all()
            converted = [employee.to_dict() for employee in employees]
            return jsonify(converted), 200
        
    except Exception as e:
        logging.error(f"Error getting employees: {e}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    