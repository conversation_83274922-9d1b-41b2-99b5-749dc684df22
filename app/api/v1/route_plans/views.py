from flask import Blueprint, request, jsonify, current_app
from app.models.central import RoutePlanRequirement, Plans
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


route_plan_requirements_api_bp = Blueprint('route_plan_requirements', __name__)


@route_plan_requirements_api_bp.route('/route_plan_requirements', methods=['GET'])
@role_required('admin')
def get_route_plan_requirements():
    try:
        route_plan_requirements = RoutePlanRequirement.get_route_plan_requirements()
        return jsonify(success=True, data=route_plan_requirements, message="Route plan requirements retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error getting route plan requirements: {e}")
        return jsonify(success=False, error='Error getting route plan requirements'), 500

@route_plan_requirements_api_bp.route('/route_plan_requirement/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_route_plan_requirement(id):
    try:
        route_plan_requirement = RoutePlanRequirement.get_route_plan_requirement_by_id(id)
        if not route_plan_requirement:
            return jsonify(success=False, error='Route plan requirement not found'), 404
        
        return jsonify(success=True, data=route_plan_requirement.to_dict(), message="Route plan requirement retrieved successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error getting route plan requirement: {e}")
        return jsonify(success=False, error='Error getting route plan requirement'), 500
    
@route_plan_requirements_api_bp.route('/add_route_plan_requirement', methods=['POST'])
@role_required('admin')
def add_route_plan_requirement():
    data = request.get_json()
    route_path = data.get("route_path")
    required_plan_id = data.get("required_plan_id")
    description = data.get("description")

    is_valid, errors = UserInputValidator.validate({
        'route_path': route_path,
        'required_plan_id': required_plan_id,
        'description': description
    }, 'add_route_plan_requirement')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    try:
        # Check if required_plan_id really exists
        plan = Plans.get_plan_by_id(required_plan_id)
        if not plan:
            return jsonify(success=False, error='Required plan does not exist'), 400
        # Attempt to add route plan requirement
        result = RoutePlanRequirement.add_route_plan_requirement(route_path, required_plan_id, description)
        if result:
            return jsonify(success=True, error='Route path requirement added successfully'), 200
        
        return jsonify(success=False, error='Error adding route path requirement'), 500
    except Exception as e:
        current_app.logger.error(f"Error adding route plan requirement: {e}")
        return jsonify(success=False, error='Error adding route plan requirement'), 500

@route_plan_requirements_api_bp.route('/update_route_plan_requirement/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_route_plan_requirement(id):
    route_plan_req = RoutePlanRequirement.get_route_plan_requirement_by_id(id)
    if not route_plan_req:
        return jsonify(success=False, error='Route plan requirement not found'), 404
    
    data = request.get_json()
    route_path = data.get("route_path")
    required_plan_id = data.get("required_plan_id")
    description = data.get("description")

    is_valid, errors = UserInputValidator.validate({
        'route_path': route_path,
        'required_plan_id': required_plan_id,
        'description': description
    }, 'add_route_plan_requirement')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    try:
        # Check if required_plan_id really exists
        plan = Plans.get_plan_by_id(required_plan_id)
        if not plan:
            return jsonify(success=False, error='Required plan does not exist'), 400
        # Attempt to update route plan requirement
        updated_req = RoutePlanRequirement.update_route_plan_requirement(id, route_path, required_plan_id, description)
        if not updated_req:
            return jsonify(success=False, error="Failed to update route plan requirement"), 500
        
        return jsonify(success=True, data=updated_req.to_dict(), message="Route plan requirement updated successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error updating route plan requirement: {e}")
        return jsonify(success=False, error='Error updating route plan requirement'), 500


@route_plan_requirements_api_bp.route('/delete_route_plan_requirement/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_route_plan_requirement(id):
    route_plan_req = RoutePlanRequirement.get_route_plan_requirement_by_id(id)
    if not route_plan_req:
        return jsonify(success=False, error='Route plan requirement not found'), 404
    
    try:
        # Attempt to delete route plan requirement
        is_deleted = RoutePlanRequirement.delete_route_plan_requirement(id)
        if not is_deleted:
            return jsonify(success=False, error="Failed to delete route plan requirement"), 500
        
        return jsonify(success=True, message="Route plan requirement deleted successfully"), 200
    except Exception as e:
        current_app.logger.error(f"Error deleting route plan requirement: {e}")
        return jsonify(success=False, error='Error deleting route plan requirement'), 500


# ======================= FUTURE MOVE =========================
@route_plan_requirements_api_bp.route('/route_plan_requirements/<uuid:plan_id>', methods=['GET'])
@role_required('admin')
def get_route_plan_requirements_by_plan_id(plan_id):
    pass # This will be additional after existing parts are done
