from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt, current_user
from app.models.company import Insurance
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


insurance_api_bp = Blueprint('insurance', __name__)
db_connection = DatabaseConnection()

@insurance_api_bp.route('/insurances', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_insurances():
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    
    with db_connection.get_session(database_name) as db_session:
        try:
            insurances = Insurance.get_insurances(db_session)
            current_app.logger.info("Insurance: ", insurances)
            return jsonify(success=False, data=insurances, message="Insurances fetched successfully"), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while fetching insurances: {str(e)}')
            return jsonify(success=False, error="An error occurred. Try again or re-login"), 500
        
@insurance_api_bp.route('/insurances/<uuid:insurance_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_single_insurance(insurance_id):
    database_name = get_jwt().get('database_name')
    
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again later"), 400
    
    with db_connection.get_session(database_name) as db_session:
        try:
            insurance = Insurance.get_insurance_by_id(db_session, insurance_id)
            if not insurance:
                return jsonify(success=False, error="Insurance not found"), 404
            
            current_app.logger.info("Insurance: ", insurance)
            return jsonify(success=False, data=insurance.to_dict(), message="Insurance fetched successfully"), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while fetching insurances: {str(e)}')
            return jsonify(success=False, error="An error occurred. Try again later"), 500

@insurance_api_bp.route('/add_insurance', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_insurance():
    jwt_data = get_jwt()
    data = request.json
    database_name = jwt_data.get('database_name')
    company_id = jwt_data.get('company_id')
    
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    if not company_id:
        current_app.logger("Company ID not found in JWT")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400
    
    insurance_name = data.get("insurance_name")
    employee_rate = data.get("employee_rate")
    employer_rate = data.get("employer_rate")

    is_valid, errors = UserInputValidator.validate({
        "insurance_name": insurance_name,
        "employee_rate": employee_rate,
        "employer_rate": employer_rate
    }, 'add_insurance')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Make sure the insurance name is lowercase
    insurance_name = insurance_name.lower()
    with db_connection.get_session(database_name) as db_session:
        try:
            insurance = Insurance.insert_insurance(db_session, insurance_name, employee_rate, employer_rate)
            current_app.logger.info("Insurance: ", insurance)
            return jsonify(success=False, data=insurance.to_dict(), message="Insurance added successfully"), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while adding insurances: {str(e)}')
            return jsonify(success=False, error="An error occurred. Try again or re-login"), 500
        
@insurance_api_bp.route('/update_insurance/<uuid:insurance_id>', methods=['PUT'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_insurance(insurance_id):
    """Update the insurance company."""
    jwt_data = get_jwt()
    data = request.json
    database_name = jwt_data.get('database_name')
    company_id = jwt_data.get('company_id')
    
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    if not company_id:
        current_app.logger("Company ID not found in JWT")
        return jsonify(success=False, error='An error occurred. Try again or re-login'), 400
    
    insurance_name = data.get("insurance_name")
    employee_rate = data.get("employee_rate")
    employer_rate = data.get("employer_rate")

    is_valid, errors = UserInputValidator.validate({
        "insurance_name": insurance_name,
        "employee_rate": employee_rate,
        "employer_rate": employer_rate
    }, 'add_insurance')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Make sure the insurance name is lowercase
    insurance_name = insurance_name.lower()
    with db_connection.get_session(database_name) as db_session:
        try:
            insurance = Insurance.update_insurance(db_session,
                                                   insurance_id,
                                                   insurance_name=insurance_name,
                                                   employee_rate=employee_rate,
                                                   employer_rate=employer_rate)
            current_app.logger.info("Insurance: ", insurance)
            return jsonify(success=False, data=insurance.to_dict(), message="Insurance updated successfully"), 200
        except Exception as e:
            current_app.logger.error(f'An error occurred while updating insurances: {str(e)}')
            return jsonify(success=False, error="An error occurred. Try again later"), 500


@insurance_api_bp.route('/delete_insurance/<uuid:insurance_id>', methods=['DELETE'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_insurance(insurance_id):
    """Delete the insurance company."""
    try:
        database_name = get_jwt().get('database_name')
        if database_name is None:
            current_app.logger.error("An error occurred while fetching company database name")
            return jsonify(success=False, error="An error occurred. Try again or re-login"), 400

        # Fetch the insurance object within a session context
        with db_connection.get_session(database_name) as db_session:
            insurance_to_delete = Insurance.get_insurance_by_id(db_session, insurance_id)
            if insurance_to_delete is None:
                current_app.logger.error("Insurance company not found.", 'danger')
                return jsonify(success=False, error="Insurance company not found."), 404

            db_session.delete(insurance_to_delete)
            db_session.commit()
            current_app.logger.info('Insurance company deleted successfully', 'success')
            return jsonify(success=True, message='Insurance company deleted successfully'), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred while deleting insurance company: {e}")
        return jsonify(success=False, error="An error occurred. Try again later")