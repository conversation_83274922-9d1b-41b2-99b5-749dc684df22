from itertools import groupby
from operator import itemgetter
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt, current_user
from app.api.v1.decorators.auth_decorators import role_required
from app.models.company_approval_work_flow import ApprovalWorkflow
from app.models.central import UserRole as CentralUserRole, ApprovalType
from app.utils.db_connection import DatabaseConnection
from app.api_helpers.ApiHelpers import UserInputValidator

db_connection = DatabaseConnection()
approval_work_flow_api_bp = Blueprint("approval_work_flow_api", __name__)

@approval_work_flow_api_bp.route("/get_approval_workflow", methods=["GET"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_approval_workflow():
    """Get all approval workflows."""
    jwt_data = get_jwt()
    database_name = jwt_data.get("database_name")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Retrieve workflows from the database
            workflows = ApprovalWorkflow.get_all_workflows(db_session)  # Assuming this returns a list of dicts
            # Log the retrieved workflows
            current_app.logger.info(f"Workflows: {workflows}")

            # Group workflows by `approval_type` (for dictionaries, use `itemgetter`)
            grouped_workflows = {
                k: list(v) for k, v in groupby(sorted(workflows, key=itemgetter("approval_type")), key=itemgetter("approval_type"))
            }
            current_app.logger.info(f"Grouped Workflows: {grouped_workflows}")
            return jsonify(success=True, data=grouped_workflows, message="Workflows retrieved successfully."), 200
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflows: {e}")
            return jsonify(success=False, error="Error retrieving workflows."), 400


@approval_work_flow_api_bp.route("/get_approval_workflow_by_id/<uuid:id>", methods=["GET"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_one_approval_workflow(id):
    """Get all approval workflows."""
    jwt_data = get_jwt()
    database_name = jwt_data.get("database_name")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            workflow = ApprovalWorkflow.get_workflow_by_id(db_session, id)
            # Log the retrieved workflows
            current_app.logger.info(f"Workflows: {workflow}")

            return jsonify(success=True, data=workflow, message="Workflow retrieved successfully."), 200
        except Exception as e:
            current_app.logger.error(f"Error retrieving workflow: {e}")
            return jsonify(success=False, error="Error retrieving workflow."), 400
        

# ==============================================================================
@approval_work_flow_api_bp.route("/create_workflow", methods=["POST"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def create_workflow():
    """Create a new workflow."""
    data = request.get_json()
    jwt_data = get_jwt()

    current_app.logger.info(f"Submitted Form Data: {data}")
    database_name = jwt_data.get("database_name")

    is_valid, errors = UserInputValidator.validate(data, 'create_update_workflow')
    if not is_valid:
        return jsonify(success=False, error=errors), 400

    approval_type = data.get('approval_type').strip()
    role = data.get('role').strip()
    sequence_order = int(data.get('sequence_order'))

    # Additional checks
    try:
        is_approval_type_exists = ApprovalType.get_approval_type_by_id(approval_type)
        if not is_approval_type_exists:
            return jsonify(success=False, error="Approval does not exists."), 400
        is_role_exists = CentralUserRole.get_user_role_by_id(role)
        if not is_role_exists:
            return jsonify(success=False, error="Role does not exist."), 400
        approval_type=is_approval_type_exists['approval_type']
        role=is_role_exists['role_name']
    except Exception as e:
        current_app.logger.error(f"Error retrieving roles or approval types: {e}")
        return jsonify(success=False, error="Error retrieving roles or approval types."), 500
    
    # connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            new_workflow = ApprovalWorkflow.create_workflow(db_session, approval_type, role, sequence_order)
            current_app.logger.info(f"New Workflow: {new_workflow}")
            if len(new_workflow) < 0:
                return jsonify(success=False, error="Error creating workflow."), 500
            
            if "error" in new_workflow:
                return jsonify(success=False, error="Workflow already exists."), 400
            
            return jsonify(success=True, data=new_workflow, message="Workflow created successfully."), 200
        except Exception as e:
            current_app.logger.error(f"Error creating workflow: {e}")
            return jsonify(success=False, error="Error creating workflow."), 500


@approval_work_flow_api_bp.route("/update_workflow/<uuid:workflow_id>", methods=["PUT"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_workflow(workflow_id):
    """Update a workflow."""
    jwt_data = get_jwt()
    data = request.get_json()
    database_name=jwt_data.get('database_name')
    
    current_app.logger.info(f"Submitted Form Data: {data}")

    # validate data
    is_valid, errors = UserInputValidator.validate(data, 'create_update_workflow')
    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    approval_type = data.get('approval_type')
    role = data.get('role')
    sequence_order = data.get('sequence_order')

    # Additional checks
    try:
        is_approval_type_exists = ApprovalType.get_approval_type_by_id(approval_type)
        if not is_approval_type_exists:
            return jsonify(success=False, error="Approval does not exists."), 400
        is_role_exists = CentralUserRole.get_user_role_by_id(role)
        if not is_role_exists:
            return jsonify(success=False, error="Role does not exist."), 400
        approval_type=is_approval_type_exists['approval_type']
        role=is_role_exists['role_name']
        current_app.logger.info(f"Approval Type: {approval_type}, Role: {role}")
    except Exception as e:
        current_app.logger.error(f"Error retrieving roles or approval types: {e}")
        return jsonify(success=False, error="Error retrieving roles or approval types."), 500
    
    # connect to the database
    with db_connection.get_session(database_name) as db_session:        
        # connect to the database
        with db_connection.get_session(database_name) as db_session:
            try:
                workflow = ApprovalWorkflow.get_workflow_by_id(db_session, workflow_id)
                if not workflow:
                    return jsonify(success=False, error="Workflow not found."), 404
                
                # Avoid having duplicates
                is_unique = ApprovalWorkflow.is_workflow_unique(db_session, approval_type, role, sequence_order)
                if not is_unique:
                    return jsonify(success=False, error="Workflow already exists"), 400
                
                updated_workflow = ApprovalWorkflow.update_workflow(db_session, workflow_id, approval_type, role, sequence_order)
                current_app.logger.info(f"Updated Workflow: {updated_workflow}")
                if len(updated_workflow) > 0:
                    return jsonify(success=True, data=updated_workflow, message="Workflow updated successfully."), 200

                return jsonify(success=False, error="Error updating workflow."), 500
            except Exception as e:
                current_app.logger.error(f"Error updating workflow: {e}")
                return jsonify(success=False, error="Error updating workflow."), 500
            

@approval_work_flow_api_bp.route("/delete_workflow/<uuid:workflow_id>", methods=["DELETE"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_workflow(workflow_id):
    """Delete a workflow."""
    jwt_data = get_jwt()
    database_name = jwt_data.get("database_name")

    # Connect to the database
    with db_connection.get_session(database_name) as db_session:
        try:
            # Check if it exists before continuing
            workflow = ApprovalWorkflow.get_workflow_by_id(db_session, workflow_id)
            if not workflow:
                return jsonify(success=False, error="Workflow not found."), 404
            
            deleted_workflow = ApprovalWorkflow.delete_workflow(db_session, workflow_id)
            current_app.logger.info(f"Deleted Workflow: {deleted_workflow}")
            if deleted_workflow:
                return jsonify(success=True, data=deleted_workflow, message="Workflow deleted successfully."), 200

            return jsonify(success=False, error="Error deleting workflow."), 500
        except Exception as e:
            current_app.logger.error(f"Error deleting workflow: {e}")
            return jsonify(success=False, error="Error deleting workflow."), 500