from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt, current_user
from app.models.company import Reimbursements, Employee
from app.utils.db_connection import DatabaseConnection
from app.helpers.auxillary import Auxillary
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator


reimbursement_api_bp = Blueprint('reimbursements', __name__)
db_connection = DatabaseConnection()


@reimbursement_api_bp.route('/reimbursements', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def reimbursements():
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    
    try:
        with db_connection.get_session(database_name) as db_session:
            reimbursements = Reimbursements.get_reimbursements(db_session)
            return jsonify(success=True, reimbursements=reimbursements, message="Reimbursements fetched successfully."), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred while fetching reimbursements: {str(e)}")
        return jsonify(success=False, error="An error occurred. Try again later"), 400
    
@reimbursement_api_bp.route('/reimbursement/<uuid:reimbursement_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def reimbursement(reimbursement_id):
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    
    try:
        with db_connection.get_session(database_name) as db_session:
            reimbursement = Reimbursements.get_reimbursement_by_id(db_session, reimbursement_id)
            return jsonify(success=True, reimbursement=reimbursement.to_dict(), message="Reimbursement fetched successfully."), 200
    except Exception as e:
        current_app.logger.error(f"An error occurred while fetching reimbursement: {str(e)}")
        return jsonify(success=False, error="An error occurred. Try again later"), 400

@reimbursement_api_bp.route('/add_reimbursements', methods=['POST'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_reimbursements():
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    if not company_id:
        return jsonify(success=False, error='Try again later or re-login'), 500

    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400
    
    data = request.get_json()
    employee_id = data.get('employee_id')
    description = data.get('description')
    reimbursement_amount = data.get('reimbursement_amount')
    reimbursement_date = data.get('reimbursement_date')

    is_valid, errors = UserInputValidator.validate({
        'employee_id': employee_id,
        'description': description,
        'reimbursement_amount': reimbursement_amount,
        'reimbursement_date': reimbursement_date
    }, 'add_reimbursement')
    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)
        if not employees:
            error = "No employees found. Please add employees before recording reimbursements."
            return jsonify(success=False, error=error), 400
    
        try:
            reimbursement = Reimbursements.add_reimbursement(db_session, employee_id, description, reimbursement_amount, reimbursement_date)
            current_app.logger.info(f"Reimbursement added successfully: {reimbursement}")
            return jsonify(success=True, message="Reimbursement added successfully."), 200
        
        except Exception as e:
            current_app.logger.error(f"An error occurred while adding reimbursement: {str(e)}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500
        

@reimbursement_api_bp.route('/update_reimbursement/<uuid:reimbursement_id>', methods=['PUT'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_reimbursement(reimbursement_id):
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    if not company_id:
        return jsonify(success=False, error='Try again later or re-login'), 500

    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400

    data = request.get_json()
    employee_id = data.get('employee_id')
    description = data.get('description')
    reimbursement_amount = data.get('reimbursement_amount')
    reimbursement_date = data.get('reimbursement_date')
    
    is_valid, errors = UserInputValidator.validate({
        'employee_id': employee_id, # Allow to update employee | maybe HR can mistakenly approve reimbursements
        'description': description,
        'reimbursement_amount': reimbursement_amount,
        'reimbursement_date': reimbursement_date
    }, 'add_reimbursement')
    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    with db_connection.get_session(database_name) as db_session:
        reimbursement_to_update = Reimbursements.get_reimbursement_by_id(db_session, reimbursement_id)
        if reimbursement_to_update is None:
            current_app.logger.error("No reimbursement found")
            return jsonify(success=False, error="No reimbursement found"), 404
        
        try:
            updated = Reimbursements.update_reimbursement_for_api(db_session, employee_id, reimbursement_id, description,
                                                           reimbursement_amount, reimbursement_date)
            current_app.logger.info(f"Reimbursement updated successfully: {updated}")
            return jsonify(success=True, data=updated.to_dict(), message="Reimbursement updated successfully."), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while updating reimbursement: {str(e)}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500


@reimbursement_api_bp.route('/delete_reimbursement/<uuid:reimbursement_id>', methods=['DELETE'], strict_slashes=False)
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_reimbursement(reimbursement_id):
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    if not company_id:
        return jsonify(success=False, error='Try again later or re-login'), 500

    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error="An error occurred. Try again or re-login"), 400

    with db_connection.get_session(database_name) as db_session:
        reimbursement = Reimbursements.get_reimbursement_by_id(db_session, reimbursement_id)
        if reimbursement is None:
            current_app.logger.error("No reimbursement found")
            return jsonify(success=False, error="No reimbursement found"), 404
        
        try:
            deleted = Reimbursements.delete_reimbursement(db_session, reimbursement_id)
            if 'An error occurred' in deleted:
                return jsonify(success=False, error=deleted), 500
            
            current_app.logger.info(f"Reimbursement deleted successfully: {deleted}")
            return jsonify(success=True, message="Reimbursement deleted successfully."), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while deleting reimbursement: {str(e)}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500