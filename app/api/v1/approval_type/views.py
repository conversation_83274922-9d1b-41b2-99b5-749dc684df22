from flask import Blueprint, jsonify, request, current_app
from app.models.central import ApprovalType
from app.api.v1.decorators.auth_decorators import role_required
from app.api_helpers.ApiHelpers import UserInputValidator

approval_types_api_bp = Blueprint('approval_types_api', __name__)


@approval_types_api_bp.route('/get_approval_types', methods=['GET'])
@role_required('admin')
def get_approval_types():
    try:
        approval_types = ApprovalType.get_approval_types()
        return jsonify(success=True, data=approval_types, message='Approval Types retrieved successfully'), 200
    except Exception as e:
        current_app.logger.error('Error getting approval_types: %s', e)
        return jsonify(success=False, error='Error getting approval_types'), 500


@approval_types_api_bp.route('/get_approval_type_by_id/<uuid:id>', methods=['GET'])
@role_required('admin')
def get_one_approval_type(id):
    try:
        approval_type = ApprovalType.get_approval_type_by_id(id)
        return jsonify(success=True, data=approval_type, message='Approval Type retrieved successfully'), 200
    except Exception as e:
        current_app.logger.error('Error getting approval_type: %s', e)
        return jsonify(success=False, error='Error getting approval_type'), 500
    

@approval_types_api_bp.route('/add_approval_type', methods=['POST'])
@role_required('admin')
def add_approval_type():
    data = request.get_json()
    name = data.get('name')
    description = data.get('description')

    is_valid, errors = UserInputValidator.validate({
        'name': name,
        'description': description
    }, 'add_approval_type')

    if not is_valid:
        return jsonify(success=False, error=errors), 400

    try:
        result = ApprovalType.add_approval_type(name, description)
        if result:
            return jsonify(success=True, error='Approval Type added successfully'), 200
        else:
            return jsonify(success=False, error='Approval Type not added'), 500
    except Exception as e:
        current_app.logger.error('Error adding Approval Type: %s', e)
        return jsonify(success=False, error='Error adding Approval Type'), 500       


@approval_types_api_bp.route('/update_approval_type/<uuid:id>', methods=['PUT'])
@role_required('admin')
def update_approval_type(id):
    data = request.get_json()
    name = data.get('name')
    description = data.get('description')

    is_valid, errors = UserInputValidator.validate({
        'name': name,
        'description': description
    }, 'add_approval_type')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    try:
        result = ApprovalType.update_approval_type(id, name, description)
        if result:
            return jsonify(success=True, error='Approval Type updated successfully'), 200
        else:
            return jsonify(success=False, error='Approval Type not updated'), 500
    except Exception as e:
        current_app.logger.error('Error updating Approval Type: %s', e)
        return jsonify(success=False, error='Error updating Approval Type'), 500


@approval_types_api_bp.route('/delete_approval_type/<uuid:id>', methods=['DELETE'])
@role_required('admin')
def delete_approval_type(id):
    try:
        # Check if it exist in db
        exists = ApprovalType.get_approval_type_by_id(id)
        if not exists:
            return jsonify(success=False, error='Approval Type does not exists'), 400
        
        result = ApprovalType.delete_approval_type(id)
        if result:
            return jsonify(success=True, message='Approval Type deleted successfully'), 200
        else:
            return jsonify(success=False, error='Approval Type not deleted'), 500
    except Exception as e:
        current_app.logger.error('Error deleting Approval Type: %s', e)
        return jsonify(success=False, error='Error deleting Approval Type'), 500