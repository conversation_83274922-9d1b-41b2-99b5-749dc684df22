from flask import Blueprint, request, jsonify, current_app
from app.models.central import User
from app.utils.db_connection import DatabaseConnection
import logging
from app.helpers.company_helpers import CompanyHelpers
from app import db
from app.routes.token.token_manager import TokenManager
from flask import url_for
from app.helpers.auxillary import Auxillary

users_bp = Blueprint('users', __name__)
@users_bp.route('/api/v1/user_signup', methods=['POST'])
def register_user():
    """Register a new user."""
    data = request.get_json()

    try:
        # Extract user details from the request data
        username = data.get('userName').strip()
        email = data.get('email').strip()
        password = data.get('password').strip()
        phone_number = data.get('phone').strip()
        first_name = data.get('firstName').strip()
        last_name = data.get('lastName').strip()
        # Check if the user already exists
        user = User.query.filter_by(email=email).first()
        if user:
            current_app.logger.info('User already exists')
            # return react response standard
            return jsonify({'success':False, 'message': 'User already exists'}), 409
        # Initialize the database connection
        # Create a new user record
        try:
            role = 'hr'
            new_user = User.register_user(username=username, email=email, password=password, phone_number=phone_number, first_name=first_name, last_name=last_name, role=role)
            if new_user:
                token_manager = TokenManager()
                token = token_manager.generate_confirmation_token(email)
                confirm_url = url_for('token.confirm_email', _external=True, token=token)
                body = f"""
                <h3>Hi {first_name},</h3>
                <p>Please click the link below to confirm your email address\n{confirm_url}</p>"""
                subject = "Confirm your email address"
                sent = Auxillary.send_netpipo_email(subject, email, body)
                if sent:
                    current_app.logger.info('Confirmation email sent successfully')
                    return jsonify({'success':True, 'message': 'Please check you email to verify your account'}), 201
                else:
                    current_app.logger.error('An error occurred while sending the confirmation email')
                    return jsonify({'success':False, 'message': 'An error occurred while sending the confirmation email'}), 500
        except Exception as e:
            current_app.logger.error(e)
            return jsonify({'success':False, 'message': 'An error occurred while registering the user'}), 500
        # Save the new user record
        try:
            db.session.add(new_user)
            db.session.commit()
            return jsonify({'success':True, 'message': 'User registered successfully'}), 201
        except Exception as e:
            logging.error(e)
            return jsonify({'success':False, 'message': 'An error occurred while registering the user'}), 500
    except Exception as e:
        logging.error(e)
        return jsonify({'success':False, 'message': 'An error occurred while registering the user'}), 500   