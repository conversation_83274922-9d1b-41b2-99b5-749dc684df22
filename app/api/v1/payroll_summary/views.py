from flask import Blueprint, request,render_template, session, jsonify, url_for, redirect, flash, send_file
import logging
from app.decorators.hr_decorator import hr_required
from app.utils.db_connection import DatabaseConnection
from app.models.company import Employee, Deductions, Insurance, Payroll, Reimbursements, Attendance
from app.models.central import NsfContributions
from app.routes.payroll.goal_Seek_mine import SalaryCalculator, SalaryCalculatorGross
import datetime
from app.routes.payroll.forms import PayrollForm, UpdatePayrollForm
from decimal import Decimal
import pandas as pd
from io import BytesIO
import calendar
from datetime import datetime, timedelta
from app.models.central import TaxBracket, CasualsTaxBracket, SecondEmployeeTaxBracket, ConsultantTaxBracket, BrdDeductions
from flask import current_app
from app.models.central import Company
from app.helpers.auxillary import Auxillary
from flask import Response
import pandas as pd
from openpyxl import Workbook
from openpyxl.drawing.image import Image
import os
import PIL.Image as PILImage
import traceback
import tempfile
import uuid
from app.decorators.role_decorator import role_required
from app.helpers.pdf_generator import PDFGenerator
from app.models.company_salary_advance import SalaryAdvanceRequest, InstallmentPlan
from app.models.company_payroll_approval import PayrollApproval
from app.decorators.subscription_decorator import require_subscription
from flask_jwt_extended import get_jwt, current_user

payroll_summary_api_bp = Blueprint("payroll_summary_api", __name__)

@payroll_summary_api_bp.route("/payroll_summary", methods=["GET", "POST"])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
@require_subscription
def payroll_summary_api():
    """Generate payroll summary for employees."""
    current_app.logger.info('In payroll_summary_api route')
    jwt_data = get_jwt()
    user_id = str(current_user.get('user_id'))
    database_name = jwt_data.get('database_name')
    company_id = jwt_data.get('company_id')
    try:
        # Parse JSON request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400

        # Validate required fields
        pay_date_str = data.get('pay_date')
        timesheet_applicable = data.get('timesheet_applicable', 'no')
        attendance_service = data.get('attendance_service', False)

        if not pay_date_str:
            return jsonify({'success': False, 'message': 'Pay date is required'}), 400
        if not company_id:
            return jsonify({'success': False, 'message': 'Company ID is required'}), 400
        if not database_name:
            return jsonify({'success': False, 'message': 'Database name is required'}), 400
        if timesheet_applicable not in ['yes', 'no']:
            return jsonify({'success': False, 'message': 'Invalid timesheet_applicable value'}), 400

        try:
            pay_date = datetime.strptime(pay_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': 'Invalid pay date format. Use YYYY-MM-DD'}), 400

        # RSSB Contributions fetching logic
        try:
            rssb_contributions = NsfContributions.get_valid_contributions(pay_date)
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting RSSB contributions: {e}")
            rssb_contributions = NsfContributions.get_nsf_contributions()

        if not rssb_contributions:
            return jsonify({'success': False, 'message': 'No RSSB contributions found'}), 400

        # Initialize contribution rates
        maternity_er_rate = maternity_ee_rate = pension_er_rate = pension_ee_rate = cbhi_er_rate = cbhi_ee_rate = Decimal('0.00')
        for contribution in rssb_contributions:
            contribution_name = contribution.contribution_name.lower()
            if contribution_name == "maternity":
                maternity_er_rate = contribution.employer_rate
                maternity_ee_rate = contribution.employee_rate
            elif contribution_name == "pension":
                pension_er_rate = contribution.employer_rate
                pension_ee_rate = contribution.employee_rate
            elif contribution_name == "cbhi":
                cbhi_er_rate = contribution.employer_rates
                cbhi_ee_rate = contribution.employee_rate

        # DB Connection logic
        db_connection = DatabaseConnection()
        with db_connection.get_session(database_name) as db_session:
            employees = Employee.get_employees(db_session)
            if not employees:
                current_app.logger.error("No employees found")
                return jsonify({'success': False, 'message': 'No employees found'}), 404

            employees_with_deductions = []
            current_month = pay_date.month
            current_year = pay_date.year
            total_days_in_month = calendar.monthrange(current_year, current_month)[1]

            for employee in employees:
                if employee.get('is_active') == 'no':
                    continue

                employee_id = employee['employee_id']
                employee_type = employee['employee_type']
                gross_salary = employee.get('gross_salary')
                net_salary = employee.get('net_salary')
                total_staff_cost = employee.get('total_staff_cost')
                transport_allowance = employee.get('transport_allowance', Decimal('0.00'))
                allowances = employee.get('allowances', Decimal('0.00'))
                salary_advance_balance = employee.get('salary_advance_balance', Decimal('0.00'))

                # Handle salary advances
                approved_advances = [a for a in employee.get('salary_advance_requests', []) if a['status'] == 'approved']
                total_due = Decimal('0.00')
                due_installments = []
                try:
                    for advance in approved_advances:
                        for installment in advance.get('installment_plans', []):
                            if installment['due_date'].month == current_month and installment['due_date'].year == current_year:
                                due_installments.append(installment)
                    total_due = sum(Decimal(str(installment['planned_amount'])) for installment in due_installments)
                except Exception as e:
                    current_app.logger.error(f"Error processing advances for employee {employee_id}: {e}")
                    total_due = Decimal('0.00')

                # Fetch insurance
                insurance = Insurance.get_insurances(db_session)
                rama_ee_rate = rama_er_rate = Decimal('0.00')
                if insurance and insurance[0].get('insurance_name') == 'rama' and employee_type != 'casual':
                    rama_ee_rate = Decimal(str(insurance[0].get('employee_rate', 0)))
                    rama_er_rate = Decimal(str(insurance[0].get('employer_rate', 0)))

                # Initialize salary variables
                applicable_net_salary = net_salary
                applicable_gross_salary = gross_salary
                total_days = total_days_in_month

                # Handle timesheet if applicable
                if attendance_service and timesheet_applicable == 'yes':
                    try:
                        timesheet = Attendance.get_employee_attendance(db_session, employee, current_month, current_year, total_days_in_month)
                        if timesheet:
                            total_days = timesheet['paid_days']
                            if employee.get('attendance_applicable') == 'yes':
                                applicable_net_salary = timesheet.get('applicable_net_salary')
                            elif employee.get('attendance_applicable') == 'no':
                                applicable_net_salary = net_salary or gross_salary
                            else:
                                applicable_net_salary = timesheet.get('applicable_net_salary')
                        else:
                            applicable_net_salary = net_salary or gross_salary
                    except Exception as e:
                        current_app.logger.error(f"Error fetching timesheet for employee {employee_id}: {e}")
                        applicable_net_salary = net_salary or gross_salary

                # Adjust allowances based on timesheet
                target_value = applicable_net_salary if timesheet_applicable == 'yes' and applicable_net_salary else net_salary or gross_salary
                if timesheet_applicable == 'yes':
                    transport_allowance = (Decimal(str(transport_allowance)) / total_days_in_month) * total_days
                    allowances = (Decimal(str(allowances)) / total_days_in_month) * total_days

                # Fetch deductions and reimbursements
                emp_deductions = Deductions.get_deduction_for_given_month_and_year_for_employee(db_session, employee_id, current_month, current_year)
                total_deductions = sum(Decimal(str(d['deduction_amount'])) for d in emp_deductions)
                emp_reimbursements = Reimbursements.get_reimbursement_for_given_month_and_year_for_employee(db_session, employee_id, current_month, current_year)
                total_reimbursements = sum(Decimal(str(r['reimbursement_amount'])) for r in emp_reimbursements)

                # BRD deductions
                brd_deduction = Decimal('0.00')
                if employee.get('is_brd_sponsored') == 'yes':
                    rate = BrdDeductions.get_brd_deductions()
                    brd_rate = Decimal(str(rate['deduction_rate'])) if rate else Decimal('0.00')
                else:
                    brd_rate = Decimal('0.00')

                # Salary calculation
                try:
                    if net_salary is not None:
                        calculator = SalaryCalculator(
                            allowances, transport_allowance, total_deductions, cbhi_ee_rate,
                            pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate,
                            rama_ee_rate, employee_type
                        )
                        results = calculator.goalseek(target_value, Decimal('1.00'))
                        (basic_needed, gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi,
                         pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value,
                         total_deductions_value, net_salary_value) = results
                        brd_deduction = gross_needed * brd_rate
                    elif gross_salary:
                        calculator = SalaryCalculatorGross(
                            allowances, transport_allowance, total_deductions, cbhi_ee_rate,
                            pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate,
                            rama_ee_rate, employee_type, pay_date
                        )
                        if timesheet_applicable == 'yes':
                            gross_salary = (Decimal(str(gross_salary)) / total_days_in_month) * total_days
                            transport_allowance = (Decimal(str(transport_allowance)) / total_days_in_month) * total_days
                            allowances = (Decimal(str(allowances)) / total_days_in_month) * total_days
                        basic_needed = calculator.calculate_basic_salary(gross_salary)
                        results = calculator.calculate_all(basic_needed)
                        (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi,
                         pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value,
                         total_deductions_value, net_salary_value) = map(Auxillary.round_to_decimal, results)
                        basic_needed = Auxillary.round_to_decimal(basic_needed)
                        brd_deduction = gross_needed * brd_rate
                    elif total_staff_cost:
                        # Calculate gross salary using the corrected method with calculation date
                        gross_salary = Employee.calculate_gross_based_on_total_staff_cost(db_session, employee, calculation_date=pay_date.date())
                        current_app.logger.info(f"API: Calculated gross_salary from total_staff_cost: {gross_salary}")

                        if timesheet_applicable == 'yes':
                            gross_salary = (Decimal(str(gross_salary)) / total_days_in_month) * total_days
                            transport_allowance = (Decimal(str(transport_allowance)) / total_days_in_month) * total_days
                            allowances = (Decimal(str(allowances)) / total_days_in_month) * total_days

                        # Use the same SalaryCalculatorGross as other salary types for consistency
                        calculator = SalaryCalculatorGross(
                            allowances, transport_allowance, total_deductions, cbhi_ee_rate,
                            pension_ee_rate, pension_er_rate, maternity_ee_rate, maternity_er_rate,
                            rama_ee_rate, employee_type, pay_date
                        )

                        # Calculate basic salary correctly: basic = gross - allowances - transport
                        basic_needed = calculator.calculate_basic_salary(gross_salary)
                        current_app.logger.info(f"API: Calculated basic_salary from gross_salary: {basic_needed}")

                        results = calculator.calculate_all(basic_needed)
                        (gross_needed, rama_ee, cbhi_value, paye, net_bcbhi, net_cbhi,
                         pension_ee_value, pension_er_value, maternity_ee_value, maternity_er_value,
                         total_deductions_value, net_salary_value) = map(Auxillary.round_to_decimal, results)
                        basic_needed = Auxillary.round_to_decimal(basic_needed)
                        brd_deduction = gross_needed * brd_rate

                        current_app.logger.info(f"API: Total staff cost employee {employee_id}: gross={gross_needed}, basic={basic_needed}, net={net_salary_value}")
                    else:
                        current_app.logger.error(f"No valid salary data for employee {employee_id}")
                        continue

                    if employee_type == 'casual':
                        rama_ee = Decimal('0.00')

                    # Store employee data
                    employee_data = {
                        'employee_id': employee_id,
                        'first_name': employee.get('first_name'),
                        'last_name': employee.get('last_name'),
                        'employee_type': employee_type,
                        'basic_needed': float(Auxillary.round_to_decimal(basic_needed)),
                        'gross_needed': float(Auxillary.round_to_decimal(gross_needed)),
                        'rama_ee': float(Auxillary.round_to_decimal(rama_ee)),
                        'cbhi_value': float(Auxillary.round_to_decimal(cbhi_value)),
                        'paye': float(Auxillary.round_to_decimal(paye)),
                        'net_bcbhi': float(Auxillary.round_to_decimal(net_bcbhi)),
                        'net_cbhi': float(Auxillary.round_to_decimal(net_cbhi)),
                        'pension_ee_value': float(Auxillary.round_to_decimal(pension_ee_value)),
                        'pension_er_value': float(Auxillary.round_to_decimal(pension_er_value)),
                        'maternity_ee_value': float(Auxillary.round_to_decimal(maternity_ee_value)),
                        'maternity_er_value': float(Auxillary.round_to_decimal(maternity_er_value)),
                        'total_maternity': float(Auxillary.round_to_decimal(maternity_ee_value + maternity_er_value)),
                        'total_deductions': float(Auxillary.round_to_decimal(total_deductions)),
                        'total_reimbursements': float(Auxillary.round_to_decimal(total_reimbursements)),
                        'total_deductions_value': float(Auxillary.round_to_decimal(total_deductions_value)),
                        'net_salary_value': float(Auxillary.round_to_decimal(net_salary_value)),
                        'total_pension': float(Auxillary.round_to_decimal(pension_ee_value + pension_er_value)),
                        'total_rama': float(Auxillary.round_to_decimal(rama_ee + rama_ee)),
                        'brd_deduction': float(Auxillary.round_to_decimal(brd_deduction)),
                        'salary_advance': float(Auxillary.round_to_decimal(total_due)),
                        'due_installments': due_installments
                    }
                    employees_with_deductions.append(employee_data)
                except Exception as e:
                    current_app.logger.error(f"Error processing employee {employee_id}: {e}")
                    continue

            # Calculate totals
            totals = {
                'total_pension_ee': float(Auxillary.round_to_decimal(sum(Decimal(str(e['pension_ee_value'])) for e in employees_with_deductions))),
                'total_pension_er': float(Auxillary.round_to_decimal(sum(Decimal(str(e['pension_er_value'])) for e in employees_with_deductions))),
                'total_maternity_ee': float(Auxillary.round_to_decimal(sum(Decimal(str(e['maternity_ee_value'])) for e in employees_with_deductions))),
                'total_maternity_er': float(Auxillary.round_to_decimal(sum(Decimal(str(e['maternity_er_value'])) for e in employees_with_deductions))),
                'total_gross': float(Auxillary.round_to_decimal(sum(Decimal(str(e['gross_needed'])) for e in employees_with_deductions))),
                'total_nbcbhi': float(Auxillary.round_to_decimal(sum(Decimal(str(e['net_bcbhi'])) for e in employees_with_deductions))),
                'total_other_allowances': float(Auxillary.round_to_decimal(sum(Decimal(str(e['employee'].get('allowances', 0))) for e in employees_with_deductions))),
                'total_paye': float(Auxillary.round_to_decimal(sum(Decimal(str(e['paye'])) for e in employees_with_deductions))),
                'total_net_cbhi': float(Auxillary.round_to_decimal(sum(Decimal(str(e['net_cbhi'])) for e in employees_with_deductions))),
                'total_payroll_deductions': float(Auxillary.round_to_decimal(sum(Decimal(str(e['total_deductions_value'])) for e in employees_with_deductions))),
                'total_pension': float(Auxillary.round_to_decimal(sum(Decimal(str(e['pension_ee_value'])) + Decimal(str(e['pension_er_value'])) for e in employees_with_deductions))),
                'total_maternity': float(Auxillary.round_to_decimal(sum(Decimal(str(e['maternity_ee_value'])) + Decimal(str(e['maternity_er_value'])) for e in employees_with_deductions))),
                'total_rama': float(Auxillary.round_to_decimal(sum(Decimal(str(e['rama_ee'])) for e in employees_with_deductions))),
                'total_deductions': float(Auxillary.round_to_decimal(sum(Decimal(str(e['total_deductions'])) for e in employees_with_deductions))),
                'total_reimbursements': float(Auxillary.round_to_decimal(sum(Decimal(str(e['total_reimbursements'])) for e in employees_with_deductions))),
                'total_net_salary': float(Auxillary.round_to_decimal(sum(Decimal(str(e['net_salary_value'])) for e in employees_with_deductions))),
                'total_cbhi_value': float(Auxillary.round_to_decimal(sum(Decimal(str(e['cbhi_value'])) for e in employees_with_deductions))),
                'total_transport_allowance': float(Auxillary.round_to_decimal(sum(Decimal(str(e['employee'].get('transport_allowance', 0))) for e in employees_with_deductions))),
                'total_basic_salary': float(Auxillary.round_to_decimal(sum(Decimal(str(e['basic_needed'])) for e in employees_with_deductions))),
                'total_brd_deductions': float(Auxillary.round_to_decimal(sum(Decimal(str(e['brd_deduction'])) for e in employees_with_deductions))),
                'total_advances': float(Auxillary.round_to_decimal(sum(Decimal(str(e['salary_advance'])) for e in employees_with_deductions))),
                'total_net_salary_after_deductions': float(Auxillary.round_to_decimal(sum(
                    Decimal(str(e['net_salary_value'])) - Decimal(str(e['total_deductions'])) +
                    Decimal(str(e['total_reimbursements'])) - Decimal(str(e['brd_deduction'])) -
                    Decimal(str(e['salary_advance'])) for e in employees_with_deductions)))
            }

            return jsonify({
                'success': True,
                'message': 'Payroll summary generated successfully',
                'data': {
                    'employees': employees_with_deductions,
                    'totals': totals,
                    'pay_date': pay_date.isoformat()
                }
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error generating payroll summary: {str(e)}")
        return jsonify({'success': False, 'message': 'An error occurred. Please try again later.'}), 500