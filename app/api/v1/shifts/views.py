from datetime import time
from app.models.company_shifts import Shift
from flask_jwt_extended import get_jwt
from app.api.v1.decorators.auth_decorators import role_required
from app.utils.db_connection import DatabaseConnection
from flask import Blueprint, request, current_app, jsonify
from app.models.company import Attendance
from app.api_helpers.ApiHelpers import UserInputValidator


shifts_api_bp = Blueprint('shifts', __name__)


@shifts_api_bp.route('/view_shifts', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def view_shifts():
    """View all shifts."""
    jwt_data = get_jwt()
    database_name = jwt_data.get('database_name')
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            shifts = Shift.get_shifts(db_session)
            current_app.logger.info(f"Shifts: {shifts}")

            # Change date and time format for these fields: start_time, end_time, created_at, updated_at
            for shift in shifts:
                shift['start_time'] = shift['start_time'].strftime('%H:%M:%S')
                shift['end_time'] = shift['end_time'].strftime('%H:%M:%S')
                shift['created_at'] = shift['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                shift['updated_at'] = shift['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify(success=True, data=shifts, message="Shifts retrieved successfully"), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while getting all shifts: {str(e)}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500


@shifts_api_bp.route('/create_shift', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager'])
def create_shift():
    """Create a shift."""
    jwt_data = get_jwt()
    database_name = jwt_data.get('database_name')

    if not database_name:
        return jsonify(success=False, error='An error occurred. Please try again later or re-login.'), 400
    
    data = request.get_json()
    name = data.get("name")
    start_time = data.get("start_time")
    end_time = data.get("end_time")
    auto_clock_out_hours = data.get("auto_clock_out_hours")

    is_valid, errors = UserInputValidator.validate({
        "name": name,
        "start_time": start_time,
        "end_time": end_time,
        "auto_clock_out_hours": auto_clock_out_hours
    }, 'create_shift')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Converting string time into time object - time(hour, minute, second)
    def parse_time(t: str):
        parts = [int(x) for x in t.split(':')]
        return time(*parts) if len(parts) == 3 else time(parts[0], parts[1], 0)

    start_time = parse_time(start_time)
    end_time = parse_time(end_time)
    
    current_app.logger.info(f"Name: {name}, Start Time: {start_time}, End Time: {end_time}, Auto Clock Out Hours: {auto_clock_out_hours}")
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info("Creating shift")
            message, category = Shift.create_shift(db_session, name, start_time, end_time, auto_clock_out_hours)
            current_app.logger.info(message)
            return jsonify(success=True, message=message), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while creating the shift: {str(e)}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500


@shifts_api_bp.route('/update_shift/<uuid:shift_id>', methods=['PUT'])
@role_required(['hr', 'company_hr', 'manager'])
def update_shift(shift_id):
    """Update a shift."""
    jwt_data = get_jwt()
    database_name = jwt_data.get('database_name')

    if not database_name:
        return jsonify(success=False, error='An error occurred. Please try again later or re-login.'), 400
    
    data = request.get_json()
    name = data.get("name")
    start_time = data.get("start_time")
    end_time = data.get("end_time")
    auto_clock_out_hours = data.get("auto_clock_out_hours")

    is_valid, errors = UserInputValidator.validate({
        "name": name,
        "start_time": start_time,
        "end_time": end_time,
        "auto_clock_out_hours": auto_clock_out_hours
    }, 'create_shift')

    if not is_valid:
        return jsonify(success=False, error=errors), 400
    
    # Converting string time into time object - time(hour, minute, second)
    def parse_time(t: str):
        parts = [int(x) for x in t.split(':')]
        return time(*parts) if len(parts) == 3 else time(parts[0], parts[1], 0)
    
    start_time = parse_time(start_time)
    end_time = parse_time(end_time)
    
    current_app.logger.info(f"Shift ID: {shift_id}, Name: {name}, Start Time: {start_time}, End Time: {end_time}, Auto Clock Out Hours: {auto_clock_out_hours}")
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info("Updating shift")
            message, category = Shift.update_shift(db_session, shift_id, name, start_time, end_time, auto_clock_out_hours)
            current_app.logger.info(message)
            return jsonify(success=True, message=message), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while updating the shift: {str(e)}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500


@shifts_api_bp.route('/auto_clockout', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager'])
def auto_clockout():
    """Auto clock out employees."""
    # Get database name
    database_name = get_jwt().get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info("Auto clocking out employees")
            message = Attendance.auto_clockout(db_session)

            current_app.logger.info(message)
            return jsonify(success=True, message=message), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while auto clocking out employees: {str(e)}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500
        

@shifts_api_bp.route('/delete_shift/<uuid:shift_id>', methods=['DELETE'])
@role_required(['hr', 'company_hr', 'manager'])
def delete_shift(shift_id):
    """Delete a shift."""
    # Get database name
    database_name = get_jwt().get('database_name')
    # Create a new database connection
    db_connection = DatabaseConnection()

    with db_connection.get_session(database_name) as db_session:
        try:
            current_app.logger.info(f"Deleting shift with ID: {shift_id}")
            message = Shift.delete_shift(db_session, shift_id)
            current_app.logger.info(message)
            return jsonify(success=True, message="Shift deleted successfully"), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while deleting the shift: {str(e)}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500
        