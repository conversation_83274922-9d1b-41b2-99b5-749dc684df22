import json
from datetime import datetime
from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import get_jwt, current_user
from app.models.company import Deductions, Employee
from app.helpers.company_helpers import CompanyHelpers
from app.utils.db_connection import DatabaseConnection
from app.helpers.auxillary import Auxillary
from app.api.v1.decorators.auth_decorators import role_required
from app.api.jwt_config import redis_client
from app.api_helpers.ApiHelpers import UserInputValidator


deduction_api_bp = Blueprint('deductions', __name__)
db_connection = DatabaseConnection()


@deduction_api_bp.route('/get_deductions', methods=['GET', 'POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_deductions():
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    
    if not company_id or not database_name:
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
        
    with db_connection.get_session(database_name) as db_session:
        deductions = db_session.query(Deductions).all()
        if not deductions:
            current_app.logger.error("No deductions found")
        # Convert deduction objects to dictionaries
        deductions_list = [deduction.to_dict() for deduction in deductions]

        try:
            for personal_deduction in deductions_list:
                # Retrieve the corresponding employee object using employee_id
                employee = db_session.query(Employee).filter_by(employee_id=personal_deduction['employee_id']).first()
                
                if employee:
                    # Add the employee's full name to the dictionary
                    personal_deduction['employee_name'] = f"{employee.first_name} {employee.last_name}"
                
                # Remove unnecessary fields
                personal_deduction.pop('employee_id', None)  # Safely remove 'employee_id'
                personal_deduction.pop('company_id', None)   # Safely remove 'company_id'
                current_app.logger.info(f"Personal Deduction: {personal_deduction}")
            return jsonify(success=True, data=deductions_list, message="Deductions fetched successfully"), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while processing deductions list: {str(e)}")
            return jsonify(success=False, error='An error occurred. Please try again later.'), 500
        

@deduction_api_bp.route('/get_deduction/<uuid:deduction_id>', methods=['GET'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def get_deduction(deduction_id):
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    
    if not company_id or not database_name:
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
        
    with db_connection.get_session(database_name) as db_session:
        deduction = Deductions.get_deduction_by_id(db_session, deduction_id)
        if not deduction:
            return jsonify(success=False, error='Deduction not found'), 404
        
        employee = db_session.query(Employee).filter_by(employee_id=deduction.employee_id).first()
        data = [{"deduction": deduction.to_dict()}, {"employee": employee.to_dict()}]
        return jsonify(success=True, data=data, message="Deduction fetched successfully"), 200


@deduction_api_bp.route('/add_deductions', methods=['POST'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def add_deductions():
    data = request.get_json()
    jwt_data = get_jwt()
    company_id = jwt_data.get('company_id')
    database_name = jwt_data.get('database_name')
    
    if not company_id or not database_name:
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400
        
    # Validation
    is_valid, error = UserInputValidator.validate(data, "deduction")
    if not is_valid:
        return jsonify(success=False, error=error), 400
    
    # Request data
    employee_id = data.get('employee_id')
    description = data.get('description')
    amount = data.get('amount')
    deduction_date = data.get('deduction_date')

    
    with db_connection.get_session(database_name) as db_session:
        employees = Employee.get_employees(db_session)

        if not employees:
            error = "No employees found. Please add employees before adding deductions."
            current_app.logger.error(error)
            return jsonify(success=False, error=error), 404
        
        # Check if the deduction date is in the future
        if deduction_date > datetime.now().strftime('%Y-%m-%d'):
            error = "Sorry! You can only add deductions of the past and present dates."
            current_app.logger.error(error)
            return jsonify(success=False, error=error), 400
        
        # Create a new deduction record
        try:
            deduction = Deductions.add_deduction(db_session, employee_id, description, amount, deduction_date)
            return jsonify(success=True, data=deduction.to_dict(), message="Deduction added successfully"), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while adding a deduction: {e}")
            return jsonify(success=False, error="An error occurred while adding a deduction. Please try again later."), 500


@deduction_api_bp.route('/update_deduction/<uuid:deduction_id>', methods=['PUT'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def update_deductions(deduction_id):
    """Update the deduction."""
    data = request.get_json()
    is_valid, error = UserInputValidator.validate(data, "update_deduction")
    if not is_valid:
        return jsonify(success=False, error=error), 400
    
    description = data.get('description')
    amount = data.get('amount')
    deduction_date = data.get('deduction_date')

    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400

    # Fetch the deduction object within a session context
    with db_connection.get_session(database_name) as db_session:
        try:
            deduction_to_update = Deductions.get_deduction_by_id(db_session, deduction_id)
            if not deduction_to_update:
                current_app.logger.error("Deduction not found")
                return jsonify(success=False, error='Deduction not found'), 404

            updated_deduction = Deductions.update_deduction(db_session, deduction_id, description, amount, deduction_date)
            if not isinstance(updated_deduction, Deductions):
                current_app.logger.error(f"An error occurred while updating deductions: {updated_deduction}")
                return jsonify(success=False, error="An error occurred. Try again later"), 500
            
            return jsonify(success=True, data=updated_deduction.to_dict(), message="Deduction updated successfully"), 200
        except Exception as e:
            current_app.logger.error(f"An error occurred while updating deductions: {e}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500
        

@deduction_api_bp.route('/delete_deductions/<uuid:deduction_id>', methods=['DELETE'])
@role_required(['hr', 'company_hr', 'manager', 'accountant'])
def delete_deductions(deduction_id):
    """Delete the deduction."""
    database_name = get_jwt().get('database_name')
    if database_name is None:
        current_app.logger.error("An error occurred while fetching company database name")
        return jsonify(success=False, error='An error occurred. Please try again later.'), 400

    # Fetch the deduction object within a session context
    with db_connection.get_session(database_name) as db_session:
        try:
            deduction_to_delete = Deductions.get_deduction_by_id(db_session, deduction_id)
            if deduction_to_delete is None:
                current_app.logger.error("Deduction not found")
                return jsonify(success=False, error='Deduction not found'), 404

            # Passed actual Deduction instance to avoid n+1 DB queries
            is_deleted = Deductions.delete_deduction(db_session, deduction_to_delete)
            if is_deleted:
                current_app.logger.info("Deduction deleted successfully")
                return jsonify(success=True, message="Deduction deleted successfully"), 200
            # When is not deleted Deduction.delete_deduction will raise an exception
        except Exception as e:
            current_app.logger.error(f"An error occurred while deleting deductions: {e}")
            return jsonify(success=False, error="An error occurred. Try again later"), 500
