import json
import os
from datetime import datetime
from flask import Blueprint, jsonify, request, current_app, url_for, render_template
from flask_jwt_extended import (
    current_user, get_jwt, create_access_token, 
    create_refresh_token, get_jti
)
from app.models.central import User
from app.helpers.auxillary import Auxillary
from app.routes.token.token_manager import TokenManager
from app.helpers.users_helpers import UsersHelpers
from app.api_helpers.ApiHelpers import Api<PERSON>elper
from app.api_helpers.ApiHelpers import UserInputValidator
from app.api.v1.decorators.auth_decorators import role_required
from app.api import jwt_config


user_api_auth = Blueprint('user_auth', __name__, url_prefix='/auth')


#create register endpoint
@user_api_auth.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    # Fetch the reCAPTCHA public and private keys
    captcha_public_key = os.getenv('RECAPTCHA_PUBLIC_KEY')
    captcha_private_key = os.getenv('RECAPTCHA_PRIVATE_KEY')
    print("captcha_public_key: ", captcha_public_key)
    print("captcha_private_key: ", captcha_private_key)
    # verify reCaptcha
    """try:
        if captcha_public_key and captcha_private_key:
            # Get the reCAPTCHA response from the form
            recaptcha_response = request.form.get('g-recaptcha-response')
            print(f"reCAPTCHA Response: {recaptcha_response}")
            # Perform reCAPTCHA verification
            verification_response = requests.post(
                'https://www.google.com/recaptcha/api/siteverify',
                data={
                    'secret': captcha_private_key,
                    'response': recaptcha_response
                }
            )
            result = verification_response.json()
            print(f"reCAPTCHA Verification Result: {result}")
            if not result.get('success'):
                flash("reCAPTCHA verification failed", 'danger')
                current_app.logger.error("reCAPTCHA verification failed")
                return redirect(url_for('user_data.register'))
    except Exception as e:
        flash("An error occurred", 'danger')
        current_app.logger.error(f"Error verifying reCAPTCHA: {str(e)}")
    """

    username = data.get('username').strip()
    email = data.get('email').strip()
    password = data.get('password').strip()
    phone_number = data.get('phone_number').strip()
    first_name = data.get('first_name').strip()
    last_name = data.get('last_name').strip()
    
    # Validate the input data
    is_valid, errors = UserInputValidator.validate({
        'username': username,
        'email': email,
        'password': password,
        'phone_number': phone_number,
        'first_name': first_name,
        'last_name': last_name
    }, "user_registration")
    if not is_valid:
        return jsonify(success=False, errors=errors), 400
    
    #check if the user already exists
    exists = UsersHelpers.check_user_exists(username, email, phone_number)
    if exists:
        return jsonify(success=False, error=exists), 400


    try:
        role = 'hr'
        register = User.register_user(
            username, email, role, password, phone_number, first_name, last_name)

        if not register:
            return jsonify(success=False, error="Registration failed"), 400
    
        token_manager = TokenManager()
        token = token_manager.generate_confirmation_token(email)
        # This confirmation URL will be set to trigger frontend route
        # Like: https://netpipo.com/auth/confirm/token=<token>
        confirm_url = url_for(
            'api_bp.user_auth.confirm_email', token=token, _external=True)
        body = f"""<h3>Welcome {first_name},</h3>
                <br>Please confirm your email by clicking on the following link: {confirm_url}"""
        subject = "Confirm your email"
        Auxillary.send_netpipo_email(subject, email, body)
        
        message = f"Registration successful! A confirmation email has been sent to {email}. Please check your inbox."
        return jsonify(success=True, message=message), 200
    except Exception as e:
        message = f"Registration failed with error: {str(e)}"
        current_app.logger.error(message)
        return jsonify(success=False, error="Registration failed with error"), 500


# HAS TO BE CHANGED TO RENDER TEMPLATE INSTEAD OF API
@user_api_auth.route('/confirm/<token>')
def confirm_email(token):
    """Confirm the user's email."""
    token_manager = TokenManager()
    email = token_manager.confirm_token(token)

    if not email:
        return render_template("confirm_email.html", error='The confirmation link is invalid or has expired.')
    
    # Update user to set is_active=True in the database
    try:
        if User.update_is_active(email):
            return render_template("confirm_email.html", message="Your email has been confirmed! Please login")
        else:
            return render_template("confirm_email.html", error="Failed to activate your account. Please try again later.")

    except Exception as e:
        current_app.logger.error(f"Error updating user: {e}")
        return render_template("confirm_email.html", error='An error occurred while updating your account.')

@user_api_auth.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username').strip() if data.get('username') else None
    password = data.get('password').strip() if data.get('password') else None

    # Validate the input data
    is_valid, errors = UserInputValidator.validate({
        'username': username,
        'password': password
    }, "user_login")
    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    user = User.get_user_by_username(username)
    # Check user credentials
    is_correct = user.login_user(username, password)
    if not is_correct:
        return jsonify(success=False, error='Invalid username or password'), 400

    # Check if the user is active
    if not user.is_active:
        return jsonify(success=False, error='Please confirm your email to activate your account.'), 400
    
    # Check if user has permission to login
    if user.role not in ['hr', 'accountant', 'manager', 'company_hr', 'admin']:
        return jsonify(success=False, error='Forbidden'), 403
    
    try:
        # Generate OTP & store it in the database
        user_otp = user.create_otp()
        body = f"""
            <blockquote>
                Hello {user.first_name},
                We received a request to reset your password. Use the OTP below to reset it. This OTP is valid for the next 10 minutes.
                OTP: <h1>{user_otp}</h1>
                If you did not request a password reset, please ignore this email or contact support if you have concerns.
                
                Best regards,
                Netpipo Team
            </blockquote>
        """
        subject = "Password Reset OTP"
        # Auxillary.send_netpipo_email(subject, user.email, body)
        print(body)

        # If user has gone to login while he still has an active refresh token  -  this is rare, but possible
        # ============== Invalidate current access & refresh token ============
        access_token_jti = jwt_config.redis_client.get(user.email+"_access")
        refresh_token_jti = jwt_config.redis_client.get(user.email+"_refresh")

        if access_token_jti:
            jwt_config.redis_client.set(access_token_jti, "", ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        if refresh_token_jti:
            jwt_config.redis_client.set(refresh_token_jti, "", ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])
        # =============== Invalidating access & refresh tokens ends here =============
        
        message = f"An OTP has been sent to {user.email}. Please check your inbox."
        return jsonify(success=True, message=message), 200
    except Exception as e:
        current_app.logger.error(f"Error sending OTP: {str(e)}")
        return jsonify(success=False, error='An error occurred while sending the OTP'), 500

@user_api_auth.route('/verify_login_otp', methods=['POST'])
def verify_login_otp():
    data = request.get_json()
    otp = data.get('otp').strip() if data.get('otp') else None

    # Validate the input data
    is_valid, errors = UserInputValidator.validate({
        'otp': otp
    }, "user_login_otp")
    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    username = data.get('username').strip() if data.get('username') else None
    password = data.get('password').strip() if data.get('password') else None

    # Validate the input data
    is_valid, errors = UserInputValidator.validate({
        'username': username,
        'password': password
    }, "user_login")
    if not is_valid:
        return jsonify(success=False, errors=errors), 400
        
    try:
        user = User.get_user_by_username(username)
        if not user:
            return jsonify(success=False, error='Invalid username or password'), 404
        
        
        is_correct = User.login_user(username, password)
        if not is_correct:
            return jsonify(success=False, error='Invalid username or password'), 404

        # Check if the OTP is correct
        is_otp_correct = user.check_otp(otp)
        if not is_otp_correct:
            return jsonify(success=False, error='Invalid OTP'), 400
        
        # Get current active company from httponly cookie/this is optional
        if user.role in ['hr', 'accountant', 'manager', 'company_hr']:
            company_id = request.cookies.get('company_id', None)
            user_company = None
            if not company_id:
                user_company = user.companies[0] if user.companies else None
            else:
                if user.companies:
                    user_company = [company for company in user.companies if company_id == str(company.id)][0]
        
        # Initializing additional claims & creating tokens
        if user.role == 'admin':
            additional_claims = {"last_activity": datetime.now().isoformat()}
        else:    
            additional_claims = {
                "last_activity": datetime.now().isoformat(),
                "company_id": str(user_company.company_id) if user_company else "",
                "company_plan_id": user_company.plan_id if user_company else "",
                "database_name": user_company.database_name if user_company else "",
            }

        refresh_token = create_refresh_token(identity=user.to_dict(), additional_claims=additional_claims)
        additional_claims.update(refresh_jti=get_jti(refresh_token))
        access_token = create_access_token(identity=user.to_dict(), additional_claims=additional_claims)
        
        # Store user access token for easy revocation
        access_jti = get_jti(access_token)
        refresh_jti = get_jti(refresh_token)
        jwt_config.redis_client.set(f"{user.email}_access", access_jti, ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        jwt_config.redis_client.set(f"{user.email}_refresh", refresh_jti, ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])
        
        return jsonify(success=True, data={"access_token": access_token, "refresh_token": refresh_token}), 200
    except Exception as e:
        current_app.logger.exception(f"Login failed with error: {str(e)}")
        return jsonify(success=False, error='An error occurred during login'), 500
    

@user_api_auth.route('/reset_password', methods=['POST'])
def reset_password():
    """Reset a user's password."""
    data = request.get_json()
    email = data.get("email")

    # Validate the input data
    is_valid, errors = UserInputValidator.validate({
        'email': email
    }, "user_reset_password")
    if not is_valid:
        return jsonify(success=False, errors=errors), 400

    try:
        user = User.get_user_by_email(email)
    except Exception as e:
        current_app.logger.error(f'Exception occured while retrieving user info: {str(e)}')
        return jsonify(success=False, error=f"The user with the provided email: {email} does not exist.")

    try:
        temporary_password = Auxillary.random_password()
        updated = user.reset_password(email, temporary_password)
        if not updated:
            current_app.logger.error("Password reset failed.")
            return jsonify(success=False, error="Password reset failed."), 500

        recipients = email
        body = f"""
            <h3>Hi {user.last_name},</h3>
            <p>Your password has been reset. Your temporary password is: <strong> {temporary_password}</strong></p>
        """
        # Auxillary.send_netpipo_email("Password Reset", recipients, body)
        print(body)

        # ============== Invalidate current access & refresh token ============
        access_token_jti = jwt_config.redis_client.get(user.email+"_access")
        refresh_token_jti = jwt_config.redis_client.get(user.email+"_refresh")

        if access_token_jti:
            jwt_config.redis_client.set(access_token_jti, "", ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        if refresh_token_jti:
            jwt_config.redis_client.set(refresh_token_jti, "", ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])
        # =============== Invalidating access & refresh tokens ends here =============

        message="A temporary password has been sent to your email. If the email is not in your inbox, kindly check your spam folder"
        return jsonify(success=True, message=message), 200
    except Exception as e:
        current_app.logger.error(f"Error sending email: {str(e)}")
        return jsonify(success=False, error="An error occurred"), 500

@user_api_auth.route('/refresh_access_token', methods=['POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr', 'admin'], use_refresh=True)
def refresh_access_token():
    """Refresh access token."""
    try:
        jwt_payload: dict = get_jwt()
        identity = json.loads(jwt_payload.get("sub"))

        if identity.get('role') == 'admin':
            additional_claims = {"last_activity": datetime.now().isoformat()}
        else:
            additional_claims = {
                "last_activity": datetime.now().isoformat(),
                "company_id": jwt_payload.get("company_id"),
                "company_plan_id": jwt_payload.get("company_plan_id"),
                "database_name": jwt_payload.get("database_name")
            }
        print("New additional claims: ", additional_claims)
        access_token, refresh_token = ApiHelper.refresh_tokens(identity, additional_claims, jwt_payload)
        
        return jsonify(success=True, 
            data={"access_token": access_token, "refresh_token": refresh_token},
            message='Access token refreshed successfully!'), 200
    except Exception as e:
        current_app.logger.exception(f'Error refreshing access token: {str(e)}')
        return jsonify(success=False, error='Error refreshing access token!'), 419
    

@user_api_auth.route('/logout', methods=['POST'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def logout_company_users(): 
    """Logout a company user."""
    try:
        jwt_data = get_jwt()
        access_jti = jwt_data.get("jti")
        refresh_jti = jwt_data.get("refresh_jti")
        
        # Storing tokens ID in blocked list (redis store)
        jwt_config.redis_client.set(access_jti, "", ex=current_app.config["JWT_ACCESS_TOKEN_EXPIRES"])
        jwt_config.redis_client.set(refresh_jti, "", ex=current_app.config["JWT_REFRESH_TOKEN_EXPIRES"])

        # Clean user email key in redis store
        user_email = json.loads(jwt_data.get("sub")).get("email")
        jwt_config.redis_client.delete(f"{user_email}_access")
        jwt_config.redis_client.delete(f"{user_email}_refresh")
        return jsonify(success=True, message='User logged out successfully!'), 200
    except Exception as e:
        current_app.logger.exception(f'Error logging out user: {str(e)}')
        return jsonify(success=False, error='Error logging out user!'), 500


#create small endpoint to check if the user is logged in
@user_api_auth.route('/is_logged_in', methods=['GET'])
@role_required(['hr', 'accountant', 'manager', 'company_hr'])
def is_logged_in():
    return jsonify(success=True, message='User is logged in!'), 200