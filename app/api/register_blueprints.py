from flask import Blueprint, jsonify


# =================== API BLUEPRINT ====================
api_bp = Blueprint("api_bp", __name__)

# Delay the import of company_users
from app.api.v1.company_users.views import company_api_users
from app.api.v1.leave_applications.views import leave_api_bp
from app.api.v1.salary_advance.views import advance_api_bp
from app.api.v1.documents.views import document_api_bp
from app.api.v1.attendance.views import attendance_api_bp
from app.api.v1.auth.user.views import user_api_auth
from app.api.v1.company.views import company_api_bp
from app.api.v1.approval_type.views import approval_types_api_bp
from app.api.v1.approval_work_flow.views import approval_work_flow_api_bp
from app.api.v1.blog_post.views import blog_post_api_bp
from app.api.v1.brd_deduction.views import brd_deductions_api_bp
from app.api.v1.company_location.views import company_locations_api_bp
from app.api.v1.employee_types.views import brd_deductions_api_bp
from app.api.v1.deductions.views import deduction_api_bp
from app.api.v1.departments.views import department_api_bp
from app.api.v1.employees_.views import employees_api_bp
from app.api.v1.features.views import features_api_bp
from app.api.v1.feedback.views import feedback_api_bp
from app.api.v1.fingerprint.views import fingerprint_api_bp
from app.api.v1.insurance.views import insurance_api_bp
from app.api.v1.leave_types.views import leave_types_api_bp
from app.api.v1.reimbursements.views import reimbursement_api_bp
from app.api.v1.plans.views import plans_api_bp
from app.api.v1.route_plans.views import route_plan_requirements_api_bp
from app.api.v1.rra.views import rra_api_bp
from app.api.v1.rssb.views import rssb_api_bp
from app.api.v1.subscription.views import subscription_api_bp
from app.api.v1.shifts.views import shifts_api_bp
from app.api.v1.taxbracket_.views import taxbracket_api_bp
from app.api.v1.user_roles.views import user_role_api_bp


api_bp.register_blueprint(company_api_users)
api_bp.register_blueprint(leave_api_bp)
api_bp.register_blueprint(advance_api_bp)
api_bp.register_blueprint(document_api_bp)
api_bp.register_blueprint(attendance_api_bp)
api_bp.register_blueprint(user_api_auth)
api_bp.register_blueprint(company_api_bp)
api_bp.register_blueprint(approval_types_api_bp)
api_bp.register_blueprint(approval_work_flow_api_bp)
api_bp.register_blueprint(blog_post_api_bp)
api_bp.register_blueprint(brd_deductions_api_bp)
api_bp.register_blueprint(company_locations_api_bp)
api_bp.register_blueprint(deduction_api_bp)
api_bp.register_blueprint(department_api_bp)
api_bp.register_blueprint(employees_api_bp)
api_bp.register_blueprint(features_api_bp)
api_bp.register_blueprint(feedback_api_bp)
api_bp.register_blueprint(fingerprint_api_bp)
api_bp.register_blueprint(insurance_api_bp)
api_bp.register_blueprint(leave_types_api_bp)
api_bp.register_blueprint(plans_api_bp)
api_bp.register_blueprint(reimbursement_api_bp)
api_bp.register_blueprint(route_plan_requirements_api_bp)
api_bp.register_blueprint(rra_api_bp)
api_bp.register_blueprint(rssb_api_bp)
api_bp.register_blueprint(shifts_api_bp)
api_bp.register_blueprint(subscription_api_bp)
api_bp.register_blueprint(taxbracket_api_bp)
api_bp.register_blueprint(user_role_api_bp)


# Test route
@api_bp.route("/test")
def index():
    return jsonify(message="Welcome to the NETPIPO API"), 200