from tests.base_test_case import BaseTestCase
from app.models.central import User
from app import db
from uuid import UUID
from werkzeug.exceptions import NotFound
from app.models.central import Company

class TestUserModel(BaseTestCase):
    """Test user model"""
    def setUp(self):
        super().setUp()
        self.company = Company(
            company_name="Test Company",
            company_alias="Test Alias",
            database_name="test_db",
            company_tin="*********",
            rssb_number="*********",
            phone_number="*********0",
            email="<EMAIL>",
            city="Test City",
            country="Rwanda",
            number_employee=10,
            company_type="private"
            )        
        db.session.add(self.company)
        db.session.commit()
        self.user = User(
            company_id = self.company.company_id,
            username = "username",
            password = "pass123",
            role = "admin",
            email = "<EMAIL>",
            phone_number = "0781039941"
            )
        
        db.session.add(self.user)
        db.session.commit()

    def test_create_user(self):
        """Test user creation"""
        # Check if user is saved
        self.assertIsNotNone(self.user.user_id)
        self.assertEqual(self.user.username, "username")
        self.assertEqual(self.user.password, "pass123")
        self.assertEqual(self.user.role, "admin")
        self.assertEqual(self.user.email, "<EMAIL>")
        self.assertEqual(self.user.phone_number, "0781039941")

    def test_to_dict(self):
        """Test user to dictionary"""
        user_dict = self.user.to_dict()
        self.assertEqual(user_dict['username'], "username")
        self.assertEqual(user_dict['role'], "admin")
        self.assertEqual(user_dict['email'], "<EMAIL>")
        self.assertEqual(user_dict['phone_number'], "0781039941")

    def test_update_user(self):
        """Test user update"""
        updated = User.update_user(
            self.user.user_id,
            username="new_username",
            role="user",
            email="<EMAIL>",
            password="new_pass",
            phone_number="0781039943"
        )
        updated_user = User.get_user_by_id(self.user.user_id)
        self.assertTrue(updated)
        self.assertEqual(updated_user.username, "new_username")
        self.assertEqual(updated_user.role, "user")
        self.assertEqual(updated_user.email, "<EMAIL>")
        self.assertEqual(updated_user.phone_number, "0781039943")

    def test_get_users(self):
        """Test get all users"""
        users = User.get_users()
        self.assertIsNotNone(users)
        self.assertEqual(len(users), 1)
        self.assertEqual(users[0]['username'], "username")
        self.assertEqual(users[0]['role'], "admin")
        self.assertEqual(users[0]['email'], "<EMAIL>")
        self.assertEqual(users[0]['phone_number'], "0781039941")

    def test_get_user_by_id(self):
        """Test get user by id"""
        user = User.get_user_by_id(self.user.user_id)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, "username")
        self.assertEqual(user.role, "admin")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.phone_number, "0781039941")
        self.assertEqual(user.user_id, self.user.user_id)

    def test_delete_user(self):
        """Test delete user"""
        user = User.get_user_by_id(self.user.user_id)
        deleted = User.delete_user(self.user.user_id)
        self.assertTrue(deleted)
        with self.assertRaises(NotFound):
            User.get_user_by_id(self.user.user_id)
        users = User.get_users()
        self.assertEqual(len(users), 0)


                         


        