from tests.base_test_case import BaseTestCase
from app.models.central import TaxBracket
from app import db
from uuid import UUID
from werkzeug.exceptions import NotFound

class TestTaxBracketModel(BaseTestCase):
    """Test tax bracket model"""
    def setUp(self):
        super().setUp()
        self.tax_bracket = TaxBracket(
            lower_bound = 0,
            upper_bound = 10000,
            rate = 0.1
            )

    def test_insert_tax_bracket(self):
        """Test tax bracket creation"""
        # insert the tax bracket
        added = self.tax_bracket.insert_taxbracket()
        self.assertTrue(added)
        # Check if tax bracket is saved
        self.assertIsNotNone(self.tax_bracket.bracket_id)
        self.assertEqual(self.tax_bracket.lower_bound, 0)
        self.assertEqual(self.tax_bracket.upper_bound, 10000)
        self.assertEqual(self.tax_bracket.rate, 0.1)

    def test_rate_validation(self):
        """Test tax bracket rate validation"""
        # Test rate validation with a rate greater than 1(100%)
        with self.assertRaises(ValueError):
            self.tax_bracket.rate = 1.1
            self.tax_bracket.insert_taxbracket()

        # Test rate validation with a rate less than 0(0%)
        with self.assertRaises(ValueError):
            self.tax_bracket.rate = -0.1
            self.tax_bracket.insert_taxbracket()

        # Test with a rate not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.rate = "Bitemwana"
            self.tax_bracket.insert_taxbracket()  

    def test_bound_valisation(self):
        """Test tax bracket bound validation"""
        # Test with upper bound less than lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = 10000
            self.tax_bracket.upper_bound = 0
            self.tax_bracket.insert_taxbracket()  

        # Test with upper bound equal to lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = 10000
            self.tax_bracket.upper_bound = 10000
            self.tax_bracket.insert_taxbracket() 

        # Test with upper bound not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = 0
            self.tax_bracket.upper_bound = "Bitemwana"
            self.tax_bracket.insert_taxbracket()

        # Test with lower bound not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = "Bitemwana"
            self.tax_bracket.upper_bound = 10000
            self.tax_bracket.insert_taxbracket()

        # Test with Both bounds not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = "Bitemwana"
            self.tax_bracket.upper_bound = "Bite"
            self.tax_bracket.insert_taxbracket()

         # Test with a negative lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = -10000
            self.tax_bracket.upper_bound = 10000
            self.tax_bracket.insert_taxbracket()

        # Test with a negative upper bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = 0
            self.tax_bracket.upper_bound = -10000
            self.tax_bracket.insert_taxbracket()

        # Test with a negative lower and upper bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = -10000
            self.tax_bracket.upper_bound = -10000
            self.tax_bracket.insert_taxbracket()

        # Test with a very large upper bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = 0
            self.tax_bracket.upper_bound = 1000000000000     
            self.tax_bracket.insert_taxbracket()

        # Test with a very large lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.lower_bound = 1000000000000
            self.tax_bracket.upper_bound = 10000
            self.tax_bracket.insert_taxbracket()

    def test_get_tax_bracket(self):
        """Test tax bracket retrieval"""
        # insert the tax bracket
        self.tax_bracket.insert_taxbracket()
        tax_brackets = TaxBracket.get_taxbrackets()
        self.assertTrue(tax_brackets)
        self.assertIsInstance(tax_brackets, list)


    def test_update_tax_bracket(self):
        """Test tax bracket update"""
        # insert the tax bracket
        self.tax_bracket.insert_taxbracket()
        # update the tax bracket
        upper_bound = 20000
        self.tax_bracket.update_permanent_employee_taxbracket(
            self.tax_bracket.bracket_id,
            self.tax_bracket.lower_bound, upper_bound, self.tax_bracket.rate
            )
        self.assertEqual(self.tax_bracket.upper_bound, 20000)
        self.assertEqual(self.tax_bracket.lower_bound, 0)
        self.assertEqual(self.tax_bracket.rate, 0.1)

        lower_bound = 10000
        self.tax_bracket.update_permanent_employee_taxbracket(
            self.tax_bracket.bracket_id,
            lower_bound, upper_bound, self.tax_bracket.rate
            )
        self.assertEqual(self.tax_bracket.upper_bound, 20000)
        self.assertEqual(self.tax_bracket.lower_bound, 10000)
        self.assertEqual(self.tax_bracket.rate, 0.1)

        rate = 0.2
        self.tax_bracket.update_permanent_employee_taxbracket(
            self.tax_bracket.bracket_id,
            lower_bound, upper_bound, rate
            )
        self.assertEqual(self.tax_bracket.upper_bound, 20000)
        self.assertEqual(self.tax_bracket.lower_bound, 10000)
        self.assertEqual(self.tax_bracket.rate, 0.2)

    def test_update_tax_bracket_with_invalid_data(self):
        """Test with different invalid data to see how the update will behave."""
        # insert the tax bracket
        self.tax_bracket.insert_taxbracket()
        # Test with a rate greater than 1(100%)
        rate = 1.1
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.lower_bound, self.tax_bracket.upper_bound, rate
                )

        # Test with a rate less than 0(0%)
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.lower_bound, self.tax_bracket.upper_bound, -0.1
                )

        # Test with a rate not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.lower_bound, self.tax_bracket.upper_bound, "Bitemwana"
                )  

        # Test with upper bound less than lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.upper_bound, self.tax_bracket.lower_bound, self.tax_bracket.rate
                )  

        # Test with upper bound equal to lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.upper_bound, self.tax_bracket.upper_bound, self.tax_bracket.rate
                ) 

        # Test with upper bound not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.lower_bound, "Bitemwana", self.tax_bracket.rate
                )

        # Test with lower bound not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                "Bitemwana", self.tax_bracket.upper_bound, self.tax_bracket.rate
                )

        # Test with Both bounds not a number
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                "Bitemwana", "Bite", self.tax_bracket.rate
                )
            
        # Test with a negative lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                -10000, self.tax_bracket.upper_bound, self.tax_bracket.rate
                )
    def test_update_with_negative_upper_bound(self):
        """Test with a negative upper bound"""
        self.tax_bracket.insert_taxbracket()    
        # Test with a negative upper bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.lower_bound, -10000, self.tax_bracket.rate
                )
    def test_update_with_negative_lower_bound(self):
        """Test with a negative lower bound"""
        self.tax_bracket.insert_taxbracket() 
        # Test with a negative lower and upper bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                -10000, -10000, self.tax_bracket.rate
                )
    def  test_update_with_very_large_upper_bound(self):
        """Test with a very large upper bound"""
        self.tax_bracket.insert_taxbracket()     
        # Test with a very large upper bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                self.tax_bracket.lower_bound, 1000000000000, self.tax_bracket.rate
                )
    def test_update_with_very_large_lower_bound(self):
        """Test with a very large lower bound"""   
        self.tax_bracket.insert_taxbracket() 
        # Test with a very large lower bound
        with self.assertRaises(ValueError):
            self.tax_bracket.update_permanent_employee_taxbracket(
                self.tax_bracket.bracket_id,
                1000000000000, self.tax_bracket.upper_bound, self.tax_bracket.rate
                )
        
        
            
        
            
            

        