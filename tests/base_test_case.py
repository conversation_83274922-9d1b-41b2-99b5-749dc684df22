# tests/base_test_case.py
import unittest
from app import app, db
from flask_testing import TestCase

class BaseTestCase(TestCase):
    def create_app(self):
        # Load the test configuration
        app.config.from_object('app.config.TestConfig')
        return app

    def setUp(self):
        db.create_all()
        db.session.commit()

    def tearDown(self):
        db.session.remove()
        db.drop_all()
