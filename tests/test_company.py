from tests.base_test_case import BaseTestCase
from app.models.central import Company
import unittest
from app import db

class TestCompanyModel(BaseTestCase ):
    """Test company model"""
    def setUp(self):
        super().setUp()
        self.company = Company(
            company_name="Another Test Company",
            company_alias="Another Test Alias",
            database_name="another_test_db",
            company_tin="*********",
            rssb_number="*********",
            phone_number="0*********",
            email="<EMAIL>",
            city="Another Test City",
            country="Rwanda",
            number_employee=20,
            company_type="public"
        )
        db.session.add(self.company)
        db.session.commit()


    def test_create_company(self):
        """Test company creation"""
        self.assertIsNotNone(self.company.company_id)
        self.assertEqual(self.company.company_name, "Another Test Company")
        self.assertEqual(self.company.company_alias, "Another Test Alias")
        self.assertEqual(self.company.database_name, "another_test_db")
        self.assertEqual(self.company.company_tin, "*********")
        self.assertEqual(self.company.rssb_number, "*********")
        self.assertEqual(self.company.phone_number, "0*********")
        self.assertEqual(self.company.email, "<EMAIL>")
        self.assertEqual(self.company.city, "Another Test City")
        self.assertEqual(self.company.country, "Rwanda")
        self.assertEqual(self.company.number_employee, 20)
        self.assertEqual(self.company.company_type, "public")

        

    def test_to_dict(self):
        """Test company to dictionary"""
        company_dict = self.company.to_dict()
        self.assertEqual(company_dict['company_name'], "Another Test Company")
        self.assertEqual(company_dict['company_alias'], "Another Test Alias")
        self.assertEqual(company_dict['database_name'], "another_test_db")
        self.assertEqual(company_dict['company_tin'], "*********")
        self.assertEqual(company_dict['rssb_number'], "*********")
        self.assertEqual(company_dict['phone_number'], "0*********")
        self.assertEqual(company_dict['email'], "<EMAIL>")
        self.assertEqual(company_dict['city'], "Another Test City")
        self.assertEqual(company_dict['country'], "Rwanda")
        self.assertEqual(company_dict['number_employee'], 20)
        self.assertEqual(company_dict['company_type'], "public")

        
    def test_update_company(self):
        """Test company update"""
        updated = Company.update_company(
            self.company.company_id,
            company_name="Updated Company",
            company_tin="*********",
            rssb_number="*********",
            phone_number="0*********",
            email="<EMAIL>",
            city="Updated City",
            country="Updated Country",
            number_employee=20,
            company_type="public"
        )

        updated_company = db.session.get(Company, self.company.company_id)
        company_id = updated_company.company_id
        print("company_id that has been updated: ", company_id)
        self.assertTrue(updated)
        self.assertEqual(updated_company.company_name, "Updated Company")
        self.assertEqual(updated_company.company_tin, "*********")
        self.assertEqual(updated_company.rssb_number, "*********")
        self.assertEqual(updated_company.phone_number, "0*********")
        self.assertEqual(updated_company.email, "<EMAIL>")
        self.assertEqual(updated_company.city, "Updated City")
        self.assertEqual(updated_company.country, "Updated Country")
        self.assertEqual(updated_company.number_employee, 20)
        self.assertEqual(updated_company.company_type, "public")

    def test_get_company(self):
        """Test retrieving a company by ID"""
        retrieved_company = Company.get_company_by_id(self.company.company_id)
        self.assertIsNotNone(retrieved_company)
        self.assertEqual(retrieved_company['company_name'], "Another Test Company")
        self.assertEqual(retrieved_company['company_alias'], "Another Test Alias")
        self.assertEqual(retrieved_company['database_name'], "another_test_db")
        self.assertEqual(retrieved_company['company_tin'], "*********")
        self.assertEqual(retrieved_company['rssb_number'], "*********")
        self.assertEqual(retrieved_company['phone_number'], "0*********")
        self.assertEqual(retrieved_company['email'], "<EMAIL>")
        self.assertEqual(retrieved_company['city'], "Another Test City")
        self.assertEqual(retrieved_company['country'], "Rwanda")
        self.assertEqual(retrieved_company['number_employee'], 20)
        self.assertEqual(retrieved_company['company_type'], "public")

    def test_get_companies(self):
        """Test get companies"""
        companies = Company.get_companies()
        self.assertEqual(len(companies), 1)
        self.assertEqual(companies[0]['company_name'], "Another Test Company")
        self.assertEqual(companies[0]['company_alias'], "Another Test Alias")
        self.assertEqual(companies[0]['database_name'], "another_test_db")
        self.assertEqual(companies[0]['company_tin'], "*********")
        self.assertEqual(companies[0]['rssb_number'], "*********")
        self.assertEqual(companies[0]['phone_number'], "0*********")
        self.assertEqual(companies[0]['email'], "<EMAIL>")
        self.assertEqual(companies[0]['city'], "Another Test City")
        self.assertEqual(companies[0]['country'], "Rwanda")
        self.assertEqual(companies[0]['number_employee'], 20)
        self.assertEqual(companies[0]['company_type'], "public")


if __name__ == '__main__':
    unittest.main()