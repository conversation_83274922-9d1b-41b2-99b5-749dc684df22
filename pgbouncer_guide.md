# PgBouncer Implementation Guide

This guide explains how to implement PgBouncer for your multi-tenant application to solve the "too many clients already" PostgreSQL error.

## What is PgBouncer?

PgBouncer is a lightweight connection pooler for PostgreSQL that dramatically reduces the number of connections to your database server. Instead of each application process creating its own connection to PostgreSQL, they connect to PgBouncer, which maintains a pool of connections to PostgreSQL.

## Benefits for Your Application

1. **Reduced Connection Count**: Instead of 100+ connections to PostgreSQL, you might only need 10-20 actual connections.
2. **Better Resource Utilization**: Less memory usage on your PostgreSQL server.
3. **Connection Reuse**: Faster response times by reusing existing connections.
4. **Automatic Cleanup**: Stale connections are automatically cleaned up.

## Installation Steps

1. **Install PgBouncer on Your Server**:
   
   Run the provided `setup_pgbouncer.sh` script:
   ```bash
   sudo ./setup_pgbouncer.sh
   ```
   
   This script will:
   - Install PgBouncer
   - Create the configuration files
   - Set up user authentication
   - Start and enable the PgBouncer service

2. **Update Your Application's Environment Variables**:

   Modify your `.env` file to connect to PgBouncer instead of directly to PostgreSQL:
   ```
   DB_HOST=localhost  # Or the server where PgBouncer is running
   DB_PORT=6432       # PgBouncer port (default is 6432)
   ```

3. **Test the Connection**:

   ```bash
   psql -h localhost -p 6432 -U your_db_user -d your_database
   ```

## Configuration Explanation

The PgBouncer configuration (`/etc/pgbouncer/pgbouncer.ini`) includes:

```ini
[databases]
* = host=127.0.0.1 port=5432

[pgbouncer]
listen_addr = *
listen_port = 6432
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 10
min_pool_size = 0
reserve_pool_size = 5
```

Key settings:

- **pool_mode = transaction**: Connections are returned to the pool after each transaction.
- **max_client_conn = 1000**: Maximum number of client connections allowed.
- **default_pool_size = 10**: Each database will have up to 10 server connections.
- **max_db_connections = 50**: Maximum connections to any single database.

## Monitoring PgBouncer

1. **Check PgBouncer Status**:
   ```bash
   sudo systemctl status pgbouncer
   ```

2. **View PgBouncer Logs**:
   ```bash
   sudo tail -f /var/log/postgresql/pgbouncer.log
   ```

3. **Connect to PgBouncer Admin Console**:
   ```bash
   psql -p 6432 -U postgres pgbouncer
   ```
   
   Useful commands in the admin console:
   ```sql
   SHOW STATS;
   SHOW POOLS;
   SHOW CLIENTS;
   SHOW SERVERS;
   ```

## Troubleshooting

1. **Connection Refused**:
   - Check if PgBouncer is running: `sudo systemctl status pgbouncer`
   - Verify listening port: `netstat -tuln | grep 6432`
   - Check firewall settings: `sudo ufw status`

2. **Authentication Failed**:
   - Verify userlist.txt has correct MD5 password
   - Check PostgreSQL pg_hba.conf allows connections from PgBouncer

3. **Too Many Connections Error Still Occurs**:
   - Increase `default_pool_size` in pgbouncer.ini
   - Check if application is properly closing connections
   - Verify that the application is actually connecting to PgBouncer and not directly to PostgreSQL

## Best Practices

1. **Regular Monitoring**: Set up monitoring for PgBouncer and PostgreSQL connection counts.

2. **Gradual Deployment**: Test with a subset of your application before full deployment.

3. **Connection Validation**: Use `pool_pre_ping=True` in SQLAlchemy to validate connections before use.

4. **Timeout Settings**: Configure appropriate timeouts in both PgBouncer and your application.

## Additional Resources

- [PgBouncer Documentation](https://www.pgbouncer.org/usage.html)
- [PostgreSQL Connection Pooling](https://www.postgresql.org/docs/current/pooling.html)
- [SQLAlchemy Engine Configuration](https://docs.sqlalchemy.org/en/14/core/engines.html)
