"""added created_at in user_role table

Revision ID: cbea70a80c19
Revises: 03ce68978a41
Create Date: 2024-09-13 10:11:09.090565

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cbea70a80c19'
down_revision: Union[str, None] = '03ce68978a41'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.add_column('user_roles', sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True))
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_roles', 'created_at')
    
    # ### end Alembic commands ###
    """
    pass
