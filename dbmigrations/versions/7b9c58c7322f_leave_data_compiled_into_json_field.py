"""leave_data compiled into json field

Revision ID: 7b9c58c7322f
Revises: 2c7d27420c15
Create Date: 2025-05-05 12:56:03.055240

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7b9c58c7322f'
down_revision: Union[str, None] = '2c7d27420c15'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('companies', sa.Column('leave_data', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('companies', 'leave_data')
    # ### end Alembic commands ###
