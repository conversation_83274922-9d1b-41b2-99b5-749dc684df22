"""added getting 6 decimal places

Revision ID: 245aaac6668a
Revises: e75e24568318
Create Date: 2025-03-31 11:43:16.889428

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '245aaac6668a'
down_revision: Union[str, None] = 'e75e24568318'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('brd_deductions', 'deduction_rate',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('casuals_tax_brackets', 'lower_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('casuals_tax_brackets', 'upper_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=2),
               existing_nullable=False)
    op.alter_column('casuals_tax_brackets', 'rate',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('consultant_tax_brackets', 'lower_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('consultant_tax_brackets', 'upper_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=2),
               existing_nullable=False)
    op.alter_column('consultant_tax_brackets', 'rate',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('payments', 'amount',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=2),
               existing_nullable=False)
    op.alter_column('plans', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('second_employee_tax_brackets', 'lower_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('second_employee_tax_brackets', 'upper_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=2),
               existing_nullable=False)
    op.alter_column('second_employee_tax_brackets', 'rate',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('tax_brackets', 'lower_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    op.alter_column('tax_brackets', 'upper_bound',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=2),
               existing_nullable=False)
    op.alter_column('tax_brackets', 'rate',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=12, scale=6),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tax_brackets', 'rate',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('tax_brackets', 'upper_bound',
               existing_type=sa.Numeric(precision=12, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('tax_brackets', 'lower_bound',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('second_employee_tax_brackets', 'rate',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('second_employee_tax_brackets', 'upper_bound',
               existing_type=sa.Numeric(precision=12, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('second_employee_tax_brackets', 'lower_bound',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('plans', 'price',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('payments', 'amount',
               existing_type=sa.Numeric(precision=12, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('consultant_tax_brackets', 'rate',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('consultant_tax_brackets', 'upper_bound',
               existing_type=sa.Numeric(precision=12, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('consultant_tax_brackets', 'lower_bound',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('casuals_tax_brackets', 'rate',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('casuals_tax_brackets', 'upper_bound',
               existing_type=sa.Numeric(precision=12, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('casuals_tax_brackets', 'lower_bound',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('brd_deductions', 'deduction_rate',
               existing_type=sa.Numeric(precision=12, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    # ### end Alembic commands ###
