"""added the tag_id as a foreign key to the blog_posts table

Revision ID: 78e4550ed58f
Revises: f1dbcaabafe8
Create Date: 2025-01-18 20:10:48.214128

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '78e4550ed58f'
down_revision: Union[str, None] = 'f1dbcaabafe8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('blog_posts', sa.Column('tag_id', sa.UUID(), nullable=True))
    op.alter_column('blog_posts', 'image_path',
               existing_type=sa.VARCHAR(length=255),
               nullable=True,
               existing_server_default=sa.text("'default.jpg'::character varying"))
    op.create_index(op.f('ix_blog_posts_date_posted'), 'blog_posts', ['date_posted'], unique=False)
    op.create_unique_constraint(None, 'blog_posts', ['blog_id'])
    op.create_foreign_key(None, 'blog_posts', 'blogtags', ['tag_id'], ['tag_id'], ondelete='SET NULL')
    op.create_unique_constraint(None, 'blogcategories', ['category_id'])
    op.create_unique_constraint(None, 'blogtags', ['tag_id'])
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'blogtags', type_='unique')
    op.drop_constraint(None, 'blogcategories', type_='unique')
    op.drop_constraint(None, 'blog_posts', type_='foreignkey')
    op.drop_constraint(None, 'blog_posts', type_='unique')
    op.drop_index(op.f('ix_blog_posts_date_posted'), table_name='blog_posts')
    op.alter_column('blog_posts', 'image_path',
               existing_type=sa.VARCHAR(length=255),
               nullable=False,
               existing_server_default=sa.text("'default.jpg'::character varying"))
    op.drop_column('blog_posts', 'tag_id')
    # ### end Alembic commands ###
    """
    pass
