"""created blog_posts and blogcategories tables with UUIDs

Revision ID: f1dbcaabafe8
Revises: 3163982f5ba0
Create Date: 2025-01-17 11:41:55.594935
"""

from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
import uuid


# revision identifiers, used by Alembic.
revision: str = 'f1dbcaabafe8'
down_revision: Union[str, None] = '3163982f5ba0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create blogcategories table
    """
    op.create_table(
        'blogcategories',
        sa.Column('category_id', sa.dialects.postgresql.UUID(as_uuid=True), primary_key=True, nullable=False, default=uuid.uuid4),
        sa.Column('category_name', sa.String(length=100), nullable=False, unique=True),
        sa.Column('category_description', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    )

    # Create blog_posts table
    op.create_table(
        'blog_posts',
        sa.Column('blog_id', sa.dialects.postgresql.UUID(as_uuid=True), primary_key=True, nullable=False, default=uuid.uuid4),
        sa.Column('title', sa.String(length=100), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('date_posted', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('category_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('image_path', sa.String(length=255), nullable=False, server_default='default.jpg'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['category_id'], ['blogcategories.category_id'], ondelete='SET NULL'),
    )
    """
    pass

def downgrade() -> None:
    """
    # Drop blog_posts table
    op.drop_table('blog_posts')

    # Drop blogcategories table
    op.drop_table('blogcategories')
    """
    pass
