"""added trial_until and subscrion_end_period columns in the companies database

Revision ID: 1ee14e4aeb24
Revises: c06acf84f5fb
Create Date: 2025-02-12 11:01:03.964599

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ee14e4aeb24'
down_revision: Union[str, None] = 'c06acf84f5fb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.add_column('companies', sa.Column('trial_until', sa.DateTime(), nullable=True))
    op.add_column('companies', sa.Column('subscription_end_period', sa.DateTime(), nullable=True))"""
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_column('companies', 'subscription_end_period')
    op.drop_column('companies', 'trial_until')"""
    # ### end Alembic commands ###
