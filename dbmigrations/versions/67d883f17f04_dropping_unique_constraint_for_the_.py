"""dropping unique constraint for the contribution_name

Revision ID: 67d883f17f04
Revises: 1ee14e4aeb24
Create Date: 2025-02-13 11:31:07.348901

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '67d883f17f04'
down_revision: Union[str, None] = '1ee14e4aeb24'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the unique constraint on contribution_name
    op.drop_constraint(
        "nsf_contributions_contribution_name_key",
        "nsf_contributions",
        type_="unique"
    )
    """
    pass


def downgrade():
    """
    # Recreate the unique constraint on contribution_name
    op.create_unique_constraint(
        "nsf_contributions_contribution_name_key",
        "nsf_contributions",
        ["contribution_name"]
    )
    # ### end Alembic commands ###
    """
    pass
