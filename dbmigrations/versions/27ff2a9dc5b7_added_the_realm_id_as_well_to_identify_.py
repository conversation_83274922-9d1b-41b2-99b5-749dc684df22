"""added the realm_id as well to identify the company'

Revision ID: 27ff2a9dc5b7
Revises: a757e5f63551
Create Date: 2025-04-10 15:27:42.487628

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '27ff2a9dc5b7'
down_revision: Union[str, None] = 'a757e5f63551'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.add_column('companies', sa.Column('quickbooks_realm_id', sa.String(length=255), nullable=True))"""
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_column('companies', 'quickbooks_realm_id')"""
    # ### end Alembic commands ###
