"""added the compreface api key, longitude and latitude columns in companies table

Revision ID: e75e24568318
Revises: 67d883f17f04
Create Date: 2025-03-21 11:38:31.688010

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e75e24568318'
down_revision: Union[str, None] = '67d883f17f04'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.add_column('companies', sa.Column('compreface_api_key', sa.String(length=128), nullable=True))
    op.add_column('companies', sa.Column('latitude', sa.Numeric(precision=9, scale=6), nullable=True))
    op.add_column('companies', sa.Column('longitude', sa.Numeric(precision=9, scale=6), nullable=True))"""
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_column('companies', 'longitude')
    op.drop_column('companies', 'latitude')
    op.drop_column('companies', 'compreface_api_key')"""
    # ### end Alembic commands ###
