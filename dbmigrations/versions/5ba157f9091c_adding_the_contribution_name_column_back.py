"""adding the contribution_name column back

Revision ID: 5ba157f9091c
Revises: 879f2e587029
Create Date: 2025-01-03 17:47:22.277188

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5ba157f9091c'
down_revision: Union[str, None] = '879f2e587029'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.add_column('nsf_contributions', sa.Column('contribution_name', sa.String(length=255), nullable=False))"""
   
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_column('nsf_contributions', 'contribution_name')"""
    # ### end Alembic commands ###
