"""added the approval_types table

Revision ID: bb6f54ef1dbf
Revises: 3ea2f815ee6f
Create Date: 2024-12-02 13:01:53.956960

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bb6f54ef1dbf'
down_revision: Union[str, None] = '3ea2f815ee6f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
