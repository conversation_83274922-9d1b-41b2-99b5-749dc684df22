"""adding approval_types table

Revision ID: 72c0de34c7c4
Revises: bb6f54ef1dbf
Create Date: 2024-12-02 13:04:26.500589

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '72c0de34c7c4'
down_revision: Union[str, None] = 'bb6f54ef1dbf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('approval_types',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('approval_type', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_table('approval_types')
    # ### end Alembic commands ###
    """
