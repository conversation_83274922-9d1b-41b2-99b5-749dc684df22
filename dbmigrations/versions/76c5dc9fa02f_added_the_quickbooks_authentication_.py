"""added the quickbooks authentication details

Revision ID: 76c5dc9fa02f
Revises: 245aaac6668a
Create Date: 2025-04-10 13:53:01.816819

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '76c5dc9fa02f'
down_revision: Union[str, None] = '245aaac6668a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.add_column('companies', sa.Column('quickbooks_access_token', sa.String(length=255), nullable=True))
    op.add_column('companies', sa.Column('quickbooks_refresh_token', sa.String(length=255), nullable=True))
    op.add_column('companies', sa.Column('quickbooks_authorization_code', sa.String(length=255), nullable=True))"""
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_column('companies', 'quickbooks_authorization_code')
    op.drop_column('companies', 'quickbooks_refresh_token')
    op.drop_column('companies', 'quickbooks_access_token')"""
    # ### end Alembic commands ###
