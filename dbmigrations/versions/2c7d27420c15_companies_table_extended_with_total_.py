"""companies table extended with total_leave_days column

Revision ID: 2c7d27420c15
Revises: 1a567c496739
Create Date: 2025-05-02 13:09:10.892797

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2c7d27420c15'
down_revision: Union[str, None] = '1a567c496739'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('companies', sa.Column('total_annual_leave_days', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('companies', 'total_annual_leave_days')
    # ### end Alembic commands ###
