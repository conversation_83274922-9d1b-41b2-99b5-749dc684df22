"""Enter commit message: removed the tag_id and category_id from the blog_posts table. added extra association tables to associate the blog with more than one tag and also more than one category

Revision ID: 89c2e481f15e
Revises: 78e4550ed58f
Create Date: 2025-01-20 10:07:36.214384

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '89c2e481f15e'
down_revision: Union[str, None] = '78e4550ed58f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('blog_post_categories',
    sa.Column('blog_id', sa.UUID(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['blog_id'], ['blog_posts.blog_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['category_id'], ['blogcategories.category_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('blog_id', 'category_id')
    )
    op.create_table('blog_post_tags',
    sa.Column('blog_id', sa.UUID(), nullable=False),
    sa.Column('tag_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['blog_id'], ['blog_posts.blog_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tag_id'], ['blogtags.tag_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('blog_id', 'tag_id')
    )
    op.drop_constraint('blog_posts_tag_id_fkey', 'blog_posts', type_='foreignkey')
    op.drop_constraint('blog_posts_category_id_fkey', 'blog_posts', type_='foreignkey')
    op.drop_column('blog_posts', 'tag_id')
    op.drop_column('blog_posts', 'category_id')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('blog_posts', sa.Column('category_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('blog_posts', sa.Column('tag_id', sa.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key('blog_posts_category_id_fkey', 'blog_posts', 'blogcategories', ['category_id'], ['category_id'], ondelete='SET NULL')
    op.create_foreign_key('blog_posts_tag_id_fkey', 'blog_posts', 'blogtags', ['tag_id'], ['tag_id'], ondelete='SET NULL')
    op.drop_table('blog_post_tags')
    op.drop_table('blog_post_categories')
    # ### end Alembic commands ###
    """
    pass
