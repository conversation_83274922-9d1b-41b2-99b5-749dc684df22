"""removed the unique constraint for the contribution_name

Revision ID: 0d9a54c14093
Revises: 4efac9abde81
Create Date: 2025-01-04 11:20:30.151441

"""
from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = '0d9a54c14093'
down_revision: Union[str, None] = '4efac9abde81'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop the existing unique constraint using its actual name
    op.drop_constraint('nsf_contributions_contribution_name_key', 'nsf_contributions', type_='unique')


def downgrade() -> None:
    # Recreate the unique constraint in case of a downgrade
    op.create_unique_constraint('nsf_contributions_contribution_name_key', 'nsf_contributions', ['contribution_name'])
