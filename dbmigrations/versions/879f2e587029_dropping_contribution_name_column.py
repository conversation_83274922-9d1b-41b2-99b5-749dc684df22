"""dropping contribution_name column

Revision ID: 879f2e587029
Revises: e97dbda84979
Create Date: 2025-01-03 17:39:36.108484

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '879f2e587029'
down_revision: Union[str, None] = 'e97dbda84979'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop the contribution_name column
    """op.drop_column('nsf_contributions', 'contribution_name')"""


def downgrade() -> None:
    # Recreate the contribution_name column
    """op.add_column(
        'nsf_contributions',
        sa.Column('contribution_name', sa.String(length=255), nullable=False)
    )"""
