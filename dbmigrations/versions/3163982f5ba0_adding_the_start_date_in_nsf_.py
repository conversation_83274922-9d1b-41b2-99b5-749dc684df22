"""adding the start_date in nsf_contributions_table

Revision ID: 3163982f5ba0
Revises: 0d9a54c14093
Create Date: 2025-01-09 15:08:18.902107

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3163982f5ba0'
down_revision: Union[str, None] = '0d9a54c14093'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nsf_contributions', sa.Column('start_date', sa.Date(), nullable=True))
    op.add_column('nsf_contributions', sa.Column('end_date', sa.Date(), nullable=True))
    op.create_unique_constraint('uq_contribution_name_start_date', 'nsf_contributions', ['contribution_name', 'start_date'])
    # ### end Alembic commands ###
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_contribution_name_start_date', 'nsf_contributions', type_='unique')
    op.drop_column('nsf_contributions', 'end_date')
    op.drop_column('nsf_contributions', 'start_date')
    # ### end Alembic commands ###
    """
    pass