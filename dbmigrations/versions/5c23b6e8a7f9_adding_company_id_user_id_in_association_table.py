"""Migrate company_id to user_company_association table

Revision ID: 5c23b6e8a7f9
Revises: 5b902ddf556a
Create Date: 2024-11-16 14:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# Revision identifiers, used by Alembic.
revision = '5c23b6e8a7f9'
down_revision = '5b902ddf556a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Step 1: Migrate data from users.company_id to user_company_association
    op.execute("""
        INSERT INTO user_company_association (user_id, company_id, added_at)
        SELECT user_id, company_id, NOW()
        FROM users
        WHERE company_id IS NOT NULL
          AND NOT EXISTS (
              SELECT 1
              FROM user_company_association
              WHERE user_company_association.user_id = users.user_id
                AND user_company_association.company_id = users.company_id
          )
    """)


def downgrade() -> None:

    # Step 2: Repopulate users.company_id from user_company_association
    op.execute("""
        UPDATE users
        SET company_id = subquery.company_id
        FROM (
            SELECT DISTINCT ON (user_id) user_id, company_id
            FROM user_company_association
            ORDER BY user_id, added_at DESC
        ) AS subquery
        WHERE users.user_id = subquery.user_id
    """)
