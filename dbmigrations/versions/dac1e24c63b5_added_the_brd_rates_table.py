"""added the BRD Rates table

Revision ID: dac1e24c63b5
Revises: c48a09724919
Create Date: 2024-10-21 16:25:26.381039

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dac1e24c63b5'
down_revision: Union[str, None] = 'c48a09724919'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'nsf_contributions', ['nsf_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'nsf_contributions', type_='unique')
    # ### end Alembic commands ###
