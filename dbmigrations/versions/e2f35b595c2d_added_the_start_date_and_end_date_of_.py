"""added the start_date and end_date of nsf contributions'

Revision ID: e2f35b595c2d
Revises: 72c0de34c7c4
Create Date: 2025-01-03 10:06:41.938006

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e2f35b595c2d'
down_revision: Union[str, None] = '72c0de34c7c4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.add_column('nsf_contributions', sa.Column('start_date', sa.Date(), nullable=True))
    op.add_column('nsf_contributions', sa.Column('end_date', sa.Date(), nullable=True))
    op.create_unique_constraint(None, 'nsf_contributions', ['start_date'])"""
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_constraint(None, 'nsf_contributions', type_='unique')
    op.drop_column('nsf_contributions', 'end_date')
    op.drop_column('nsf_contributions', 'start_date')"""
    # ### end Alembic commands ###
