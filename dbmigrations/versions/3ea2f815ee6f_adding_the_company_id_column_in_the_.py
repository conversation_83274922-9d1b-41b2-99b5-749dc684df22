"""adding the company_id column in the users table

Revision ID: 3ea2f815ee6f
Revises: 5c23b6e8a7f9
Create Date: 2024-11-20 13:22:17.633672

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3ea2f815ee6f'
down_revision: Union[str, None] = '5c23b6e8a7f9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('company_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'users', 'companies', ['company_id'], ['company_id'])
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'company_id')
    # ### end Alembic commands ###
    """
    pass
