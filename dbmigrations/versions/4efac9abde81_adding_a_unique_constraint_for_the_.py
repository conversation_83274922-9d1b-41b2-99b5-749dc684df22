"""adding a unique constraint for the start_date on the same name

Revision ID: 4efac9abde81
Revises: 5ba157f9091c
Create Date: 2025-01-04 11:14:13.432360

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4efac9abde81'
down_revision: Union[str, None] = '5ba157f9091c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_constraint('nsf_contributions_start_date_key', 'nsf_contributions', type_='unique')
    """op.create_unique_constraint('uq_contribution_name_start_date', 'nsf_contributions', ['contribution_name', 'start_date'])
    op.create_unique_constraint(None, 'nsf_contributions', ['contribution_name'])"""
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """op.drop_constraint(None, 'nsf_contributions', type_='unique')
    op.drop_constraint('uq_contribution_name_start_date', 'nsf_contributions', type_='unique')"""
    #op.create_unique_constraint('nsf_contributions_start_date_key', 'nsf_contributions', ['start_date'])
    # ### end Alembic commands ###
