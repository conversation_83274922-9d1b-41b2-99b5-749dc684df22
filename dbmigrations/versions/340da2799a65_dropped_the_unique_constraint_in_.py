"""dropped the unique constraints in contribution_name and start_date

Revision ID: 340da2799a65
Revises: e2f35b595c2d
Create Date: 2025-01-03 10:20:49.415442

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '340da2799a65'
down_revision: Union[str, None] = 'e2f35b595c2d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop the unique constraint on contribution_name
    """op.drop_constraint('nsf_contributions_contribution_name_key', 'nsf_contributions', type_='unique')
    
    # Drop the unique constraint on start_date
    op.drop_constraint('nsf_contributions_start_date_key', 'nsf_contributions', type_='unique')"""


def downgrade() -> None:
    # Recreate the unique constraint on contribution_name
    """op.create_unique_constraint('nsf_contributions_contribution_name_key', 'nsf_contributions', ['contribution_name'])
    
    # Recreate the unique constraint on start_date
    op.create_unique_constraint('nsf_contributions_start_date_key', 'nsf_contributions', ['start_date'])"""
