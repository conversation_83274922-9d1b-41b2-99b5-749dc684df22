"""Extended plans table - added price_per_employee column

Revision ID: 1a567c496739
Revises: 27ff2a9dc5b7
Create Date: 2025-04-28 16:35:47.170063

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1a567c496739'
down_revision: Union[str, None] = '27ff2a9dc5b7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.add_column('plans', sa.Column('price_per_employee', sa.Numeric(precision=12, scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('plans', 'price_per_employee')
    
