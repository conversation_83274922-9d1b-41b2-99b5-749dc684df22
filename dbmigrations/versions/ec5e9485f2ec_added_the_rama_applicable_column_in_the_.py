"""added the rama_applicable column in the companies table

Revision ID: ec5e9485f2ec
Revises: dd390dfbce4f
Create Date: 2025-05-23 11:24:57.330651

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ec5e9485f2ec'
down_revision: Union[str, None] = 'dd390dfbce4f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('companies', sa.Column('rama_applicable', sa.<PERSON>(), nullable=True))
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('companies', 'rama_applicable')
    # ### end Alembic commands ###
