"""added description in the plan_requirements table

Revision ID: 5b902ddf556a
Revises: 52a58fa949a2
Create Date: 2024-11-08 15:27:36.583678

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5b902ddf556a'
down_revision: Union[str, None] = '52a58fa949a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('companies', 'plan_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.add_column('route_plan_requirements', sa.Column('description', sa.Text(), nullable=True))
    # ### end Alembic commands ###
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('route_plan_requirements', 'description')
    op.alter_column('companies', 'plan_id',
               existing_type=sa.UUID(),
               nullable=False)
    # ### end Alembic commands ###
    """
    pass
