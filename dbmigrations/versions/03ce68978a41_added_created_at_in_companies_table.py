"""added created_at in companies table

Revision ID: 03ce68978a41
Revises: 
Create Date: 2024-09-11 14:18:10.994661

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine.reflection import Inspector


# revision identifiers, used by Alembic.
revision: str = '03ce68978a41'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Get the connection and inspector object to check if the column exists
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    
    # Check if 'created_at' column exists in 'users' table
    columns = [col['name'] for col in inspector.get_columns('users')]
    
    if 'created_at' not in columns:
        # Add the column only if it doesn't exist
        op.add_column('users', sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True))


def downgrade() -> None:
    # Drop the column in downgrade (if needed)
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    
    # Check if the column exists before dropping
    columns = [col['name'] for col in inspector.get_columns('users')]
    
    if 'created_at' in columns:
        op.drop_column('users', 'created_at')
