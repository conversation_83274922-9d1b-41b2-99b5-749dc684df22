"""refactored the companies table to include the plan id

Revision ID: 52a58fa949a2
Revises: dac1e24c63b5
Create Date: 2024-11-05 10:56:20.181856

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '52a58fa949a2'
down_revision: Union[str, None] = 'dac1e24c63b5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """"
    # Step 1: Add the column as nullable
    op.add_column('companies', sa.Column('plan_id', sa.UUID(), nullable=True))
    
    # Step 2: Populate existing records with a default value
    default_plan_id = '5a7d4444-5566-4b9d-9807-3ea3226d93e0'
    op.execute(f"UPDATE companies SET plan_id = '{default_plan_id}' WHERE plan_id IS NULL")
    
    # Step 3: Alter the column to be non-nullable
    op.alter_column('companies', 'plan_id', existing_type=sa.UUID(), nullable=False)

    # Step 4: Create the foreign key constraint
    op.create_foreign_key(None, 'companies', 'plans', ['plan_id'], ['plan_id'])
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'companies', type_='foreignkey')
    op.drop_column('companies', 'plan_id')
    # ### end Alembic commands ###
    """
    pass

