"""added otp fields on user

Revision ID: dd390dfbce4f
Revises: 7b9c58c7322f
Create Date: 2025-05-21 11:56:57.167550

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dd390dfbce4f'
down_revision: Union[str, None] = '7b9c58c7322f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('users', sa.Column('otp', sa.String(length=255), nullable=True))
    op.add_column('users', sa.Column('otp_expiry', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('is_otp_used', sa.Boolean(), nullable=True))


def downgrade() -> None:
    op.drop_column('users', 'is_otp_used')
    op.drop_column('users', 'otp_expiry')
    op.drop_column('users', 'otp')
