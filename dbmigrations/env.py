from logging.config import fileConfig
from sqlalchemy import create_engine
from sqlalchemy import pool
from alembic import context
from sqlalchemy import engine_from_config
from app import db
from app.models.central import User, Company, TaxBracket, ConsultantTaxBracket
from app.models.central import UserRole, EmployeeType, SecondEmployeeTaxBracket, CasualsTaxBracket
from app.models.central import NsfContributions, BrdDeductions, Features, plan_features, Plans
from app.models.central import RoutePlanRequirement
from app.models.central_payments import Payments
import os
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Add your model's MetaData object here for 'autogenerate' support
target_metadata = db.metadata

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""

    # Load the database URL from the environment variables
    url = os.getenv("DATABASE_URL")

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    pwd = os.getenv('DB_PASSWORD')
    db_user = os.getenv('DB_USER')
    db_host = os.getenv('DB_HOST')
    name = os.getenv('DB_NAME')

    url = f'postgresql://{db_user}:{pwd}@{db_host}/{name}'
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
        url=url
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

# Determine whether to run in 'offline' or 'online' mode
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()

