"""added the location_names of the clockin and clockout'

Revision ID: 0a1f30498b14
Revises: 2453b2f3d572
Create Date: 2025-02-17 16:32:56.475796

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0a1f30498b14'
down_revision: Union[str, None] = '2453b2f3d572'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.add_column('attendance', sa.Column('clockin_location_name', sa.String(length=128), nullable=True))
    #op.add_column('attendance', sa.Column('clockout_location_name', sa.String(length=128), nullable=True))

    # ### end Alembic commands ###
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('attendance', 'clockout_location_name')
    #op.drop_column('attendance', 'clockin_location_name')
    # ### end Alembic commands ###
    pass
