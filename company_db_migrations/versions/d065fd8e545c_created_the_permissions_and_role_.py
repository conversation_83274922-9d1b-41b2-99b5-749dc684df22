"""created the permissions and role_permissions tables also modified user_roles and users tables

Revision ID: d065fd8e545c
Revises: 634dd68531fc
Create Date: 2024-10-08 12:52:30.528762

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd065fd8e545c'
down_revision: Union[str, None] = '634dd68531fc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('permissions',
    sa.Column('permission_id', sa.UUID(), nullable=False),
    sa.Column('permission_name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('permission_id'),
    sa.UniqueConstraint('permission_name')
    )
    op.create_table('role_permissions',
    sa.Column('role_permission_id', sa.UUID(), nullable=False),
    sa.Column('role_id', sa.UUID(), nullable=False),
    sa.Column('permission_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.permission_id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['user_roles.role_id'], ),
    sa.PrimaryKeyConstraint('role_permission_id')
    )
    op.add_column('user_roles', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('role_id', sa.UUID(), nullable=False))
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=128),
               nullable=True)
    op.create_foreign_key(None, 'users', 'user_roles', ['role_id'], ['role_id'])
    op.drop_column('users', 'role')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('role', sa.VARCHAR(length=128), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=128),
               nullable=False)
    op.drop_column('users', 'role_id')
    op.drop_column('user_roles', 'description')
    op.drop_table('role_permissions')
    op.drop_table('permissions')
    # ### end Alembic commands ###
    """
    pass
