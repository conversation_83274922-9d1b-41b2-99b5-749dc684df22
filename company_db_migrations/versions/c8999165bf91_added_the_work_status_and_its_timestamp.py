"""added the work_status and its timestamp

Revision ID: c8999165bf91
Revises: c5d981601c44
Create Date: 2024-10-16 13:29:53.473312

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c8999165bf91'
down_revision: Union[str, None] = 'c5d981601c44'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # Create the ENUM type if it doesn't exist
    work_status_enum = sa.Enum('leave', 'off', name='work_status_enum')
    work_status_enum.create(op.get_bind(), checkfirst=True)

    # Add new columns
    op.add_column('attendance', sa.Column('work_status', work_status_enum, nullable=True))
    op.add_column('attendance', sa.Column('status_timestamp', sa.DateTime(), nullable=True))
    """
    pass


def downgrade() -> None:
    """
    # Drop the columns
    op.drop_column('attendance', 'status_timestamp')
    op.drop_column('attendance', 'work_status')

    # Drop the ENUM type
    work_status_enum = sa.Enum(name='work_status_enum')
    work_status_enum.drop(op.get_bind(), checkfirst=True)
    """
    pass
