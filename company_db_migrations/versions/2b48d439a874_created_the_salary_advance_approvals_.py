"""created the salary_advance_approvals table

Revision ID: 2b48d439a874
Revises: a16f4514efd1
Create Date: 2024-12-05 09:36:08.484885

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2b48d439a874'
down_revision: Union[str, None] = 'a16f4514efd1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.create_table('salary_advance_approvals',
    #sa.Column('approval_id', sa.UUID(), nullable=False),
    #sa.Column('request_id', sa.UUID(), nullable=False),
    #sa.Column('approver_id', sa.UUID(), nullable=False),
    #sa.Column('approver_role', sa.String(length=128), nullable=False),
    #sa.Column('status', sa.String(length=128), nullable=True),
    #sa.Column('remarks', sa.Text(), nullable=True),
    #sa.Column('created_at', sa.DateTime(), nullable=True),
    #sa.Column('updated_at', sa.DateTime(), nullable=True),
    #sa.ForeignKeyConstraint(['request_id'], ['salary_advance_requests.request_id'], ),
    #sa.PrimaryKeyConstraint('approval_id')
    #)
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_table('salary_advance_approvals')
    # ### end Alembic commands ###
    pass