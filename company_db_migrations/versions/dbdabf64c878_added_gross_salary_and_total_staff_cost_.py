"""added gross_salary and total_staff_cost in employees table and made the net_salary nullable

Revision ID: dbdabf64c878
Revises: 805365ed4e4c
Create Date: 2024-11-04 16:33:32.081826

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dbdabf64c878'
down_revision: Union[str, None] = '805365ed4e4c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'attendance', 'employees', ['employee_id'], ['employee_id'])
    op.create_foreign_key(None, 'deductions', 'employees', ['employee_id'], ['employee_id'])
    op.add_column('employees', sa.Column('gross_salary', sa.Float(), nullable=True))
    op.add_column('employees', sa.Column('total_staff_cost', sa.Float(), nullable=True))
    op.alter_column('employees', 'net_salary',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.create_foreign_key(None, 'payrolls', 'employees', ['employee_id'], ['employee_id'])
    op.create_foreign_key(None, 'reimbursements', 'employees', ['employee_id'], ['employee_id'])
    op.create_foreign_key(None, 'users', 'employees', ['employee_id'], ['employee_id'])
    # ### end Alembic commands ###
    """


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_constraint(None, 'reimbursements', type_='foreignkey')
    op.drop_constraint(None, 'payrolls', type_='foreignkey')
    op.alter_column('employees', 'net_salary',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.drop_column('employees', 'total_staff_cost')
    op.drop_column('employees', 'gross_salary')
    op.drop_constraint(None, 'deductions', type_='foreignkey')
    op.drop_constraint(None, 'attendance', type_='foreignkey')
    # ### end Alembic commands ###
    """
