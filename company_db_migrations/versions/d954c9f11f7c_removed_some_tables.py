"""removed some tables

Revision ID: d954c9f11f7c
Revises: 6c9c31a7437c
Create Date: 2024-10-10 15:30:27.123456

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd954c9f11f7c'
down_revision = '6c9c31a7437c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """

    # Drop constraints that depend on the user_roles table
    op.drop_constraint('role_permissions_role_id_fkey', 'role_permissions', type_='foreignkey')
    op.drop_constraint('users_role_id_fkey', 'users', type_='foreignkey')

    # Drop the tables after the dependent constraints have been removed
    op.drop_table('role_permissions')
    op.drop_table('user_roles')
    op.drop_table('permissions')

    # Add the new column to the users table with a default value to avoid null issues
    op.add_column('users', sa.Column('role', sa.String(length=128), nullable=False, server_default='user'))

    # Drop the role_id column from the users table since it's no longer needed
    op.drop_column('users', 'role_id')

    # Remove the default value constraint now that existing data has been set
    op.alter_column('users', 'role', server_default=None)
    # ### end Alembic commands ###
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###

    # Recreate the user_roles table
    op.create_table(
        'user_roles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('role_name', sa.String(length=255), nullable=False),
        sa.PrimaryKeyConstraint('id', name='user_roles_pkey')
    )

    # Recreate the permissions table
    op.create_table(
        'permissions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('permission_name', sa.String(length=255), nullable=False),
        sa.PrimaryKeyConstraint('id', name='permissions_pkey')
    )

    # Recreate the role_permissions table
    op.create_table(
        'role_permissions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('role_id', sa.Integer(), nullable=False),
        sa.Column('permission_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['role_id'], ['user_roles.id'], name='role_permissions_role_id_fkey'),
        sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name='role_permissions_permission_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='role_permissions_pkey')
    )

    # Add the role_id column back to the users table
    op.add_column('users', sa.Column('role_id', sa.Integer(), nullable=True))
    op.create_foreign_key('users_role_id_fkey', 'users', 'user_roles', ['role_id'], ['id'])

    # Remove the role column from the users table
    op.drop_column('users', 'role')
    # ### end Alembic commands ###
    """
    pass
