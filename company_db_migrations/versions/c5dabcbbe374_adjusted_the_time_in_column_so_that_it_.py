from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c5dabcbbe374'
down_revision = '4f990c500f62'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Alter the time_in column to be nullable and remove the default value
    op.alter_column('attendance', 'time_in',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    server_default=None)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Revert the time_in column to not nullable and set default to datetime.now
    op.alter_column('attendance', 'time_in',
                    existing_type=sa.DateTime(),
                    nullable=False,
                    server_default=sa.func.now())
    # ### end Alembic commands ###
