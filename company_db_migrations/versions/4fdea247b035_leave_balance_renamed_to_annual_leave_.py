"""leave_balance renamed to annual_leave_balance

Revision ID: 4fdea247b035
Revises: 88e4fcd03785
Create Date: 2025-05-02 13:58:29.398393

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4fdea247b035'
down_revision: Union[str, None] = '88e4fcd03785'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('annual_leave_balance', sa.Numeric(precision=12, scale=9), nullable=True))
    op.drop_column('employees', 'leave_balance')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.<PERSON>umn('leave_balance', sa.NUMERIC(precision=12, scale=9), autoincrement=False, nullable=True))
    op.drop_column('employees', 'annual_leave_balance')
    # ### end Alembic commands ###
    """
    pass
    