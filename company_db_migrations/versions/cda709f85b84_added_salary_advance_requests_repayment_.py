"""added salary_advance_requests repayment_logs salary_advance_approvals tables

Revision ID: cda709f85b84
Revises: 3d84e68a2efe
Create Date: 2024-11-29 16:15:17.937279

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cda709f85b84'
down_revision: Union[str, None] = '3d84e68a2efe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('repayment_logs',
    sa.Column('repayment_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('amount_paid', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('payment_date', sa.DateTime(), nullable=True),
    sa.Column('balance_after_payment', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('repayment_id')
    )
    op.create_table('salary_advance_requests',
    sa.Column('request_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('installments', sa.Integer(), nullable=True),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('status', sa.Enum('pending', 'approved', 'rejected', name='request_status_enum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('request_id')
    )
    op.create_table('salary_advance_approvals',
    sa.Column('approval_id', sa.UUID(), nullable=False),
    sa.Column('request_id', sa.UUID(), nullable=False),
    sa.Column('approver_id', sa.UUID(), nullable=False),
    sa.Column('approver_role', sa.String(length=128), nullable=False),
    sa.Column('status', sa.Enum('pending', 'approved', 'rejected', name='salary_approval_status_enum'), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['request_id'], ['salary_advance_requests.request_id'], ),
    sa.PrimaryKeyConstraint('approval_id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('salary_advance_approvals')
    op.drop_table('salary_advance_requests')
    op.drop_table('repayment_logs')
    # ### end Alembic commands ###
    """
    pass