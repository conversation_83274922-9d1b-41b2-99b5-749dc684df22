"""modified the salary_advance_requests table by adding balance column also added request_id in the repayment_logs table

Revision ID: 2311eac7e194
Revises: 6fc6ec51dd35
Create Date: 2024-12-09 14:13:41.324127

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2311eac7e194'
down_revision: Union[str, None] = '6fc6ec51dd35'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    
    # ### commands auto generated by Alembic - please adjust! ###
  
    #op.add_column('repayment_logs', sa.Column('request_id', sa.UUID(), nullable=False))
    op.create_foreign_key(None, 'repayment_logs', 'salary_advance_requests', ['request_id'], ['request_id'])
    #op.add_column('salary_advance_requests', sa.Column('balance', sa.Numeric(precision=10, scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
     
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('salary_advance_requests', 'balance')
    op.drop_constraint(None, 'repayment_logs', type_='foreignkey')
    #op.drop_column('repayment_logs', 'request_id')
  
    # ### end Alembic commands ###
    