"""made the phone number unique

Revision ID: 36710f09323f
Revises: 9c496b5f6c74
Create Date: 2024-11-13 12:25:31.036200

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '36710f09323f'
down_revision: Union[str, None] = '9c496b5f6c74'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'employees', ['phone'])
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'employees', type_='unique')
    # ### end Alembic commands ###
    """
    pass
