"""employees table extended with leave_record column

Revision ID: 3c788cd352b2
Revises: 1b6a89639c73
Create Date: 2025-05-02 13:23:54.422113

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3c788cd352b2'
down_revision: Union[str, None] = '1b6a89639c73'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('leave_balance', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'leave_balance')
    # ### end Alembic commands ###
