"""added is_void column and void_reason in attendance table

Revision ID: 422ea09b5381
Revises: cf2dd7f02101
Create Date: 2024-10-29 09:49:23.138622

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '422ea09b5381'
down_revision: Union[str, None] = 'cf2dd7f02101'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('attendance', sa.Column('is_void', sa.<PERSON>(), nullable=True))
    op.add_column('attendance', sa.Column('void_reason', sa.Text(), nullable=True))
    # ### end Alembic commands ###
    """


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('attendance', 'void_reason')
    op.drop_column('attendance', 'is_void')
    # ### end Alembic commands ###
    """
