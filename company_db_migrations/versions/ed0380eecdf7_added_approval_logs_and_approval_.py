"""added approval_logs and approval_workflows tables

Revision ID: ed0380eecdf7
Revises: e68711eff962
Create Date: 2024-11-25 14:39:03.598434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ed0380eecdf7'
down_revision: Union[str, None] = 'e68711eff962'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('approval_workflows',
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('approval_type', sa.String(), nullable=False),
    sa.Column('role', sa.String(), nullable=False),
    sa.Column('sequence_order', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('workflow_id')
    )
    op.create_table('approval_logs',
    sa.Column('log_id', sa.UUID(), nullable=False),
    sa.Column('application_id', sa.UUID(), nullable=False),
    sa.Column('approver_role', sa.String(), nullable=False),
    sa.Column('approver_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['application_id'], ['leave_applications.leave_id'], ),
    sa.ForeignKeyConstraint(['approver_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('log_id')
    )
    op.alter_column('leave_approvals', 'approver_role',
               existing_type=postgresql.ENUM('supervisor', 'hr', name='approver_role_enum'),
               type_=sa.String(length=128),
               existing_nullable=False)
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('leave_approvals', 'approver_role',
               existing_type=sa.String(length=128),
               type_=postgresql.ENUM('supervisor', 'hr', name='approver_role_enum'),
               existing_nullable=False)
    op.drop_table('approval_logs')
    op.drop_table('approval_workflows')
    # ### end Alembic commands ###  
    """
    pass