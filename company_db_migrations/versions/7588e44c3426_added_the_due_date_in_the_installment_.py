"""added the due_date in the installment plan

Revision ID: 7588e44c3426
Revises: de57fde36ab1
Create Date: 2024-12-11 19:09:05.945155

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '7588e44c3426'
down_revision: Union[str, None] = 'de57fde36ab1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:

    # ### commands auto generated by Alembic - please adjust! ###
    #op.add_column('installment_plans', sa.Column('due_date', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('installment_plans', 'due_date')
    # ### end Alembic commands ###
    pass