"""added a column in payrolls table to add the brd deductions

Revision ID: d5643e795a61
Revises: 56fa10cc09e1
Create Date: 2024-12-31 14:22:07.148971

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd5643e795a61'
down_revision: Union[str, None] = '56fa10cc09e1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
   
    #op.add_column('payrolls', sa.Column('brd_deductions', sa.Float(), nullable=True))
    # ### end Alembic commands ###
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('payrolls', 'brd_deductions')
    pass
    
    # ### end Alembic commands ###
