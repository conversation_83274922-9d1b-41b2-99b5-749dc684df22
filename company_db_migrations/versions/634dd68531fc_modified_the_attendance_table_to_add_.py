"""modified the attendance table to add clockin_location and clockout_location

Revision ID: 634dd68531fc
Revises: b27335c7259b
Create Date: 2024-10-02 14:53:01.199043

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect

# revision identifiers, used by Alembic.
revision: str = '634dd68531fc'
down_revision: Union[str, None] = 'b27335c7259b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Get the database connection and inspector to check existing columns
    conn = op.get_bind()
    inspector = inspect(conn)

    # Get all the columns in the 'attendance' table
    columns = [col['name'] for col in inspector.get_columns('attendance')]

    # Check if 'location' column exists and rename it to 'clockin_location' if needed
    if 'location' in columns:
        op.alter_column('attendance', 'location', new_column_name='clockin_location', type_=sa.String(length=128), existing_type=sa.String(length=128), nullable=True)

    # Check if 'clockout_location' column exists and add it if not present
    if 'clockout_location' not in columns:
        op.add_column('attendance', sa.Column('clockout_location', sa.String(length=128), nullable=True))


def downgrade() -> None:
    # Drop the 'clockout_location' column if it exists
    op.drop_column('attendance', 'clockout_location')
    
    # Rename 'clockin_location' back to 'location' if needed
    op.alter_column('attendance', 'clockin_location', new_column_name='location', type_=sa.String(length=128), existing_type=sa.String(length=128), nullable=True)
