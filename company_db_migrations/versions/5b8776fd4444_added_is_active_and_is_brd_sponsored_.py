"""added is_active and is_brd_sponsored columns in employees

Revision ID: 5b8776fd4444
Revises: 1d32f8979d25
Create Date: 2024-10-22 09:20:30.207382

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5b8776fd4444'
down_revision: Union[str, None] = '1d32f8979d25'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('is_active', sa.String(length=16), nullable=True))
    op.add_column('employees', sa.Column('is_brd_sponsored', sa.String(length=16), nullable=True))
    # ### end Alembic commands ###
    """


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'is_brd_sponsored')
    op.drop_column('employees', 'is_active')
    # ### end Alembic commands ###
    """
