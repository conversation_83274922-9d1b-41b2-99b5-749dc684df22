"""changed leave_balance to decimal

Revision ID: 88e4fcd03785
Revises: 3c788cd352b2
Create Date: 2025-05-02 13:34:12.248882

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '88e4fcd03785'
down_revision: Union[str, None] = '3c788cd352b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('employees', 'leave_balance',
               existing_type=sa.INTEGER(),
               type_=sa.Numeric(precision=12, scale=9),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('employees', 'leave_balance',
               existing_type=sa.Numeric(precision=12, scale=9),
               type_=sa.INTEGER(),
               existing_nullable=True)
    # ### end Alembic commands ###
