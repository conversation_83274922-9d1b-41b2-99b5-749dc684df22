"""adding the attendance table

Revision ID: 126eb4afb605
Revises: 68c387fb2962
Create Date: 2024-10-01 11:52:05.779171

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine.reflection import Inspector


# revision identifiers, used by Alembic.
revision: str = '126eb4afb605'
down_revision: Union[str, None] = '68c387fb2962'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Get the current connection and inspector
    bind = op.get_bind()
    inspector = Inspector.from_engine(bind)

    # Check if 'attendance' table already exists
    if 'attendance' not in inspector.get_table_names():
        # Create 'attendance' table if it does not exist
        op.create_table('attendance',
            sa.Column('attendance_id', sa.UUID(), nullable=False),
            sa.Column('time_in', sa.DateTime(), nullable=True),
            sa.Column('time_out', sa.DateTime(), nullable=True),
            sa.Column('employee_id', sa.UUID(), nullable=False),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('recorgnition_status', sa.String(length=128), nullable=True),
            sa.Column('department_id', sa.UUID(), nullable=True),
            sa.Column('location', sa.String(length=128), nullable=False),
            sa.Column('device_used', sa.String(length=128), nullable=False),
            sa.Column('field_in', sa.String(length=128), nullable=True),
            sa.Column('field_out', sa.String(length=128), nullable=True),
            sa.Column('break_in', sa.DateTime(), nullable=True),
            sa.Column('break_out', sa.DateTime(), nullable=True),
            sa.Column('break_duration', sa.String(length=128), nullable=True),
            sa.Column('total_duration', sa.String(length=128), nullable=True),
            sa.ForeignKeyConstraint(['department_id'], ['departments.department_id'], ),
            sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
            sa.PrimaryKeyConstraint('attendance_id')
        )


def downgrade() -> None:
    # Drop the 'attendance' table if it exists
    op.drop_table('attendance')
