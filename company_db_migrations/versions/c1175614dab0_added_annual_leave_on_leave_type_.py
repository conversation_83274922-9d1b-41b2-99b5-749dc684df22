"""added annual_leave on leave_type LeaveApplication column

Revision ID: c1175614dab0
Revises: 18a363f1c12f
Create Date: 2025-05-13 11:38:21.762220

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c1175614dab0'
down_revision: Union[str, None] = '18a363f1c12f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('employees', 'net_salary',
    #            existing_type=sa.NUMERIC(precision=12, scale=6),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=True)
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('employees', 'net_salary',
    #            existing_type=sa.Numeric(precision=18, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=6),
    #            existing_nullable=True)
    pass
    # ### end Alembic commands ###
