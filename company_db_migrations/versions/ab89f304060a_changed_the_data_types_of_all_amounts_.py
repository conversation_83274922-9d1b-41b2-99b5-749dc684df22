"""changed the data types of all amounts to decimal

Revision ID: ab89f304060a
Revises: ec36f43cc5af
Create Date: 2025-03-28 14:58:32.509895

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ab89f304060a'
down_revision: Union[str, None] = 'ec36f43cc5af'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('deductions', 'deduction_amount',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('employees', 'net_salary',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'gross_salary',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'total_staff_cost',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'transport_allowance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'housing_allowance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'communication_allowance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'over_time',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'other_allowance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'salary_advance_balance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('insurances', 'employee_rate',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('insurances', 'employer_rate',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'basic_salary',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'transport_allowance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'housing_allowance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'bonus',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'medical_fee',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'gross_salary',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_pension',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_pension',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_maternity',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_maternity',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'payee',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'cbhi',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'total_deductions',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'net_pay',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'net_salary',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'reimbursement',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'advance',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'brd_deductions',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'other_deductions',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('reimbursements', 'reimbursement_amount',
    #            existing_type=sa.DOUBLE_PRECISION(precision=53),
    #            type_=sa.Numeric(precision=12, scale=2),
    #            existing_nullable=False)
    # ### end Alembic commands ###
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('reimbursements', 'reimbursement_amount',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'other_deductions',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'brd_deductions',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'advance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'reimbursement',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'net_salary',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'net_pay',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'total_deductions',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'cbhi',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'payee',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_maternity',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_maternity',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_pension',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_pension',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'gross_salary',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'medical_fee',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'bonus',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'housing_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'transport_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'basic_salary',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('insurances', 'employer_rate',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('insurances', 'employee_rate',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # op.alter_column('employees', 'salary_advance_balance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'other_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'over_time',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'communication_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'housing_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'transport_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'total_staff_cost',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'gross_salary',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('employees', 'net_salary',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=True)
    # op.alter_column('deductions', 'deduction_amount',
    #            existing_type=sa.Numeric(precision=12, scale=2),
    #            type_=sa.DOUBLE_PRECISION(precision=53),
    #            existing_nullable=False)
    # ### end Alembic commands ###
    pass
