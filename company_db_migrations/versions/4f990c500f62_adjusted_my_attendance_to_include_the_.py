"""adjusted my Attendance to include the being and end of the leave

Revision ID: 4f990c500f62
Revises: c8999165bf91
Create Date: 2024-10-16 14:25:47.208296

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4f990c500f62'
down_revision: Union[str, None] = 'c8999165bf91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('attendance', sa.Column('time_off_begin_date', sa.DateTime(), nullable=True))
    op.add_column('attendance', sa.Column('time_off_end_date', sa.DateTime(), nullable=True))
    op.drop_column('attendance', 'status_timestamp')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('attendance', sa.Column('status_timestamp', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.drop_column('attendance', 'time_off_end_date')
    op.drop_column('attendance', 'time_off_begin_date')
    # ### end Alembic commands ###
    """
    pass
