"""removed the employees relation in repayment logs table

Revision ID: b7008ad9a370
Revises: 2311eac7e194
Create Date: 2024-12-09 14:30:07.097923

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b7008ad9a370'
down_revision: Union[str, None] = '2311eac7e194'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
   
    #op.drop_constraint('repayment_logs_employee_id_fkey', 'repayment_logs', type_='foreignkey')
    #op.drop_column('repayment_logs', 'employee_id')
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.add_column('repayment_logs', sa.Column('employee_id', sa.UUID(), autoincrement=False, nullable=False))
    #op.create_foreign_key('repayment_logs_employee_id_fkey', 'repayment_logs', 'employees', ['employee_id'], ['employee_id'])
    op.alter_column('leave_applications', 'status',
               existing_type=sa.Enum('pending', 'approved', 'rejected', name='leaves_status_enum'),
               type_=postgresql.ENUM('pending', 'approved', 'rejected', name='leave_status_enum'),
               existing_nullable=True)
    
    # ### end Alembic commands ###
