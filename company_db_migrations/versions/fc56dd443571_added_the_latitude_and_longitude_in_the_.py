"""added the latitude and longitude in the site table

Revision ID: fc56dd443571
Revises: 0a1f30498b14
Create Date: 2025-03-24 11:32:09.600434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'fc56dd443571'
down_revision: Union[str, None] = '0a1f30498b14'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    #op.add_column('sites', sa.Column('longitude', sa.Float(), nullable=True))
    #op.add_column('sites', sa.Column('latitude', sa.Float(), nullable=True))
    # ### end Alembic commands ###
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('sites', 'latitude')
    #op.drop_column('sites', 'longitude')
    pass

    # ### end Alembic commands ###
