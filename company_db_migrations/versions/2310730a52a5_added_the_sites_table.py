"""added the sites table

Revision ID: 2310730a52a5
Revises: 020bb127a345
Create Date: 2024-10-14 11:27:10.555243

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2310730a52a5'
down_revision: Union[str, None] = '020bb127a345'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sites',
    sa.Column('site_id', sa.UUID(), nullable=False),
    sa.Column('site_name', sa.String(length=128), nullable=False),
    sa.Column('location', sa.String(length=256), nullable=True),
    sa.PrimaryKeyConstraint('site_id')
    )
    op.add_column('employees', sa.Column('site_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'employees', 'sites', ['site_id'], ['site_id'])
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'employees', type_='foreignkey')
    op.drop_column('employees', 'site_id')
    op.drop_table('sites')
    # ### end Alembic commands ###
    """
    pass
