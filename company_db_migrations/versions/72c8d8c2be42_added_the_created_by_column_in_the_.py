"""added the created_by column in the payroll table

Revision ID: 72c8d8c2be42
Revises: d8f19ccef676
Create Date: 2025-06-13 13:49:27.710663

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '72c8d8c2be42'
down_revision: Union[str, None] = 'd8f19ccef676'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ####
    op.add_column('payrolls', sa.Column('created_by', sa.UUID(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payrolls', 'created_by')
    # ### end Alembic commands ###
