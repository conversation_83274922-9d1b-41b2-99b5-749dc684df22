"""added the salary_advance_balance in the employees table

Revision ID: 224215a21ff3
Revises: 2b48d439a874
Create Date: 2024-12-06 11:38:03.297452

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '224215a21ff3'
down_revision: Union[str, None] = '2b48d439a874'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.add_column('employees', sa.Column('salary_advance_balance', sa.Float(), nullable=True))
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_column('employees', 'salary_advance_balance')
    # ### end Alembic commands ###
    """
    pass
