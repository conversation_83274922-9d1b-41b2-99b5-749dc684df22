"""manual scripts for annual_leave type

Revision ID: af074bb6e683
Revises: c1175614dab0
Create Date: 2025-05-13 11:50:17.428863

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'af074bb6e683'
down_revision: Union[str, None] = 'c1175614dab0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
     # Create the enum type if it doesn't exist
    op.execute(
        DO $$ BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'leave_types_enum') THEN
                CREATE TYPE leave_types_enum AS ENUM ('leave', 'off');
            END IF;
        END $$;
    
    # Add the new value 'annual_leave' to the Enum type 'leave_types_enum'
    op.execute("ALTER TYPE leave_types_enum ADD VALUE 'annual_leave'")
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    # Create a new Enum type without 'annual_leave'
    op.execute("CREATE TYPE leave_types_enum_new AS ENUM('leave', 'off')")

    # Update the column to use the new Enum type
    op.execute(
        ALTER TABLE leave_application
        ALTER COLUMN leave_type TYPE leave_types_enum_new
        USING leave_type::TEXT::leave_types_enum_new
    )

    # Drop the old Enum type
    op.execute("DROP TYPE leave_types_enum")

    # Rename the new Enum type to the original name
    op.execute("ALTER TYPE leave_types_enum_new RENAME TO leave_types_enum")
    # ### end Alembic commands ###
    """
    pass
