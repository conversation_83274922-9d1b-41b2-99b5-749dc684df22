"""added the site_id in attendance

Revision ID: 484b8923d986
Revises: 2310730a52a5
Create Date: 2024-10-14 11:31:58.998364

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '484b8923d986'
down_revision: Union[str, None] = '2310730a52a5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """"
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('attendance', sa.Column('site_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'attendance', 'sites', ['site_id'], ['site_id'])
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'attendance', type_='foreignkey')
    op.drop_column('attendance', 'site_id')
    # ### end Alembic commands ###
    """
    pass
