"""added net_salary, reimbursements and salary_advances in the payrolls table

Revision ID: a488f82e3225
Revises: 8bd84012d5b2
Create Date: 2024-12-19 12:27:39.449445

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a488f82e3225'
down_revision: Union[str, None] = '8bd84012d5b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.add_column('payrolls', sa.Column('net_salary', sa.Float(), nullable=True))
    #op.add_column('payrolls', sa.Column('reimbursement', sa.Float(), nullable=True))
    #op.add_column('payrolls', sa.Column('advance', sa.Float(), nullable=True))
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('payrolls', 'advance')
    #op.drop_column('payrolls', 'reimbursement')
    #op.drop_column('payrolls', 'net_salary')
    
    # ### end Alembic commands ###
    pass