"""added a relationship between employees and leave_applications

Revision ID: 56060b321993
Revises: ed0380eecdf7
Create Date: 2024-11-25 16:39:23.289804

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '56060b321993'
down_revision: Union[str, None] = 'ed0380eecdf7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
