"""made the phone_number not unique

Revision ID: 805365ed4e4c
Revises: edeb43e21be3
Create Date: 2024-10-31 14:45:24.209669

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '805365ed4e4c'
down_revision: Union[str, None] = 'edeb43e21be3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('employees_phone_key', 'employees', type_='unique')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('employees_phone_key', 'employees', ['phone'])
    # ### end Alembic commands ###

    """
    pass