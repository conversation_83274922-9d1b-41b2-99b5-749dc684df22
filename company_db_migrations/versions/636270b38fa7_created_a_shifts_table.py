"""created a shifts table

Revision ID: 636270b38fa7
Revises: d5643e795a61
Create Date: 2025-01-10 13:42:03.031915

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '636270b38fa7'
down_revision: Union[str, None] = 'd5643e795a61'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('shifts',
    sa.Column('shift_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('auto_clock_out_hours', sa.Float(), nullable=True),
    sa.PrimaryKeyConstraint('shift_id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    
    op.drop_table('shifts')
    # ### end Alembic commands ###
    """
    pass
