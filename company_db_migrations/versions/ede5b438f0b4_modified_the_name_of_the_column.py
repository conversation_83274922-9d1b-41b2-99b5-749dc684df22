"""modified the name of the column

Revision ID: ede5b438f0b4
Revises: ad2f6e10dab1
Create Date: 2025-04-17 15:41:27.128737

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ede5b438f0b4'
down_revision: Union[str, None] = 'ad2f6e10dab1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.add_column('documents', sa.Column('file_label', sa.String(length=64), nullable=True))
    # op.drop_column('documents', 'file_type')
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.add_column('documents', sa.Column('file_type', sa.VARCHAR(length=64), autoincrement=False, nullable=False))
    # op.drop_column('documents', 'file_label')
    pass
    # ### end Alembic commands ###
