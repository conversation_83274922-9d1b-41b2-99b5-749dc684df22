"""removing certain tables for now

Revision ID: a16f4514efd1
Revises: cda709f85b84
Create Date: 2024-12-02 10:57:17.258548

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a16f4514efd1'
down_revision: Union[str, None] = 'cda709f85b84'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('repayment_logs')
    op.drop_table('salary_advance_approvals')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('salary_advance_approvals',
    sa.Column('approval_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('request_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('approver_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('approver_role', sa.VARCHAR(length=128), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('pending', 'approved', 'rejected', name='salary_approval_status_enum'), autoincrement=False, nullable=True),
    sa.Column('remarks', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['request_id'], ['salary_advance_requests.request_id'], name='salary_advance_approvals_request_id_fkey'),
    sa.PrimaryKeyConstraint('approval_id', name='salary_advance_approvals_pkey')
    )
    op.create_table('repayment_logs',
    sa.Column('repayment_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('employee_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('amount_paid', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=False),
    sa.Column('payment_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('balance_after_payment', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], name='repayment_logs_employee_id_fkey'),
    sa.PrimaryKeyConstraint('repayment_id', name='repayment_logs_pkey')
    )
    # ### end Alembic commands ###
    """
    pass
