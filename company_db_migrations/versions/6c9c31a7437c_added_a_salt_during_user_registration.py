"""added a salt during user registration

Revision ID: 6c9c31a7437c
Revises: 5985fb0fc4ca
Create Date: 2024-10-09 12:21:48.424564

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6c9c31a7437c'
down_revision: Union[str, None] = '5985fb0fc4ca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('salt', sa.String(length=128), nullable=False))
    # ### end Alembic commands ###
    """


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'salt')
    # ### end Alembic commands ###
    """
