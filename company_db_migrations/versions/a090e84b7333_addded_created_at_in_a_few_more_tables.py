"""addded created_at in a few more tables

Revision ID: a090e84b7333
Revises: 5acc7ddbd19c
Create Date: 2024-09-11 13:20:20.592315

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a090e84b7333'
down_revision: Union[str, None] = '5acc7ddbd19c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.add_column('deductions', sa.Column('created_at', sa.DateTime(), nullable=True))
    #op.add_column('users', sa.Column('created_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('users', 'created_at')
    #op.drop_column('deductions', 'created_at')
    # ### end Alembic commands ###
    pass
