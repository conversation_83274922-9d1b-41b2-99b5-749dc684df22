"""added the documents table

Revision ID: ad2f6e10dab1
Revises: 69a138581846
Create Date: 2025-04-17 15:32:50.162494

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ad2f6e10dab1'
down_revision: Union[str, None] = '69a138581846'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.create_table('documents',
    # sa.Column('document_id', sa.UUID(), nullable=False),
    # sa.Column('document_type', sa.String(length=64), nullable=False),
    # sa.Column('employee_id', sa.UUID(), nullable=True),
    # sa.Column('file_name', sa.String(length=255), nullable=False),
    # sa.Column('file_type', sa.String(length=64), nullable=False),
    # sa.Column('file_url', sa.String(length=512), nullable=False),
    # sa.Column('uploaded_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    # sa.Column('uploaded_by', sa.String(length=128), nullable=True),
    # sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    # sa.PrimaryKeyConstraint('document_id')
    # )
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.drop_table('documents')
    # ### end Alembic commands ###
    pass
