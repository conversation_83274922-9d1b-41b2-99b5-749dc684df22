"""added attendance_applicable column

Revision ID: cf2dd7f02101
Revises: 5b8776fd4444
Create Date: 2024-10-28 15:59:02.814454

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cf2dd7f02101'
down_revision: Union[str, None] = '5b8776fd4444'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('attendance_applicable', sa.String(length=16), nullable=True))
    # ### end Alembic commands ###
    """


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'attendance_applicable')
    # ### end Alembic commands ###
    """
