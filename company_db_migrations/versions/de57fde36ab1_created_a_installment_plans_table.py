"""created a installment_plans table

Revision ID: de57fde36ab1
Revises: b7008ad9a370
Create Date: 2024-12-09 16:27:42.620470

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'de57fde36ab1'
down_revision: Union[str, None] = 'b7008ad9a370'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """"
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('installment_plans',
    sa.Column('installment_id', sa.UUID(), nullable=False),
    sa.Column('request_id', sa.UUID(), nullable=False),
    sa.Column('installment_number', sa.Integer(), nullable=False),
    sa.Column('planned_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('is_paid', sa.<PERSON>(), nullable=True),
    sa.Column('paid_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['request_id'], ['salary_advance_requests.request_id'], ),
    sa.PrimaryKeyConstraint('installment_id')
    )
   
    # ### end Alembic commands ###
    """
    pass
def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    #op.drop_table('installment_plans')
    # ### end Alembic commands ###
    pass
