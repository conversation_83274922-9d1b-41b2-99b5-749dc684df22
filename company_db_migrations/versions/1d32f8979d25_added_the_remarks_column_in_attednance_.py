"""added the remarks column in attednance table

Revision ID: 1d32f8979d25
Revises: c5dabcbbe374
Create Date: 2024-10-18 12:51:58.516094

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1d32f8979d25'
down_revision: Union[str, None] = 'c5dabcbbe374'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('attendance', sa.Column('remarks', sa.String(length=128), nullable=True))
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('attendance', 'remarks')
    # ### end Alembic commands ###
    """
    pass
