"""added the contract end_date in the employees table

Revision ID: 1b18fc5a3815
Revises: 72c8d8c2be42
Create Date: 2025-06-16 13:22:16.802505

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1b18fc5a3815'
down_revision: Union[str, None] = '72c8d8c2be42'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('contract_end_date', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'contract_end_date')
    # ### end Alembic commands ###
