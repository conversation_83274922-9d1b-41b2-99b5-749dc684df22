"""removed the foregn key constraint from the approver_id from the approval_logs table

Revision ID: 3d84e68a2efe
Revises: 56060b321993
Create Date: 2024-11-27 14:10:42.032175

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3d84e68a2efe'
down_revision: Union[str, None] = '56060b321993'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('approval_logs_approver_id_fkey', 'approval_logs', type_='foreignkey')
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('approval_logs_approver_id_fkey', 'approval_logs', 'employees', ['approver_id'], ['employee_id'])
    # ### end Alembic commands ###
    """
    pass
