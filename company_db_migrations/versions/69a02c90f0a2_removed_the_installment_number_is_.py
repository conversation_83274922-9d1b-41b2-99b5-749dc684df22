"""removed the installment number is installment plans table

Revision ID: 69a02c90f0a2
Revises: 7588e44c3426
Create Date: 2024-12-17 12:47:54.555593

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '69a02c90f0a2'
down_revision: Union[str, None] = '7588e44c3426'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('installment_plans', 'installment_number')
   
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    #op.add_column('installment_plans', sa.Column('installment_number', sa.INTEGER(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
    pass