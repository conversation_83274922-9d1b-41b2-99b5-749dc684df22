"""adjusted the users table to contain the employee_id

Revision ID: 5985fb0fc4ca
Revises: d065fd8e545c
Create Date: 2024-10-09 11:23:12.534135

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5985fb0fc4ca'
down_revision: Union[str, None] = 'd065fd8e545c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('employee_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'users', 'employees', ['employee_id'], ['employee_id'])
    # ### end Alembic commands ###
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'employee_id')
    # ### end Alembic commands ###
    """
    pass
