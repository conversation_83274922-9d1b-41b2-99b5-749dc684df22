"""added columns in the employees table to track percentages

Revision ID: d8f19ccef676
Revises: 6055921720a0
Create Date: 2025-05-26 12:50:17.053398

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd8f19ccef676'
down_revision: Union[str, None] = '6055921720a0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('basic_salary', sa.Numeric(precision=18, scale=6), nullable=True))
    op.add_column('employees', sa.Column('salary_calculation_method', sa.String(length=20), nullable=True))
    op.add_column('employees', sa.Column('basic_salary_percentage', sa.Numeric(precision=5, scale=2), nullable=True))
    op.add_column('employees', sa.Column('transport_allowance_percentage', sa.Numeric(precision=5, scale=2), nullable=True))
    op.add_column('employees', sa.Column('housing_allowance_percentage', sa.Numeric(precision=5, scale=2), nullable=True))
    op.add_column('employees', sa.Column('communication_allowance_percentage', sa.Numeric(precision=5, scale=2), nullable=True))
    # ### end Alembic commands ###
    """
    pass
def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'communication_allowance_percentage')
    op.drop_column('employees', 'housing_allowance_percentage')
    op.drop_column('employees', 'transport_allowance_percentage')
    op.drop_column('employees', 'basic_salary_percentage')
    op.drop_column('employees', 'salary_calculation_method')
    op.drop_column('employees', 'basic_salary')
    # ### end Alembic commands ###
    """
    pass