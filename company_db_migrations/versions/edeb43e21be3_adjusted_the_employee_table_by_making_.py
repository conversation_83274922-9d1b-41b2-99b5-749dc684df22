"""adjusted the employee table by making some fields nullable

Revision ID: edeb43e21be3
Revises: 422ea09b5381
Create Date: 2024-10-31 14:04:31.414853

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'edeb43e21be3'
down_revision: Union[str, None] = '422ea09b5381'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('employees', 'birth_date',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('employees', 'marital_status',
               existing_type=sa.VARCHAR(length=16),
               nullable=True)
    op.alter_column('employees', 'phone',
               existing_type=sa.VARCHAR(length=20),
               nullable=True)
    op.alter_column('employees', 'job_title',
               existing_type=sa.VARCHAR(length=128),
               nullable=True)
    op.alter_column('employees', 'hire_date',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('employees', 'hire_date',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('employees', 'job_title',
               existing_type=sa.VARCHAR(length=128),
               nullable=False)
    op.alter_column('employees', 'phone',
               existing_type=sa.VARCHAR(length=20),
               nullable=False)
    op.alter_column('employees', 'marital_status',
               existing_type=sa.VARCHAR(length=16),
               nullable=False)
    op.alter_column('employees', 'birth_date',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    # ### end Alembic commands ###
