"""created leave_applications and leave_approvals tables

Revision ID: e68711eff962
Revises: 36710f09323f
Create Date: 2024-11-22 10:34:54.720847

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
import logging


# revision identifiers, used by Alembic.
revision: str = 'e68711eff962'
down_revision: Union[str, None] = '36710f09323f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

from sqlalchemy.sql import text
import logging

def table_exists(table_name: str) -> bool:
    """
    Check if a table exists in the current database using a safe query.

    Args:
        table_name (str): The name of the table to check.

    Returns:
        bool: True if the table exists, False otherwise.
    """
    try:
        query = text("SELECT to_regclass(:table_name)")
        bind = op.get_bind()
        result = bind.execute(query, {"table_name": table_name}).fetchone()
        logging.info(f"Checking if table {table_name} exists: {result}")
        return result and result[0] is not None
    except Exception as e:
        logging.error(f"Error checking if table {table_name} exists: {e}")
        return False



def upgrade() -> None:
    # Check and create `leave_applications` table if it doesn't exist
    if not table_exists('leave_applications'):
        op.create_table(
            'leave_applications',
            sa.Column('leave_id', sa.UUID(), nullable=False),
            sa.Column('employee_id', sa.UUID(), nullable=False),
            sa.Column('attendance_id', sa.UUID(), nullable=True),
            sa.Column('leave_type', sa.Enum('leave', 'off', name='leave_types_enum'), nullable=False),
            sa.Column('time_off_begin_date', sa.DateTime(), nullable=False),
            sa.Column('time_off_end_date', sa.DateTime(), nullable=False),
            sa.Column('reason', sa.Text(), nullable=True),
            sa.Column('status', sa.Enum('pending', 'approved', 'rejected', name='leaves_status_enum'), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['attendance_id'], ['attendance.attendance_id']),
            sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id']),
            sa.PrimaryKeyConstraint('leave_id')
        )

    # Check and create `leave_approvals` table if it doesn't exist
    if not table_exists('leave_approvals'):
        op.create_table(
            'leave_approvals',
            sa.Column('approval_id', sa.UUID(), nullable=False),
            sa.Column('leave_id', sa.UUID(), nullable=False),
            sa.Column('approver_id', sa.UUID(), nullable=False),
            sa.Column('approver_role', sa.Enum('supervisor', 'hr', name='approver_role_enum'), nullable=False),
            sa.Column('status', sa.Enum('pending', 'approved', 'rejected', name='approval_status_enum'), nullable=True),
            sa.Column('remarks', sa.Text(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['leave_id'], ['leave_applications.leave_id']),
            sa.PrimaryKeyConstraint('approval_id')
        )


def downgrade() -> None:
    # Drop `leave_approvals` table if it exists
    if table_exists('leave_approvals'):
        op.drop_table('leave_approvals')

    # Drop `leave_applications` table if it exists
    if table_exists('leave_applications'):
        op.drop_table('leave_applications')
