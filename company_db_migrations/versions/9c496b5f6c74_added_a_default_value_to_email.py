"""added a default value to email

Revision ID: 9c496b5f6c74
Revises: dbdabf64c878
Create Date: 2024-11-13 11:57:51.763054

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9c496b5f6c74'
down_revision: Union[str, None] = 'dbdabf64c878'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """"
    op.alter_column('employees', 'email',
                    existing_type=sa.String(length=128),
                    nullable=True,
                    server_default=None) 
    """
    pass

def downgrade():
    """
    op.alter_column('employees', 'email',
                    existing_type=sa.String(length=128),
                    nullable=True,
                    server_default=sa.text("''")) 
    """
    pass