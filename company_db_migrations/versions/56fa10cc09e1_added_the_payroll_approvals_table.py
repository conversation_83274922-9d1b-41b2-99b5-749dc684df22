"""added the payroll_approvals table

Revision ID: 56fa10cc09e1
Revises: a488f82e3225
Create Date: 2024-12-27 14:05:26.580508

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '56fa10cc09e1'
down_revision: Union[str, None] = 'a488f82e3225'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('payroll_approvals',
    sa.Column('approval_id', sa.UUID(), nullable=False),
    sa.Column('payroll_id', sa.UUID(), nullable=False),
    sa.Column('approver_id', sa.UUID(), nullable=False),
    sa.Column('approver_role', sa.String(length=128), nullable=False),
    sa.Column('status', sa.String(length=128), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['payroll_id'], ['payrolls.payroll_id'], ),
    sa.PrimaryKeyConstraint('approval_id')
    ) 
    """  
    #op.add_column('payrolls', sa.Column('status', sa.String(length=128), nullable=True))
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('payrolls', 'status')   
    #op.drop_table('payroll_approvals')
    # ### end Alembic commands ###
    pass
