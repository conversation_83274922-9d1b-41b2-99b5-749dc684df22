"""added annual_leave on attendance

Revision ID: 6055921720a0
Revises: af074bb6e683
Create Date: 2025-05-13 12:09:20.076353

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6055921720a0'
down_revision: Union[str, None] = 'af074bb6e683'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # Add the new value 'annual_leave' to the Enum type 'work_status_enums'
    op.execute("ALTER TYPE work_status_enum ADD VALUE 'annual_leave'")
    """
    pass

def downgrade() -> None:
    """
    # Create a new Enum type without 'annual_leave'
    op.execute("CREATE TYPE work_status_enums AS ENUM('leave', 'off')")

    # Update the column to use the new Enum type
    op.execute("""
        ##ALTER TABLE leave_application
        #ALTER COLUMN leave_type TYPE work_status_enums
        #USING leave_type::TEXT::work_status_enums
    """)

    # Drop the old Enum type
    op.execute("DROP TYPE work_status_enum")

    # Rename the new Enum type to the original name
    op.execute("ALTER TYPE work_status_enums RENAME TO work_status_enum")
    # ### end Alembic commands ###
    # ### end Alembic commands ###
    """
    pass
