"""Placeholder for missing revision

Revision ID: 68c387fb2962
Revises: e1c836dcc559
Create Date: 2024-10-01 11:19:35.720694

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '68c387fb2962'
down_revision: Union[str, None] = 'e1c836dcc559'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
