"""added the field_clockin location and clockout location, the name in the attendance table

Revision ID: ec36f43cc5af
Revises: 1eaaf7ac9b48
Create Date: 2025-03-27 13:34:31.111096

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ec36f43cc5af'
down_revision: Union[str, None] = '1eaaf7ac9b48'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

from alembic import op
import sqlalchemy as sa


def upgrade() -> None:
    # ### Add new columns ###
    """
    op.add_column('attendance', sa.Column('field_in_location', sa.String(length=128), nullable=True))
    op.add_column('attendance', sa.Column('field_in_location_name', sa.String(length=128), nullable=True))
    op.add_column('attendance', sa.Column('field_out_location', sa.String(length=128), nullable=True))
    op.add_column('attendance', sa.Column('field_out_location_name', sa.String(length=128), nullable=True))

    # ### Change field types from VARCHAR to TIMESTAMP ###
    op.execute(""""""
        ALTER TABLE attendance 
        ALTER COLUMN field_in 
        TYPE TIMESTAMP 
        USING field_in::timestamp
    )
    op.execute(
        ALTER TABLE attendance 
        ALTER COLUMN field_out 
        TYPE TIMESTAMP 
        USING field_out::timestamp
    )
    """
    pass


def downgrade() -> None:
    # ### Revert field types from TIMESTAMP to VARCHAR ###
    """
    op.execute(""""""
        ALTER TABLE attendance 
        ALTER COLUMN field_out 
        TYPE VARCHAR(128) 
        USING field_out::text
    )
    op.execute(
        ALTER TABLE attendance 
        ALTER COLUMN field_in 
        TYPE VARCHAR(128) 
        USING field_in::text
    )

    # ### Drop newly added columns ###
    op.drop_column('attendance', 'field_out_location_name')
    op.drop_column('attendance', 'field_out_location')
    op.drop_column('attendance', 'field_in_location_name')
    op.drop_column('attendance', 'field_in_location')
    """
    pass
