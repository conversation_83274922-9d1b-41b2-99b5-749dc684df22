"""added created_at column in departments and deductions'

Revision ID: 5acc7ddbd19c
Revises: 
Create Date: 2024-09-11 12:57:22.867297

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5acc7ddbd19c'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.alter_column('departments', 'created_at',
     #          existing_type=postgresql.TIMESTAMP(),
      #         nullable=True)
    # ### end Alembic commands ###
    pass

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.alter_column('departments', 'created_at',
     #          existing_type=postgresql.TIMESTAMP(),
      #         nullable=False)
    # ### end Alembic commands ###
    pass
