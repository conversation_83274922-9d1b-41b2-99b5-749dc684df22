"""extended Employee table with etra_leave_days

Revision ID: 18a363f1c12f
Revises: 4fdea247b035
Create Date: 2025-05-05 11:15:04.095887

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '18a363f1c12f'
down_revision: Union[str, None] = '4fdea247b035'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('employees', sa.Column('extra_leave_days', sa.Integer(), nullable=True))
    # ### end Alembic commands ###
    """
    pass

def downgrade() -> None:
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'extra_leave_days')
    # ### end Alembic commands ###
    """
    pass
