"""added the quickbooks_audit_logs table

Revision ID: 69a138581846
Revises: ab89f304060a
Create Date: 2025-04-11 08:59:00.273472

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '69a138581846'
down_revision: Union[str, None] = 'ab89f304060a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.create_table('quickbooks_audit_logs',
    # sa.Column('id', sa.UUID(), nullable=False),
    # sa.Column('action_type', sa.String(length=128), nullable=False),
    # sa.Column('timestamp', sa.DateTime(), nullable=True),
    # sa.Column('operation_status', sa.Enum('Success', 'Failure', name='operation_status'), nullable=False),
    # sa.Column('request_payload', sa.JSON(), nullable=True),
    # sa.Column('response_payload', sa.JSON(), nullable=True),
    # sa.Column('error_message', sa.String(length=512), nullable=True),
    # sa.Column('user_id', sa.UUID(), nullable=True),
    # sa.PrimaryKeyConstraint('id')
    # )
    # op.alter_column('deductions', 'deduction_amount',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('employees', 'net_salary',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'gross_salary',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'total_staff_cost',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'transport_allowance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'housing_allowance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'communication_allowance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'over_time',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'other_allowance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'salary_advance_balance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('insurances', 'employee_rate',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('insurances', 'employer_rate',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'basic_salary',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'transport_allowance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'housing_allowance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'bonus',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'medical_fee',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'gross_salary',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_pension',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_pension',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_maternity',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_maternity',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'payee',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'cbhi',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'total_deductions',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'net_pay',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'net_salary',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'reimbursement',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'advance',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'brd_deductions',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'other_deductions',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=True)
    # op.alter_column('reimbursements', 'reimbursement_amount',
    #            existing_type=sa.NUMERIC(precision=12, scale=2),
    #            type_=sa.Numeric(precision=12, scale=6),
    #            existing_nullable=False)
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('reimbursements', 'reimbursement_amount',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'other_deductions',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'brd_deductions',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'advance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'reimbursement',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'net_salary',
    #            existing_type=sa.Numeric(precision=18, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'net_pay',
    #            existing_type=sa.Numeric(precision=18, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'total_deductions',
    #            existing_type=sa.Numeric(precision=18, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'cbhi',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'payee',
    #            existing_type=sa.Numeric(precision=18, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_maternity',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_maternity',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employee_pension',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'employer_pension',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'gross_salary',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=18, scale=6),
    #            existing_nullable=False)
    # op.alter_column('payrolls', 'medical_fee',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'bonus',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'housing_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'transport_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('payrolls', 'basic_salary',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=18, scale=6),
    #            existing_nullable=False)
    # op.alter_column('insurances', 'employer_rate',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('insurances', 'employee_rate',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.alter_column('employees', 'salary_advance_balance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'other_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'over_time',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'communication_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'housing_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'transport_allowance',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('employees', 'total_staff_cost',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'gross_salary',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=18, scale=6),
    #            existing_nullable=True)
    # op.alter_column('employees', 'net_salary',
    #            existing_type=sa.Numeric(precision=18, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=True)
    # op.alter_column('deductions', 'deduction_amount',
    #            existing_type=sa.Numeric(precision=12, scale=6),
    #            type_=sa.NUMERIC(precision=12, scale=2),
    #            existing_nullable=False)
    # op.drop_table('quickbooks_audit_logs')
    pass
    # ### end Alembic commands ###
