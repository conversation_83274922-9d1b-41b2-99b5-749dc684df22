"""changed the data type from float to Decimal for accurrancy in the latitude and longitude

Revision ID: 1eaaf7ac9b48
Revises: fc56dd443571
Create Date: 2025-03-24 11:53:34.766051

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1eaaf7ac9b48'
down_revision: Union[str, None] = 'fc56dd443571'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.alter_column('sites', 'longitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=9, scale=6),
               existing_nullable=True)
    op.alter_column('sites', 'latitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=9, scale=6),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sites', 'latitude',
               existing_type=sa.Numeric(precision=9, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=True)
    op.alter_column('sites', 'longitude',
               existing_type=sa.Numeric(precision=9, scale=6),
               type_=sa.DOUBLE_PRECISION(precision=53),
                existing_nullable=True)
    # ### end Alembic commands ###
