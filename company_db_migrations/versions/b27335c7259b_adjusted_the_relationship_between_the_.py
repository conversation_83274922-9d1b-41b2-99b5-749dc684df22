"""adjusted the relationship between the employee, attendance and department'

Revision ID: b27335c7259b
Revises: 126eb4afb605
Create Date: 2024-10-01 16:07:32.731827

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b27335c7259b'
down_revision: Union[str, None] = '126eb4afb605'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
