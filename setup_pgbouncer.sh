#!/bin/bash
# Script to install and configure PgBouncer for connection pooling
# Optimized for multi-tenant architecture where each company has its own database

# Exit on error
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}PgBouncer Setup Script for Multi-Tenant Architecture${NC}"
echo "This script will install and configure PgBouncer for PostgreSQL connection pooling."
echo "Optimized for systems where each company has its own database."
echo

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}Please run as root or with sudo${NC}"
  exit 1
fi

# Function to read environment variables from .env file
read_env_file() {
  if [ -f .env ]; then
    echo -e "${BLUE}Reading configuration from .env file...${NC}"
    # Export all variables from .env file
    export $(grep -v '^#' .env | xargs)
  else
    echo -e "${YELLOW}No .env file found. Will prompt for values.${NC}"
  fi
}

# Read environment variables
read_env_file

# Install PgBouncer
echo -e "${YELLOW}Installing PgBouncer...${NC}"
apt-get update
apt-get install -y pgbouncer postgresql-client

# Backup original config
echo -e "${YELLOW}Backing up original configuration...${NC}"
cp /etc/pgbouncer/pgbouncer.ini /etc/pgbouncer/pgbouncer.ini.bak

# Get PostgreSQL connection details from environment or prompt
echo -e "${YELLOW}Configuring PostgreSQL connection details...${NC}"

# Use environment variables if available, otherwise prompt
PG_HOST=${DB_HOST:-localhost}
read -p "PostgreSQL Host [$PG_HOST]: " input_host
PG_HOST=${input_host:-$PG_HOST}

PG_PORT=${DB_PORT:-5432}
read -p "PostgreSQL Port [$PG_PORT]: " input_port
PG_PORT=${input_port:-$PG_PORT}

PG_USER=${DB_USER:-postgres}
read -p "PostgreSQL User [$PG_USER]: " input_user
PG_USER=${input_user:-$PG_USER}

# Only prompt for password if not in environment
if [ -z "$DB_PASSWORD" ]; then
  read -s -p "PostgreSQL Password: " PG_PASSWORD
  echo
else
  PG_PASSWORD=$DB_PASSWORD
  echo "Using password from environment variable"
fi

BOUNCER_PORT=${PGBOUNCER_PORT:-6432}
read -p "PgBouncer Listen Port [$BOUNCER_PORT]: " input_bouncer_port
BOUNCER_PORT=${input_bouncer_port:-$BOUNCER_PORT}

# Get central database name
CENTRAL_DB=${DB_NAME:-netpipo}
read -p "Central Database Name [$CENTRAL_DB]: " input_central_db
CENTRAL_DB=${input_central_db:-$CENTRAL_DB}

# Get connection pool settings
echo -e "${YELLOW}Configuring connection pool settings...${NC}"

# Default pool size (connections per database)
DEFAULT_POOL_SIZE=${PGBOUNCER_DEFAULT_POOL_SIZE:-5}
read -p "Default Pool Size (connections per database) [$DEFAULT_POOL_SIZE]: " input_pool_size
DEFAULT_POOL_SIZE=${input_pool_size:-$DEFAULT_POOL_SIZE}

# Central database pool size (typically needs more connections)
CENTRAL_POOL_SIZE=${PGBOUNCER_CENTRAL_POOL_SIZE:-10}
read -p "Central Database Pool Size [$CENTRAL_POOL_SIZE]: " input_central_pool_size
CENTRAL_POOL_SIZE=${input_central_pool_size:-$CENTRAL_POOL_SIZE}

# Max client connections
MAX_CLIENT_CONN=${PGBOUNCER_MAX_CLIENT_CONN:-1000}
read -p "Maximum Client Connections [$MAX_CLIENT_CONN]: " input_max_client
MAX_CLIENT_CONN=${input_max_client:-$MAX_CLIENT_CONN}

# Try to discover company databases
echo -e "${YELLOW}Attempting to discover company databases...${NC}"
COMPANY_DBS=""

if [ -n "$PG_PASSWORD" ]; then
  export PGPASSWORD="$PG_PASSWORD"
  DB_LIST=$(psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -t -c "SELECT datname FROM pg_database WHERE datname NOT IN ('postgres', 'template0', 'template1') AND datname != '$CENTRAL_DB';")

  # Filter for company databases (those ending with _db based on your naming convention)
  for db in $DB_LIST; do
    if [[ "$db" == *"_db" ]]; then
      COMPANY_DBS="$COMPANY_DBS $db"
    fi
  done

  if [ -n "$COMPANY_DBS" ]; then
    echo -e "${GREEN}Discovered company databases: ${COMPANY_DBS}${NC}"
  else
    echo -e "${YELLOW}No company databases discovered. Will use wildcard configuration.${NC}"
  fi
else
  echo -e "${YELLOW}No password provided. Skipping database discovery.${NC}"
fi

# Create pgbouncer.ini
echo -e "${YELLOW}Creating PgBouncer configuration...${NC}"

# Create a simpler configuration first to ensure it works
cat > /etc/pgbouncer/pgbouncer.ini << EOF
[databases]
# Use a wildcard for all databases initially
* = host=$PG_HOST port=$PG_PORT

[pgbouncer]
listen_addr = 127.0.0.1
listen_port = $BOUNCER_PORT
auth_type = scram-sha-256
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = transaction
max_client_conn = $MAX_CLIENT_CONN
default_pool_size = $DEFAULT_POOL_SIZE
min_pool_size = 0
reserve_pool_size = 5
reserve_pool_timeout = 3
max_db_connections = 50
max_user_connections = 50
server_reset_query = DISCARD ALL
server_check_delay = 30
server_check_query = SELECT 1
server_lifetime = 3600
server_idle_timeout = 600
log_connections = 1
log_disconnections = 1
application_name_add_host = 1

# Logging settings
log_stats = 1
stats_period = 60

# Admin console settings
admin_users = $PG_USER
EOF

# Create userlist.txt
echo -e "${YELLOW}Creating user authentication file...${NC}"

# Get the correct password format for PostgreSQL 15+ (SCRAM-SHA-256)
if [ -n "$PG_PASSWORD" ] && [ -n "$PG_USER" ]; then
  # Export password for psql
  export PGPASSWORD="$PG_PASSWORD"

  # Try to get the SCRAM-SHA-256 password hash directly from PostgreSQL
  echo -e "${YELLOW}Getting SCRAM-SHA-256 password hash from PostgreSQL...${NC}"
  PG_HASH=$(psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -t -c "SELECT rolpassword FROM pg_authid WHERE rolname='$PG_USER'")

  if [ -z "$PG_HASH" ] || ! [[ "$PG_HASH" == SCRAM-SHA-256* ]]; then
    echo -e "${YELLOW}Could not get SCRAM-SHA-256 hash, falling back to plain password...${NC}"
    # If we can't get the hash, use plain password (less secure but will work)
    cat > /etc/pgbouncer/userlist.txt << EOF
"${PG_USER}" "${PG_PASSWORD}"
EOF
  else
    # Create the userlist.txt file with SCRAM-SHA-256 hash
    echo -e "${GREEN}Using SCRAM-SHA-256 authentication...${NC}"
    cat > /etc/pgbouncer/userlist.txt << EOF
"${PG_USER}" "${PG_HASH}"
EOF
  fi

  # Also add postgres user if different from PG_USER
  if [ "$PG_USER" != "postgres" ]; then
    # Try to get postgres password hash
    PG_ADMIN_HASH=$(psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -t -c "SELECT rolpassword FROM pg_authid WHERE rolname='postgres'" 2>/dev/null)

    if [ -n "$PG_ADMIN_HASH" ] && [[ "$PG_ADMIN_HASH" == SCRAM-SHA-256* ]]; then
      echo "\"postgres\" \"${PG_ADMIN_HASH}\"" >> /etc/pgbouncer/userlist.txt
    else
      # If we can't get postgres hash, use the same as the user (may not work)
      echo "\"postgres\" \"${PG_PASSWORD}\"" >> /etc/pgbouncer/userlist.txt
    fi
  fi
else
  echo -e "${RED}Error: Username or password is empty. Cannot create userlist.txt${NC}"
  exit 1
fi

# Set permissions
echo -e "${YELLOW}Setting permissions...${NC}"
chown postgres:postgres /etc/pgbouncer/pgbouncer.ini
chown postgres:postgres /etc/pgbouncer/userlist.txt
chmod 640 /etc/pgbouncer/pgbouncer.ini
chmod 640 /etc/pgbouncer/userlist.txt

# Restart PgBouncer with debugging
echo -e "${YELLOW}Restarting PgBouncer...${NC}"

# Enable PgBouncer at startup
systemctl enable pgbouncer

# Try to start PgBouncer
if ! systemctl restart pgbouncer; then
  echo -e "${RED}PgBouncer failed to start. Checking logs...${NC}"

  # Show PgBouncer status
  echo -e "${YELLOW}PgBouncer Status:${NC}"
  systemctl status pgbouncer

  # Show PgBouncer logs
  echo -e "${YELLOW}PgBouncer Logs:${NC}"
  journalctl -xeu pgbouncer.service --no-pager | tail -n 20

  # Check configuration
  echo -e "${YELLOW}Checking configuration file:${NC}"
  cat /etc/pgbouncer/pgbouncer.ini

  # Check userlist file
  echo -e "${YELLOW}Checking userlist file:${NC}"
  cat /etc/pgbouncer/userlist.txt

  # Try to fix common issues
  echo -e "${YELLOW}Attempting to fix common issues...${NC}"

  # Fix permissions
  chown -R postgres:postgres /etc/pgbouncer
  chmod 755 /etc/pgbouncer
  chmod 640 /etc/pgbouncer/pgbouncer.ini
  chmod 640 /etc/pgbouncer/userlist.txt

  # Check for configuration issues
  if grep -q "#" /etc/pgbouncer/pgbouncer.ini; then
    echo -e "${YELLOW}Found comments in configuration file. Removing them...${NC}"
    sed -i 's/\s*#.*$//' /etc/pgbouncer/pgbouncer.ini
  fi

  # Fix userlist.txt format if needed
  echo -e "${YELLOW}Recreating userlist.txt with correct format...${NC}"
  if [ -n "$PG_PASSWORD" ] && [ -n "$PG_USER" ]; then
    # Export password for psql
    export PGPASSWORD="$PG_PASSWORD"

    # Try to get the SCRAM-SHA-256 password hash directly from PostgreSQL
    echo -e "${YELLOW}Getting SCRAM-SHA-256 password hash from PostgreSQL...${NC}"
    PG_HASH=$(psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -t -c "SELECT rolpassword FROM pg_authid WHERE rolname='$PG_USER'")

    if [ -z "$PG_HASH" ] || ! [[ "$PG_HASH" == SCRAM-SHA-256* ]]; then
      echo -e "${YELLOW}Could not get SCRAM-SHA-256 hash, trying plain password...${NC}"
      # Try plain password authentication
      echo "\"$PG_USER\" \"$PG_PASSWORD\"" > /etc/pgbouncer/userlist.txt

      # Update pgbouncer.ini to use plain auth
      sed -i 's/auth_type = scram-sha-256/auth_type = plain/g' /etc/pgbouncer/pgbouncer.ini
    else
      # Create userlist.txt with SCRAM-SHA-256 hash
      echo -e "${GREEN}Using SCRAM-SHA-256 authentication...${NC}"
      echo "\"$PG_USER\" \"$PG_HASH\"" > /etc/pgbouncer/userlist.txt
    fi

    # Also add postgres user if different
    if [ "$PG_USER" != "postgres" ]; then
      # Try to get postgres password hash
      PG_ADMIN_HASH=$(psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -t -c "SELECT rolpassword FROM pg_authid WHERE rolname='postgres'" 2>/dev/null)

      if [ -n "$PG_ADMIN_HASH" ] && [[ "$PG_ADMIN_HASH" == SCRAM-SHA-256* ]]; then
        echo "\"postgres\" \"${PG_ADMIN_HASH}\"" >> /etc/pgbouncer/userlist.txt
      else
        # If we can't get postgres hash, use the same as the user
        echo "\"postgres\" \"$PG_PASSWORD\"" >> /etc/pgbouncer/userlist.txt
      fi
    fi

    # Fix permissions
    chown postgres:postgres /etc/pgbouncer/userlist.txt
    chmod 640 /etc/pgbouncer/userlist.txt
  fi

  # Try starting again
  echo -e "${YELLOW}Trying to start PgBouncer again...${NC}"
  systemctl restart pgbouncer
fi

# Check if PgBouncer is running now
if systemctl is-active --quiet pgbouncer; then
  echo -e "${GREEN}PgBouncer is running!${NC}"
  echo -e "Connection string for local connections: ****************************************************************"
  echo -e "Connection string for remote connections: *************************************** -I | awk '{print $1}'):$BOUNCER_PORT/$CENTRAL_DB"
  echo
  echo -e "${YELLOW}Next steps:${NC}"
  echo "1. Update your application's .env file to use PgBouncer:"
  echo "   DB_HOST=$(hostname -I | awk '{print $1}')  # IP address of this server"
  echo "   DB_PORT=$BOUNCER_PORT"
  echo
  echo "2. Test the connection to central database:"
  echo "   psql -h localhost -p $BOUNCER_PORT -U $PG_USER -d $CENTRAL_DB"
  echo
  echo "3. Test the connection to a company database:"
  if [ -n "$COMPANY_DBS" ]; then
    SAMPLE_DB=$(echo $COMPANY_DBS | awk '{print $1}')
    echo "   psql -h localhost -p $BOUNCER_PORT -U $PG_USER -d $SAMPLE_DB"
  else
    echo "   psql -h localhost -p $BOUNCER_PORT -U $PG_USER -d your_company_database"
  fi
  echo
  echo "4. Monitor PgBouncer:"
  echo "   sudo tail -f /var/log/postgresql/pgbouncer.log"
  echo
  echo "5. Check PgBouncer stats:"
  echo "   psql -h localhost -p $BOUNCER_PORT -U $PG_USER pgbouncer -c 'SHOW POOLS;'"
else
  echo -e "${RED}PgBouncer failed to start. Check logs with:${NC}"
  echo "sudo journalctl -u pgbouncer"
fi

echo
echo -e "${GREEN}Setup complete!${NC}"
echo -e "${YELLOW}Note: You may need to adjust pool settings based on your application's usage patterns.${NC}"

# Create an advanced configuration file for later use
if [ -n "$COMPANY_DBS" ]; then
  echo -e "${YELLOW}Creating advanced configuration file for future use...${NC}"

  # Create the advanced configuration file
  cat > /etc/pgbouncer/pgbouncer.advanced.ini << EOF
[databases]
# Central database with specific pool size
$CENTRAL_DB = host=$PG_HOST port=$PG_PORT pool_size=$CENTRAL_POOL_SIZE

# Company databases with their own pool sizes
EOF

  # Add discovered company databases
  for db in $COMPANY_DBS; do
    echo "$db = host=$PG_HOST port=$PG_PORT pool_size=$DEFAULT_POOL_SIZE" >> /etc/pgbouncer/pgbouncer.advanced.ini
  done

  # Add wildcard for any other databases
  echo "* = host=$PG_HOST port=$PG_PORT pool_size=3" >> /etc/pgbouncer/pgbouncer.advanced.ini

  # Add the pgbouncer section
  cat >> /etc/pgbouncer/pgbouncer.advanced.ini << EOF

[pgbouncer]
listen_addr = *
listen_port = $BOUNCER_PORT
auth_type = scram-sha-256
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = transaction
max_client_conn = $MAX_CLIENT_CONN
default_pool_size = $DEFAULT_POOL_SIZE
min_pool_size = 0
reserve_pool_size = 5
reserve_pool_timeout = 3
max_db_connections = 50
max_user_connections = 50
server_reset_query = DISCARD ALL
server_check_delay = 30
server_check_query = SELECT 1
server_lifetime = 3600
server_idle_timeout = 600
log_connections = 1
log_disconnections = 1
application_name_add_host = 1
ignore_startup_parameters = extra_float_digits

# Logging settings
log_stats = 1
stats_period = 60

# Admin console settings
admin_users = $PG_USER
EOF

  echo -e "${YELLOW}Once basic configuration is working, you can upgrade to the advanced configuration with:${NC}"
  echo "  sudo cp /etc/pgbouncer/pgbouncer.advanced.ini /etc/pgbouncer/pgbouncer.ini"
  echo "  sudo systemctl restart pgbouncer"
fi
