from app import app
from app.models.central import RoutePlanRequirement, db
import uuid

# This script fixes the route permissions for the send_payslip_email route

with app.app_context():
    # Get your plan ID - you'll need to replace this with the actual plan ID
    # This is the enterprise plus plan ID based on the logs
    plan_id = None
    
    # Find the plan ID by name
    from app.models.central import Plans
    plan = db.session.query(Plans).filter_by(plan_name='enterprise plus').first()
    if plan:
        plan_id = plan.plan_id
        print(f"Found plan ID: {plan_id}")
    else:
        print("Plan 'enterprise plus' not found")
        exit(1)
    
    # Check if the route with space exists
    space_route = db.session.query(RoutePlanRequirement).filter_by(
        route_path=' /send_payslip_email',
        required_plan_id=plan_id
    ).first()
    
    if space_route:
        print(f"Found route with space: {space_route.route_path}")
        # Delete the route with space
        db.session.delete(space_route)
        db.session.commit()
        print("Deleted route with space")
    
    # Check if the correct route already exists
    correct_route = db.session.query(RoutePlanRequirement).filter_by(
        route_path='/send_payslip_email',
        required_plan_id=plan_id
    ).first()
    
    if not correct_route:
        # Add the correct route
        new_route = RoutePlanRequirement(
            id=uuid.uuid4(),
            route_path='/send_payslip_email',
            required_plan_id=plan_id,
            description='Send payslip as email attachment'
        )
        db.session.add(new_route)
        db.session.commit()
        print("Added correct route without space")
    else:
        print("Correct route already exists")
    
    # Add the route with the UUID parameter
    uuid_route = db.session.query(RoutePlanRequirement).filter_by(
        route_path='/send_payslip_email/<uuid:employee_id>',
        required_plan_id=plan_id
    ).first()
    
    if not uuid_route:
        # Add the route with UUID parameter
        new_uuid_route = RoutePlanRequirement(
            id=uuid.uuid4(),
            route_path='/send_payslip_email/<uuid:employee_id>',
            required_plan_id=plan_id,
            description='Send payslip as email attachment to specific employee'
        )
        db.session.add(new_uuid_route)
        db.session.commit()
        print("Added route with UUID parameter")
    else:
        print("Route with UUID parameter already exists")
    
    print("Route permissions fixed successfully!")
