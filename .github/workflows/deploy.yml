name: Deploy to App Servers

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    if: contains(github.event.head_commit.message, '[deploy]')
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to Server 1
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.APP1 }}
          username: ${{ secrets.ACR }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          timeout: 60s
          command_timeout: 5m
          script: |
            LOG_FILE="/home/<USER>/automations/github.txt"
            {
              echo "===== 🚀 Deploy to Server 1 - $(date) ====="
              cd /home/<USER>/acr_hrms || exit 1
              whoami
              pwd
              git fetch origin
              git reset --hard origin/main
              git clean -fd
              git status
              sudo service netpipo restart
              sudo service netpipo status
              echo "✅ Deployment finished for Server 1"
              echo
            } >> "$LOG_FILE" 2>&1

      - name: Deploy to Server 2
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.APP2 }}
          username: ${{ secrets.ACR }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          timeout: 60s
          command_timeout: 5m
          script: |
            LOG_FILE="/home/<USER>/automations/github.txt"
            {
              echo "===== 🚀 Deploy to Server 2 - $(date) ====="
              cd /home/<USER>/acr_hrms || exit 1
              whoami
              pwd
              git fetch origin
              git reset --hard origin/main
              git clean -fd
              git status
              sudo service netpipo restart
              sudo service netpipo status
              echo "✅ Deployment finished for Server 2"
              echo
            } >> "$LOG_FILE" 2>&1
