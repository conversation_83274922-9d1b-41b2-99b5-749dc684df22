import os
from test1 import FaceRecognitionService
from test2 import WebcamCapture

def main():
    known_faces_dir = "/home/<USER>/acr_hrms/app/data/known_faces"
    unknown_faces_dir = "/home/<USER>/acr_hrms/app/data/unknown_faces"

    # Create the face recognition service
    face_recognition_service = FaceRecognitionService(known_faces_dir)

    # Create the webcam capture service
    webcam_capture = WebcamCapture(unknown_faces_dir)

    # Open the webcam and capture an image
    captured_image_path = webcam_capture.open_and_capture()

    # If an image was captured, attempt to recognize the face
    if captured_image_path:
        result = face_recognition_service.recognize_faces(captured_image_path)
        print(result)

        # Optionally, keep or delete the captured image
        # os.remove(captured_image_path)

if __name__ == "__main__":
    main()
