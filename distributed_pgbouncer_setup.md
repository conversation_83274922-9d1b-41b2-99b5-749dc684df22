# Distributed PgBouncer Setup Guide

This guide provides specific instructions for setting up PgBouncer in a distributed architecture where:
1. PostgreSQL and PgBouncer run on the same server
2. The Flask application connects from a different server

## Architecture Overview

```
┌─────────────────────┐           ┌─────────────────────┐
│                     │           │                     │
│  Application Server │           │  Database Server    │
│  ---------------    │           │  ---------------    │
│                     │           │                     │
│  - Flask App        │◄─────────►│  - PgBouncer        │
│  - SQLAlchemy       │    TCP    │  - PostgreSQL       │
│                     │           │                     │
└─────────────────────┘           └─────────────────────┘
```

## Installation Steps

### On the Database Server

1. **Install PgBouncer**:
   ```bash
   sudo ./setup_pgbouncer_improved.sh
   ```

2. **Configure Firewall**:
   ```bash
   # Allow connections from your application server only (more secure)
   sudo ufw allow from your_flask_server_ip to any port 6432
   
   # OR allow connections from anywhere (less secure)
   sudo ufw allow 6432/tcp
   ```

3. **Verify PgBouncer is Listening on All Interfaces**:
   ```bash
   sudo netstat -tuln | grep 6432
   ```
   
   You should see something like:
   ```
   tcp        0      0 0.0.0.0:6432            0.0.0.0:*               LISTEN
   ```

4. **Test Local Connection**:
   ```bash
   psql -h localhost -p 6432 -U your_db_user -d your_central_db
   ```

### On the Application Server

1. **Update Environment Variables**:
   Edit your `.env` file to point to the database server:
   ```
   DB_HOST=your_db_server_ip  # IP address of the database server
   DB_PORT=6432               # PgBouncer port
   ```

2. **Test Remote Connection**:
   ```bash
   psql -h your_db_server_ip -p 6432 -U your_db_user -d your_central_db
   ```

3. **Update Connection Limiter** (if needed):
   Since your application will now connect to PgBouncer instead of directly to PostgreSQL, you may want to adjust your connection limiter settings in `app/utils/connection_limiter.py`:
   ```python
   # You can increase this value since PgBouncer will manage the actual PostgreSQL connections
   MAX_CONCURRENT_CONNECTIONS = int(os.getenv('MAX_DB_CONNECTIONS', '100'))
   ```

## Network Considerations

1. **Latency**:
   - There will be a small additional network latency between your application and database
   - This is typically negligible within the same data center or cloud region
   - PgBouncer's connection pooling benefits usually outweigh this small latency increase

2. **Security**:
   - Ensure the connection between servers is secure
   - Consider using a private network if available
   - If using a public network, consider setting up SSL for PgBouncer

3. **Reliability**:
   - Network issues between servers could affect database connectivity
   - Implement proper error handling and retry logic in your application
   - Consider monitoring network connectivity between servers

## PgBouncer Configuration for Distributed Setup

The improved setup script already configures PgBouncer for a distributed setup, but here are the key settings:

```ini
[pgbouncer]
listen_addr = *  # Listen on all interfaces, not just localhost
listen_port = 6432
```

## Monitoring in a Distributed Setup

1. **From the Database Server**:
   ```bash
   ./monitor_pgbouncer.sh
   ```

2. **From the Application Server**:
   ```bash
   ./monitor_pgbouncer.sh -h your_db_server_ip
   ```

3. **Network Monitoring**:
   Consider monitoring network connectivity between your application and database servers:
   ```bash
   ping -c 3 your_db_server_ip
   ```

## Troubleshooting Connection Issues

1. **Check Firewall Rules**:
   ```bash
   sudo ufw status
   ```

2. **Verify PgBouncer is Running**:
   ```bash
   sudo systemctl status pgbouncer
   ```

3. **Check PgBouncer Logs**:
   ```bash
   sudo tail -f /var/log/postgresql/pgbouncer.log
   ```

4. **Test Network Connectivity**:
   ```bash
   telnet your_db_server_ip 6432
   ```

5. **Check PostgreSQL Authentication**:
   Ensure PostgreSQL's `pg_hba.conf` allows connections from PgBouncer.

## Performance Tuning for Distributed Setup

1. **Connection Pooling Settings**:
   - Increase `default_pool_size` slightly to account for network latency
   - Consider increasing `reserve_pool_size` for handling connection spikes

2. **Timeout Settings**:
   - Increase `server_connect_timeout` if network latency is higher
   - Adjust `server_login_retry` for more resilient connections

3. **SQLAlchemy Settings**:
   - Consider increasing `pool_timeout` slightly
   - Keep `pool_pre_ping=True` to validate connections

## Best Practices for Distributed PgBouncer

1. **Regular Monitoring**:
   - Monitor both PgBouncer and network connectivity
   - Set up alerts for connection issues

2. **Connection String Updates**:
   - Update ALL connection strings to use PgBouncer
   - This includes application code, migration scripts, and backup tools

3. **Backup and Restore**:
   - When running backup tools, connect directly to PostgreSQL if possible
   - For restore operations, bypass PgBouncer to avoid timeout issues

4. **Load Testing**:
   - Test your application under load to ensure connection pooling works as expected
   - Monitor connection usage during peak times

5. **Documentation**:
   - Document your PgBouncer setup for your team
   - Include network diagram and connection details
