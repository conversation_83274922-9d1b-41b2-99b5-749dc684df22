import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from threading import Thread
from dotenv import load_dotenv
from datetime import datetime
import uuid

# Load environment variables
load_dotenv()

def send_email_postmark(to_email, subject, body_text, body_html=None, attachments=None, cc=None, bcc=None,
                        from_email=None, from_name=None, tag=None, track_opens=True, track_links=None):
    """
    Send an email using Postmark SMTP server with optional attachments.

    Args:
        to_email (str or list): Email address(es) of the recipient(s)
        subject (str): Subject of the email
        body_text (str): Plain text body of the email
        body_html (str, optional): HTML body of the email. Defaults to None.
        attachments (list, optional): List of file paths to attach. Defaults to None.
        cc (str or list, optional): Email address(es) for CC. Defaults to None.
        bcc (str or list, optional): Email address(es) for BCC. Defaults to None.
        from_email (str, optional): Sender email address. Defaults to env variable.
        from_name (str, optional): Sender name. Defaults to None.
        tag (str, optional): Tag for tracking in Postmark. Defaults to None.
        track_opens (bool, optional): Whether to track email opens. Defaults to True.
        track_links (str, optional): Track links in email. Options: None, 'HtmlAndText', 'HtmlOnly', 'TextOnly'. Defaults to None.

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    # Get credentials from environment variables
    # For Postmark, the server token is used as both username and password
    server_token = os.getenv('POSTMARK_SERVER_TOKEN')
    smtp_server = 'smtp.postmarkapp.com'
    smtp_port = 587  # Postmark supports ports 25, 587, and 2525

    # Set default sender email if not provided
    sender_email = from_email or os.getenv('POSTMARK_FROM_EMAIL', '<EMAIL>')

    # Format sender with name if provided
    sender = f"{from_name} <{sender_email}>" if from_name else sender_email

    # Convert single email to list
    if isinstance(to_email, str):
        to_email = [to_email]
    if isinstance(cc, str):
        cc = [cc]
    if isinstance(bcc, str):
        bcc = [bcc]

    # Ensure cc and bcc are lists or None
    cc = cc or []
    bcc = bcc or []

    # Create a list of all recipients for the actual sending
    all_recipients = to_email + cc + bcc

    # Create message
    msg = MIMEMultipart('alternative')
    msg['From'] = sender
    msg['To'] = ', '.join(to_email)
    if cc:
        msg['Cc'] = ', '.join(cc)
    msg['Subject'] = subject

    # Add unique Message-ID
    msg['Message-ID'] = f"<{uuid.uuid4()}@{sender_email.split('@')[1]}>"

    # Add Postmark specific headers
    if tag:
        msg['X-PM-Tag'] = tag

    msg['X-PM-TrackOpens'] = 'true' if track_opens else 'false'

    if track_links:
        msg['X-PM-TrackLinks'] = track_links

    # Attach text part
    part1 = MIMEText(body_text, 'plain')
    msg.attach(part1)

    # Attach HTML part if provided
    if body_html:
        part2 = MIMEText(body_html, 'html')
        msg.attach(part2)

    # Attach files if provided
    if attachments:
        for attachment_path in attachments:
            try:
                with open(attachment_path, 'rb') as file:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(file.read())

                # Encode attachment to base64
                encoders.encode_base64(part)

                # Add header with filename
                filename = os.path.basename(attachment_path)
                part.add_header('Content-Disposition', f'attachment; filename="{filename}"')

                # Attach the part to the message
                msg.attach(part)
                print(f"✅ Attached file: {filename}")
            except Exception as e:
                print(f"❌ Error attaching file {attachment_path}: {e}")

    # Function to send email in a separate thread
    def send_email_task():
        try:
            # Connect to SMTP server
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()  # Secure the connection
                # For Postmark, the server token is used as both username and password
                server.login(server_token, server_token)
                server.sendmail(sender_email, all_recipients, msg.as_string())
            print(f"✅ Email sent successfully to {', '.join(to_email)}")
            return True
        except Exception as e:
            print(f"❌ Error sending email: {e}")
            return False

    # Start email sending in a separate thread
    email_thread = Thread(target=send_email_task)
    email_thread.start()

    # Return immediately while the email is being sent in the background
    return True

if __name__ == "__main__":
    # Example usage of the Postmark SMTP function
    to_email = "<EMAIL>"
    subject = "Test Email via Postmark SMTP"
    body_text = "This is a test email sent using Postmark SMTP."
    body_html = """
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; }
            .container { padding: 20px; }
            .header { background-color: #4CAF50; color: white; padding: 10px; }
            .content { margin: 20px 0; }
            .footer { font-size: 12px; color: #888; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Test Email</h1>
            </div>
            <div class="content">
                <p>Hello,</p>
                <p>This is a test email sent using Postmark SMTP.</p>
                <p>It demonstrates HTML formatting and styling.</p>
            </div>
            <div class="footer">
                <p>This is an automated message. Please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
    """

    # Example with additional Postmark features
    send_email_postmark(
        to_email=to_email,
        subject=subject,
        body_text=body_text,
        body_html=body_html,
        from_name="Netpipo Team",
        tag="test-email",
        track_opens=True,
        track_links="HtmlAndText"
    )

    # Uncomment the following to test with attachments
    # attachments = ["path/to/your/file.pdf"]
    # send_email_postmark(
    #     to_email=to_email,
    #     subject=subject,
    #     body_text=body_text,
    #     body_html=body_html,
    #     attachments=attachments
    # )
